services:
  backendlmo:
    image: backend:latest
    command: gunicorn -w 4 -b 0.0.0.0:5000 manage:app
    networks:
      - inbound
    depends_on:
      - redislmo
      - dblmo
    volumes:
      - static_volume:/usr/src/app/backend/sistemaOrdenes/static
      - media_volume:/usr/src/app/backend/sistemaOrdenes/media
      - xml_invoices_volume:/usr/src/app/backend/sistemaOrdenes/xml_invoices
      - guides_volume:/usr/src/app/backend/sistemaOrdenes/guides
      - pdf_quote_volume:/usr/src/app/backend/sistemaOrdenes/pdf_quote
      #- ../../services/backend:/usr/src/app
    env_file:
      - ./.env.prod
    deploy:
      labels:
        - "traefik.http.routers.backendlmo.rule=Host(`lmo-api.mercaleader.io`)"
        - "traefik.http.routers.backendlmo.service=backendlmo"
        - "traefik.http.routers.backendlmo.entrypoints=websecurity"
        - "traefik.http.routers.backendlmo.tls.certresolver=myresolver"
        - "traefik.http.services.backendlmo.loadbalancer.server.port=5000"
        - "traefik.docker.network=inbound"
  
  dblmo:
    image: postgres:16
    networks:
      - inbound
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data/
    env_file:
      - ../.env.prod.db

  workerlmo:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery worker --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
    env_file:
      - ./.env.prod
    depends_on:
      - backendlmo
      - redislmo


  beatlmo:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery beat --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
    env_file:
      - ./.env.prod
    depends_on:
      - backendlmo
      - redislmo
    
    
  dashboardlmo:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery flower --port=5555 --broker=redis://redislmo:6379/0
    deploy:
      labels:
        - "traefik.http.routers.dashboardlmo.rule=Host(`lmo-flower.mercaleader.io`)"
        - "traefik.http.routers.dashboardlmo.service=dashboardlmo"
        - "traefik.http.routers.dashboardlmo.entrypoints=websecurity"
        - "traefik.http.routers.dashboardlmo.tls.certresolver=myresolver"
        - "traefik.http.services.dashboardlmo.loadbalancer.server.port=5555"
        - "traefik.docker.network=inbound"
    env_file:
      - ./.env.prod
    depends_on:
      - backendlmo
      - redislmo
      - workerlmo

  redislmo:
    image: redis:6-alpine
    networks:
      - inbound

volumes:
  postgres_data_prod:
  static_volume:
  media_volume:
  xml_invoices_volume:
  guides_volume:
  pdf_quote_volume:
networks:
  inbound:
   external: true