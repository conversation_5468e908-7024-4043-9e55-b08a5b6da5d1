services:
  backendgapy:
    image: backend:latest
    command: gunicorn -w 4 -b 0.0.0.0:5000 manage:app
    networks:
      - inbound
    depends_on:
      - redisgapy
      - dbgapy
    volumes:
      - static_volume:/usr/src/app/backend/sistemaOrdenes/static
      - media_volume:/usr/src/app/backend/sistemaOrdenes/media
      - xml_invoices_volume:/usr/src/app/backend/sistemaOrdenes/xml_invoices
      - guides_volume:/usr/src/app/backend/sistemaOrdenes/guides
      - pdf_quote_volume:/usr/src/app/backend/sistemaOrdenes/pdf_quote
      #- ../../services/backend:/usr/src/app
    env_file:
      - ./.env.prod
    deploy:
      labels:
        - "traefik.http.routers.backendgapy.rule=Host(`gapy-api.mercaleader.io`)"
        - "traefik.http.routers.backendgapy.service=backendgapy"
        - "traefik.http.routers.backendgapy.entrypoints=websecurity"
        - "traefik.http.routers.backendgapy.tls.certresolver=myresolver"
        - "traefik.http.services.backendgapy.loadbalancer.server.port=5000"
        - "traefik.docker.network=inbound"
  
  dbgapy:
    image: postgres:16
    networks:
      - inbound
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data/
    env_file:
      - ../.env.prod.db

  workergapy:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery worker --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
    env_file:
      - ./.env.prod
    depends_on:
      - backendgapy
      - redisgapy

  beatgapy:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery beat --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
    env_file:
      - ./.env.prod
    depends_on:
      - backendgapy
      - redisgapy
    
  dashboardgapy:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery flower --port=5555 --broker=redis://redisgapy:6379/0
    deploy:
      labels:
        - "traefik.http.routers.dashboardgapy.rule=Host(`gapy-flower.mercaleader.io`)"
        - "traefik.http.routers.dashboardgapy.service=dashboardgapy"
        - "traefik.http.routers.dashboardgapy.entrypoints=websecurity"
        - "traefik.http.routers.dashboardgapy.tls.certresolver=myresolver"
        - "traefik.http.services.dashboardgapy.loadbalancer.server.port=5555"
        - "traefik.docker.network=inbound"
    env_file:
      - ./.env.prod
    depends_on:
      - backendgapy
      - redisgapy
      - workergapy

  redisgapy:
    image: redis:6-alpine
    networks:
      - inbound

volumes:
  postgres_data_prod:
  static_volume:
  media_volume:
  xml_invoices_volume:
  guides_volume:
  pdf_quote_volume:
networks:
  inbound:
   external: true