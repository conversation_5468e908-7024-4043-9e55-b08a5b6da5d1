services:
  backendcpnotification:
    image: backend:latest
    command: gunicorn -w 4 -b 0.0.0.0:5000 manage:app
    networks:
      - inbound
    depends_on:
      - rediscpnotification
      - dbcpnotification
    volumes:
      - static_volume:/usr/src/app/backend/sistemaOrdenes/static
      - media_volume:/usr/src/app/backend/sistemaOrdenes/media
      - xml_invoices_volume:/usr/src/app/backend/sistemaOrdenes/xml_invoices
      - guides_volume:/usr/src/app/backend/sistemaOrdenes/guides
      - pdf_quote_volume:/usr/src/app/backend/sistemaOrdenes/pdf_quote
    env_file:
      - ./.env.prod
    deploy:
      labels:
        - "traefik.http.routers.backendcpnotification.rule=Host(`cpnotification-api.creativeplanet.com.mx`)"
        - "traefik.http.routers.backendcpnotification.service=backendcpnotification"
        - "traefik.http.routers.backendcpnotification.entrypoints=websecurity"
        - "traefik.http.routers.backendcpnotification.tls.certresolver=myresolver"
        - "traefik.http.services.backendcpnotification.loadbalancer.server.port=5000"
        - "traefik.docker.network=inbound"
  
  dbcpnotification:
    image: postgres:16
    networks:
      - inbound
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data/
    env_file:
      - ../.env.prod.db

  #beatcpnotification:
  #  image: backend:latest
  #  networks:
  #    - inbound
  #  command: celery --app sistemaOrdenes.app.tasks.celery beat --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
  #  volumes:
  #    - ../../services/backend:/usr/src/app
  #  env_file:
  #    - ./.env.prod
  #  depends_on:
  #    - backendcpnotification
  #    - rediscpnotification
  
  workercpnotification:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery worker --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
    env_file:
      - ./.env.prod
    depends_on:
      - backendcpnotification
      - rediscpnotification
    
  dashboardcpnotification:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery flower --port=5555 --broker=redis://rediscpnotification:6379/0
    deploy:
      labels:
        - "traefik.http.routers.dashboardcpnotification.rule=Host(`cpnotification-flower.creativeplanet.com.mx`)"
        - "traefik.http.routers.dashboardcpnotification.service=dashboardcpnotification"
        - "traefik.http.routers.dashboardcpnotification.entrypoints=websecurity"
        - "traefik.http.routers.dashboardcpnotification.tls.certresolver=myresolver"
        - "traefik.http.services.dashboardcpnotification.loadbalancer.server.port=5555"
        - "traefik.docker.network=inbound"
    env_file:
      - ./.env.prod
    depends_on:
      - backendcpnotification
      - rediscpnotification
      - workercpnotification

  rediscpnotification:
    image: redis:6-alpine
    networks:
      - inbound

volumes:
  postgres_data_prod:
  static_volume:
  media_volume:
  xml_invoices_volume:
  guides_volume:
  pdf_quote_volume:
networks:
  inbound:
   external: true