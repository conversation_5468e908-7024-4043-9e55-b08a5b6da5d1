services:
  backendcp:
    image: backend:latest
    command: gunicorn -w 4 -b 0.0.0.0:5000 manage:app
    networks:
      - inbound
    depends_on:
      - rediscp
      - dbcp
    volumes:
      - static_volume:/usr/src/app/backend/sistemaOrdenes/static
      - media_volume:/usr/src/app/backend/sistemaOrdenes/media
      - xml_invoices_volume:/usr/src/app/backend/sistemaOrdenes/xml_invoices
      - guides_volume:/usr/src/app/backend/sistemaOrdenes/guides
      - pdf_quote_volume:/usr/src/app/backend/sistemaOrdenes/pdf_quote
      #- ../../services/backend:/usr/src/app
    env_file:
      - ./.env.prod
    deploy:
      labels:
        - "traefik.http.routers.backendcp.rule=Host(`cp-api.mercaleader.io`)"
        - "traefik.http.routers.backendcp.service=backendcp"
        - "traefik.http.routers.backendcp.entrypoints=websecurity"
        - "traefik.http.routers.backendcp.tls.certresolver=myresolver"
        - "traefik.http.services.backendcp.loadbalancer.server.port=5000"
        - "traefik.docker.network=inbound"
  
  dbcp:
    image: postgres:16
    networks:
      - inbound
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data/
    env_file:
      - ../.env.prod.db

  workercp:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery worker --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
    env_file:
      - ./.env.prod
    depends_on:
      - backendcp
      - rediscp

  beatcp:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery beat --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
    env_file:
      - ./.env.prod
    depends_on:
      - backendcp
      - rediscp
    
  dashboardcp:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery flower --port=5555 --broker=redis://rediscp:6379/0
    deploy:
      labels:
        - "traefik.http.routers.dashboardcp.rule=Host(`cp-flower.mercaleader.io`)"
        - "traefik.http.routers.dashboardcp.service=dashboardcp"
        - "traefik.http.routers.dashboardcp.entrypoints=websecurity"
        - "traefik.http.routers.dashboardcp.tls.certresolver=myresolver"
        - "traefik.http.services.dashboardcp.loadbalancer.server.port=5555"
        - "traefik.docker.network=inbound"
    env_file:
      - ./.env.prod
    depends_on:
      - backendcp
      - rediscp
      - workercp

  rediscp:
    image: redis:6-alpine
    networks:
      - inbound

volumes:
  postgres_data_prod:
  static_volume:
  media_volume:
  xml_invoices_volume:
  guides_volume:
  pdf_quote_volume:
networks:
  inbound:
   external: true