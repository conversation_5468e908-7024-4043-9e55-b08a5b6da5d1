services:
  backend:
    image: backend:latest
    command: gunicorn -w 4 -b 0.0.0.0:5000 manage:app
    networks:
      - inbound
    depends_on:
      - redis
      - db
    volumes:
      - static_volume:/usr/src/app/backend/sistemaOrdenes/static
      - media_volume:/usr/src/app/backend/sistemaOrdenes/media
      - xml_invoices_volume:/usr/src/app/backend/sistemaOrdenes/xml_invoices
      - guides_volume:/usr/src/app/backend/sistemaOrdenes/guides
      - pdf_quote_volume:/usr/src/app/backend/sistemaOrdenes/pdf_quote
      - ../../services/backend:/usr/src/app
    env_file:
      - ./.env.prod
    deploy:
      labels:
        - "traefik.http.routers.backend.rule=Host(`tortuga-api.creativeplanet.com.mx`)"
        - "traefik.http.routers.backend.service=backend"
        - "traefik.http.routers.backend.entrypoints=websecurity"
        - "traefik.http.routers.backend.tls.certresolver=myresolver"
        - "traefik.http.services.backend.loadbalancer.server.port=5000"
        - "traefik.docker.network=inbound"
  
  db:
    image: postgres:16
    networks:
      - inbound
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data/
    env_file:
      - ../.env.prod.db

  #beat:
  #  image: backend:latest
  #  networks:
  #    - inbound
  #  command: celery --app sistemaOrdenes.app.tasks.celery beat --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
  #  volumes:
  #    - ../../services/backend:/usr/src/app
  #  env_file:
  #    - ./.env.prod
  #  depends_on:
  #    - backend
  #    - redis

  worker:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery worker --loglevel=info --logfile=sistemaOrdenes/logs/celery.log
    volumes:
      - ../../services/backend:/usr/src/app
    env_file:
      - ./.env.prod
    depends_on:
      - backend
      - redis
  
  dashboard:
    image: backend:latest
    networks:
      - inbound
    command: celery --app sistemaOrdenes.app.tasks.celery flower --port=5555 --broker=redis://redis:6379/0
    deploy:
      labels:
        - "traefik.http.routers.dashboard.rule=Host(`flower.creativeplanet.com.mx`)"
        - "traefik.http.routers.dashboard.service=dashboard"
        - "traefik.http.routers.dashboard.entrypoints=websecurity"
        - "traefik.http.routers.dashboard.tls.certresolver=myresolver"
        - "traefik.http.services.dashboard.loadbalancer.server.port=5555"
        - "traefik.docker.network=inbound"
    env_file:
      - ./.env.prod
    depends_on:
      - backend
      - redis
      - worker

  redis:
    image: redis:6-alpine
    networks:
      - inbound

volumes:
  postgres_data_prod:
  static_volume:
  media_volume:
  xml_invoices_volume:
  guides_volume:
  pdf_quote_volume:
networks:
  inbound:
   external: true