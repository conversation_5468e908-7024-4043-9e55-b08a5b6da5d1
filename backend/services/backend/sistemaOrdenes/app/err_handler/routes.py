import traceback
from flask import jsonify, make_response
from flask_smorest import Blueprint

err_handler = Blueprint("err_handler", __name__, description="<PERSON><PERSON><PERSON> Handler")


@err_handler.app_errorhandler(404)
def not_found(e):
    return make_response(
        jsonify({'error': 'El endpoint al que intentas acceder no existe'}), 404)


@err_handler.app_errorhandler(Exception)
def internal_server_error(e):
    traceback.print_exc()
    return make_response(jsonify(errores = 'ocurrio un error inesperado'), 500)