from flask_smorest import Blueprint
from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.configs import ROLES
from flask import request, jsonify, make_response, abort, Response, send_file
from flask_jwt_extended import jwt_required
from sistemaOrdenes.app.models.Models import OrderMessage, Order
from datetime import datetime, timedelta
from sqlalchemy import desc, func
import os
from sistemaOrdenes.app import config
from sistemaOrdenes.marketplaces.marketplaceUtils import get_marketplace_obj

MESSAGE_ATTACHMENTS = config.Config.MESSAGE_ATTACHMENTS_FOLDER

order_messages = Blueprint("Order_messages", __name__, description="Messaging on Orders")



def valid_date(name, date, date_format):
    try:
        return datetime.strptime(date, date_format)
    except ValueError as ve:
        print(ve)
        raise Exception(f"{name} con formato de fecha invalido")


def build_filter_orderMessages(query, pending, start_date, end_date):
    # filt
    if pending:
        query = query.filter(
            OrderMessage.Order.PendingToResponse.in_(pending))
    date_format = "%Y-%m-%d"
    if start_date and end_date:
        start_date = valid_date("fecha inicial", start_date, date_format)
        end_date = valid_date("fecha final", end_date, date_format) + timedelta(days=1)
        query = query.filter(
            OrderMessage.TimeStamp.between(start_date, end_date))
    return query


@order_messages.route('/api/order/messages/prueba')
def prueba_order_messages():
    return {'prueba': 'prueba'}


def manage_boolean_filter(is_pending):
    if is_pending is None or is_pending == "":
        return None
    is_pending = is_pending.lstrip().lower()
    if (is_pending != "true" or is_pending != "True"):
        is_pending = True
    elif (is_pending != "false" or is_pending != "False"):
        is_pending = False
    else:
        raise Exception('pending no es válido')
    return is_pending


def filta(query, is_pending, start_date, end_date):
    if manage_boolean_filter(is_pending) != None:
        query = query.join(Order).filter(Order.PendingToResponse == is_pending)
    if start_date and end_date:
        print('11111')
        date_format = "%Y-%m-%d"
        start_date = valid_date("fecha inicial", start_date, date_format)
        end_date = valid_date("fecha final", end_date, date_format) + timedelta(days=1)
        query = query.filter(
            OrderMessage.TimeStamp.between(start_date, end_date))
    return query


def order_query(query, offset, nc, param_to_order):
    if ((offset is not None and offset != "") and (nc is not None and nc != "")):
        query = query.order_by(
            desc(param_to_order)).offset(offset).limit(nc)
    else:
        query = query.order_by(
            desc(param_to_order)).offset(0).limit(30)
    return query


@order_messages.route('/api/orders/messages', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_stock_product_inStore(session):
    offset = request.args.get('offset')
    nc = request.args.get('next')
    is_pending = request.args.get('pending')
    start_date = request.args.get('startDate')
    end_date = request.args.get('endDate')
    query = session.query(OrderMessage)
    query = filta(query, is_pending, start_date, end_date)
    query = order_query(query, offset, nc, func.max(OrderMessage.TimeStamp))
    scope = {
        'orderMessageAtachments': {},
        # "orderMessages": {},
        #client": {},
        #"productsInOrder": {}
    }
    serialized_messages_orders = list(map(lambda message: message.serialize(scope=scope), query))
    return make_response(jsonify({'messages_orders': serialized_messages_orders}), 200)


"""@order_messages.route('/api/orders/messages', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_stock_product_inStore(session):
    offset = request.args.get('offset')#
    nc = request.args.get('next')#
    start_date = request.args.get('startDate')
    end_date = request.args.get('endDate')
    query = session.query(OrderMessage)
    query = build_filter_orderMessages(query, pending, start_date, end_date)
    query = order_query(query, offset, nc, OrderMessage.TimeStamp)
    messages = query.all()
    serialized_messages = serialize_list(messages, {})
    return make_response(jsonify({'messages': serialized_messages}), 200)"""


@order_messages.route('/api/orders/order/<int:id>/messages', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_message_from_order(session, id):
    order = ownUtils.validate_if_object_exists(session, "id", "native", id, Order)
    print('order.OrderMessages')
    print('order.OrderMessages')
    scope = {
        'orderMessageAtachments': {},
    }
    serialized_messages = list(map(lambda message: message.serialize(scope=scope), order.OrderMessages))
    return make_response(jsonify({'messages': serialized_messages}), 200)


@order_messages.route('/api/orders/order/<int:id>/insertTestmessages', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def set_message_from_order(session, id):
    order = ownUtils.validate_if_object_exists(session, "id", "native", id, Order)
    # Get the current datetime
    current_datetime = datetime.now()
    message_1 = OrderMessage(TimeStamp =current_datetime - timedelta(minutes=2),
                             Message = "Mensaje cliente prueba",
                             FromClient= True)
    message_2 = OrderMessage(
        TimeStamp = current_datetime - timedelta(minutes=1),
        Message = "Mensaje vendedor prueba", 
        FromClient= False)
    message_3 = OrderMessage(TimeStamp = current_datetime,
                             Message = "Mensaje cliente prueba 2",
                             FromClient= True)
    order.OrderMessages = [message_1, message_2, message_3]
    session.commit()
    return Response(status=200)

@order_messages.route('/api/order/messages/message/<int:id>', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_message_by_id(id, session):
    message = ownUtils.validate_if_object_exists(session, "id", "native", id, OrderMessage)
    message = message.serialize(scope={})
    return make_response(jsonify({'message': message}), 200)


def send_to_marketplace():
    return True


@order_messages.route('/api/order/<int:id>/message', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def send_new_message(id, session):
    """
    Only mercado libre
    Keyword arguments:
    argument -- description
    Return: return_description
    """
    data = ownUtils.get_info_from_json(request)
    order = ownUtils.validate_if_object_exists(session, "orderId", "native", id, Order)
    message = ownUtils.manage_request_field(OrderMessage.Message, field_name="message", container_dict=data, validation_function=ownUtils.check_len_sql_column)
    from_client = False
    mercado_libre_obj = get_marketplace_obj(session, "Mercado Libre")
    marketplace_order_message = mercado_libre_obj.send_message_to_client(order.MarketplaceOrderId, order.Cliente.MarketplaceClientId, message)
    order_message = OrderMessage(Message=message, FromClient=from_client, MarketplaceId=marketplace_order_message["id"])
    order.PendingToResponse = False
    order_message.Order = order
    session.commit()
    return make_response(jsonify({'mensaje': 'mensaje enviado con exito'}), 201)


@order_messages.route('/api/order/<int:id>/message', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def send_new_message(id, session):
    """
    Only mercado libre
    Keyword arguments:
    argument -- description
    Return: return_description
    """
    data = ownUtils.get_info_from_json(request)
    order = ownUtils.validate_if_object_exists(session, "orderId", "native", id, Order)
    option_type = ownUtils.manage_request_field(OrderMessage.Message, field_name="text", container_dict=data, validation_function=ownUtils.check_len_sql_column)
    option_id = ownUtils.manage_request_field(OrderMessage.Message, field_name="text", container_dict=data, validation_function=ownUtils.check_len_sql_column)
    from_client = False
    mercado_libre_obj = get_marketplace_obj(session, "Mercado Libre")
    
    #if order.OrderConversation and order.OrderConvesation.TemplateOptionOrderMessage:
    """FormatText
    TextTemplate
    OrderConversation
    TemplateOptionOrderMessage
    OptionOrderMessage"""
    #######################
    #order.
    if option_type == "TEMPLATE":
        template_id = ownUtils.manage_request_field(OrderMessage.Message, field_name="text", container_dict=data, validation_function=ownUtils.check_len_sql_column)
        marketplace_order_message = mercado_libre_obj.send_message_using_action_guide(order.MarketplaceOrderId, option_type, option_id, template_id)
        message = "cuack"
    elif option_type == "FREE_TEXT":
        text = ownUtils.manage_request_field(OrderMessage.Message, field_name="text", container_dict=data, validation_function=ownUtils.check_len_sql_column)
        marketplace_order_message = mercado_libre_obj.send_message_using_action_guide(order.MarketplaceOrderId, option_type, option_id, text)
        message = text
    else:
        abort(make_response(jsonify({'error': "El costo ingresado no es válido"}), 400))
    ###########
    
    order_message = OrderMessage(Message=message, FromClient=from_client, MarketplaceId=marketplace_order_message["id"])
    order.PendingToResponse = False
    order_message.Order = order
    session.commit()
    return make_response(jsonify({'mensaje': 'mensaje enviado con exito'}), 201)


@order_messages.route('/api/order/<int:id>/messages/markAsRead', methods=['PATCH'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def mark_as_read(id, session):
    pending_to_response_r = request.args.get('pednignToResponse')
    if pending_to_response_r in ["True","true"]:
        pending_to_response = True
    elif pending_to_response_r in ["False","false"]:
        pending_to_response = False
    else:
        abort(make_response(jsonify({'error': "scope no válido"}), 400))
    order = ownUtils.validate_if_object_exists(session, "orderId", "native", id, Order)
    order.PendingToResponse = pending_to_response
    session.commit()
    return make_response(jsonify({'mensaje': 'marcado como leido'}), 201)


def return_atachement_by_atachment_name(atachment_name):
    try:
        rutaArchivoConDiagonal = ownUtils.ownUnquote(atachment_name)
        ruta_img = os.path.join(MESSAGE_ATTACHMENTS, rutaArchivoConDiagonal)
        extension = atachment_name.rsplit('.', 1)[-1]
        mimetypee = f'image/{extension}'
        return send_file(ruta_img, mimetype=mimetypee)
    except Exception as e:
        abort(make_response(jsonify({'error': 'recurso no encontrado'}), 404))

@order_messages.route('/api/order/messages/atachments/<atachment_name>')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def return_image_atachment(atachment_name, session):
    print('00000000000000000>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>')
    return return_atachement_by_atachment_name(atachment_name)