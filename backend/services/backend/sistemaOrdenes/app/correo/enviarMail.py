import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from sistemaOrdenes import configs
from sistemaOrdenes.app import config

cuentaRemitente = config.Config.ACCOUNT_SYSTEM_MAIL
contraseniaRemitente = config.Config.PASS_SYSTEM_MAIL
message = MIMEMultipart("alternative")
message['Subject'] = "Alerta: Error al consultar los pedidos"
message['From'] = cuentaRemitente
message['To'] = configs.DESTINARIO_EMAIL

text = """\
Hola,
Surgió un error al consultar los pedidos, necesitamos tu ayuda"""

html = """\
<html>
  <body>
    <p><PERSON><PERSON>,<br>
       Surgió un error al consultar los pedidos, necesitamos tu ayuda
    </p>
  </body>
</html>
"""

part1 = MIMEText(text, "plain")
part2 = MIMEText(html, "html")

message.attach(part1)
message.attach(part2)


def enviarCorreoDeAlerta():
    try:
        context = ssl.create_default_context()
        with smtplib.SMTP_SSL(configs.SERVIDOR_SMTP,
                              configs.PUERTO_SMTP, context=context) as server:
            server.login(cuentaRemitente, contraseniaRemitente)
            server.sendmail(cuentaRemitente,
                            configs.DESTINARIO_EMAIL, message.as_string())
            return True, 'exito'
    except Exception as e:
        return False, str(e)
