import os
import requests
from flask import send_file, Response
from werkzeug.utils import secure_filename

from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.app import config

CARPETA_COMPANY = config.Config.COMPANY_IMAGE_FOLDER
RUTA_COMPANY_IMAGE = os.path.join(CARPETA_COMPANY, 'companyImage')
RUTA_DEFAULT_COMPANY = os.path.join(CARPETA_COMPANY, 'defaultPhoto')
QR_PATH = 'qr_image'
RUTA_QR_COMPANY = os.path.join(CARPETA_COMPANY, QR_PATH)


def create_companyImage_folder():
    # Verificar si la ruta completa existe y, si no, crearla
    if not os.path.exists(RUTA_COMPANY_IMAGE):
        os.makedirs(RUTA_COMPANY_IMAGE)


def create_qr_folder():
    # Verificar si la ruta completa existe y, si no, crearla
    if not os.path.exists(RUTA_QR_COMPANY):
        os.makedirs(RUTA_QR_COMPANY)


def addNewPhotoCompany(imageFile):
    try:
        create_companyImage_folder()
        miniImage = ownUtils.checkValidImageFile(imageFile)
        filename = imageFile.filename
        if not ownUtils.allowed_img_file_company(filename):
            raise Exception("La extension de la imagen es invalido")

        nombreSeguroImg = secure_filename(f'companyImage.{filename.rsplit(".", 1)[1].lower()}')
        destination = os.path.join(RUTA_COMPANY_IMAGE, nombreSeguroImg)
        miniImage.save(destination)
        return nombreSeguroImg

    except Exception as e:
        raise Exception(str(e))


def eliminate_Photo_existed(nameImg):
    ubicationPhoto = os.path.join(RUTA_COMPANY_IMAGE, nameImg)
    if os.path.exists(ubicationPhoto):
        os.remove(ubicationPhoto)


def return_qr_image(nameImg):
    try:
        ruta_img = ''
        mimetypee = ''
        if nameImg is None:
            default_ruta = os.path.join(
                RUTA_DEFAULT_COMPANY, 'NotFoundImage.jpg')
            ruta_img = default_ruta
            mimetypee = 'image/jpg'
            return send_file(ruta_img, mimetype=mimetypee)
        elif ownUtils.check_url_imagen(nameImg):
            response = requests.get(nameImg)
            if response.status_code == 200:
                extension = nameImg.rsplit('.', 1)[-1]
                mimetypee = f'image/{extension}'
                return Response(response.content, mimetype=mimetypee)
            else:
                return_qr_image(None)
        else:
            ruta_img = os.path.join(RUTA_QR_COMPANY, nameImg)
            extension = nameImg.rsplit('.', 1)[-1]
            mimetypee = f'image/{extension}'
            return send_file(ruta_img, mimetype=mimetypee)

    except Exception as e:
        raise Exception(str(e))


def return_img_company(nameImg):
    try:
        ruta_img = ''
        mimetypee = ''
        if nameImg is None:
            default_ruta = os.path.join(
                RUTA_DEFAULT_COMPANY, 'NotFoundImage.jpg')
            ruta_img = default_ruta
            mimetypee = 'image/jpg'
            return send_file(ruta_img, mimetype=mimetypee)
        elif ownUtils.check_url_imagen(nameImg):
            response = requests.get(nameImg)
            if response.status_code == 200:
                extension = nameImg.rsplit('.', 1)[-1]
                mimetypee = f'image/{extension}'
                return Response(response.content, mimetype=mimetypee)
            else:
                return_img_company(None)
        else:
            ruta_img = os.path.join(RUTA_COMPANY_IMAGE, nameImg)
            extension = nameImg.rsplit('.', 1)[-1]
            mimetypee = f'image/{extension}'
            return send_file(ruta_img, mimetype=mimetypee)

    except Exception as e:
        raise Exception(str(e))
