from flask_smorest import Blueprint
from flask import jsonify, request, make_response, current_app, send_file
import os
from .companyUtils import addNewPhotoCompany, \
    return_img_company, eliminate_Photo_existed, return_qr_image, \
    create_qr_folder, QR_PATH
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.app.models.Models import Company, BankAccount, CommercialTerm
import qrcode
import traceback
from sistemaOrdenes.app import config

GENERAL_IMAGE_FOLDER = config.Config.GENERAL_IMAGE_FOLDER
QR_NAME = 'qr_company.png'
company = Blueprint("Company", __name__,
                    description="Company methods")


def set_company_photo(files, data, company):
    if 'url_image_company' in data:
        if ((data['url_image_company'] != '') and (data['url_image_company'] is not None)):
            if ownUtils.check_url_imagen(data['url_image_company']):
                image_name = data['url_image_company']
            else:
                return 'URL de la imagen no valido'
    elif 'image_company' in files:
        if files['image_company'] is not None:
            image_file = files['image_company']
            image_name = addNewPhotoCompany(image_file)
    # if company.ImageCompany is not None:
    #     if not ownUtils.check_url_imagen(company.ImageCompany):
    #         eliminate_Photo_existed(company.ImageCompany)
    company.ImageCompany = image_name


@company.route('/api/getLoginPhoto')
def return_imgLoginPhoto():
    try:
        default_ruta = os.path.join(GENERAL_IMAGE_FOLDER, 'mercalider.png')
        ruta_img = default_ruta
        mimetypee = 'image/png'
        imageToReturn = send_file(ruta_img, mimetype=mimetypee)
    except Exception as e:
        print(str(e))
        imageToReturn = make_response(
            jsonify({'error': f'{str(e)}'}), 500)
    finally:
        return imageToReturn


@company.route('/api/company/getCompanyPhoto')
def return_company_imgLoginPhoto():
    try:
        session = ScopedSession()
        if session.query(Company).count() == 0:
            imageToReturn = return_img_company(None)
        else:
            miniImg = session.query(Company).first()
            imageToReturn = return_img_company(miniImg.ImageCompany)

    except Exception as e:
        print(str(e))
        imageToReturn = return_img_company(None)
    finally:
        session.close()
        return imageToReturn


@company.route('/api/company/getCompanyQrCode')
def get_company_qr_code():
    try:
        session = ScopedSession()
        if session.query(Company).count() == 0:
            imageToReturn = return_qr_image(None)
        else:
            miniImg = session.query(Company).first()
            imageToReturn = return_qr_image(miniImg.Qr_code_SAT)
    except Exception as e:
        print(str(e))
        imageToReturn = return_qr_image(None)
    finally:
        session.close()
        return imageToReturn


@company.route('/api/company/getInfoCompany')
def get_info_company():
    try:
        session = ScopedSession()
        scope = request.args.get('scope')
        if not scope:
            scope = "basic"
        company = session.query(Company).first()
        if company:
            if scope == 'basic':
                scope_dict = {}
            elif scope == 'extended':
                scope_dict = {
                    'bankAccounts': {},
                    'commercialTerms': {}
                }
            else:
                raise Exception('Scope no válido')
            company_serialized = company.serialize(scope=scope_dict)
        else:
            company_serialized = {}
        created_response = make_response(jsonify(
            {'company': company_serialized}), 200)
    except Exception as e:
        print(str(e))
        created_response = make_response(
            jsonify({'error': f'{str(e)}'}), 500)
    finally:
        session.close()
        return created_response


def link_to_qr_code(path, link, company):
    try:
        if len(link) >= 500:
            raise Exception('Link demasiado largo')
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(link)
        qr.make(fit=True)

        # Create an Image object from the QR Code instance
        img = qr.make_image(fill_color="black", back_color="white")
        # Save the image
        create_qr_folder()
        qr_img_path = f'{path}/{QR_PATH}/{QR_NAME}'
        img.save(f'{qr_img_path}')
        company.Qr_code_SAT = QR_NAME
        company.Url_SAT = link
    except Exception as e:
        traceback.print_exc()
        return str(e)


def set_company_data(company, data, files, session):
    errors = ''
    alerts = ''
    if 'company_name' in data:
        company_name = data.get('company_name')
        if len(company_name) > \
                Company.CompanyName.property.columns[0].type.length:
            errors = ownUtils.\
                concatenar_mensaje_retroalimentacion_sin_validacion(
                    errors,
                    (f"el nombre de la compañia debe ser menor a "
                     f"{Company.CompanyName.property.columns[0].type.length}"))
        else:
            company.CompanyName = company_name
    else:
        alerts = ownUtils.\
            concatenar_mensaje_retroalimentacion_sin_validacion(
                alerts,
                ('nombre de la compañia')
            )
    if 'RFC' in data:
        rfc = data.get('RFC')
        error = ownUtils.is_valid_rfc(rfc)
        if not error:
            company.RFC = rfc
        else:
            errors = ownUtils.\
                concatenar_mensaje_retroalimentacion_sin_validacion(
                    errors, 'rfc inválido')
    else:
        alerts = ownUtils.\
            concatenar_mensaje_retroalimentacion_sin_validacion(
                alerts,
                ('RFC')
            )
    if 'image_company' in files or 'url_image_company' in data:
        errores = set_company_photo(files, data, company)
        if errores:
            errors = ownUtils.\
                concatenar_mensaje_retroalimentacion_sin_validacion(
                    errors, f'{errores}')
    else:
        alerts = ownUtils.\
            concatenar_mensaje_retroalimentacion_sin_validacion(
                alerts,
                ('imagen de la compañia')
            )
    if 'qr_code_SAT' in data:
        qr_code = data.get('qr_code_SAT')
        if qr_code:
            path = current_app.config["COMPANY_IMAGE_FOLDER"]
            errores = link_to_qr_code(path, qr_code, company)
            if errores:
                errors = ownUtils.\
                    concatenar_mensaje_retroalimentacion_sin_validacion(
                        errors, f'{errores}')
    else:
        alerts = ownUtils.\
            concatenar_mensaje_retroalimentacion_sin_validacion(
                alerts,
                ('url para generar código qr SAT')
            )
    if alerts:
        alerts = f'no se actualizo:{alerts}'
    return errors, alerts


@company.route('/api/company/setInfoCompany', methods=['PUT'])
def update_company():
    try:
        session = ScopedSession()
        data = request.form
        files = request.files
        if session.query(Company).count() == 0:
            company = Company()
            errores, alerts = set_company_data(company, data, files, session)
            if errores:
                raise Exception(errores)
            else:
                session.add(company)
        elif session.query(Company).count() == 1:
            company = session.query(Company).first()
            errores, alerts = set_company_data(company, data, files, session)
            if errores:
                raise Exception(errores)
        else:
            raise Exception('Error, notidicar a desarrollo')
        session.commit()
        created_response = make_response(jsonify(
            {'mensaje': f"{ownUtils.concatenarMensajeRetroalimentacion('Actualizado con exito', alerts)}"}), 200)
    except Exception as e:
        traceback.print_exc()
        session.rollback()
        print(str(e))
        created_response = make_response(
            jsonify({'error': f'{str(e)}'}), 500)
    finally:
        session.close()
        return created_response


def validate_account_data(request, info_dict):
    data = ownUtils.get_info_from_json(request)
    bank_name = ownUtils.manage_request_field(field_name='bankName', container_dict=data, validation_function=ownUtils.is_valid_name, isRequired=True)
    account_num = data.get('accountNum')
    # account_num = ownUtils
    clabe = data.get('clabe')
    # account_num = ownUtils
    if info_dict["errores"]:
        raise Exception(info_dict["errores"])
    return bank_name, account_num, clabe


def return_company(session):
    if session.query(Company).count() == 0:
        raise Exception('No hay información previa de la compañia')
    return session.query(Company).first()


@company.route('/api/company/bankAccount', methods=['POST'])
@ownUtils.my_decorator_http_manage
def add_bankAccount(session):
    info_dict = {
        "info": "",
        "errores": ""
    }
    bank_name, account_num, clabe = validate_account_data(request, info_dict)
    company = return_company(session)
    bank_account = BankAccount(bankName=bank_name, accountNumber=account_num, clabe=clabe)
    bank_account.Company = company
    session.add(bank_account)
    ownUtils.commit_catching_unique_constraint(session=session)
    return make_response(
        jsonify({'mensaje': ('Cuenta bancaria registrada '
                             'exitosamente')}), 201)


@company.route('/api/company/bankAccount/<id>', methods=['PUT'])
@ownUtils.my_decorator_http_manage
def modify_bankAccount(session, id):
    info_dict = {
        "info": "",
        "errores": ""
    }
    bank_account = ownUtils.validate_if_object_exists(session, "id", "native", id, BankAccount)
    bank_name, account_num, clabe = validate_account_data(request, info_dict)
    bank_account.BankName = bank_name
    bank_account.AccountNumber = account_num
    bank_account.Clabe = clabe
    ownUtils.commit_catching_unique_constraint(session=session)
    return make_response(jsonify({'mensaje': 'Cuenta bancaria modificada exitosamente'}), 200)


@company.route('/api/company/bankAccount/<id>', methods=['DELETE'])
@ownUtils.my_decorator_http_manage
def update_bankAccount(session, id):
    bank_account = ownUtils.validate_if_object_exists(session, "id", "native", id, BankAccount)
    session.delete(bank_account)
    session.commit()
    return make_response(jsonify({'mensaje': 'Cuenta bancaria eliminada exitosamente'}), 200)


def validate_commercialTerm(request):
    data = ownUtils.get_info_from_json(request)
    commercial_term = ownUtils.manage_request_field(CommercialTerm.CommercialTerm, field_name='commercialTerm', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=True)
    return commercial_term


@company.route('/api/company/commercialTerm', methods=['POST'])
@ownUtils.my_decorator_http_manage
def add_comercialTerm(session):
    commercial_term_request = validate_commercialTerm(request)
    company = return_company(session)
    commercial_term = CommercialTerm(commercialTerm=commercial_term_request)
    commercial_term.Company = company
    session.add(commercial_term)
    ownUtils.commit_catching_unique_constraint(session=session)
    return make_response(
        jsonify(
            {
                'mensaje': 'Condición comercial registrada exitosamente',
                'commercialTermInfo': commercial_term.serialize()
            }
        ), 201)


@company.route('/api/company/commercialTerm/<id>', methods=['PUT'])
@ownUtils.my_decorator_http_manage
def update_comercialTerm(session, id):
    commercial_term = ownUtils.validate_if_object_exists(session, "id", "native", id, CommercialTerm)
    commercial_term_request = validate_commercialTerm(request)
    commercial_term.CommercialTerm = commercial_term_request
    ownUtils.commit_catching_unique_constraint(session=session)
    return make_response(jsonify({'mensaje': 'Condición comercial modificada exitosamente'}), 200)


@company.route('/api/company/commercialTerm/<id>', methods=['DELETE'])
@ownUtils.my_decorator_http_manage
def delete_comercialTerm(session, id):
    commercial_term = ownUtils.validate_if_object_exists(session, "id", "native", id, CommercialTerm)
    session.delete(commercial_term)
    session.commit()
    return make_response(jsonify({'mensaje': 'Condición comercial eliminada exitosamente'}), 200)
