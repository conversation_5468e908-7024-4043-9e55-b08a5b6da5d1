from marshmallow import Schema, fields


class AuthSchema(Schema):
    email = fields.Str(
        required=True, description="Email Adress", example="<EMAIL>")
    password = fields.Str(
        required=True, description="Password", example="Pass_word123")


class SignUpSchema(AuthSchema):
    name = fields.Str(required=True, description="User name", example="<PERSON>")
    roleId = fields.Str(required=True, description="User role",
                        example="Logistics_employee")


class UserSchema(Schema):
    user = fields.Str(required=True, description="User", example="<PERSON>")
