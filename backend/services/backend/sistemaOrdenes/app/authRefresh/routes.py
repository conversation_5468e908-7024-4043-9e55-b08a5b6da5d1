# flask imports
from .schemas import AuthSchema
from sistemaOrdenes.app.ownUtils import validate_hashed
from sistemaOrdenes.app.models.Models import User
from flask import request, jsonify, make_response
from flask_jwt_extended import create_access_token, \
    set_access_cookies, unset_jwt_cookies
from flask_smorest import Blueprint
from sistemaOrdenes.app import ownUtils

authRefresh = Blueprint("AuthRefresh", __name__,
                        description="Autentication methods")

# Route for logIn


@authRefresh.route('/loginAuth', methods=['POST'])
@authRefresh.arguments(AuthSchema)
@ownUtils.my_decorator_http_manage
def login(AuthSchema, session):
    auth = ownUtils.get_info_from_json(request)
    email = auth.get('email')
    password = auth.get('password')
    if not email or not password:
        # returns 401 if any email or / and password is missing
        return make_response(
            jsonify({'errores': 'email o contraseña incorrecta'}), 401)
    user = session.query(User).filter(User.Email == email).first()
    if not user or not validate_hashed(auth['password'], user.Password):
        # returns 401 if user does not exist
        return make_response(jsonify({'errores': 'Usuario o contraseña incorrecta'}), 401)
    if not user.Password:
        return make_response(jsonify({'errores': 'Usuario no ha terminado su registro'}), 401)
    # generates the JWT Token
    additional_claims = {
        'user': user.Name,
        'role': user.Role.RoleName,
        'userId': user.UserId}
    access_token = create_access_token(
        identity=user.UserId,
        additional_claims=additional_claims)
    response = jsonify(
        {'user': user.Name, 'role': user.Role.RoleName})
    set_access_cookies(response, access_token)
    return response


@authRefresh.route("/logout", methods=["POST"])
def logout():
    try:
        # Removes client session cookies and return success message
        response = jsonify({"mensaje": "logout exitoso"})
        unset_jwt_cookies(response)
        return response
    except Exception as e:
        # Returns error message
        return make_response(jsonify({'errores': f'{str(e)}'}), 500)
