import requests
def test_api(): 
    url = 'https://localhost:5000/api/messages/defaultMessage'
    list_test_messages = [
  { "name": "greetings", "message": "Hi, good afternoon!" },
  { "name": "check_in", "message": "Just checking in to see how things are going." },
  { "name": "morning_boost", "message": "Good morning! Wishing you a productive day." },
  { "name": "positive_vibes", "message": "Hope you’re having a great day!" },
  { "name": "offer_help", "message": "Let me know if you need anything." },
  { "name": "casual_hello", "message": "Hey, what’s up?" },
  { "name": "meeting_reminder", "message": "Are we still on for the meeting today?" },
  { "name": "gratitude", "message": "Thanks so much for your help!" },
  { "name": "follow_up", "message": "Can we follow up later today?" },
  { "name": "anticipation", "message": "Looking forward to our meeting tomorrow." },
  { "name": "praise", "message": "Great job on the presentation!" },
  { "name": "farewell", "message": "See you later!" },
  { "name": "no_problem", "message": "No problem at all. Happy to help!" },
  { "name": "well_wishes", "message": "Take care and stay safe!" },
  { "name": "bye", "message": "Catch you later!" },
  { "name": "thanks", "message": "Thank you!" },
  { "name": "motivational", "message": "Let’s do this and make it count!" },
  { "name": "status_ok", "message": "All good on my end." },
  { "name": "ready_status", "message": "I’m ready when you are." },
  { "name": "agreement", "message": "Sounds good to me." },
  { "name": "supportive", "message": "I’m here if you need anything." },
  { "name": "resend_request", "message": "Can you please resend that file?" },
  { "name": "taking_care", "message": "I’ll handle it, don’t worry." },
  { "name": "thoughts_request", "message": "Let me know your thoughts when you can." },
  { "name": "idea_approval", "message": "That’s actually a great idea!" },
  { "name": "on_way", "message": "I'm on my way." },
  { "name": "appreciation", "message": "Thanks for checking in with me." },
  { "name": "time_request", "message": "Do you have a quick minute to talk?" },
  { "name": "approval", "message": "That works for me!" },
  { "name": "wrap_up", "message": "Talk soon, take care." },
  { "name": "friendly_reply", "message": "Nice hearing from you." },
  { "name": "agreement_simple", "message": "That’s fine with me." },
  { "name": "grateful", "message": "Really appreciate it!" },
  { "name": "investigating", "message": "I'm looking into it right now." },
  { "name": "cool", "message": "No worries at all." },
  { "name": "followup", "message": "I’ll follow up on that tomorrow." },
  { "name": "confirmation", "message": "Got it, thanks!" },
  { "name": "availability_check", "message": "Need anything else from me?" },
  { "name": "catch_up", "message": "Let’s catch up sometime soon." },
  { "name": "acknowledge", "message": "Message received, thank you." },
  { "name": "arrival_notice", "message": "I’m almost there." },
  { "name": "thanks_again", "message": "Thanks again for your support!" },
  { "name": "agreement_strong", "message": "I completely agree with that." },
  { "name": "confirmation_simple", "message": "Understood." },
  { "name": "on_my_way", "message": "I’ll be there shortly." },
  { "name": "reminder", "message": "Just a quick reminder for later today." },
  { "name": "consent", "message": "I'm good with that plan." },
  { "name": "let_me_know", "message": "Let me know if anything changes." },
  { "name": "perfect", "message": "Perfect, thanks again!" }
]

    for message in list_test_messages:
        response = requests.post(url, json=message, verify=False)  # Set verify=False to ignore SSL warnings
        if response.status_code == 200:
            print(f"Message '{message['name']}' created successfully.")
        else:
            print(f"Failed to create message '{message['name']}': {response.status_code} - {response.text}")


if __name__ == "__main__":
    test_api()
    print("Test completed.")