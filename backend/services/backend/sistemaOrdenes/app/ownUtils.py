from urllib.parse import urlparse
from werkzeug.utils import secure_filename
from flask_jwt_extended import jwt_required, get_jwt
from sistemaOrdenes import configs
import os
import base64
import re
from PIL import Image
import requests
import uuid
import bcrypt
import pytz
import traceback
from cryptography.fernet import Fernet
from datetime import datetime, timezone
from sistemaOrdenes.app import config
from sistemaOrdenes.app.models.db import ScopedSession
from sqlalchemy.inspection import inspect
from sqlalchemy.orm.exc import UnmappedInstanceError
from sqlalchemy.exc import OperationalError
from flask import jsonify, make_response, abort
from dateutil import parser
import json
import validators

ALLOWED_IMG_EXTENSIONS = configs.EXTENSIONES_PERMITIDAS_IMAGENES_PRODUCTOS
ALLOWED_IMG_ENCODING = configs.ALLOWED_IMG_ENCODING
ALLOWED_COMPANY_IMG_EXTENSIONS = configs.EXTS_PERMITIDAS_IMAGENES_COMPANY
ALLOWED_MASSIVE_UPDATE_EXTENSIONS = configs.EXTS_PERM_ARCHIVO_CARGA_MASIVA
MASIVE_UPDATE_FILE_NAME = configs.NOMBRE_ARCHIVO_CARGA_MASIVA
ALLOWED_EXTENSIONS_INVOICES = configs.EXTENSIONES_PERMITIDAS_FACTURAS
NUMERIC_RE = r'[-+]?\d*\.?\d+'
JWT_SECRET_KEY = config.Config.JWT_SECRET_KEY
BACKEND_URL = config.Config.BACKEND_URL


def ownUnquote(path):
    path_elements = path.split('+++')
    pathQuote = os.path.join(*path_elements)
    return pathQuote


def ownQuote(path):
    path_elements = get_path_elements(path)
    pathQuote = '+++'.join(path_elements)
    return pathQuote


def get_path_elements(path):
    # Initialize an empty list to store the path elements
    path = f'/{path}'
    path_elements = []

    # Iterate until the root directory is reached
    while path != '/':
        path, element = os.path.split(path)
        path_elements.insert(0, element)
    return path_elements


def is_valid_url(url):
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except ValueError:
        return False


def return_most_relevant_photo(photos):
    most_relevant_photo_id = len(photos)
    most_relevant_photo = None
    for photo in photos:
        if photo.ItemNumber == 1:
            return return_image_url(photo.URLPhoto)
        if photo.ItemNumber < most_relevant_photo_id:
            most_relevant_photo_id = photo.ItemNumber
            most_relevant_photo = photo.URLPhoto
    print('monst')
    print(most_relevant_photo)
    print('monst')
    return return_image_url(most_relevant_photo)


def return_image_url(string_image_ubication):
    image_url = string_image_ubication if is_valid_url(string_image_ubication) else imagePathToImageURL(string_image_ubication, 'products')
    return image_url


def imagePathToImageURL(imagePath, module):
    encodepath = ownQuote(imagePath)
    imageURL = f'{BACKEND_URL}/api/{module}/getImage/{encodepath}'
    return imageURL

def imagePathToImageURLAttachment(attachment_path):
    encodepath = ownQuote(attachment_path)
    imageURL = f'{BACKEND_URL}/api/order/messages/atachments/{encodepath}'
    return imageURL


def imageURLToImagePath(imageURL, module):
    pathImage = imageURL.replace(
        f'{BACKEND_URL}/api/{module}/getImage/', '')
    decodePathImage = ownUnquote(pathImage)
    return os.path.realpath(decodePathImage)


def generate_internal_base_sku(brand, model):
    brandPart = re.sub(r'\W+', '', brand)[0:3]
    modelPart = re.sub(r'\W+', '', model)
    return f'{brandPart.upper()}{modelPart.upper()}'


def generate_internal_sku(internalBaseSku, variations):
    variations_int_list = [key for key in variations.keys()]
    variations_int_list.sort()
    internalSku = f'{internalBaseSku}'
    for variationName in variations_int_list:
        internalSku = f'{internalSku}_{variationName}-{variations[variationName]}'
    return internalSku


def generate_hashedsalt(param):
    # This funcion return convert param with hash and salt
    # generate salt
    salt = bcrypt.gensalt()
    # implement hash
    hashed = bcrypt.hashpw(param.encode('utf-8'), salt)
    hashed = hashed.decode('utf-8')
    return hashed


def validate_hashed(param, hashed):
    # This funcion return true if param funcion correctly with hash
    param_bytes = param.encode('utf-8')
    hashed_bytes = hashed.encode('utf-8')
    return bcrypt.checkpw(param_bytes, hashed_bytes)


def generate_encrypt(param):
    # Return encrypted param
    secret_key_creative = JWT_SECRET_KEY.encode()
    clave = base64.urlsafe_b64encode(secret_key_creative)
    cipher_suite = Fernet(clave)
    param_bytes = param.encode()
    datos_cifrados = cipher_suite.encrypt(param_bytes).decode()

    return datos_cifrados


def decrypt_data(param):
    # Return decrypted param
    secrets = JWT_SECRET_KEY
    secret_key_creative = secrets['secret_key']
    clave = secret_key_creative.encode()
    cipher_suite = Fernet(clave)
    datos_descifrados = cipher_suite.decrypt(param.encode()).decode()
    return datos_descifrados


def generate_obj_time(expiredTime):
    try:
        year = int(expiredTime[:4])
        month = int(expiredTime[4:6])
        day = int(expiredTime[6:8])
        hour = int(expiredTime[8:10])
        minute = int(expiredTime[10:12])
        second = int(expiredTime[12:14])
        fecha_obj = datetime(year, month, day, hour, minute, second)
    except Exception as e:
        print(str(e))
        fecha_obj = datetime(1919, 10, 10, 10, 10, 10)
    finally:
        return fecha_obj


def get_time_zone_now():
    now_utc = datetime.now(timezone.utc)
    return now_utc


def get_date_now():
    zone = pytz.timezone(configs.TIME_ZONE)
    time_zone = datetime.now(zone)
    return time_zone.strftime('%Y-%m-%d')


def save_img_secure_name(image, directory):
    if image.filename == '':
        raise Exception("No se puede guardar imagenes sin nombre")
    imageReturn = checkValidImageFile(image)
    print('imageReturn')
    print(imageReturn)
    print('imageReturn')
    if imageReturn is False:
        raise Exception(f'La imagen no es válida {image.filename}')
    if image and allowed_img_file(image.filename):
        print('pppppp')
        print(image)
        print(allowed_img_file(image.filename))
        print('pppppppp')
        nombreSeguro = secure_filename(
            f'{str(uuid.uuid4())}.{image.filename.rsplit(".", 1)[1].lower()}')
        imageReturn.save(os.path.join(directory, nombreSeguro))
        return nombreSeguro
    else:
        raise Exception("La imagen no tiene una extensión válida")


def allowed_img_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_IMG_EXTENSIONS


def allowed_img_file_company(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_COMPANY_IMG_EXTENSIONS


def concatenarMensajeRetroalimentacion(mensajeCompleto, parteAConcatenar):
    if parteAConcatenar:
        mensajeCompleto = concatenar_mensaje_retroalimentacion_sin_validacion(
            mensajeCompleto, parteAConcatenar)
    return mensajeCompleto


def concatenar_mensaje_retroalimentacion_envuelto(
        mensaje_completo, mensaje_externo, mensaje_interno):
    if mensaje_interno:
        mensaje_envuelto = f'{mensaje_externo}({mensaje_interno})'
        mensaje_completo = concatenar_mensaje_retroalimentacion_sin_validacion(
            mensaje_completo, mensaje_envuelto)
    return mensaje_completo


def concatenar_mensaje_retroalimentacion_sin_validacion(
        mensajeCompleto, parteAConcatenar):
    mensajeCompleto = ((f'{mensajeCompleto}'
                        f'{parteAConcatenar[0].upper()}{parteAConcatenar[1:]}')
                       if mensajeCompleto == ''
                       else (f'{mensajeCompleto}, {parteAConcatenar}'))
    return mensajeCompleto

# ------------------------------------------
# VALIDACIONES DATOS


def validateSatCode(satCode):
    satCode = str(satCode)
    if ((len(satCode) == 8) and re.match('^[0-9_]+$', satCode)):
        return True
    else:
        return False


def validateCondition(condicion):
    if (condicion == 'Nuevo' or condicion == 'Usado'):
        return True
    else:
        return False


def validateUnitCode(unitCode):
    unitCode = str(unitCode)
    if ((len(unitCode) == 2 or len(unitCode) == 3) and re.match('^[a-zA-Z0-9_]+$', unitCode)):
        return True
    else:
        return False


def validateVideo(videoUrl):
    if re.match(r"^((?:https?:)?\/\/)?((?:www|m)\.)?"
                r"((?:youtube(-nocookie)?\.com|youtu.be))"
                r"(\/(?:[\w\-]+\?v=|embed\/|v\/)?)([\w\-]+)(\S+)?$",
                videoUrl):
        return True
    else:
        return False


def revisarFloatPositivo(numero):
    try:
        if not float(numero) >= 0:
            raise Exception("El numero no es positivo")
        return None
    except Exception as e:
        print(str(e))
        return f"{numero} no es un float positivo"


def revisar_descuento_a_precio(numero):
    try:
        float_num = float(numero)
        if not (float_num >= 0 and float_num <= 100):
            raise Exception("Descuento debe ser un numero entre 0 y 100")
        return None
    except Exception as e:
        print(str(e))
        return "Descuento debe ser un numero entre 0 y 100"


def revisarIntPositivo(numero):
    try:
        if not (int(numero) >= 1):
            raise Exception("El numero no es entero positivo")
        return None
    except Exception as e:
        print(str(e))
        return "El numero no es entero positivo" 


def checkInt(num):
    try:
        return int(num)
    except Exception as e:
        print(str(e))
        return False


def revisar_int_0_o_mayor(numero):
    try:
        return int(numero) >= -0
    except Exception as e:
        print(str(e))
        return False


def revisar_int_mayor_a(num: str | int, start: int) -> bool:
    try:
        return int(num) >= start
    except Exception as e:
        print(str(e))
        return False


def checkValidImageFromUrl(url):
    try:
        # image = Image.open(requests.get(url, stream=True).raw)
        return True
    except Exception as e:
        print(str(e))
        return False


def check_url_imagen(url):
    try:
        response = requests.head(url)
        if response.status_code == 200:
            content_type = response.headers.get('Content-Type')
            if 'image' in content_type:
                return True
    except requests.exceptions.RequestException:
        pass
    return False


def checkValidImageFile(file):
    try:
        image = Image.open(file)
        return image
        # return file
    except Exception as e:
        print(str(e))
        return False


def ajustarLongitud(nombre_campo, campo, longitudEnBD):
    campo = str(campo)

    if len(campo) > longitudEnBD:
        return (f'El campo {nombre_campo} fue recortado a {longitudEnBD} caracteres',
                campo[0:longitudEnBD])
    else:
        return True, campo


def save_b64_img(image64_string, directory):
    try:
        img_b, extension = return_b_image(image64_string)
        name_file = f'{secure_filename(str(uuid.uuid4()))}.{extension}'
        full_path = os.path.join(directory, name_file)
        fh = open(full_path, "wb")
        fh.write(base64.b64decode(img_b))
        fh.close()
        return name_file
    except Exception as e:
        print(e)
        return False


def return_b_image(img_str):
    img_spt = img_str.split(',')
    if len(img_spt) != 2:
        # raise Exception("Imagen  no válida")
        return False
    info = img_spt[0].split('/')
    if len(info) != 2:
        # raise Exception("Imagen  no válida")
        return False
    if info[0] != "data:image":
        # raise Exception("data debe ser image")
        return False
    extension_base = info[1].split(';')
    if extension_base[0] not in ALLOWED_IMG_EXTENSIONS:
        # raise Exception("Extensión no permitida")
        return False
    if extension_base[1] not in ALLOWED_IMG_ENCODING:
        # raise Exception("Extensión no permitida")
        return False
    return img_spt[1].encode(), extension_base[0]


def checkUrlPhotos(imageUrls, mode, directory, internalSku):
    try:
        if mode == 0:
            if not has_duplicate_in_list(imageUrls):
                urlsNoValidas = ''
                for url in imageUrls:
                    if not checkValidImageFromUrl(url):
                        urlsNoValidas = ((f'{urlsNoValidas}{url}')
                                         if urlsNoValidas == ''
                                         else (f'{urlsNoValidas}, {url}'))
                if urlsNoValidas == '':
                    success = imageUrls
                else:
                    success = f'Imágenes no válidas: {urlsNoValidas}'
            else:
                success = 'No puedes guardar la misma url mas de una vez'
        else:
            success = [None] * mode
            imageNames = [imageUrl["img"] for imageUrl in imageUrls]
            if not has_duplicate_in_list(imageNames):
                urlsNoValidas = ''
                for objeto in imageUrls:
                    url = objeto["img"]
                    if "http://" in url or "https://" in url:
                        pass
                    else:
                        file_name = save_b64_img(url, directory)
                        if file_name:
                            url = os.path.join(internalSku, file_name)
                        else:
                            url_rep = f'{url[0:20]}...'
                            urlsNoValidas = ((f'{urlsNoValidas}{url_rep}') if url_rep == '' else (f'{urlsNoValidas}, {url_rep}'))
                    if urlsNoValidas == '':
                        success[objeto["position"]] = url
                    else:
                        success = f'Imágenes no válidas: {urlsNoValidas}'
                        break
                    if not checkValidImageFromUrl(url):
                        urlsNoValidas = ((f'{urlsNoValidas}{url}')
                                         if urlsNoValidas == ''
                                         else (f'{urlsNoValidas}, {url}'))
                    if urlsNoValidas == '':
                        success[objeto["position"]] = url
                    else:
                        success = f'Imágenes no válidas: {urlsNoValidas}'
                        break
            else:
                success = 'No puedes guardar la misma url mas de una vez'
    except Exception as e:
        success = f'Ocurrio un error al guardar las imagenes url:{str(e)}'
    finally:
        return success
    # check if items are unique


def has_duplicate_in_list(anylist):
    return len(anylist) != len(set(anylist))


def has_duplicate_dicts__by_key(dict_list, key):
    return len(dict_list) != len(set(d[key] for d in dict_list))


def logearMensaje(mensajeCompleto, objetoDeError, tipoDeError):
    if mensajeCompleto == '':
        mensajeCompleto = (f'{mensajeCompleto}{objetoDeError} : {tipoDeError}')
    else:
        mensajeCompleto = (f'{mensajeCompleto}{objetoDeError} : {tipoDeError}')
    return mensajeCompleto


def allowed_file_massive_update(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower(
           ) in ALLOWED_MASSIVE_UPDATE_EXTENSIONS


def allowed_name_massive_update(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[0] == MASIVE_UPDATE_FILE_NAME


def allowed_file_invoices(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS_INVOICES


def allowed_name_invoices(filename):
    return '.' in filename and \
           len(filename.rsplit('.', 1)[0].lower()) > 0


def validateSecurePassword(password):
    reasons = []

    # Minimum length of 8 characters
    if len(password) < 8:
        reasons.append("debe tener por lo menos 8 caracteres")

    # Contains at least one lowercase letter
    if not re.search("[a-z]", password):
        reasons.append("debe tener por lo menos una letra minúsculas")

    # Contains at least one uppercase letter
    if not re.search("[A-Z]", password):
        reasons.append("debe tener por lo menos una letra mayúscula")

    # Contains at least one digit
    if not re.search("[0-9]", password):
        reasons.append("debe tener por lo menos un dígito")

    # Contains at least one special character
    if not re.search(r"[!#$%&'()*+,-./:;<=>?@[\]^_`{|}~]", password):
        reasons.append("debe tener por lo menos un caracter especial")

    # Passes all checks
    if len(reasons) == 0:
        return True, ""
    else:
        delimiter = ', '
        return False, f'La contraseña {delimiter.join(reasons)}'


def is_valid_email(email):
    """
    Returns True if the given email address is valid, False otherwise.
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def is_valid_name(name):
    """
    Returns True if the given name is valid, False otherwise.
    """
    pattern = r'^[\w\s\-\'À-ÖØ-öø-ÿ]+$'

    if re.match(pattern, name) is None:
        return "El nombre no tiene la estructura correcta"
    return None

def is_valid_rfc(rfc):
    """
    Returns True if the given rfc is valid, False otherwise.
    """
    pattern = r'^[A-Za-zñÑ&]{3,4}\d{6}\w{3}$'
    if re.match(pattern, rfc) is None:
        return "El RFC no tiene la estructura correcta"
    return None


def is_valid_phonenumber(phonenumber):
    """
    Returns True if the given phonenumber is valid, False otherwise.
    """
    pattern = r'^((\+\d{1,3}\s?)?((\(\d{3}\)\s?)|(\d{3})(\s|-?))(\d{3}(\s|-?))(\d{4})(\s?(([E|e]xt[:|.|]?)|x|X)(\s?\d+))?)$'
    if re.match(pattern, phonenumber) is None:
        return "El numero de telefono no tiene la estructura correcta"
    return None


def is_valid_uuid(val):
    """
    Returns True if the given uuid is valid, False otherwise.
    """
    try:
        uuid.UUID(str(val))
        return None
    except ValueError:
        return "El uuid no tiene la estructura correcta"


#####################
# def valid_int()
# def mrf(value, typo, required):
#     if required:
#         typo(value)
#######################
def manage_request_field(*args, field_name=None, container_dict=None, validation_function=None, isRequired=True):
    value_request = container_dict.get(field_name)
    # if not isRequired and (value_request is None):
    if not isRequired and (value_request is None or value_request == ''):
        return None
    # if isRequired and value_request is None:
    if isRequired and (value_request is None or value_request == ''):
        abort(make_response(jsonify({'errores': f'{field_name} obligario'}), 400))
    args = args + (value_request,)
    error = validation_function(*args)
    if error:
        abort(make_response(jsonify({'errores': f"{field_name} no válido: {error}"}), 400))  # Revisar status 400
    return value_request


def manage_field(field_name=None, container_dict=None,
                 sqlAlchemy_object=None, object_attribute_name=None,
                 sqlAlchemy_class=None, validation_funtion=None):
    alerta_campo = ''
    errores_campo = ''
    if field_name in container_dict:
        field_var = container_dict.get(field_name)
        if field_var:
            validated = validation_funtion(field_var, sqlAlchemy_class, object_attribute_name)
            if validated:
                setattr(sqlAlchemy_object, object_attribute_name, field_var)
            else:
                errores_campo = f'{field_name} inválido'
        else:
            alerta_campo = f'{field_name} vacio'
    return alerta_campo, errores_campo


"""def validate_if_object_exists(session, field_name, variable_type, info_variable, sqlAlchemy_class):
    ""
    Check if sqlAlchemy object exist

    Arguments:
        session {str} -- field name to be check
        field_name {str} -- type of variable for info_variable (can be "dict" or "native")
        variable_type {dict or native object as int, str} -- variable that contain info to check
        info_variable
    Keyword arguments:

    Return:
        Return None
    ""
    if variable_type == 'dict':
        field_data = info_variable.get(field_name)
    elif variable_type == 'native':
        field_data = info_variable
    else:
        raise Exception('variable_type debe ser "dict" o "native"')
    if field_data is None:
        raise Exception(f'{field_name} obligario')
    try:
        field_data = inspect(sqlAlchemy_class).mapper.primary_key[0].type.python_type(field_data)
    except Exception as e:
        raise Exception(f"El tipo de dato ingresado no corresponde al de la llave primaria: {str(e)}")
    obj_from_db = session.get(sqlAlchemy_class, field_data)
    if not obj_from_db:
        raise Exception(f'{sqlAlchemy_class.__name__} con id {field_data} no existe')
    return obj_from_db"""

def validate_if_object_exists(session, field_name, variable_type, info_variable, sqlAlchemy_class):
    """
    Check if sqlAlchemy object exist

    Arguments:
        session {str} -- field name to be check
        field_name {str} -- type of variable for info_variable (can be "dict" or "native")
        variable_type {dict or native object as int, str} -- variable that contain info to check
        info_variable
    Keyword arguments:

    Return:
        Return None
    """
    if variable_type == 'dict':
        field_data = info_variable.get(field_name)
    elif variable_type == 'native':
        field_data = info_variable
    else:
        raise Exception('variable_type debe ser "dict" o "native"')

    obj_from_db = session.get(sqlAlchemy_class, field_data)
    if not obj_from_db:
        abort(make_response(jsonify({'message': f'{sqlAlchemy_class.__name__} no encontrado'}), 404))
    return obj_from_db

def manage_request_field_patch(*args, field_name=None, container_dict=None, validation_function=None):
    if field_name not in container_dict:
        return None
    value_request = container_dict.get(field_name)
    args = args + (value_request,)
    if not validation_function(*args):
        raise Exception(f"{field_name} no válido")
    return value_request


def check_len_sql_column(column, info_request):
    length = column.property.columns[0].type.length    
    if not (len(str(info_request)) > 0 and len(str(info_request)) < length):
        return f"no debe ser vacio ni contener más de {length} caracteres"
    return None

def check_paymentMethod(paymentMethod):
    if paymentMethod != 'PUE' and paymentMethod != 'PPD':
        return "Metodo de pago no válido"
    return None


def check_currency(currency):
    if currency != 'MXN' and currency != 'USD':
        return "Moneda no válida"
    return None


def is_valid_date(date_str):
    try:
        if bool(parser.parse(date_str)):
            return None
        else:
            return "Fecha invalida"
    except ValueError:
        return "Fecha invalida"


def get_cost_from_string(cadena):
    cadena = str(cadena)
    num_extracted = get_re_extract(NUMERIC_RE, cadena)
    if num_extracted:
        error = revisarFloatPositivo(num_extracted)
        if error:
            return False
        else:
            return float(num_extracted)
    else:
        return False


def get_re_extract(expresion, cadena):
    try:
        matches = re.findall(expresion, cadena)
        if len(matches) != 1:
            raise Exception()
        return matches[0]
    except Exception as e:
        print(str(e))
        return False


def validar_valores_order(num_order_values, n):
    try:
        integer_list = [int(x) for x in num_order_values]
        if set(integer_list) == set(range(1, n + 1)):
            return integer_list
    except ValueError:
        return False


def check_ids_internal_status(num_order_keys, internal_statuses, nameColumnId):
    try:
        internal_statuses_ids = [getattr(internal_status, nameColumnId) for internal_status in internal_statuses]
        num_order_keys_ints = [int(num_order_key) for num_order_key in num_order_keys]
        if set(internal_statuses_ids) == set(num_order_keys_ints):
            return num_order_keys_ints
    except ValueError:
        return False


def update_order_nums(internal_statuses, new_order_to_status, nameColumnId):
    try:
        for internal_status in internal_statuses:
            new_order_num = new_order_to_status[str(getattr(internal_status, nameColumnId))]
            internal_status.OrderNum = new_order_num
        return True
    except Exception as e:
        print(str(e))
        return False


def validate_date(date_str, format="%Y-%m-%d"):
    try:
        # Try to create a datetime object using the specified format
        datetime.strptime(date_str, format)
        return True
    except ValueError:
        # If the creation of the datetime object fails, the date is not valid
        return False


def return_amount_changer(amount_changer_list,
                          amount_changer_name,
                          amount_changer_type_name):
    for amount_changer_obj in amount_changer_list:
        if (amount_changer_obj.Name == amount_changer_name and amount_changer_obj.AmountChangerType.Name == amount_changer_type_name):
            return amount_changer_obj
    return None


def commit_catching_unique_constraint(session):
    try:
        session.commit()
    except Exception as e:
        try:
            error = e.orig.args[0]
            unique_name = error[error.find('("') + 2:error.find('")')]
        except Exception as e2:
            print(e2)
            unique_name = "Columna"
        if e.orig.pgcode == "23505":
            abort(make_response(jsonify({'errores': f"{unique_name} ya existe"}), 400))
        else:
            raise Exception(e.orig.args[0])


def commit_catching_dependency_rule(session):
    try:
        session.commit()
        print('commiteed')
    except AssertionError as assertionError:
        print(str('assertionError'))
        print(str(assertionError))
        print(str('assertionError'))

        raise Exception("No es posible eliminar este objeto")
    except Exception as e:
        raise Exception(f"Error desconocido:{str(e)}")


def manage_delete_product(session, sql_alchemy_class, id):
    try:
        object = session.query(sql_alchemy_class).get(id)
        session.delete(object)
        session.commit()
        return {"mensaje": "Produto eliminado exitosamente"}, 200
    except UnmappedInstanceError as unmappedInstanceError:
        print(unmappedInstanceError)
        return {"errores": f"{sql_alchemy_class.__name__} no encontrado"}, 404
    except AssertionError as assertionError:
        print(str('assertionError'))
        print(str(assertionError))
        print(str('assertionError'))
        return {"errores": "El producto no pudo ser eliminado debido a que tiene historial de movimientos en stock"}, 409
    except Exception as e:
        return {"errores": f"Error desconocido:{str(e)}"}, 500


def find_object_in_list(attr_id, object_list, attr_name):
    """
    Return ProductInDirectSale whose id corresponds to direct_sale_id, otherwise return None

    Arguments:
        id {int} -- id to search
        object_list {list[sqlAlchemy objects]} -- list of sqlalchemy objects sale item

    Keyword arguments:

    Return:
        SqlAlchemy object or None -- Return  SqlAlchemy object if id is founded, otherwise return None
    """
    # Chek if id is an positive integer
    error = revisarIntPositivo(attr_id)
    if error:
        return None
    else:
        id = int(attr_id)
    # Loop for each product_in_direct_sale.Id to find direct_sale_id
    for object in object_list:
        if id == getattr(object, attr_name):
            return object
    return None


def my_decorator_http_manage(func):
    def wrapper_func(*args, **kwargs):
        for i in range(7):
            try:
                session = ScopedSession()
                kwargs['session'] = session
                return func(*args, **kwargs)
            except OperationalError as e:
                print(i)
                if i == 6:
                    raise e
            except Exception as e:
                session.rollback()
                raise e
            finally:
                session.close()
    return wrapper_func

# bring a session and check if a param role is allowed,return role for grainer manage
def my_decorator_http_manage3(allowed_roles=[]):
    # valida y retorna el rol
    def decorator(func):
        def wrapper_func(*args, **kwargs):
            for i in range(7):
                try:
                    session = ScopedSession()
                    claims = get_jwt()
                    role = claims.get('role')
                    if role not in allowed_roles:
                        abort(make_response(jsonify({'errores': 'No tienes permisos para ver esta información'}), 403))
                    kwargs['session'] = session
                    kwargs['role'] = role
                    return func(*args, **kwargs)
                except OperationalError as e:
                    print(i)
                    if i == 6:
                        raise e
                except Exception as e:
                    session.rollback()
                    raise e
                finally:
                    session.close()
        return wrapper_func
    return decorator

# bring a session and check if a param role is allowed,return claims for get more user info
def my_decorator_http_manage4(allowed_roles=[]):
    # valida y retorna el rol
    def decorator(func):
        def wrapper_func(*args, **kwargs):
            for i in range(7):  
                try:
                    session = ScopedSession()
                    claims = get_jwt()
                    role = claims.get('role')
                    if role not in allowed_roles:
                        print('abort')
                    kwargs['session'] = session
                    kwargs['claims'] = claims
                    return func(*args, **kwargs)
                except OperationalError as e:
                    if i == 6:
                        raise e
                except Exception as e:
                    session.rollback()
                    raise e
                finally:
                    session.close()
        return wrapper_func
    return decorator

# bring a session and check if a param role is allowed
def my_decorator_http_manage2(allowed_roles=[]):
    # valida el rol y retorna la session
    def decorator(func):
        def wrapper_func(*args, **kwargs):
            for i in range(7):
                try:
                    session = ScopedSession()
                    claims = get_jwt()
                    role = claims.get('role')
                    if role not in allowed_roles:
                        abort(make_response(jsonify({'errores': 'No tienes permisos para ver esta información'}), 403))
                    kwargs['session'] = session
                    return func(*args, **kwargs)
                except OperationalError as e:
                    if i == 6:
                        raise e
                except Exception as e:
                    session.rollback()
                    raise e
                finally:
                    session.close()
        return wrapper_func
    return decorator


# bring a session and no check role just pass claims
def my_decorator_http_manage2_no_check():
    # Envuelve la funcion por algun error y retorna la session y los claims sin validar el rol
    def decorator(func):
        def wrapper_func(*args, **kwargs):
            for i in range(7):
                try:
                    claims = get_jwt()
                    session = ScopedSession()
                    kwargs['session'] = session
                    kwargs['claims'] = claims
                    return func(*args, **kwargs)
                except OperationalError as e:
                    if i == 6:
                        raise e
                except Exception as e:
                    session.rollback()
                    raise e
                finally:
                    session.close()
        return wrapper_func
    return decorator


def my_decorator_http_returnSession():
    def decorator(func):
        def wrapper_func(*args, **kwargs):
            for i in range(7):
                try:
                    session = ScopedSession()
                    kwargs['session'] = session
                    return func(*args, **kwargs)
                except OperationalError as e:
                    if i == 6:
                        raise e
                except Exception as e:
                    session.rollback()
                    raise e
                finally:
                    session.close()
        return wrapper_func
    return decorator


def get_info_from_json(request):
    try:
        return request.get_json(force=True)
    except Exception as e:
        abort(make_response(jsonify({'errores': 'Los datos deben ingresar en formato JSON'}), 400))


def catched_json_loads(object_string):
    try:
        return json.loads(object_string)
    except Exception as e:
        raise Exception(
            f'Los datos no se encuentran en formato JSON: {str(e)}')


def get_nested_value_dict(dicti, keys_list):
    sub = dicti
    for key in keys_list[:-1]:
        sub = sub[key]
    return sub
