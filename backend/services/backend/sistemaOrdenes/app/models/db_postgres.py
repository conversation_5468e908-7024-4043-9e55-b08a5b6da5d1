from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool
from sistemaOrdenes.app import config
import logging

# Set the SQLAlchemy logger to DEBUG level
logging.basicConfig()
logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)
# db_url = "**************************************************/turtleSCGP2"
# db_url = "*************************************************/turtleSCGP2"
# db_url = "postgresql://postgres:pass@localhost:5432/prueba"
db_url = config.Config.DATABASE_URL
engine = create_engine(db_url, poolclass=QueuePool)
Session = sessionmaker(bind=engine)
ScopedSession = scoped_session(Session)
# ScopedSession = Session
Base = declarative_base()
