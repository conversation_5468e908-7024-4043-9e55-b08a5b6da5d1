# for postgresql
from . import db
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, \
    String, Float, DateTime, UniqueConstraint, case, \
    Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.schema import ForeignKeyConstraint
from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.app import config
from datetime import datetime, timezone
BACKEND_URL = config.Config.BACKEND_URL


class ProductBase(db.Base):
    # Table name in database
    __tablename__ = 'ProductBase'
    # Columns
    InternalBaseSku = Column(String(80), primary_key=True, nullable=False)
    Brand = Column(String(50), nullable=False)
    Model = Column(String(80), nullable=False)
    Description = Column(String(300), nullable=False)
    Upc = Column(String(15), nullable=True)
    LongDescription = Column(String(3000), nullable=True)
    SatCode = Column(String(8), nullable=True)
    UnitCode = Column(String(3), nullable=True)
    Video = Column(String(150), nullable=True)
    # RelationShips
    ProductBaseProductDimensions = relationship(
        "ProductBaseProductDimension",
        uselist=False, cascade="all, delete-orphan")
    ProductBaseShippingDimensions = relationship(
        "ProductBaseShippingDimension",
        uselist=False, cascade="all, delete-orphan")
    ProductBasePhotos = relationship(
        "ProductBasePhoto",
        uselist=True, cascade="all, delete-orphan")
    ProductBaseAttributes = relationship(
        "ProductBaseAttribute", uselist=True, cascade="all, delete-orphan")
    ProductBaseTags = relationship(
        "ProductBaseTag", uselist=True, cascade="all, delete-orphan")
    Products = relationship(
        "Product", uselist=True,
        back_populates='ProductBase',
        # lazy='dynamic',
        cascade="all, delete-orphan")
    ProductBase_CustomVariations = relationship(
        "ProductBase_CustomVariation",
        uselist=True, cascade="all, delete-orphan")
    ProductsInEntryDocument = relationship("ProductInEntryDocument")

    # Initializing
    # crrate internalSKu at__init_
    def __init__(self, internalBaseSku=None, brand=None, model=None,
                 description=None, upc=None, longDescription=None,
                 satCode=None, unitCode=None, video=None,
                 productBaseShippingDimensions=None,
                 productBaseProductDimensions=None,
                 productBasePhotos=None):
        self.InternalBaseSku = internalBaseSku
        self.Brand = brand
        self.Model = model
        self.Description = description
        self.Upc = upc
        self.LongDescription = longDescription
        self.SatCode = satCode
        self.UnitCode = unitCode
        self.Video = video
        self.productBaseShippingDimensions = productBaseShippingDimensions
        self.productBaseProductDimensions = productBaseProductDimensions
        self.productBasePhotos = productBasePhotos
    # String representations

    def __repr__(self):
        return (
            f'ProductBase({self.InternalBaseSku},'
            f'{self.Brand},'
            f'{self.Model},'
            f'{self.Description},'
            f'{self.Upc},'
            f'{self.LongDescription},'
            f'{self.SatCode},'
            f'{self.UnitCode},'
            f'{self.Video})'
        )

    def serialize(self, scope={}):
        product_base = {
            "internalBaseSku": self.InternalBaseSku,
            "brand": self.Brand,
            "model": self.Model,
            "description": self.Description,
            "upc": self.Upc,
            "longDescription": self.LongDescription,
            "satCode": self.SatCode,
            "unitCode": self.UnitCode,
            "video": self.Video,
        }
        if 'dimensions' in scope:
            product_base["productBaseShippingDimensions"] = {} if self.ProductBaseShippingDimensions is None else self.ProductBaseShippingDimensions.serialize(scope=scope['dimensions'])
            product_base["productBaseProductDimensions"] = {} if self.ProductBaseProductDimensions is None else self.ProductBaseProductDimensions.serialize(scope=scope['dimensions'])
        if 'photos' in scope:
            product_base["productBasePhotos"] = [productBasePhoto.serialize(scope=scope['photos']) for productBasePhoto in self.ProductBasePhotos]
        if 'attributes' in scope:
            product_base["productBaseAttributes"] = [productBaseAttribute.serialize(scope=scope['attributes']) for productBaseAttribute in self.ProductBaseAttributes]
        if 'tags' in scope:
            product_base["productBaseTags"] = [productBaseTag.serialize(scope=scope['tags']) for productBaseTag in self.ProductBaseTags]
        if 'productBase_CustomVariations' in scope:
            product_base["productBase_CustomVariations"] = [productBase_CustomVariation.serialize(scope=scope['productBase_CustomVariations']) for productBase_CustomVariation in self.ProductBase_CustomVariations]
        if 'products' in scope:
            product_base["products"] = [product.serialize(scope=scope['products']) for product in self.Products]
        return product_base

    def serializeWithStock(self, stoks):
        return {
            "internalBaseSku": self.InternalBaseSku,
            "brand": self.Brand,
            "model": self.Model,
            "description": self.Description,
            "upc": self.Upc,
            "longDescription": self.LongDescription,
            "satCode": self.SatCode,
            "unitCode": self.UnitCode,
            "video": self.Video,
            "productBaseShippingDimensions": (
                {}
                if self.ProductBaseShippingDimensions is None
                else self.ProductBaseShippingDimensions.serialize()),
            "productBaseProductDimensions": (
                {}
                if self.ProductBaseProductDimensions is None
                else self.ProductBaseProductDimensions.serialize()),
            "productBasePhotos": [productBasePhoto.serialize()
                                  for productBasePhoto
                                  in self.ProductBasePhotos],
            "productBaseAttributes": [productBaseAttribute.serialize()
                                      for productBaseAttribute
                                      in self.ProductBaseAttributes],
            "products": [product.serialize()
                         for product in self.Products],
            "productBase_CustomVariations": [
                productBase_CustomVariation.serialize()
                for productBase_CustomVariation
                in self.ProductBase_CustomVariations],
            "productBaseTags": [productBaseTag.serialize()
                                for productBaseTag
                                in self.ProductBaseTags],
            "stock": stoks,
        }


class ProductBaseProductDimension(db.Base):
    # Table name in database
    __tablename__ = 'ProductBaseProductDimension'
    # Columns
    InternalBaseSku = Column(String(80), ForeignKey(
        "ProductBase.InternalBaseSku"), primary_key=True, nullable=False)
    Length = Column(Float, nullable=True)
    Width = Column(Float, nullable=True)
    Height = Column(Float, nullable=True)
    Weight = Column(Float, nullable=True)
    # RelationShips

    # Initializing
    def __init__(self, internalBaseSku=None, length=None,
                 width=None, height=None, weight=None):
        self.InternalBaseSku = internalBaseSku
        self.Length = length
        self.Width = width
        self.Height = height
        self.Weight = weight
    # String representations

    def __repr__(self):
        return (
            f'ProductBaseProductDimension({self.InternalBaseSku},'
            f'{self.Length},'
            f'{self.Width},'
            f'{self.Height},'
            f'{self.Weight})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        productBaseProductDimension = {
            'length': self.Length,
            'width': self.Width,
            'height': self.Height,
            'weight': self.Weight
        }
        if 'internalBaseSku' in scope:
            productBaseProductDimension['internalBaseSku'] = self.InternalBaseSku
        return productBaseProductDimension


class ProductBaseShippingDimension(db.Base):
    # Table name in database
    __tablename__ = 'ProductBaseShippingDimension'
    # Columns
    InternalBaseSku = Column(String(80), ForeignKey(
        "ProductBase.InternalBaseSku"), primary_key=True, nullable=False)
    Length = Column(Float, nullable=True)
    Width = Column(Float, nullable=True)
    Height = Column(Float, nullable=True)
    Weight = Column(Float, nullable=True)
    # RelationShips

    # Initializing
    def __init__(self, internalBaseSku=None, length=None,
                 width=None, height=None, weight=None):
        self.InternalBaseSku = internalBaseSku
        self.Length = length
        self.Width = width
        self.Height = height
        self.Weight = weight
    # String representations

    def __repr__(self):
        return (
            f'ProductBaseShippingDimension({self.InternalBaseSku},'
            f'{self.Length},'
            f'{self.Width},'
            f'{self.Height},'
            f'{self.Weight})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        productBaseShippingDimension = {
            'length': self.Length,
            'width': self.Width,
            'height': self.Height,
            'weight': self.Weight
        }
        if 'internalBaseSku' in scope:
            productBaseShippingDimension['internalBaseSku'] = self.InternalBaseSku
        return productBaseShippingDimension


class ProductBasePhoto(db.Base):
    # Table name in database
    __tablename__ = 'ProductBasePhoto'
    # Columns
    InternalBaseSku = Column(String(80), ForeignKey(
        "ProductBase.InternalBaseSku"), primary_key=True, nullable=False)
    URLPhoto = Column(String(300), primary_key=True, nullable=False)
    ItemNumber = Column(Integer, nullable=False)
    # RelationShips

    # Initializing
    def __init__(self, internalBaseSku=None, URLPhoto=None, ItemNumber=None):
        self.InternalBaseSku = internalBaseSku
        self.URLPhoto = URLPhoto
        self.ItemNumber = ItemNumber
    # String representations

    def __repr__(self):
        return (
            f'ProductBasePhoto({self.InternalBaseSku},'
            f'{self.URLPhoto},'
            f'{self.ItemNumber})')
    # serialize (to json)

    def serialize(self, scope={}):
        productBasePhoto = {
            'URLPhoto': (self.URLPhoto if ownUtils.is_valid_url(self.URLPhoto) else ownUtils.imagePathToImageURL(self.URLPhoto, 'products')),
            'itemNumber': self.ItemNumber
        }
        if 'internalBaseSku' in scope:
            productBasePhoto['internalBaseSku'] = self.InternalBaseSku
        return productBasePhoto


class ProductBaseAttribute(db.Base):
    # Table name in database
    __tablename__ = 'ProductBaseAttribute'
    # Columns
    InternalBaseSku = Column(String(80), ForeignKey(
        "ProductBase.InternalBaseSku"), primary_key=True, nullable=False)
    AttributeName = Column(String(50), nullable=False, primary_key=True)
    AttributeValue = Column(String(150), nullable=False, primary_key=True)
    # RelationShips

    # Initializing
    def __init__(self, attributeName, attributeValue):
        self.AttributeName = attributeName
        self.AttributeValue = attributeValue
    # String representations

    def __repr__(self):
        return (
            f'ProductBaseAttribute({self.InternalBaseSku},'
            f'{self.AttributeName},'
            f'{self.AttributeValue})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        productBaseAttribute = {
            'attributeName': self.AttributeName,
            'attributeValue': self.AttributeValue
        }
        if 'internalBaseSku' in scope:
            productBaseAttribute['internalBaseSku'] = self.InternalBaseSku,
        return productBaseAttribute


class ProductBaseTag(db.Base):
    # Table name in database
    __tablename__ = 'ProductBaseTag'
    # Columns
    InternalBaseSku = Column(String(80), ForeignKey(
        "ProductBase.InternalBaseSku"), primary_key=True, nullable=False)
    TagValue = Column(String(50), nullable=False, primary_key=True)
    # RelationShips

    # Initializing
    def __init__(self, tagValue):
        self.TagValue = tagValue
    # String representations

    def __repr__(self):
        return f'ProductBaseTag({self.InternalBaseSku},{self.TagValue})'
    # serialize (to json)

    def serialize(self, scope={}):
        productBaseTag = {
            'tagValue': self.TagValue
        }
        if 'internalBaseSku' in scope:
            productBaseTag['internalBaseSku'] = self.InternalBaseSku
        return productBaseTag


class Product(db.Base):
    # Table name in database
    __tablename__ = 'Product'
    # Columns
    InternalSku = Column(String(150), primary_key=True, nullable=False)
    InternalBaseSku = Column(String(80), ForeignKey(
        "ProductBase.InternalBaseSku"), nullable=False)
    VariationTitle = Column(String(150), nullable=True)
    VariationSku = Column(String(20), nullable=True)
    # RelationShips
    ProductBase = relationship('ProductBase', back_populates='Products')
    ProductProductDimensions = relationship(
        "ProductProductDimension",
        uselist=False, cascade="all, delete-orphan")
    ProductShippingDimensions = relationship(
        "ProductShippingDimension",
        uselist=False, cascade="all, delete-orphan")
    ProductPhotos = relationship(
        "ProductPhoto", uselist=True, cascade="all, delete-orphan")
    Product_SupplierStores = relationship(
        "Product_SupplierStore", uselist=True)
    Product_Stores = relationship(
        "Product_Store", uselist=True)
    ProductVariationCustoms = relationship(
        "ProductVariationCustom", uselist=True, cascade="all, delete-orphan")
    Kits_Product = relationship("Kit_Product")

    # Initializing
    def __init__(self, internalSku=None):
        self.InternalSku = internalSku

    # String representations
    def __repr__(self):
        return (
            f'Product({self.InternalSku},'
            f'{self.InternalBaseSku},'
            f'{self.VariationTitle},'
            f'{self.VariationSku})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        product = {
            'internalSku': self.InternalSku,
            'internalBaseSku': self.InternalBaseSku,
            'variationDescription': self.VariationTitle,
            'variationSku': self.VariationSku,
        }
        if 'dimensions' in scope:
            product["productShippingDimensions"] = {} if self.ProductShippingDimensions is None else self.ProductShippingDimensions.serialize(scope=scope['dimensions'])
            product["productProductDimensions"] = {} if self.ProductProductDimensions is None else self.ProductProductDimensions.serialize(scope=scope['dimensions'])
        if 'photos' in scope:
            product['productPhotos'] = [productPhoto.serialize(scope=scope['photos']) for productPhoto in self.ProductPhotos]
        if 'productVariationCustom' in scope:
            product['productVariationCustom'] = [productVariationCustom.serialize(scope=scope['productVariationCustom']) for productVariationCustom in self.ProductVariationCustoms]
        if 'product_stores' in scope:
            product["product_stores"] = [product_store.serialize(scope=scope['product_stores']) for product_store in self.Product_Stores]
        if 'product_supplierStores' in scope:
            product["product_supplierStores"] = [product_supplierStore.serialize(scope=scope['product_supplierStores']) for product_supplierStore in self.Product_SupplierStores]
        if 'productBase' in scope:
            product['productBase'] = self.ProductBase.serialize(scope={})
        if 'extras' in scope:
            extras = scope['extras']
            if 'productImage' in extras:
                product['productImage'] = self.return_main_photo()
            if 'productDescription' in extras:
                product['productDescription'] = self.return_description_to_show()
        return product

    def serialize_table_info_product(self):
        return {
            'internalSku': self.InternalSku,
            'main_photo': self.return_main_photo(),
            'variationDescription': self.return_description_to_show(),
            "ProductVariationCustom": [
                {
                    'customVariationValue':
                        prod.CustomVariationValue.serialize(),
                    'custonVariation':
                        prod.CustomVariationValue.CustomVariation.serialize()
                }
                for prod in self.ProductVariationCustom],
            'productBase': self.ProductBase.serialize_basic_data()
        }

    def return_photos_to_show(self):
        if len(self.ProductPhotos) > 0:
            photos = self.ProductPhotos
        elif len(self.ProductBase.ProductBasePhotos) > 0:
            photos = self.ProductBase.ProductBasePhotos
        else:
            return []
        return [ownUtils.return_image_url(photo.URLPhoto) for photo in photos]

    def return_main_photo(self):
        print('main photo---')
        if len(self.ProductPhotos):
            photos = self.ProductPhotos
        elif len(self.ProductBase.ProductBasePhotos) > 0:
            photos = self.ProductBase.ProductBasePhotos
        else:
            return None
        print('main-fhotos')
        print(photos)
        print('main-fhotos')
        main_photo = ownUtils.return_most_relevant_photo(photos)
        print('main-fhoto--')
        print(main_photo)
        print('main-fhot--')
        return main_photo

    def return_dimensions_to_show(self):
        pass

    def return_description_to_show(self):
        return self.VariationTitle if self.VariationTitle else self.ProductBase.Description


class CustomVariation(db.Base):
    # Table name in database
    __tablename__ = 'CustomVariation'
    # Columns
    CustomVariationId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    CustomVariationName = Column(String(50), unique=True, nullable=False)
    # RelationShips
    ProductBase_CustomVariations = relationship(
        "ProductBase_CustomVariation",
        uselist=True, cascade="all, delete-orphan")
    CustomVariationValues = relationship(
        "CustomVariationValue",
        uselist=True, cascade="all, delete-orphan")

    # Initializing

    def __init__(self, customvariationName):
        self.CustomVariationName = customvariationName
    # String representations

    def __repr__(self):
        return (
            f'CustomVariation({self.CustomVariationId},'
            f'{self.CustomVariationName})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        customVariation = {
            'customVariationId': self.CustomVariationId,
            'customVariationName': self.CustomVariationName
        }
        if 'customVariationValues' in scope:
            customVariation['customVariationValues'] = [customVariationValue.serialize(scope=scope['customVariationValues']) for customVariationValue in self.CustomVariationValues]
        return customVariation


class CustomVariationValue(db.Base):
    # Table name in database
    __tablename__ = 'CustomVariationValue'
    # Columns
    CustomVariationValueId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    CustomVariationId = Column(Integer, ForeignKey(
        "CustomVariation.CustomVariationId"), nullable=False)
    CustomVariationValue = Column(String(50), nullable=False)
    # RelationShips
    ProductVariationCustoms = relationship("ProductVariationCustom")
    CustomVariation = relationship(
        "CustomVariation", back_populates='CustomVariationValues')

    # Initializing
    def __init__(self, customvariationValue):
        self.CustomVariationValue = customvariationValue

    # String representations
    def __repr__(self):
        return (
            f'CustomVariationValue({self.CustomVariationValueId},'
            f'{self.CustomVariationId},'
            f'{self.CustomVariationValue})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        customVariationValue = {
            'customVariationValueId': self.CustomVariationValueId,
            'customVariationValue': self.CustomVariationValue
        }
        if 'productVariationCustoms' in scope:
            customVariationValue['productVariationCustoms'] = [productVariationCustom.serialize(scope=scope['productVariationCustoms']) for productVariationCustom in self.ProductVariationCustoms]
        if 'customVariation' in scope:
            customVariationValue['customVariation'] = self.CustomVariation.serialize(scope=scope['customVariation'])
        return customVariationValue


class ProductBase_CustomVariation(db.Base):
    # Table name in database
    __tablename__ = 'ProductBase_CustomVariation'
    # Columns
    ProductBase_CustomVariationId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    InternalBaseSku = Column(String(80), ForeignKey(
        "ProductBase.InternalBaseSku"), nullable=False)
    CustomVariationId = Column(Integer, ForeignKey(
        "CustomVariation.CustomVariationId"), nullable=False)
    # RelationShips
    ProductBase = relationship(
        "ProductBase", back_populates="ProductBase_CustomVariations")
    CustomVariation = relationship(
        "CustomVariation", back_populates="ProductBase_CustomVariations")
    ProductVariationCustoms = relationship("ProductVariationCustom")

    # Initializing

    # String representations
    def __repr__(self):
        return (
            f'ProductBase_CustomVariation('
            f'{self.ProductBase_CustomVariationId},'
            f'{self.InternalBaseSku},'
            f'{self.CustomVariationId})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        productBase_CustomVariation = {
            'productBase_CustomVariationId':
                self.ProductBase_CustomVariationId,
            # 'internalBaseSku': self.InternalBaseSku,
            # 'customVariationId': self.CustomVariationId
        }
        if 'productBase' in scope:
            productBase_CustomVariation['productBase'] = self.ProductBase.serialize(scope=scope['productBase'])
        if 'customVariation' in scope:
            productBase_CustomVariation['customVariation'] = self.CustomVariation.serialize(scope=scope['customVariation'])
        if 'productVariationCustoms' in scope:
            productBase_CustomVariation['productVariationCustoms'] = [productVariationCustom.serialize(scope=scope['productVariationCustom']) for productVariationCustom in self.ProductVariationCustoms]
        return productBase_CustomVariation


class ProductVariationCustom(db.Base):
    # Table name in database
    __tablename__ = 'ProductVariationCustom'
    # Columns
    InternalSku = Column(String(150), ForeignKey(
        "Product.InternalSku"), primary_key=True, nullable=False)
    ProductBase_CustomVariationId = Column(Integer, ForeignKey(
        "ProductBase_CustomVariation.ProductBase_CustomVariationId"),
        primary_key=True, nullable=False)
    CustomVariationValueId = Column(Integer, ForeignKey(
        "CustomVariationValue.CustomVariationValueId"), nullable=False)
    # RelationShips
    Product = relationship("Product", back_populates="ProductVariationCustoms")
    ProductBase_CustomVariation = relationship(
        "ProductBase_CustomVariation", back_populates="ProductVariationCustoms")
    CustomVariationValue = relationship(
        "CustomVariationValue", back_populates="ProductVariationCustoms")

    # Initializing

    # String representations
    def __repr__(self):
        return (
            f'ProductVariationCustom({self.InternalSku},'
            f'{self.ProductBase_CustomVariationId},'
            f'{self.CustomVariationValueId})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        productVariationCustom = {}
        if 'product' in scope:
            productVariationCustom['product'] = self.Product.serialize(scope=scope['product'])
        if 'customVariationValue' in scope:
            productVariationCustom['customVariationValue'] = self.CustomVariationValue.serialize(scope=scope['customVariationValue'])
        if 'productBase_CustomVariation' in scope:
            productVariationCustom['productBase_CustomVariation'] = self.ProductBase_CustomVariation.serialize(scope=scope['productBase_CustomVariation'])
        return productVariationCustom


class ProductProductDimension(db.Base):
    # Table name in database
    __tablename__ = 'ProductProductDimension'
    # Columns
    InternalSku = Column(String(150), ForeignKey(
        "Product.InternalSku"), primary_key=True, nullable=False)
    Length = Column(Float, nullable=True)
    Width = Column(Float, nullable=True)
    Height = Column(Float, nullable=True)
    Weight = Column(Float, nullable=True)
    # RelationShips

    # Initializing
    def __init__(self, internalSku=None, length=None,
                 width=None, height=None, weight=None):
        self.InternalSku = internalSku
        self.Length = length
        self.Width = width
        self.Height = height
        self.Weight = weight
    # String representations

    def __repr__(self):
        return (
            f'ProductProductDimension({self.InternalSku},'
            f'{self.Length},'
            f'{self.Width},'
            f'{self.Height},'
            f'{self.Weight})'
        )

    def serialize(self, scope={}):
        productProductDimension = {
            'length': self.Length,
            'width': self.Width,
            'height': self.Height,
            'weight': self.Weight
        }
        if 'internalBaseSku' in scope:
            productProductDimension['internalBaseSku'] = self.InternalBaseSku
        return productProductDimension


class ProductShippingDimension(db.Base):
    # Table name in database
    __tablename__ = 'ProductShippingDimension'
    # Columns
    InternalSku = Column(String(150), ForeignKey(
        "Product.InternalSku"), primary_key=True, nullable=False)
    Length = Column(Float, nullable=True)
    Width = Column(Float, nullable=True)
    Height = Column(Float, nullable=True)
    Weight = Column(Float, nullable=True)
    # RelationShips

    # Initializing
    def __init__(self, internalSku=None, length=None,
                 width=None, height=None, weight=None):
        self.InternalSku = internalSku
        self.Length = length
        self.Width = width
        self.Height = height
        self.Weight = weight

    # String representations
    def __repr__(self):
        return (
            f'ProductShippingDimension({self.InternalSku},'
            f'{self.Length},'
            f'{self.Width},'
            f'{self.Height},'
            f'{self.Weight})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        productShippingDimension = {
            'length': self.Length,
            'width': self.Width,
            'height': self.Height,
            'weight': self.Weight
        }
        if 'internalBaseSku' in scope:
            productShippingDimension['internalBaseSku'] = self.InternalBaseSku
        return productShippingDimension


class ProductPhoto(db.Base):
    # Table name in database
    __tablename__ = 'ProductPhoto'
    # Columns
    InternalSku = Column(String(150), ForeignKey(
        "Product.InternalSku"), primary_key=True, nullable=False)
    URLPhoto = Column(String(300), primary_key=True, nullable=False)
    ItemNumber = Column(Integer, nullable=False)
    # RelationShips

    # Initializing
    def __init__(self, URLPhoto, ItemNumber):
        self.URLPhoto = URLPhoto
        self.ItemNumber = ItemNumber
    # String representations

    def __repr__(self):
        return (f'ProductPhoto('
                f'{self.InternalSku},'
                f'{self.URLPhoto},'
                f'{self.ItemNumber})')
    # serialize (to json)

    def serialize(self, scope={}):
        productPhoto = {
            'URLPhoto': (self.URLPhoto
                         if ownUtils.is_valid_url(self.URLPhoto)
                         else ownUtils.imagePathToImageURL(self.URLPhoto, 'products')),
            'itemNumber': self.ItemNumber
        }
        if 'internalBaseSku' in scope:
            productPhoto['internalSku'] = self.InternalSku
        return productPhoto


class Supplier(db.Base):
    # Table name in database
    __tablename__ = 'Supplier'
    # Columns
    RFC = Column(String(13), primary_key=True, nullable=False)
    SupplierName = Column(String(50), nullable=False)
    Email = Column(String(50), nullable=True)
    SalesExecutive = Column(String(50), nullable=True)
    # RelationShips
    SupplierStores = relationship(
        "SupplierStore", back_populates="Supplier", cascade='all, delete')
    SupplierPlatform = relationship(
        "SupplierPlatform", uselist=False, cascade='all, delete')
    # Initializing

    def __init__(self, RFC, supplierName, email, salesExecutive):
        self.RFC = RFC
        self.SupplierName = supplierName
        self.Email = email
        self.SalesExecutive = salesExecutive
    # String representations

    def __repr__(self):
        return (
            f'Supplier({self.RFC},'
            f'{self.SupplierName},'
            f'{self.Email},'
            f'{self.SalesExecutive})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        supplier = {
            'RFC': self.RFC,
            'supplierName': self.SupplierName,
            'email': self.Email,
            'salesExecutive': self.SalesExecutive
        }
        if 'supplierStores' in scope:
            supplier['supplierStores'] = [supplierStore.serialize(scope=scope['supplierStores']) for supplierStore in self.SupplierStores]
        if 'supplierPlatform' in scope:
            supplier['supplierPlatform'] = self.SupplierPlatform(scope=scope['supplierPlatform'])
        return supplier


class Zone(db.Base):
    # Table name in database
    __tablename__ = 'Zone'
    # Columns
    ZoneNumber = Column(Integer, primary_key=True, nullable=False)
    DeliveryTime = Column(String(30), nullable=False)
    # RelationShips
    SupplierStores = relationship("SupplierStore")
    Stores = relationship("Store")

    # Initializing

    def __init__(self, zoneNumber, deliveryTime):
        self.ZoneNumber = zoneNumber
        self.DeliveryTime = deliveryTime
    # String representations

    def __repr__(self):
        return f'Zone({self.ZoneNumber},{self.DeliveryTime})'
    # serialize (to json)

    def serialize(self, scope={}):
        zone = {
            'zoneNumber': self.ZoneNumber,
            'deliveryTime': self.DeliveryTime,
        }
        if 'stores' in scope:
            zone['stores'] = [store.serialize(scope=scope['stores']) for store in self.Stores]
        if 'supplierStores' in scope:
            zone['supplierStores'] = [store.serialize(scope=scope['supplierStores']) for store in self.Stores]
        return zone


class SupplierStore(db.Base):
    # Table name in database
    __tablename__ = 'SupplierStore'
    # Columns
    SupplierStoreId = Column(Integer, primary_key=True,
                             nullable=False, autoincrement=True)
    ZoneNumber = Column(Integer, ForeignKey("Zone.ZoneNumber"), nullable=False)
    RFC = Column(String(13), ForeignKey("Supplier.RFC"), nullable=False)
    StoreName = Column(String(50), nullable=False)
    SupplierAppId = Column(String(50), nullable=True)
    Address = Column(String(200), nullable=True)
    Phone = Column(String(30), nullable=True)
    UrlMaps = Column(String(100), nullable=True)
    # RelationShips
    Zone = relationship("Zone", back_populates="SupplierStores", uselist=False)
    Supplier = relationship(
        "Supplier", back_populates="SupplierStores", uselist=False)
    Product_SupplierStores = relationship(
        "Product_SupplierStore",
        back_populates="SupplierStore", cascade='all, delete')

    # Initializing

    def __init__(self, storeName=None, supplierAppId=None,
                 address=None, phone=None, urlMaps=None):
        self.StoreName = storeName
        self.SupplierAppId = supplierAppId
        self.Address = address
        self.Phone = phone
        self.UrlMaps = urlMaps
    # String representations

    def __repr__(self):
        return (
            f'SupplierStore({self.SupplierStoreId},'
            f'{self.StoreName},'
            f'{self.SupplierAppId},'
            f'{self.Address},'
            f'{self.Phone},'
            f'{self.UrlMaps})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        supplier_store = {
            'id': self.SupplierStoreId,
            'storeName': self.StoreName,
            'supplierAppId': self.SupplierAppId,
            'address': self.Address,
            'phone': self.Phone,
            'urlMaps': self.UrlMaps
        }
        if 'zone' in scope:
            supplier_store['zone'] = self.Zone.serialize(scope=scope['zone'])
        if 'product_supplierStore' in scope:
            supplier_store['product_supplierStore'] = [product_SupplierStore.serialize(scope=scope['product_supplierStore']) for product_SupplierStore in self.Product_SupplierStores]
        if 'supplier' in scope:
            supplier_store['supplier'] = self.Supplier.serialize(scope=scope['supplier'])
        return supplier_store


class Product_SupplierStore(db.Base):
    # Table name in database
    __tablename__ = "Product_SupplierStore"
    # Columns
    InternalSku = Column(String(150), ForeignKey(
        "Product.InternalSku"), primary_key=True, nullable=False)
    SupplierStoreId = Column(Integer, ForeignKey(
        "SupplierStore.SupplierStoreId"), primary_key=True, nullable=False)
    Stock = Column(Float)
    Cost = Column(Float)
    # RelationShips
    # , cascade="all, delete-orphan", single_parent=True
    Product = relationship(
        "Product", back_populates="Product_SupplierStores", uselist=False)
    SupplierStore = relationship(
        "SupplierStore", back_populates="Product_SupplierStores")
    ProductsInDirectSale = relationship("ProductInDirectSale")
    PublicationProducts_Product_SupplierStore = relationship("PublicationProduct_Product_SupplierStore")
    Product_SupplierStore_kit_Products = relationship("Product_SupplierStore_kit_Product")
    # Initializing
    # String representations

    def __repr__(self):
        return f'Product_SupplierStore({self.Stock},{self.Cost})'
    # serialize (to json)

    def serialize(self, scope={}):
        product_supplierStore = {
            'stock': self.Stock,
            'cost': self.Cost
        }
        if 'product' in scope:
            product_supplierStore['product'] = self.Product.serialize(scope=scope['product'])
        if 'supplierStore' in scope:
            product_supplierStore['supplierStore'] = self.SupplierStore.serialize(scope=scope['supplierStore'])
        if 'publicationProducts_product_supplierStore' in scope:
            product_supplierStore['publicationProducts_product_supplierStore'] = [publicationProducts_product_supplierStore.serialize(scope=scope['publicationProducts_product_supplierStore']) for publicationProducts_product_supplierStore in self.PublicationProducts_Product_SupplierStore]
        return product_supplierStore


class Store(db.Base):
    # Table name in database
    __tablename__ = 'Store'
    # Columns
    StoreId = Column(Integer, primary_key=True,
                     nullable=False, autoincrement=True)
    ZoneNumber = Column(Integer, ForeignKey("Zone.ZoneNumber"), nullable=False)
    StoreName = Column(String(100), unique=True, nullable=False)
    StoreDescription = Column(String(400), unique=True, nullable=False)
    UrlMaps = Column(String(500), unique=True, nullable=False)
    Address = Column(String(500), nullable=True)
    Phone = Column(String(30), nullable=True)

    # RelationShips
    Zone = relationship("Zone", back_populates="Stores", uselist=False)
    Product_Stores = relationship("Product_Store", back_populates="Store")
    LocationLevelItems = relationship("LocationLevelItem", order_by="LocationLevelItem.ParentId, LocationLevelItem.Name")

    # Initializing
    def __init__(self, storeName=None, storeDescription=None, address=None, phone=None, urlMaps=None):
        self.StoreName = storeName
        self.StoreDescription = storeDescription
        self.Address = address
        self.Phone = phone
        self.UrlMaps = urlMaps

    # String representations
    def __repr__(self):
        return (
            f'Store({self.StoreId},'
            f'{self.StoreName},'
            f'{self.Address},'
            f'{self.Phone},'
            f'{self.UrlMaps})'
        )

    def insert_location_at_hierarchically(self, list, locationLevelItem):
        for item_list in list:
            if item_list["id"] == locationLevelItem["parentId"]:
                item_list["children"].append(locationLevelItem)
            else:
                self.insert_location_at_hierarchically(item_list["children"], locationLevelItem)

    def get_internal_locations_hierarchically(self, scope={}):
        otros = []
        internal_locations_hierarchically = []
        for locationLevelItem in self.LocationLevelItems:
            locationLevelItem_json = locationLevelItem.serialize(scope=scope)
            locationLevelItem_json['children'] = []
            if locationLevelItem_json["parentId"] is None:
                internal_locations_hierarchically.append(locationLevelItem_json)
            else:
                otros.append(locationLevelItem_json)
        for otro in otros:
            self.insert_location_at_hierarchically(internal_locations_hierarchically, otro)
        return internal_locations_hierarchically

    # serialize (to json)
    StoreDescription = Column(String(400), unique=True, nullable=False)
    UrlMaps = Column(String(100), unique=True, nullable=False)

    def serialize(self, scope={}):
        store = {
            'id': self.StoreId,
            'storeName': self.StoreName,
            'storeDescription': self.StoreDescription,
            'urlMaps': self.UrlMaps,
            'address': self.Address,
            'phone': self.Phone,
        }
        if 'zone' in scope:
            store['zone'] = self.Zone.serialize(scope=scope['zone'])
        if 'product_stores' in scope:
            store['product_stores'] = [product_Store.serialize(scope=scope['product_stores']) for product_Store in self.Product_Stores]
        if 'locationLevelItems' in scope:
            store['locationLevelItems'] = self.get_internal_locations_hierarchically(scope=scope['locationLevelItems'])
        return store


class Product_Store(db.Base):
    # Table name in database
    __tablename__ = "Product_Store"
    # Columns
    InternalSku = Column(String(150), ForeignKey("Product.InternalSku"), primary_key=True, nullable=False)
    StoreId = Column(Integer, ForeignKey("Store.StoreId"), primary_key=True, nullable=False)
    Stock = Column(Float, nullable=False)
    Cost = Column(Float, nullable=False)
    # RelationShips
    Product = relationship("Product", back_populates="Product_Stores")
    Store = relationship("Store", back_populates="Product_Stores")
    ProductsInDirectSale = relationship("ProductInDirectSale")
    PublicationProducts_Product_Store = relationship("PublicationProduct_Product_Store")
    LocationLevelItems_Product_Store = relationship("LocationLevelItem_Product_Store")
    Product_Store_kit_Products = relationship("Product_Store_kit_Product")
    # Initializing

    def __init__(self, stock=None, cost=None):
        self.Stock = stock
        self.Cost = cost

    # String representations
    def __repr__(self):
        return (
            f'Product_Store({self.InternalSku},'
            f'{self.StoreId},'
            f'{self.Stock},'
            f'{self.Cost})'
            # f'{self.InternalLocation})'
        )

    def serialize(self, scope={}):
        product_store = {
            'stock': self.Stock,
            'cost': self.Cost
        }
        if 'product' in scope:
            product_store['product'] = self.Product.serialize(scope=scope['product'])
        if 'store' in scope:
            print('----store')
            product_store['store'] = self.Store.serialize(scope=scope['store'])
        if 'publicationProducts_product_store' in scope:
            product_store['publicationProducts_product_store'] = [publicationProducts_Product_Store.serialize(scope=scope['publicationProducts_product_store']) for publicationProducts_Product_Store in self.PublicationProducts_Product_Store]
        if 'locationLevelItems_product_store' in scope:
            product_store['locationLevelItems_product_store'] = [locationLevelItem_product_store.serialize(scope=scope['locationLevelItems_product_store']) for locationLevelItem_product_store in self.LocationLevelItems_Product_Store]
        return product_store


"""
class TransferStatus(db.Base):
    # Table name in database
    __tablename__ = 'TransferStatus'
    # Columns
    TransferStatusId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    TransferStatus = Column(String(50), nullable=False)
    Description = Column(String(100), nullable=False)
    # RelationShips

    # Initializing
    def __init__(self, transferStatus, description):
        self.TransferStatus = transferStatus
        self.Description = description
    # String representations

    def __repr__(self):
        return f'TransferStatus({self.TransferStatus},{self.Description})'
    # serialize (to json)

    def serialize(self):
        return {
            'transferStatusId': self.TransferStatusId,
            'transferStatus': self.TransferStatus,
            'description': self.Description
        }


class Transfer(db.Base):
    # Table name in database
    __tablename__ = 'Transfer'
    # Columns
    TransferId = Column(Integer, primary_key=True,
                        nullable=False, autoincrement=True)
    InternalSku = Column(String(150), ForeignKey(
        "Product.InternalSku"), nullable=False)
    OriginStoreId = Column(Integer, ForeignKey(
        "Store.StoreId"), nullable=False)
    DestinationStoreId = Column(
        Integer, ForeignKey("Store.StoreId"), nullable=False)
    TransferStatusId = Column(Integer, ForeignKey(
        "TransferStatus.TransferStatusId"), nullable=False)
    TransferOutputId = Column(String(150), nullable=False)
    TransferInputId = Column(String(150), nullable=False)
    Units = Column(Float, nullable=False)
    TransferDate = Column(DateTime, nullable=False)
    # RelationShips

    # Initializing
    def __init__(self, transferOutputId, transferInputId, units, transferDate):
        self.TransferOutputId = transferOutputId
        self.TransferInputId = transferInputId
        self.Units = units
        self.TransferDate = transferDate
    # String representations

    def __repr__(self):
        return (
            f'Transfer({self.TransferId},'
            f'{self.InternalSku},'
            f'{self.OriginStoreId},'
            f'{self.DestinationStoreId},'
            f'{self.TransferStatusId},'
            f'{self.TransferOutputId},'
            f'{self.TransferInputId},'
            f'{self.Units},'
            f'{self.TransferDate})'
        )
    # serialize (to json)

    def serialize(self):
        return {
            'transferId': self.TransferId,
            'internalSku': self.InternalSku,
            'originStoreId': self.OriginStoreId,
            'destinationStoreId': self.DestinationStoreId,
            'transferOutputId': self.TransferOutputId,
            'transferInputId': self.TransferInputId,
            'units': self.Units,
            'transferDate': str(self.TransferDate),
        }


class TransferStatusChange(db.Base):
    # Table name in database
    __tablename__ = 'TransferStatusChange'
    # Columns
    TransferStatusChangeId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    TransferId = Column(Integer, ForeignKey(
        "Transfer.TransferId"), nullable=False)
    TransferStatusId = Column(Integer, ForeignKey(
        "TransferStatus.TransferStatusId"), nullable=False)
    Date = Column(DateTime, nullable=False)
    # RelationShips

    # Initializing
    def __init__(self, date):
        self.Date = date
    # String representations

    def __repr__(self):
        return (
            f'TransferStatusChange({self.TransferStatusChangeId},'
            f'{self.TransferId},'
            f'{self.TransferStatusId},'
            f'{self.Date})'
        )
    # serialize (to json)

    def serialize(self):
        return {
            'transferStatusChangeId': self.TransferStatusChangeId,
            'transferId': self.TransferId,
            'transferStatusId': self.TransferStatusId,
            'date': str(self.Date)
        }
"""


class EntryDocumentStackableComment(db.Base):
    # Table name in database
    __tablename__ = 'EntryDocumentStackableComment'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    EntryDocumentId = Column(Integer, ForeignKey("EntryDocument.Id"), nullable=False)
    UserId = Column(Integer, ForeignKey("User.UserId"), nullable=False)
    Comment = Column(String(100), nullable=True)
    TimeStamp = Column(DateTime(timezone=True), nullable=True)
    # RelationShips
    EntryDocument = relationship("EntryDocument", back_populates="EntryDocumentStackableComments")
    User = relationship("User", back_populates="EntryDocumentStackableComments")

    # Initializing
    def __init__(self, userId, comment, timeStamp):
        self.UserId = userId
        self.Comment = comment
        self.TimeStamp = timeStamp
    # String representations

    def __repr__(self):
        return (
            f'EntryDocumentStackableComments({self.Id},'
            f'{self.EntryDocumentId},'
            f'{self.Comment},'
            f'{self.TimeStamp})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        entryDocumentComment = {
            'id': self.Id,
            'comment': self.Comment,
            'timeStamp': str(self.TimeStamp),
            'userName': self.User.Name
        }
        if 'entryDocument' in scope:
            entryDocumentComment['entryDocument'] = self.EntryDocument.serialize(scope=scope['entryDocument'])
        if 'user' in scope:
            entryDocumentComment['user'] = self.User.serialize(scope=scope['user'])
        return entryDocumentComment


class EntryDocument(db.Base):
    # Table name in database
    __tablename__ = 'EntryDocument'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    InternalId = Column(String(20), nullable=False, unique=True)
    # ReceiverRfc = Column(String(15), nullable=True)
    IssuePlace = Column(String(40), nullable=True)
    IssueDate = Column(DateTime(timezone=True), nullable=True)
    CertificationDate = Column(DateTime(timezone=True), nullable=True)
    ExpirationDate = Column(DateTime(timezone=True), nullable=True)
    PaymentMethod = Column(String(30), nullable=True)
    PaymentForm = Column(String(20), nullable=True)
    Currency = Column(String(20), nullable=True)
    IssuerRfc = Column(String(15), nullable=True)
    SaleOrder = Column(String(30), nullable=True)
    PurchaseOrder = Column(String(30), nullable=True)
    PaymentConditions = Column(String(30), nullable=True)
    SalesRep = Column(String(25), nullable=True)
    # RelationShips
    ProductsInEntryDocument = relationship("ProductInEntryDocument", cascade="all, delete-orphan")
    EntryDocumentStackableComments = relationship("EntryDocumentStackableComment", cascade="all, delete-orphan")
    Invoice = relationship("Invoice", uselist=False, cascade="all, delete-orphan")

    # Initializing
    def __init__(self, internalId=None, issuePlace=None, issueDate=None,
                 certificationDate=None, expirationDate=None, paymentMethod=None,
                 paymentForm=None, currency=None, issuerRfc=None, saleOrder=None,
                 purchaseOrder=None, paymentConditions=None, salesRep=None):  # receiverRfc=None,
        self.InternalId = internalId
        # self.ReceiverRfc = receiverRfc
        self.IssuePlace = issuePlace
        self.IssueDate = issueDate
        self.CertificationDate = certificationDate
        self.ExpirationDate = expirationDate
        self.PaymentMethod = paymentMethod
        self.PaymentForm = paymentForm
        self.Currency = currency
        self.IssuerRfc = issuerRfc
        self.SaleOrder = saleOrder
        self.PurchaseOrder = purchaseOrder
        self.PaymentConditions = paymentConditions
        self.SalesRep = salesRep

    # String representations
    def __repr__(self):
        return (
            f'EntryDocument({self.Id},'
            f'{self.InternalId},'
            # f'{self.ReceiverRfc},'
            f'{self.IssuePlace},'
            f'{self.IssueDate},'
            f'{self.CertificationDate},'
            f'{self.ExpirationDate},'
            f'{self.PaymentMethod},'
            f'{self.PaymentForm},'
            f'{self.Currency},'
            f'{self.IssuerRfc})'
            f'{self.SaleOrder},'
            f'{self.PurchaseOrder},'
            f'{self.PaymentConditions},'
            f'{self.SalesRep})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        entryDocument = {
            'id': self.Id,
            'internalId': self.InternalId,
            # 'receiverRfc': self.ReceiverRfc,
            'issuePlace': self.IssuePlace,
            'issueDate': str(self.IssueDate) if self.IssueDate else self.IssueDate,
            'certificationDate': str(self.CertificationDate) if self.CertificationDate else self.CertificationDate,
            'expirationDate': str(self.ExpirationDate) if self.ExpirationDate else self.ExpirationDate,
            'issuerRfc': self.IssuerRfc,
            'saleOrder': self.SaleOrder,
            'purchaseOrder': self.PurchaseOrder,
            'salesRep': self.SalesRep,
        }
        if 'basics' in scope:
            basics = scope['basics']
            if 'paymentMethod' in basics:
                entryDocument['paymentMethod'] = self.PaymentMethod
            if 'paymentForm' in basics:
                entryDocument['paymentForm'] = self.PaymentForm
            if 'paymentConditions' in basics:
                entryDocument['paymentConditions'] = self.PaymentConditions
            if 'currency' in basics:
                entryDocument['currency'] = self.Currency
        if 'productsInEntryDocument' in scope:
            print('------<>>>>>>><')
            entryDocument['productsInEntryDocument'] = [productInEntryDocument.serialize(scope['productsInEntryDocument']) for productInEntryDocument in self.ProductsInEntryDocument]
        if 'entryDocumentStackableComments' in scope:
            entryDocument['entryDocumentStackableComments'] = [entryDocumentStackableComment.serialize(scope['entryDocumentStackableComments']) for entryDocumentStackableComment in self.EntryDocumentStackableComments]
        if 'invoice' in scope:
            entryDocument['invoice'] = self.Invoice.serialize(scope['invoice']) if self.Invoice else None
        return entryDocument


class Invoice(db.Base):
    # Table name in database
    __tablename__ = 'Invoice'
    # Columns
    EntryDocumentId = Column(Integer, ForeignKey("EntryDocument.Id"), primary_key=True, nullable=False)
    Uuid = Column(String(40), nullable=False, unique=True)
    Serie = Column(String(15), nullable=False)
    CsdSerie = Column(String(30), nullable=False)
    BranchOffice = Column(String(30), nullable=True)
    CfdiUsage = Column(String(15), nullable=False)
    # RelationShips
    EntryDocument = relationship("EntryDocument", back_populates="Invoice")

    # Initializing
    def __init__(self, uuid=None, serie=None,
                 csdSerie=None, branchOffice=None, cfdiUsage=None):
        self.Uuid = uuid
        self.Serie = serie
        self.CsdSerie = csdSerie
        self.BranchOffice = branchOffice
        self.CfdiUsage = cfdiUsage

    # String representations
    def __repr__(self):
        return (
            f'Invoice({self.EntryDocumentId},'
            f'{self.Uuid},'
            f'{self.Serie},'
            f'{self.CsdSerie},'
            f'{self.BranchOffice},'
            f'{self.CfdiUsage})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        invoice = {
            'uuid': self.Uuid,
            'serie': self.Serie,
            'csdSerie': self.CsdSerie,
            'branchOffice': self.BranchOffice,
            'cfdiUsage': self.CfdiUsage
        }
        if 'entryDocument' in scope:
            invoice['entryDocument'] = self.EntryDocument.serialize(scope=scope['entryDocument'])
        return invoice


class ProductInEntryDocumentMotion(db.Base):
    # Table name in database
    __tablename__ = 'ProductInEntryDocumentMotion'
    # Columns
    ProductInEntryDocumentId = Column(Integer, ForeignKey("ProductInEntryDocument.Id"), primary_key=True, nullable=False)
    Motion = Column(String(25), primary_key=True, nullable=False)
    Customs = Column(String(30), nullable=True)
    MotionDate = Column(DateTime(timezone=True), nullable=True)
    # RelationShips
    ProductInEntryDocument = relationship("ProductInEntryDocument", back_populates='ProductInEntryDocumentMotions')

    # Initializing
    def __init__(self, motion=None, customs=None, motionDate=None):
        self.Motion = motion
        self.Customs = customs
        self.MotionDate = motionDate

    # String representations
    def __repr__(self):
        return (
            f'ProductInEntryDocumentMotion('
            f'{self.Motion},'
            f'{self.Customs},'
            f'{self.MotionDate})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        productInEntryDocumentMotion = {
            'motion': self.Motion,
            'customs': self.Customs,
            'motionDate': str(self.MotionDate)
        }
        if 'productInEntryDocument' in scope:
            productInEntryDocumentMotion['productInEntryDocument'] = self.ProductInEntryDocument.serialize(scope=scope['productInEntryDocument'])
        return productInEntryDocumentMotion


class ProductInEntryDocumentSerie(db.Base):
    # Table name in database
    __tablename__ = 'ProductInEntryDocumentSerie'
    # Columns
    ProductInEntryDocumentId = Column(Integer, ForeignKey("ProductInEntryDocument.Id"), primary_key=True, nullable=False)
    Serie = Column(String(20), primary_key=True, nullable=False)
    # RelationShips
    ProductInEntryDocument = relationship("ProductInEntryDocument", back_populates='ProductInEntryDocumentSeries')

    # Initializing
    def __init__(self, serie=None):
        self.Serie = serie

    # String representations
    def __repr__(self):
        return (
            f'ProductInEntryDocumentSerie('
            f'{self.Serie})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        productInEntryDocumentSerie = {
            'motion': self.Serie
        }
        if 'productInEntryDocument' in scope:
            productInEntryDocumentSerie['productInEntryDocument'] = self.ProductInEntryDocument.serialize(scope=scope['productInEntryDocument'])
        return productInEntryDocumentSerie


class ProductInEntryDocumentTax(db.Base):
    # Table name in database
    __tablename__ = 'ProductInEntryDocumentTax'
    # Columns
    ProductInEntryDocumentId = Column(Integer, ForeignKey("ProductInEntryDocument.Id"), primary_key=True, nullable=False)
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    TaxKey = Column(String(15), nullable=True)
    FactorType = Column(String(10), nullable=False)
    TaxRate = Column(Float, nullable=True)
    # RelationShips
    ProductInEntryDocument = relationship("ProductInEntryDocument", back_populates='ProductInEntryDocumentTaxes')

    # Initializing
    def __init__(self, taxKey=None, factorType=None, taxRate=None):
        self.TaxKey = taxKey
        self.FactorType = factorType
        self.TaxRate = taxRate

    # String representations
    def __repr__(self):
        return (
            f'ProductInEntryDocumentTax('
            f'{self.Id},'
            f'{self.TaxKey},'
            f'{self.FactorType},'
            f'{self.TaxRate})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        productInEntryDocumentTax = {
            'id': self.Id,
            "taxKey": self.TaxKey,
            'factorType': self.FactorType,
            'taxRate': self.TaxRate
        }
        if 'productInEntryDocument' in scope:
            productInEntryDocumentTax['productInEntryDocument'] = self.ProductInEntryDocument.serialize(scope=scope['productInEntryDocument'])
        return productInEntryDocumentTax


class WarrantyTimeFrame(db.Base):
    # Table name in database
    __tablename__ = 'WarrantyTimeFrame'
    # primary key
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    Name = Column(String(15), unique=True, nullable=False)

    # RelationShips
    Warranties = relationship('Warranty')

    # Initializing
    def __init__(self, name=None):
        self.Name = name

    # String representations
    def __repr__(self):
        return (
            f'WarrantyTimeFrame({self.Id},'
            f'{self.Name})'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "id": self.Id,
            "name": self.Name
        }


class WarrantyType(db.Base):
    # Table name in database
    __tablename__ = 'WarrantyType'
    # primary key
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    Name = Column(String(30), unique=True, nullable=False)

    # RelationShips
    Warranties = relationship('Warranty')

    # Initializing
    def __init__(self, name=None):
        self.Name = name

    # String representations
    def __repr__(self):
        return (
            f'WarrantyType({self.Id},'
            f'{self.Name})'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "id": self.Id,
            "name": self.Name
        }


class Warranty(db.Base):
    # Table name in database
    __tablename__ = 'Warranty'
    # primary key
    PublicationBaseId = Column(Integer, ForeignKey("PublicationBase.Id"), primary_key=True, nullable=False)
    WarrantyTypeId = Column(Integer, ForeignKey("WarrantyType.Id"), primary_key=True, nullable=False)
    WarrantyTimeFrameId = Column(Integer, ForeignKey("WarrantyTimeFrame.Id"), nullable=False)
    Amount = Column(Integer, nullable=False)
    # RelationShips
    WarrantyTimeFrame = relationship('WarrantyTimeFrame', back_populates='Warranties', uselist=False)
    WarrantyType = relationship('WarrantyType', back_populates='Warranties', uselist=False)
    PublicationBase = relationship('PublicationBase', back_populates='Warranties', uselist=False)

    # Initializing
    def __init__(self, amount=None):
        self.Amount = amount

    # String representations
    def __repr__(self):
        return (
            f'Warranty({self.PublicationBaseId},'
            f'{self.WarrantyTypeId},'
            f'{self.WarrantyTimeFrameId},'
            f'{self.Amount})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        warranty = {
            "publicationId": self.PublicationBaseId,
            "warrantyTypeId": self.WarrantyTypeId,
            "warrantyTimeFrameId": self.WarrantyTimeFrameId
        }
        if 'warrantyTimeFrame' in scope:
            warranty['warrantyTimeFrame'] = self.WarrantyTimeFrame.serialize(scope=scope['warrantyTimeFrame'])
        if 'warrantyType' in scope:
            warranty['warrantyType'] = self.WarrantyType.serialize(scope=scope['warrantyType'])
        if 'publicationBase' in scope:
            warranty['publicationBase'] = self.PublicationBase.serialize(scope=scope['publicationBase'])
        return warranty


class PublicationStatus(db.Base):
    # Table name in database
    __tablename__ = 'PublicationStatus'
    # primary key
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    # Columns
    Name = Column(String(15), unique=True, nullable=False)

    # RelationShips
    PublicationBases = relationship('PublicationBase')

    # Initializing
    def __init__(self, name=None):
        self.Name = name

    # String representations
    def __repr__(self):
        return (
            f'PublicationStatus({self.Id},'
            f'{self.Name})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        publicationStatus = {
            "id": self.Id,
            "name": self.Name
        }
        if 'publicationBases' in scope:
            publicationStatus['publicationBases'] = [publicationBase.serialize(scope=scope['publicationBases']) for publicationBase in self.PublicationBases]
        return publicationStatus


class OrderStatusFlag(db.Base):
    # Table name in database
    __tablename__ = 'OrderStatusFlag'
    # Columns
    OrderStatusFlagId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    OrderStatusFlag = Column(String(10), nullable=False)
    # RelationShips
    OrderStatus = relationship("OrderStatus")

    # Initializing
    def __init__(self, orderStatusFlag):
        self.OrderStatusFlag = orderStatusFlag

    # String representations
    def __repr__(self):
        return (
            f'OrderStatusFlag({self.OrderStatusFlagId},'
            f'{self.OrderStatusFlag})'
        )
    # serialize (to json)

    def serialize(self):
        return {
            "orderStatusFlagId": self.OrderStatusFlagId,
            "orderStatusFlag": self.OrderStatusFlag
        }


class FulfillmentChannel(db.Base):
    # Table name in database
    __tablename__ = 'FulfillmentChannel'
    # Columns
    FulfillmentChannelId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    FulfillmentChannel = Column(String(15), nullable=False)
    # RelationShips

    # Initializing
    def __init__(self, fulfillmentChannel):
        self.FulfillmentChannel = fulfillmentChannel

    # String representations
    def __repr__(self):
        return (
            f'FulfillmentChannel({self.FulfillmentChannelId},'
            f'{self.FulfillmentChannel})'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "fulfillmentChannelId": self.FulfillmentChannelId,
            "fulfillmentChannel": self.FulfillmentChannel
        }


class OrderInternalStatusType(db.Base):
    # Table name in database
    __tablename__ = 'OrderInternalStatusType'
    # Columns
    OrderInternalStatusTypeId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    OrderInternalStatusType = Column(String(40), nullable=False)
    OrderNum = Column(Integer, nullable=False)
    # RelationShips
    OrderInternalStatus = relationship("OrderInternalStatus", cascade="all, delete-orphan")
    
    # Initializing
    def __init__(self, orderInternalStatusType, orderNum):
        self.OrderInternalStatusType = orderInternalStatusType
        self.OrderNum = orderNum

    # String representations
    def __repr__(self):
        return (
            f'OrderInternalStatusType({self.OrderInternalStatusTypeId},'
            f'{self.OrderInternalStatusType},'
            f'{self.OrderNum})'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "orderInternalStatusTypeId": self.OrderInternalStatusTypeId,
            "orderInternalStatusType": self.OrderInternalStatusType,
            "orderNum": self.OrderNum
        }

    def serialize_with_internal_status(self):
        return {
            "orderInternalStatusTypeId": self.OrderInternalStatusTypeId,
            "orderInternalStatusType": self.OrderInternalStatusType,
            "orderNum": self.OrderNum,
            "OrderInternalStatus": [orderInternalStatus.serialize()
                                    for orderInternalStatus in
                                    self.OrderInternalStatus]
        }


class OrderStatus(db.Base):
    # Table name in database
    __tablename__ = 'OrderStatus'
    # Columns
    OrderStatusId = Column(Integer, primary_key=True,
                           nullable=False, autoincrement=True)
    OrderStatusFlagId = Column(Integer, ForeignKey(
        "OrderStatusFlag.OrderStatusFlagId"), nullable=False)
    OrderStatus = Column(String(40), nullable=False)
    Description = Column(String(200), nullable=False)
    # RelationShips
    OrderStatusFlag = relationship(
        "OrderStatusFlag", back_populates="OrderStatus", uselist=False)

    # Initializing
    def __init__(self, orderStatus, description):
        self.OrderStatus = orderStatus
        self.Description = description

    # String representations
    def __repr__(self):
        return (
            f'OrderStatus({self.OrderStatusId},'
            f'{self.OrderStatusFlagId},'
            f'{self.OrderStatus},'
            f'{self.Description})'
        )
    # serialize (to json)

    def serialize(self):
        return {
            "orderStatusId": self.OrderStatusId,
            "orderStatusFlagId": self.OrderStatusFlagId,
            "orderStatus": self.OrderStatus,
            "description": self.Description
        }

    def basic_data_serialize(self):
        return {
            "orderStatusId": self.OrderStatusId,
            "orderStatus": self.OrderStatus,
            "orderStatusFlagId": self.OrderStatusFlagId,
            "orderStatusFlag": self.OrderStatusFlag.OrderStatusFlag
        }


class OrderInternalStatus(db.Base):
    # Table name in database
    __tablename__ = 'OrderInternalStatus'
    # Columns
    OrderInternalStatusId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    OrderInternalStatusTypeId = Column(Integer, ForeignKey(
        "OrderInternalStatusType.OrderInternalStatusTypeId"), nullable=False)
    OrderInternalStatus = Column(String(40), nullable=False)
    Description = Column(String(200), nullable=False)
    OrderNum = Column(Integer, nullable=False)
    __table_args__ = (
        UniqueConstraint('OrderInternalStatusTypeId', 'OrderInternalStatus',
                         name='uq_orderInternalStatusTypeId_orderInternalStatus'),
    )
    # RelationShips
    OrderInternalStatusType = relationship(
        "OrderInternalStatusType",
        back_populates="OrderInternalStatus", uselist=False)
    OrderInternalStatusChanges = relationship("OrderInternalStatusChange")

    # Initializing
    def __init__(self, orderInternalStatus, description, orderNum):
        self.OrderInternalStatus = orderInternalStatus
        self.Description = description
        self.OrderNum = orderNum

    # String representations
    def __repr__(self):
        return (
            f'OrderInternalStatus({self.OrderInternalStatusId},'
            f'{self.OrderInternalStatusTypeId},'
            f'{self.OrderInternalStatus},'
            f'{self.Description},'
            f'{self.OrderNum})'
        )
    # serialize (to json)

    def serialize(self):
        return {
            "orderInternalStatusId": self.OrderInternalStatusId,
            "orderInternalStatusTypeId": self.OrderInternalStatusTypeId,
            "orderInternalStatus": self.OrderInternalStatus,
            "orderNum": self.OrderNum,
            "description": self.Description
        }

    def basic_data_serialize(self):
        return {
            "orderInternalStatusId": self.OrderInternalStatusId,
            "orderInternalStatusTypeId": self.OrderInternalStatusTypeId,
            "orderInternalStatus": self.OrderInternalStatus,
            "orderNum": self.OrderNum,
            "orderInternalStatusType":
                self.OrderInternalStatusType.OrderInternalStatusType
        }


class Client(db.Base):
    # Table name in database
    __tablename__ = 'Client'
    # Columns
    ClientId = Column(Integer, primary_key=True,
                      nullable=False, autoincrement=True)
    MarketplaceClientId = Column(String(15), nullable=True)
    RegistrationDate = Column(DateTime(timezone=True), nullable=True)
    City = Column(String(50), nullable=True)
    State = Column(String(50), nullable=True)
    Nickname = Column(String(50), nullable=True)
    Name = Column(String(100), nullable=True)
    Score = Column(Integer, nullable=True)
    ZipCode = Column(String(5), nullable=True)
    PhoneNumber = Column(String(15), nullable=True)
    Email = Column(String(50), nullable=True)
    Address = Column(String(150), nullable=True)
    # RelationShips
    Orders = relationship("Order", back_populates="Cliente")
    DirectSales = relationship("DirectSale")
    # Initializing

    def __init__(self, marketplaceClientId,
                 registrationDate, city, state, nickname,
                 name, score, zipCode, phoneNumber, email,
                 address, orders):
        self.MarketplaceClientId = marketplaceClientId
        self.RegistrationDate = registrationDate
        self.City = city
        self.State = state
        self.Nickname = nickname
        self.Name = name
        self.Score = score
        self.ZipCode = zipCode
        self.PhoneNumber = phoneNumber
        self.Email = email
        self.Address = address
        self.Orders = orders

    def addClientOrder(self, order):
        # orders = self.Orders
        orders = []
        if self.Orders is None or self.Orders == []:
            orders = [order,]
        else:
            for previous_order in self.Orders:
                orders.append(previous_order)
            orders.append(order)
        self.Orders = orders
        return 0

    # String representations
    def __repr__(self):
        return (
            f'Client({self.ClientId},'
            f'{self.MarketplaceClientId},'
            f'{self.RegistrationDate},'
            f'{self.City},'
            f'{self.State},'
            f'{self.Nickname},'
            f'{self.Name},'
            f'{self.Score},'
            f'{self.ZipCode},'
            f'{self.PhoneNumber},'
            f'{self.Email},'
            f'{self.Address})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        client = {
            "clientId": self.ClientId,
            "registrationDate": str(self.RegistrationDate),
            "city": self.City,
            "state": self.State,
            "nickname": self.Nickname,
            "name": self.Name,
            "score": self.Score,
            "zipCode": self.ZipCode,
            "phoneNumber": self.PhoneNumber,
            "email": self.Email,
            "address": self.Address
        }
        if 'orders' in scope:
            client["orders"] = [order.serialize(scope=scope['orders']) for order in self.Orders]
        return client

###############################3


class FormatText(db.Base):
    # Table name in database
    __tablename__ = 'FormatText'
    Html = Column(String(100), primary_key=True, nullable=False)
    # RelationShips
    #TextTemplate = relationship("TextTemplate", uselist=False)
     # String representations
    def __repr__(self):
        return (
            f'TextTemplate({self.Html},'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        formatText = {
            "html": self.Html,
        }
        #if 'textTemplate' in scope:
        #    formatText["textTemplate"] = self.TextTemplate.serialize(scope=scope['textTemplate'])
        return formatText

class TextTemplate(db.Base):
    # Table name in database
    __tablename__ = 'TextTemplate'
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    TemplateOptionOrderMessageId = Column(String(100), ForeignKey("TemplateOptionOrderMessage.Id"), nullable=False)
    MluHtml = Column(String(100), ForeignKey("FormatText.Html"), nullable=True)
    MpeHtml = Column(String(100), ForeignKey("FormatText.Html"), nullable=True)
    MlmHtml = Column(String(100), ForeignKey("FormatText.Html"), nullable=True)
    MecHtml = Column(String(100), ForeignKey("FormatText.Html"), nullable=True)
    MlaHtml = Column(String(100), ForeignKey("FormatText.Html"), nullable=True)
    McoHtml = Column(String(100), ForeignKey("FormatText.Html"), nullable=True)
    # RelationShips
    TemplateOptionOrderMessage = relationship("TemplateOptionOrderMessage", back_populates="TextTemplates")
    Mlu = relationship("FormatText", foreign_keys=[MluHtml])
    Mpe = relationship("FormatText", foreign_keys=[MpeHtml])
    Mlm = relationship("FormatText", foreign_keys=[MlmHtml])
    Mec = relationship("FormatText", foreign_keys=[MecHtml])
    Mla = relationship("FormatText", foreign_keys=[MlaHtml])
    Mco = relationship("FormatText", foreign_keys=[McoHtml])
     # String representations
    def __repr__(self):
        return (
            f'FormatText({self.Id},'
            f'{self.TemplateOptionOrderMessageId},'
            f'{self.MluHtml},'
            f'{self.MpeHtml},'
            f'{self.MlmHtml},'
            f'{self.MecHtml},'
            f'{self.MlaHtml},'
            f'{self.McoHtml}'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        textTemplate = {
            "orderId": self.OrderId,
            "status": self.Status,
            "substatus": self.Substatus
        }
        if 'templateOptionOrderMessage' in scope:
            textTemplate["templateOptionOrderMessage"] = self.TemplateOptionOrderMessage.serialize(scope=scope['templateOptionOrderMessage'])
        if 'mlu' in scope:
            textTemplate["mlu"] = self.Mlu.serialize(scope=scope['mlu'])
        if 'mpe' in scope:
            textTemplate["mpe"] = self.Mpe.serialize(scope=scope['mpe'])
        if 'mlm' in scope:
            textTemplate["mlm"] = self.Mlm.serialize(scope=scope['mlm'])
        if 'mec' in scope:
            textTemplate["mec"] = self.Mec.serialize(scope=scope['mec'])
        if 'mla' in scope:
            textTemplate["mla"] = self.Mla.serialize(scope=scope['mla'])
        if 'mco' in scope:
            textTemplate["mco"] = self.Mco.serialize(scope=scope['mco'])
        return textTemplate

class OrderConversation(db.Base):
    # Table name in database
    __tablename__ = 'OrderConversation'
    OrderId = Column(Integer, ForeignKey("Order.OrderId"), primary_key=True, nullable=False)
    Status = Column(String(50), nullable=False)
    Substatus = Column(String(100), nullable=False)
    # RelationShips
    Order = relationship("Order", back_populates="OrderConversation")
    OptionsOrderMessage = relationship("OptionOrderMessage", cascade="all, delete-orphan")
    OrderMessages = relationship("OrderMessage", cascade="all, delete-orphan", order_by="OrderMessage.TimeStamp")
    # String representations
    def __repr__(self):
        return (
            f'OrderConversation({self.OrderId},'
            f'{self.Status},'
            f'{self.Substatus}'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        orderConversation = {
            "orderId": self.OrderId,
            "status": self.Status,
            "substatus": self.Substatus
        }
        if 'order' in scope:
            orderConversation["order"] = self.Order.serialize(scope=scope['order'])
        if 'optionOrderMessage' in scope:
            orderConversation["optionOrderMessage"] = [optionOrderMessage.serialize(scope=scope['optionOrderMessage']) for optionOrderMessage in self.OptionsOrderMessage]
        if 'orderMessages' in scope:
            orderConversation["orderMessages"] = [orderMessage.serialize(scope=scope['orderMessages']) for orderMessage in self.OrderMessages]
        return orderConversation

class TemplateOptionOrderMessage(db.Base):
    # Table name in database
    __tablename__ = 'TemplateOptionOrderMessage'
    Id = Column(String(100), primary_key=True, nullable=False)
    OptionOrderMessageId = Column(String(80), ForeignKey("OptionOrderMessage.Id"), nullable=False)
    # RelationShips
    OptionOrderMessage = relationship('OptionOrderMessage', back_populates="TemplatesOptionOrderMessage")
    TextTemplates = relationship('TextTemplate')
    # String representations
    def __repr__(self):
        return (
            f'TemplateOptionOrderMessage({self.Id},'
            f'{self.OptionOrderMessageId}'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        templateOptionOrderMessage = {
            "Id": self.Id
        }
        if 'optionOrderMessage' in scope:
            templateOptionOrderMessage["optionOrderMessage"] = self.OptionOrderMessage.serialize(scope=scope['optionOrderMessage'])
        if 'textTemplate' in scope:
            templateOptionOrderMessage["textTemplate"] = [textTemplate.serialize(scope=scope['textTemplate']) for textTemplate in self.TextTemplates]
        return templateOptionOrderMessage

class OptionOrderMessage(db.Base):
    # Table name in database
    __tablename__ = 'OptionOrderMessage'
    Id = Column(String(80), primary_key=True, nullable=False)
    OrderConversationId = Column(Integer, ForeignKey("OrderConversation.OrderId"), nullable=False)
    Type = Column(String(350), nullable=False)
    Enable = Column(Boolean, nullable=False)
    InternalDescription = Column(String(350), nullable=False)
    Actionable = Column(Boolean, nullable=False)
    CharLimit = Column(Integer, nullable=True)
    CapAvailable = Column(Integer, nullable=True)
    # RelationShips
    OrderConvesation = relationship("OrderConversation", back_populates="OptionsOrderMessage")
    TemplatesOptionOrderMessage = relationship("TemplateOptionOrderMessage", cascade="all, delete-orphan")
    # String representations
    def __repr__(self):
        return (
            f'OptionOrderMessage({self.Id},'
            f'{self.Type},'
            f'{self.Enable},'
            f'{self.InternalDescription},'
            f'{self.Actionable},'
            f'{self.CharLimit},'
            f'{self.CapAvailable})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        optionOrderMessage = {
            "id": self.Id,
            "type": self.Type,
            "enable": self.Enable,
            "internalDescription": self.InternalDescription,
            "actionable": self.Actionable,
            "charLimit": self.CharLimit,
            "capAvailable": self.CapAvailable
        }
        if 'orderConvesation' in scope:
            optionOrderMessage["orderConvesation"] = self.OrderConvesation.serialize(scope=scope['orderConvesation'])
        if 'templatesOptionOrderMessage' in scope:
            optionOrderMessage["templatesOptionOrderMessage"] = [templateOptionOrderMessage.serialize(scope=scope['templatesOptionOrderMessage']) for templateOptionOrderMessage in self.TemplatesOptionOrderMessage]
        return optionOrderMessage

#######################################

class OrderMessage(db.Base):
    # Table name in database
    __tablename__ = 'OrderMessage'
    # Columns
    Id = Column(Integer, primary_key=True, autoincrement=True)
    OrderConversationOrderId = Column(Integer, ForeignKey("OrderConversation.OrderId"), nullable=False)
    TimeStamp = Column(DateTime(timezone=True), default=datetime.now(timezone.utc), nullable=False)
    MarketplaceId = Column(String, nullable=False, unique=True)
    Message = Column(String(350), nullable=False)
    FromClient = Column(Boolean, nullable=False)
    # RelationShips
    OrderConversation = relationship("OrderConversation", back_populates="OrderMessages")
    OrderMessageAtachments = relationship("OrderMessageAtachment", cascade="all, delete-orphan")
    # String representations
    def __repr__(self):
        return (
            f'OrderMessage({self.Id},'
            f'{self.OrderConversationOrderId},'
            f'{self.TimeStamp},'
            f'{self.MarketplaceId},'
            f'{self.Message},'
            f'{self.FromClient})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        order_message = {
            "id": self.Id,
            "orderConversationOrderId": self.OrderConversationOrderId,
            "timeStamp": self.TimeStamp,
            "marketplaceId": self.MarketplaceId,
            "message": self.Message,
            "fromClient": self.FromClient
        }
        if 'orderConversation' in scope:
            order_message["orderConversation"] = self.OrderConversation.serialize(scope=scope['orderConversation'])
        if 'orderMessageAtachments' in scope:
            order_message["orderMessageAtachments"] = [orderMessageAtachment.serialize(scope=scope['orderMessageAtachments']) for orderMessageAtachment in self.OrderMessageAtachments]
        return order_message


class OrderInternalStatusChange(db.Base):
    # Table name in database
    __tablename__ = 'OrderInternalStatusChange'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    OrderId = Column(Integer, ForeignKey("Order.OrderId"), nullable=False)
    UserId = Column(Integer, ForeignKey("User.UserId"), nullable=False)
    OrderInternalStatusId = Column(Integer, ForeignKey("OrderInternalStatus.OrderInternalStatusId"), nullable=False)
    TimeStamp = Column(DateTime(timezone=True),nullable=False)

    # RelationShips
    Order = relationship("Order", back_populates="OrderInternalStatusChanges")
    User = relationship("User", back_populates="OrderInternalStatusChanges")
    OrderInternalStatus = relationship("OrderInternalStatus", back_populates="OrderInternalStatusChanges")
    OrderInternalStatus_Order = relationship("OrderInternalStatus_Order", uselist=False)
    # String representations
    def __repr__(self):
        return (
            f'OrderInternalStatusChange({self.Id},'
            f'{self.OrderId},'
            f'{self.UserId},'
            f'{self.OrderInternalStatusId},'
            f'{self.TimeStamp})'
        )

    # serialize (to json)

    def serialize(self, scope={}):
        orderInternalStatusChange = {
            'id': self.Id,
            'timeStamp': str(self.TimeStamp)
        }
        if 'order' in scope:
            orderInternalStatusChange['order'] = self.Order.serialize(scope=scope['order'])
        if 'user' in scope:
            orderInternalStatusChange['user'] = self.User.serialize(scope=scope['user'])
        if 'orderInternalStatus' in scope:
            orderInternalStatusChange['orderInternalStatus'] = self.OrderInternalStatus.serialize()
        if 'orderInternalStatus_Order' in scope:
            orderInternalStatusChange['orderInternalStatus_Order'] = self.OrderInternalStatus_Order.serialize(scope=scope['orderInternalStatus_Order'])
        return orderInternalStatusChange


class OrderInternalStatus_Order(db.Base):
    # Table name in database
    __tablename__ = 'OrderInternalStatus_Order'
    # Columns
    OrderId = Column(Integer, ForeignKey("Order.OrderId"), primary_key=True)
    OrderInternalStatusChangeId = Column(Integer, ForeignKey("OrderInternalStatusChange.Id"), nullable=False, unique=True)

    # RelationShips
    Order = relationship("Order", back_populates="OrderInternalStatus_Order")
    OrderInternalStatusChange = relationship("OrderInternalStatusChange", back_populates="OrderInternalStatus_Order")
    # String representations
    def __repr__(self):
        return (
            f'OrderInternalStatus_Order({self.OrderId},'
            f'{self.OrderInternalStatusChangeId}'
        )

    # serialize (to json)

    def serialize(self, scope={}):
        orderInternalStatus_Order = {}
        if 'order' in scope:
            orderInternalStatus_Order['order'] = self.Order.serialize(scope=scope['order'])
        if 'orderInternalStatusChange' in scope:
            orderInternalStatus_Order['orderInternalStatusChange'] = self.OrderInternalStatusChange.serialize(scope=scope['orderInternalStatusChange'])
        return orderInternalStatus_Order


class Order(db.Base):
    # Table name in database
    __tablename__ = 'Order'
    # Columns
    OrderId = Column(Integer, primary_key=True, autoincrement=True)
    ClientId = Column(Integer, ForeignKey("Client.ClientId"), nullable=True)
    OrderStatusId = Column(Integer, ForeignKey(
        "OrderStatus.OrderStatusId"), nullable=True)
    FulfillmentChannelId = Column(Integer, ForeignKey(
        "FulfillmentChannel.FulfillmentChannelId"), nullable=False)
    CreationDate = Column(DateTime(timezone=True), nullable=False)
    MarketplaceOrderId = Column(String(50), unique=True, nullable=False)
    OrderUrl = Column(String(100), nullable=False)
    PaidAmount = Column(Float, nullable=True)
    Fee = Column(Float, nullable=True)
    Shipping = Column(Float, nullable=True)
    ReceivedAmount = Column(Float, nullable=True)
    PendingToResponse = Column(Boolean, nullable=False, default=False)
    # RelationShips
    # OrderStatusChanges = relationship(
    #     "OrderStatusChange",
    #     back_populates="Order", cascade="all, delete-orphan")
    OrderStackableComments = relationship(
        "OrderStackableComment",
        back_populates="Order", cascade="all, delete-orphan")
    ProductsInOrder = relationship(
        "ProductInOrder", cascade="all, delete-orphan", uselist=True)
    OrderOperations = relationship(
        "OrderOperation", cascade="all, delete-orphan")
    Cliente = relationship("Client", back_populates="Orders")
    OrderShippingInfo = relationship("OrderShippingInfo", uselist=False)
    #OrderMessages = relationship("OrderMessage", cascade="all, delete-orphan", order_by="OrderMessage.TimeStamp")
    OrderConversation = relationship("OrderConversation", uselist=False)
    OrderInternalStatusChanges = relationship("OrderInternalStatusChange", cascade="all, delete-orphan")
    OrderInternalStatus_Order = relationship("OrderInternalStatus_Order", uselist=False, cascade="all, delete-orphan")
    # OrderStatus
    # FulfillmentChannelId
    # Initializing

    def _setOrderData(self, orderStatusId,
                      fulfillmentChannelId, creationDate,
                      marketplaceOrderId, orderUrl,
                      paidAmount, fee, shipping, receivedAmount):
        self.OrderStatusId = orderStatusId
        self.FulfillmentChannelId = fulfillmentChannelId
        self.CreationDate = creationDate
        self.MarketplaceOrderId = marketplaceOrderId
        self.OrderUrl = orderUrl
        self.PaidAmount = paidAmount
        self.Fee = fee
        self.Shipping = shipping
        self.ReceivedAmount = receivedAmount

    def __init__(self, orderStatusId, fulfillmentChannelId,
                 creationDate, marketplaceOrderId, orderUrl,
                 paidAmount, fee, shipping, receivedAmount,
                 statusChanges, productsInOrder, cliente):
        self._setOrderData(orderStatusId,
                           fulfillmentChannelId, creationDate,
                           marketplaceOrderId, orderUrl, paidAmount,
                           fee, shipping, receivedAmount)
        # self.OrderStatusChanges = statusChanges
        self.ProductsInOrder = productsInOrder
        self.Cliente = cliente

    def updateOrder(self, creationDate, marketplaceOrderId,
                    orderUrl, paidAmount, fee, shipping, receivedAmount):
        self._setOrderData(creationDate, marketplaceOrderId,
                           orderUrl, paidAmount, fee, shipping, receivedAmount)

    # String representations
    def __repr__(self):
        return (
            f'Order({self.OrderId},'
            f'{self.ClientId},'
            f'{self.OrderStatusId},'
            f'{self.FulfillmentChannelId},'
            f'{self.CreationDate},'
            f'{self.MarketplaceOrderId},'
            f'{self.OrderUrl},'
            f'{self.PaidAmount},'
            f'{self.Fee},'
            f'{self.Shipping},'
            f'{self.ReceivedAmount})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        try:
            marketplace_id = self.ProductsInOrder[0].PublicationProduct.PublicationBase.Marketplace.Id
        except Exception as e:
            print(str(e))
            marketplace_id = None
        order = {
            "orderId": self.OrderId,
            "marketplaceId": marketplace_id,
            "clientId": self.ClientId,
            "orderStatusId": self.OrderStatusId,
            "fulfillmentChannel": self.FulfillmentChannelId,
            "creationDate": str(self.CreationDate),
            "marketplaceOrderId": self.MarketplaceOrderId,
            "orderURL": self.OrderUrl,
            "pendingToResponse": self.PendingToResponse
        }
        ##################
        if 'basics' in scope:
            basics = scope['basics']
            if 'paidAmount' in basics:
                order["paidAmount"] = None if self.PaidAmount is None else round(self.PaidAmount, 2)
            if 'fee' in basics:
                order["fee"] = None if self.Fee is None else round(self.Fee, 2)
            if 'shipping' in basics:
                order["shipping"] = None if self.Shipping is None else round(self.Shipping, 2)
            if 'receivedAmount' in basics:
                order["receivedAmount"] = None if self.ReceivedAmount is None else round(self.ReceivedAmount, 2)
        if 'client' in scope:
            order["client"] = {} if self.Cliente is None else self.Cliente.serialize(scope=scope['client'])
        # if 'orderStatusChanges' in scope:
        #     order["orderStatusChanges"] = [orderStatusChange.serialize(scope=scope['orderStatusChanges']) for orderStatusChange in self.OrderStatusChanges]
        if 'productsInOrder' in scope:
            order["productsInOrder"] = [productInOrder.serialize(scope=scope['productsInOrder'])for productInOrder in self.ProductsInOrder]
        if 'orderStackableComments' in scope:
            order["orderStackableComments"] = [orderStackableComment.serialize(scope=scope['orderStackableComments']) for orderStackableComment in self.OrderStackableComments]
        if 'guideInfo' in scope:
            order["guideInfo"] = {} if self.OrderShippingInfo is None else self.OrderShippingInfo.serialize()
        if 'orderConversation' in scope:
            order["orderConversation"] = None if self.OrderConversation is None else self.OrderConversation.serialize(scope=scope['orderConversation'])
        if 'orderInternalStatusChanges' in scope:
            order['orderInternalStatusChanges'] = [status_change.serialize(scope=scope['orderInternalStatusChanges']) for status_change in self.OrderInternalStatusChanges]
        if 'orderInternalStatus_Order' in scope:
            order['orderInternalStatus_Order'] = self.OrderInternalStatus_Order.serialize(scope=scope['orderInternalStatus_Order'])
        return order


class OrderShippingInfo(db.Base):
    # Table name in database
    __tablename__ = 'OrderShippingInfo'
    # Columns
    OrderShippingInfoId = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    OrderId = Column(Integer, ForeignKey("Order.OrderId"), nullable=False)
    ShippingNumber = Column(String(20), nullable=True)
    Status = Column(Boolean, nullable=True)
    Message = Column(String(100), nullable=True)
    # RelationShips
    Order = relationship("Order", back_populates="OrderShippingInfo")

    # Initializing
    def __init__(self, shippingNumber, status, message):
        self.ShippingNumber = shippingNumber
        self.Status = status
        self.Message = message

    # String representations
    def __repr__(self):
        return (
            f'OrderShippingInfo({self.OrderShippingInfoId},'
            f'{self.OrderId},'
            f'{self.ShippingNumber},'
            f'{self.Status},'
            f'{self.Message})'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "orderShippingInfoId": self.OrderShippingInfoId,
            "shippingNumber": self.ShippingNumber,
            "status": self.Status,
            "message": self.Message
        }


class OrderOperation(db.Base):
    # Table name in database
    __tablename__ = 'OrderOperation'
    # Columns
    OrderOperationId = Column(String(20), primary_key=True, nullable=False)
    OrderId = Column(Integer, ForeignKey("Order.OrderId"), nullable=False)
    # RelationShips

    # Initializing
    def __init__(self, orderOperationId):
        self.OrderOperationId = orderOperationId

    # String representations
    def __repr__(self):
        return f'OrderOperation({self.OrderOperationId},{self.OrderId})'
    # serialize (to json)

    def serialize(self):
        return {
            'orderOperationId': self.OrderOperationId,
            'orderId': self.OrderId
        }


"""class OrderStatusChange(db.Base):
    # Table name in database
    __tablename__ = 'OrderStatusChange'
    # Columns
    OrderStatusChangeId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    OrderId = Column(Integer, ForeignKey("Order.OrderId"), nullable=True)
    OrderStatusId = Column(Integer, ForeignKey(
        "OrderStatus.OrderStatusId"), nullable=True)
    Date = Column(DateTime, nullable=False)
    # RelationShips
    Order = relationship("Order", back_populates="OrderStatusChanges")

    # Initializing

    def __init__(self, date):
        self.Date = date

    # String representations
    def __repr__(self):
        return (
            f'OrderStatusChange({self.OrderStatusChangeId},'
            f'{self.OrderId},'
            f'{self.OrderStatusId},'
            f'{self.Date})'
        )

    # serialize (to json)

    def serialize(self, scope={}):
        orderStatusChange = {
            'orderStatusChangeId': self.OrderStatusChangeId,
            'orderId': self.OrderId,
            'orderStatusId': self.OrderStatusId,
            'date': str(self.Date)
        }
        if 'order' in scope:
            orderStatusChange['order'] = self.Order.serialize(scope=scope['order'])"""



class ProductInOrder(db.Base):
    # Table name in database
    __tablename__ = 'ProductInOrder'
    # Columns
    ProductInOrderId = Column(Integer, primary_key=True, autoincrement=True)
    OrderId = Column(Integer, ForeignKey("Order.OrderId"), nullable=False)
    # Llave foranea Nullable debido a la falta de publicaciones
    PublicationProductId = Column(Integer, ForeignKey("PublicationProduct.Id"),
                                  nullable=True)
    Title = Column(String(300), nullable=False)
    Brand = Column(String(30), nullable=False)
    Model = Column(String(80), nullable=False)
    Units = Column(Integer, nullable=False)
    ShippedUnits = Column(Integer, nullable=False)
    Photo = Column(String(150), nullable=True)
    OperationId = Column(String(50), nullable=True)
    PaidAmount = Column(Float, nullable=True)
    Fee = Column(Float, nullable=True)
    Shipping = Column(Float, nullable=True)
    ReceivedAmount = Column(Float, nullable=True)

    # RelationShips
    Order = relationship("Order", back_populates="ProductsInOrder", uselist=False)
    PublicationProduct = relationship("PublicationProduct", back_populates="ProductsInOrder", uselist=False)
    # Initializing

    def __init__(self, title, brand, model, units, photo,
                 operationId, paidAmount, fee, shipping,
                 receivedAmount):
        self.Title = title
        self.Brand = brand
        self.Model = model
        self.Units = units
        self.Photo = photo
        self.OperationId = operationId
        self.PaidAmount = paidAmount
        self.Fee = fee
        self.Shipping = shipping
        self.ReceivedAmount = receivedAmount
    # String representations

    def __repr__(self):
        return (
            f'ProductInOrder('
            f'{self.ProductInOrderId},'
            f'{self.Title},'
            f'{self.Brand},'
            f'{self.Model},'
            f'{self.Units},'
            f'{self.Photo},'
            f'{self.OperationId},'
            f'{self.PaidAmount},'
            f'{self.Fee},'
            f'{self.Shipping},'
            f'{self.ReceivedAmount})'
        )

    # serialize (to json)

    def serialize(self, scope={}):
        product_in_order = {
            "id": self.ProductInOrderId,
            "title": self.Title,
            "brand": self.Brand,
            "model": self.Model,
            "units": self.Units,
            "photo": self.Photo,
            "operationId": self.OperationId,
            "shippedUnits": self.ShippedUnits
        }
        if 'basics' in scope:
            basics = scope['basics']
            if 'paidAmount' in basics:
                product_in_order['paidAmount'] = None if self.PaidAmount is None else round(self.PaidAmount, 2)
            if 'fee' in basics:
                product_in_order['fee'] = None if self.Fee is None else round(self.Fee, 2)
            if 'shipping' in basics:
                product_in_order['shipping'] = None if self.Shipping is None else round(self.Shipping, 2)
            if 'receivedAmount' in basics:
                product_in_order['receivedAmount'] = None if self.ReceivedAmount is None else round(self.ReceivedAmount, 2)
        if 'order' in scope:
            product_in_order['order'] = self.Order.serialize(scope=scope['order'])
        if 'publicationProduct' in scope:
            product_in_order['publicationProduct'] = self.PublicationProduct.serialize(scope=scope['publicationProduct'])
        return product_in_order


class Role(db.Base):
    # Table name in database
    __tablename__ = 'Role'
    # Columns
    RoleId = Column(Integer, primary_key=True,
                    nullable=False, autoincrement=True)
    RoleName = Column(String(50), nullable=False)
    # RelationShips
    Users = relationship("User", back_populates="Role")

    # Initializing

    def __init__(self, roleName):
        self.RoleName = roleName
    # String representations

    def __repr__(self):
        return f'Role({self.RoleId},{self.RoleName})'
    # serialize (to json)

    def serialize(self, scope):
        role = {
            "roleId": self.RoleId,
            "roleName": self.RoleName
        }
        if 'users' in scope:
            role['users'] = [user.serialize(scope=scope['users']) for user in self.Users]
        return role


class User(db.Base):
    # Table name in database
    __tablename__ = 'User'
    # Columns
    UserId = Column(Integer, primary_key=True,
                    nullable=False, autoincrement=True)
    RoleId = Column(Integer, ForeignKey("Role.RoleId"), nullable=False)
    Public_Id = Column(String(50), nullable=False)
    Name = Column(String(100), nullable=False)
    Email = Column(String(70), unique=True, nullable=False)
    Password = Column(String(200), nullable=True)
    Alias = Column(String(100), nullable=True)
    PhoneNumber = Column(String(20), nullable=True)
    URLPhoto = Column(String(150), nullable=True)
    # RelationShip
    Role = relationship("Role", back_populates="Users")
    EntryDocumentStackableComments = relationship("EntryDocumentStackableComment")
    DirectSaleStackableComments = relationship("DirectSaleStackableComment")
    DirectSales = relationship("DirectSale")
    DirectInventoryMovements = relationship("DirectInventoryMovement")
    OrderStackableCommentRecords = relationship("OrderStackableCommentRecord")
    OrderStackableCommentsDeleted = relationship("OrderStackableCommentDeleted")
    OrderInternalStatusChanges = relationship("OrderInternalStatusChange")
    # Initializing

    def __init__(self, public_id, name, email, password):
        self.Public_Id = public_id
        self.Name = name
        self.Email = email
        self.Password = password
    # String representations

    def __repr__(self):
        return (
            f'User({self.UserId},{self.RoleId},'
            f'{self.Public_Id},{self.Name},'
            f'{self.Email},{self.Password},'
            f'{self.Alias},{self.PhoneNumber},'
            f'{self.URLPhoto})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        user = {
            "userId": self.UserId,
            "public_Id": self.Public_Id,
            "name": self.Name,
            "email": self.Email,
            # "password": self.Password, no send
        }
        if 'basics' in scope:
            basics = scope['basics']
            if 'alias' in basics:
                user['alias'] = self.Alias
            if 'phoneNumber' in basics:
                user['phoneNumber'] = self.PhoneNumber
            if 'URLPhoto' in basics:
                user['URLPhoto'] = (f'{BACKEND_URL}/api/user/getImage/DefaultImage' if self.URLPhoto is None else f'{BACKEND_URL}/api/user/getImage/{self.URLPhoto}')
        if 'role' in scope:
            user['role'] = self.Role.serialize(scope=scope['role'])
        if 'directInventoryMovements' in scope:
            user['directInventoryMovements'] = [directInventoryMovement.serialize(scope=scope['directInventoryMovements']) for directInventoryMovement in self.DirectInventoryMovements]
        return user


class DirectSaleInternalStatus(db.Base):
    # Table name in database
    __tablename__ = 'DirectSaleInternalStatus'
    # Columns
    DirectSaleInternalStatusId = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    DirectSaleInternalStatus = Column(String(50), unique=True, nullable=False)
    Description = Column(String(200), unique=True, nullable=False)
    OrderNum = Column(Integer, nullable=False)

    # RelationShips
    DirectSales = relationship("DirectSale")

    # Initializing
    def __init__(self, directSaleInternalStatus, description, orderNum):
        self.DirectSaleInternalStatus = directSaleInternalStatus
        self.Description = description
        self.OrderNum = orderNum

    # String representations
    def __repr__(self):
        return (
            f'DirectSaleInternalStatus('
            f'{self.DirectSaleInternalStatusId},'
            f'{self.DirectSaleInternalStatus},{self.Description},'
            f'{self.OrderNum})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        directSaleInternalStatus = {
            "directSaleInternalStatusId": self.DirectSaleInternalStatusId,
            "directSaleInternalStatus": self.DirectSaleInternalStatus,
            "description": self.Description,
            "orderNum": self.OrderNum
        }
        if 'directSales' in scope:
            directSaleInternalStatus['directSales'] = [directSale.serialize(scope=scope['directSales']) for directSale in self.DirectSales]
        return directSaleInternalStatus


class DirectSaleType(db.Base):
    # Table name in database
    __tablename__ = 'DirectSaleType'
    # Columns
    DirectSaleTypeId = Column(Integer, primary_key=True, nullable=False)
    DirectSaleType = Column(String(100), nullable=False)
    Description = Column(String(200), nullable=False)
    # RelationShips
    DirectSales = relationship("DirectSale")

    # Initializing
    def __init__(self, directSaleType, description):
        self.DirectSaleType = directSaleType
        self.Description = description

    # String representations
    def __repr__(self):
        return (
            f'DirectSaleType({self.DirectSaleTypeId},'
            f'{self.DirectSaleType},{self.Description})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        directSaleType = {
            "directSaleTypeId": self.DirectSaleTypeId,
            "directSaleType": self.DirectSaleType,
            "description": self.Description
        }
        if 'directSales' in scope:
            directSaleType['directSales'] = [directSale.serialize(scope=scope['directSales']) for directSale in self.DirectSales]
        return directSaleType


class DirectSaleStackableComment(db.Base):
    # Table name in database
    __tablename__ = 'DirectSaleStackableComment'
    # Columns
    Id = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    DirectSaleId = Column(Integer, ForeignKey("DirectSale.DirectSaleId"), nullable=False)
    UserId = Column(Integer, ForeignKey("User.UserId"), nullable=False)
    Comment = Column(String(100), nullable=True)
    TimeStamp = Column(DateTime(timezone=True), nullable=True)
    # RelationShips
    DirectSale = relationship("DirectSale", back_populates="DirectSaleStackableComments")
    User = relationship("User", back_populates="DirectSaleStackableComments")

    # Initializing
    def __init__(self, comment, timeStamp):
        self.Comment = comment
        self.TimeStamp = timeStamp

    # String representations
    def __repr__(self):
        return (
            f'DirectSaleStackableComment({self.Id},'
            f'{self.DirectSaleId},'
            f'{self.Comment},'
            f'{self.TimeStamp})'
        )

    def serialize(self, scope={}):
        directSaleStackableComment = {
            'id': self.Id,
            'userName': self.User.Name,
            'comment': self.Comment,
            'timeStamp': str(self.TimeStamp)
        }
        if 'directSale' in scope:
            directSaleStackableComment['directSale'] = self.Order.serialize(scope=scope['directSale'])
        if 'user' in scope:
            directSaleStackableComment['user'] = self.User.serialize(scope=scope['user'])
        return directSaleStackableComment


class DirectSale(db.Base):
    # Table name in database
    __tablename__ = 'DirectSale'
    # Columns
    DirectSaleId = Column(Integer, primary_key=True,
                          nullable=False, autoincrement=True)
    DirectSaleTypeId = Column(Integer, ForeignKey(
        "DirectSaleType.DirectSaleTypeId"), nullable=False)
    ClientId = Column(Integer, ForeignKey("Client.ClientId"), nullable=False)
    SellerId = Column(Integer, ForeignKey("User.UserId"), nullable=False)
    DirectSaleInternalStatusId = Column(Integer, ForeignKey(
        "DirectSaleInternalStatus.DirectSaleInternalStatusId"), nullable=False)
    SaleDate = Column(DateTime(timezone=True), nullable=False)
    Shipping = Column(Float, nullable=False)
    # RelationShips
    ProductsInDirectSale = relationship("ProductInDirectSale", cascade="all, delete-orphan")
    ProofOfPayment = relationship("ProofOfPayment", cascade="all, delete-orphan", uselist=False)
    DirectSaleStackableComments = relationship("DirectSaleStackableComment", cascade="all, delete-orphan")
    DirectSale_AmountChangers = relationship("DirectSale_AmountChanger", cascade="all, delete-orphan")
    DirectSale_CommercialTerms = relationship("DirectSale_CommercialTerm", cascade="all, delete-orphan")
    DirectSaleType = relationship("DirectSaleType", back_populates="DirectSales", uselist=False)
    Client = relationship("Client", back_populates="DirectSales")
    Seller = relationship("User", back_populates="DirectSales")
    DirectSaleInternalStatus = relationship("DirectSaleInternalStatus", back_populates="DirectSales")
    # Initializing

    # String representations
    def __repr__(self):
        return (
            f'DirectSale({self.DirectSaleId},{self.DirectSaleTypeId},'
            f'{self.ClientId},{self.SellerId},'
            f'{self.DirectSaleInternalStatusId},{self.SaleDate},'
            f'{self.Shipping})'
        )

    def serialize(self, scope={}):
        directSale = {
            "directSaleId": self.DirectSaleId,
            "saleDate": str(self.SaleDate),
            "shipping": str(round(self.Shipping, 2)),
            "directSaleInternalStatusId": self.DirectSaleInternalStatusId
        }
        if 'directSaleType' in scope:
            directSale['directSaleType'] = {} if self.DirectSaleType is None else self.DirectSaleType.serialize(scope=scope['directSaleType'])
        if 'client' in scope:
            directSale['client'] = {} if self.Client is None else self.Client.serialize(scope=scope['client'])
        if 'seller' in scope:
            directSale['seller'] = {} if self.Seller is None else self.Seller.serialize(scope=scope['seller'])
        if 'proofOfPayment' in scope:
            directSale['proofOfPayment'] = self.ProofOfPayment.serialize(scope=scope['proofOfPayment'])
        if 'directSaleInternalStatus' in scope:
            directSale['directSaleInternalStatus'] = self.DirectSaleInternalStatus.serialize(scope=scope['directSaleInternalStatus'])
        if 'productsInDirectSale' in scope:
            directSale['productsInDirectSale'] = [productsInDirectSale.serialize(scope=scope['productsInDirectSale']) for productsInDirectSale in self.ProductsInDirectSale]
        if 'directSale_amountChangers' in scope:
            directSale['directSale_amountChangers'] = [directSale_AmountChanger.serialize(scope=scope['directSale_amountChangers']) for directSale_AmountChanger in self.DirectSale_AmountChangers]
        if 'directSaleStackableComments' in scope:
            directSale['directSaleStackableComments'] = [directSaleStackableComment.serialize(scope=scope['directSaleStackableComments']) for directSaleStackableComment in self.DirectSaleStackableComments]
        if 'directSale_commercialTerms' in scope:
            directSale['directSale_commercialTerms'] = [directSale_commercialTerm.serialize(scope=scope['directSale_commercialTerms']) for directSale_commercialTerm in self.DirectSale_CommercialTerms]
        return directSale


class ProofOfPayment(db.Base):
    # Table name in database
    __tablename__ = 'ProofOfPayment'
    # Columns
    DirectSaleId = Column(Integer, ForeignKey(
        "DirectSale.DirectSaleId"), primary_key=True, nullable=False)
    OperationNum = Column(String(30), nullable=False)
    VoucherImageUrl = Column(String(200), nullable=True)
    # RelationShips

    # Initializing
    def __init__(self, operationNum=None, voucherImageName=None):
        self.OperationNum = operationNum
        self.VoucherImageUrl = voucherImageName

    # String representations
    def __repr__(self):
        return (
            f'ProofOfPayment({self.DirectSaleId},'
            f'{self.OperationNum},'
            f'{self.VoucherImageUrl})')
    # serialize (to json)

    def serialize(self, scope={}):
        proofOfPayment = {
            'operationNum': self.OperationNum,
            'voucherImageUrl': ownUtils.imagePathToImageURL(self.VoucherImageUrl, 'directSale/proofPayment'),
        }
        return proofOfPayment


class ProductInDirectSale(db.Base):
    # Table name in database
    __tablename__ = 'ProductInDirectSale'
    # Columns
    Id = Column(Integer, primary_key=True, autoincrement=True)
    DirectSaleId = Column(Integer, ForeignKey(
        "DirectSale.DirectSaleId"), nullable=False)
    # Product_Store keys
    InternalSkuStore = Column(String(150), nullable=True)
    StoreId = Column(Integer, nullable=True)
    # Product_SupplierStore
    InternalSkuSupplierStore = Column(String(150), nullable=True)
    SupplierStoreId = Column(Integer, nullable=True)
    # others
    Units = Column(Integer, nullable=False)
    ShippedUnits = Column(Integer, nullable=False)
    CostAtTimeOfSale = Column(Float, nullable=False)
    # args
    __table_args__ = (ForeignKeyConstraint(
        ["InternalSkuStore", "StoreId"],
        ["Product_Store.InternalSku", "Product_Store.StoreId"]),
        ForeignKeyConstraint(
        ["InternalSkuSupplierStore", "SupplierStoreId"],
        ["Product_SupplierStore.InternalSku", "Product_SupplierStore.SupplierStoreId"])
    )
    # RelationShips
    DirectSale = relationship("DirectSale", back_populates="ProductsInDirectSale")
    ProductInDirectSale_AmountChangers = relationship("ProductInDirectSale_AmountChanger", cascade="all, delete-orphan")
    Product_Store = relationship("Product_Store", back_populates="ProductsInDirectSale")
    Product_SupplierStore = relationship("Product_SupplierStore", back_populates="ProductsInDirectSale")

    # Initializing
    def __init__(self, units, costAtTimeOfSale):
        self.Units = units
        self.CostAtTimeOfSale = costAtTimeOfSale
        self.ShippedUnits = 0

    # String representations
    def __repr__(self):
        return (
            f'ProductInDirectSale('
            f'{self.Id},'
            f'{self.DirectSaleId},'
            f'{self.InternalSkuStore},'
            f'{self.StoreId},'
            f'{self.InternalSkuSupplierStore},'
            f'{self.SupplierStoreId},'
            f'{self.Units},'
            f'{self.ShippedUnits},'
            f'{self.CostAtTimeOfSale}'
        )

    def __str__(self):
        return (
            f'ProductInDirectSale('
            f'{self.Id},'
            f'{self.DirectSaleId},'
            f'{self.InternalSkuStore},'
            f'{self.StoreId},'
            f'{self.InternalSkuSupplierStore},'
            f'{self.SupplierStoreId},'
            f'{self.Units},'
            f'{self.ShippedUnits},'
            f'{self.CostAtTimeOfSale})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        productInDirectSale = {
            "id": self.Id,
            "units": str(round(self.Units, 2)),
            "shippedUnits": str(round(self.ShippedUnits, 2)) if self.ShippedUnits is not None else self.ShippedUnits,
            "costAtTimeOfSale": self.CostAtTimeOfSale,
        }
        if 'directSale' in scope:
            productInDirectSale['directSale'] = self.DirectSale.serialize(scope=scope['directSale'])
        if 'productInDirectSale_amountChangers' in scope:
            productInDirectSale['productInDirectSale_amountChangers'] = [productInDirectSale_amountChanger.serialize(scope=scope['productInDirectSale_amountChangers']) for productInDirectSale_amountChanger in self.ProductInDirectSale_AmountChangers]
        if 'product_store' in scope:
            productInDirectSale['product_store'] = None if self.Product_Store is None else self.Product_Store.serialize(scope=scope['product_store'])
        if 'product_supplierStore' in scope:
            productInDirectSale['product_supplierStore'] = None if self.Product_SupplierStore is None else self.Product_SupplierStore.serialize(scope=scope['product_supplierStore'])
        if 'storeExits' in scope:
            productInDirectSale['storeExits'] = [storeExit.serialize(scope=scope['storeExits']) for storeExit in self.StoreExits]
        if 'productAtStore' in scope:
            productInDirectSale['productAtStore'] = self.returnProductAtStore()
        return productInDirectSale

    # serialize (to json)
    def returnProductAtStore(self):
        if self.Product_Store:
            product_obj = {
                "internalSku": self.Product_Store.Product.InternalSku,
                "productDescription": self.Product_Store.Product.return_description_to_show(),
                "productImage": self.Product_Store.Product.return_main_photo(),
                "productBase": self.Product_Store.Product.ProductBase.serialize(scope={})
            }
            store_obj = {
                "type": "store",
                **self.Product_Store.Store.serialize()
            }
            internalLocations = [locationLevelItems_product_store.serialize(scope={'locationLevelItem': {}}) for locationLevelItems_product_store in self.Product_Store.LocationLevelItems_Product_Store]
        else:
            product_obj = {
                "internalSku": self.Product_SupplierStore.Product.InternalSku,
                "productDescription": self.Product_SupplierStore.Product.return_description_to_show(),
                "productImage": self.Product_SupplierStore.Product.return_main_photo(),
                "productBase": self.Product_SupplierStore.Product.ProductBase.serialize(scope={})
            }
            store_obj = {
                "type": "supplier_store",
                **self.Product_SupplierStore.SupplierStore.serialize()
            }
            internalLocations = None
        return {
            "product": product_obj,
            "store": store_obj,
            "internalLocations": internalLocations,
        }


class Company(db.Base):
    # Table name in database
    __tablename__ = 'Company'
    # Columns
    CompanyId = Column(Integer, primary_key=True,
                       nullable=False, autoincrement=True)
    ImageCompany = Column(String(150), nullable=True)
    CompanyName = Column(String(150), nullable=True)
    RFC = Column(String(13), nullable=True)
    Url_SAT = Column(String(150), nullable=True)
    Qr_code_SAT = Column(String(150), nullable=True)
    # RelationShips
    CommercialTerms = relationship("CommercialTerm")
    BankAccounts = relationship("BankAccount")

    # Initializing
    def __init__(self, image=None, name=None, RFC=None, url_SAT=None, qr_code_SAT=None):
        self.ImageCompany = image
        self.CompanyName = name
        self.RFC = RFC
        self.Url_SAT = url_SAT
        self.Qr_code_SAT = qr_code_SAT

    # String representations
    def __repr__(self):
        return f'Company({self.CompanyId},{self.ImageCompany},{self.CompanyName},{self.RFC}, {self.Url_SAT},{self.Qr_code_SAT})'

    # serialize (to json)
    def serialize(self, scope={}):
        company = {
            "companyId": self.CompanyId,
            "imageCompany": f'{BACKEND_URL}/api/company/getCompanyPhoto',
            "companyName": self.CompanyName,
            "RFC": self.RFC,
            "url_SAT": self.Url_SAT,
            "qr_code_SAT": f'{BACKEND_URL}/api/company/getCompanyQrCode',
        }
        if 'commercialTerms' in scope:
            company['commercialTerms'] = [commercialTerm.serialize(scope=scope['commercialTerms']) for commercialTerm in self.CommercialTerms]
        if 'bankAccounts' in scope:
            company['bankAccounts'] = [bankAccount.serialize(scope=scope['bankAccounts']) for bankAccount in self.BankAccounts]
        return company


class DirectSale_CommercialTerm(db.Base):
    # Table name in database
    __tablename__ = 'DirectSale_CommercialTerm'
    # Columns
    DirectSaleId = Column(Integer, ForeignKey("DirectSale.DirectSaleId"), primary_key=True, nullable=False)
    CommercialTermId = Column(Integer, ForeignKey("CommercialTerm.Id"), primary_key=True, nullable=False)
    # RelationShips
    DirectSale = relationship("DirectSale", back_populates="DirectSale_CommercialTerms")
    CommercialTerm = relationship("CommercialTerm", back_populates="DirectSales_CommercialTerm")

    # String representation
    def __repr__(self):
        return f'DirectSale_CommercialTerm({self.DirectSaleId},{self.CommercialTermId})'

    # serialize (to json)
    def serialize(self, scope={}):
        directSale_commercialTerm = {
        }
        if 'directSale' in scope:
            directSale_commercialTerm['directSale'] = self.DirectSale.serialize(scope=scope['directSale'])
        if 'commercialTerm' in scope:
            directSale_commercialTerm['commercialTerm'] = self.CommercialTerm.serialize(scope=scope['commercialTerm'])
        return directSale_commercialTerm


class CommercialTerm(db.Base):
    # Table name in database
    __tablename__ = 'CommercialTerm'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    CompanyId = Column(Integer, ForeignKey(
        "Company.CompanyId"), nullable=False)
    CommercialTerm = Column(String(500), nullable=False, unique=True)
    # RelationShips
    Company = relationship("Company", back_populates="CommercialTerms", uselist=False)
    DirectSales_CommercialTerm = relationship("DirectSale_CommercialTerm")

    # Initializing
    def __init__(self, commercialTerm=None):
        self.CommercialTerm = commercialTerm

    # String representation
    def __repr__(self):
        return f'CommercialTerm({self.Id},{self.CompanyId},{self.CommercialTerm})'

    # serialize (to json)
    def serialize(self, scope={}):
        commercialTerm = {
            "id": self.Id,
            "commercialTerm": self.CommercialTerm
        }
        if 'company' in scope:
            commercialTerm['company'] = self.Company.serialize(scope=scope['company'])
        if 'directSales_commercialTerm' in scope:
            commercialTerm['directSales_commercialTerm'] = [directSale_commercialTerm.serialize(scope=scope['directSales_commercialTerm']) for directSale_commercialTerm in self.DirectSales_CommercialTerm]
        return commercialTerm


class BankAccount(db.Base):
    # Table name in database
    __tablename__ = 'BankAccount'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    CompanyId = Column(Integer, ForeignKey(
        "Company.CompanyId"), nullable=False)
    BankName = Column(String(50), nullable=False)
    AccountNumber = Column(String(30), unique=True, nullable=True)
    Clabe = Column(String(30), unique=True, nullable=True)
    # RelationShips
    Company = relationship("Company", back_populates="BankAccounts", uselist=False)

    # Initializing
    def __init__(self, bankName=None, accountNumber=None, clabe=None):
        self.BankName = bankName
        self.AccountNumber = accountNumber
        self.Clabe = clabe

    # String representations
    def __repr__(self):
        return f'BankAccount({self.Id},{self.CompanyId},{self.BankName},{self.AccountNumber}, {self.Clabe})'
    # serialize (to json)

    def serialize(self, scope={}):
        bankAccount = {
            "id": self.Id,
            "bankName": self.BankName,
            "accountNumber": self.AccountNumber,
            "clabe": self.Clabe
        }
        if 'company' in scope:
            bankAccount['company'] = self.Company.serialize(scope=scope['company'])
        return bankAccount


class SupplierPlatform(db.Base):
    # Table name in database
    __tablename__ = 'SupplierPlatform'
    # Columns
    RFC = Column(String(13), ForeignKey(
        "Supplier.RFC"), primary_key=True, nullable=False)
    Url = Column(String(500), nullable=False)
    User = Column(String(70), nullable=True)
    Password = Column(String(100), nullable=True)
    Token = Column(String(1400), nullable=True)
    RefreshToken = Column(String(1000), nullable=True)

    # RelationShips
    # Initializing
    def __init__(self, url=None, user=None,
                 password=None, token=None, refreshToken=None):
        self.Url = url
        self.User = user
        self.Password = password
        self.Token = token
        self.RefreshToken = refreshToken
    # String representations

    def __repr__(self):
        return (
            f'SupplierPlatform({self.RFC},{self.Url},'
            f'{self.User},{self.Password},{self.Token},'
            f'{self.RefreshToken})'
        )

    # serialize (to json)

    def serialize(self, scope={}):
        supplierPlatform = {
            "rfc": self.RFC,
            "user": self.Url,
            "password": self.User,
            "token": self.Token,
            "refresh_token": self.RefreshToken
        }
        return supplierPlatform


class AmountChangerType(db.Base):
    # Table name in database
    __tablename__ = 'AmountChangerType'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    Name = Column(String(20), nullable=False)
    Description = Column(String(200), nullable=False)
    # RelationShips
    AmountChangers = relationship("AmountChanger", uselist=True)

    # Initializing
    def __init__(self, name=None, description=None):
        self.Name = name
        self.Description = description

    # String representation
    def __repr__(self):
        return (
            f'AmountChangerType({self.Id}, {self.Name}, {self.Description})'
        )

    # serialize (json)
    def serialize(self, scope={}):
        amountChangerType = {
            "id": self.Id,
            "name": self.Name,
            "descripcion": self.Description
        }
        if 'amountChangers' in scope:
            amountChangerType['amountChangers'] = [amountChanger.serialize(scope=scope['amountChangers']) for amountChanger in self.AmountChangers]
        return amountChangerType


class AmountChanger(db.Base):
    # Table name in database
    __tablename__ = 'AmountChanger'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    AmountChangerTypeId = Column(Integer, ForeignKey(
        "AmountChangerType.Id"), nullable=False)
    ChangerGroupId = Column(Integer, ForeignKey("ChangerGroup.Id"), nullable=True)
    Name = Column(String(20), nullable=False)
    Operation = Column(String(1), nullable=False)
    CurrentValue = Column(Float, nullable=True)
    # RelationShips
    ProductInDirectSales_AmountChanger = relationship('ProductInDirectSale_AmountChanger', cascade="all, delete-orphan")
    DirectSales_AmountChanger = relationship('DirectSale_AmountChanger', cascade="all, delete-orphan")
    AmountChangerType = relationship("AmountChangerType", back_populates="AmountChangers", uselist=False)
    ChangerGroup = relationship('ChangerGroup', back_populates="AmountChangers", uselist=False)

    # Initializing
    def __init__(self, name=None, operation=None, currentValue=None):
        self.Name = name
        self.Operation = operation
        self.CurrentValue = currentValue

    # String representation
    def __repr__(self):
        return (
            f'AmountChanger({self.Id}, {self.Name}, {self.Operation}, {self.CurrentValue})'
        )

    def serialize(self, scope={}):
        amountChanger = {
            "id": self.Id,
            "name": self.Name,
            "operation": self.Operation,
            "currentValue": self.CurrentValue
        }
        if 'productInDirectSales_amountChanger' in scope:
            amountChanger['productInDirectSales_amountChanger'] = [productInDirectSale_amountChanger.serialize(scope=scope['productInDirectSales_amountChanger']) for productInDirectSale_amountChanger in self.ProductInDirectSales_AmountChanger]
        if 'directSales_amountChanger' in scope:
            amountChanger['directSales_amountChanger'] = [directSale_amountChanger.serialize(scope=scope['directSales_amountChanger']) for directSale_amountChanger in self.DirectSales_AmountChanger]
        if 'amountChangerType' in scope:
            amountChanger['amountChangerType'] = {} if self.AmountChangerType is None else self.AmountChangerType.serialize(scope=scope['amountChangerType'])
        if 'changerGroup' in scope:
            amountChanger['changerGroup'] = {} if self.ChangerGroup is None else self.ChangerGroup.serialize(scope=scope['changerGroup'])
        return amountChanger


class ProductInDirectSale_AmountChanger(db.Base):
    # Table name in database
    __tablename__ = 'ProductInDirectSale_AmountChanger'
    # Columns
    ProductInDirectSaleId = Column(Integer, ForeignKey(
        "ProductInDirectSale.Id"), primary_key=True, nullable=False)
    AmountChangerId = Column(Integer, ForeignKey("AmountChanger.Id"), primary_key=True, nullable=False)
    Amount = Column(Float, nullable=False)
    # RelationShips
    ProductInDirectSale = relationship("ProductInDirectSale", back_populates="ProductInDirectSale_AmountChangers")
    AmountChanger = relationship(
        "AmountChanger", back_populates="ProductInDirectSales_AmountChanger")

    # Initializing
    def __init__(self, amount):
        self.Amount = amount

    # String representation
    def __repr__(self):
        return (
            f'ProductInDirectSale_AmountChanger({self.ProductInDirectSaleId}, {self.AmountChangerId}, {self.Amount})'
        )

    # serialize (json)
    def serialize(self, scope={}):
        productInDirectSale_amountChanger = {
            'amount': self.Amount
        }
        if 'productInDirectSale' in scope:
            productInDirectSale_amountChanger['productInDirectSale'] = self.ProductInDirectSale.serialize(scope=scope['productInDirectSale'])
        if 'amountChanger' in scope:
            productInDirectSale_amountChanger['amountChanger'] = self.AmountChanger.serialize(scope=scope['amountChanger'])
        return productInDirectSale_amountChanger


class DirectSale_AmountChanger(db.Base):
    # Table name in database
    __tablename__ = 'DirectSale_AmountChanger'
    # Columns
    DirectSaleId = Column(Integer, ForeignKey(
        "DirectSale.DirectSaleId"), primary_key=True, nullable=False)
    AmountChangerId = Column(
        Integer, ForeignKey("AmountChanger.Id"), primary_key=True, nullable=False)
    Amount = Column(Float, nullable=False)
    # RelationShips
    DirectSale = relationship(
        "DirectSale", back_populates="DirectSale_AmountChangers")
    AmountChanger = relationship(
        "AmountChanger", back_populates="DirectSales_AmountChanger")

    # Initializing
    def __init__(self, amount):
        self.Amount = amount

    # String representation
    def __repr__(self):
        return (
            f'DirectSale_AmountChanger({self.DirectSaleId}, {self.AmountChangerId}, {self.Amount})'
        )

    # serialize (json)
    def serialize(self, scope={}):
        directSale_amountChanger = {
            'amount': self.Amount
        }
        if 'directSale' in scope:
            directSale_amountChanger['directSale'] = self.DirectSale.serialize(scope=scope['directSale'])
        if 'amountChanger' in scope:
            directSale_amountChanger['amountChanger'] = self.AmountChanger.serialize(scope=scope['amountChanger'])
        return directSale_amountChanger


class ChangerGroup(db.Base):
    # Table name in database
    __tablename__ = 'ChangerGroup'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    Name = Column(String(50), unique=True, nullable=False)
    Description = Column(String(200), unique=True, nullable=False)
    # RelationShips
    AmountChangers = relationship("AmountChanger", uselist=True)

    # Initializing
    def __init__(self, name=None, description=None):
        self.Name = name
        self.Description = description

    # String representations
    def __repr__(self):
        return (
            f'ChangerGroup({self.Name},'
            f'{self.Description})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        changerGroup = {
            'id': self.Id,
            "name": self.Name,
            "descripcion": self.Description
        }
        if 'amountChangers' in scope:
            changerGroup['amountChangers'] = [amountChanger.serialize(scope=scope['amountChangers']) for amountChanger in self.AmountChangers]
        return changerGroup


class Kit(db.Base):
    # Table name in database
    __tablename__ = 'Kit'
    # Primary key
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    # Columns
    Title = Column(String(100), unique=True, nullable=False)
    Description = Column(String(3000), nullable=False)
    # RelationShips
    Kit_Products = relationship("Kit_Product", cascade="all, delete-orphan")
    Kit_PublicationBases = relationship("Kit_PublicationBase")

    # Initializing
    def __init__(self, title=None, descripcion=None):
        self.Title = title
        self.Description = descripcion

    # String representations
    def __repr__(self):
        return (
            f'Kit('
            f'{self.Id},'
            f'{self.Title}'
            f'{self.Description}'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "id": self.Id,
            "title": self.Title,
            "description": self.Description
        }

    def serialize_with_products(self):
        return {
            "id": self.Id,
            "title": self.Title,
            "description": self.Description,
            "products": [kit_product.serialize() for kit_product in self.Kit_Products]
        }

    def serialize_table_info_products(self):
        return {
            "id": self.Id,
            "title": self.Title,
            "description": self.Description,
            "products": [kit_product.serialize_table_info_product() for kit_product in self.Kit_Products]
        }


class Kit_Product(db.Base):
    # Table name in database
    __tablename__ = 'Kit_Product'
    # Columns
    # Kit key
    KitId = Column(Integer, ForeignKey("Kit.Id"), primary_key=True, nullable=False)
    # Product key
    InternalSku = Column(String(150), ForeignKey("Product.InternalSku"), primary_key=True, nullable=False)
    # Self columns
    Amount = Column(Integer, nullable=False)
    # RelationShips
    kit = relationship("Kit", back_populates="Kit_Products")
    Product = relationship("Product", back_populates="Kits_Product")

    Product_Store_kit_Products = relationship("Product_Store_kit_Product")
    Product_SupplierStore_kit_Products = relationship("Product_SupplierStore_kit_Product")

    # Initializing
    def __init__(self, amount=None):
        self.Amount = amount

    # String representations
    def __repr__(self):
        return (
            f'Kit_Product('
            f'{self.KitId},'
            f'{self.InternalSku},'
            f'{self.Amount}'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "kitId": self.KitId,
            "internalSku": self.InternalSku,
            "amount": self.Amount
        }

    def serialize_with_info_product(self):
        return {
            "kitId": self.KitId,
            "internalSku": self.InternalSku,
            "amount": self.Amount,
            "product": self.Product.serialize()
        }

    def serialize_table_info_product(self):
        return {
            "kitId": self.KitId,
            "amount": self.Amount,
            "product": self.Product.serialize_table_info_product()
        }


class Product_Store_kit_Product(db.Base):
    # Table name in database
    __tablename__ = 'Product_Store_kit_Product'
    # Columns
    # Kit_product keys
    KitId = Column(Integer, primary_key=True, nullable=False)
    InternalSkuKit = Column(String(150), primary_key=True, nullable=False)
    # Product_Store keys
    InternalSkuStore = Column(String(150), primary_key=True, nullable=False)
    StoreId = Column(Integer, primary_key=True, nullable=False)

    # Constraint
    __table_args__ = __table_args__ = (ForeignKeyConstraint(
        ["InternalSkuStore", "StoreId"],
        ["Product_Store.InternalSku", "Product_Store.StoreId"]),
        ForeignKeyConstraint(
        ["KitId", "InternalSkuKit"],
        ["Kit_Product.KitId", "Kit_Product.InternalSku"])
    )
    # RelationShips
    Product_Store = relationship("Product_Store", back_populates="Product_Store_kit_Products")
    Kit_Product = relationship("Kit_Product", back_populates="Product_Store_kit_Products")

    # Initializing
    def __init__(self, amount):
        self.Amount = amount

    # String representations
    def __repr__(self):
        return (
            f'Product_Store_kit_Product('
            f'{self.KitId},'
            f'{self.InternalSkuKit},'
            f'{self.StoreId}),'
            f'{self.InternalSkuStore})'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "publicationId": self.PublicationId,
            "internalSku": self.InternalSku,
            "storeId": self.StoreId
        }


class Product_SupplierStore_kit_Product(db.Base):
    # Table name in database
    __tablename__ = 'Product_SupplierStore_kit_Product'
    # Columns
    # Kit_product keys
    KitId = Column(Integer, primary_key=True, nullable=False)
    InternalSkuKit = Column(String(150), primary_key=True, nullable=False)
    # Product_Store keys
    InternalSkuSupplierStoreStore = Column(String(150), primary_key=True, nullable=False)
    SupplierStoreId = Column(Integer, primary_key=True, nullable=False)

    # Constraint
    __table_args__ = __table_args__ = (ForeignKeyConstraint(
        ["InternalSkuSupplierStoreStore", "SupplierStoreId"],
        ["Product_SupplierStore.InternalSku", "Product_SupplierStore.SupplierStoreId"]),
        ForeignKeyConstraint(
        ["KitId", "InternalSkuKit"],
        ["Kit_Product.KitId", "Kit_Product.InternalSku"])
    )
    # RelationShips
    Product_SupplierStore = relationship("Product_SupplierStore", back_populates="Product_SupplierStore_kit_Products")
    Kit_Product = relationship("Kit_Product", back_populates="Product_SupplierStore_kit_Products")

    # Initializing
    def __init__(self, amount):
        self.Amount = amount

    # String representations
    def __repr__(self):
        return (
            f'Product_SupplierStore_kit_Product('
            f'{self.KitId},'
            f'{self.InternalSkuKit},'
            f'{self.StoreId}),'
            f'{self.InternalSkuStore})'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "publicationId": self.PublicationId,
            "internalSku": self.InternalSku,
            "storeId": self.StoreId
        }


class Kit_PublicationBase(db.Base):
    # Table name in database
    __tablename__ = 'Kit_Publication'
    # Columns
    # Kit key
    KitId = Column(Integer, ForeignKey("Kit.Id"), primary_key=True, nullable=False)
    # Publication key
    PublicationBaseId = Column(Integer, ForeignKey("PublicationBase.Id"), primary_key=True, nullable=False)
    # RelationShips
    PublicationBase = relationship("PublicationBase", back_populates="Kits_PublicationBase")
    Kit = relationship("Kit", back_populates="Kit_PublicationBases")

    # Initializing
    def __init__(self, amount):
        self.Amount = amount

    # String representations
    def __repr__(self):
        return (
            f'Kit_Publication('
            f'{self.KitId},'
            f'{self.PublicationId})'
        )

    # serialize (to json)
    def serialize(self):
        return {
            "publicationId": self.PublicationId,
            "kitId": self.KitId,
        }


class LocationLevel(db.Base):
    # Table name in database
    __tablename__ = 'LocationLevel'
    # Columns
    # LocationLevel key
    Level = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    # Anothers
    Name = Column(String(50), nullable=False, unique=True)
    Description = Column(String(300), nullable=False, unique=True)
    # RelationShips
    LocationLevelTypes = relationship("LocationLevelType")

    # Initializing
    def __init__(self, name, description):
        self.Name = name
        self.Description = description

    # String representations
    def __repr__(self):
        return (
            f'LocationLevel('
            f'{self.Name},'
            f'{self.Description})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        locationLevel = {
            "level": self.Level,
            "name": self.Name,
            "description": self.Description,
        }
        if 'locationLevelTypes' in scope:
            locationLevel['locationLevelTypes'] = [locationLevelType.serialize(scope=scope['locationLevelTypes']) for locationLevelType in self.LocationLevelTypes]
        return locationLevel


class LocationLevelType(db.Base):
    # Table name in database
    __tablename__ = 'LocationLevelType'
    # Columns
    # LocationLevelType key
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    Name = Column(String(50), nullable=False, unique=True)
    Description = Column(String(300), nullable=False, unique=True)
    LocationLevelLevel = Column(Integer, ForeignKey("LocationLevel.Level"), nullable=False)

    # RelationShips
    LocationLevel = relationship("LocationLevel", back_populates="LocationLevelTypes")
    LocationLevelItems = relationship("LocationLevelItem")

    # Initializing
    def __init__(self, name, description):
        self.Name = name
        self.Description = description

    # String representations
    def __repr__(self):
        return (
            f'LocationLevelType('
            f'{self.Id})'
            f'{self.Name},'
            f'{self.Description})'
            f'{self.LocationLevelLevel})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        locationLevelType = {
            "id": self.Id,
            "name": self.Name,
            "description": self.Description
        }
        if 'locationLevel' in scope:
            locationLevelType['locationLevel'] = self.LocationLevel.serialize(scope=scope['locationLevel'])
        if 'locationLevelItems' in scope:
            locationLevelType['locationLevelItems'] = [locationLevelItem.serilaize(scope=scope['locationLevelItems']) for locationLevelItem in self.LocationLevelItems]
        return locationLevelType


class LocationLevelItem(db.Base):
    # Table name in database
    __tablename__ = 'LocationLevelItem'
    # Columns
    # LocationLevelItem key
    Id = Column(Integer, primary_key=True, nullable=False)
    Name = Column(String(100), nullable=False)
    StoreId = Column(Integer, ForeignKey("Store.StoreId"), nullable=False)
    LocationLevelTypeId = Column(Integer, ForeignKey("LocationLevelType.Id"), nullable=False)
    ParentId = Column(Integer, ForeignKey("LocationLevelItem.Id"), nullable=True)
    # RelationShips
    LocationLevelType = relationship("LocationLevelType", back_populates="LocationLevelItems", uselist=False)
    Store = relationship("Store", back_populates="LocationLevelItems")
    LocationLevelItemChildren = relationship("LocationLevelItem")
    LocationLevelItemParent = relationship("LocationLevelItem", back_populates="LocationLevelItemChildren", remote_side=[Id])
    LocationLevelItem_Product_Stores = relationship("LocationLevelItem_Product_Store", cascade="all, delete-orphan")
    __table_args__ = (
        UniqueConstraint('ParentId', 'LocationLevelTypeId', 'Name',
                         name='uq_LocationLevelTypeId_ParentId_Name'),
    )

    # Initializing
    def __init__(self, name):
        self.Name = name

    # String representations
    def __repr__(self):
        return (
            f'LocationLevelItem('
            f'{self.Id},'
            f'{self.Name},'
            f'{self.StoreId},'
            f'{self.LocationLevelTypeId},'
            f'{self.ParentId})'
        )

    def write_entire_position(self):
        stack_position = []
        currentLevelItem = self
        while currentLevelItem:
            stack_position.append(currentLevelItem)
            currentLevelItem = currentLevelItem.LocationLevelItemParent
        entire_position = ''
        while stack_position:
            position = stack_position.pop()
            position_aux = position.LocationLevelType.Name if (position.LocationLevelType.Name.lower() == position.Name.lower()) else f'{position.LocationLevelType.Name}:{position.Name}'
            if entire_position:
                entire_position = f'{entire_position} | {position_aux}'
            else:
                entire_position = position_aux
        return entire_position

    # serialize (to json)
    def serialize(self, scope={}):
        location_level_item = {
            "id": self.Id,
            "name": self.Name,
            "entireName": self.write_entire_position(),
            "parentId": self.ParentId
        }
        if 'locationLevelType' in scope:
            location_level_item['locationLevelType'] = self.LocationLevelType.serialize(scope=scope['locationLevelType'])
        if 'locationLevelItem_Product_Store' in scope:
            location_level_item['locationLevelItem_Product_Store'] = [locationLevelItem_Product_Store.serialize(scope=scope['locationLevelItem_Product_Store']) for locationLevelItem_Product_Store in self.LocationLevelItem_Product_Stores]
        return location_level_item


class LocationLevelItem_Product_Store(db.Base):
    # Table name in database
    __tablename__ = 'LocationLevelItem_Product_Store'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    # LocationLevelItem keys
    LocationLevelItemId = Column(Integer, ForeignKey("LocationLevelItem.Id"), nullable=False)
    # Product_Store keys
    InternalSku = Column(String(150), nullable=False)
    StoreId = Column(Integer, nullable=False)
    #
    Stock = Column(Float, nullable=False)
    __table_args__ = (ForeignKeyConstraint(
        ["InternalSku", "StoreId"],
        ["Product_Store.InternalSku", "Product_Store.StoreId"]),
        UniqueConstraint('LocationLevelItemId', 'InternalSku',
                         name='uq_LocationLevelItemId_InternalSku')  # unique constaint -> 'LocationLevelItemId', 'InternalSku'
    )
    # RelationShips
    LocationLevelItem = relationship("LocationLevelItem", back_populates="LocationLevelItem_Product_Stores")
    Product_Store = relationship("Product_Store", back_populates="LocationLevelItems_Product_Store")
    DirectInventoryMovements = relationship("DirectInventoryMovement")
    ProductsEnteredIntoStore = relationship("ProductEnteredIntoStore")

    # Initializing
    def __init__(self, stock):
        self.Stock = stock

    # String representations
    def __repr__(self):
        return (
            f'LocationLevelItem_Product_Store('
            f'{self.LocationLevelItemId},'
            f'{self.InternalSku},'
            f'{self.StoreId},'
            f'{self.Stock})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        locationLevelItem_product_store = {
            "stock": self.Stock,
        }
        if 'extras' in scope:
            extras = scope['extras']
            if 'locationLevelItemId' in extras:
                locationLevelItem_product_store['locationLevelItemId'] = self.LocationLevelItemId
            if 'internalSku' in extras:
                locationLevelItem_product_store['internalSku'] = self.InternalSku
            if 'storeId' in extras:
                locationLevelItem_product_store['storeId'] = self.StoreId
        if 'locationLevelItem' in scope:
            locationLevelItem_product_store['locationLevelItem'] = self.LocationLevelItem.serialize(scope=scope['locationLevelItem'])
        if 'product_store' in scope:
            locationLevelItem_product_store['product_store'] = self.Product_Store.serialize(scope=scope['product_store'])
        if 'directInventoryMovements' in scope:
            locationLevelItem_product_store['directInventoryMovements'] = [directInventoryMovement.serialize(scope=scope['directInventoryMovements']) for directInventoryMovement in self.DirectInventoryMovements]
        if 'productsEnteredIntoStore' in scope:
            locationLevelItem_product_store['productsEnteredIntoStore'] = [productEnteredIntoStore.serialize(scope=scope['productsEnteredIntoStore']) for productEnteredIntoStore in self.ProductsEnteredIntoStore]
        return locationLevelItem_product_store


class PublicationProduct_Product_Store(db.Base):
    # Table name in database
    __tablename__ = 'PublicationProduct_Product_Store'
    #
    # Kit_product keys
    PublicationProductId = Column(Integer, ForeignKey("PublicationProduct.Id"), primary_key=True, nullable=False)
    # Product_Store keys
    InternalSku = Column(String(150), primary_key=True, nullable=False)
    StoreId = Column(Integer, primary_key=True, nullable=False)

    # Constraint
    __table_args__ = (ForeignKeyConstraint(
        ["InternalSku", "StoreId"],
        ["Product_Store.InternalSku", "Product_Store.StoreId"]),
    )
    # RelationShips
    Product_Store = relationship("Product_Store", back_populates="PublicationProducts_Product_Store")
    PublicationProduct = relationship("PublicationProduct", back_populates="PublicationProduct_Product_Stores")
    # Initializing

    # String representations
    def __repr__(self):
        return (
            f'PublicationProduct_Product_Store('
            f'{self.PublicationProductId}'
            f'{self.InternalSku}'
            f'{self.StoreId})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        publicationProduct_product_store = {}
        if 'product_store' in scope:
            publicationProduct_product_store['product_store'] = self.Product_Store.serialize(scope=scope['product_store'])
        if 'publicationProduct' in scope:
            publicationProduct_product_store['publicationProduct'] = self.PublicationProduct.serialize(scope=scope['publicationProduct'])
        return publicationProduct_product_store


class PublicationProduct_Product_SupplierStore(db.Base):
    # Table name in database
    __tablename__ = 'PublicationProduct_Product_SupplierStore'
    # Columns
    # PublicationProduct keys
    PublicationProductId = Column(Integer, ForeignKey("PublicationProduct.Id"), primary_key=True, nullable=False)
    # Product_Store keys
    InternalSku = Column(String(150), primary_key=True, nullable=False)
    SupplierStoreId = Column(Integer, primary_key=True, nullable=False)
    # cantidad ingresada
    __table_args__ = (ForeignKeyConstraint(
        ["InternalSku", "SupplierStoreId"],
        ["Product_SupplierStore.InternalSku", "Product_SupplierStore.SupplierStoreId"]),
    )
    # RelationShips
    Product_SupplierStore = relationship("Product_SupplierStore", back_populates="PublicationProducts_Product_SupplierStore")
    PublicationProduct = relationship("PublicationProduct", back_populates="PublicationProduct_Product_SupplierStores")

    # String representations
    def __repr__(self):
        return (
            f'Publication_Product_SupplierStore('
            f'{self.PublicationId},'
            f'{self.InternalSku},'
            f'{self.StoreId})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        publicationProduct_product_supplierStore = {}
        if 'poduct_supplierStore' in scope:
            publicationProduct_product_supplierStore['poduct_supplierStore'] = self.Product_SupplierStore.serialize(scope=scope['poduct_supplierStore'])
        if 'publicationProduct' in scope:
            publicationProduct_product_supplierStore['publicationProduct'] = self.PublicationProduct.serialize(scope=scope['publicationProduct'])
        return publicationProduct_product_supplierStore


class PublicationProduct(db.Base):
    # Table name in database
    __tablename__ = 'PublicationProduct'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    MarketplacePublicationVariationId = Column(String(50), nullable=False)
    SkuMarketplaceVariation = Column(String(50), nullable=True)
    PublicationBaseId = Column(Integer, ForeignKey("PublicationBase.Id"), nullable=False)
    Stock = Column(Float, nullable=True)
    Cost = Column(Float, nullable=True)

    # RelationShips
    PublicationProduct_Product_Stores = relationship("PublicationProduct_Product_Store")
    PublicationProduct_Product_SupplierStores = relationship("PublicationProduct_Product_SupplierStore")
    PublicationBase = relationship("PublicationBase", back_populates='PublicationProducts')
    ProductsInOrder = relationship("ProductInOrder")
    PublicationProductVariationCustoms = relationship("PublicationProductVariationCustom")

    # Initializing
    def __init__(self, stock, cost, marketplacePublicationVariationId, skuMarketplaceVariation):
        self.Stock = stock
        self.Cost = cost
        self.MarketplacePublicationVariationId = marketplacePublicationVariationId
        self.SkuMarketplaceVariation = skuMarketplaceVariation

    # String representations
    def __repr__(self):
        return (
            f'PublicationProduct('
            f'{self.Id},'
            f'{self.MarketplacePublicationVariationId},'
            f'{self.SkuMarketplaceVariation},'
            f'{self.PublicationBaseId},'
            f'{self.Stock},'
            f'{self.Cost})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        publication_product = {
            "id": self.Id,
            "marketplacePublicationVariationId": self.MarketplacePublicationVariationId,
            "skuMarketplaceVariation": self.SkuMarketplaceVariation,
            "publicationBaseId": self.PublicationBaseId,
            "stock": self.Stock,
            "cost": self.Cost
        }
        if 'publicationProduct_product_stores' in scope:
            publication_product['publicationProduct_product_stores'] = [publicationProduct_Product_Store.serialize(scope=scope['publicationProduct_product_stores']) for publicationProduct_Product_Store in self.PublicationProduct_Product_Stores]
        if 'publicationProduct_product_supplierStores' in scope:
            publication_product['publicationProduct_product_supplierStores'] = [publicationProduct_Product_SupplierStore.serialize(scope=scope['publicationProduct_product_supplierStores']) for publicationProduct_Product_SupplierStore in self.PublicationProduct_Product_SupplierStores]
        if 'publicationBase' in scope:
            publication_product['publicationBase'] = self.PublicationBase.serialize(scope=scope['publicationBase'])
        if 'productsInOrder' in scope:
            publication_product['productsInOrder'] = [productInOrder.serialize(scope=scope['productsInOrder']) for productInOrder in self.ProductsInOrder]
        return publication_product


class PublicationBase(db.Base):
    # Table name in database
    __tablename__ = 'PublicationBase'
    # Columns
    #   primary key
    Id = Column(Integer, primary_key=True, nullable=False)
    #   foreign key
    MarketplaceId = Column(Integer, ForeignKey("Marketplace.Id"), nullable=False)
    PublicationStatusId = Column(Integer, ForeignKey("PublicationStatus.Id"), nullable=False)
    #   Others
    MarkeplacePublicationId = Column(String(100), unique=True, nullable=False)
    Stock = Column(Float, nullable=True)
    Price = Column(Float, nullable=True)
    BoardingTime = Column(Integer, nullable=True)
    # RelationShips
    Marketplace = relationship('Marketplace', back_populates='PublicationBases', uselist=False)
    Warranties = relationship('Warranty')
    PublicationStatus = relationship('PublicationStatus', back_populates='PublicationBases', uselist=False)
    Kits_PublicationBase = relationship("Kit_PublicationBase")
    PublicationProducts = relationship("PublicationProduct")
    PublicationBase_PublicationCustomVariations = relationship("PublicationBase_PublicationCustomVariation")

    # Initializing
    def __init__(self, markeplacePublicationId=None, stock=None, price=None, boardingTime=None):
        self.MarkeplacePublicationId = markeplacePublicationId
        self.Stock = stock
        self.Price = price
        self.BoardingTime = boardingTime

    # String representations
    def __repr__(self):
        return (
            f'PublicationBase('
            f'{self.Id}'
            f'{self.MarketplaceId}'
            f'{self.PublicationStatusId}'
            f'{self.Stock}'
            f'{self.Price})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        publicationBase = {
            "id": self.Id,
            "markeplacePublicationId": self.MarkeplacePublicationId,
            "stock": self.Stock,
            "cost": self.Price,
            "boardingTime": self.BoardingTime
        }
        if 'marketplace' in scope:
            publicationBase['marketplace'] = self.Marketplace.serialize(scope=scope['marketplace'])
        if 'publicationStatus' in scope:
            publicationBase['publicationStatus'] = self.PublicationStatus.serialize(scope=scope['publicationStatus'])
        if 'warranties' in scope:
            publicationBase['warranties'] = [warranty.serialize(scope=scope['warranties']) for warranty in self.Warranties]
        if 'publicationProducts' in scope:
            publicationBase['publicationProducts'] = [publicationProduct.serialize(scope=scope['publicationProducts']) for publicationProduct in self.PublicationProducts]
        return publicationBase


class PublicationBase_PublicationCustomVariation(db.Base):
    # Table name in database
    __tablename__ = 'PublicationBase_PublicationCustomVariation'
    # Columns
    Id = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    PublicationBaseId = Column(Integer, ForeignKey(
        "PublicationBase.Id"), nullable=False)
    PublicationCustomVariationId = Column(Integer, ForeignKey(
        "PublicationCustomVariation.Id"), nullable=False)
    # RelationShips

    PublicationBase = relationship(
        "PublicationBase", back_populates="PublicationBase_PublicationCustomVariations")
    PublicationCustomVariation = relationship(
        "PublicationCustomVariation", back_populates="PublicationBases_PublicationCustomVariation")

    PublicationProductVariationCustom = relationship("PublicationProductVariationCustom")

    # String representations
    def __repr__(self):
        return (
            f'ProductBase_CustomVariation('
            f'{self.Id},'
            f'{self.PublicationBaseId},'
            f'{self.PublicationCustomVariationId})'
        )
    # serialize (to json)

    def serialize(self):
        return {
            'publicationBase_PublicationCustomVariationId':
                self.Id,
            'publicationBaseId': self.PublicationBaseId,
            'publicationCustomVariationId': self.PublicationCustomVariationId
        }


class PublicationProductVariationCustom(db.Base):
    # Table name in database
    __tablename__ = 'PublicationProductVariationCustom'
    # Columns
    PublicationProductId = Column(Integer, ForeignKey(
        "PublicationProduct.Id"), primary_key=True, nullable=False)
    PublicationBase_PublicationCustomVariationId = Column(Integer, ForeignKey(
        "PublicationBase_PublicationCustomVariation.Id"),
        primary_key=True, nullable=False)
    PublicationCustomVariationValueId = Column(Integer, ForeignKey(
        "PublicationCustomVariationValue.Id"), nullable=False)
    # RelationShips
    PublicationProduct = relationship("PublicationProduct", back_populates="PublicationProductVariationCustoms")
    PublicationBase_PublicationCustomVariation = relationship(
        "PublicationBase_PublicationCustomVariation", back_populates="PublicationProductVariationCustom")
    PublicationCustomVariationValues = relationship(
        "PublicationCustomVariationValue", back_populates="PublicationProductVariationCustom")

    # Initializing

    # String representations
    def __repr__(self):
        return (
            f'PublicationProductVariationCustom({self.PublicationProductId},'
            f'{self.PublicationBase_PublicationCustomVariationId},'
            f'{self.PublicationCustomVariationValueId})'
        )
    # serialize (to json)

    def serialize(self):
        return {
            'publicationProductId': self.PublicationProductId,
            'publicationBase_PublicationCustomVariationId': self.PublicationBase_PublicationCustomVariationId,
            'publicationCustomVariationValueId': self.PublicationCustomVariationValueId,
        }


class PublicationCustomVariationValue(db.Base):
    # Table name in database
    __tablename__ = 'PublicationCustomVariationValue'
    # Columns
    Id = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    PublicationCustomVariationId = Column(Integer, ForeignKey(
        "PublicationCustomVariation.Id"), nullable=False)
    PublicationCustomVariationValue = Column(String(50), nullable=False)
    # RelationShips
    PublicationProductVariationCustom = relationship("PublicationProductVariationCustom")
    PublicationCustomVariation = relationship(
        "PublicationCustomVariation", back_populates='PublicationCustomVariationValues')

    # Initializing
    def __init__(self, publicationCustomVariationValue):
        self.PublicationCustomVariationValue = publicationCustomVariationValue
    # String representations

    def __repr__(self):
        return (
            f'CustomVariationValue({self.Id},'
            f'{self.PublicationCustomVariationId},'
            f'{self.PublicationCustomVariationValue})'
        )
    # serialize (to json)

    def serialize(self):
        return {
            'id': self.Id,
            'publicationCustomVariationId': self.PublicationCustomVariationId,
            'publicationCustomVariationValue': self.PublicationCustomVariationValue
        }


class PublicationCustomVariation(db.Base):
    # Table name in database
    __tablename__ = 'PublicationCustomVariation'
    # Columns
    Id = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    PublicationCustomVariationName = Column(String(50), nullable=False)
    # RelationShips
    PublicationBases_PublicationCustomVariation = relationship(
        "PublicationBase_PublicationCustomVariation",
        uselist=True, cascade="all, delete-orphan")
    PublicationCustomVariationValues = relationship(
        "PublicationCustomVariationValue",
        uselist=True, cascade="all, delete-orphan")

    # Initializing
    def __init__(self, publicationCustomvariationName):
        self.PublicationCustomVariationName = publicationCustomvariationName

    # String representations
    def __repr__(self):
        return (
            f'CustomVariation({self.Id},'
            f'{self.PublicationCustomVariationName})'
        )

    # serialize (to json)
    def serialize(self):
        return {
            'Id': self.Id,
            'publicationCustomVariationName': self.PublicationCustomVariationName
        }


class ProductInEntryDocument(db.Base):
    # Table name in database
    __tablename__ = 'ProductInEntryDocument'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    EntryDocumentId = Column(Integer, ForeignKey("EntryDocument.Id"), nullable=False)
    InvoiceItemNumber = Column(Integer, nullable=False)
    InternalBaseSku = Column(String(150), ForeignKey("ProductBase.InternalBaseSku"), nullable=True)
    Model = Column(String(80), nullable=False)
    SupplierSku = Column(String(30), nullable=True)
    # PredialNum = Column(String(60), nullable=True)
    Upc = Column(String(20), nullable=True)
    SatKey = Column(String(15), nullable=False)
    Units = Column(Integer, nullable=False)
    DiscountRate = Column(Float, nullable=False)
    UnitPrice = Column(String(10), nullable=False)
    UnitKey = Column(String(5), nullable=False)
    ConceptAmount = Column(String(100), nullable=False)
    Description = Column(String(500), nullable=True)
    TotalEntered = Column(Integer, nullable=False, default=0)
    __table_args__ = (
        UniqueConstraint('EntryDocumentId', 'InvoiceItemNumber',
                         name='uq_EntryDocumentId_InvoiceItemNumber'),
    )

    # RelationShips
    ProductInEntryDocumentMotions = relationship("ProductInEntryDocumentMotion", cascade="all, delete-orphan")
    ProductInEntryDocumentSeries = relationship("ProductInEntryDocumentSerie", cascade="all, delete-orphan")
    ProductInEntryDocumentTaxes = relationship("ProductInEntryDocumentTax", cascade="all, delete-orphan")
    ProductBase = relationship("ProductBase", back_populates="ProductsInEntryDocument", uselist=False)
    ProductsEnteredIntoStore = relationship("ProductEnteredIntoStore")
    EntryDocument = relationship("EntryDocument", back_populates="ProductsInEntryDocument")

    def __init__(self, model=None, invoiceItemNumber=None, supplierSku=None, upc=None,
                 satKey=None, units=None, discountRate=None, unitPrice=None,
                 description=None, unitKey=None, conceptAmount=None, totalEntered=0):  # predialNum=None,
        self.Model = model
        self.InvoiceItemNumber = invoiceItemNumber
        self.SupplierSku = supplierSku
        # self.PredialNum = predialNum
        self.Upc = upc
        self.SatKey = satKey
        self.Units = units
        self.DiscountRate = discountRate
        self.UnitPrice = unitPrice
        self.Description = description
        self.UnitKey = unitKey
        self.ConceptAmount = conceptAmount
        self.TotalEntered = totalEntered

    # String representations
    def __repr__(self):
        return (
            f'ProductInEntryDocument({self.Id}, '
            f'{self.EntryDocumentId}, '
            f'{self.InvoiceItemNumber}, '
            f'{self.InternalBaseSku}, '
            f'{self.Model}, '
            f'{self.SupplierSku}, '
            # f'{self.PredialNum}, '
            f'{self.Upc}, '
            f'{self.SatKey}, '
            f'{self.Units}, '
            f'{self.DiscountRate}, '
            f'{self.UnitPrice}, '
            f'{self.UnitKey}, '
            f'{self.ConceptAmount}, '
            f'{self.Description}), '
        )

    def serialize(self, scope={}):
        productInEntryDocument = {
            'id': self.Id,
            'model': self.Model,
            'invoiceItemNumber': self.InvoiceItemNumber,
            'internalBaseSku': self.InternalBaseSku,
            'supplierSku': self.SupplierSku,
            # 'predialNum': self.PredialNum,
            'upc': self.Upc,
            'satKey': self.SatKey,
            'units': self.Units,
            'description': self.Description,
            'unitKey': self.UnitKey,
            'conceptAmount': self.ConceptAmount,
            'totalEntered': self.TotalEntered
        }
        if 'basics' in scope:
            basics = scope['basics']
            if 'discountRate' in basics:
                productInEntryDocument['discountRate'] = self.DiscountRate
            if 'unitPrice' in basics:
                productInEntryDocument['unitPrice'] = self.UnitPrice
        if 'productInEntryDocumentMotions' in scope:
            productInEntryDocument['productInEntryDocumentMotions'] = [productInEntryDocumentMotion.serialize(scope=scope['productInEntryDocumentMotions']) for productInEntryDocumentMotion in self.ProductInEntryDocumentMotions]
        if 'productInEntryDocumentSeries' in scope:
            productInEntryDocument['productInEntryDocumentSeries'] = [productInEntryDocumentSerie.serialize(scope=scope['productInEntryDocumentSeries']) for productInEntryDocumentSerie in self.ProductInEntryDocumentSeries]
        if 'productInEntryDocumentTaxes' in scope:
            productInEntryDocument['productInEntryDocumentTaxes'] = [productInEntryDocumentTax.serialize(scope=scope['productInEntryDocumentTaxes']) for productInEntryDocumentTax in self.ProductInEntryDocumentTaxes]
        if 'productBase' in scope:
            productInEntryDocument['productBase'] = self.ProductBase.serialize(scope=scope['productBase']) if self.ProductBase else None
        if 'entryDocument' in scope:
            productInEntryDocument['entryDocument'] = self.EntryDocument.serialize(scope=scope['entryDocument'])
        return productInEntryDocument


class SupportedMarketplace(db.Base):
    # Table name in database
    __tablename__ = 'SupportedMarketplace'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    Name = Column(String(50), nullable=False, unique=True)
    URLPhoto = Column(String(150), nullable=True)
    SupportedMarketplaceGroupId = Column(Integer, ForeignKey(
        "SupportedMarketplaceGroup.Id"), nullable=False)
    # RelationShips
    SupportedMarketplaceGroup = relationship(
        "SupportedMarketplaceGroup", back_populates="SupportedMarketplaces"
    )
    Marketplace = relationship(
        "Marketplace", uselist=False, cascade="all, delete-orphan")
    # Initializing

    def __init__(self, name=None, urlPhoto=None):
        self.Name = name
        self.URLPhoto = urlPhoto

    # String representations
    def __repr__(self):
        return f'SupportedMarketplace({self.Id},{self.Name},{self.URLPhoto})'
    # serialize (to json)

    def serialize(self, scope={}):
        supportedMarketplace = {
            "id": self.Id,
            "name": self.Name,
            'urlPhoto': (None if not self.URLPhoto else (self.URLPhoto
                         if ownUtils.is_valid_url(self.URLPhoto)
                         else ownUtils.imagePathToImageURL(self.URLPhoto, 'marketplaces')))
        }
        if 'marketplace' in scope:
            supportedMarketplace['marketplace'] = self.Marketplace.serialize(scope=scope['marketplace'])
        if 'supportedMarketplaceGroup' in scope:
            supportedMarketplace['supportedMarketplaceGroup'] = self.SupportedMarketplaceGroup.serialize(scope=scope['supportedMarketplaceGroup'])
        return supportedMarketplace


class SupportedMarketplaceGroup(db.Base):
    # Table name in database
    __tablename__ = 'SupportedMarketplaceGroup'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    Name = Column(String(50), nullable=False, unique=True)
    URLPhoto = Column(String(150), nullable=True)
    # RelationShips
    MarketplaceGroup = relationship(
        "MarketplaceGroup", uselist=False, cascade="all, delete-orphan")
    SupportedMarketplaces = relationship(
        "SupportedMarketplace", cascade="all, delete-orphan"
    )
    # Initializing

    def __init__(self, name=None, urlPhoto=None):
        self.Name = name
        self.URLPhoto = urlPhoto

    # String representations
    def __repr__(self):
        return f'SupportedMarketplaceGroup({self.Id},{self.Name},{self.URLPhoto})'
    # serialize (to json)

    def serialize(self, scope={}):
        supportedMarketplace = {
            "id": self.Id,
            "name": self.Name,
            'urlPhoto': (None if not self.URLPhoto else (self.URLPhoto
                         if ownUtils.is_valid_url(self.URLPhoto)
                         else ownUtils.imagePathToImageURL(self.URLPhoto, 'marketplaces')))
        }
        if 'supportedMarketplaces' in scope:
            supportedMarketplace['supportedMarketplaces'] = [supportedMarketplace.serialize(scope=scope['supportedMarketplaces']) for supportedMarketplace in self.SupportedMarketplaces]
        if 'marketplaceGroup' in scope:
            supportedMarketplace['marketplaceGroup'] = self.MarketplaceGroup.serialize(scope=scope['marketplaceGroup']) if self.MarketplaceGroup else None
        return supportedMarketplace


class Marketplace(db.Base):
    # Table name in database
    __tablename__ = 'Marketplace'
    # Columns
    Id = Column(Integer, ForeignKey(
        "SupportedMarketplace.Id"), primary_key=True, nullable=False)
    MarketplaceGroupId = Column(Integer, ForeignKey(
        "MarketplaceGroup.Id"), nullable=False)
    # RelationShips
    MarketplaceGroup = relationship("MarketplaceGroup", back_populates='Marketplaces')
    SupportedMarketplace = relationship('SupportedMarketplace', back_populates='Marketplace')
    PublicationBases = relationship('PublicationBase', cascade="all, delete-orphan")
    # Initializing

    # String representations
    def __repr__(self):
        return f'Marketplace({self.Id})'
    # serialize (to json)

    def serialize(self, scope={}):
        marketplace = {
            "id": self.Id
        }
        if 'marketplaceGrooup' in scope:
            marketplace['marketplaceGrooup'] = self.MarketplaceGroup.serialize(scope=scope['marketplaceGrooup'])
        if 'supportedMarketplace' in scope:
            marketplace['supportedMarketplace'] = self.SupportedMarketplace.serialize(scope=scope['supportedMarketplace'])
        if 'publicationBases' in scope:
            marketplace['publicationBases'] = [publicationBase.serialize(scope=scope['publicationBases']) for publicationBase in self.PublicationBases]
        return marketplace


class MarketplaceGroup(db.Base):
    # Table name in database
    __tablename__ = 'MarketplaceGroup'
    # Columns
    Id = Column(Integer, ForeignKey(
        "SupportedMarketplaceGroup.Id"), primary_key=True, nullable=False)
    LastUpdate = Column(DateTime(timezone=True), nullable=True)
    CommentUpdate = Column(String(500), nullable=True)
    # RelationShips
    SupportedMarketplaceGroup = relationship('SupportedMarketplaceGroup', back_populates='MarketplaceGroup')
    MarketplaceGroupCredentials = relationship(
        "MarketplaceGroupCredentials", uselist=False, cascade="all, delete-orphan")
    Marketplaces = relationship("Marketplace", cascade="all, delete-orphan")
    # Initializing

    # String representations
    def __repr__(self):
        return f'MarketplaceGroup({self.Id}, {self.LastUpdate}, {self.CommentUpdate})'
    # serialize (to json)

    def serialize(self, scope={}):
        marketplaceGroup = {
            'id': self.Id,
            'lastUpdate': str(self.LastUpdate) if self.LastUpdate else self.LastUpdate,
            'commentUpdate': self.CommentUpdate
        }
        if 'supportedMarketplaceGroup' in scope:
            marketplaceGroup['supportedMarketplaceGroup'] = self.SupportedMarketplaceGroup.serialize(scope=scope['supportedMarketplaceGroup'])
        if 'marketplaceGroupCredentials' in scope:
            marketplaceGroup['marketplaceGroupCredentials'] = self.MarketplaceGroupCredentials.serialize(scope=scope['marketplaceGroupCredentials']) if self.MarketplaceGroupCredentials else {}
        if 'marketplaces' in scope:
            marketplaceGroup['marketplaces'] = [marketplace.serialize(scope=scope['marketplaces']) for marketplace in self.Marketplaces]
        return marketplaceGroup


class MarketplaceGroupCredentials(db.Base):
    # Table name in database
    __tablename__ = 'MarketplaceGroupCredentials'
    # Columns
    MarketplaceGroupId = Column(Integer, ForeignKey(
        "MarketplaceGroup.Id"), primary_key=True, nullable=False)
    ClientId = Column(String(400), nullable=False)
    ClientSecret = Column(String(400), nullable=False)
    RefreshToken = Column(String(1000), nullable=True)
    AccessToken = Column(String(1500), nullable=True)

    # RelationShips
    MarketplaceGroup = relationship(
        "MarketplaceGroup", back_populates="MarketplaceGroupCredentials")
    # Initializing

    def __init__(self, clientId=None, clientSecret=None,
                 refreshToken=None, accessToken=None):
        self.ClientId = clientId
        self.ClientSecret = clientSecret
        self.RefreshToken = refreshToken
        self.AccessToken = accessToken
    # String representations

    def __repr__(self):
        return (
            f'MarketplaceGroupCredentials({self.MarketplaceGroupId},'
            f'{self.ClientId},{self.ClientSecret},'
            f'{self.RefreshToken},{self.AccessToken})'
        )
    # serialize (to json)

    def serialize(self, scope={}):
        marketplaceCredentials = {
            "clientId": self.ClientId,
            "clientSecret": self.ClientSecret,
            "refreshToken": self.RefreshToken,
            "accessToken": self.AccessToken,
        }
        return marketplaceCredentials


class DefaultMessage(db.Base):
    # Table name in database
    __tablename__ = 'DefaultMessage'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    Name = Column(String(50), nullable=False, unique=True)
    Message = Column(String(500), nullable=False)
    # Initializing

    def __repr__(self):
        return f'DefaultMessage({self.Id},{self.Name},{self.Message})'

    def serialize(self):
        return {
            'id': self.Id,
            'name': self.Name,
            'message': self.Message
        }


class DirectInventoryMovement(db.Base):
    # Table name in database
    __tablename__ = 'DirectInventoryMovement'
    # Columns
    # Primary key
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    # Foreign keys
    #   LocationLevelItem_Product_Store
    LocationLevelItem_Product_StoreId = Column(Integer, ForeignKey("LocationLevelItem_Product_Store.Id"), nullable=True)
    #    ProductEnteredIntoStore
    ProductEnteredIntoStoreId = Column(Integer, ForeignKey("ProductEnteredIntoStore.Id"), nullable=True)
    #    User
    UserId = Column(Integer, ForeignKey("User.UserId"), nullable=False)
    # Others
    Amount = Column(Integer, nullable=False)
    TimeStamp = Column(DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc))
    Entry = Column(Boolean, nullable=False)
    Reasons = Column(String(500), nullable=True)

    # RelationShips
    LocationLevelItem_Product_Store = relationship("LocationLevelItem_Product_Store", back_populates="DirectInventoryMovements")
    User = relationship("User", back_populates="DirectInventoryMovements")
    ProductEnteredIntoStore = relationship("ProductEnteredIntoStore", back_populates="DirectInventoryMovements")
    # Initializing

    # String representations
    def __repr__(self):
        return (
            f'MovementForProductEnteredIntoStore('
            f'{self.Id}, '
            f'{self.LocationLevelItem_Product_StoreId}, '
            f'{self.ProductEnteredIntoStoreId}, '
            f'{self.UserId}, '
            f'{self.Amount}, '
            f'{self.TimeStamp}'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        movementForProductEnteredIntoStore = {
            "id": self.Id,
            "amount": self.Amount,
            "timeStamp": self.TimeStamp
        }
        if 'locationLevelItem_Product_Store' in scope:
            movementForProductEnteredIntoStore['locationLevelItem_Product_Store'] = self.LocationLevelItem_Product_Store.serialize(scope=scope['locationLevelItem_Product_Store'])
        if 'user' in scope:
            movementForProductEnteredIntoStore['user'] = self.User.serialize(scope=scope['user'])
        if 'productEnteredIntoStore' in scope:
            movementForProductEnteredIntoStore['productEnteredIntoStore'] = self.ProductEnteredIntoStore.serialize(scope=scope['productEnteredIntoStore'])
        return movementForProductEnteredIntoStore


class ProductEnteredIntoStore(db.Base):
    # Table name in database
    __tablename__ = 'ProductEnteredIntoStore'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    # ProductInEntryDocument keys
    ProductInEntryDocumentId = Column(Integer, ForeignKey("ProductInEntryDocument.Id"), nullable=False)
    # LocationLevelItem_Product_Store keys
    LocationLevelItem_Product_StoreId = Column(Integer, ForeignKey("LocationLevelItem_Product_Store.Id"), nullable=False)
    #  -------------------------------------Puede ser relacionada a ubicacion interna
    # cantidad ingresada
    Amount = Column(Integer, nullable=False)

    __table_args__ = (UniqueConstraint('ProductInEntryDocumentId', 'LocationLevelItem_Product_StoreId',
                                       name='uq_ProductInEntryDocumentId_LocationLevelItem_Product_StoreId'),)

    # RelationShips
    ProductInEntryDocument = relationship("ProductInEntryDocument", back_populates="ProductsEnteredIntoStore")
    LocationLevelItem_Product_Store = relationship("LocationLevelItem_Product_Store", back_populates="ProductsEnteredIntoStore")
    DirectInventoryMovements = relationship("DirectInventoryMovement")

    # Initializing
    def __init__(self, amount):
        self.Amount = amount

    # String representations
    def __repr__(self):
        return (
            f'ProductEnteredIntoStore('
            f'{self.Id}, '
            f'{self.ProductInEntryDocumentId}, '
            f'{self.LocationLevelItem_Product_StoreId}, '
            f'{self.Amount})'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        productEnteredIntoStore = {
            "id": self.Id,
            "amount": self.Amount,
        }
        if 'productInEntryDocument' in scope:
            productEnteredIntoStore['productInEntryDocument'] = self.ProductInEntryDocument.serialize(scope=scope['productInEntryDocument'])
        if 'locationLevelItem_Product_Store' in scope:
            productEnteredIntoStore['locationLevelItem_Product_Store'] = self.LocationLevelItem_Product_Store.serialize(scope=scope['locationLevelItem_Product_Store'])
        if 'directInventoryMovements' in scope:
            productEnteredIntoStore['directInventoryMovements'] = [directInventoryMovement.serialize(scope=scope['directInventoryMovements']) for directInventoryMovement in self.DirectInventoryMovements]
        return productEnteredIntoStore


class NotificationAux(db.Base):
    # Table name in database
    __tablename__ = 'NotificationAux'
    # Columns
    Id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    # ProductInEntryDocument keys
    NotificacionText = Column(String(2000), nullable=False)

    # String representations
    def __repr__(self):
        return (
            f'NotificationAUx('
            f'{self.Id}, '
            f'{self.NotificacionText}'
        )

    # serialize (to json)
    def serialize(self, scope={}):
        productEnteredIntoStore = {
            "id": self.Id,
            "notificationText": self.NotificacionText,
        }
        return productEnteredIntoStore


class OrderMessageAtachment(db.Base):
    # Table name in database
    __tablename__ = 'OrderMessageAtachment'
    # Columns
    Id = Column(Integer, primary_key=True, autoincrement=True)
    OrderMessageId = Column(Integer, ForeignKey("OrderMessage.Id"), nullable=False)
    Type = Column(String, nullable=False)
    URLPhoto = Column(String(300), nullable=False)
    MarketplaceId = Column(String, nullable=False, unique=True)
    # RelationShips
    OrderMessage = relationship("OrderMessage", back_populates="OrderMessageAtachments")

    # String representations
    def __repr__(self):
        return (
            f'OrderMessageAtachment({self.Id},'
            f'{self.OrderMessageId},'
            f'{self.Type},'
            f'{self.URLPhoto},'
            f'{self.MarketplaceId})'
        )
    # serialize (to json)
    def serialize(self, scope={}):
        order_message_atachment = {
            "id": self.Id,
            "type": self.Type,
            "URLPhoto": ownUtils.imagePathToImageURLAttachment(self.URLPhoto),
            "marketplaceId": self.MarketplaceId
        }
        if 'orderMessage' in scope:
            order_message_atachment["orderMessage"] = self.OrderMessage.serialize(scope=scope['orderMessage'])
        return order_message_atachment

class OrderStackableCommentDeleted(db.Base):
    # Table name in database
    __tablename__ = 'OrderStackableCommentDeleted'
    # Columns
    OrderStackableCommentId = Column(Integer, ForeignKey("OrderStackableComment.Id"), primary_key=True, nullable=False)
    TimeStamp = Column(DateTime(timezone=True), nullable=False, default=datetime.now(timezone.utc))
    userId = Column(Integer, ForeignKey("User.UserId"), nullable=False)
    # RelationShips
    OrderStackableComment = relationship("OrderStackableComment", back_populates="OrderStackableCommentDeleted")
    User = relationship("User", back_populates="OrderStackableCommentsDeleted")
    # String representations
    def __repr__(self):
        return (
            f'OrderStackableCommentDeleted('
            f'{self.OrderStackableCommentId},'
            f'{self.TimeStamp}',
            f'{self.userId}')
    
    # serialize (to json)

    def serialize(self, scope={}):
        orderStackableCommentDeleted = {
            'orderStackableCommentId': self.OrderStackableCommentId,
            'timeStamp': str(self.TimeStamp)
        }
        if 'user' in scope:
            orderStackableCommentDeleted['user'] = self.User.serialize(scope=scope['user'])
        if 'orderStackableComment' in scope:
            orderStackableCommentDeleted['orderStackableComment'] = self.OrderStackableComment.serialize(scope=scope['orderStackableComment'])
        return orderStackableCommentDeleted


class OrderStackableComment(db.Base):
    # Table name in database
    __tablename__ = 'OrderStackableComment'
    # Columns
    Id = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    OrderId = Column(Integer, ForeignKey("Order.OrderId"), nullable=False)
    # RelationShips
    Order = relationship("Order", back_populates="OrderStackableComments")
    OrderStackableCommentRecords = relationship("OrderStackableCommentRecord", cascade="all, delete-orphan")
    OrderStackableCommentRelevantRecords = relationship("OrderStackableCommentRelevantRecords", uselist=False, cascade="all, delete-orphan")
    OrderStackableCommentDeleted = relationship("OrderStackableCommentDeleted", uselist=False, cascade="all, delete-orphan")
    # String representations
    def __repr__(self):
        return (
            f'OrderStackableComment({self.Id},'
            f'{self.OrderId}'
        )

    # serialize (to json)

    def serialize(self, scope={}):
        orderStackableComment = {
            'id': self.Id
        }
        if 'order' in scope:
            orderStackableComment['order'] = self.Order.serialize(scope=scope['order'])
        if 'orderStackableCommentRelevantRecords' in scope:
            orderStackableComment['orderStackableCommentRelevantRecords'] = self.OrderStackableCommentRelevantRecords.serialize(scope=scope['orderStackableCommentRelevantRecords'])
        if 'orderStackableCommentRecords' in scope:
            orderStackableComment['orderStackableCommentRecords'] = [record.serialize(scope=scope['orderStackableCommentRecords']) for record in self.OrderStackableCommentRecords]
        if 'orderStackableCommentDeleted' in scope:
            orderStackableComment['orderStackableCommentDeleted'] = self.OrderStackableCommentDeleted.serialize(scope=scope['orderStackableCommentDeleted']) if self.OrderStackableCommentDeleted else None
        return orderStackableComment
    

class OrderStackableCommentRecord(db.Base):
    # Table name in database
    __tablename__ = 'OrderStackableCommentRecord'
    # Columns
    Id = Column(
        Integer, primary_key=True, nullable=False, autoincrement=True)
    OrderStackableCommentId = Column(Integer, ForeignKey("OrderStackableComment.Id"), nullable=False)
    UserId = Column(Integer, ForeignKey("User.UserId"), nullable=False)
    Comment = Column(String(300), nullable=True)
    TimeStamp = Column(DateTime(timezone=True), nullable=True)

    # RelationShips
    OrderStackableComment = relationship("OrderStackableComment", back_populates="OrderStackableCommentRecords")
    User = relationship("User", back_populates="OrderStackableCommentRecords")
    #OrderStackableCommentRelevantRecords = relationship("OrderStackableCommentRelevantRecords", uselist=False, cascade="all, delete-orphan")
    # String representations
    def __repr__(self):
        return (
            f'OrderStackableCommentRecord({self.Id},'
            f'{self.OrderStackableCommentId},'
            f'{self.UserId},'
            f'{self.Comment},'
            f'{self.TimeStamp})'
        )

    # serialize (to json)

    def serialize(self, scope={}):
        orderStackableComment = {
            'id': self.Id,
            'comment': self.Comment,
            'timeStamp': str(self.TimeStamp)
        }
        if 'orderStackableComment' in scope:
            orderStackableComment['orderStackableComment'] = self.OrderStackableComment.serialize(scope=scope['orderStackableComment'])
        if 'user' in scope:
            orderStackableComment['user'] = self.User.serialize(scope=scope['user'])
        #if 'orderStackableCommentRelevantRecords' in scope:
        #    orderStackableComment['orderStackableCommentRelevantRecords'] = self.User.serialize(scope=scope['orderStackableCommentRelevantRecords'])
        return orderStackableComment


class OrderStackableCommentRelevantRecords(db.Base):
    # Table name in database
    __tablename__ = 'OrderStackableCommentRelevantRecords'
    # Columns
    OrderStackableCommentId = Column(Integer, ForeignKey("OrderStackableComment.Id"), primary_key=True)
    FirstCommentRecordId = Column(Integer, ForeignKey("OrderStackableCommentRecord.Id"), nullable=False, unique=True)
    LastCommentRecordId = Column(Integer, ForeignKey("OrderStackableCommentRecord.Id"), nullable=False, unique=True)

    # RelationShips
    OrderStackableComment = relationship("OrderStackableComment", back_populates="OrderStackableCommentRelevantRecords")
    #FirstCommentRecord = relationship("FirstCommentRecord", back_populates="OrderStackableCommentRecords", foreign_keys=[FirstCommentRecordId])
    #LastCommentRecord = relationship("LastCommentRecord", back_populates="OrderStackableCommentRecords", foreign_keys=[LastCommentRecordId])
    FirstCommentRecord = relationship("OrderStackableCommentRecord", foreign_keys=[FirstCommentRecordId])
    LastCommentRecord = relationship("OrderStackableCommentRecord", foreign_keys=[LastCommentRecordId])
    # String representations
    def __repr__(self):
        return (
            f'OrderStackableCommentRecord({self.OrderStackableCommentId},'
            f'{self.FirstCommentRecordId},'
            f'{self.LastCommentRecordId},'
        )

    # serialize (to json)

    def serialize(self, scope={}):
        orderStackableComment = {
            'id': self.OrderStackableCommentId
        }
        if 'orderStackableComment' in scope:
            orderStackableComment['orderStackableComment'] = self.OrderStackableComment.serialize(scope=scope['orderStackableComment'])
        if 'firstCommentRecord' in scope:
            orderStackableComment['firstCommentRecord'] = self.FirstCommentRecord.serialize(scope=scope['firstCommentRecord'])
        if 'lastCommentRecord' in scope:
            orderStackableComment['lastCommentRecord'] = self.LastCommentRecord.serialize(scope=scope['lastCommentRecord'])
        return orderStackableComment


