from flask import request, jsonify, make_response
from flask_jwt_extended import jwt_required, get_jwt
from sistemaOrdenes.app.models.Models import Store, Supplier, \
    Zone, Product_Store, LocationLevelItem, LocationLevelType, \
    LocationLevelItem_Product_Store, LocationLevel
from .storesUtils import validate_if_LocationLevelItem_can_be_deleted, validate_internal_location_name, validate_internal_location_parent
from sistemaOrdenes.configs import ROLES, ADMIN_ROLE, WAREHOUSE_ROLE
import traceback
from flask_smorest import Blueprint
from .schemas import StoreSchema
from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.app.ownUtils import validate_if_object_exists, commit_catching_unique_constraint, revisarIntPositivo
import re
stores = Blueprint("Stores", __name__, description="Operations on Stores")


@stores.route('/api/stores/loadStoresPage')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def load_store_pages(session):
    stores = session.query(Store).all()
    storesSerializados = [store.serialize(scope={'zone': {}}) for store in stores]
    zones = session.query(Zone).all()
    serialized_zones = [zone.serialize(scope={}) for zone in zones]
    return make_response(jsonify({'stores': storesSerializados,
                                  'zones': serialized_zones
                                  }), 200)


@stores.route('/api/stores/getStores')
# @stores.response(500,AllStoresSchema)
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_stores(session):
    scope = request.args.get('scope')
    if scope is None:
        scope = "basic"
    query = session.query(Store)
    todosAlmacenes = query.all()
    if scope == 'basic':
        scope_dict = {}
    elif scope == 'zone':
        scope_dict = {
            'zone': {}
        }
    elif scope == 'products':
        scope_dict = {
            'product_stores': {
                'product': {}
            }
        }
    elif scope == 'internalLocations':
        scope_dict = {
            'locationLevelItems': {
                'locationLevelType': {
                    'locationLevel': {}
                },
            }
        }
    elif scope == 'stockDistribution':
        scope_dict = {
            'locationLevelItems': {
                'locationLevelType': {
                    'locationLevel': {}
                },
                'locationLevelItem_Product_Store': {
                    'product_store': {
                        'product': {}
                    }
                }
            }
        }
    else:
        raise Exception('El scope no es valido')
    storesSerializados = [store.serialize(scope=scope_dict) for store in todosAlmacenes]
    return make_response(jsonify({'stores': storesSerializados}), 200)


@stores.route('/api/stores/store/<int:id>')
@jwt_required()
@ownUtils.my_decorator_http_manage
def get_store(id, session):
    claims = get_jwt()
    role = claims.get('role')
    role = ADMIN_ROLE
    if role not in ROLES:
        # Return 403 if user is valid user but it is not Admin
        return make_response(jsonify({'errores': "No tienes permisos para dar de alta un almacen"}), 403)
    store = session.query(Store).get(id)
    if not store:
        return make_response(jsonify({"errores": "store no encontrado"}), 404)
    scope = request.args.get('scope')
    if not scope:
        scope = "basic"
    if scope == 'basic':
        scope_dict = {}
    elif scope == "zone":
        scope_dict = {
            "zone": {}
        }
    else:
        return make_response(jsonify({"errores": "El scope no es válido"}), 409)
    store_serialized = store.serialize(scope=scope_dict)
    return make_response(jsonify({"store": store_serialized}), 200)


@stores.route('/api/stores/store/<int:id>', methods=['DELETE'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def delete_store(id, session):
    store = session.query(Store).get(id)
    if not store:
        return make_response(jsonify({"errores": "store no encontrado"}), 404)
    session.delete(store)
    ownUtils.commit_catching_dependency_rule(session)
    return make_response(jsonify({"mensaje": "eliminado exitosamente"}), 200)


def validate_entire_store_data(data):
    name = ownUtils.manage_request_field(Store.StoreName, field_name='name', container_dict=data, validation_function=ownUtils.check_len_sql_column)
    description = ownUtils.manage_request_field(Store.StoreDescription, field_name='description', container_dict=data, validation_function=ownUtils.check_len_sql_column)
    urlMaps = ownUtils.manage_request_field(Store.UrlMaps, field_name='urlMaps', container_dict=data, validation_function=ownUtils.check_len_sql_column)
    address = ownUtils.manage_request_field(Store.Address, field_name='address', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=False)
    phone = ownUtils.manage_request_field(field_name='phone', container_dict=data, validation_function=ownUtils.is_valid_phonenumber, isRequired=False)
    return name, description, urlMaps, address, phone


@stores.route('/api/stores/store', methods=['POST'])
# @stores.arguments(StoreSchema)
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def post_store(session):
    # creates a dictionary of the form data
    data = request.get_json()
    print('daaaaaaaaaaata')
    print(data)
    print('daaaaaaaaaaata')
    name, description, urlMaps, address, phone = validate_entire_store_data(data)
    store = Store(
        storeName=name,
        storeDescription=description,
        address=address,
        phone=phone,
        urlMaps=urlMaps
    )
    zone = ownUtils.validate_if_object_exists(session, 'zoneNumber', 'dict', data, Zone)
    store.Zone = zone
    session.add(store)
    print('before commi')
    ownUtils.commit_catching_unique_constraint(session)
    return make_response(jsonify({'mensaje': 'Almacen registrado exitosamente'}), 201)


@stores.route('/api/stores/store/<int:id>', methods=['PUT'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def put_store(id, session):
    store = session.get(Store, id)
    if not store:
        return make_response(jsonify({"errores": "store no encontrado"}), 404)
    # creates a dictionary of the form data
    data = request.get_json()
    name, description, urlMaps, address, phone = validate_entire_store_data(data)
    store.StoreName = name
    store.StoreDescription = description
    store.UrlMaps = urlMaps
    store.Address = address
    store.Phone = phone
    zone = ownUtils.validate_if_object_exists(session, 'zoneNumber', 'dict', data, Zone)
    store.Zone = zone
    ownUtils.commit_catching_unique_constraint(session)
    return make_response(jsonify({'mensaje': 'Almacen actualizado exitosamente'}), 200)


@stores.route('/api/stores/store/<int:id>', methods=['PATCH'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def patch_store(id, session):
    store = session.get(Store, id)
    if not store:
        return make_response(jsonify({"errores": "store no encontrado"}), 404)
    # creates a dictionary of the form data
    data = request.get_json()
    name = ownUtils.manage_request_field_patch(Store.StoreName, field_name='name', container_dict=data, validation_function=ownUtils.check_len_sql_column)
    if name:
        store.StoreName = name
    description = ownUtils.manage_request_field_patch(Store.StoreDescription, field_name='description', container_dict=data, validation_function=ownUtils.check_len_sql_column)
    if description:
        store.StoreDescription = description
    urlMaps = ownUtils.manage_request_field_patch(Store.UrlMaps, field_name='urlMaps', container_dict=data, validation_function=ownUtils.check_len_sql_column)
    if urlMaps:
        store.UrlMaps = urlMaps
    address = ownUtils.manage_request_field_patch(Store.Address, field_name='address', container_dict=data, validation_function=ownUtils.check_len_sql_column)
    if address:
        store.Address = address
    phone = ownUtils.manage_request_field_patch(field_name='phone', container_dict=data, validation_function=ownUtils.check_len_sql_column)
    if phone:
        store.Phone = phone
    if 'zoneNumber' in data:
        zone = ownUtils.validate_if_object_exists(session, 'zoneNumber', 'dict', data, Zone)
        store.Zone = zone
    ownUtils.commit_catching_unique_constraint(session)
    return make_response(jsonify({'mensaje': 'Almacen actualizado exitosamente'}), 200)


@stores.route('/api/stores/loadPageInternalLocations')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def load_page_internalLocations(session):
    query = session.query(Store)
    todosAlmacenes = query.all()
    scope_locationLevelitems = {
        'locationLevelItems': {
            'locationLevelType': {
                'locationLevel': {}
            },
            'locationLevelItem_Product_Store': {
                'extras': ['locationLevelItemId',],
                'product_store': {
                    'product': {}
                }
            }
        }
    }
    storesSerializados = [store.serialize(scope=scope_locationLevelitems) for store in todosAlmacenes]
    query = session.query(LocationLevel)
    all_levels = query.all()
    scope_locationLevel = {
        'locationLevelTypes': {}
    }
    locationLevels_serialized = [level.serialize(scope=scope_locationLevel) for level in all_levels]
    return make_response(jsonify({'almacenes': storesSerializados,
                                  'locationLevels': locationLevels_serialized
                                  }), 200)


def validate_stock_to_move_internal_locations(stock_to_move, locationLevelItem_Product_Store):
    if not revisarIntPositivo(stock_to_move):
        stock_to_move = int(stock_to_move)
        if not locationLevelItem_Product_Store:
            raise Exception('El producto no existe o no se encuentra en la ubicación interna')
        if stock_to_move > locationLevelItem_Product_Store.Stock:
            raise Exception('No existen unidades suficientes para esta operación')
    else:
        raise Exception('El stock a mover debe ser un entero positivo')
    return stock_to_move


@stores.route('/api/stores/store/changeInternalLocation', methods=['PUT'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE, WAREHOUSE_ROLE])
def change_internal_locations(session):
    data = ownUtils.get_info_from_json(request)
    origin_internal_location = validate_if_object_exists(session, 'originInternalLocation', 'dict', data, LocationLevelItem)
    origin_store = origin_internal_location.Store
    origin_locationLevelItem_Product_Store = session.query(LocationLevelItem_Product_Store).get((origin_internal_location.Id, data.get('internalSku'), origin_store.StoreId))
    stock_to_move_str = data.get('stockToMove')
    stock_to_move = validate_stock_to_move_internal_locations(stock_to_move_str, origin_locationLevelItem_Product_Store)
    origin_locationLevelItem_Product_Store.Stock -= stock_to_move
    destination_internal_location = validate_if_object_exists(session, 'destinationInternalLocation', 'dict', data, LocationLevelItem)
    destination_store = destination_internal_location.Store
    if origin_store is not destination_store:
        raise Exception('Las ubicaciones de origen y destino, deben ser del mismo almacen')
    destination_locationLevelItem_Product_Store = session.query(LocationLevelItem_Product_Store).get((destination_internal_location.Id, data.get('internalSku'), origin_store.StoreId))
    if not destination_locationLevelItem_Product_Store:
        destination_locationLevelItem_Product_Store = LocationLevelItem_Product_Store(stock_to_move)
        destination_locationLevelItem_Product_Store.Product_Store = origin_locationLevelItem_Product_Store.Product_Store
        destination_locationLevelItem_Product_Store.LocationLevelItem = destination_internal_location
        session.add(destination_locationLevelItem_Product_Store)
    else:
        destination_locationLevelItem_Product_Store.Stock += stock_to_move
    session.commit()
    info_to_return = request.args.get('infoToReturn', '')
    dict_to_return = {
        'mensaje': f'{stock_to_move_str} piezas de {data.get("internalSku")} cambiado de {origin_internal_location.Name} a {destination_internal_location.Name}'
    }
    if info_to_return.lower() == 'listinternallocations':
        scope_locationLevelitems = {
            'locationLevelItems': {
                'locationLevelItem_Product_Store': {
                    'extras': ['locationLevelItemId',],
                    'product_store': {
                        'product': {}
                    }
                },
                'locationLevelType': {
                    'locationLevel': {}
                }
            }
        }
        dict_to_return['store'] = origin_store.serialize(scope=scope_locationLevelitems)
    elif info_to_return.lower() == 'updatedstock':
        scope_for_internal_locations = {
            'extras': ['locationLevelItemId', 'internalSku', 'storeId']
        }
        dict_to_return['updatedstock'] = {
            "origin": origin_locationLevelItem_Product_Store.serialize(scope=scope_for_internal_locations),
            "destination": destination_locationLevelItem_Product_Store.serialize(scope=scope_for_internal_locations)
        }
    return make_response(jsonify(dict_to_return), 200)


@stores.route('/api/stores/store/getStoreProducts/<StoreId>', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_stock_product_inStore(StoreId, session):
    store = session.query(Store).get(StoreId)
    if not store:
        return make_response(
            jsonify({'store': "Id no encontrado"}), 404)
    dataProductsStore = []
    productos_relacionados = session.query(
        Product_Store).join(Store).filter(
        Store.StoreId == StoreId).all()
    for row in productos_relacionados:
        dataProductsStore.append(row.serialize(False))
    return make_response(jsonify(
        {'store': store.serialize(scope="basic"),
         'productsInStore': dataProductsStore}), 200)


@stores.route('/api/stores/store/internalLocation/<id>', methods=['PUT'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def update_internal_location(id, session):
    data = ownUtils.get_info_from_json(request)
    location_level_item = validate_if_object_exists(session, 'locationLevelItemId', 'native', id, LocationLevelItem)
    if ('storeId' in data) or ('locationLevelTypeId' in data) or ('locationLevelItemParentId' in data):
        validate_if_LocationLevelItem_can_be_deleted(location_level_item, "modificar")
        if 'storeId' in data:
            new_store = validate_if_object_exists(session, 'storeId', 'dict', data, Store)
            current_store = location_level_item.Store
            if new_store is not current_store:
                location_level_item.LocationLevelItem_Product_Stores = []
                current_store = new_store
        if 'locationLevelTypeId' in data:
            new_location_level_type = validate_if_object_exists(session, 'locationLevelTypeId', 'dict', data, LocationLevelType)
            current_location_level_type = location_level_item.LocationLevelType
            if new_location_level_type is not current_location_level_type:
                current_location_level_type = new_location_level_type
        if 'locationLevelItemParentId' in data:
            new_location_level_item_parent = validate_internal_location_parent(session, 'locationLevelItemParentId', location_level_item.LocationLevelType, location_level_item.Store, data)
            current_location_level_item_parent = location_level_item.LocationLevelItemParent
            if new_location_level_item_parent is not current_location_level_item_parent:
                current_location_level_item_parent = new_location_level_item_parent
    if 'name' in data:
        name = validate_internal_location_name('name', 'dict', data)
        location_level_item.Name = name
    commit_catching_unique_constraint(session)
    return make_response(jsonify({'mensaje': 'Ubicación interna modificada'}), 200)


@stores.route('/api/stores/store/internalLocation', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def create_internal_location(session):
    data = ownUtils.get_info_from_json(request)
    # validate name
    name = validate_internal_location_name('name', 'dict', data)
    # mandatory
    store = validate_if_object_exists(session, 'storeId', 'dict', data, Store)
    location_level_type = validate_if_object_exists(session, 'locationLevelTypeId', 'dict', data, LocationLevelType)
    # optional
    location_level_item_parent = validate_internal_location_parent(session, 'locationLevelItemParentId', location_level_type, store, data)
    # Creating object
    location_level_item = LocationLevelItem(name=name)
    location_level_item.LocationLevelType = location_level_type
    location_level_item.Store = store
    location_level_item.LocationLevelItemParent = location_level_item_parent
    session.add(location_level_item)
    commit_catching_unique_constraint(session)
    info_to_return = request.args.get('infoToReturn', '')
    dict_to_return = {
        'mensaje': 'Ubicación interna creada'
    }
    if info_to_return.lower() == 'listinternallocations':
        scope_locationLevelitems = {
            'locationLevelItems': {
                'locationLevelItem_Product_Store': {
                    'extras': ['locationLevelItemId',],
                    'product_store': {
                        'product': {}
                    }
                },
                'locationLevelType': {
                    'locationLevel': {}
                }
            }
        }
        dict_to_return['store'] = store.serialize(scope=scope_locationLevelitems)
    return make_response(jsonify(dict_to_return), 200)


@stores.route('/api/stores/store/internalLocation/<id>', methods=['DELETE'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def delete_internal_location(id, session):
    location_level_item = validate_if_object_exists(session, 'locationLevelItemId', 'native', id, LocationLevelItem)
    validate_if_LocationLevelItem_can_be_deleted(location_level_item, "eliminar")
    session.delete(location_level_item)
    session.commit()
    return make_response(jsonify({'mensaje': 'Ubicación interna eliminada'}), 200)
