from marshmallow import Schema, fields


class AllStoresSchema(Schema):
    almacenes = fields.Dict(keys=fields.Str(), values=fields.Int())


class StoreSchema(Schema):
    storeName = fields.Str(
        required=True, description="StoreName String", example="SterenStore")
    zoneNumber = fields.Int(
        required=False, description="Number of Zone Int", example="4")
    rfc = fields.Str(required=False, description="RFC String",
                     example="SSC840823JT3")
    address = fields.Str(
        required=False, description="Address String", example="Av. Madero")
    phone = fields.Str(
        required=False, description="Phone String", example="(55) 5555-5555")
