from flask import request, jsonify, make_response
from flask_jwt_extended import jwt_required, get_jwt
from flask_smorest import Blueprint
import datetime
# from .schemas import ZoneSchema
from sistemaOrdenes.configs import ROLES
from .metricsUtils import convertDateToString, convertDateToStringTime, convertStringToDate, get_hours_of_day, get_month_start_end_dates, getAverageTicketByDate, getInitialDateAndFinalDateBetweenDates, \
    getNumOfClientByDate, getNumOfSalesByDate, getPreviusPeriod, getReceiptsByDate, \
    getSalesByDate, getSalesMetricsByDate, getTodayDate

from sistemaOrdenes.app.models.db import ScopedSession

metrics = Blueprint("Metrics", __name__, description="Operations for Metrics")


@metrics.route('/api/metrics/all', methods=['GET'])
def all_metrics():
    session = ScopedSession()
    try:
        todayDate = getTodayDate()
        months = get_month_start_end_dates()
        lastMonth = list(months.keys())[-1]
        averageTicket = getAverageTicketByDate(months[lastMonth]['initial'], months[lastMonth]['final'], session)
        todayReceipts = getReceiptsByDate(todayDate, todayDate, session)

        metricsSalesHoy = getSalesMetricsByDate(todayDate, todayDate, session, 'day')

        createdResponse = {
            'metricas': {
                'ventas': {
                    'hoy': metricsSalesHoy,
                    'meses': getSalesMetricsByDate(months[1]['initial'], months[lastMonth]['final'], session, 'month'),  # enero
                },
                'ingresos': {
                    'ingresosHoy': todayReceipts,
                    'calculoExacto': False,
                    'meses': {
                        month: {
                            'ingresos': getReceiptsByDate(months[month]['initial'], months[month]['final'], session),
                            'numMes': month
                        } for month in months
                    },
                },
                'clientes': {
                    'ticketPromedio': averageTicket,
                    'meses': {
                        month: {
                            'numClientes': getNumOfClientByDate(months[month]['initial'], months[month]['final'], session),
                            'numMes': month
                        } for month in months
                    }
                },
            }}
    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@metrics.route('/api/metrics/interval', methods=['GET'])
def interval_metrics():
    session = ScopedSession()
    try:
        initialDate = request.args.get('fechaInicial')
        finalDate = request.args.get('fechaFinal')

        initialDate = convertStringToDate(initialDate)
        finalDate = convertStringToDate(finalDate)

        previous_start_date, previous_end_date = getPreviusPeriod(initialDate, finalDate)

        date_ranges = getInitialDateAndFinalDateBetweenDates(initialDate, finalDate)
        previous_date_ranges = getInitialDateAndFinalDateBetweenDates(convertStringToDate(previous_start_date), convertStringToDate(previous_end_date))
        metrics = {}
        previous_metrics = {}

        for initialDate, finalDate in date_ranges:
            day_key = convertDateToString(initialDate.date())  # Utiliza la fecha como clave
            metrics[day_key] = {
                'ventas': {
                    'numVentas': getNumOfSalesByDate(initialDate, finalDate, session),
                    'ventas': getSalesByDate(initialDate, finalDate, session),
                },
                'ingresos': {
                    'ingresos': getReceiptsByDate(initialDate, finalDate, session),
                    'calculoExacto': False,
                },
                'clientes': {
                    'ticketPromedio': getAverageTicketByDate(initialDate, finalDate, session),
                    'numClientes': getNumOfClientByDate(initialDate, finalDate, session),
                }
            }

        for initialDate, finalDate in previous_date_ranges:
            day_key = convertDateToString(initialDate.date())
            previous_metrics[day_key] = {
                'ventas': {
                    'numVentas': getNumOfSalesByDate(initialDate, finalDate, session),
                    'ventas': getSalesByDate(initialDate, finalDate, session),
                },
                'ingresos': {
                    'ingresos': getReceiptsByDate(initialDate, finalDate, session),
                    'calculoExacto': False,
                },
                'clientes': {
                    'ticketPromedio': getAverageTicketByDate(initialDate, finalDate, session),
                    'numClientes': getNumOfClientByDate(initialDate, finalDate, session),
                }
            }

        createdResponse = {'metricasIntervalo': metrics, 'metricasIntervaloAnterior': previous_metrics}

    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@metrics.route('/api/metrics/daybyhours', methods=['GET'])
def day_by_hours_metrics():
    session = ScopedSession()
    try:
        initialDate = request.args.get('fecha')
        initialDate = convertStringToDate(initialDate)
        previus_date = initialDate - datetime.timedelta(days=1)

        hours = get_hours_of_day(initialDate)
        previus_hours = get_hours_of_day(previus_date)
        metrics = {}
        previus_metrics = {}

        for i in range(len(hours) - 1):
            initialDate = hours[i]
            finalDate = hours[i + 1]
            day_key = convertDateToStringTime(initialDate)
            metrics[day_key] = {
                'ventas': {
                    'numVentas': getNumOfSalesByDate(initialDate, finalDate, session),
                    'ventas': getSalesByDate(initialDate, finalDate, session),
                },
                'ingresos': {
                    'ingresos': getReceiptsByDate(initialDate, finalDate, session),
                    'calculoExacto': False,
                },
                'clientes': {
                    'ticketPromedio': getAverageTicketByDate(initialDate, finalDate, session),
                    'numClientes': getNumOfClientByDate(initialDate, finalDate, session),
                }
            }

        for i in range(len(previus_hours) - 1):
            initialDate = previus_hours[i]
            finalDate = previus_hours[i + 1]
            day_key = convertDateToStringTime(initialDate)
            previus_metrics[day_key] = {
                'ventas': {
                    'numVentas': getNumOfSalesByDate(initialDate, finalDate, session),
                    'ventas': getSalesByDate(initialDate, finalDate, session),
                },
                'ingresos': {
                    'ingresos': getReceiptsByDate(initialDate, finalDate, session),
                    'calculoExacto': False,
                },
                'clientes': {
                    'ticketPromedio': getAverageTicketByDate(initialDate, finalDate, session),
                    'numClientes': getNumOfClientByDate(initialDate, finalDate, session),
                }
            }

        createdResponse = {'metricasPorHora': metrics,
                           'metricasPorHoraAnterior': previus_metrics}

    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse
