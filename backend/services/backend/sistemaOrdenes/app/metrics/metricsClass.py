class MetricUtility():
    def __init__(self, firstUtility, secondUtility, porciento):
        self.FirstUtility = firstUtility
        self.SecondUtility = secondUtility
        self.Porciento = porciento

    def serialize(self):
        return {
            'FirstUtility': self.FirstUtility,
            'SecondUtility': self.SecondUtility,
            'Porciento': self.Porciento
        }


class MetricClients():
    def __init__(self, clientId, clientName, montoGastado,
                 ultimaCompra, comprasRealizadas, RFM, calificacion):
        self.ClientId = clientId
        self.ClientName = clientName
        self.MontoGastado = montoGastado
        self.UltimaCompra = ultimaCompra
        self.ComprasRealizadas = comprasRealizadas
        self.RFM = RFM
        self.Calificacion = calificacion

    def serialize(self):
        return {
            'clientId': self.ClientId,
            'clientName': self.ClientName,
            'montoGastado': self.<PERSON><PERSON><PERSON><PERSON><PERSON>,
            'ultimaCompra': self.UltimaCompra,
            'comprasRealizadas': self.ComprasRealizadas,
            'RFM': self.RFM,
            'calificacion': self.Calificacion
        }
