import os
import platform
from datetime import timedelta

# ---Variables configuración

if platform.system() == 'Windows':
    path_raiz = 'C:'
    #database_url = 'postgresql://postgres:postgres@localhost:5432/turtlescgp2'
    #database_url = '*************************************************/turtlescgp2'
    # database_url = '*************************************************/turtlescgp3'
    #database_url = '*********************************************************/turtlescgp2'  # preprod
    database_url = '*********************************************************/turtlescgp3'  # preprod
    domain = None
    secret_key_creative = 'secret creative'
    samesite = "None"
    frontend_url = 'http://localhost:5173'
    backend_url = 'https://localhost:5000'
elif platform.system() == 'Linux':
    path_raiz = os.getenv('APP_FOLDER')
    protocol = os.getenv('PROTOCOL')
    frontend_subdomain = os.getenv('FRONTEND_SUBDOMAIN')
    domain = os.getenv('DOMAIN')
    database_url = os.getenv('DATABASE_URL')
    secret_key_creative = os.getenv('SECRET_KEY')
    samesite = "Lax"
    backend_url = f'{protocol}{frontend_subdomain}-api.{domain}'
    frontend_url = f'{protocol}{frontend_subdomain}.{domain}'
elif platform.system() == 'Darwin':
    path_raiz = '/Users'
    # database_url = '*************************************************/turtlescgp2'
    database_url = '*********************************************************/turtlescgp2'  # preprod
    domain = None
    secret_key_creative = 'secret creative'
    samesite = "None"
    frontend_url = 'http://localhost:5173'
    backend_url = 'https://localhost:5000'
else:
    raise Exception('Sistema operativo desconocido')


class Config(object):
    STATIC_FOLDER = f"{path_raiz}/sistemaOrdenes/static"
    MEDIA_FOLDER = f"{path_raiz}/sistemaOrdenes/media"
    UPLOAD_FOLDER = f"{path_raiz}/sistemaOrdenes/xml_invoices"
    GUIDES_FOLDER = f"{path_raiz}/sistemaOrdenes/guides"
    QUOTES_FOLDER = f"{path_raiz}/sistemaOrdenes/pdf_quotes"
    INVOICES_FOLDER = f"{UPLOAD_FOLDER}/suppliers"
    PRODUCT_IMAGES_FOLDER = f"{MEDIA_FOLDER}/productImages"
    USER_IMAGE_FOLDER = f"{MEDIA_FOLDER}/userImages"
    COMPANY_IMAGE_FOLDER = f"{MEDIA_FOLDER}/companyImages"
    MARKETPLACE_IMAGE_FOLDER = f"{MEDIA_FOLDER}/marketplaceImages"
    GENERAL_IMAGE_FOLDER = f"{MEDIA_FOLDER}/generalImages"
    DIRECTSALE_IMAGE_FOLDER = f"{MEDIA_FOLDER}/directSaleImages"
    MESSAGE_ATTACHMENTS_FOLDER =  F"{MEDIA_FOLDER}/messageAttachments" 

    API_TITLE = "Tortuga"
    API_VERSION = "v2"
    OPENAPI_VERSION = "3.0.3"
    OPENAPI_URL_PREFIX = "/"
    OPENAPI_SWAGGER_UI_PATH = "/swagger-ui"
    OPENAPI_SWAGGER_UI_URL = "https://cdn.jsdelivr.net/npm/swagger-ui-dist/"
    JWT_COOKIE_SECURE = True
    JWT_COOKIE_DOMAIN = domain
    JWT_COOKIE_SAMESITE = samesite
    JWT_TOKEN_LOCATION = "cookies"
    JWT_ACCESS_COOKIE_PATH = '/'
    JWT_SECRET_KEY = secret_key_creative
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=32)
    FRONTEND_URL = frontend_url
    BACKEND_URL = backend_url
    DATABASE_URL = database_url
    ACCOUNT_SYSTEM_MAIL = "<EMAIL>"  # "<EMAIL>"
    PASS_SYSTEM_MAIL = "KDYjmn^Q"  # "8b4K3Smv"