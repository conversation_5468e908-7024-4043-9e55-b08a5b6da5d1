from flask_smorest import Blueprint
from flask_jwt_extended import jwt_required, get_jwt
from flask import jsonify, request, make_response
from .clientsUtils import armar_query_clients, armar_query_buscador_clientes
from sistemaOrdenes.app.models.Models import Client, Order
import traceback
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.app.clients.clientsUtils import is_valid_Client, redefine_Client

clients = Blueprint("Clients", __name__, description="Operations on Clients")


@clients.route('/api/clients/client/<clientId>')
# @jwt_required()
def get_info_client(clientId):
    try:
        session = ScopedSession()

        # claims = get_jwt()
        # role = claims.get('role')
        # if role is not ADMIN_ROLE:
        #     return make_response(jsonify({
        #         'error':
        #         "No tienes permiso para ver la informacion"}), 202)

        client = session.query(Client).get(clientId)
        if client:
            createdResponse = make_response(
                jsonify({'client': client.serialize()}), 200)
        else:
            createdResponse = make_response(
                jsonify({'client': "cliente no existe"}), 200)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@clients.route('/api/clients/clientsFiltro')
# @jwt_required()
def return_clients():
    try:
        session = ScopedSession()
        # Buscador
        search = request.args.get('search')
        # Buscador"""
        offset = request.args.get('offset')
        nc = request.args.get('next')
        query = armar_query_clients(session)
        query = armar_query_buscador_clientes(query, search)
        if ((offset is not None and offset != "") and (nc is not None and nc != "")):
            query = query.order_by(Client.ClientId).offset(offset).limit(nc)
        else:
            query = query.order_by(Client.ClientId).offset(0).limit(30)
        clientes_filtrados = query.all()
        clientes_filtrados_serializados = [
            cliente_filtrados.serialize()
            for cliente_filtrados in clientes_filtrados]
        createdResponse = {'clients': clientes_filtrados_serializados}
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errors': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@clients.route('/api/clients/numClientsFiltro')
# @jwt_required()
def return_num_clients():
    try:
        session = ScopedSession()
        search = request.args.get('search')
        query = armar_query_clients(session)
        query = armar_query_buscador_clientes(query, search)
        num_clients = query.count()
        createdResponse = {'numClients': num_clients}
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errors': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@clients.route('/api/clients/new', methods=['POST'])
# @jwt_required()
def new_client():
    try:
        # claims = get_jwt()
        # role = claims.get('role')
        # if role is not ADMIN_ROLE:
        #     return make_response(jsonify({
        #         'error':
        #         "No tienes permiso para ver la informacion"}), 202)
        session = ScopedSession()
        data = request.json
        client = is_valid_Client(data)
        session.add(client)
        session.commit()
        createdResponse = make_response(
            jsonify({
                'client': client.serialize(),
                'message': 'Client created successfully'}), 201)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@clients.route('/api/clients/update/<clientId>', methods=['PUT'])
# @jwt_required()
def update_client(clientId):
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        # if role is not ADMIN_ROLE:
        #     return make_response(jsonify({
        #         'error':
        #         "No tienes permiso para ver la informacion"}), 202)
        client = session.query(Client).get(clientId)
        if client:
            data = request.json
            clientData = is_valid_Client(data)
            redefine_Client(clientData, client)
            session.commit()
            createdResponse = make_response(
                jsonify({
                    'client': client.serialize(),
                    'message': 'Client updated successfully'}), 200)
        else:
            createdResponse = make_response(
                jsonify({'client': "client doesnt exist"}), 404)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@clients.route('/api/clients/delete/<clientId>', methods=['DELETE'])
# @jwt_required()
def delete_client(clientId):
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        # if role is not ADMIN_ROLE:
        #     return make_response(jsonify({
        #         'error':
        #         "No tienes permiso para ver la informacion"}), 202)
        client = session.query(Client).get(clientId)
        if client:
            orderByClient = session.query(Order).filter(Order.ClientId == clientId).count()
            if orderByClient > 0:
                createdResponse = make_response(
                    jsonify({'client': "client has orders"}), 422)
            else:
                session.delete(client)
                session.commit()
                createdResponse = make_response(
                    jsonify({
                        'message': 'Client deleted successfully'
                    }), 200)
        else:
            createdResponse = make_response(
                jsonify({'client': "client doesnt exist"}), 404)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse
