from sistemaOrdenes.app.models.Models import Client
from sqlalchemy import String, cast
from sqlalchemy import or_
from sistemaOrdenes.app.ownUtils import is_valid_email


def if_exist_Return(name, dict):
    if name in dict:
        return dict[name]
    return ''


def is_valid_Int(value, name):
    try:
        if not isinstance(value, int):
            value = int(value)
        return value
    except Exception:
        raise Exception(f"{name} no es de tipo numerico")


def armar_query_clients(session):
    query = session.query(Client)
    return query


def armar_query_buscador_clientes(query, search):

    umbral = 0.9
    if search is not None and search != '':
        search = "%{}%".format(search)
        query = query.filter(or_((cast(Client.ClientId, String).op('<->')(search) < umbral),
                                 (Client.MarketplaceClientId.op('<->')(search) < umbral),
                                 (Client.City.op('<->')(search) < umbral),
                                 (Client.State.op('<->')(search) < umbral),
                                 (Client.Nickname.op('<->')(search) < umbral),
                                 (Client.Name.op('<->')(search) < umbral),
                                 (Client.ZipCode.op('<->')(search) < umbral),
                                 (Client.PhoneNumber.op('<->')(search) < umbral),
                                 (Client.Email.op('<->')(search) < umbral),
                                 (Client.Address.op('<->')(search) < umbral)
                                 ))
    return query


def is_valid_Client(dataClient):
    try:
        MarketplaceClientId = if_exist_Return(
            "marketplaceClientId", dataClient)
        if MarketplaceClientId != '':
            MarketplaceClientId = is_valid_Int(
                MarketplaceClientId, 'MarketplaceClientId')

        RegistrationDate = if_exist_Return("registrationDate", dataClient)

        City = if_exist_Return("city", dataClient)

        State = if_exist_Return("state", dataClient)

        Nickname = if_exist_Return("nickname", dataClient)

        Name = if_exist_Return("name", dataClient)

        Score = if_exist_Return("score", dataClient)

        if Score != '':
            Score = is_valid_Int(Score, 'score')

        ZipCode = if_exist_Return("zipCode", dataClient)

        PhoneNumber = if_exist_Return("phoneNumber", dataClient)

        Email = if_exist_Return("email", dataClient)
        if Email != '':
            if not is_valid_email(Email):
                raise Exception("Email del Cliente no valido")

        Address = if_exist_Return("address", dataClient)

        miniClient = Client(str(MarketplaceClientId),
                            RegistrationDate, City,
                            State, Nickname, Name, Score,
                            ZipCode, PhoneNumber, Email, Address, [])
        return miniClient
    except Exception as e:
        raise Exception(str(e))


def redefine_Client(dataClient, oldClient):
    oldClient.MarketplaceClientId = dataClient.MarketplaceClientId
    oldClient.RegistrationDate = dataClient.RegistrationDate
    oldClient.City = dataClient.City
    oldClient.State = dataClient.State
    oldClient.Nickname = dataClient.Nickname
    oldClient.Name = dataClient.Name
    oldClient.Score = dataClient.Score
    oldClient.ZipCode = dataClient.ZipCode
    oldClient.PhoneNumber = dataClient.PhoneNumber
    oldClient.Email = dataClient.Email
    oldClient.Address = dataClient.Address
