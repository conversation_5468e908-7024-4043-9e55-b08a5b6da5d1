# Built in modules
import os
from datetime import datetime
from datetime import timedelta
from datetime import timezone
# Third-party libraries
# Flask
from flask import Flask, make_response, jsonify
# Flask-complements
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from flask_smorest import Api
from flask_jwt_extended import create_access_token
from flask_jwt_extended import get_jwt_identity
from flask_jwt_extended import set_access_cookies
from flask_jwt_extended import get_jwt
# Others
from .authRefresh.routes import authRefresh as AuthRefreshBlueprint
from .default.routes import default as DefaultBlueprint
from .stores.routes import stores as StorageBlueprint
from .products.routes import products as ProductBlueprint
from .err_handler.routes import err_handler as ErrorHandlerBlueprint
from .invoices.routes import invoices as InvoicesBlueprint
from .orders.routes import orders as OrdersBlueprint
from .suppliers.routes import suppliers as SuppliersBlueprint
from .test_endpoints.routes import test_endpoints as TestEndPointsBlueprint
from .zones.routes import zones as ZonesBlueprint
from .metrics.routes import metrics as MetricsBlueprint
from .clients.routes import clients as ClientsBlueprint
from .direct_sales.routes import direct_sales as Direct_Sales_Blueprint
from .users.routes import users as Users_Blueprint
from .company.routes import company as Company_Blueprint
from .supplierStores.routes import supplierStores as Supplier_Stores_Blueprint
from .marketplaces.routes import marketplaces as Marketplaces_Blueprint
from .meli.routes import meli as Meli_Blueprint
from .publications.routes import postProducts as Post_products_Blueprint
from .guias.routes import guides as Guides_Blueprint
from .sales.routes import sales as Sales_Blueprint
from .kits.routes import kits as Kits_Blueprint
from .orderMessages.routes import order_messages as Order_messages_Blueprint
from .defaultMessages.routes import defaultMessages as DefaultMessagesBlueprint
from sistemaOrdenes.app import config

def create_app():
    # app = Flask(__name__, static_url_path='', static_folder='app', template_folder="app")
    app = Flask(__name__)
    app_settings = os.getenv("APP_SETTINGS", 'sistemaOrdenes.app.config.Config')
    app.config.from_object(app_settings)
    # CORS(app, supports_credentials=True, expose_headers=["Content-Disposition"])
    # CORS(app, supports_credentials=True, methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
    # CORS(app, supports_credentials=True, origins=['https://tortugacp.creativeplanet.com.mx'])
    # CORS(app, supports_credentials=True, methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
    CORS(app, supports_credentials=True)
    app.secret_key = config.Config.JWT_SECRET_KEY
    jwt = JWTManager(app)
    print(str(jwt))
    api = Api(app) #revisar
    

    @app.after_request
    def refresh_expiring_jwts(response):
        try:
            exp_timestamp = get_jwt()["exp"]
            now = datetime.now(timezone.utc)
            target_timestamp = datetime.timestamp(now + timedelta(minutes=180))
            if target_timestamp > exp_timestamp:
                access_token = create_access_token(identity=get_jwt_identity())
                set_access_cookies(response, access_token)
            return response
        except (RuntimeError, KeyError):
            # Is not a valid JWT, Just return the original response
            return response
    # register blueprints
    # List of blueprints
    blueprints = [
        AuthRefreshBlueprint,
        DefaultBlueprint,
        ErrorHandlerBlueprint,
        InvoicesBlueprint,
        OrdersBlueprint,
        ProductBlueprint,
        StorageBlueprint,
        SuppliersBlueprint,
        TestEndPointsBlueprint,
        ZonesBlueprint,
        MetricsBlueprint,
        ClientsBlueprint,
        Direct_Sales_Blueprint,
        Users_Blueprint,
        Supplier_Stores_Blueprint,
        Company_Blueprint,
        Marketplaces_Blueprint,
        Meli_Blueprint,
        Post_products_Blueprint,
        Guides_Blueprint,
        Sales_Blueprint,
        Kits_Blueprint,
        Order_messages_Blueprint,
        DefaultMessagesBlueprint
    ]
    for blueprint in blueprints:
        api.register_blueprint(blueprint)

    # shell context for flask cli
    #app.shell_context_processor({"app": app})

    return app
