from sistemaOrdenes.configs import ADMIN_ROLE, \
    ADMNISTRATIVE_ACCOUNTANT_ROLE, SERVICES_ROLE, ROLES
from flask import jsonify, request, Response, make_response
from flask_jwt_extended import jwt_required, get_jwt
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.app.models.Models import Kit, Kit_Product, Product
from .kitsUtils import fuzzyKitsSearch
from flask_smorest import Blueprint
import traceback
from datetime import datetime, timedelta
from sistemaOrdenes.app import ownUtils

kits = Blueprint("Kits", __name__, description="product kits")


@kits.route('/api/kits/prueba')
def return_pedidos_por_busqueda():  # SearchSchema
    return {"kits": "prueba"}


def set_info_kit(session=None, kit=None, data=None):
    validated, info = validate_kit_request_data(data)
    if not validated:
        raise Exception(info)
    title_request, description_request, kit_products_request = info
    # validar
    kit_products = []
    for kit_product_request in kit_products_request:
        amount = kit_product_request.get('amount')
        error = ownUtils.revisarIntPositivo(amount)
        if error:
            raise Exception(f'La cantidad del producto debe ser entero: {error}')
        internalSku = kit_product_request.get('internalSKu')
        product = session.query(Product).get(internalSku)
        if not product:
            raise Exception(f'El sku interno "{internalSku}" no corresponde a ningún producto')
        kit_products.append(
            {
                "product": product,
                "amount": amount
            })
    kit.Kit_Products = []
    for kit_product in kit_products:
        kit_product_object = Kit_Product(amount=kit_product['amount'])
        kit_product_object.Product = kit_product['product']
        kit_product_object.kit = kit
        kit.Kit_Products.append(kit_product_object)
    kit.Title = title_request
    kit.Description = description_request


def validate_kit_request_data(data):
    errors = ''
    title_request = data.get("title")
    title_len = Kit.Title.property.columns[0].type.length
    description_request = data.get("description")
    description_len = Kit.Description.property.columns[0].type.length
    if not title_request:
        errors = ownUtils.concatenarMensajeRetroalimentacion(errors, "el título es obligatorio")
    elif len(title_request) > title_len:
        errors = ownUtils.concatenarMensajeRetroalimentacion(errors, f"el título debe ser menor a {title_len}")
    if not description_request:
        errors = ownUtils.concatenarMensajeRetroalimentacion(errors, "la descripción es obligatoria")
    elif len(description_request) > description_len:
        errors = ownUtils.concatenarMensajeRetroalimentacion(errors, f"La descripción debe ser menor a {description_len}")
    kit_products_request = data.get("kit_products")
    if type(kit_products_request) != list:
        errors = ownUtils.concatenarMensajeRetroalimentacion(errors, "Los productos del kit deben ser una list")
    if errors:
        return False, errors
    else:
        return True, (title_request, description_request, kit_products_request)


@kits.route('/api/kits/kit', methods=['POST'])  # create
# @orders.arguments(SearchSchema, location="query")
# @jwt_required()
def create_kit():  # SearchSchema
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role == ADMIN_ROLE or role == ADMNISTRATIVE_ACCOUNTANT_ROLE:
            data = request.get_json()
            if False:
                raise Exception("El kit que se intenta dar de alta ya existe")
            else:
                kit = Kit()
                set_info_kit(session=session, kit=kit, data=data)
                session.add(kit)
                session.commit()
                created_response = make_response(jsonify(
                    {'mensaje':
                     'kit creado exitosamente'}), 200)
        else:
            created_response = make_response(
                'No tienes permisos para crear kits', 403)
    except Exception as e:
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@kits.route('/api/kits/kit', methods=['PUT'])  # update
# @jwt_required()
def update_kit():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role == ADMIN_ROLE or role == ADMNISTRATIVE_ACCOUNTANT_ROLE:
            data = request.get_json()
            kit_id = data.get('kit_id')
            kit = session.query(Kit).get(kit_id)
            if not kit:
                created_response = make_response(
                    jsonify({'errores': 'Producto no existe'}), 202)
            else:
                set_info_kit(session=session, kit=kit, data=data)
                session.commit()
                created_response = make_response(jsonify(
                    {'mensaje':
                     'kit actualizado exitosamente'}), 200)
        else:
            created_response = make_response(
                'No tienes permisos para crear kits', 403)
    except Exception as e:
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@kits.route('/api/kits/kit', methods=['DELETE'])  # delete *
# @jwt_required()
def delete_kit():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role == ADMIN_ROLE or role == ADMNISTRATIVE_ACCOUNTANT_ROLE:
            data = request.get_json()
            kit_id = data.get('kit_id')
            print(kit_id)
            kit = session.query(Kit).get(kit_id)
            if not kit:
                created_response = make_response(jsonify(
                    {'errores':
                     'kit para eliminar no encontrado'}), 404)
            else:
                session.delete(kit)
                session.commit()
                created_response = make_response(jsonify(
                    {'mensaje':
                     'kit eliminado exitosamente'}), 200)
            # Return 403 if role user is not valid
        else:
            created_response = make_response(
                'No tienes permisos para eliminar kits', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@kits.route('/api/kits/filtered_kits')  # read
# @jwt_required()
def get_kits():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            offset = request.args.get('offset')
            nc = request.args.get('next')
            search = request.args.get('search')
            query = fuzzyKitsSearch(session, search)
            if ((offset is not None and offset != "") and (nc is not None and nc != "")):
                query = query.order_by(
                    Kit.Id).offset(offset).limit(nc)
            else:
                query = query.order_by(
                    Kit.Id).offset(0).limit(30)
            filtered_kits = query.all()
            serialized_kits = [filtered_kit.serialize_table_info_products() for filtered_kit in filtered_kits]
            created_response = make_response(jsonify({
                "kits": serialized_kits
            }), 200)
        else:
            created_response = make_response(
                'No tienes permisos para ver kits', 403
            )
    except Exception as e:
        print(e)
        created_response = make_response(
            jsonify({"errores": str(e)}, 500)
        )
    finally:
        session.close()
        return created_response


@kits.route('/api/kits/kit/<id>')  # read one
def get_kit_by_id(id):
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            error = ownUtils.revisarIntPositivo(id)
            if error:
                raise Exception(f'id de kit debe ser tipo int: {error}')
            kit = session.query(Kit).get(id)
            if not kit:
                created_response = make_response(
                    jsonify({'errores': 'Kit no encontrado'}), 401)
            else:
                serialized_kit = kit.serialize_table_info_products()
                created_response = make_response(jsonify(serialized_kit), 200)
        else:
            created_response = make_response(
                'No tienes permisos para ver kits', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response
