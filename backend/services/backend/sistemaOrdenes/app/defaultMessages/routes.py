from flask import request, jsonify, make_response
from flask_smorest import Blueprint
from flask_jwt_extended import jwt_required

from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.app.models.Models import DefaultMessage
from sistemaOrdenes.configs import ROLES
from sistemaOrdenes.app.defaultMessages.defaultMessagesUtils import get_all_default_messages
from sqlalchemy import desc, func, cast, String, select

defaultMessages = Blueprint('defaultMessages', __name__, description='Default messages for orders')

def messages_all(session, search, offset, nc):
    if search is not None and search != '':
        query = search_query(session, search)
        query = query.order_by(desc("greatest"))
        query = order_by_query(query, offset, nc)
        default_messages_greatest = query.all()
        default_messages = map(lambda default_messages_greatest: default_messages_greatest.DefaultMessage, default_messages_greatest)
    else:
        query = no_search_query(session)
        query = order_by_query(query, offset, nc)
        default_messages = query.all()
    return default_messages

def order_by_query(query, offset, nc):
    if ((offset is not None and offset != "") and (nc is not None and nc != "")):
        query = query.offset(offset).limit(nc)
    else:
        query = query.offset(0).limit(30)
    return query

def search_query(session, search, columns_to_get=(DefaultMessage,)):
    query = session.query(*columns_to_get, func.greatest(func.similarity(DefaultMessage.Name, search),
                                                         func.similarity(DefaultMessage.Message, search)).label('greatest'))
    return query


def no_search_query(session, columns_to_get=(DefaultMessage,)):
    query = session.query(*columns_to_get)
    return query


@defaultMessages.route('/api/messages/defaultMessage', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_default_messages(session):
    search = request.args.get('search')
    offset = request.args.get('offset')
    nc = request.args.get('nc')
    default_messages = messages_all(session, search, offset, nc)
    default_messages = list(map(lambda default_message: default_message.serialize(), default_messages))
    return make_response(jsonify({'messages': default_messages}), 200)


@defaultMessages.route('/api/messages/defaultMessage/<int:id>', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_default_message_by_id(id, session):
    message = ownUtils.validate_if_object_exists(session, "id", "native", id, DefaultMessage)
    message = message.serialize()
    return make_response(jsonify({'message': message}), 201)

@defaultMessages.route('/api/messages/defaultMessage/<int:id>', methods=['DELETE'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def delete_default_message(id, session):
    message = ownUtils.validate_if_object_exists(session, "id", "native", id, DefaultMessage)
    session.delete(message)
    session.commit()
    message = message.serialize()
    return make_response(jsonify({'message': message}), 200)


@defaultMessages.route('/api/messages/defaultMessage', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
#@ownUtils.my_decorator_http_returnSession()
def create_default_message(session):
    data = ownUtils.get_info_from_json(request)
    if not data or not data.get('name') or not data.get('message'):
        return make_response(jsonify({'error': 'Name and message are required'}), 400)
    message = DefaultMessage(
        Name=data.get('name'),
        Message=data.get('message')
    )
    session.add(message)
    session.commit()
    message = message.serialize()
    return make_response(jsonify({'message': message}), 200)


@defaultMessages.route('/api/messages/defaultMessage/<int:id>', methods=['PUT'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def update_default_message(id, session):
    data = ownUtils.get_info_from_json(request)
    message = ownUtils.validate_if_object_exists(session, "id", "native", id, DefaultMessage)
    message.Name = data.get('name', message.Name)
    message.Message = data.get('message', message.Message)
    session.commit()
    message = message.serialize()
    return make_response(jsonify({'message': message}), 200)



