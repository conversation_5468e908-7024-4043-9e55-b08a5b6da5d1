import uuid
from datetime import datetime
from flask_smorest import Blueprint
from flask import jsonify, request, make_response, abort
from flask_jwt_extended import get_jwt_identity, \
    jwt_required, get_jwt
from .users_utils import armar_query_users, armar_query_browser_users, \
    addNewPhotoToUser, eliminate_Photo_existed, return_img_user, \
    send_email_new_account
from sistemaOrdenes.app.ownUtils import validate_hashed
from sistemaOrdenes.app.models.Models import User, Role
import traceback
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.app import config
from sistemaOrdenes.configs import ADMIN_ROLE, HUMAN_ROLES

users = Blueprint("users", __name__, description="Operations on User")
BACKEND_URL = config.Config.BACKEND_URL



@users.route('/user', methods=['GET'])
# @authRefresh.response(200, UserSchema)
@jwt_required()
@ownUtils.my_decorator_http_manage
def get_current_user(session):
    # Get current user identity
    userIdentity = get_jwt_identity()
    # Get user data from database using id
    user = ownUtils.validate_if_object_exists(session, "id", "native", userIdentity, User)
    # Return name and role user
    return make_response(
        jsonify({
            'user': user.Name,
            'role': user.Role.RoleName,
            'userInfo': user.serialize(scope={
                'basics': ['alias', 'phoneNumber', 'URLPhoto'],
                'role': {}})}), 200)


@users.route('/api/users/loadPageUsers', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def get_all_user(session):
    scope = request.args.get('scope')
    if not scope:
        scope = "basic"
    users = session.query(User).\
        filter(User.RoleId != 5)
    roles = session.query(Role).\
        filter(Role.RoleId != 5)
    # Return name and role user
    if scope == 'basic':
        scope_dict = {
            'basics': ['alias', 'phoneNumber']
        }
    elif scope == 'role':
        scope_dict = {
            'basics': ['alias', 'phoneNumber'],
            'role': {}
        }
    else:
        abort(make_response(jsonify({'error': "scope no válido"}), 400))
    return make_response(
        jsonify({'usersInfo': [
            miniUser.serialize(scope=scope_dict)
            for miniUser in users],
            'roles': [
            miniRole.serialize(scope={})
            for miniRole in roles]}), 200)

@users.route('/api/users/user/<userId>')
# @jwt_required()
def get_info_user(userId):
    try:
        session = ScopedSession()
        user = session.query(User).get(userId)
        print(user)
        if user:
            createdResponse = make_response(
                jsonify({'usuario': user.serialize(scope={})}), 200)
        else:
            createdResponse = make_response(
                jsonify({'usuario': "usuario no existe"}), 200)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@users.route('/api/users/usersFiltro')
# @products.arguments(SearchSchema,location="query")
# @jwt_required()
def return_users():
    try:
        session = ScopedSession()
        # Buscador
        search = request.args.get('search')
        # Buscador"""
        offset = request.args.get('offset')
        nc = request.args.get('next')
        query = armar_query_users(session)
        query = armar_query_browser_users(query, search)
        if ((offset is not None and offset != "") and (nc is not None and nc != "")):
            query = query.order_by(User.UserId).offset(offset).limit(nc)
        else:
            query = query.order_by(User.UserId).offset(0).limit(30)
        clientes_filtrados = query.all()
        clientes_filtrados_serializados = [
            cliente_filtrados.serialize(scope={})
            for cliente_filtrados in clientes_filtrados]
        createdResponse = {'usuarios': clientes_filtrados_serializados}
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 202)
    finally:
        session.close()
        return createdResponse
    

@users.route('/api/users/changePassword', methods=['PATCH'])
@jwt_required()
@ownUtils.my_decorator_http_manage4(allowed_roles=HUMAN_ROLES)
def changePassword(session, claims):
    userNow = claims.get('userId')
    user = ownUtils.validate_if_object_exists(session, 'userId', 'native', userNow, User)
    data = ownUtils.get_info_from_json(request)
    oldPassword = data.get('oldPassword', '')
    if not validate_hashed(oldPassword, user.Password):
        abort(make_response(jsonify({'errores': 'Contraseña antigua incorrecta'}), 400))
    newPassword = data.get('newPassword', '')
    if oldPassword == newPassword:
        abort(make_response(jsonify({'errores': 'La nueva contraseña no puede ser igual a la antigua'}), 400))
    validatedPassword, reasons = ownUtils.validateSecurePassword(newPassword)
    if not validatedPassword:
        abort(make_response(jsonify({'errores': reasons}), 400))
    user.Password = ownUtils.generate_hashedsalt(newPassword)
    session.commit()
    return make_response(jsonify({'mensaje': 'Contraseña cambiada exitosamente'}), 200)

@users.route('/api/users/generatePass')
#@jwt_required()
#@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def generatePass():
    data = request.get_json()
    return {'pass': ownUtils.generate_hashedsalt(data.get('password'))}
    

@users.route('/signup', methods=['POST'])
# @jwt_required()
def signup():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role == ADMIN_ROLE:
            data = request.get_json()
            # gets name, email and password
            name = data.get('name')
            email = data.get('email')
            roleId = data.get('roleId')
            password = data.get('password')
            # Checking if the data is not empty
            if not (name and email and role and password):
                raise Exception(
                    "Nombre, email, rol y contraseña son obligatorios")
            name = str(name)
            email = str(email)
            password = str(password)
            # Checking if password is secure
            validatedPassword, reasons = ownUtils.validateSecurePassword(password)
            if not validatedPassword:
                raise Exception(reasons)
            # Checking if name is valid
            if not ownUtils.is_valid_name(name):
                raise Exception("El nombre ingresado no es válido")
            # Checking if email is valid
            if not ownUtils.is_valid_email(email):
                raise Exception("El correo ingresado no es válido")
            # --------
            # Checking for existing user
            user = session.query(User).filter(
                User.Email == email).first()

            if not user:
                print(roleId)
                role = session.query(Role).get(roleId)
                if role:
                    # Database ORM object
                    user = User(
                        public_id=str(uuid.uuid4()),
                        name=name,
                        email=email,
                        password=ownUtils.generate_hashedsalt(password)
                    )
                    user.Role = role
                    # Adds user to database
                    session.add(user)
                    session.commit()
                    # Returs success response
                    createdResponse = make_response(
                        jsonify({'mensaje': 'Registrado exitosamente'}), 200)
                else:
                    raise Exception('El rol que intentas usar no es válido')
            else:
                # Returns 202 if user already exists
                createdResponse = make_response(
                    jsonify({'mensaje': 'El usuario ya existe'}), 202)
        else:
            # Return 403 if user is valid user but it is not Admin
            createdResponse = make_response(
                jsonify({'errores':
                         'No tienes permisos para dar de alta usuarios'}), 403)
    except Exception as e:
        # Rollbacks to session and return error into response
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        # Closes session and return response
        session.close()
        return createdResponse


@users.route('/api/users/user/updateUser', methods=['PUT'])
@jwt_required()
def updateUser():
    try:
        session = ScopedSession()
        # Get data and files from formData
        if request.form is None and request.files is None:
            raise Exception('No se ha enviado nada')

        data = request.form
        files = request.files
        # ------------
        print('00000000000000000000000000000000000')
        print(data)
        print(files)
        print('00000000000000000000000000000000000')
        userIdentity = get_jwt_identity()
        # Get user data from database using id
        userThis = session.query(User).get(userIdentity)
        updateComment = ''
        if ('image' not in files and 'nickname' not in data and 'phoneNumber' not in data):
            raise Exception('No se ha enviado ningun parametro a altualizar')
        if 'nickname' in data:
            miniAlias = data['nickname']
            if miniAlias != '' and miniAlias is not None:
                userThis.Alias = miniAlias
                updateComment = miniAlias
            else:
                raise Exception('Sent Alias, but is Null')
        elif 'phoneNumber' in data:
            miniPhoneNumber = data['phoneNumber']
            if miniPhoneNumber != '' and miniPhoneNumber is not None:
                error = ownUtils.is_valid_phonenumber(miniPhoneNumber)
                if error:
                    raise Exception(error)
                userThis.PhoneNumber = miniPhoneNumber
                updateComment = miniPhoneNumber
            else:
                raise Exception('Sent phoneNumber, but is Null')
        elif 'image' in files:
            miniImage = files['image']
            if miniImage is not None:
                image_name = addNewPhotoToUser(miniImage)

                if userThis.URLPhoto is not None:
                    eliminate_Photo_existed(userThis.URLPhoto)

                userThis.URLPhoto = image_name

                updateComment = (f'{BACKEND_URL}'
                                 f'/api/user/getImage/{image_name}')
            else:
                raise Exception('Sent image, but is Null')
        else:
            raise Exception('No se envio ningun parametro para actualizar')

        session.commit()
        createdResponse = make_response(
            jsonify({'user': 'Actualizado con exito',
                     'update': updateComment}), 200)

    except Exception as e:
        traceback.print_exc()
        # Rollbacks to session and return error into response
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        # Closes session and return response
        session.close()
        return createdResponse


@users.route('/api/user/getImage/<nameImg>')
def return_imgProfile_user(nameImg):
    try:
        imageToReturn = return_img_user(nameImg)
    except Exception as e:
        print(str(e))
        imageToReturn = return_img_user(None)
    finally:
        return imageToReturn


@users.route('/api/users/news/signup', methods=['POST'])
@jwt_required()
def signupWithoutPassword():
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role == ADMIN_ROLE:
            data = request.get_json()
            # gets name, email
            name = data.get('name')
            email = data.get('email')
            roleId = data.get('roleId')
            # Checking if the data is not empty
            if not (name and email and role):
                raise Exception(
                    "Nombre, email, rol son obligatorios")
            name = str(name)
            email = str(email)
            # Checking if name is valid
            if not ownUtils.is_valid_name(name):
                raise Exception("El nombre ingresado no es válido")
            # Checking if email is valid
            if not ownUtils.is_valid_email(email):
                raise Exception("El correo ingresado no es válido")
            # Checking for existing user
            user = session.query(User).filter(
                User.Email == email).first()
            if not user:
                role = session.query(Role).get(roleId)
                if role:
                    # Database ORM object
                    user = User(
                        public_id=str(uuid.uuid4()),
                        name=name,
                        email=email,
                        password=None,
                    )

                    user.Role = role
                    # Adds user to database
                    session.add(user)
                    send_email_new_account(user.Name, user.Email, user.Public_Id)
                    session.commit()
                    # Returs success response
                    createdResponse = make_response(
                        jsonify({'mensaje': 'Registrado exitosamente',
                                 'newUser': user.serialize(scope={
                                     'basics': ['alias', 'phoneNumber']})}), 200)
                else:
                    raise Exception('El rol no es válido')
            else:
                # Returns 202 if user already exists
                createdResponse = make_response(
                    jsonify({'mensaje': ('El usuario ya existe'
                                         ',Correo ya existente')}), 202)
        else:
            # Return 403 if user is valid user but it is not Admin
            createdResponse = make_response(
                jsonify({'errores':
                         'No tienes permisos para dar de alta usuarios'}), 403)
    except Exception as e:
        # Rollbacks to session and return error into response
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        # Closes session and return response
        session.close()
        return createdResponse


@users.route('/api/users/updateUsers', methods=['PUT'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def updateUserfromAdmin(session):
     # Get data and files from formData
    data = request.get_json()
    # ------------
    userIdentity = data.get('userId')
    name = data.get('name')
    email = data.get('email')
    roleId = data.get('roleId')
    if not (name and email and role and userIdentity):
        raise Exception(
            "Nombre, email, rol y userId son obligatorios")
    # Get user data from database using id
    userThis = session.query(User).get(userIdentity)
    if not ownUtils.is_valid_name(name):
        raise Exception("El nombre ingresado no es válido")
        # Checking if email is valid
    if not ownUtils.is_valid_email(email):
        raise Exception("El correo ingresado no es válido")
    role = session.query(Role).get(roleId)
    if role:
        userThis.Name = name
        userThis.Email = email
        userThis.Role = role
        session.commit()
        return make_response(
            jsonify({'status': 'Actualizado con exito',
                     'user': userThis.serialize(scope={
                         'basics': ['alias', 'phoneNumber'],
                         'role': {}
                     })}), 200)
    else:
        raise Exception('No se encontró el RoleId enviado')


@users.route('/api/users/deleteUsers', methods=['DELETE'])
@jwt_required()
def deleteUserfromAdmin():
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role == ADMIN_ROLE:
            # Get data and files from formData
            data = request.get_json()
            # ------------
            userIdentity = data.get('userId')
            if userIdentity:
                userThis = session.query(User).get(userIdentity)
                if userThis:
                    session.delete(userThis)
                    session.commit()
                    session.close()
                else:
                    raise Exception('No se encontró el usuario')

            createdResponse = make_response(
                jsonify({'status': 'Eliminado con exito'}), 200)
        else:
            createdResponse = make_response(
                jsonify({'errores':
                         'No tienes permisos para eliminar usuarios'}), 403)

    except Exception as e:
        # Rollbacks to session and return error into response
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        # Closes session and return response
        session.close()
        return createdResponse


@users.route('/api/users/changePasswithToken', methods=['POST'])
def changePasswordUser():
    try:
        session = ScopedSession()
        data = request.get_json()
        token = data.get('token')
        password = data.get('password')
        # Checking if the data is not empty
        if not (token and password):
            raise Exception(
                "Datos obligatorios")
        password = str(password)
        tokenNormal = token
        public_id = tokenNormal[48:]
        # Checking if password is secure
        validatedPassword, reasons = ownUtils.validateSecurePassword(password)
        if not validatedPassword:
            raise Exception(reasons)
        # Checking for existing user
        user = session.query(User).filter(
            User.Public_Id == public_id).first()
        if user:
            user.Password = ownUtils.generate_hashedsalt(password)
            session.commit()
            # Returs success response
            createdResponse = make_response(
                jsonify({'mensaje': 'Registrada exitosamente'}), 200)

        else:
            # Returns 202 if user already exists
            createdResponse = make_response(
                jsonify({'mensaje': 'El usuario no se encontró'}), 404)
    except Exception as e:
        # Rollbacks to session and return error into response
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        # Closes session and return response
        session.close()
        return createdResponse


@users.route('/api/users/checkToken', methods=['POST'])
def checkToken():
    try:
        session = ScopedSession()
        data = request.get_json()
        token = data.get('token')
        # Checking if the data is not empty
        if not token:
            raise Exception(
                "Token obligatorio")
        expiredTime = ownUtils.generate_obj_time(token[:14])
        public_id = token[48:]
        user = session.query(User).filter(
            User.Public_Id == public_id).first()

        if not (user.Password):
            if ownUtils.get_time_zone_now() < expiredTime:
                messaje = 'Apto para ocupar'
                code = 200
            else:
                messaje = 'Expirado'
                code = 202
        else:
            messaje = 'Usado'
            code = 202
    except Exception as e:
        # Rollbacks to session and return error into response
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        # Closes session and return response
        session.close()
        if user:
            createdResponse = make_response(
                jsonify({'Token': messaje,
                         'User': user.serialize(scope={})}), code)
        else:
            createdResponse = make_response(
                jsonify({'Token': messaje}), code)
        return createdResponse
