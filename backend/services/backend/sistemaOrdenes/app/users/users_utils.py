import os
import smtplib
import ssl
import secrets
from datetime import datetime, timedelta
from sqlalchemy import or_
from flask import send_file
from werkzeug.utils import secure_filename
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.app import config
from sistemaOrdenes.app.models.Models import User
from sistemaOrdenes import configs

IMGS_USERS = config.Config.USER_IMAGE_FOLDER
ACCOUNT_SYSTEM_MAIL = config.Config.ACCOUNT_SYSTEM_MAIL
PASS_SYSTEM_MAIL = config.Config.PASS_SYSTEM_MAIL
FRONTEND_URL = config.Config.FRONTEND_URL
RUTA_IMGS_USERS = os.path.join(IMGS_USERS, 'profilePhoto')
RUTA_DEFAULT_USERS = os.path.join(IMGS_USERS, 'defaultPhoto')


def armar_query_users(session):
    query = session.query(User)
    return query


def armar_query_browser_users(query, search):
    # cada campo users
    # marketpalce despues
    if search:
        search = "%{}%".format(search)
        # UserId,RoleId,Public_Id,Name,Email,Password
        query = query.filter(or_(
            User.UserId.like(search),
            User.RoleId.like(search),
            User.Public_Id.like(search),
            User.Name.like(search),
            User.Email.like(search),))
    return query


def create_UserImages_folder():
    # Verificar si la ruta completa existe y, si no, crearla
    if not os.path.exists(RUTA_IMGS_USERS):
        os.makedirs(RUTA_IMGS_USERS)


def addNewPhotoToUser(imageFile):
    try:
        create_UserImages_folder()
        miniImage = ownUtils.checkValidImageFile(imageFile)

        if not ownUtils.allowed_img_file_company(imageFile.filename):
            raise Exception("La extension de la imagen es invalido")

        nombreSeguroImg = secure_filename(f'{imageFile.filename}')

        destination = os.path.join(RUTA_IMGS_USERS, nombreSeguroImg)
        passWhile = True
        count = 1
        while passWhile:
            destination = os.path.join(RUTA_IMGS_USERS, nombreSeguroImg)
            if not os.path.exists(destination):
                passWhile = False
            else:
                nombreSeguroImg = f'{str(count)}_{nombreSeguroImg}'
                count = count + 1

        miniImage.save(destination)

        return nombreSeguroImg

    except Exception as e:
        raise Exception(str(e))


def eliminate_Photo_existed(nameImg):
    ubicationPhoto = os.path.join(RUTA_IMGS_USERS, nameImg)
    if os.path.exists(ubicationPhoto):
        os.remove(ubicationPhoto)


def return_img_user(nameImg):
    try:
        ruta_img = ''
        mimetypee = ''
        if nameImg is None:
            default_ruta = os.path.join(
                RUTA_DEFAULT_USERS, 'Default_UserProfilePhoto.png')
            ruta_img = default_ruta
            mimetypee = 'image/png'
        else:
            ruta_img = os.path.join(RUTA_IMGS_USERS, nameImg)
            extension = nameImg.rsplit('.', 1)[-1]
            mimetypee = f'image/{extension}'
        return send_file(ruta_img, mimetype=mimetypee)

    except Exception as e:
        raise Exception(str(e))


# Función para generar un token aleatorio
def generate_token(public_id):
    token = secrets.token_urlsafe(25)
    expirationTime = generate_expiration_time()
    newTimeFormat = expirationTime.strftime("%Y%m%d%H%M%S")
    finalToken = str(newTimeFormat) + str(token) + str(public_id)
    return finalToken


# Función para generar una fecha de expiración
def generate_expiration_time():
    hours = 1
    current_time = ownUtils.get_time_zone_now()
    expiration_time = current_time + timedelta(hours=hours)
    return expiration_time


def send_email_new_account(user_name, destination_email, public_id):
    try:
        # Información del remitente
        cuentaRemitente = ACCOUNT_SYSTEM_MAIL
        contraseniaRemitente = PASS_SYSTEM_MAIL
        url_sitio = FRONTEND_URL
        token = generate_token(public_id)
        url_pagina = f'/crearPassword?token={token}'
        url_boton = f'{url_sitio}{url_pagina}'
        # Crear el mensaje
        mensaje = MIMEMultipart('alternative')
        mensaje['From'] = cuentaRemitente
        mensaje['To'] = destination_email
        mensaje['Subject'] = '¡Bienvenido a la Tortuga! Tu cuenta ha sido creada'
        print('************==================00000')
        print(url_boton)
        print('************==================00000')
        contenido_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    background-color: #f0f0f0;
                    margin: 0;
                    padding: 20px;
                }}

                .container {{
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: #ffffff;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0px 0px 10px #999;
                }}

                h1 {{
                    color: #0073e6;
                }}

                p {{
                    font-size: 16px;
                    line-height: 1.5;
                    color: #333;
                }}

                a {{
                    text-decoration: none;
                    color: #0073e6;
                }}

                a:hover {{
                    text-decoration: underline;
                }}

                .signature {{
                    font-size: 14px;
                    color: #777;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Bienvenido a La Tortuga</h1>
                <p>Hola <strong>{user_name}</strong>,</p>
                <p>¡Te has registrado en La Tortuga!</p>
                <p>Nos complace mucho que te hayas unido a nuestra comunidad. Este correo es para verificar que tu cuenta ha sido creada con éxito y está lista para usar. ¡Gracias por confiar en nosotros!</p>
                <p style="text-align: center;">
                <a href=\"{url_boton}\" style="background-color: #0073e6; color: #fff; text-decoration: none; padding: 10px 20px; border-radius: 5px; display: inline-block;">
                    <strong>Verificar cuenta</strong>
                </a>
                </p>
                <p>Si recibiste este correo por error, ignora este mensaje.</p>
                <p>¿Tienes alguna pregunta o requieres asistencia?</p>
                <p>No dudes en ponerte en contacto en: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p class="signature">Equipo de Creative Planet<br>
                    Cda. Atenea 26, Plaza de las rosas<br>
                    Tlalnepantla de Baz, Méx 54069</p>
            </div>
        </body>
        </html>
        """
        # mensaje.attach(MIMEText(contenido_html, 'html'))
        mensaje.attach(MIMEText(contenido_html, "html"))
        # Conectar al servidor de correo y enviar el mensaje
        try:
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(configs.SERVIDOR_SMTP, configs.PUERTO_SMTP, context=context)
            server.login(cuentaRemitente, contraseniaRemitente)
            server.sendmail(cuentaRemitente, destination_email, mensaje.as_string())
            server.quit()
            print("¡Correo enviado correctamente!")
        except Exception as e:
            print("Error al enviar el correo:", e)
            raise Exception(str(e))
    except Exception as e:
        print(str(e))
        raise Exception(str(e))
