from sistemaOrdenes.app.models.Models import Product_Store, DirectSale, \
    DirectSaleInternalStatus, Client, ProofOfPayment, ProductInDirectSale, \
    Product_SupplierStore, Product, ProductBase
from sistemaOrdenes.app import ownUtils
from datetime import datetime, timedelta
from sqlalchemy import desc, func, and_, select


def update_Atenea_inventary(internal_sku, units, subtract_add, session):
    try:
        error = ownUtils.revisarIntPositivo(units)
        if error:
            raise Exception(f'Las unidades son incorrectas: {error}')
        product_store = session.query(
            Product_Store).get(internal_sku, 1)
        if not product_store:
            raise Exception(
                'El producto no esta registrado para este producto')
        current_stock = product_store.Stock
        if not current_stock:
            raise Exception('El producto no tiene stock')
        if subtract_add == 'add':
            stock_left = current_stock + units
            product_store.Stock = stock_left
        elif subtract_add == 'subtract':
            if (current_stock - units) < 0:
                raise Exception(
                    'El producto no tiene suficiente stock para esta venta')
            stock_left = current_stock - units
            product_store.Stock = stock_left
        else:
            raise Exception('Operacion invalida(add, subtract)')
    except Exception as e:
        return str(e)


def direct_sales_store(query):
    return query.join(Product_Store, and_(ProductInDirectSale.InternalSkuStore == Product_Store.InternalSku, ProductInDirectSale.StoreId == Product_Store.StoreId), isouter=True). \
        join(Product, Product_Store.InternalSku == Product.InternalSku, isouter=True). \
        join(ProductBase, Product.InternalBaseSku == ProductBase.InternalBaseSku, isouter=True)


def direct_sale_supplierStore(query):
    return query.join(Product_SupplierStore, and_(ProductInDirectSale.InternalSkuSupplierStore == Product_SupplierStore.InternalSku, ProductInDirectSale.SupplierStoreId == Product_SupplierStore.SupplierStoreId), isouter=True). \
        join(Product, Product_SupplierStore.InternalSku == Product.InternalSku, isouter=True). \
        join(ProductBase, Product.InternalBaseSku == ProductBase.InternalBaseSku, isouter=True)


def supply_productInDirectSale_from_internal_location(locations, productInDirectSale):
    if not productInDirectSale:
        return 'El id de producto ingresado no corresponde a ningún producto vendido por venta directa'
    locationLevelItems = productInDirectSale.Product_Store.LocationLevelItems_Product_Store
    if not locationLevelItems:
        return 'El producto no se encuentra en ningún lugar del almacen'
    # Get Product_Store linked to productInDirectSale object
    product_store = productInDirectSale.Product_Store
    # Get previously shipped units
    shipped_units = productInDirectSale.ShippedUnits
    # Get previously shipped units
    total_units = productInDirectSale.Units
    total_products = shipped_units
    if type(locations) is not list:
        return "La llave locations(lista de donde se descontará el stock) debe ser tipo list"
    for index, location in enumerate(locations):
        if type(location) is not dict:
            return f'ubicacion num {index}: no es un json'
        amount = location.get("amount")
        if not ownUtils.revisar_int_0_o_mayor(amount):
            return f"ubicacion num {index}:La cantidad ingresada no es correcta (no es un int válido)"
        amount = int(amount)
        total_products += amount
        if total_products > total_units:
            return "los productos a surtir son mas que los reflejados en la venta"
        id_location = location.get("id")
        error = supply_product_from_levelItem(id_location, amount, locationLevelItems)
        if error:
            return f'ubicacion num {index}: {error}'
    # Check if there are enough units in stock cover new shipped amount
    stockToShip = product_store.Stock - (total_products - shipped_units)
    if stockToShip < 0:
        return 'No hay stock suficiente en el almacen para surtir la cantidad solicitada'
    # Set new shipped units to sale item
    productInDirectSale.ShippedUnits = total_products
    # Set new stock to product in store linked
    product_store.Stock = stockToShip


def supply_product_from_levelItem(id_location, amount, locationLevelItems):
    location_level_item_product_store = ownUtils.find_object_in_list(id_location, locationLevelItems, "LocationLevelItemId")
    if not location_level_item_product_store:
        return 'No se encontro el producto ubicacion con este id'
    if amount > location_level_item_product_store.Stock:
        return f"No hay stock suficiente en la ubiacion: {location_level_item_product_store.LocationLevelItem.write_entire_position()}"
    location_level_item_product_store.Stock -= amount


def check_if_direct_sale_is_closed(session, direct_sale):
    """
    Check if direct sale has been closed

    Arguments:
        session {scoped_session} -- sqlalchemy scoped session to access db model
        direct_sale {DirectSale} -- sqlalchemy object DirectSale to get its current DirectSaleInternalStatus

    Keyword arguments:

    Return:
        None
    """
    # Get DirectSaleInternalStatus when DirectSaleInternalStatus is "Cerrada"
    closed_internal_status = session.query(DirectSaleInternalStatus).filter(DirectSaleInternalStatus.DirectSaleInternalStatus == "Cerrada").first()
    if not closed_internal_status:
        raise Exception("Error al consultar status de venta directa: consultar a soporte")
    # Check if DirectSaleInternalStatus ordernum is greater than ordernum from DirectSaleInternalStatus Cerrada
    if direct_sale.DirectSaleInternalStatus.OrderNum < closed_internal_status.OrderNum:
        raise Exception("No se puede surtir una venta que aun no ha sido cerrada")


def direct_sales_count(session, order_internalStatus_ids, start_date, end_date):
    query = no_search_query(session, order_internalStatus_ids, start_date, end_date)
    count = query.count()
    return count


def direct_sales_all(session, order_internalStatus_ids, start_date, end_date, search, offset, nc):
    if search is not None and search != '':
        query = search_query(session, order_internalStatus_ids, start_date, end_date, search)
        query = query.order_by(desc("greatest"))
        query = order_query(query, offset, nc)
        ventas_directas_filtradas = query.all()
        ventas_directas_filtradas = map(lambda venta_directa: venta_directa.DirectSale, ventas_directas_filtradas)
    else:
        query = no_search_query(session, order_internalStatus_ids, start_date, end_date)
        query = order_query(query, offset, nc)
        ventas_directas_filtradas = query.all()
    return ventas_directas_filtradas


def search_query_core(session, order_internalStatus_ids, start_date, end_date, search, columns_to_get=(DirectSale,)):
    query = select(*columns_to_get, func.greatest(func.similarity(ProofOfPayment.OperationNum, search),
                                                  func.similarity(Client.Name, search),
                                                  func.similarity(Client.Nickname, search),
                                                  func.similarity(Client.Email, search),
                                                  func.similarity(Product.InternalSku, search),
                                                  func.similarity(Product.InternalBaseSku, search),
                                                  func.similarity(Product.VariationTitle, search),
                                                  func.similarity(Product.VariationSku, search),
                                                  func.similarity(ProductBase.InternalBaseSku, search),
                                                  func.similarity(ProductBase.Brand, search),
                                                  func.similarity(ProductBase.Description, search),
                                                  func.similarity(ProductBase.Model, search)).label('greatest'))
    query = armar_query_filtros_direct_sales(query, order_internalStatus_ids, start_date, end_date)
    query = query.join(Client, DirectSale.ClientId == Client.ClientId). \
        join(ProofOfPayment, DirectSale.DirectSaleId == ProofOfPayment.DirectSaleId, isouter=True). \
        join(ProductInDirectSale, DirectSale.DirectSaleId == ProductInDirectSale.DirectSaleId)
    query_stores = direct_sales_store(query)
    query_supplierStores = direct_sale_supplierStore(query)
    return query_stores, query_supplierStores


def search_query(session, order_internalStatus_ids, start_date, end_date, search, columns_to_get=(DirectSale,)):
    query = session.query(*columns_to_get, func.greatest(func.similarity(ProofOfPayment.OperationNum, search),
                                                         func.similarity(Client.Name, search),
                                                         func.similarity(Client.Nickname, search),
                                                         func.similarity(Client.Email, search),
                                                         func.similarity(Product.InternalSku, search),
                                                         func.similarity(Product.InternalBaseSku, search),
                                                         func.similarity(Product.VariationTitle, search),
                                                         func.similarity(Product.VariationSku, search),
                                                         func.similarity(ProductBase.InternalBaseSku, search),
                                                         func.similarity(ProductBase.Brand, search),
                                                         func.similarity(ProductBase.Description, search),
                                                         func.similarity(ProductBase.Model, search)).label('greatest'))
    query = armar_query_filtros_direct_sales(query, order_internalStatus_ids, start_date, end_date)
    query = query.join(Client, DirectSale.ClientId == Client.ClientId). \
        join(ProofOfPayment, DirectSale.DirectSaleId == ProofOfPayment.DirectSaleId, isouter=True). \
        join(ProductInDirectSale, DirectSale.DirectSaleId == ProductInDirectSale.DirectSaleId)
    query_stores = direct_sales_store(query)
    query_supplierStores = direct_sale_supplierStore(query)
    query = query_stores.union(query_supplierStores)
    return query


def no_search_query(session, order_internalStatus_ids, start_date, end_date, columns_to_get=(DirectSale,)):
    query = session.query(*columns_to_get)
    query = armar_query_filtros_direct_sales(query, order_internalStatus_ids, start_date, end_date)
    return query


def armar_query_filtros_direct_sales(query, order_internalStatus_ids, start_date, end_date):
    if order_internalStatus_ids:
        internalStatuses = tuple(order_internalStatus_ids.split('-'))
        query = query.filter(
            DirectSale.DirectSaleInternalStatusId.in_(internalStatuses))
    date_format = "%Y-%m-%d"
    if start_date and end_date:
        start_date = datetime.strptime(start_date, date_format)
        end_date = datetime.strptime(
            end_date, date_format) + timedelta(days=1)
        query = query.filter(
            DirectSale.SaleDate.between(start_date, end_date))
    return query


def order_query(query, offset, nc):
    if ((offset is not None and offset != "") and (nc is not None and nc != "")):
        query = query.order_by(
            desc(DirectSale.SaleDate)).offset(offset).limit(nc)
    else:
        query = query.order_by(
            desc(DirectSale.SaleDate)).offset(0).limit(30)
    return query
