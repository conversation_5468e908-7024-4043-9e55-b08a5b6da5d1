from datetime import datetime, timedelta
import traceback
import os
from flask_smorest import Blueprint
from flask import jsonify, request, make_response,\
    current_app, send_from_directory, send_file
from sqlalchemy import desc, func
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet,\
    ParagraphStyle
from reportlab.platypus import SimpleDocTemplate,\
    Paragraph, Spacer, Table, TableStyle, Image, \
    ListFlowable, ListItem
from reportlab.lib import colors
from reportlab.platypus.flowables import Flowable
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.pdfmetrics import registerFontFamily
from reportlab.pdfbase.ttfonts import TTFont
from sistemaOrdenes.app.models.Models import Client, DirectSaleType, \
    DirectSale, User, DirectSaleInternalStatus, \
    ProductInDirectSale, Product, AmountChanger, \
    ChangerGroup, ProductInDirectSale_AmountChanger, \
    Company, DirectSale_AmountChanger, Product_Store, \
    Product_SupplierStore, DirectSaleStackableComment, BankAccount, \
    CommercialTerm, DirectSale_CommercialTerm, ProofOfPayment
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from sistemaOrdenes.app import ownUtils

from sistemaOrdenes.app.models.db import ScopedSession

from sistemaOrdenes.configs import ADMIN_ROLE, \
    ADMNISTRATIVE_ACCOUNTANT_ROLE, ROLES, WAREHOUSE_ROLE

from .direct_sales_utils import direct_sales_all,\
    direct_sales_count, \
    supply_productInDirectSale_from_internal_location

current = os.path.dirname(os.path.realpath(__file__))
QR_PATH = 'qr_image'
IMAGE_PATH = 'companyImage'
bg_color_cell_primary = colors.Color(225 / 255, 239 / 255, 250 / 255)
bg_color_cell_gray_scale = colors.lightgrey

direct_sales = Blueprint("direct_sales", __name__,
                         description="Operations on DirectSale")


def manage_amount_changer_own_sale(changer, amountChangers, changerName):
    if not changer:
        return False, 'campo de descuentos obligatorio'
    else:
        if type(changer) is not dict:
            return False, 'campo de descuento debe ser un diccionario'
        else:
            cantidad_descuento = changer['amount']
            error = ownUtils.revisarFloatPositivo(cantidad_descuento)
            if error:
                return False, error
            tipo_descuento = changer['type']
            print('descount_type-----------------')
            print(tipo_descuento)
            print('descount_type-----------------')
            discount_object = ownUtils.return_amount_changer(amountChangers,
                                                             changerName,
                                                             tipo_descuento)
            if not discount_object:
                return False, 'tipo de descuento no válido'
    return True, {'amount': cantidad_descuento,
                  'amountChanger_obj': discount_object}


@direct_sales.route('/api/directSales/prueba')
# @jwt_required()
def get_info_product():
    return 'prueba direct sales'


@direct_sales.route('/api/directSales/getDirectSale/<directSaleId>')
def get_direct_sale(directSaleId):
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            direct_sale = session.query(DirectSale).get(directSaleId)
            if not direct_sale:
                created_response = make_response(jsonify(
                    {'errores': 'la venta directa no fue encontrada'}), 404)
            else:
                scope = {
                    'directSaleType': {},
                    'client': {},
                    'seller': {},
                    'directSaleInternalStatus': {},
                    'productsInDirectSale': {
                        'productInDirectSale_amountChangers': {
                            'amountChanger': {
                                'amountChangerType': {},
                                'changerGroup': {}
                            }
                        },
                        'productAtStore': {}
                    },
                    'directSale_amountChangers': {
                        'amountChanger': {
                            'amountChangerType': {},
                            'changerGroup': {}
                        }
                    },
                    'directSaleStackableComments': {},
                    'directSale_commercialTerms': {
                        'commercialTerm': {}
                    }
                }
                direct_sale_serialize = direct_sale.serialize(scope=scope)
                created_response = jsonify(direct_sale_serialize)
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para ver ventas directas', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@direct_sales.route('/api/directSales/registerPayment/<directSaleId>', methods=['POST'])
@ownUtils.my_decorator_http_manage
def register_payment(directSaleId, session):
    # claims = get_jwt()
    # role = claims.get('role')
    role = ADMIN_ROLE
    if role in [ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE]:
        data = request.form
        files = request.files
        direct_sale = ownUtils.validate_if_object_exists(session, "directSaleId", "native", directSaleId, DirectSale)
        print('2222222222222222222222')
        print(direct_sale)
        print('2222222222222222222222')
        if direct_sale.ProofOfPayment:
            return make_response(jsonify(
                {'errores':
                 'El comprobante de pago ya ha sido registrado'}), 409)
        operation_num = data.get('operation_num')
        voucher_image = files.get('voucher_image')
        directory = os.path.\
            join(current_app.config['DIRECTSALE_IMAGE_FOLDER'], 'proofsOfPayment')
        secure_name = ownUtils.save_img_secure_name(voucher_image, directory)
        proofOfPayment = ProofOfPayment(operationNum=operation_num, voucherImageName=secure_name)
        closed_status = session.query(DirectSaleInternalStatus).filter(DirectSaleInternalStatus.DirectSaleInternalStatus == 'Cerrada(Pago verificado)').first()
        direct_sale.ProofOfPayment = proofOfPayment
        direct_sale.DirectSaleInternalStatus = closed_status
        session.commit()
        return make_response(jsonify({'info': 'El pago para la venta ha sido registrado'}), 200)
    else:
        # Return 403 if role user is not valid
        return make_response(
            'No tienes permisos para ver ventas directas', 403)


@direct_sales.route('/api/directSales/proofPayment/<directSaleId>')
# @jwt_required()
@ownUtils.my_decorator_http_manage
def get_payment(directSaleId, session):
    # claims = get_jwt()
    # role = claims.get('role')
    role = ADMIN_ROLE
    if role in [ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE]:
        proofPayment = ownUtils.validate_if_object_exists(session, "directSaleId", "native", directSaleId, ProofOfPayment)
        if not proofPayment:
            return make_response(jsonify(
                {'errores':
                 'ProofOfPayment no encontrado'}), 404)
        proofPayment_serialize = proofPayment.serialize(scope={})
        session.commit()
        return make_response(jsonify({'proofPayment': proofPayment_serialize}), 200)
    else:
        # Return 403 if role user is not valid
        return make_response(
            'No tienes permisos para ver ventas directas', 403)


@direct_sales.route('/api/directSale/proofPayment/getImage/<rutaArchivo>')
@jwt_required()
def get_proof_payment_image(rutaArchivo):
    try:
        print('*************************')
        print(rutaArchivo)
        rutaArchivoConDiagonal = ownUtils.ownUnquote(rutaArchivo)

        path = os.path.\
            join(current_app.config['DIRECTSALE_IMAGE_FOLDER'],
                 'proofsOfPayment', rutaArchivoConDiagonal)
        imageToReturn = send_file(path)
    except Exception as e:
        print(str(e))
        path = os.path.\
            join(current_app.config['DIRECTSALE_IMAGE_FOLDER'],
                 "_NotFoundImage\\NotFoundImage.jpg")
        imageToReturn = send_file(path)
        print('final..............')
    finally:
        return imageToReturn


@direct_sales.route('/api/directSales/deleteDirectSale', methods=['DELETE'])
@jwt_required()
@ownUtils.my_decorator_http_manage
def delete_direct_sale(session):
    # claims = get_jwt()
    # role = claims.get('role')
    role = ADMIN_ROLE
    if role in ROLES:
        data = request.get_json()
        direct_sale_id = data.get('direct_sale_id')
        print(direct_sale_id)
        direct_sale = session.query(DirectSale).get(direct_sale_id)
        if not direct_sale:
            return make_response(jsonify(
                {'errores':
                 'Cotización para eliminar no encontrado'}), 404)
        if direct_sale.DirectSaleInternalStatus.DirectSaleInternalStatus != 'En cotización':
            return make_response(jsonify(
                {'errores':
                 'No puedes eliminar una venta con un status que no sea En cotización'}), 409)
        session.delete(direct_sale)
        session.commit()
        return make_response(jsonify(
            {'mensaje':
             'cotización eliminada exitosamente'}), 200)
        # Return 403 if role user is not valid
    else:
        return make_response(
            'No tienes permisos para ver ventas directas', 403)


@direct_sales.route('/api/directSales/productToSell/<internalSku>')
@jwt_required()
@ownUtils.my_decorator_http_manage
def get_product_to_cell(internalSku, session):
    claims = get_jwt()
    role = claims.get('role')
    if role in [ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE]:
        product = session.query(Product).get(internalSku)
        if not product:
            raise Exception('El sku interno no pertence a ningún producto')
        stocksInSupplierStore = product.Product_SupplierStores
        stocksInInternalStore = product.Product_Stores
        stocksInSupplierStoreSerialized = [
            stock.serialize({
                'supplierStore': {}
            })
            for stock in stocksInSupplierStore]
        stocksInInternalStoreSerialized = [
            stock.serialize({
                'store': {}
            })
            for stock in stocksInInternalStore]
        return make_response(jsonify(
            {'productInSupplierStore': stocksInSupplierStoreSerialized,
             'productInInternalStore': stocksInInternalStoreSerialized}), 200)


@direct_sales.route('/api/directSales/updateDirectSale', methods=['PUT'])
@jwt_required()
@ownUtils.my_decorator_http_manage
def update_direct_sale(session):
    claims = get_jwt()
    role = claims.get('role')
    role = ADMIN_ROLE
    if not (role in [ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE]):
        return make_response(
            'No tienes permisos para modificar cotizaciones', 403)
    data = ownUtils.get_info_from_json(request)
    direct_sale_id = data.get('direct_sale_id')
    direct_sale = session.query(DirectSale).get(direct_sale_id)
    if not direct_sale:
        return make_response(jsonify(
            {'errores': 'la cotización no fue encontrada'}), 404)
    if direct_sale.DirectSaleInternalStatus.DirectSaleInternalStatus != 'En cotización':
        return make_response(jsonify(
            {'errores':
             'No puedes editar una venta con un status que no sea En cotización'}), 409)
    # SellerId sesion ------------------
    userIdentity = get_jwt_identity()
    user = session.query(User).get(userIdentity)
    if not user:
        raise Exception('Usuario invalido')
    direct_sale_seller = direct_sale.Seller
    if user != direct_sale_seller:
        raise Exception(
            'Solo el vendedor puede modificar la venta')
    # SaleDate server date
    current_time = ownUtils.get_time_zone_now()
    direct_sale.SaleDate = current_time
    if 'clientId' in data:
        client_id = data.get('clientId')
        if not client_id:
            raise Exception('Cliente obligatorio')
        else:
            client = session.query(Client).get(client_id)
            if not client:
                raise Exception('El cliente no existe')
        direct_sale.Client = client
    # Shipping front -----------------------------
    if 'shipping' in data:
        shipping = data.get('shipping')
        if not shipping:
            raise Exception('Costo de envio obligatorio')
        error = ownUtils.revisarFloatPositivo(shipping)
        if error:
            raise Exception(f'El costo de envio no es válido: {error}')
        direct_sale.Shipping = shipping
    # Check direct sale type ---------------------- front despues
    if 'directSaleType' in data:
        direct_sale_type = data.get('directSaleType')
        if not direct_sale_type:
            raise Exception('Tipo de venta obligatoria')
        direct_sale_type = session.\
            query(DirectSaleType).get(direct_sale_type)
        if not direct_sale_type:
            raise Exception('El tipo de venta no existe')
        direct_sale.DirectSaleType = direct_sale_type
    amountChangers = session.query(AmountChanger).all()
    # DirectSaleInternalStatusId ------------------
    if 'total_discount' in data:
        total_discount_from_request = data.get('total_discount')
        success, total_discount_to_db = manage_amount_changer_own_sale(total_discount_from_request, amountChangers, 'descuento')
        if not success:
            raise Exception(f'Descuento total: {total_discount_to_db}')
        total_discount = DirectSale_AmountChanger(total_discount_to_db['amount'])
        direct_sale.DirectSale_AmountChangers = [total_discount,]
        total_discount.AmountChanger = total_discount_to_db['amountChanger_obj']
    if 'commercialTerms' in data:
        commercial_terms_request = data.get('commercialTerms')
        commercialTerms = []
        if not commercial_terms_request:
            commercial_terms_request = []
        if type(commercial_terms_request) is not list:
            raise Exception('commercialTerms debe ser una lista')
        if ownUtils.has_duplicate_in_list(commercial_terms_request):
            raise Exception('Existen terminos comerciales repetidos')
        for commercial_term_request in commercial_terms_request:
            commercial_term = ownUtils.validate_if_object_exists(session, "id", "native", commercial_term_request, CommercialTerm)
            commercialTerms.append(commercial_term)
        directSale_commercialTerms = []
        direct_sale.DirectSale_CommercialTerms = []
        for commercial_term in commercialTerms:
            directSale_commercialTerm = DirectSale_CommercialTerm()
            directSale_commercialTerm.CommercialTerm = commercial_term
            directSale_commercialTerm.DirectSale = direct_sale
            directSale_commercialTerms.append(directSale_commercialTerm)
        # direct_sale.DirectSale_CommercialTerms = directSale_commercialTerms
    # Products in direct sale
    if 'products' in data:
        products_request = data.get('products')
        if not products_request:
            raise Exception('La lista de productos esta vacia')
        if type(products_request) is not list:
            raise Exception('Los productos deben ser una lista')
        lista_productos_para_vender_desde_bd = []
        errores = ''
        for index, product_request in enumerate(products_request, start=1):
            error_producto = ''
            if type(product_request) == dict:
                # ----------------------
                product_at_store = product_request.get('productAtStore')
                if not product_at_store or type(product_at_store) is not dict:
                    error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'productAtStore debe ser un json')
                else:
                    store = product_at_store.get('store')
                    if not store:
                        error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'productAtStore debe contener la llave store')
                    else:
                        if store.get('type') == 'store':
                            product_store = session.query(Product_Store).get((product_at_store.get('internalSku'), store.get('id')))
                            if not product_store:
                                error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'El producto no existe o no se encuentra en este almacen')
                            else:
                                paid_amount_product = product_store.Cost
                        elif store.get('type') == 'supplier_store':
                            product_store = session.query(Product_SupplierStore).get((product_at_store.get('internalSku'), store.get('id')))
                            if not product_store:
                                error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'El producto no existe o no se encuentra en este almacen del proveedor')
                            else:
                                paid_amount_product = product_store.Cost
                                if not paid_amount_product:
                                    error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'El producto no tiene costo')
                        else:
                            error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'El tipo de almacen no existe')
                # ------------------------
                units_product = product_request.get('units')
                error = ownUtils.revisarIntPositivo(units_product)
                if error:
                    error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, f'unidades no válidas: {error}')
                utility_from_request = product_request.get('utility')
                success, utility_to_db = manage_amount_changer_own_sale(utility_from_request, amountChangers, 'utilidad')
                if not success:
                    error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, utility_to_db)
                discount_from_request = product_request.get('discount')
                success, discount_to_db = manage_amount_changer_own_sale(discount_from_request, amountChangers, 'descuento')
                if not success:
                    error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, discount_to_db)
                # manage discount ---------------------
                if error_producto:
                    errores = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(errores, f'partida {index}', error_producto)
                else:
                    product_to_bd = {
                        'store_type': store.get('type'),
                        'product_store': product_store,
                        'units': units_product,
                        'costAtTimeOfSale': paid_amount_product,
                        'utility': {
                            'amount': utility_to_db['amount'],
                            'amountChanger_obj': utility_to_db['amountChanger_obj']
                        },
                        'discount': {
                            'amount': discount_to_db['amount'],
                            'amountChanger_obj': discount_to_db['amountChanger_obj']
                        }
                    }
                    lista_productos_para_vender_desde_bd.append(product_to_bd)
            else:
                raise Exception('El producto no pudo ser leido')
        # Create products sqlAlchemy object----------------
        if errores:
            raise Exception(errores)
        amountChangerIVA = ownUtils.return_amount_changer(amountChangers, 'IVA', 'percent')
        products_direct_sale = []
        for product_to_bd in lista_productos_para_vender_desde_bd:
            product_in_direct_sale = ProductInDirectSale(product_to_bd['units'], product_to_bd['costAtTimeOfSale'])
            products_direct_sale.append(product_in_direct_sale)
        direct_sale.ProductsInDirectSale = products_direct_sale
        for index, productInDirectSale in enumerate(products_direct_sale):
            iva = ProductInDirectSale_AmountChanger(amountChangerIVA.CurrentValue)
            iva.AmountChanger = amountChangerIVA
            utility = ProductInDirectSale_AmountChanger(lista_productos_para_vender_desde_bd[index]['utility']['amount'])
            utility.AmountChanger = lista_productos_para_vender_desde_bd[index]['utility']['amountChanger_obj']
            discount = ProductInDirectSale_AmountChanger(lista_productos_para_vender_desde_bd[index]['discount']['amount'])
            discount.AmountChanger = lista_productos_para_vender_desde_bd[index]['discount']['amountChanger_obj']
            productInDirectSale.ProductInDirectSale_AmountChangers = [iva, utility, discount]
            if lista_productos_para_vender_desde_bd[index]['store_type'] == 'store':
                productInDirectSale.Product_Store = lista_productos_para_vender_desde_bd[index]['product_store']
            elif lista_productos_para_vender_desde_bd[index]['store_type'] == 'supplier_store':
                productInDirectSale.Product_SupplierStore = lista_productos_para_vender_desde_bd[index]['product_store']
            else:
                print('cuaaaaaaaaaaaaaaaaakkkkkkkkkkkkkkk')
    session.commit()
    return make_response(jsonify({'mensaje': 'Cambios realizados en la venta'}), 200)


@direct_sales.route('/api/directSales/recordDirectSale', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles={ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE})
def register_direct_sale(session):
    data = ownUtils.get_info_from_json(request)
    print(data)
    commercial_terms_request = data.get('commercialTerms')
    commercialTerms = []
    if not commercial_terms_request:
        commercial_terms_request = []
    if type(commercial_terms_request) is not list:
        raise Exception('commercialTerms debe ser una lista')
    if ownUtils.has_duplicate_in_list(commercial_terms_request):
        raise Exception('Existen terminos comerciales repetidos')
    for commercial_term_request in commercial_terms_request:
        commercial_term = ownUtils.validate_if_object_exists(session, "id", "native", commercial_term_request, CommercialTerm)
        commercialTerms.append(commercial_term)
    userIdentity = get_jwt_identity()
    user = session.query(User).get(userIdentity)
    if not user:
        raise Exception('Usuario  vendedor invalido')
    # SaleDate server date
    current_time = ownUtils.get_time_zone_now()
    client_id = data.get('clientId')
    if not client_id:
        raise Exception('Cliente obligatorio')
    client = session.query(Client).get(client_id)
    if not client:
        raise Exception('El cliente no existe')
    # Shipping front -----------------------------
    shipping = data.get('shipping')
    if not shipping:
        raise Exception('Costo de envio obligatorio')
    error = ownUtils.revisarFloatPositivo(shipping)
    if error:
        raise Exception(f'El costo de envio no es válido: {error}')
    # Check direct sale type ---------------------- front despues
    direct_sale_type = data.get('directSaleType')
    if not direct_sale_type:
        raise Exception('Tipo de venta obligatoria')
    direct_sale_type = session.query(DirectSaleType).get(direct_sale_type)
    if not direct_sale_type:
        raise Exception('El tipo de venta no existe')
    # DirectSaleInternalStatusId ------------------ front despues
    direct_sale_internal_status = session.query(DirectSaleInternalStatus).filter(DirectSaleInternalStatus.DirectSaleInternalStatus == 'En cotización').first()
    if not direct_sale_internal_status:
        raise Exception('Status en cotización no fue encotrado, informar a soporte')
    amountChangers = session.query(AmountChanger).all()
    # Products in direct sale
    products_request = data.get('products')
    if not products_request:
        raise Exception('La lista de productos esta vacia')
    if type(products_request) is not list:
        raise Exception('Los productos deben ser una lista')
    total_discount_from_request = data.get('total_discount')
    if total_discount_from_request:
        success, total_discount_to_db = manage_amount_changer_own_sale(total_discount_from_request, amountChangers, 'descuento')
        if not success:
            raise Exception(f'Descuento total: {total_discount_to_db}')
    else:
        total_discount_to_db = None
    lista_productos_para_vender_desde_bd = []
    errores = ''
    for index, product_request in enumerate(products_request, start=1):
        error_producto = ''
        if type(product_request) == dict:
            product_at_store = product_request.get('productAtStore')
            if not product_at_store or type(product_at_store) is not dict:
                error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'productAtStore debe ser un json')
            else:
                store = product_at_store.get('store')
                if not store:
                    error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'productAtStore debe contener la llave store')
                else:
                    if store.get('type') == 'store':
                        product_store = session.query(Product_Store).get((product_at_store.get('internalSku'), store.get('id')))
                        if not product_store:
                            error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'El producto no existe o no se encuentra en este almacen')
                        else:
                            paid_amount_product = product_store.Cost
                    elif store.get('type') == 'supplier_store':
                        product_store = session.query(Product_SupplierStore).get((product_at_store.get('internalSku'), store.get('id')))
                        if not product_store:
                            error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'El producto no existe o no se encuentra en este almacen del proveedor')
                        else:
                            paid_amount_product = product_store.Cost
                            if not paid_amount_product:
                                error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'El producto no tiene costo')
                    else:
                        error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, 'El tipo de almacen no existe')
            units_product = product_request.get('units')
            error = ownUtils.revisarIntPositivo(units_product)
            if error:
                error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, f'unidades no válidas: {error}')
            utility_from_request = product_request.get('utility')
            success, utility_to_db = manage_amount_changer_own_sale(utility_from_request, amountChangers, 'utilidad')
            if not success:
                error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, utility_to_db)
            # Manejar descuento
            discount_from_request = product_request.get('discount')
            success, discount_to_db = manage_amount_changer_own_sale(discount_from_request, amountChangers, 'descuento')
            if not success:
                error_producto = ownUtils.concatenarMensajeRetroalimentacion(error_producto, discount_to_db)
            # manage discount ---------------------
            if error_producto:
                errores = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(errores, f'partida {index}', error_producto)
            else:
                product_to_bd = {
                    'store_type': store.get('type'),
                    'product_store': product_store,
                    'units': units_product,
                    'costAtTimeOfSale': paid_amount_product,
                    'utility': {
                        'amount': utility_to_db['amount'],
                        'amountChanger_obj': utility_to_db['amountChanger_obj']
                    },
                    'discount': {
                        'amount': discount_to_db['amount'],
                        'amountChanger_obj': discount_to_db['amountChanger_obj']
                    }
                }
                lista_productos_para_vender_desde_bd.append(product_to_bd)
        else:
            raise Exception('El producto no pudo ser leido')
    # Create products sqlAlchemy object----------------s
    if errores:
        raise Exception(errores)
    else:
        amountChangerIVA = ownUtils.return_amount_changer(amountChangers, 'IVA', 'percent')
        products_direct_sale = []
        for product_to_bd in lista_productos_para_vender_desde_bd:
            product_in_direct_sale = ProductInDirectSale(product_to_bd['units'], product_to_bd['costAtTimeOfSale'])
            #
            if product_to_bd['store_type'] == 'store':
                product_in_direct_sale.Product_Store = product_to_bd['product_store']
            elif product_to_bd['store_type'] == 'supplier_store':
                product_in_direct_sale.Product_SupplierStore = product_to_bd['product_store']
            iva = ProductInDirectSale_AmountChanger(amountChangerIVA.CurrentValue)
            iva.AmountChanger = amountChangerIVA
            utility = ProductInDirectSale_AmountChanger(product_to_bd['utility']['amount'])
            utility.AmountChanger = product_to_bd['utility']['amountChanger_obj']
            discount = ProductInDirectSale_AmountChanger(product_to_bd['discount']['amount'])
            discount.AmountChanger = product_to_bd['discount']['amountChanger_obj']
            product_in_direct_sale.ProductInDirectSale_AmountChangers = [iva, utility, discount]
            products_direct_sale.append(product_in_direct_sale)
    directSale_commercialTerms = []
    for commercial_term in commercialTerms:
        directSale_commercialTerm = DirectSale_CommercialTerm()
        directSale_commercialTerm.CommercialTerm = commercial_term
        directSale_commercialTerms.append(directSale_commercialTerm)
    direct_sale = DirectSale()
    direct_sale.DirectSaleType = direct_sale_type
    direct_sale.Client = client
    direct_sale.Seller = user
    direct_sale.Shipping = shipping
    direct_sale.DirectSaleInternalStatus = direct_sale_internal_status
    direct_sale.SaleDate = current_time
    direct_sale.DirectSale_CommercialTerms = directSale_commercialTerms
    if total_discount_to_db:
        total_discount = DirectSale_AmountChanger(total_discount_to_db['amount'])
        total_discount.AmountChanger = total_discount_to_db['amountChanger_obj']
        direct_sale.DirectSale_AmountChangers = [total_discount,]
    direct_sale.ProductsInDirectSale = products_direct_sale
    session.commit()
    return make_response(jsonify({'mensaje': 'Venta registrada exitosamente'}), 201)


@direct_sales.route('/api/directSales/getDirectSalesFiltro')
@jwt_required()
@ownUtils.my_decorator_http_manage
def get_direct_sales_filtro(session):
    claims = get_jwt()
    role = claims.get('role')
    if role in ROLES:
        offset = request.args.get('offset')
        nc = request.args.get('next')
        order_internalStatus_ids = request.args.get('orderInternalStatusId')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        search = request.args.get('search')
        scope = {
            'directSaleType': {},
            'client': {},
            'seller': {},
            'directSaleInternalStatus': {},
            'productsInDirectSale': {
                'productInDirectSale_amountChangers': {
                    'amountChanger': {
                        'amountChangerType': {},
                        'changerGroup': {}
                    }
                },
                'productAtStore': {}
            },
            'directSale_amountChangers': {
                'amountChanger': {
                    'amountChangerType': {},
                    'changerGroup': {}
                }
            },
            'directSaleStackableComments': {}
        }
        ventas_directas_filtradas = direct_sales_all(session, order_internalStatus_ids, start_date, end_date, search, offset, nc)
        venta_directa_filtradas_serizalizadas = list(map(lambda venta_directa: venta_directa.serialize(scope=scope), ventas_directas_filtradas))
        # print(venta_directa.keys())
        return jsonify(
            {'ventas_directas': venta_directa_filtradas_serizalizadas})
    else:
        # Return 403 if role user is not valid
        return make_response(
            'No tienes permisos para ver ventas directas', 403)


@direct_sales.route('/api/directSales/loadPageNewDirectSale')
def load_page_new_direct_sale():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            order_internalStatus_ids = request.args.get('orderInternalStatusId')
            start_date = request.args.get('startDate')
            end_date = request.args.get('endDate')
            # -----
            query = session.query(DirectSaleInternalStatus)
            direct_sale_internal_statuses = query.all()
            direct_sale_internal_statuses_serialized = [direct_sale_internal_status.serialize() for direct_sale_internal_status in direct_sale_internal_statuses]
            query = session.query(DirectSaleType)
            direct_sale_types = query.all()
            direct_sale_types_serialized = [direct_sale_type.serialize() for direct_sale_type in direct_sale_types]
            query = session.query(AmountChanger).join(ChangerGroup).filter(ChangerGroup.Name == 'Impuesto')
            taxes = query.all()
            tax_scope = {
                'amountChangerType': {},
                'changerGroup': {}
            }
            taxes_serialized = [tax.serialize(scope=tax_scope) for tax in taxes]
            query = session.query(CommercialTerm)
            commercial_terms = query.all()
            commercial_terms_serialized = [commercial_term.serialize() for commercial_term in commercial_terms]
            ####
            directSales_count = direct_sales_count(session, order_internalStatus_ids, start_date, end_date)
            loadPageNewDirectSale = {
                'directSaleTypes': direct_sale_types_serialized,
                'directSaleInternalStatuses': direct_sale_internal_statuses_serialized,
                'taxes': taxes_serialized,
                'commercialTerms': commercial_terms_serialized,
                'numDirectSales': directSales_count
            }
            createdResponse = jsonify({'loadPageNewDirectSale': loadPageNewDirectSale})
        else:
            # Return 403 if role user is not valid
            createdResponse = make_response(
                'No tienes permisos para realizar ventas', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@direct_sales.route('/api/directSales/internalStatuses/getInternalStatuses')
def get_info_for_direct_sale():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            query = session.query(DirectSaleInternalStatus)
            direct_sale_internal_statuses = query.all()
            print(direct_sale_internal_statuses)
            direct_sale_internal_statuses_serialized = [direct_sale_internal_status.serialize() for direct_sale_internal_status in direct_sale_internal_statuses]
            createdResponse = jsonify({'directSaleInternalStatuses': direct_sale_internal_statuses_serialized})
        else:
            # Return 403 if role user is not valid
            createdResponse = make_response(
                'No tienes permisos para realizar ventas', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@direct_sales.route('/api/directSales/internalStatuses/createInternalStatus', methods=['POST'])
def create_internal_status_for_direct_sale():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            # -----
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON: {str(e)}')
            # -----
            errores = ''
            name = data.get('name')
            if not name:
                errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, 'Nombre del status interno obligatorio (name)')
            else:
                len_name = DirectSaleInternalStatus.DirectSaleInternalStatus.property.columns[0].type.length
                if len(name) > len_name:
                    errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, f'La descripción del status interno debe ser menor {len_name}')
            description = data.get('description')
            if not description:
                errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, 'Descripción del status interno obligatorio (description)')
            else:
                len_description = DirectSaleInternalStatus.Description.property.columns[0].type.length
                if len(description) > len_description:
                    errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, f'La descripción del status interno debe ser menor {len_description}')
            if errores:
                raise Exception(errores)
            # ------------
            max_order_num = session.query(func.max(DirectSaleInternalStatus.OrderNum)).scalar()
            if not max_order_num:
                max_order_num = 0
            print("Maximum value:", max_order_num)
            direct_sale_internal_status = DirectSaleInternalStatus(name, description, max_order_num + 1)
            # ------------
            session.add(direct_sale_internal_status)
            session.commit()
            created_response = make_response(jsonify({'mensaje': 'Status interno para cotizaciones agregado'}), 201)
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para realizar ventas', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@direct_sales.route('/api/directSales/internalStatuses/renameInternalStatus', methods=['PUT'])
def update_internal_status_for_direct_sale():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            # -----
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON: {str(e)}')
            # -----
            id = data.get('id')
            if not id:
                raise Exception('El id de status interno es obligatorio(id)')
            direct_sale_internal_status = session.query(DirectSaleInternalStatus).get(id)
            if not direct_sale_internal_status:
                raise Exception('El id enviado no corresponde a ningun status interno para ordenes directas')
            errores = ''
            if 'name' in data:
                name = data.get('name')
                if not name:
                    errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, 'Nombre del status interno obligatorio (name)')
                else:
                    len_name = DirectSaleInternalStatus.DirectSaleInternalStatus.property.columns[0].type.length
                    if len(name) > len_name:
                        errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, f'La descripción del status interno debe ser menor {len_name}')
                    else:
                        direct_sale_internal_status.DirectSaleInternalStatus = name
            if errores:
                raise Exception(errores)
            session.commit()
            created_response = make_response(jsonify({'mensaje': 'Status interno para cotizaciones modificado'}), 200)
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para realizar ventas', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@direct_sales.route('/api/directSales/internalStatuses/deleteInternalStatus', methods=['DELETE'])
def get_info_for_direct_sale_():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON: {str(e)}')
            # -----
            id = data.get('id')
            if not id:
                raise Exception('Id de estatus interno para venta directa obligatorio (id)')
            # ----
            direct_saleInternal_status = session.query(DirectSaleInternalStatus).get(id)
            if not direct_saleInternal_status:
                raise Exception('El id enviado no corresponde a ningún status interno para venta directa')
            # ----
            order_num_deleted_direct_sale = direct_saleInternal_status.OrderNum
            session.delete(direct_saleInternal_status)
            up_direct_sale_internal_statuses = session.query(DirectSaleInternalStatus).filter(DirectSaleInternalStatus.OrderNum > order_num_deleted_direct_sale)
            print(up_direct_sale_internal_statuses)
            for status_aux in up_direct_sale_internal_statuses:
                status_aux.OrderNum = status_aux.OrderNum - 1
            session.commit()
            created_response = jsonify({'mensaje': 'Status interno eliminado exitosamente'})
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para eliminar status interno para las ventas', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@direct_sales.route('/api/directSales/internalStatuses/reOrder', methods=['PUT'])
def reorder_internal_status_for_direct_sale():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            # -----
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON: {str(e)}')
            # -----
            new_order_to_status = data.get('new_order_to_status')
            if type(new_order_to_status) is not dict:
                raise Exception('El formato para el nuevo orden de los estatus debe ser un dict')
            print(new_order_to_status)
            num_order_values = new_order_to_status.values()
            print('0000000000000000000000')
            print(num_order_values)
            print('0000000000000000000000')
            internal_statuses = session.query(DirectSaleInternalStatus).all()
            num_internal_statuses = len(internal_statuses)
            print(num_internal_statuses)
            if not ownUtils.validar_valores_order(num_order_values, num_internal_statuses):
                raise Exception('los valores deben ser enteros, no repetirse y ser continuos hasta la cantidad de status existentes')
            num_order_keys = new_order_to_status.keys()
            num_order_keys_ints = ownUtils.check_ids_internal_status(num_order_keys, internal_statuses, 'DirectSaleInternalStatusId')
            if not num_order_keys_ints:
                raise Exception('Los ids no corresponden a los guardados en el sistema')
            if not ownUtils.update_order_nums(internal_statuses, new_order_to_status, 'DirectSaleInternalStatusId'):
                raise Exception('Ocurrio un error inesperado')
            session.commit()
            created_response = make_response(jsonify({'mensaje': 'Orden de los status internos modificados'}), 200)
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para realizar ventas', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


class HRFlowable(Flowable):

    def __init__(self, width=1, color=colors.black, line_width=None, indent=0):
        Flowable.__init__(self)
        self.stroke_width = width
        self.color = color
        self.line_width = line_width
        self.indent = indent

    def draw(self):
        self.canv.setStrokeColor(self.color)
        self.canv.setLineWidth(self.stroke_width)
        self.canv.line(self.indent - 5, 0, self.line_width - 4, 0)


def extract_data_company(company):
    if not company:
        raise Exception('Dar de alta la información de la compañia para generar cotizaciones en pdf')
    if not company.ImageCompany:
        raise Exception('Información de la compañia incompleta: sin imagen')
    company_photo = os.path.join(current_app.config['COMPANY_IMAGE_FOLDER'], IMAGE_PATH, company.ImageCompany)
    if not company.CompanyName:
        raise Exception('Información de la compañia incompleta: Sin nombre')
    company_name = company.CompanyName
    if not company.RFC:
        raise Exception('Información de la compañia incompleta: Sin RFC')
    rfc = company.RFC
    if not company.Qr_code_SAT:
        raise Exception('Información de la compañia incompleta: Sin link de validación SAT')
    qr_code = os.path.join(
        current_app.config['COMPANY_IMAGE_FOLDER'], QR_PATH, company.Qr_code_SAT)
    return company_photo, company_name, rfc, qr_code


def calculate_line_width(margin):
    global line_width
    line_width = line_width = (letter[0] - 2 * margin) - 3


def get_width_for_n_columns(n):
    return (line_width / 12) * n


def found_amount_changer_by_name(changers, name):
    for changer in changers:
        if changer.AmountChanger.Name == name:
            return changer
    raise Exception('Descuento no válido')


def calcular_precio_con_utilidad(costo_proveedor, utilidad_obj):
    utilidad_amount = utilidad_obj.Amount
    utilidad_type = utilidad_obj.AmountChanger.AmountChangerType.Name
    if utilidad_type == 'percent':
        precio_con_utilidad = costo_proveedor * (1 + (utilidad_amount / 100))
    elif utilidad_type == 'nominal':
        precio_con_utilidad = costo_proveedor + utilidad_amount
    else:
        precio_con_utilidad = False
    return precio_con_utilidad


def calcular_iva_partida(importe_partida, IVA_obj):
    IVA_amount = IVA_obj.Amount
    IVA_type = IVA_obj.AmountChanger.AmountChangerType.Name
    if IVA_type == 'percent':
        precio_con_IVA = importe_partida * (IVA_amount / 100)
    else:
        precio_con_IVA = False
    return precio_con_IVA, IVA_amount


def calcular_precio_con_descuento(precio_con_utilidad, descuento_obj):
    descuento_amount = descuento_obj.Amount
    descuento_type = descuento_obj.AmountChanger.AmountChangerType.Name
    if descuento_type == 'percent':
        precio_con_descuento = precio_con_utilidad * (1 - (descuento_amount / 100))
        descuento_str = f'{descuento_amount} %'
    elif descuento_type == 'nominal':
        precio_con_descuento = precio_con_utilidad - descuento_amount
        descuento_str = f'$ {descuento_amount}'
    else:
        precio_con_descuento = False
        descuento_str = False
    return precio_con_descuento, descuento_str


def calcular_cantidades_desglozadas(partida):
    units = partida.Units
    cost = partida.CostAtTimeOfSale
    changers = partida.ProductInDirectSale_AmountChangers
    utilidad_obj = found_amount_changer_by_name(changers, "utilidad")
    precio_con_utilidad = calcular_precio_con_utilidad(cost, utilidad_obj)
    descuento_obj = found_amount_changer_by_name(changers, "descuento")
    precio_con_descuento, descuento_str = calcular_precio_con_descuento(precio_con_utilidad, descuento_obj)
    importe_partida = precio_con_descuento * units
    IVA_obj = found_amount_changer_by_name(changers, "IVA")
    iva_partida, IVA_amount = calcular_iva_partida(importe_partida, IVA_obj)
    return round(precio_con_utilidad, 2), descuento_str, round(precio_con_descuento, 2), round(importe_partida, 2), round(iva_partida, 2), IVA_amount


def create_table_1(company, styles):
    company_photo, company_name, rfc, qr_code = extract_data_company(company)
    img = Image(company_photo, width=160, height=60)
    codigo = Image(qr_code, width=60, height=60)
    rfc_str = f"<b>RFC: </b> {rfc}"
    company_name_str = f"<b>{company_name}</b>"
    partidas_style = ParagraphStyle(
        'PartidasStyle',
        parent=styles['Normal'],
        fontName='Montserrat',
        textColor=colors.Color(0.2, 0.2, 0.2),
        alignment=1,
        fontSize=10
    )
    data = [
        [img, Paragraph(company_name_str, partidas_style), codigo],  # Another regular row for demonstration
        [None, Paragraph(rfc_str, partidas_style), None]
    ]
    table = Table(data, colWidths=[200, 200, 137])  # Equally divided columns
    # Base table styling
    style = [
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('BACKGROUND', (1, 0), (1, 0), bg_color_cell_primary),
        ('SPAN', (0, 0), (0, 1)),
        ('SPAN', (2, 0), (2, 1))
    ]
    table.setStyle(TableStyle(style))
    return table


def create_table_2(direct_sale, styles):
    # Create a new style with a background color
    mixed_style = ParagraphStyle(
        'MixedStyle',
        parent=styles['Normal'],
        fontName='Montserrat',  # Default font
        textColor=colors.black,
        fontSize=10,
        allowWidows=1,  # This is the magic part that allows the embedded tags to work
    )
    mixed_style_12 = ParagraphStyle(
        'MixedStyle',
        parent=mixed_style,
        fontSize=12
    )

    pedido = "<b>PEDIDO</b>"
    num_pedido_text = "Num. pedido:"
    num_pedido = f'<b>P{direct_sale.DirectSaleId}</b>'
    o_c_cliente_text = "O.C Cliente"
    moneda_text = "Moneda"
    moneda = "<b>MXN</b>"
    proyect_text = "Proyecto"

    dates = f'Fecha de pedido: {direct_sale.SaleDate.strftime("%d/%m/%Y")} <br/> Vencimiento: <b>{(direct_sale.SaleDate + timedelta(days=2)).strftime("%d/%m/%Y")}</b>'
    data = [
        [Paragraph(pedido, mixed_style_12), Paragraph(num_pedido_text, mixed_style), Paragraph(o_c_cliente_text, mixed_style), Paragraph(moneda_text, mixed_style), Paragraph(proyect_text, mixed_style), Paragraph(dates, mixed_style)],
        [None, Paragraph(num_pedido, mixed_style), None, Paragraph(moneda, mixed_style), None, None],
    ]
    table = Table(data, colWidths=[(line_width / 4) - 50, line_width / 8, line_width / 8, line_width / 8, line_width / 8, (line_width / 4) + 50])  # Equally divided columns
    style = [
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('SPAN', (0, 0), (0, 1)),
        ('SPAN', (-1, 0), (-1, 1)),
        ('BACKGROUND', (0, 0), (-1, 0), bg_color_cell_primary),
        ('BACKGROUND', (0, 1), (0, 1), bg_color_cell_primary),
        ('BACKGROUND', (-1, 1), (-1, 1), bg_color_cell_primary),
    ]
    table.setStyle(TableStyle(style))
    return table


def create_table_3(direct_sale, styles):
    address = (f'{direct_sale.Client.Address} {direct_sale.Client.ZipCode},<br/>'
               f'{direct_sale.Client.City},<br/>'
               f'{direct_sale.Client.State}'
               )
    mixed_style = ParagraphStyle(
        'MixedStyle',
        parent=styles['Normal'],
        fontName='Montserrat',  # Default font
        textColor=colors.black,
        fontSize=8,
        allowWidows=1,  # This is the magic part that allows the embedded tags to work
    )
    cliente_text_str = "<b>Cliente</b>"
    direccion_text_str = "<b>Dirección</b>"
    rfc_text_str = "<b>RFC</b>"
    uso_de_CFDI_text_str = "<b>Uso de CFDI</b>"
    sucursal_text_str = "<b>Sucursal</b>"
    metodo_entrega_text_str = "<b>Método de entrega</b>"
    direccion_entrega_text_str = "<b>Dirección de entrega</b>"
    condicion_pago_text_str = "<b>Condición de pago</b>"
    client_str = direct_sale.Client.Name
    address_str = (f'{direct_sale.Client.Address} {direct_sale.Client.ZipCode},<br/>'
                   f'{direct_sale.Client.City},<br/>'
                   f'{direct_sale.Client.State}'
                   )
    rfc_client_str = "rfc__cliente"
    condicion_pago_str = "G01 - Adquisición de Mercancias"
    sucursal_str = "Atenea"
    metodo_entrega_str = "Will call"
    condicion_pago_str = "Pago en una sola exhibición"
    data = [
        [Paragraph(cliente_text_str, mixed_style), Paragraph(client_str, mixed_style), Paragraph(metodo_entrega_text_str, mixed_style), Paragraph(metodo_entrega_str, mixed_style)],
        [Paragraph(direccion_text_str, mixed_style), Paragraph(address_str, mixed_style), Paragraph(direccion_entrega_text_str, mixed_style), Paragraph(address, mixed_style)],
        [Paragraph(rfc_text_str, mixed_style), Paragraph(rfc_client_str, mixed_style), None, None],
        [Paragraph(uso_de_CFDI_text_str, mixed_style), Paragraph(condicion_pago_str, mixed_style), Paragraph(condicion_pago_text_str, mixed_style), Paragraph(condicion_pago_str, mixed_style)],
        [Paragraph(sucursal_text_str, mixed_style), Paragraph(sucursal_str, mixed_style), None, None]
    ]
    table = Table(data, colWidths=[get_width_for_n_columns(2), get_width_for_n_columns(4), get_width_for_n_columns(2), get_width_for_n_columns(4)])  # Equally divided columns
    style = [
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
    ]
    table.setStyle(TableStyle(style))
    return table


def get_product_attribute_from_partida(partida, atribute_name):
    if partida.Product_Store:
        attribute = getattr(partida.Product_Store.Product.ProductBase, atribute_name)
    else:
        attribute = getattr(partida.Product_SupplierStore.Product.ProductBase, atribute_name)
    return attribute


def create_table_4(direct_sale, styles):
    partidas_style = ParagraphStyle(
        'PartidasStyle',
        parent=styles['Normal'],
        fontName='Montserrat',
        textColor=colors.Color(0.2, 0.2, 0.2),
        spaceBefore=30,
        spaceAfter=30,
        alignment=1,
        fontSize=7,
        allowWidows=1,  # This is the magic part that allows the embedded tags to work
    )
    data = [
        [Paragraph("Cantidad", partidas_style),
         Paragraph("Marca", partidas_style),
         Paragraph("Modelo", partidas_style),
         Paragraph("Descripción", partidas_style),
         Paragraph("Precio", partidas_style),
         Paragraph("Descuento", partidas_style),
         Paragraph("P.Unitario", partidas_style),
         Paragraph("Importe", partidas_style)],
    ]

    costo_total_sin_IVA = 0
    IVA_total = 0

    for partida in direct_sale.ProductsInDirectSale:
        units_str = str(partida.Units) if partida.Units else "Desconocido"
        brand_str = get_product_attribute_from_partida(partida, "Brand")
        model_str = get_product_attribute_from_partida(partida, "Model")
        description_str = get_product_attribute_from_partida(partida, "Description")

        precio_con_utilidad, descuento_str, precio_con_descuento, importe_partida, IVA_partida, IVA_amount = calcular_cantidades_desglozadas(partida)
        precio_con_utilidad_str = f'${precio_con_utilidad}'
        precio_con_descuento_str = f'${precio_con_descuento}'
        importe_partida_str = f'${importe_partida}'
        costo_total_sin_IVA += importe_partida
        IVA_total += IVA_partida
        info_product_aux = [Paragraph(units_str, partidas_style),
                            Paragraph(brand_str, partidas_style),
                            Paragraph(model_str, partidas_style),
                            Paragraph(description_str, partidas_style),
                            Paragraph(precio_con_utilidad_str, partidas_style),
                            Paragraph(descuento_str, partidas_style),
                            Paragraph(precio_con_descuento_str, partidas_style),
                            Paragraph(importe_partida_str, partidas_style)
                            ]
        data.append(info_product_aux)
    # Create a Table object
    table = Table(data, colWidths=[get_width_for_n_columns(1), get_width_for_n_columns(1), get_width_for_n_columns(1), get_width_for_n_columns(5), get_width_for_n_columns(1), get_width_for_n_columns(1), get_width_for_n_columns(1), get_width_for_n_columns(1)])
    # Add style to the table
    style = TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), bg_color_cell_gray_scale),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('GRID', (0, 0), (-1, -1), 1, colors.white)
    ])
    table.setStyle(style)
    return table, costo_total_sin_IVA, IVA_total, IVA_amount


def create_table_5(costo_total_sin_IVA, IVA_total, IVA_amount, styles):
    costo_total_sin_IVA_str = f'${round(costo_total_sin_IVA, 2)}'
    IVA_total_str = f'${round(IVA_total, 2)}(%{IVA_amount})'
    total_str = f'${round(costo_total_sin_IVA + IVA_total, 2)}'
    mixed_style = ParagraphStyle(
        'MixedStyle',
        parent=styles['Normal'],
        fontName='Montserrat',  # Default font
        textColor=colors.black,
        fontSize=10,
        allowWidows=1,  # This is the magic part that allows the embedded tags to work
    )
    comentarios_text_str = "<b>Comentarios:</b>"
    mxn_text_str = "<b>MXN</b>"
    subtotal_text_str = "<b>Subtotal</b>"
    IVA_text_str = "<b>IVA</b>"
    total_text_str = "<b>Total</b>"
    data = [
        [Paragraph(comentarios_text_str, mixed_style), None, Paragraph(mxn_text_str, mixed_style)],
        [None, Paragraph(subtotal_text_str, mixed_style), Paragraph(costo_total_sin_IVA_str, mixed_style)],
        [None, Paragraph(IVA_text_str, mixed_style), Paragraph(IVA_total_str, mixed_style)],
        [None, Paragraph(total_text_str, mixed_style), Paragraph(total_str, mixed_style)],
    ]
    table = Table(data, colWidths=[get_width_for_n_columns(8), get_width_for_n_columns(2), get_width_for_n_columns(2)])
    style = TableStyle([
        ('BACKGROUND', (1, 0), (1, -1), bg_color_cell_gray_scale),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.white),
        ('SPAN', (0, 1), (0, 3)),
        ('BACKGROUND', (0, 1), (0, 1), bg_color_cell_gray_scale),
    ])
    table.setStyle(style)
    return table


def create_table_6(company, styles):
    # Custom style that allows for embedded font changes
    title_style = ParagraphStyle(
        'MixedStyle',
        parent=styles['Normal'],
        fontName='Montserrat',  # Default font
        textColor=colors.black,
        fontSize=8,
        alignment=1,
        allowWidows=1,  # This is the magic part that allows the embedded tags to work
    )
    mixed_style = ParagraphStyle(
        'MixedStyle',
        parent=styles['Normal'],
        fontName='Montserrat',  # Default font
        textColor=colors.black,
        fontSize=8,
        allowWidows=1,  # This is the magic part that allows the embedded tags to work
    )
    data = [
        [Paragraph("<b>CUENTAS BANCARIAS</b>", title_style)],
    ]
    for bank_account in company.BankAccounts:
        if not bank_account.AccountNumber and not bank_account.Clabe:
            continue
        bank_account_str = f"{bank_account.BankName}"
        if bank_account.AccountNumber:
            bank_account_str = f"{bank_account_str} | Cuenta: {bank_account.AccountNumber}"
        if bank_account.Clabe:
            bank_account_str = f"{bank_account_str} | Clabe: {bank_account.Clabe}"
        data.append([Paragraph(bank_account_str, mixed_style)])
    print('data------------------')
    print(data)
    print('data------------------')
    table = Table(data, colWidths=[get_width_for_n_columns(12)])
    style = [
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
        ('GRID', (0, 0), (-1, -1), 1, bg_color_cell_gray_scale),
        ('BACKGROUND', (0, 0), (-1, 0), bg_color_cell_primary),
    ]
    table.setStyle(TableStyle(style))
    return table


def create_table_7(direct_sale, styles):
    # Custom style that allows for embedded font changes
    mixed_style = ParagraphStyle(
        'MixedStyle',
        parent=styles['Normal'],
        fontName='Montserrat',  # Default font
        textColor=colors.black,
        fontSize=8,
    )
    datos_vendedor = f'<b>{direct_sale.Seller.Name}</b> | {direct_sale.Seller.Email}'
    phone_vendedor = f'{direct_sale.Seller.PhoneNumber}' if direct_sale.Seller.PhoneNumber else "Desconocido"
    phone_vendedor = f'Cel/Whatsapp: {phone_vendedor}'
    data = [
        [None, Paragraph(datos_vendedor, mixed_style), HRFlowable(line_width=get_width_for_n_columns(4))],
        [None, Paragraph(phone_vendedor, mixed_style), Paragraph("Nombre y firma de aceptación", styles['Normal'])]
    ]
    table = Table(data, colWidths=[get_width_for_n_columns(1), get_width_for_n_columns(7), get_width_for_n_columns(4)])  # Equally divided columns
    style = [
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('BACKGROUND', (0, 0), (0, -1), bg_color_cell_primary),
    ]
    table.setStyle(TableStyle(style))
    return table


def create_pdf(filename, direct_sale, company):
    # Create doc
    styles = getSampleStyleSheet()
    small_last_style = ParagraphStyle(
        'MixedStyle',
        parent=styles['Normal'],
        fontName='Montserrat',  # Default font
        textColor=colors.black,
        fontSize=7,
        allowWidows=1,  # This is the magic part that allows the embedded tags to work
    )
    margin = 36
    calculate_line_width(margin)
    doc = SimpleDocTemplate(filename, pagesize=letter, leftMargin=margin, rightMargin=margin, topMargin=margin)
    pdfmetrics.registerFont(TTFont('Montserrat', os.path.join(current, 'static/Montserrat-Regular.ttf'), subfontIndex=0))
    pdfmetrics.registerFont(TTFont('Montserrat-Bold', os.path.join(current, 'static/Montserrat-Bold.ttf'), subfontIndex=1))
    pdfmetrics.registerFont(TTFont('Montserrat-Italic', os.path.join(current, 'static/Montserrat-Italic.ttf'), subfontIndex=2))
    pdfmetrics.registerFont(TTFont('Montserrat-BoldItalic', os.path.join(current, 'static/Montserrat-BlackItalic.ttf'), subfontIndex=3))
    pdfmetrics.registerFontFamily(
        'Montserrat',
        normal='Montserrat',
        bold='Montserrat-Bold',
        italic='Montserrat-Italic',
        boldItalic='Montserrat-BoldItalic'
    )
    doc.title = f"Cotización folio {direct_sale.DirectSaleId}"
    doc.author = "Creative Planet"
    story = []
    table_aux = create_table_1(company, styles)
    story.append(table_aux)
    table_aux = create_table_2(direct_sale, styles)
    story.append(table_aux)
    story.append(Spacer(1, 2))
    story.append(HRFlowable(line_width=line_width))
    story.append(Spacer(1, 2))
    table_aux = create_table_3(direct_sale, styles)
    story.append(table_aux)
    story.append(Spacer(1, 2))
    story.append(HRFlowable(line_width=line_width))
    story.append(Spacer(1, 2))
    story.append(Spacer(1, 5))
    table_aux, costo_total_sin_IVA, IVA_total, IVA_amount = create_table_4(direct_sale, styles)
    story.append(Spacer(1, 5))
    story.append(table_aux)
    table_aux = create_table_5(costo_total_sin_IVA, IVA_total, IVA_amount, styles)
    story.append(table_aux)
    story.append(HRFlowable(line_width=line_width))
    story.append(Spacer(1, 10))
    transfer = Paragraph(f"Favor de realizar sus trasferencias o pagos con la siguiente referencia: <b>P{direct_sale.DirectSaleId}</b>", small_last_style)
    story.append(transfer)
    story.append(Spacer(1, 10))
    table_aux = create_table_6(company, styles)
    story.append(table_aux)
    story.append(Spacer(1, 20))
    table_aux = create_table_7(direct_sale, styles)
    story.append(table_aux)
    story.append(Spacer(1, 15))
    small_last_style = ParagraphStyle(
        'MixedStyle',
        parent=styles['Normal'],
        fontName='Montserrat',
        textColor=colors.black,
        fontSize=7,
        allowWidows=1,
        spaceBefore=0
    )
    items_bullet = [
        ListItem(Paragraph(directSale_commercialTerm.CommercialTerm.CommercialTerm, small_last_style)) for directSale_commercialTerm in direct_sale.DirectSale_CommercialTerms
    ]
    bullet_commercialTerms_list = ListFlowable(
        items_bullet,
        bulletType='bullet',
        bulletFontName='Montserrat-Bold',
        bulletFontSize=7,
        bulletColor='black'
    )
    story.append(bullet_commercialTerms_list)
    doc.build(story)


@direct_sales.route('/api/directSales/direcSale/getpdfdoc/<folio>')
# @jwt_required()
def create_direct_sale_pdf_file(folio):
    try:
        session = ScopedSession()
        # claims= get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            direct_sale = session.query(DirectSale).get(folio)
            company = session.query(Company).first()
            if not direct_sale:
                created_response = make_response(jsonify({'errores': "venta directa no existe"}), 404)
            else:
                name = "cuack.pdf"
                path_for_quotes = current_app.config['QUOTES_FOLDER']
                file_name = os.path.join(path_for_quotes, name)
                create_pdf(file_name, direct_sale, company)
                created_response = send_from_directory(path_for_quotes, name, mimetype='application/pdf')
    except Exception as e:
        session.rollback()
        traceback.print_exc()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@direct_sales.route('/api/directSales/directSale/updateInternalStatus', methods=['PUT'])
# @orders.arguments(OrdernewInternalStatusSchema)
@jwt_required()
@ownUtils.my_decorator_http_manage
def updateInternalStatus(session):  # OrdernewInternalStatusSchema
    session = ScopedSession()
    claims = get_jwt()
    role = claims.get('role')
    if not (role in ROLES):
        return make_response(jsonify({'errores': "Sin permisos necesarios para esta acción"}), 403)
    jsonActualizarEstado = request.get_json()
    direct_sale_id = jsonActualizarEstado['directSaleId']
    direct_sale = session.query(DirectSale).get(direct_sale_id)
    if not direct_sale:
        return make_response(
            jsonify({'errores': "venta directa no existe"}), 404)
    if direct_sale.DirectSaleInternalStatus.DirectSaleInternalStatus == 'En cotización':
        return make_response(jsonify({'errores': 'La unica manera de cambiar el status "En cotización" es registrar el pago'}), 409)
    new_internal_status_id = jsonActualizarEstado['newInternalStatusId']
    new_internal_status = session.query(DirectSaleInternalStatus).get(new_internal_status_id)
    print('0000000000000llllllllllllllllllllllllllllllllllllllllllllllllllllllllllll')
    print(new_internal_status_id)
    print(new_internal_status)
    print(new_internal_status.DirectSaleInternalStatus)
    print('0000000000000llllllllllllllllllllllllllllllllllllllllllllllllllllllllllll')
    if not new_internal_status:
        return make_response(jsonify({'errores': "Status interno erroneo"}), 400)
    if new_internal_status.DirectSaleInternalStatus == 'En cotización':
        return make_response(jsonify({'errores': 'No se puede cambiar una venta en el status "En cotización"'}), 409)
    direct_sale.DirectSaleInternalStatus = new_internal_status
    session.commit()
    return make_response(jsonify({'mensaje': "Actualizado con exito"}), 200)


@direct_sales.route('/api/directSales/directSale/newComment', methods=['POST'])
# @orders.arguments(OrderNewCommentsSchema)
@jwt_required()
def newComment_Order_by_ID():  # OrderNewCommentsSchema
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role not in ROLES:
            createdResponse = make_response(jsonify({
                'error':
                "No tienes permiso para agregar un comentario"}), 403)
        else:
            data = request.get_json()
            direct_sale_id = data.get('directSaleId')
            new_comment = data.get('newComment')
            direct_sale = session.query(DirectSale).get(direct_sale_id)
            if direct_sale:
                if not new_comment:
                    createdResponse = make_response(jsonify({
                        'commentInfo':
                        "Comentario vacio"}), 400)
                elif len(str(new_comment)) > 100:
                    createdResponse = make_response(jsonify({
                        'commentInfo':
                        "Excede los 100 caracteres"}), 400)
                else:
                    print('000000000000000000000000---')
                    print(direct_sale.DirectSaleStackableComments)
                    print('000000000000000000000000***')
                    rightNow = ownUtils.get_time_zone_now()
                    user = session.query(User).get(claims.get('userId'))
                    stackable_comment = DirectSaleStackableComment(new_comment, rightNow)
                    stackable_comment.User = user
                    direct_sale.DirectSaleStackableComments.append(stackable_comment)
                    session.commit()
                    createdResponse = make_response(jsonify(
                        {'DirectSale':
                         "Comentario Agreado exitosamente",
                         "commentInfo": stackable_comment.serialize()}), 200)
            else:
                createdResponse = make_response(
                    jsonify({'DirectSale': "ID no encontrada"}), 404)
    except Exception as e:
        session.rollback()
        traceback.print_exc()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado: {str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@direct_sales.route('/api/directSales/directSale/updateComment', methods=['PUT'])
# @orders.arguments(OrderUpdateCommentsSchema)
@jwt_required()
def updateComments():  # OrderUpdateCommentsSchema
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        userNow = claims.get('userId')
        data = request.get_json()
        comment_id = data.get('commentId')
        comment_info = data.get('commentInfo')
        comment = session.query(DirectSaleStackableComment).get(comment_id)
        if not (role == ADMIN_ROLE or userNow == comment.UserId):
            createdResponse = make_response(jsonify({
                'error':
                "No tienes permiso para modificar este comentario"}), 403)
        else:
            if comment:
                if not comment_info:
                    createdResponse = make_response(jsonify({
                        'commentInfo':
                        "Comentario vacio"}), 400)
                elif len(str(comment_info)) > 100:
                    createdResponse = make_response(jsonify({
                        'commentInfo':
                        "Excede los 100 caracteres"}), 400)
                else:
                    rightNow = ownUtils.get_time_zone_now()
                    comment.Comment = comment_info
                    comment.TimeStamp = rightNow
                    session.commit()
                    createdResponse = make_response(jsonify(
                        {'directSale':
                         "Comentario Actualizado exitosamente",
                         "commentInfo":
                         comment.serialize()}), 200)
            else:
                createdResponse = make_response(
                    jsonify({'comment': "ID no encontrado"}), 404)

    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado: {str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@direct_sales.route('/api/directSales/directSale/deleteComment', methods=['DELETE'])
# @orders.arguments(CommentIdSchema)
@jwt_required()
def deleteComments():  # CommentIdSchema
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if (not role == ADMIN_ROLE):
            createdResponse = make_response(jsonify({
                'error':
                "No tienes permiso para eliminar un comentario"}),
                403)
        else:
            data = request.get_json()
            commentId = data.get('commentId')
            comment = session.query(
                DirectSaleStackableComment).get(commentId)
            if comment:
                session.delete(comment)
                session.commit()
                createdResponse = make_response(
                    jsonify({'comment': "Comentario Eliminado exitosamente"}), 200)
            else:
                createdResponse = make_response(
                    jsonify({'comment': "ID no encontrada"}), 404)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado: {str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@direct_sales.route('/api/directSales/directSale/supplyProduct', methods=['POST'])
# @invoices.arguments(InternalSkuSchema)
# Authentication using jwt
@jwt_required()
def supply_product():
    """
    Endpoint that updates the number of units supplied for a direct sale product.
    """
    try:
        # Getting scopedSession for access to db info
        session = ScopedSession()
        # Getting info from jwt token
        claims = get_jwt()
        # Getting user role from jwt claims
        role = claims.get('role')
        # Checking if user is authorized
        if role == WAREHOUSE_ROLE or role == ADMIN_ROLE:
            # Getting a dict from json in body
            data = request.get_json()
            # Checking if productInDirectSaleId make a match with a ProductInDirectSale record
            productInDirectSaleId = data.get('productInDirectSaleId')
            error = ownUtils.revisarIntPositivo(productInDirectSaleId)
            if error:
                raise Exception(f"id no valido: {error}")
            productInDirectSale = session.query(ProductInDirectSale).get(int(productInDirectSaleId))
            # Uncommend next line for check if direct sale is at least closed
            # check_if_direct_sale_is_closed(session, productInDirectSale.DirectSale)
            # Getting locations from data dict
            locations = data.get('locations')
            # Updating shippedAmount for productInDirectSale and stock to product(product_store) in store linked
            error = supply_productInDirectSale_from_internal_location(locations, productInDirectSale)
            if error:
                raise Exception(error)
            # Saving changes at db
            session.commit()
            created_response = make_response(jsonify({'info': 'Productos surtidos exitosamente'}), 200)
        else:
            created_response = make_response(
                jsonify({'login': 'No tienes los permisos necesarios'}), 202)
    except Exception as e:
        traceback.print_exc()
        # Not saving any change if exception
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        # Closing scopedSession because is not going to be used anymore
        session.close()
        return created_response


@direct_sales.route('/api/directSales/directSale/supplyDirectSale', methods=['POST'])
# @invoices.arguments(InternalSkuSchema)
@jwt_required()
def supply_direct_sale():
    """
    Endpoint that updates the number of units supplied for entire direct sale products at given sale.
    """
    try:
        # Getting scopedSession for access to db info
        session = ScopedSession()
        # Getting info from jwt token
        claims = get_jwt()
        # Getting user role from jwt claims
        role = claims.get('role')
        # Checking if user is authorized
        if role == WAREHOUSE_ROLE or role == ADMIN_ROLE:
            # Getting a dict from json in body
            data = request.get_json()
            print('daaaaaaaaaaata')
            print(data)
            print('daaaaaaaaaaata')
            # Checking if directSaleId make a match with a DirectSale record
            direct_sale = ownUtils.validate_if_object_exists(session, 'directSaleId', 'dict', data, DirectSale)
            # Uncommend next line for check if direct sale is at least closed
            # check_if_direct_sale_is_closed(session, direct_sale)
            # Getting directsale items
            products_in_direct_sale = direct_sale.ProductsInDirectSale
            # Getting list of items that are going to be updated
            supplies_list = data.get('supplies')
            # Checking is supplier_list is a list
            if type(supplies_list) is not list:
                raise Exception('Supplies debe ser una lista')
            # Looping over supplies_list
            for index, supply in enumerate(supplies_list, start=1):
                # Checking if item is a dict
                if type(supply) is not dict:
                    raise Exception(f'Producto num {index} debe ser json')
                # Getting locations from data dict
                locations = supply.get('locations')
                direct_sale_id = supply.get('productInDirectSaleId')
                # Checking if item id make match with any productInDirectSale at DirectSale
                product_in_direct_sale = ownUtils.find_object_in_list(direct_sale_id, products_in_direct_sale, "Id")
                if not product_in_direct_sale:
                    raise Exception(f'Producto num {index} no existe')
                # Updating shippedAmount for productInDirectSale and stock to product(product_store) in store linked
                error = supply_productInDirectSale_from_internal_location(locations, product_in_direct_sale)
                if error:
                    raise Exception(f'Producto num {index}: {error}')
            # Saving changes at db
            session.commit()
            created_response = make_response(jsonify({'info': 'Productos surtidos exitosamente'}), 200)
        else:
            created_response = make_response(
                jsonify({'login': 'No tienes los permisos necesarios'}), 202)
    except Exception as e:
        traceback.print_exc()
        # Not saving any change if exception
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        # Closing scopedSession because is not going to be used anymore
        session.close()
        return created_response
