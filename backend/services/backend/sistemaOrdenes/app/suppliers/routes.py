from flask import request, jsonify, make_response
from flask_jwt_extended import jwt_required, get_jwt
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.app.models.Models import Supplier, SupplierStore
from sistemaOrdenes.configs import ROLES, ADMIN_ROLE
from flask_smorest import Blueprint
from .schemas import SupplierSchema, RFCSchema
from sistemaOrdenes.app.ownUtils import is_valid_email, \
    is_valid_name, is_valid_rfc

suppliers = Blueprint("Suppliers", __name__,
                      description="Operations on Suppliers")

NoActualizarValue = 'No Actualizar'


@suppliers.route('/api/suppliers/getSuppliers')
@jwt_required()
def return_todos_proveedores():
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role in ROLES:
            query = session.query(Supplier)
            todosProveedores = query.all()
            suppliersSerializados = [
                supplier.serialize(scope='basic')
                for supplier in todosProveedores]
            createdResponse = {'proveedores': suppliersSerializados}
        else:
            createdResponse = make_response(
                'No tienes permisos para observar los proveedores', 403)
        return createdResponse
    except Exception as e:
        session.rollback()
        createdResponse = make_response(str(e), 500)
    finally:
        session.close()
        return createdResponse


@suppliers.route('/api/suppliers/supplier/new', methods=['POST'])
@suppliers.arguments(SupplierSchema)
@jwt_required()
def create_supplier(SupplierSchema):
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if (role == ADMIN_ROLE):
            # creates a dictionary of the form data
            data = request.get_json()
            # gets atributtes from data
            rfc = data.get('rfc')
            if not rfc:
                raise Exception('RFC requerido')
            else:
                error = is_valid_rfc(rfc)
                if error:
                    raise Exception(error)
            rfcExist = session.query(
                Supplier).filter(Supplier.RFC == rfc).count()
            if rfcExist != 0:
                raise Exception('RFC ya existente')

            supplierName = data.get('supplierName')
            if not supplierName:
                raise Exception('supplierName requerido')
            supplierNameExist = session.query(Supplier).filter(
                Supplier.SupplierName == supplierName).count()
            if supplierNameExist != 0:
                raise Exception('supplierName ya existente')

            email = data.get('email')
            if not is_valid_email(email):
                raise Exception('Email no valido')
            emailExist = session.query(
                Supplier).filter(Supplier.Email == email).count()
            if emailExist != 0:
                raise Exception('Email de proveedor ya existente')

            salesExecutive = data.get('salesExecutive')
            if not is_valid_name(salesExecutive):
                raise Exception('Nombre no valido')
            salesExecutive = session.query(Supplier).filter(
                Supplier.SalesExecutive == salesExecutive).count()
            if salesExecutive != 0:
                raise Exception('Nombre de proveedor ya existente')

            # database ORM object
            supplier = Supplier(
                rfc,
                supplierName,
                email,
                salesExecutive
            )
            # insert product
            session.add(supplier)
            session.commit()

            createdResponse = make_response(
                'Proveedor registrado exitosamente.', 200)
        else:
            # Return 403 if user is valid user but it is not Admin
            createdResponse = make_response(
                'No tienes permisos para dar de alta un proveedor', 403)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(str(e), 500)
    finally:
        session.close()
        return createdResponse


@suppliers.route(
    '/api/suppliers/supplier/getSupplierStores/<RFC>',
    methods=['GET'])
@jwt_required()
def get_supplierStores_by_RFC(RFC):
    try:
        session = ScopedSession()
        supplier = session.query(Supplier).get(RFC)
        if supplier:
            supplierStoreStock = session.query(
                SupplierStore).filter(SupplierStore.RFC == RFC).all()
            if supplierStoreStock:
                dataSupplierStore = [row.serialize() for row in supplierStoreStock]
                createdResponse = make_response(
                    jsonify({'supplierStores': dataSupplierStore}), 200)
            else:
                createdResponse = make_response(
                    jsonify({'supplier': "sin Stores"}), 202)
        else:
            createdResponse = make_response(
                jsonify({'supplier': "RFC no encontrado"}), 404)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@suppliers.route('/api/suppliers/supplier/update', methods=['PUT'])
@suppliers.arguments(SupplierSchema)
@jwt_required()
def update_Supplier_by_RFC(SupplierSchema):
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if (role == ADMIN_ROLE):
            data = request.get_json()
            rfc = data.get('rfc')
            if not rfc:
                raise Exception('RFC requerido')
            supplier = session.query(Supplier).get(rfc)
            if supplier:
                # gets atributtes from data
                supplierName = data.get('supplierName')
                if not supplierName:
                    raise Exception('supplierName requerido')
                supplierNameExist = session.query(Supplier).filter(
                    Supplier.SupplierName == supplierName).count()
                if supplierNameExist != 0:
                    raise Exception('supplierName ya existente')

                email = data.get('email')
                if not is_valid_email(email):
                    raise Exception('Email no valido')
                emailExist = session.query(
                    Supplier).filter(Supplier.Email == email).count()
                if emailExist != 0:
                    raise Exception('Email de proveedor ya existente')

                salesExecutive = data.get('salesExecutive')
                if not is_valid_name(salesExecutive):
                    raise Exception('Nombre no valido')
                salesExecutive = session.query(Supplier).filter(
                    Supplier.SalesExecutive == salesExecutive).count()
                if salesExecutive != 0:
                    raise Exception('Nombre de proveedor ya existente')

                supplier.SupplierName = supplierName
                supplier.Email = email
                supplier.SalesExecutive = salesExecutive

                session.commit()

                createdResponse = make_response(
                    {'supplier': 'Actualizado exitosamente'}, 200)
            else:
                createdResponse = make_response(
                    jsonify({'supplier': "RFC no encontrado"}), 404)
        else:
            createdResponse = make_response(
                {'auth':
                 'No tienes permisos para actualizar a un proveedor'}, 403)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@suppliers.route('/api/suppliers/supplier/delete', methods=['DELETE'])
@suppliers.arguments(RFCSchema)
@jwt_required()
def delete_Supplier_by_RFC(RFCSchema):
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if (role == ADMIN_ROLE):
            data = request.get_json()
            rfc = data.get('rfc')
            if not rfc:
                raise Exception('RFC requerido')
            supplier = session.query(Supplier).get(rfc)
            if supplier:

                session.delete(supplier)
                session.commit()

                createdResponse = make_response(
                    {'supplier': 'Eliminado exitosamente'}, 200)
            else:
                createdResponse = make_response(
                    jsonify({'supplier': "RFC no encontrado"}), 404)
        else:
            createdResponse = make_response(
                {'auth':
                 'No tienes permisos para eliminar a un proveedor'}, 403)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error':
                     f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse
