from sistemaOrdenes.configs import ADMIN_ROLE, \
    ADMNISTRATIVE_ACCOUNTANT_ROLE, SERVICES_ROLE, ROLES, \
    WAREHOUSE_ROLE
from flask import jsonify, request, Response, make_response, abort
from flask_jwt_extended import jwt_required, get_jwt
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.app.models.Models import Order, Client, \
    ProductInOrder, OrderStackableComment, OrderInternalStatus, \
    OrderStatus, SupportedMarketplace, OrderInternalStatusType, \
    Marketplace, MarketplaceGroup, SupportedMarketplaceGroup, PublicationBase, \
    PublicationProduct, OrderStackableCommentRelevantRecords, \
    OrderStackableCommentRecord, OrderStackableComment, User,\
    OrderStackableCommentDeleted, OrderInternalStatusChange, \
    OrderInternalStatus_Order
from .orderUtils import is_valid_Order, is_valid_Required, getMarketplaceIdByName, generateXLS, \
    return_xls_orders, orders_all, orders_count, load_massive_orders
from sqlalchemy import desc, func
from flask_smorest import Blueprint
import traceback
from sistemaOrdenes.app import ownUtils

orders = Blueprint("Orders", __name__, description="Operations on Orders")


@orders.route('/api/orders/ordersFiltro')
# @orders.arguments(OrdersFiltroSchema, location="query")
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def return_Orders_Filtro(session):  # OrdersFiltroSchema
    claims = get_jwt()
    role = claims.get('role')
    offset = request.args.get('offset')
    nc = request.args.get('next')
    proveedores = request.args.get('proveedores')
    orderStatusID = request.args.get('orderStatusId')
    orderStatusInternoID = request.args.get('orderStatusInternoId')
    is_pending = request.args.get('pending')
    start_date = request.args.get('fechaInicial')
    end_date = request.args.get('fechaFinal')
    # Filtros predeterminados
    abiertosCerrados = request.args.get('abiertosCerrados')
    sellerMarketplace = request.args.get('sellerMarketplace')
    # Filtros predeterminados
    # Buscador
    search = request.args.get('search')
    pedidosFiltrados = orders_all(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace, search, offset, nc)
    if role == ADMIN_ROLE or role == ADMNISTRATIVE_ACCOUNTANT_ROLE:  # Finances
        scope = {
            'basics': ['paidAmount', 'fee', 'shipping', 'receivedAmount'],
            'client': {},
            'orderInternalStatus_Order': {
                'orderInternalStatusChange': {
                    'user': {},
                    "orderInternalStatus": {}
                }
            },
            'orderStackableComments': {
                'orderStackableCommentDeleted': {},
                'orderStackableCommentRelevantRecords': {
                    'firstCommentRecord': {
                        'user': {}
                    },
                    'lastCommentRecord': {
                        'user': {}
                    }
                }
            },
            'orderStatusChanges': {},
            'productsInOrder': {
                'basics': ['paidAmount', 'fee', 'shipping', 'receivedAmount'],
                'publicationProduct': {
                    'publicationProduct_product_stores': {
                        'poduct_store': {
                            'product': {
                                'productBase': {},
                            },
                            'store': {},
                            'locationLevelItems_product_store': {
                                'locationLevelItem': {}
                            }
                        }
                    },
                    'publicationProduct_product_supplierStores': {
                        'poduct_supplierStore': {}
                    }
                }
            },
            'guideInfo': {}
        }
    else:
        scope = {
            'client': {},
            'orderInternalStatus_Order': {
                'orderInternalStatusChange': {
                    'user': {},
                    "orderInternalStatus": {}
                }
            },
            'orderStackableComments': {
                'orderStackableCommentDeleted': {},
                'orderStackableCommentRelevantRecords': {
                    'firstCommentRecord': {
                        'user': {}
                    },
                    'lastCommentRecord': {
                        'user': {}
                    }
                }
            },
            'orderStatusChanges': {},
            'productsInOrder': {
                'publicationProduct': {
                    'publicationProduct_product_stores': {
                        'poduct_store': {
                            'product': {
                                'productBase': {},
                            },
                            'store': {},
                            'locationLevelItems_product_store': {
                                'locationLevelItem': {}
                            }
                        }
                    },
                    'publicationProduct_product_supplierStores': {
                        'poduct_supplierStore': {}
                    }
                }
            },
            'guideInfo': {}
        }
    #
    #pedidosFiltradosSerizalizados = [orderFiltrada.serialize(scope=scope) for orderFiltrada in pedidosFiltrados]
    pedididos_filtrados_serializados = []
    for pedido_filtrado in pedidosFiltrados:
        pedido_filtrado_serializado = pedido_filtrado.serialize(scope=scope)
        pedido_filtrado_serializado['orderStackableComments'] = list(filter(lambda comment: not comment['orderStackableCommentDeleted'], pedido_filtrado_serializado['orderStackableComments']))
        pedididos_filtrados_serializados.append(pedido_filtrado_serializado)
    return {'orders': pedididos_filtrados_serializados}


@orders.route('/api/orders/loadPageOrders')
# @orders.arguments(numOrdersFiltroSchema, location="query")
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def load_page_orders(session):  # numOrdersFiltroSchema
    proveedores = request.args.get('proveedores')
    orderStatusID = request.args.get('orderStatusId')
    orderStatusInternoID = request.args.get('orderStatusInternoId')
    is_pending = request.args.get('pending')
    fechaInicial = request.args.get('fechaInicial')
    fechaFinal = request.args.get('fechaFinal')
    # Filtros predeterminados
    abiertosCerrados = request.args.get('abiertosCerrados')
    sellerMarketplace = request.args.get('sellerMarketplace')
    # Filtros predeterminados
    registros = orders_count(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, fechaInicial, fechaFinal, abiertosCerrados, sellerMarketplace)
    miniInternalStatus = session.query(OrderInternalStatus)
    miniStatus = session.query(OrderStatus)
    queryMarketplaces = session.query(Marketplace)
    return make_response(jsonify(
                {'numeroRegistros': registros,
                 'orderInternalStatus': [
                     finalInternalStatus.basic_data_serialize()
                     for finalInternalStatus
                     in miniInternalStatus
                 ],
                 'orderStatus': [
                     finalStatus.basic_data_serialize()
                     for finalStatus
                     in miniStatus
                 ],
                 'marketplaces': [
                     finalMarket.serialize(scope={'supportedMarketplace': {}})
                     for finalMarket
                     in queryMarketplaces
                 ],
                 'configs': {
                     'commentMaxLength': OrderStackableCommentRecord.Comment.property.columns[0].type.length
                 }
                 }), 200)


@orders.route('/api/orders/statusOrders')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def load_status_orders(session):
    miniStatus = session.query(OrderStatus)
    return make_response(jsonify(
        {'orderStatus': [
            finalStatus.basic_data_serialize()
            for finalStatus
            in miniStatus]
         }), 200)


@orders.route('/api/orders/order/<int:orderId>', methods=['GET'])
# @jwt_required()
def return_order(orderId):
    try:
        session = ScopedSession()
        order = session.query(Order).get(orderId)
        createdResponse = order.serialize(scope='basic')
    except Exception as e:
        print(str(e))
        session.rollback()
        createdResponse = Response(404)
    finally:
        session.close()
        return createdResponse


@orders.route('/api/orders/order/comment/orderStackableCommentDeleted', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2_no_check()
def get_comments_from_order(session, claims):
    userNow = claims.get('userId')
    role = claims.get('role')
    data = ownUtils.get_info_from_json(request)
    order_stackable_comment_id = data.get('orderStackableCommentId')
    try:
        order_stackable_comment_id = int(order_stackable_comment_id)
    except Exception as e:
        abort(make_response(jsonify({'error': "Id de comentario no válido"}), 400))
    order_stackable_comment = ownUtils.validate_if_object_exists(session, 'orderStackableCommentId', 'native', order_stackable_comment_id, OrderStackableComment)
    if not (role == ADMIN_ROLE or userNow == order_stackable_comment.OrderStackableCommentRelevantRecords.FirstCommentRecord.User.UserId):
        abort(make_response(jsonify({'error': "No tienes permiso para eliminar este comentario"}), 403))
    order_stackable_comment_deleted = session.query(OrderStackableCommentDeleted).get(order_stackable_comment_id)
    if order_stackable_comment_deleted:
        abort(make_response(jsonify({'error': "Este comentario ya ha sido eliminado"}), 400))
    user = ownUtils.validate_if_object_exists(session, 'id', 'native', claims.get('userId'), User)
    order_stackable_comment_deleted = OrderStackableCommentDeleted()
    order_stackable_comment_deleted.OrderStackableComment = order_stackable_comment
    order_stackable_comment_deleted.User = user
    session.commit()
    return make_response(jsonify(
        {'message':
         "Comentario eliminadao exitosamente",
         "commentInfo":
         order_stackable_comment_deleted.serialize(scope={})}), 200)

@orders.route('/api/orders/order/comment/<int:id>', methods=['PUT'])
# @orders.arguments(OrderUpdateCommentsSchema)
@jwt_required()
@ownUtils.my_decorator_http_manage2_no_check()
def updateComments(id, session, claims):  # OrderUpdateCommentsSchema
    data = ownUtils.get_info_from_json(request)
    print('data')
    print(data)
    print('data')
    userNow = claims.get('userId')
    role = claims.get('role')
    order_stackable_comment = ownUtils.validate_if_object_exists(session, 'idCommentario', 'native', id, OrderStackableComment)
    if order_stackable_comment.OrderStackableCommentDeleted:
        abort(make_response(jsonify({'error': "No se puede modificar un comentario eliminado"}), 400))
    if not (role == ADMIN_ROLE or userNow == order_stackable_comment.OrderStackableCommentRelevantRecords.FirstCommentRecord.User.UserId):
        abort(make_response(jsonify({'error': "No tienes permiso para modificar este comentario"}), 403))
    comment = ownUtils.manage_request_field(OrderStackableCommentRecord.Comment, field_name='commentInfo', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=True)
    user = ownUtils.validate_if_object_exists(session, 'id', 'native', claims.get('userId'), User)
    new_comment_record = OrderStackableCommentRecord(
        Comment = comment,
        TimeStamp = ownUtils.get_time_zone_now()
    )
    new_comment_record.User = user
    new_comment_record.OrderStackableComment = order_stackable_comment
    order_stackable_comment.OrderStackableCommentRelevantRecords.LastCommentRecord = new_comment_record
    session.commit()
    return make_response(jsonify(
        {'message':
         "Comentario Actualizado exitosamente",
         "commentInfo":
         order_stackable_comment.serialize(scope={})}), 200)

@orders.route('/api/orders/order/comment/<int:id>/records', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def get_comments_records(id, session):
    scope = request.args.get('scope')
    if not scope:
        scope = "basic"

    if scope == 'basic':
        scope_dict = {}
    elif scope == 'orderStackableCommentRecords':
        scope_dict = {
        'orderStackableCommentRecords': {
            'user': {}
        }
    }
    else:
        abort(make_response(jsonify({'error': "scope no válido"}), 400))
    order_stackable_comment = ownUtils.validate_if_object_exists(session, 'idCommentario', 'native', id, OrderStackableComment)
    serialized_comments_records = order_stackable_comment.serialize(scope=scope_dict)
    return make_response(jsonify({'commentsRecords': serialized_comments_records}), 200)

@orders.route('/api/orders/order/<int:id>/comment', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage3(allowed_roles=ROLES)
def get_comments_from_order(id, session, role):
    order = ownUtils.validate_if_object_exists(session, 'id', 'native', id, Order)
    show_deleted_comments = request.args.get('showDeletedComments')
    print(show_deleted_comments)
    if not show_deleted_comments or show_deleted_comments in ["False","false"]:
        orderStackableComments = list(filter(lambda comment: not comment.OrderStackableCommentDeleted, order.OrderStackableComments))
    elif show_deleted_comments in ["True","true"]:
        if role != ADMIN_ROLE:
            abort(make_response(jsonify({'error': "No tienes permiso para ver comentarios eliminados"}), 403))
        orderStackableComments = order.OrderStackableComments
    else:
        abort(make_response(jsonify({'error': "showDeletedComments no válido"}), 400))
    scope = {
                'orderStackableCommentDeleted': {
                    'user': {}
                },
                'orderStackableCommentRelevantRecords': {
                    'firstCommentRecord': {
                        'user': {}
                    },
                    'lastCommentRecord': {
                        'user': {}
                    }
                }
            }
    serialized_comments = [comment.serialize(scope=scope) for comment in orderStackableComments]
    return make_response(jsonify({'comments': serialized_comments}), 200)


@orders.route('/api/orders/newOrder', methods=['POST'])
# @orders.arguments(OrderSchema)
@jwt_required()
def registerNewOrder():  # OrderSchema
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role in ROLES:
            # creates a dictionary of the form data
            data = request.get_json()
            is_valid_Order(data, 0, session)
            createdResponse = make_response('Order agregada con éxito', 200)
        else:
            createdResponse = make_response(
                'No tienes permisos para subir una nueva Order', 500)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
        print(str(createdResponse))
    finally:
        session.close()
        return createdResponse


@orders.route('/api/orders/updateOrder', methods=['PUT'])
# @orders.arguments(OrderSchema)
@jwt_required()
def registerUpdateOrder():  # OrderSchema
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role in ROLES:
            # creates a dictionary of the form data
            data = request.get_json()
            MarketplaceOrderId = is_valid_Required('MarketplaceOrderId', data)
            if session.query(Order).\
                    filter(Order.MarketplaceOrderId == MarketplaceOrderId).\
                    count() == 0:
                raise Exception(
                    "No se encontró una Order con ese MarketplaceOrderId")
            else:
                is_valid_Order(data, 1, session)
            createdResponse = make_response('Order actualizada con éxito', 200)

        else:
            createdResponse = make_response(
                'No tienes permisos para actualizar una Order', 500)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
        print(str(createdResponse))

    finally:
        session.close()
        return createdResponse


@orders.route('/api/orders/MassiveLoadOrders', methods=['POST'])
# @orders.arguments(MassiveOrdersList)
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=SERVICES_ROLE)
def LoadMultipleOrder(session):  # MassiveOrdersList
    report = {
        "inserted": 0,
        "updated": 0,
    }
    # creates a dictionary of the form data
    data = ownUtils.get_info_from_json(request)
    multiplesOrders = data.get('OrderList')
    error_orders = data.get('errorOrders')  # 'ids','details'
    marketplaceGroupId = data.get('marketplaceGroupId')
    load_massive_orders(session, multiplesOrders, error_orders, report, marketplaceGroupId)
    return make_response(jsonify(
        {'orders': "Procesadas con exito",
         'report': report,
         "errorOrders": error_orders}), 200)


@orders.route('/api/orders/existenceOrder/<MarketplaceOrderId>', methods=['GET'])
@jwt_required()
def obtener_Order_by_MarketplaceId(MarketplaceOrderId):
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role in ROLES:
            if session.query(Order).\
                    filter(Order.MarketplaceOrderId == MarketplaceOrderId).\
                    count() != 0:
                createdResponse = make_response("200", 200)
            else:
                createdResponse = make_response("404", 404)

        else:
            createdResponse = make_response(
                'No tienes permisos para buscar Order', 500)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@orders.route('/api/orders/internalStatuses/getInternalStatusTypesWithInternalStatus')
def get_internsal_status_types_with_internal_status():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            query = session.query(OrderInternalStatusType)
            order_internalStatus_types = query.all()
            order_internalStatus_types_serialized = [order_internalStatus_type.serialize_with_internal_status() for order_internalStatus_type in order_internalStatus_types]
            createdResponse = jsonify({'directSaleInternalStatuses': order_internalStatus_types_serialized})
        else:
            # Return 403 if role user is not valid
            createdResponse = make_response(
                'No tienes permisos para observar los status internos para ordenes', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@orders.route('/api/orders/internalStatuses/types/create', methods=['POST'])
def create_internal_status_type_for_orders():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON:{str(e)}')
            name = data.get('name')
            if not name:
                raise Exception('Nombre del tipo de status interno obligatorio')
            else:
                len_name = OrderInternalStatusType.OrderInternalStatusType.property.columns[0].type.length
                if len(name) > len_name:
                    raise Exception('Longitud del nombre demasiado larga')
            max_order_num = session.query(func.max(OrderInternalStatusType.OrderNum)).scalar()
            if not max_order_num:
                max_order_num = 0
            order_internal_Status_type = OrderInternalStatusType(name, max_order_num + 1)
            session.add(order_internal_Status_type)
            session.commit()
            created_response = make_response(jsonify({'mensaje': 'Tipo de Status interno para ordenes agregado'}), 201)
        else:
            created_response = make_response(
                'No tienes permisos para dar de alta', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@orders.route('/api/orders/internalStatuses/internalStatus/create', methods=['POST'])
def create_internal_status_for_orders():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON:{str(e)}')
            errores = ''
            name = data.get('name')
            if not name:
                errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, 'Nombre del status interno obligatorio (name)')
            else:
                len_name = OrderInternalStatus.OrderInternalStatus.property.columns[0].type.length
                if len(name) > len_name:
                    errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, f'La descripción del status interno debe ser menor {len_name}')
            description = data.get('description')
            if not description:
                errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, 'Descripción del status interno obligatorio (description)')
            else:
                len_description = OrderInternalStatus.Description.property.columns[0].type.length
                if len(description) > len_description:
                    errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, f'La descripción del status interno debe ser menor {len_description}')
            if errores:
                raise Exception(errores)
            id_type = data.get('id_type')
            if not id_type:
                raise Exception('id de tipo de status interno obligatorio (OrderInternalStatusType)')
            else:
                order_internal_status_type = session.query(OrderInternalStatusType).get(id_type)
                if not order_internal_status_type:
                    raise Exception('El id no coresponde a un OrderInternalStatusType válido')
            order_internal_statuses = order_internal_status_type.OrderInternalStatus
            if order_internal_statuses:
                max_order_num = max(order_internal_statuses, key=lambda obj: obj.OrderNum).OrderNum
            else:
                max_order_num = 0
            print("Maximum value:", max_order_num)
            order_internal_status = OrderInternalStatus(name, description, max_order_num + 1)
            order_internal_status.OrderInternalStatusType = order_internal_status_type
            session.add(order_internal_status)
            session.commit()
            created_response = make_response(jsonify({'mensaje': 'Status interno para cotizaciones agregado'}), 201)
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para dar de alta', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@orders.route('//api/orders/internalStatuses/types/rename', methods=['PUT'])
@jwt_required()
def update_internal_status_type_for_orders():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON:{str(e)}')
            id = data.get('id')
            if not id:
                raise Exception('El id de tipo para status interno para ordenes es obligatorio(id)')
            order_internal_status = session.query(OrderInternalStatusType).get(id)
            if not order_internal_status:
                raise Exception('El id enviado no corresponde a ningun tipo de status interno para ordenes')
            errores = ''
            if 'name' in data:
                name = data.get('name')
                if not name:
                    errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, 'Nombre del tipo de status interno obligatorio (name)')
                else:
                    len_name = OrderInternalStatusType.OrderInternalStatusType.property.columns[0].type.length
                    if len(name) > len_name:
                        errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, f'La descripción del status interno debe ser menor {len_name}')
                    else:
                        order_internal_status.OrderInternalStatusType = name
            if errores:
                raise Exception(errores)
            session.commit()
            created_response = make_response(jsonify({'mensaje': 'Status interno para ordenes renombrado'}), 200)
        else:
            created_response = make_response(
                'No tienes permisos para renombrar ', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@orders.route('/api/orders/internalStatuses/internalStatus/rename', methods=['PUT'])
def update_internal_status_for_orders():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON:{str(e)}')
            id = data.get('id')
            if not id:
                raise Exception('El id des status interno para ordenes es obligatorio(id)')
            order_internal_status = session.query(OrderInternalStatus).get(id)
            if not order_internal_status:
                raise Exception('El id enviado no corresponde a ningun status interno para ordenes')
            errores = ''
            if 'name' in data:
                name = data.get('name')
                if not name:
                    errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, 'Nombre de status interno obligatorio (name)')
                else:
                    len_name = OrderInternalStatus.OrderInternalStatus.property.columns[0].type.length
                    if len(name) > len_name:
                        errores = ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(errores, f'La descripción del status interno debe ser menor {len_name}')
                    else:
                        order_internal_status.OrderInternalStatus = name
            if errores:
                raise Exception(errores)
            session.commit()
            created_response = make_response(jsonify({'mensaje': 'Status interno para ordenes renombrado'}), 200)
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para renombrar ', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@orders.route('/api/orders/internalStatuses/types/delete', methods=['DELETE'])
def delete_internal_status_type_for_orders():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON:{str(e)}')
            id = data.get('id')
            if not id:
                raise Exception('Id de tipo para status interno para ordenes obligatorio (id)')
            order_internal_status_type = session.query(OrderInternalStatusType).get(id)
            if not order_internal_status_type:
                raise Exception('El id enviado no corresponde a ningún tipo de status interno para ordenes')
            order_num_deleted_status_type = order_internal_status_type.OrderNum
            session.delete(order_internal_status_type)
            up_order_internal_statuses_type = session.query(OrderInternalStatusType).filter(OrderInternalStatusType.OrderNum > order_num_deleted_status_type)
            print(up_order_internal_statuses_type)
            for status_aux in up_order_internal_statuses_type:
                status_aux.OrderNum = status_aux.OrderNum - 1
            session.commit()
            created_response = jsonify({'mensaje': 'Tipo de status interno eliminado exitosamente'})
        else:
            created_response = make_response(
                'No tienes permisos para eliminar tipos de status internos para las ordenes', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@orders.route('/api/orders/internalStatuses/internalStatus/delete', methods=['DELETE'])
def delete_internal_status_for_orders():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON:{str(e)}')
            id = data.get('id')
            if not id:
                raise Exception('Id de status interno para ordenes obligatorio (id)')
            order_internal_status_for_delete = session.query(OrderInternalStatus).get(id)
            if not order_internal_status_for_delete:
                raise Exception('El id enviado no corresponde a ningún status interno para ordenes')
            num_order_internal_status_for_delete = order_internal_status_for_delete.OrderNum
            order_internal_statuses = order_internal_status_for_delete.OrderInternalStatusType.OrderInternalStatus
            print(order_internal_statuses)
            for order_internal_status_aux in order_internal_statuses:
                print(order_internal_status_aux)
                if order_internal_status_aux.OrderNum > num_order_internal_status_for_delete:
                    order_internal_status_aux.OrderNum -= 1
            session.delete(order_internal_status_for_delete)
            session.commit()
            created_response = jsonify({'mensaje': 'Status interno eliminado exitosamente'})
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para eliminar status internos para las ordenes', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@orders.route('/api/orders/internalStatuses/types/reOrder', methods=['PUT'])
def reorder_internal_status_for_order():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            # -----
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON:{str(e)}')
            # -----
            new_order_to_status = data.get('new_order_to_status')
            if type(new_order_to_status) is not dict:
                raise Exception('El formato para el nuevo orden de los estatus debe ser un dict')
            print(new_order_to_status)
            num_order_values = new_order_to_status.values()
            print('0000000000000000000000')
            print(num_order_values)
            print('0000000000000000000000')
            order_Internal_status_types = session.query(OrderInternalStatusType).all()
            num_internal_statuses = len(order_Internal_status_types)
            print(num_internal_statuses)
            if not ownUtils.validar_valores_order(num_order_values, num_internal_statuses):
                raise Exception('los valores deben ser enteros, no repetirse y ser continuos hasta la cantidad de status existentes')
            num_order_keys = new_order_to_status.keys()
            num_order_keys_ints = ownUtils.check_ids_internal_status(num_order_keys, order_Internal_status_types, 'OrderInternalStatusTypeId')
            if not num_order_keys_ints:
                raise Exception('Los ids no corresponden a los guardados en el sistema')
            if not ownUtils.update_order_nums(order_Internal_status_types, new_order_to_status, 'OrderInternalStatusTypeId'):
                raise Exception('Ocurrio un error inesperado')
            session.commit()
            created_response = make_response(jsonify({'mensaje': 'Orden de los para los status internos modificados'}), 200)
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para realizar ventas', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@orders.route('/api/orders/internalStatuses/internalStatus/reOrder', methods=['PUT'])
def reorder_internal_status_types_for_order():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if role in ROLES:
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON:{str(e)}')
            new_order_to_status = data.get('new_order_to_status')
            if type(new_order_to_status) is not dict:
                raise Exception('El formato para el nuevo orden de los estatus debe ser un dict')
            internal_status_type_id = data.get('id_internal_status_type')
            if not internal_status_type_id:
                raise Exception('El id de tipo de status interno es obligatorio')
            internal_status_type = session.query(OrderInternalStatusType).get(internal_status_type_id)
            if not internal_status_type:
                raise Exception('El id de tipo de status interno no corresponde a ninguno dado de alta en el sistema')
            internal_statuses = internal_status_type.OrderInternalStatus
            if not internal_statuses:
                raise Exception('El tipo de status interno no tiene status dados de alta')
            num_internal_statuses = len(internal_statuses)
            num_order_values = new_order_to_status.values()
            if not ownUtils.validar_valores_order(num_order_values, num_internal_statuses):
                raise Exception('los valores deben ser enteros, no repetirse y ser continuos hasta la cantidad de status existentes')
            num_order_keys = new_order_to_status.keys()
            num_order_keys_ints = ownUtils.check_ids_internal_status(num_order_keys, internal_statuses, 'OrderInternalStatusId')
            if not num_order_keys_ints:
                raise Exception('Los ids no corresponden a los guardados en el sistema')
            if not ownUtils.update_order_nums(internal_statuses, new_order_to_status, 'OrderInternalStatusId'):
                raise Exception('Ocurrio un error inesperado')
            session.commit()
            created_response = make_response(jsonify({'mensaje': 'Orden de los status internos modificados'}), 200)
        else:
            # Return 403 if role user is not valid
            created_response = make_response(
                'No tienes permisos para realizar ventas', 403)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


def find_supply_for_product_in_order(productInOrder, supplies):
    supply_for_productInOrder = None
    for index, supply in enumerate(supplies, start=1):
        # Checking if item is a dict
        if type(supply) is not dict:
            raise Exception(f'Producto num {index} debe ser json')
        product_in_order_id = supply.get('productInOrderId')
        error = ownUtils.revisarIntPositivo(product_in_order_id)
        if error:
            raise Exception(f'productInOrderId para el producto num {index} debe ser int positivo: {error}')
        product_in_order_id = int(product_in_order_id)
        if productInOrder.ProductInOrderId == product_in_order_id:
            supply_for_productInOrder = supply
            supplies.remove(supply)
    if not supply_for_productInOrder:
        raise Exception(f'Todos los productos deben ser surtidos: productInOrder con id {productInOrder.ProductInOrderId} no se encuentra en supplies')
    return supply_for_productInOrder


def supply_product_from_levelItem(id_location, amount, locationLevelItems):
    location_level_item_product_store = ownUtils.find_object_in_list(id_location, locationLevelItems, "LocationLevelItemId")
    if not location_level_item_product_store:
        return 'No se encontro el producto ubicacion con este id'
    if amount > location_level_item_product_store.Stock:
        return f"No hay stock suficiente en la ubiacion: {location_level_item_product_store.LocationLevelItem.write_entire_position()}"
    location_level_item_product_store.Stock -= amount
    if amount > location_level_item_product_store.Product_Store.Stock:
        return "No hay stock suficiente en el almacen de esta ubicación"
    location_level_item_product_store.Product_Store.Stock -= amount


def supply_productInOrder_from_internal_location(locations, productInOrder):
    if not productInOrder:
        return 'El id de producto ingresado no corresponde a ningún producto vendido por marketplace'
    publicationProduct_Product_Stores = productInOrder.PublicationProduct.PublicationProduct_Product_Stores
    if not publicationProduct_Product_Stores:
        return "El producto no se encuentra relacionado a ningún almacen"
    # Get Product_Store linked to productInDirectSale object
    # product_store = publicationProduct_Product_Stores.Product_Store
    locationLevelItems = []
    for publicationProduct_Product_Store in publicationProduct_Product_Stores:
        locationLevelItems.extend(publicationProduct_Product_Store.Product_Store.LocationLevelItems_Product_Store)
    if not locationLevelItems:
        return 'El producto no se encuentra en ningún lugar de los almacen'
    # Get previously shipped units
    shipped_units = productInOrder.ShippedUnits
    # Get previously shipped units
    total_units = productInOrder.Units
    if shipped_units > 0 and shipped_units < total_units:
        return 'Se realizo una salida parcial del producto, reportar a soporte'
    if shipped_units == total_units:
        return "Este producto ya fue surtido"
    total_products = 0
    if type(locations) is not list:
        return "La llave locations(lista de donde se descontará el stock) debe ser tipo list"
    for index, location in enumerate(locations):
        if type(location) is not dict:
            return f'ubicacion num {index}: no es un json'
        amount = location.get("amount")
        if not ownUtils.revisar_int_0_o_mayor(amount):
            return f"ubicacion num {index}:La cantidad ingresada no es correcta (no es un int válido)"
        amount = int(amount)
        total_products += amount
        if total_products > total_units:
            return "los productos a surtir son mas que los reflejados en la venta"
        id_location = location.get("id")
        error = supply_product_from_levelItem(id_location, amount, locationLevelItems)
        if error:
            raise Exception(f'Producto num {index}: {error}')
    if total_products != total_units:
        return "Es necesario surtir el total de unidades para la venta"
    # Set new shipped units to sale item
    productInOrder.ShippedUnits = total_products


@orders.route('/api/order/supply', methods=['POST'])
# @invoices.arguments(InternalSkuSchema)
@jwt_required()
def supply_order():
    """
    Endpoint that updates the number of units supplied for entire direct sale products at given sale.
    """
    try:
        # Getting scopedSession for access to db info
        session = ScopedSession()
        # Getting info from jwt token
        claims = get_jwt()
        # Getting user role from jwt claims
        role = claims.get('role')
        # Checking if user is authorized
        if role == WAREHOUSE_ROLE or role == ADMIN_ROLE:
            # Getting a dict from json in body
            try:
                data = request.get_json()
            except Exception as e:
                raise Exception(
                    f'Los datos deben ser enviados en formato JSON:{str(e)}')
            # Checking if directSaleId make a match with a DirectSale record
            print('999999999999999999999999999999999999999999999999999999999999')
            print(data)
            print('999999999999999999999999999999999999999999999999999999999999')
            order = ownUtils.validate_if_object_exists(session, 'orderId', 'dict', data, Order)
            # Uncommend next line for check if direct sale is at least closed
            # check_if_direct_sale_is_closed(session, direct_sale)
            # Getting directsale items
            products_in_order = order.ProductsInOrder
            # Getting list of items that are going to be updated
            supplies_list = data.get('supplies')
            # Checking is supplier_list is a list
            if type(supplies_list) is not list:
                raise Exception('Supplies debe ser una lista')
            # Looping over supplies_list
            for product_in_order in products_in_order:
                # Checking if item id make match with any productInDirectSale at DirectSale
                supply_for_productInOrder = find_supply_for_product_in_order(product_in_order, supplies_list)
                locations = supply_for_productInOrder.get('locations')
                # Updating shippedAmount for productInDirectSale and stock to product(product_store) in store linked
                error = supply_productInOrder_from_internal_location(locations, product_in_order)
                if error:
                    raise Exception(f'En al intentar guardar productInOrder con id {product_in_order.ProductInOrderId}: {error}')
            if len(supplies_list) != 0:
                raise Exception('Existen supplies que no corresponden a la orden')
            # Saving changes at db
            session.commit()
            created_response = make_response(jsonify({'info': 'Productos surtidos exitosamente'}), 200)
        else:
            created_response = make_response(
                jsonify({'login': 'No tienes los permisos necesarios'}), 403)
    except Exception as e:
        traceback.print_exc()
        # Not saving any change if exception
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        # Closing scopedSession because is not going to be used anymore
        session.close()
        return created_response


@orders.route('/api/orders/exportXLS', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE])
def export_orders_xls(session):
    data = request.get_json()
    list_orders = data.get('list_orders')
    if not list_orders:
        raise Exception('No se recibieron ordenes')
    orders = session.query(Order).filter(Order.OrderId.in_(list_orders)).all()
    if not orders:
        raise Exception('No se encontraron ordenes con esos Ids')
    generateXLS(session, orders)
    return return_xls_orders()


@orders.route('/api/orders/exportXLSFiltro', methods=['GET'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE])
def return_exportXLS_Filtro(session):  # OrdersFiltroSchema
    limit = request.args.get('limit', 1000)
    proveedores = request.args.get('proveedores')
    orderStatusID = request.args.get('orderStatusId')
    orderStatusInternoID = request.args.get('orderStatusInternoId')
    is_pending = request.args.get('pending')
    start_date = request.args.get('fechaInicial')
    end_date = request.args.get('fechaFinal')
    # Filtros predeterminados
    abiertosCerrados = request.args.get('abiertosCerrados')
    sellerMarketplace = request.args.get('sellerMarketplace')
    # Buscador
    search = request.args.get('search')
    ###
    pedidosFiltrados = orders_all(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace, search, 0, limit)
    if not pedidosFiltrados:
        raise Exception('No se encontraron ordenes con esos filtros')
    generateXLS(session, pedidosFiltrados)
    return return_xls_orders()


###
@orders.route('/api/orders/order/newComment', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage4(allowed_roles=ROLES)
def newComment_Order_by_ID(session, claims):  # OrderNewCommentsSchema
    print('newComment_Order_by_ID')
    data = ownUtils.get_info_from_json(request)
    order = ownUtils.validate_if_object_exists(session, 'orderId', 'dict', data, Order)
    comment = ownUtils.manage_request_field(OrderStackableCommentRecord.Comment, field_name='newComment', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=True)
    rightNow = ownUtils.get_time_zone_now()
    user = ownUtils.validate_if_object_exists(session, 'id', 'native', claims.get('userId'), User)

    new_comment_record = OrderStackableCommentRecord(
        Comment = comment,
        TimeStamp = rightNow
    )
    new_comment_record.User = user

    orderStackableCommentRelevantRecords = OrderStackableCommentRelevantRecords()
    orderStackableCommentRelevantRecords.FirstCommentRecord = new_comment_record
    orderStackableCommentRelevantRecords.LastCommentRecord = new_comment_record

    orderStackableComment = OrderStackableComment()
    #orderStackableComment.OrderStackableCommentRecords = [new_comment_record, ]
    new_comment_record.OrderStackableComment = orderStackableComment
    orderStackableComment.OrderStackableCommentRelevantRecords = orderStackableCommentRelevantRecords
    orderStackableComment.Order = order
    #order.OrderStackableComments.append(orderStackableComment)
    
    session.commit()
    return make_response(jsonify(
        {'message':
         "Comentario Agreado exitosamente",
         "commentInfo": orderStackableComment.serialize(scope={})}), 201)

@orders.route('/api/orders/internalStatusChange', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage4(allowed_roles=ROLES)
def add_internal_status_change(session, claims):
    data = ownUtils.get_info_from_json(request)
    order = ownUtils.validate_if_object_exists(session, 'orderId', 'dict', data, Order)
    user = ownUtils.validate_if_object_exists(session, 'id', 'native', claims.get('userId'), User)
    order_internal_status = ownUtils.validate_if_object_exists(session, 'orderInternalStatusId', 'dict', data, OrderInternalStatus)
    order_internal_status_change = OrderInternalStatusChange(
        TimeStamp = ownUtils.get_time_zone_now()
    )
    order_internal_status_change.Order = order
    order_internal_status_change.User = user
    order_internal_status_change.OrderInternalStatus = order_internal_status

    prderInternalStatus_Order = OrderInternalStatus_Order()
    prderInternalStatus_Order.Order = order 
    prderInternalStatus_Order.OrderInternalStatusChange = order_internal_status_change

    session.commit()
    return make_response(jsonify(
        {'message': "Status interno modificado exitosamente",
         "internalStatusChangeInfo": order_internal_status_change.serialize(scope={})}), 201)


@orders.route('/api/orders/<int:orderId>/internalStatusChange')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_internal_status_changes_by_order_id(orderId, session):  # OrderInternalStatusChangeSchema
    order = ownUtils.validate_if_object_exists(session, "orderId", "native", orderId, Order)
    scope = {
        "user": {},
        "orderInternalStatus": {}
    }
    serialized_internal_status_changes = list(map(lambda internal_status_change: internal_status_change.serialize(scope=scope), order.OrderInternalStatusChanges))
    return make_response(jsonify({'internalStatusChanges': serialized_internal_status_changes}), 200)
