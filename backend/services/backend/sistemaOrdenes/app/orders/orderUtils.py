from sistemaOrdenes.app.models.Models import Order, Marketplace, \
    OrderStatus, OrderInternalStatus, FulfillmentChannel, Client, \
    ProductInOrder, OrderOperation, PublicationProduct, \
    SupportedMarketplace, PublicationBase, PublicationStatus, OrderShippingInfo, \
    MarketplaceGroup, OrderInternalStatus_Order, OrderInternalStatusChange, User
from sistemaOrdenes.app import config
from sistemaOrdenes.app import ownUtils
import os
from openpyxl import Workbook, load_workbook
from flask import send_file
from datetime import datetime, timedelta
from sqlalchemy import desc, func, cast, String, select

RUTE_STATIC = config.Config.STATIC_FOLDER
PLANTILLA = os.path.join(RUTE_STATIC, 'Plantilla_export_orders.xlsx')


def is_valid_Int(value, name):
    try:
        if not isinstance(value, int):
            value = int(value)
        return value
    except Exception:
        raise Exception(f"{name} no es de tipo numerico")


def is_valid_Float(value, name):
    try:
        if not isinstance(value, float):
            value = float(value)
        return value
    except Exception:
        raise Exception(f"{name} no es de tipo flotante")


def is_valid_Required(name, dict):
    try:
        value = dict[name]
        return value
    except Exception:
        raise Exception(f"{name} es requerido")


def if_exist_Return(name, dict):
    if name in dict:
        return dict[name]
    return ''

# mode 0 new, mode 1 update


def is_valid_Order(dataOrder, mode, session):
    add_client = True
    MarketplaceId = if_exist_Return("MarketplaceId", dataOrder)
    if MarketplaceId != '':
        MarketplaceId = is_valid_Int(MarketplaceId, 'MarketplaceId')
        if not session.\
            query(SupportedMarketplace).\
                get(MarketplaceId):
            raise Exception(
                "MarketplaceId no tiene un numero aceptado de Id")
    OrderStatusId = if_exist_Return("OrderStatusId", dataOrder)
    if OrderStatusId != '':
        OrderStatusId = is_valid_Int(OrderStatusId, 'OrderStatusId')
        if not session.\
            query(OrderStatus).\
                get(OrderStatusId):
            raise Exception(
                "OrderStatusId no es un numero aceptado existente")
    
    FulfillmentChannelId = if_exist_Return(
        "FulfillmentChannelId", dataOrder)
    if FulfillmentChannelId != '':
        FulfillmentChannelId = is_valid_Int(
            FulfillmentChannelId, 'FulfillmentChannelId')
        if session.\
                query(FulfillmentChannel).\
                filter(FulfillmentChannel.FulfillmentChannelId == FulfillmentChannelId).count() == 0:
            raise Exception(
                "FulfillmentChannelId no es un numero aceptado existente")
    CreationDate = is_valid_Required('CreationDate', dataOrder)

    MarketplaceOrderId = is_valid_Required('MarketplaceOrderId', dataOrder)
    if (len(MarketplaceOrderId) > Order.MarketplaceOrderId.property.columns[0].type.length):
        raise Exception(
            f"El MarketplaceOrderId debe tener una longitud menor a "
            f"{Order.MarketplaceOrderId.property.columns[0].type.length}"
            " de Order")

    OrderUrl = is_valid_Required('OrderUrl', dataOrder)
    if len(OrderUrl) > Order.OrderUrl.property.columns[0].type.length:
        raise Exception(
            f"El URL debe tener una longitud menor a "
            f"{Order.OrderUrl.property.columns[0].type.length} de Order")
    if not ownUtils.is_valid_url(OrderUrl):
        raise Exception("URL no valida de Order")
    if not (('mercadolibre' in OrderUrl) or ('amazon' in OrderUrl) or ('walmart' in OrderUrl) or ('claroshop' in OrderUrl)):
        raise Exception(
            "URL no es de ningún MarketPlace manejado de Order")

    PaidAmount = is_valid_Required('PaidAmount', dataOrder)
    if PaidAmount is not None:
        PaidAmount = is_valid_Float(PaidAmount, 'PaidAmount')

    Fee = is_valid_Required('Fee', dataOrder)
    if Fee is not None:
        Fee = is_valid_Float(Fee, 'Fee')

    Shipping = is_valid_Required('Shipping', dataOrder)
    if Shipping is not None:
        Shipping = is_valid_Float(Shipping, 'Shipping')

    ReceivedAmount = is_valid_Required('ReceivedAmount', dataOrder)
    if ReceivedAmount is not None:
        ReceivedAmount = is_valid_Float(ReceivedAmount, 'ReceivedAmount')

    ShippingInfoNumber = is_valid_Required('ShippingInfoNumber', dataOrder)
    MessageGuide = is_valid_Required('MessageGuide', dataOrder)
    StatusGuide = is_valid_Required('StatusGuide', dataOrder)

    Client = is_valid_Required('Client', dataOrder)
    Client = is_valid_Client(Client)

    exist, ClientTemporal = if_exist_Client(Client, MarketplaceId, session)

    if exist:
        redefine_Client(Client, ClientTemporal)
        add_client = False

    if mode == 0:

        orderObj = Order(OrderStatusId,
                         FulfillmentChannelId, CreationDate,
                         MarketplaceOrderId, OrderUrl, PaidAmount,
                         Fee, Shipping, ReceivedAmount, [],
                         [], Client)
        orderShipping = OrderShippingInfo(ShippingInfoNumber, StatusGuide, MessageGuide)
        orderShipping.Order = orderObj
        ProductsInOrder = is_valid_Required('ProductsInOrder', dataOrder)
        ProductsInOrder, Publications = is_valid_ProductsInOrder(
            ProductsInOrder, MarketplaceId, session, orderObj)

        OperationIds = if_exist_Return("OperationIds", dataOrder)
        if OperationIds:
            OperationIds = is_valid_OperationsIds(OperationIds)
            orderObj.OrderOperations = OperationIds
        ###############################################################   
        user = session.query(User).filter(User.Email == "<EMAIL>").first()
        order_internal_status = session.query(OrderInternalStatus).filter(OrderInternalStatus.OrderInternalStatus == "No revisado").first()
        order_internal_status_change = OrderInternalStatusChange(
            TimeStamp = ownUtils.get_time_zone_now()
        )
        order_internal_status_change.Order = orderObj
        order_internal_status_change.User = user
        order_internal_status_change.OrderInternalStatus = order_internal_status

        prderInternalStatus_Order = OrderInternalStatus_Order()
        prderInternalStatus_Order.Order = orderObj 
        prderInternalStatus_Order.OrderInternalStatusChange = order_internal_status_change
        ###############################################################
        session.add(orderObj)
        return orderObj
    elif mode == 1:
        OrderTemporal = session.query(Order).filter(
            Order.MarketplaceOrderId == MarketplaceOrderId).first()
        
        statusReturn = session.query(OrderStatus.OrderStatusId).filter(OrderStatus.OrderStatus == 'En Devolución').first()
        statusRefund = session.query(OrderStatus.OrderStatusId).filter(OrderStatus.OrderStatus == 'Reembolsado').first()
        oldOrderShipping = session.query(OrderShippingInfo).filter(OrderShippingInfo.OrderId == OrderTemporal.OrderId).first()
        orderShipping = OrderShippingInfo(ShippingInfoNumber, StatusGuide, MessageGuide)

        redefine_ShippingInfo(orderShipping, oldOrderShipping)

        listStatusNotif = [statusReturn, statusRefund]

        redefine_Order(dataOrder, OrderTemporal, listStatusNotif)
        Products = session.query(ProductInOrder).filter(
            ProductInOrder.OrderId == OrderTemporal.OrderId).all()
        for miniProduct in Products:
            session.delete(miniProduct)

        ProductsInOrder = is_valid_Required('ProductsInOrder', dataOrder)
        ProductsInOrder, Publications = is_valid_ProductsInOrder(
            ProductsInOrder, MarketplaceId, session, OrderTemporal)
        for miniPubli in Publications:
            session.add(miniPubli)
        for miniProduct in ProductsInOrder:
            session.add(miniProduct)
        OrderTemporal.ProductsInOrder = ProductsInOrder
        return OrderTemporal

def is_valid_OperationsIds(dataOperationIds):
    if len(dataOperationIds) > 0:
        listIds = []
        for ids in dataOperationIds:
            miniId = OrderOperation(ids)
            listIds.append(miniId)
    return listIds


def is_valid_Client(dataClient):
    try:
        MarketplaceClientId = if_exist_Return(
            "MarketplaceClientId", dataClient)
        if MarketplaceClientId != '':
            MarketplaceClientId = is_valid_Int(
                MarketplaceClientId, 'MarketplaceClientId')

        RegistrationDate = if_exist_Return("RegistrationDate", dataClient)

        City = if_exist_Return("City", dataClient)

        State = if_exist_Return("State", dataClient)

        Nickname = if_exist_Return("Nickname", dataClient)

        Name = if_exist_Return("Name", dataClient)

        Score = if_exist_Return("Score", dataClient)

        if Score != '':
            Score = is_valid_Int(Score, 'Score')

        ZipCode = if_exist_Return("ZipCode", dataClient)

        PhoneNumber = if_exist_Return("PhoneNumber", dataClient)

        Email = if_exist_Return("Email", dataClient)
        if Email != '':
            if not ownUtils.is_valid_email(Email):
                raise Exception("Email del Cliente no valido")

        Address = if_exist_Return("Address", dataClient)

        miniClient = Client(str(MarketplaceClientId),
                            RegistrationDate, City,
                            State, Nickname, Name, Score,
                            ZipCode, PhoneNumber, Email, Address, [])
        return miniClient
    except Exception as e:
        raise Exception(str(e))


def is_valid_ProductsInOrder(dataProducts, MarketplaceId, session, orderObj):
    try:
        productsList = []
        publicationList = []
        if len(dataProducts) > 0:

            for product in dataProducts:
                sellerSku = is_valid_Required('seller_sku', product)
                Title = is_valid_Required('Title', product)

                Brand = is_valid_Required('Brand', product)

                Model = is_valid_Required('Model', product)

                Units = is_valid_Required('Units', product)
                Units = is_valid_Int(Units, 'Units')

                Photo = if_exist_Return("Photo", product)

                OperationId = if_exist_Return("OperationId", product)

                PaidAmount = is_valid_Required('PaidAmount', product)
                if PaidAmount is not None:
                    PaidAmount = is_valid_Float(PaidAmount, 'PaidAmount')

                Fee = is_valid_Required('Fee', product)
                if Fee is not None:
                    Fee = is_valid_Float(Fee, 'Fee')

                Shipping = is_valid_Required('Shipping', product)
                if Shipping is not None:
                    Shipping = is_valid_Float(Shipping, 'Shipping')

                ReceivedAmount = is_valid_Required('ReceivedAmount', product)
                if ReceivedAmount is not None:
                    ReceivedAmount = is_valid_Float(
                        ReceivedAmount, 'ReceivedAmount')

                ProductVariations = if_exist_Return(
                    'ProductVariations', product)
                ProductVariations = is_valid_ProductsInOrderVariations(
                    ProductVariations, sellerSku)

                productObj = ProductInOrder(Title, Brand, Model,
                                            Units, Photo, OperationId,
                                            PaidAmount, Fee, Shipping,
                                            ReceivedAmount)
                productObj.ShippedUnits = 0
                productObj.Order = orderObj

                publicationBaseObj, existPubli, publicationProductObj = is_valid_Publication(
                    product, MarketplaceId, session, ProductVariations, sellerSku)
                if not existPubli:
                    publicationList.append(publicationBaseObj)

                productObj.PublicationProduct = publicationProductObj
                # print("$$$$$$$$$$$$$$")
                # print(productObj)

                productsList.append(productObj)

        return productsList, publicationList

    except Exception as e:
        print(str(e))
        print("ssssssweeeeeeeeeeeeeeweeeeeeeeeeeeewewe")
        raise Exception(str(e))


def is_valid_Publication(product, MarketplaceId, session, variation, sellerSku):
    try:
        exist = False
        publicationObj = None
        publicationVariation = None
        MarkeplacePublicationId = is_valid_Required('publicationId', product)
        Stock = is_valid_Required('publicationStock', product)
        Price = is_valid_Required('publicationPrice', product)
        BoardingTime = is_valid_Required('publicationTime', product)
        PublicationStatus = is_valid_Required('publicationStatus', product)
        PublicationStatus = define_PublicationStatus(PublicationStatus, session)
        if not Price:
            Price = 0
        if session.query(PublicationBase).filter(PublicationBase.MarkeplacePublicationId == str(MarkeplacePublicationId)).count() == 0:
            # print("//////////////////////7")
            marketplaceObj = session.query(Marketplace).get(MarketplaceId)
            publicationObj = PublicationBase(MarkeplacePublicationId,
                                             Stock, Price, BoardingTime)
            publicationObj.PublicationStatus = PublicationStatus
            publicationObj.Marketplace = marketplaceObj

            exist = False

            if variation is not None:
                variation.PublicationBase = publicationObj
                return publicationObj, exist, variation
            else:
                auxPublicVariation = PublicationProduct(Stock, Price,
                                                        MarkeplacePublicationId, sellerSku)
                auxPublicVariation.PublicationBase = publicationObj
                return publicationObj, exist, auxPublicVariation

        else:
            exist = True
            publicationObj = session.query(PublicationBase).filter(PublicationBase.MarkeplacePublicationId == str(MarkeplacePublicationId)).first()
            # publicationObj.Stock = Stock
            publicationObj.Price = Price
            publicationObj.BoardingTime = BoardingTime
            publicationObj.PublicationStatus = PublicationStatus
            if variation is not None:
                if session.query(PublicationProduct).filter(
                    PublicationProduct.PublicationBaseId == (publicationObj.Id),
                        PublicationProduct.MarketplacePublicationVariationId == str(variation.MarketplacePublicationVariationId)).count() > 0:
                    auxPublicVariation = session.query(PublicationProduct).filter(
                        PublicationProduct.PublicationBaseId == (publicationObj.Id),
                        PublicationProduct.MarketplacePublicationVariationId == str(variation.MarketplacePublicationVariationId)).first()
                    auxPublicVariation.Stock = variation.Stock
                    auxPublicVariation.Cost = variation.Cost
                    auxPublicVariation.SkuMarketplaceVariation = sellerSku
                    auxPublicVariation.PublicationBase = publicationObj
                else:
                    variation.PublicationBase = publicationObj
                    return publicationObj, exist, variation

            else:
                auxPublicVariation = session.query(PublicationProduct).filter(
                    PublicationProduct.PublicationBaseId == (publicationObj.Id)).first()
                auxPublicVariation.Stock = Stock
                auxPublicVariation.Cost = Price
                auxPublicVariation.SkuMarketplaceVariation = sellerSku
                auxPublicVariation.PublicationBase = publicationObj

            return publicationObj, exist, auxPublicVariation

    except Exception as e:
        raise Exception(str(e))


def define_PublicationStatus(Status, session):
    statusobj = None
    if Status == "activo" or Status == "active" or Status == "PUBLISHED":
        statusobj = session.query(PublicationStatus).get(1)
    elif Status == "paused" or Status == "PAUSED":
        statusobj = session.query(PublicationStatus).get(2)
    else:
        statusobj = session.query(PublicationStatus).get(3)

    return statusobj


def is_valid_ProductsInOrderVariations(dataVariations, sellerSku):
    try:
        if dataVariations is not None:
            if isinstance(dataVariations, dict):
                Id = is_valid_Required('variation_id', dataVariations)
                Price = is_valid_Required('price', dataVariations)
                Stock = is_valid_Required('stock', dataVariations)
                variationObj = PublicationProduct(
                    Stock, Price, Id, sellerSku)
                return variationObj
            else:
                raise Exception(
                    "Las Variaciones de Producto no son un Diccionario")
        else:
            return None
    except Exception as e:
        raise Exception(str(e))


def if_exist_Client(dataClient, MarketplaceId, session):
    try:
        exist = False
        clientObj = None
        miniMarketplace = session.query(Marketplace).filter(Marketplace.Id == MarketplaceId).first()
        miniNameMarketplace = miniMarketplace.SupportedMarketplace.Name
        if miniNameMarketplace == "Amazon":
            if session.query(Client).\
                filter(Client.City == dataClient.City,
                       Client.State == dataClient.State).count() != 0:
                clientObj = session.query(Client).\
                    filter(Client.Name == dataClient.Name,
                           Client.ZipCode == dataClient.ZipCode).first()
                exist = True

        elif miniNameMarketplace == "Claro Shop":
            if session.query(Client).\
                filter(Client.Name == dataClient.Name,
                       Client.ZipCode == dataClient.ZipCode).count() != 0:
                clientObj = session.query(Client).\
                    filter(Client.Name == dataClient.Name,
                           Client.ZipCode == dataClient.ZipCode).first()
                exist = True

        elif miniNameMarketplace == "Mercado Libre":
            if session.query(Client).\
                filter(
                    Client.MarketplaceClientId == dataClient.MarketplaceClientId).count() != 0:
                clientObj = session.query(Client).\
                    filter(
                        Client.MarketplaceClientId == dataClient.MarketplaceClientId).first()
                exist = True

        elif miniNameMarketplace == "Walmart":
            if session.query(Client).\
                    filter(Client.Email == dataClient.Email).count() != 0:
                clientObj = session.query(Client).\
                    filter(Client.Email == dataClient.Email).first()
                exist = True
        else:
            clientObj = None

        return exist, clientObj
    except Exception as e:
        raise Exception(str(e))


def redefine_Client(dataClient, oldClient):
    oldClient.City = dataClient.City
    oldClient.State = dataClient.State
    oldClient.Nickname = dataClient.Nickname
    oldClient.Score = dataClient.Score
    oldClient.ZipCode = dataClient.ZipCode
    oldClient.PhoneNumber = dataClient.PhoneNumber
    oldClient.Email = dataClient.Email


def redefine_ShippingInfo(dataShippingInfo, oldShippingInfo):
    oldShippingInfo.ShippingNumber = dataShippingInfo.ShippingNumber
    oldShippingInfo.Message = dataShippingInfo.Message
    oldShippingInfo.Status = dataShippingInfo.Status


def redefine_Order(dataOrder, oldOrder, listStatusNotif):
    if not (oldOrder.OrderStatusId in listStatusNotif):
        oldOrder.OrderStatusId = dataOrder['OrderStatusId']
    oldOrder.FulfillmentChannelId = dataOrder['FulfillmentChannelId']
    oldOrder.CreationDate = dataOrder['CreationDate']
    oldOrder.OrderUrl = dataOrder['OrderUrl']
    oldOrder.PaidAmount = dataOrder['PaidAmount']
    oldOrder.Fee = dataOrder['Fee']
    oldOrder.Shipping = dataOrder['Shipping']
    oldOrder.ReceivedAmount = dataOrder['ReceivedAmount']
    oldOrder.ShippingInfoNumber = dataOrder['ShippingInfoNumber']


def getMarketplaceIdByName(session, name):
    allMarketplaces = session.query(Marketplace)
    marketPlaces = [miniMarket.serialize(scope={'supportedMarketplace': {}, 'marketplaceCredentials': {}}) for miniMarket in allMarketplaces]
    for miniMarket in marketPlaces:
        if miniMarket["supportedMarketplace"]["supportedMarketplaceName"] == name:
            marketplaceId = miniMarket["marketplaceId"]
    return marketplaceId


def generateXLS(session, data):
    file_path = PLANTILLA
    # file_path = os.path.join(os.path.dirname(__file__), 'Plantilla_export_orders.xlsx')

    workbook = load_workbook(file_path)
    sheet = workbook['Orders']

    fullfilmentChannel = session.query(FulfillmentChannel).all()
    statusExternals = session.query(OrderStatus).all()

    for row in sheet.iter_rows(min_row=2):
        for cell in row:
            cell.value = None

    finaldata = []
    for miniOrder in data:
        allComments = [miniComment.OrderStackableCommentRelevantRecords.LastCommentRecord.Comment for miniComment in miniOrder.OrderStackableComments]
        allComments = " --- ".join(allComments)
        
        for miniProduct in miniOrder.ProductsInOrder:
            date = miniOrder.CreationDate.strftime("%Y-%m-%d")
            time = miniOrder.CreationDate.strftime("%H:%M:%S")
            miniFila = []
            miniFila.append(miniOrder.OrderId)
            miniFila.append(miniProduct.PublicationProduct.PublicationBase.Marketplace.SupportedMarketplace.Name)
            miniFila.append(fullfilmentChannel[miniOrder.FulfillmentChannelId - 1].FulfillmentChannel)
            miniFila.append(miniOrder.MarketplaceOrderId)
            miniFila.append(miniOrder.Cliente.Name)
            miniFila.append(miniOrder.CreationDate.strftime(date))
            miniFila.append(miniOrder.CreationDate.strftime(time))
            miniFila.append(miniProduct.PublicationProduct.SkuMarketplaceVariation)
            miniFila.append(miniProduct.Model)
            miniFila.append(miniProduct.Brand)
            miniFila.append(miniProduct.Title)
            miniFila.append(miniProduct.Units)
            miniFila.append(miniProduct.PaidAmount)
            miniFila.append(miniProduct.Fee)
            miniFila.append(miniProduct.Shipping)
            miniFila.append(miniProduct.ReceivedAmount)
            
            miniFila.append(statusExternals[miniOrder.OrderStatusId - 1].OrderStatus)
            miniFila.append(allComments)

            finaldata.append(miniFila)
    start_row = 2
    for i, row_data in enumerate(finaldata, start=start_row):
        for j, value in enumerate(row_data, start=1):
            sheet.cell(row=i, column=j, value=value)

    workbook.save(file_path)


def return_xls_orders():
    try:
        ruta_plantilla = PLANTILLA
        mimetypee = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        return send_file(ruta_plantilla, mimetype=mimetypee, as_attachment=True)
    except Exception as e:
        raise Exception(str(e))


def getStatusAbiertosCerrados(abiertosCerrados, session):
    # Conseguimos los Ids de los estatus
    listStatusIds = session.query(OrderStatus).all()
    listAbiertos = []
    listCerrados = []
    # Los guardamos en la lista de acuerdo a su nombre
    for miniStatus in listStatusIds:
        if miniStatus.OrderStatus == "Pendiente de pago":
            listCerrados.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Esperando stock":
            listAbiertos.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Pendiente de envío":
            listAbiertos.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Enviado":
            listAbiertos.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Entregado":
            listCerrados.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Cancelado no procesado":
            listCerrados.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Cancelado ya procesado":
            listCerrados.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Error en el pedido":
            listCerrados.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Inconsistencia de datos":
            listCerrados.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Reembolsado":
            listCerrados.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Rechazado":
            listCerrados.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Contracargo":
            listCerrados.append(miniStatus.OrderStatusId)
        elif miniStatus.OrderStatus == "Entregado sin posiblidad de cambios":
            listAbiertos.append(miniStatus.OrderStatusId)
    # Regresamos la lista dependiendo si son estatus abiertos o cerrados
    if abiertosCerrados == "Abiertos":
        return listAbiertos
    else:
        return listCerrados


def orders_count(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace):
    query = no_search_query(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace)
    print('query.count()-------------')
    print(query.count())
    print('query.count()--------------------')
    count = query.count()
    return count


def orders_all(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace, search, offset, nc):
    if search is not None and search != '':
        query = search_query(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace, search)
        query = query.order_by(desc("greatest"))
        query = order_query(query, offset, nc)
        ordenes_filtradas = query.all()
        print('query.count()')
        print(query.count())
        print('query.count()')
        print('query.all()')
        print(len(ordenes_filtradas))
        print('query.all()')
        ordenes_filtradas = map(lambda order: order.Order, ordenes_filtradas)
    else:
        query = no_search_query(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace)
        query = order_query(query, offset, nc)
        ordenes_filtradas = query.all()
        print('query.count()')
        print(query.count())
        print('query.count()')
        print('query.all()')
        print(len(ordenes_filtradas))
        print('query.all()')
    return ordenes_filtradas


def search_query_core(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace, search, columns_to_get=(Order,)):
    query = select(*columns_to_get, func.greatest(func.similarity(Order.MarketplaceOrderId, search),
                                                  func.similarity(cast(Order.OrderId, String), search),
                                                  func.similarity(ProductInOrder.Title, search),
                                                  func.similarity(ProductInOrder.Model, search),
                                                  func.similarity(ProductInOrder.Brand, search),
                                                  func.similarity(Client.Name, search)).label('greatest'))
    query, products_in_order_joined = armar_query_filtros_orders(query, session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace)
    if not products_in_order_joined:
        query = query.join(ProductInOrder,
                           Order.OrderId == ProductInOrder.OrderId)
    query = query.join(Client, Order.ClientId == Client.ClientId)
    return query


def search_query(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace, search, columns_to_get=(Order,)):
    query = session.query(*columns_to_get, func.greatest(func.similarity(Order.MarketplaceOrderId, search),
                                                         func.similarity(cast(Order.OrderId, String), search),
                                                         func.similarity(ProductInOrder.Title, search),
                                                         func.similarity(ProductInOrder.Model, search),
                                                         func.similarity(ProductInOrder.Brand, search),
                                                         func.similarity(Client.Name, search)).label('greatest'))
    query, products_in_order_joined = armar_query_filtros_orders(query, session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace)
    if not products_in_order_joined:
        query = query.join(ProductInOrder,
                           Order.OrderId == ProductInOrder.OrderId)
    query = query.join(Client, Order.ClientId == Client.ClientId)
    query = query.distinct()
    return query


def no_search_query(session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace, columns_to_get=(Order,)):
    query = session.query(*columns_to_get)
    query, _ = armar_query_filtros_orders(query, session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace)
    query = query.distinct()
    return query


def armar_query_filtros_orders(query, session, proveedores, orderStatusID, orderStatusInternoID, is_pending, start_date, end_date, abiertosCerrados, sellerMarketplace):
    # filtros_predeterminados
    if abiertosCerrados:
        listStatus = getStatusAbiertosCerrados(abiertosCerrados, session)
        query = query.filter(
            Order.OrderStatusId.in_(listStatus))
    # ----------
    if sellerMarketplace:
        sellerMarketplaceRequest = sellerMarketplace
        query = query.\
            filter(Order.FulfillmentChannelId == sellerMarketplaceRequest)
    # filtros_predeterminados

    if orderStatusID:
        statusRequest = tuple(orderStatusID.split('-'))
        query = query.filter(Order.OrderStatusId.in_(statusRequest))

    if orderStatusInternoID:
        internalStatusRequest = tuple(orderStatusInternoID.split('-'))
        query = query.join(OrderInternalStatus_Order, Order.OrderId == OrderInternalStatus_Order.OrderId).join(OrderInternalStatusChange, OrderInternalStatus_Order.OrderInternalStatusChangeId == OrderInternalStatusChange.Id).filter(OrderInternalStatusChange.OrderInternalStatusId.in_(internalStatusRequest))
    if is_pending:
        is_pending = is_pending.lstrip().lower()
        if (is_pending != "true" and is_pending != "false"):
            raise Exception('pending no es válido')
        query = query.filter(Order.PendingToResponse == is_pending)

    date_format = "%Y-%m-%d"
    print('00000000000000000000000000000>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>')
    print(start_date)
    print(end_date)
    print('00000000000000000000000000000>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>')
    if start_date and end_date:
        start_date = datetime.strptime(start_date, date_format)
        end_date = datetime.strptime(
            end_date, date_format) + timedelta(days=1)
        query = query.filter(
            Order.CreationDate.between(start_date, end_date))

    if proveedores:
        marketplacesRequest = tuple(proveedores.split('-'))
        query = query.join(ProductInOrder, Order.OrderId == ProductInOrder.OrderId).join(PublicationProduct, ProductInOrder.PublicationProductId == PublicationProduct.Id).join(PublicationBase, PublicationProduct.PublicationBaseId == PublicationBase.Id).join(Marketplace, PublicationBase.MarketplaceId == Marketplace.Id).filter(Marketplace.Id.in_(marketplacesRequest))
        products_in_order_joined = True
    else:
        products_in_order_joined = False
    return query, products_in_order_joined


def order_query(query, offset, nc):
    if ((offset is not None and offset != "") and (nc is not None and nc != "")):
        query = query.order_by(
            desc(Order.CreationDate)).offset(offset).limit(nc)
    else:
        query = query.order_by(
            desc(Order.CreationDate)).offset(0).limit(30)
    return query


def load_massive_orders(session, multiplesOrders, error_orders, report, marketplaceGroupId):
    if type(multiplesOrders) is not list:
        raise Exception('OrderList debe ser una lista')
    for index, miniOrder in enumerate(multiplesOrders):
        try:
            MarketplaceOrderId = is_valid_Required(
                'MarketplaceOrderId', miniOrder)
            exist = session.query(Order).filter(
                Order.MarketplaceOrderId == MarketplaceOrderId).count()
            if exist == 0:
                try:
                    is_valid_Order(miniOrder, 0, session)
                    report['inserted'] += 1
                except Exception as e:
                    print('000000000000000000000000000000000RRRRRRRRRRRRRRRRR1')
                    print(str(e))
                    print('000000000000000000000000000000000RRRRRRRRRRRRRRRRR1')
                    error_orders['ids'].append(MarketplaceOrderId)
                    error_orders['details'].append(f'{MarketplaceOrderId} => {str(e)}')
            elif exist > 0:
                try:
                    # if indets % 4 == 0: # for test
                    #    raise Exception('cuat')
                    is_valid_Order(miniOrder, 1, session)
                    report['updated'] += 1
                except Exception as e:
                    print('000000000000000000000000000000000RRRRRRRRRRRRRRRRR2')
                    print(str(e))
                    print('000000000000000000000000000000000RRRRRRRRRRRRRRRRR2')
                    error_orders['ids'].append(MarketplaceOrderId)
                    error_orders['details'].append(f'{MarketplaceOrderId} => {str(e)}')
            else:
                error_orders['ids'].append(MarketplaceOrderId)
                error_orders['details'].append(f'{MarketplaceOrderId} => Orden existe mas de una vez')
        except Exception as e:
            error_orders['ids'].append(f'desconocido({index})')
            error_orders['details'].append(f"desconocido({index}) => {str(e)}")
    hora_actual = ownUtils.get_time_zone_now()
    marketplaceGroupToUpdate = ownUtils.validate_if_object_exists(session, "marketplaceGroupId", "native", marketplaceGroupId, MarketplaceGroup)
    marketplaceGroupToUpdate.LastUpdate = hora_actual

    if not error_orders['ids']:
        comment_update = f'Actualización exitosa: {report["inserted"]} nuevas, {report["updated"]} actualizadas'
        marketplaceGroupToUpdate.CommentUpdate = comment_update[0:499]
    session.commit()