from flask import jsonify, request, make_response, \
    send_file, current_app, abort
from flask_jwt_extended import jwt_required
from flask_jwt_extended import get_jwt
from flask_smorest import Blueprint
import traceback
import os
from sistemaOrdenes.configs import ADMIN_ROLE, SERVICES_ROLE, WAREHOUSE_ROLE, LOGISTICS_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE, ROLES
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.app.models.Models import Marketplace, SupportedMarketplace, SupportedMarketplaceGroup, MarketplaceGroup, PublicationBase, MarketplaceGroupCredentials
from sistemaOrdenes.app import ownUtils
from .marketplaceUtils import validate_credentials_by_marketplace, set_group_credentials, set_group_credentials_patch, get_marketplace_credentials, get_marketplace_credentials_by_id
from typing import Tuple

marketplaces = Blueprint("Marketplaces", __name__,
                         description="Operations on Marketplaces")


@marketplaces.route('/api/marketplaces/supportedMarketplaceGroup')
@jwt_required()
@ownUtils.my_decorator_http_manage2_no_check()
def return_supported_marketplaces_by_turtle(session, claims):
    role = claims.get('role')
    scope = request.args.get('scope')
    if not scope:
        scope = "basic"
    if scope == 'basic':
        scope_dict = {}
    elif scope == 'marketplacegroupcredentials_supportedmarketplace':
        if role != ADMIN_ROLE:
            return make_response(jsonify({'errores': 'No tienes permisos para registrar un marketplace'}), 403)
        scope_dict = {
            'supportedMarketplaces': {},
            'marketplaceGroup': {
                'marketplaceGroupCredentials': {},
            },
        }
    else:
        return make_response(jsonify({'error': "scope no válido"}), 400)
    query = session.query(SupportedMarketplaceGroup)
    marketplaces_group_list = query.all()
    marketplaces_group_serialized = [marketplace.serialize(scope=scope_dict) for marketplace in marketplaces_group_list]
    if scope == 'marketplacegroupcredentials_supportedmarketplace':
        return_status_check_credentials_support(marketplaces_group_serialized)
    created_response = {'supportedMarketplaceGroups': marketplaces_group_serialized}
    return created_response


def return_status_check_credentials_support(marketplaces_serialized: dict) -> None:
    print('return_status_check_credentials_support------------------')
    print(marketplaces_serialized)
    print('return_status_check_credentials_support------------------')
    for miniMarket in marketplaces_serialized:
        if not miniMarket["marketplaceGroup"]:
            continue
        if miniMarket["marketplaceGroup"]["marketplaceGroupCredentials"]:
            result = validate_credentials_by_marketplace(miniMarket["name"],
                                                         miniMarket["marketplaceGroup"]["marketplaceGroupCredentials"])            
            miniMarket["marketplaceGroup"]["marketplaceGroupCredentials"] = {
            "status": "Credenciales correctas" if not result else "Credenciales incorrectas",
            "check": True if not result else False,
            "clientId":  miniMarket["marketplaceGroup"]["marketplaceGroupCredentials"]["clientId"]
        }
        else:
            miniMarket["marketplaceGroup"]["marketplaceGroupCredentials"] = {
                "status": "No sincronizado",
                "check": False
            }


@marketplaces.route('/api/marketplaces/getImage/<nombreImagen>')
# @jwt_required()
@ownUtils.my_decorator_http_manage
def return_marketplace_image(nombreImagen, session):
    # claims = get_jwt()
    # role = claims.get('role')
    role = ADMIN_ROLE
    if role in ROLES:
        try:
            rutaArchivoConDiagonal = ownUtils.ownUnquote(nombreImagen)
            path = os.path.join(current_app.config['MARKETPLACE_IMAGE_FOLDER'], rutaArchivoConDiagonal)
            created_response = send_file(path)
        except Exception as e:
            print(str(e))
            path = os.path.join(current_app.config['MARKETPLACE_IMAGE_FOLDER'], "_defaultPhoto", "DefaultMarketplace.jpg")
            created_response = send_file(path)
    else:
        created_response = make_response(
            jsonify({'errores':
                     'No tienes permisos acceder a esta información'}), 403)
    return created_response


@marketplaces.route('/api/marketplaces/marketplaceGroup')
# @products.arguments(ProductsFiltroSchema, location="query")
@jwt_required()
@ownUtils.my_decorator_http_manage3(allowed_roles=[ADMIN_ROLE, SERVICES_ROLE])
def return_marketplacesGroups(session, role):
    scope = request.args.get('scope')
    marketplaces_serialized = get_marketplace_credentials(session, scope, role)
    return {'marketplaceGroups': marketplaces_serialized}


@marketplaces.route('/api/marketplaces/marketplaceGroup/<id>')
# @products.arguments(ProductsFiltroSchema, location="query")
@jwt_required()
@ownUtils.my_decorator_http_manage3(allowed_roles=[ADMIN_ROLE, SERVICES_ROLE])
def return_marketplacesGroups(session, role: str, id: int):
    scope = request.args.get('scope')
    marketplacaGroup_serialized = get_marketplace_credentials_by_id(session, scope, role, id)
    return marketplacaGroup_serialized


@marketplaces.route('/api/marketplaces/registerErrorInOrdersUpdater', methods=['POST'])
@ownUtils.my_decorator_http_manage2(allowed_roles=SERVICES_ROLE)
def register_error_in_orders_updater(session):
    error_data = request.get_json()
    error = error_data.get('errorMessage')
    marketplace_group__id = error_data.get('marketplaceId')
    register_error_in_orders_updater(session, marketplace_group__id, error)
    return make_response(jsonify({'mensaje': 'Error registrado exitosamente'}), 200)



@marketplaces.route('/api/marketplaces/sincronizeMarketplaceGroup', methods=['POST'])
# @jwt_required()
def register_marketplace():
    try:
        session = ScopedSession()
        alertas = ''
        # claims = get_jwt()
        # role = claims.get('role')
        role = ADMIN_ROLE
        if (role == ADMIN_ROLE):
            marketplace_data = request.get_json()
            id = marketplace_data.get('id')
            print('0000000000000000000000---------------------------------->>>>>>>>>>>>>>><<')
            print(marketplace_data)
            print('0000000000000000000000---------------------------------->>>>>>>>>>>>>>>>><<')
            if not id:
                raise Exception('El id de marketplace es obligatorio')
            supported_marketplaceGroup = session.query(SupportedMarketplaceGroup).get(id)
            if not supported_marketplaceGroup:
                created_response = make_response(jsonify({'errores': 'suportedMarketplaceGroup para registrar no encontrado'}), 404)
            else:
                marketplaceGroup = session.query(MarketplaceGroup).get(id)
                if not marketplaceGroup:
                    marketplaceGroup = MarketplaceGroup()
                    marketplaceGroup.SupportedMarketplaceGroup = supported_marketplaceGroup
                    if 'credentials' in marketplace_data:
                        marketplaceGroup_credentials = marketplace_data.get('credentials')
                        if marketplaceGroup_credentials:
                            # Verifies specific credentials for each marketplace
                            error = validate_credentials_by_marketplace(supported_marketplaceGroup.Name, marketplaceGroup_credentials)
                            if error:
                                raise Exception(error)
                            exito, info = set_group_credentials(marketplaceGroup_credentials, marketplaceGroup)
                            if exito:
                                alertas = info
                            else:
                                raise Exception(info)
                        for supported_marketplace in supported_marketplaceGroup.SupportedMarketplaces:
                            marketplace = Marketplace()
                            marketplace.SupportedMarketplace = supported_marketplace
                            marketplace.MarketplaceGroup = marketplaceGroup
                    else:
                        raise Exception('Credenciales obligatorias')
                    session.add(marketplaceGroup)
                    session.commit()
                    created_response = make_response(jsonify({'mensaje': f'marketplaceGroup registrado exitosamente.{alertas}'}), 201)
                else:
                    # returns 202 if user already exists
                    created_response = make_response(jsonify({'errores': 'marketplaceGroup dado de alta previamente'}), 202)
        else:
            created_response = make_response(jsonify({'errores': 'no tienes permisos para registrar un marketplace'}), 403)
    except Exception as e:
        created_response = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return created_response


@marketplaces.route('/api/marketplaces/marketplaceGroupCredentials', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def post_marketplaceCredentials(session):
    marketplacegroup_request = ownUtils.get_info_from_json(request)
    print('marketplacegroup_request------------------')
    print(marketplacegroup_request)
    print('marketplacegroup_request------------------')
    supported_marketplaceGroup = ownUtils.validate_if_object_exists(session, 'supportedMarketplaceGroupId', 'dict', marketplacegroup_request, SupportedMarketplaceGroup)
    #marketplace_group = session.query(MarketplaceGroup).join(SupportedMarketplaceGroup).filter(SupportedMarketplaceGroup.Id == marketplacegroup_request.get('supportedMarketplaceGroupId')).first()        
    marketplace_group = supported_marketplaceGroup.MarketplaceGroup
    if not marketplace_group:
        supported_marketplaceGroup = marketplace_credentials = ownUtils.validate_if_object_exists(session, 'supportedMarketplaceGroupId', 'dict', marketplacegroup_request, SupportedMarketplaceGroup)
        marketplace_group = MarketplaceGroup()
        marketplace_group.SupportedMarketplaceGroup = supported_marketplaceGroup
        for supported_marketplace in supported_marketplaceGroup.SupportedMarketplaces:
            marketplace = Marketplace()
            marketplace.SupportedMarketplace = supported_marketplace
            marketplace.MarketplaceGroup = marketplace_group
    
    if marketplace_group.MarketplaceGroupCredentials:
        abort(make_response(jsonify({'error': 'Ya existe un grupo de marketplace con este supportedMarketplaceGroupId'}), 409))


    marketplace_credentials_request = marketplacegroup_request.get('credentials')
    error = validate_credentials_by_marketplace(supported_marketplaceGroup.Name, marketplace_credentials_request)
    if error:
        abort(make_response(jsonify({'error': error}), 409))
    
    marketplace_credentials = MarketplaceGroupCredentials()
    exito, info = set_group_credentials(marketplace_credentials_request, marketplace_credentials)
    if exito:
        alertas = info
    else:
        raise Exception(info)
    marketplace_group.MarketplaceGroupCredentials = marketplace_credentials
    session.commit()
    created_response = make_response(jsonify({'mensaje': f'marketplaceGroup actualizado exitosamente.{alertas}'}), 200)
    return created_response    


@marketplaces.route('/api/marketplaces/marketplaceGroupCredentials/<int:id>', methods=['PUT'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def put_marketplaceCredentials(session, id: int):
    marketplace_credentials = ownUtils.validate_if_object_exists(session, 'id', 'native', id, MarketplaceGroupCredentials)
    marketplace_credentials_request = ownUtils.get_info_from_json(request)
    # Verifies specific credentials for each marketplace
    error = validate_credentials_by_marketplace(marketplace_credentials.MarketplaceGroup.SupportedMarketplaceGroup.Name, marketplace_credentials_request)
    if error:
        abort(make_response(jsonify({'error': error}), 409))
    exito, info = set_group_credentials(marketplace_credentials_request, marketplace_credentials)
    if exito:
        alertas = info
    else:
        raise Exception(info)
    session.commit()
    created_response = make_response(jsonify({'mensaje': f'marketplaceGroup actualizado exitosamente.{alertas}'}), 200)
    return created_response


@marketplaces.route('/api/marketplaces/marketplaceGroupCredentials/<int:id>', methods=['PATCH'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[SERVICES_ROLE,])
def patch_marketplaceCredentials(id, session):
    marketplace_group_credentials_obj = ownUtils.validate_if_object_exists(session, 'id', 'native', id, MarketplaceGroupCredentials)
    credentials = ownUtils.get_info_from_json(request)
    exito, info = set_group_credentials_patch(credentials, marketplace_group_credentials_obj)
    if not exito: raise Exception(info)
    session.commit()
    return make_response(jsonify({'mensaje': f'marketplaceGroup actualizado exitosamente.{exito}'}), 200)


@marketplaces.route('/api/marketplaces/marketplaceGroupCredentials/<int:id>', methods=['DELETE'])
# esquema 422
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def delete_marketplace_credentials(session, id: int):
    marketplace_group_credentials = ownUtils.validate_if_object_exists(session, 'id', 'native', id, MarketplaceGroupCredentials)
    session.delete(marketplace_group_credentials)
    session.commit()
    return make_response(jsonify({'mensaje': f'marketplaceGroup eliminado exitosamente'}), 200)
