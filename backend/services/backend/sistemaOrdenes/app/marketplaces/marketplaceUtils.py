from flask import abort, jsonify, make_response
from sistemaOrdenes.app.models.Models import MarketplaceGroupCredentials, MarketplaceGroup, SupportedMarketplaceGroup
import sistemaOrdenes.app.ownUtils as ownUtils
import sistemaOrdenes.app.marketplace_connections.walmart as walmart_conn
import sistemaOrdenes.app.marketplace_connections.mercado_libre as ml_conn
import sistemaOrdenes.app.marketplace_connections.claro_shop as claro_conn
import sistemaOrdenes.app.marketplace_connections.amazon as amazon_conn
from sistemaOrdenes.configs import ADMIN_ROLE, SERVICES_ROLE

def validate_field(campo, longitud_campo):
    print(f'Validando campo: {len(campo)} con longitud máxima: {longitud_campo}')
    if type(campo) is not str:
        return False
    if len(campo) > longitud_campo:
        return False
    return True


def manage_field(field_name=None, container_dict=None, sqlAlchemy_object=None, object_attribute_name=None, sqlAlchemy_class=None):
    alerta_campo = ''
    errores_campo = ''
    if field_name in container_dict:
        field_var = container_dict.get(field_name)
        if field_var:
            validated = validate_field(field_var, getattr(sqlAlchemy_class, object_attribute_name).property.columns[0].type.length)
            if validated:
                setattr(sqlAlchemy_object, object_attribute_name, field_var)
            else:
                errores_campo = f'{field_name} inválido'
        else:
            alerta_campo = f'{field_name} vacio'
    return alerta_campo, errores_campo


def set_campo_credencial(alertas_validar_credenciales=None, errores_validar_credenciales=None, field_name=None, container_dict=None, sqlAlchemy_object=None, object_attribute_name=None, sqlAlchemy_class=None):
    alerta_campo, errrores_campo = manage_field(field_name=field_name, container_dict=container_dict, sqlAlchemy_object=sqlAlchemy_object, object_attribute_name=object_attribute_name, sqlAlchemy_class=sqlAlchemy_class)
    alertas_validar_credenciales = ownUtils.concatenarMensajeRetroalimentacion(alertas_validar_credenciales, alerta_campo)
    errores_validar_credenciales = ownUtils.concatenarMensajeRetroalimentacion(errores_validar_credenciales, errrores_campo)
    return alertas_validar_credenciales, errores_validar_credenciales


"""def set_aws_connection(credentials, marketplace_credentials):
    alertas_aws_connection = ''
    errores_aws_connection = ''
    if 'marketplaceCredentialsAwsConnection' in credentials:
        marketplaceCredentialsAwsConnection = MarketplaceCredentialsAwsConnection()
        aws_connection = credentials.get('marketplaceCredentialsAwsConnection')
        if type(aws_connection) is not dict:
            return '', 'marketplaceCredentialsAwsConnection debe ser un diccionario'
        if not aws_connection.get('awsAccessKey') or not aws_connection.get('awsSecretKey') or not aws_connection.get('roleArn'):
            return '', ' awsAccessKey, awsSecretKey, roleArn obligatorios'
        alertas_aws_connection, errores_aws_connection = set_campo_credencial(alertas_validar_credenciales=alertas_aws_connection, errores_validar_credenciales=errores_aws_connection, field_name='awsAccessKey', container_dict=aws_connection, sqlAlchemy_object=marketplaceCredentialsAwsConnection, object_attribute_name='AwsAccessKey', sqlAlchemy_class=MarketplaceCredentialsAwsConnection)
        alertas_aws_connection, errores_aws_connection = set_campo_credencial(alertas_validar_credenciales=alertas_aws_connection, errores_validar_credenciales=errores_aws_connection, field_name='awsSecretKey', container_dict=aws_connection, sqlAlchemy_object=marketplaceCredentialsAwsConnection, object_attribute_name='AwsSecretKey', sqlAlchemy_class=MarketplaceCredentialsAwsConnection)
        alertas_aws_connection, errores_aws_connection = set_campo_credencial(alertas_validar_credenciales=alertas_aws_connection, errores_validar_credenciales=errores_aws_connection, field_name='roleArn', container_dict=aws_connection, sqlAlchemy_object=marketplaceCredentialsAwsConnection, object_attribute_name='RoleArn', sqlAlchemy_class=MarketplaceCredentialsAwsConnection)
        if not errores_aws_connection:
            marketplace_credentials.MarketplaceCredentialsAwsConnection = marketplaceCredentialsAwsConnection
    return alertas_aws_connection, errores_aws_connection"""


def set_group_credentials(credentials_request, marketplace_credentials):
    alertas_validar_credenciales = ''
    errores_validar_credenciales = ''
    if not credentials_request.get('clientId') or not credentials_request.get('clientSecret'):
        return False, ' ClientId y ClientSecret obligatorios'
    print('00000000000000000000000000.---------------------------------------------')
    print(credentials_request)
    print('00000000000000000000000000.---------------------------------------------')
    alertas_validar_credenciales, errores_validar_credenciales = set_campo_credencial(alertas_validar_credenciales=alertas_validar_credenciales, errores_validar_credenciales=errores_validar_credenciales, field_name='clientId', container_dict=credentials_request, sqlAlchemy_object=marketplace_credentials, object_attribute_name='ClientId', sqlAlchemy_class=MarketplaceGroupCredentials)
    alertas_validar_credenciales, errores_validar_credenciales = set_campo_credencial(alertas_validar_credenciales=alertas_validar_credenciales, errores_validar_credenciales=errores_validar_credenciales, field_name='clientSecret', container_dict=credentials_request, sqlAlchemy_object=marketplace_credentials, object_attribute_name='ClientSecret', sqlAlchemy_class=MarketplaceGroupCredentials)
    alertas_validar_credenciales, errores_validar_credenciales = set_campo_credencial(alertas_validar_credenciales=alertas_validar_credenciales, errores_validar_credenciales=errores_validar_credenciales, field_name='refreshToken', container_dict=credentials_request, sqlAlchemy_object=marketplace_credentials, object_attribute_name='RefreshToken', sqlAlchemy_class=MarketplaceGroupCredentials)
    alertas_validar_credenciales, errores_validar_credenciales = set_campo_credencial(alertas_validar_credenciales=alertas_validar_credenciales, errores_validar_credenciales=errores_validar_credenciales, field_name='accessToken', container_dict=credentials_request, sqlAlchemy_object=marketplace_credentials, object_attribute_name='AccessToken', sqlAlchemy_class=MarketplaceGroupCredentials)
    if errores_validar_credenciales:
        return False, errores_validar_credenciales
    return True, alertas_validar_credenciales


def set_group_credentials_patch(credentials: dict, marketplace_credentials_object: dict) -> tuple[bool, str]:
    alertas_validar_credenciales = ''
    errores_validar_credenciales = ''
    if type(credentials) is not dict:
        return False, 'credentials debe ser un diccionario'
    if 'clientId' in credentials:
        alertas_validar_credenciales, errores_validar_credenciales = set_campo_credencial(alertas_validar_credenciales=alertas_validar_credenciales, errores_validar_credenciales=errores_validar_credenciales, field_name='clientId', container_dict=credentials, sqlAlchemy_object=marketplace_credentials_object, object_attribute_name='ClientId', sqlAlchemy_class=MarketplaceGroupCredentials)
    if 'clientSecret' in credentials:
        alertas_validar_credenciales, errores_validar_credenciales = set_campo_credencial(alertas_validar_credenciales=alertas_validar_credenciales, errores_validar_credenciales=errores_validar_credenciales, field_name='clientSecret', container_dict=credentials, sqlAlchemy_object=marketplace_credentials_object, object_attribute_name='ClientSecret', sqlAlchemy_class=MarketplaceGroupCredentials)
    if 'refreshToken' in credentials:
        alertas_validar_credenciales, errores_validar_credenciales = set_campo_credencial(alertas_validar_credenciales=alertas_validar_credenciales, errores_validar_credenciales=errores_validar_credenciales, field_name='refreshToken', container_dict=credentials, sqlAlchemy_object=marketplace_credentials_object, object_attribute_name='RefreshToken', sqlAlchemy_class=MarketplaceGroupCredentials)
    if 'accessToken' in credentials:
        alertas_validar_credenciales, errores_validar_credenciales = set_campo_credencial(alertas_validar_credenciales=alertas_validar_credenciales, errores_validar_credenciales=errores_validar_credenciales, field_name='accessToken', container_dict=credentials, sqlAlchemy_object=marketplace_credentials_object, object_attribute_name='AccessToken', sqlAlchemy_class=MarketplaceGroupCredentials)
    if errores_validar_credenciales:
        return False, errores_validar_credenciales
    return True, alertas_validar_credenciales


def validate_walmart_credentials(credentials):
    """
    Verifies Walmart credentials by obtaining an access token.

    Arguments:
        credentials {dict} -- dict with user credentials

    Keyword arguments:

    Return:
        String or None -- Return a string if some error appears, otherwise return None
    """
    # Setting credentials as global variables
    walmart_conn.set_credentials_and_id_marketplace(credentials_param=credentials)
    # Getting access token
    exito, info = walmart_conn.obtenerTokenJSON()
    # Returns an error if it exists, or adds the accessToken to the credentials if not.
    if exito:
        credentials['accessToken'] = info
        return None
    else:
        return info


def validate_ml_credentials(credentials):
    """
    Verifies Mercado libre credentials by obtaining an refresh token.

    Arguments:
        credentials {dict} -- dict with user credentials and necesary info.

    Keyword arguments:

    Return:
        String or None -- Return a string if some error appears, otherwise return None
    """
    # Getting specific info from crendentials dict
    client_id = credentials.get('clientId')
    client_secret = credentials.get('clientSecret')
    redirect_url = credentials.get('redirectUrl')
    code = credentials.get('code')
    print('00000000000000000000000000.---------------------------------------------')
    print(code)
    print(redirect_url)
    print('00000000000000000000000000.---------------------------------------------')
    if code:
        print('code')
        # Changing code to refresh token
        exito, info = ml_conn.get_refreshtoken_by_code(client_id, client_secret, redirect_url, code)
        credentials['refreshToken'] = info
    else:
        ml_conn.set_credentials_and_id_marketplace(credentials, 2)
        print('-.1')
        exito, info = ml_conn.renovar_access_token()
        print('-2.')
        print(exito)
        print(info)
        print('-2.')
        credentials['accessToken'] = info
    # Returns an error if it exists, or adds the refreshToken to the credentials if not
    if exito:
        return None
    else:
        return info


def prueba_amazon(credentials):
    amazon_conn.set_credentials_and_id_marketplace(credentials_param=credentials, marketplace_id=1)
    print('000000000000000000......')
    print(amazon_conn.get_orders(6))


def validate_amazon_credentials(credentials):
    """
    Verifies Amazon credentials by obtaining an access token.

    Arguments:
        credentials {dict} -- dict with user credentials

    Keyword arguments:

    Return:
        String or None -- Return a string if some error appears, otherwise return None
    """
    # Getting specific info from crendentials dict
    client_id = credentials.get('clientId')
    client_secret = credentials.get('clientSecret')
    refreshToken = credentials.get('refreshToken')
    # Getting access token
    exito, info = amazon_conn.get_access_token(client_id, client_secret, refreshToken)
    # Returns an error if it exists, or adds the accessToken to the credentials if not
    if exito:
        credentials['accessToken'] = info
        return None
    else:
        return info


def validate_claro_shop_credentials(credentials):
    """
    Verifies Claro shop credentials by obtaining pending orders.

    Arguments:
        credentials {dict} -- dict with user credentials

    Keyword arguments:

    Return:
        String or None -- Return a string if some error appears, otherwise return None
    """
    # Setting credentials as global variables
    claro_conn.set_credentials_and_id_marketplace(credentials_param=credentials)
    # for to manage current error: timeout
    for i in range(3):
        # Getting pending orders given that claro uses basic auth
        info = claro_conn.obtenerPedidosPendientesPrueba()
        if info:
            if not info.startswith("Error"):
                return info
        else:
            return None


def validate_credentials_by_marketplace(supported_marketplace_name, credentials):
    """
    Verifies the credentials by specific funtion for each marketplace

    Arguments:
        credentials {dict} -- dict with user credentials
        supported_marketplace_name {String} -- Name of the marketplace from which the credentials will be reviewed

    Keyword arguments:

    Return:
        String or None -- Return a string if some error appears, otherwise return None
    """
    if supported_marketplace_name == 'Amazon':
        return validate_amazon_credentials(credentials)
    elif supported_marketplace_name == 'T1comercios':
        return validate_claro_shop_credentials(credentials)
    elif supported_marketplace_name == 'Mercado Libre':
        return validate_ml_credentials(credentials)
    elif supported_marketplace_name == 'Walmart':
        return validate_walmart_credentials(credentials)
    else:
        return "El nombre de marketplace no fue encontrado para validar"


def register_error_in_orders_updater(session, marketplace_group__id, error):
    timeNow = ownUtils.get_time_zone_now()
    if not error:
       abort(make_response(jsonify({'errores': 'No tienes permisos para ver esta información'}), 403))
    marketplaceGroup = ownUtils.validate_if_object_exists(session, "id", "native", marketplace_group__id, MarketplaceGroup)
    marketplaceGroup.LastUpdate = timeNow
    marketplaceGroup.CommentUpdate = error
    session.commit()


def get_marketplace_credentials(session, scope, role):
    scope_dict = manage_marketplaceGroup_scope(scope, role)
    query = session.query(MarketplaceGroup)
    marketplaces_list = query.all()
    marketplaces_serialized = [marketplace.serialize(scope=scope_dict) for marketplace in marketplaces_list]
    if scope in ['marketplacegroupcredentials_supportedMarketplaceGroup', 'marketplaceGroupCredentials_supportedMarketplaceGroup_marketplaces-supportedMarketplace']:
        for miniMarket in marketplaces_serialized:
            return_status_check_credentials(miniMarket)
    return marketplaces_serialized

def get_marketplace_credentials_raw(session, scope, role):
    scope_dict = manage_marketplaceGroup_scope(scope, role)
    query = session.query(MarketplaceGroup)
    marketplaces_list = query.all()
    marketplaces_serialized = [marketplace.serialize(scope=scope_dict) for marketplace in marketplaces_list]
    return marketplaces_serialized

def get_marketplace_credentials_by_id(session, scope, role, id):
    marketplace = ownUtils.validate_if_object_exists(session, 'id', 'native', id, MarketplaceGroup)
    scope_dict = manage_marketplaceGroup_scope(scope, role)
    marketplace_serialized = marketplace.serialize(scope=scope_dict)
    if scope in ['marketplacegroupcredentials_supportedMarketplaceGroup', 'marketplaceGroupCredentials_supportedMarketplaceGroup_marketplaces-supportedMarketplace']:
        return_status_check_credentials(marketplace_serialized)
    return marketplace_serialized



def get_marketplace_credentials_by_name(session, scope, role, name):
    marketplaceGroup = session.query(MarketplaceGroup).join(SupportedMarketplaceGroup).filter(SupportedMarketplaceGroup.Name == name).first()
    if not marketplaceGroup:
        abort(make_response(jsonify({'error': 'MarketplaceGroup no encontrado'}), 404))
    scope_dict = manage_marketplaceGroup_scope(scope, role)
    marketplaceGroup_serialized = marketplaceGroup.serialize(scope=scope_dict)
    if scope in ['marketplacegroupcredentials_supportedMarketplaceGroup', 'marketplaceGroupCredentials_supportedMarketplaceGroup_marketplaces-supportedMarketplace']:
        return_status_check_credentials(marketplaceGroup_serialized) 
    return marketplaceGroup_serialized


def get_marketplace_credentials_by_name_raw(session, scope, role, name):
    marketplaceGroup = session.query(MarketplaceGroup).join(SupportedMarketplaceGroup).filter(SupportedMarketplaceGroup.Name == name).first()
    if not marketplaceGroup:
        abort(make_response(jsonify({'error': 'MarketplaceGroup no encontrado'}), 404))
    scope_dict = manage_marketplaceGroup_scope(scope, role)
    marketplaceGroup_serialized = marketplaceGroup.serialize(scope=scope_dict)
    return marketplaceGroup_serialized


def manage_marketplaceGroup_scope(scope: str, role: str) -> dict:
    if not scope:
        scope = "basic"
    if scope == 'basic':
        scope_dict = {}
    elif scope == 'marketplaces_info':
        scope_dict = {
            'supportedMarketplaceGroup': {},
            'marketplaces': {
                'supportedMarketplace': {}
            }
        }
    elif scope == 'marketplacegroupcredentials_supportedMarketplaceGroup':
        if role != ADMIN_ROLE and role != SERVICES_ROLE:
            abort(make_response(jsonify({'errores': 'No tienes permisos para ver esta información'}), 403))
        scope_dict = {
            'marketplaceGroupCredentials': {},
            'supportedMarketplaceGroup': {}
        }
    elif scope == 'marketplaceGroupCredentials_supportedMarketplaceGroup_marketplaces-supportedMarketplace':
        if role != ADMIN_ROLE and role != SERVICES_ROLE:
            abort(make_response(jsonify({'errores': 'No tienes permisos para ver esta información'}), 403))
        scope_dict = {
            'marketplaceGroupCredentials': {},
            'supportedMarketplaceGroup': {},
            'marketplaces': {
                'supportedMarketplace': {}
            }
        }
    else:
        abort(make_response(jsonify({'error': "scope no válido"}), 400))
    return scope_dict


def return_status_check_credentials(miniMarket: dict) -> None:
    if miniMarket["marketplaceGroupCredentials"]:
        result = validate_credentials_by_marketplace(miniMarket["supportedMarketplaceGroup"]["name"],
                                                     miniMarket["marketplaceGroupCredentials"])
        miniMarket["marketplaceGroupCredentials"] = {
            "status": "Credenciales correctas" if not result else "Credenciales incorrectas",
            "check": True if not result else False,
            "clientId": miniMarket["marketplaceGroupCredentials"]["clientId"]
        }
    else:
        miniMarket["marketplaceGroupCredentials"] = {
            "status": "No sincronizado",
            "check": False
        }