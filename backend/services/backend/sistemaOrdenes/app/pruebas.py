import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

cuentaRemitente = "<EMAIL>"
contraseniaRemitente = "KDYjmn^Q"
message = MIMEMultipart("alternative")
message['Subject'] = "Alerta: Error al consultar los pedidos"
message['From'] = cuentaRemitente
message['To'] = "<EMAIL>"

# ---------Puerto
PUERTO_SMTP = 465
# ---------Servidor SMTP
SERVIDOR_SMTP = "smtp.dreamhost.com"

text = """\
Hola,
Surgió un error al consultar los pedidos, necesitamos tu ayuda"""

html = """\
<html>
  <body>
    <p>Hola,<br>
       Esto es
    </p>
  </body>
</html>
"""

part1 = MIMEText(text, "plain")
part2 = MIMEText(html, "html")

message.attach(part1)
message.attach(part2)


def enviarCorreoDeAlerta():
    try:
        context = ssl.create_default_context()
        with smtplib.SMTP_SSL(SERVIDOR_SMTP,
                              PUERTO_SMTP, context=context) as server:
            server.login(cuentaRemitente, contraseniaRemitente)
            server.sendmail(cuentaRemitente,
                            "<EMAIL>", message.as_string())
            print('enviado--')
            return True, 'exito'
    except Exception as e:
        return False, str(e)


def enviarNoWith():
    # Conectar al servidor de correo y enviar el mensaje
    try:
        context = ssl.create_default_context()
        server = smtplib.SMTP_SSL(SERVIDOR_SMTP, PUERTO_SMTP, context=context)
        server.login(cuentaRemitente, contraseniaRemitente)
        server.sendmail(cuentaRemitente, "<EMAIL>", message.as_string())
        server.quit()
        print("¡Correo enviado correctamente!")
    except Exception as e:
        print("Error al enviar el correo:", e)
        raise Exception(str(e))
