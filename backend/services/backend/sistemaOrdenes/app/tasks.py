import os
import sys
sys.path.append(os.getcwd())
import time
from datetime import datetime
from celery import Celery
from celery.schedules import crontab
from sistemaOrdenes.app.models.Models import MarketplaceGroup, SupportedMarketplaceGroup, \
    Order, OrderMessage, OrderStatus, OrderMessageAtachment
from sistemaOrdenes import configs
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.marketplaces.mercado_libre import Mercado_libre
from sistemaOrdenes.app import config
from sistemaOrdenes.app.orders.orderUtils import is_valid_Required, is_valid_Order
from sistemaOrdenes.ordersUpdater.updater import get_order_statuses, create_dic_Order, actualizar_ordenes_marketplaces
import shutil

MESSAGE_ATTACHMENTS = config.Config.MESSAGE_ATTACHMENTS_FOLDER

celery = Celery(__name__)
celery.conf.broker_url = os.environ.get("CELERY_BROKER_URL", "redis://localhost:6379")
celery.conf.result_backend = os.environ.get("CELERY_RESULT_BACKEND", "redis://localhost:6379")


@celery.task(name="create_test_task")
def create_test_task(task_type):
    time.sleep(int(task_type) * 10)
    return True


@celery.task(name="check")
def check():
    print("I am checking your stuff")

@celery.task(name="update_orders")
def update_orders():
    report = actualizar_ordenes_marketplaces(configs.DIAS_ACTUALIZACION_1)
    print(report)


def five_attemps(func):
    def wrapper_func(*args, **kwargs):
        for i in range(5):
            try:
                response = func(*args, **kwargs)
                break
            except Exception as e:
                if i == 4:
                    raise Exception(str(e)) # check before implement
        return response
    return wrapper_func

celery.conf.beat_schedule = {
    "update-orders-each-fifteen-minutes": {
        "task": "update_orders",
        "schedule": crontab(minute="*/15")
    }
}

def notification_choose(topic, resource):
    """
    Switch to call the correct function based on the topic.
    """
    if topic == "orders_v2":
        manage_orders_v2(resource)
    elif topic == "messages":
        manage_message(resource)
    elif topic == "fbm_stock_operations":
        manage_fbm_stock_operations(resource)
    elif topic == "flex-handshakes":
        manage_flex_handshakes(resource)
    elif topic == "post_purchase":
        manage_post_purchase(resource)
    else:
        pass
    


@celery.task(name="manage_notification")
def manage_notification(notification):
    """
    {
        "_id": "b2bcd95d-6cbd-4158-b1da-4e1e33183609",
        "topic": "items",
        "resource": "/items/MLM836822616",
        "user_id": 32435319,
        "application_id": 7033618764711339,
        "sent": "2025-05-29T08:27:21.295Z",
        "attempts": 1,
        "received": "2025-05-29T08:27:21.074Z",
        "actions": []
    }
    """
    topic = notification.get("topic")
    resource = notification.get("resource")
    notification_choose(topic, resource)

def manage_notification_dev(notification):
    topic = notification.get("topic")
    resource = notification.get("resource")
    notification_choose(topic, resource)

def get_marketplace_obj(session, name):
    marketplace_db = session.query(MarketplaceGroup).join(SupportedMarketplaceGroup).filter(SupportedMarketplaceGroup.Name == name).first()
    if not marketplace_db:
        raise Exception("No se encontro el grupo de mercado libre")
    marketplace_db_serialized = marketplace_db.serialize(scope=scope)
    marketplace_group_id = marketplace_db_serialized['id']
    supported_marketplace_credentials = marketplace_db_serialized['marketplaceGroupCredentials'] #c
    supported_marketplace_group_name = marketplace_db_serialized['supportedMarketplaceGroup']['name']
    marketplace_obj = Mercado_libre(marketplace_group_name=supported_marketplace_group_name)
    marketplace_obj.set_credentials_and_id_marketplace(credentials_param=supported_marketplace_credentials, marketplace_id=marketplace_group_id)
    marketplace_obj.set_session(session)
    return marketplace_obj



def bring_session():
    # valida el rol y retorna la session
    def decorator(func):
        def wrapper_func(*args, **kwargs):
            try:
                session = ScopedSession()
                kwargs['session'] = session
                return func(*args, **kwargs)
            except Exception as e:
                session.rollback()
                raise e
            finally:
                session.close()
        return wrapper_func
    return decorator


def get_order_info(order_id, session):
    list_status = get_order_statuses(session)
    mercado_libre_obj = get_marketplace_obj(session, "Mercado Libre")
    order = mercado_libre_obj.get_turtle_format_order_info(order_id, list_status)
    order = create_dic_Order(order)
   
    return order


def create_or_update_order(order_id, session):
    order = get_order_info(order_id, session)
    MarketplaceOrderId = is_valid_Required('MarketplaceOrderId', order)
    order_db = session.query(Order).filter(Order.MarketplaceOrderId == str(MarketplaceOrderId)).first()
    if not order_db:
        order_db = is_valid_Order(order, 0, session)
    else:
        order_db = is_valid_Order(order, 1, session)
    return order_db

def create_or_return(order_id, session):
    order = get_order_info(order_id, session)
    MarketplaceOrderId = is_valid_Required('MarketplaceOrderId', order)
    order_db = session.query(Order).filter(Order.MarketplaceOrderId == str(MarketplaceOrderId)).first()
    if not order_db:
        order_db = is_valid_Order(order, 0, session)
    return order_db


@five_attemps
@bring_session()
def manage_orders_v2(resource, session):
    order_id = resource.split("/")[-1] # Extract the order ID from the resource path
    create_or_update_order(order_id, session)
    session.commit()

scope = {
    'marketplaceGroupCredentials': {},
    'supportedMarketplaceGroup': {},
    'marketplaces': {
        'supportedMarketplace': {}
    }
}
#########3
ATACHMENTS_FOLDER = config.Config.MESSAGE_ATTACHMENTS_FOLDER


def create_message_folder(message_id):
    # Verificar si la ruta completa existe y, si no, crearla
    print("Creating message folder for message_id:", message_id)
    path = os.path.join(ATACHMENTS_FOLDER, message_id)
    if not os.path.exists(path):
        os.makedirs(path)
    print("Message folder created at:", os.path.join(ATACHMENTS_FOLDER, message_id))

def addAtachemntToMessageFolder(message_id, filename, atachment):
    create_message_folder(message_id)
    path = os.path.join(ATACHMENTS_FOLDER, message_id, filename)
    with open(path, 'wb') as file:  # Choose your desired filename
        file.write(atachment)
    print("Image downloaded successfully!")
########3

def manage_db_order_convesation(conversation, mercado_libre_obj, order, session):
    for message in conversation['messages']:
        message_db = session.query(OrderMessage).filter(OrderMessage.MarketplaceId == message['id']).first()
        if not message_db:
            message_db = OrderMessage(
                TimeStamp = message['message_date']['received'],
                Message = message['text'],
                MarketplaceId = message['id'],
                FromClient = not (str(Mercado_libre.get_message_resource_by_name(message, 'sellers')) == str(message['from']['user_id']))
            )
            message_attachments = message['message_attachments']
            if message_attachments:
                attachments_db = []
                for attachment in message_attachments:
                    attachment_raw = mercado_libre_obj.get_attachment(attachment['filename'])
                    addAtachemntToMessageFolder(message['id'], attachment['filename'], attachment_raw)
                    attachment_db = OrderMessageAtachment(
                        MarketplaceId = attachment['filename'],
                        Type = attachment['type'],
                        URLPhoto = os.path.join(message['id'], attachment['filename']),
                    )
                    attachments_db.append(attachment_db)
                message_db.OrderMessageAtachments = attachments_db
            order.OrderMessages.append(message_db)

@five_attemps
@bring_session()
def manage_message(resource, session):
    mercado_libre_obj = get_marketplace_obj(session, "Mercado Libre")
    conversation = mercado_libre_obj.get_conversation_from_message_id(resource)
    path = conversation['conversation_status']['path']
    pack_id = get_string_between(path, "packs/", "/sellers")
    try:
        pack = mercado_libre_obj.get_an_order_id_from_pack_id(pack_id)
        order_id = pack['orders'][0]['id']
    except Exception as e:
        if get_string_between(str(e), '<', '>') != "404":
            raise e
        order_id = pack_id
    order = create_or_return(order_id, session)
    manage_db_order_convesation(conversation, mercado_libre_obj, order, session)
    session.commit()



def get_string_between(original_string, start, end):
    idx1 = original_string.find(start)
    len_start = len(start)
    # Find the index of the end substring, starting after the start substring
    idx2 = original_string.find(end, idx1 + len_start)
    return original_string[idx1 + len(start):idx2]


def manage_fbm_stock_operations(resource):
    pass


def manage_flex_handshakes(resource):
    pass


def manage_claim(resource, session):
    mercado_libre_obj = get_marketplace_obj(session, "Mercado Libre")
    new_status = session.query(OrderStatus.OrderStatusId).filter(OrderStatus.OrderStatus == 'Desconocido').first()[0]
    
    statusReturn = session.query(OrderStatus.OrderStatusId).filter(OrderStatus.OrderStatus == 'En Devolución').first()[0]
    statusRefund = session.query(OrderStatus.OrderStatusId).filter(OrderStatus.OrderStatus == 'Reembolsado').first()[0]
    delivered_without_change_posibility = session.query(OrderStatus.OrderStatusId).filter(OrderStatus.OrderStatus == 'Entregado sin posiblidad de cambios').first()[0]
    claim_info = mercado_libre_obj.get_claims_info(resource)
    resolution = claim_info['resolution']
    reason = resolution['reason']
    if reason:
        if reason == "item_returned":
            new_status = statusReturn
        elif reason == "partial_refunded" or reason == "payment_refunded" or reason == "reimbursed":
            new_status = statusRefund
        elif reason == "no_bpp":
           new_status = delivered_without_change_posibility
        elif reason == "coverage_decision":
            new_status = statusRefund
    else:
        if claim_info['type'] == 'return' or claim_info['type'] == 'change':
            new_status = statusReturn
    return new_status, claim_info

@five_attemps
@bring_session()
def manage_post_purchase(resource, session):
    new_status, claim_info = manage_claim(resource, session)
    order = create_or_return(str(claim_info['resource_id']), session)
    order.OrderStatusId = new_status
    session.commit()

    


if __name__ == "__main__":
    print("Starting task to manage orders v2")
    manage_orders_v2('/orders/2000011616532822')
    print("Starting task to manage orders v2")

