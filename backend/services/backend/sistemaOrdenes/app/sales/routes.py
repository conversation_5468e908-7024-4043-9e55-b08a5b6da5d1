import json
import traceback
from datetime import datetime
from flask_smorest import Blueprint
from flask import Response, request, jsonify, make_response
from flask_jwt_extended import jwt_required, get_jwt
import sqlalchemy
from sqlalchemy.orm import aliased
from sqlalchemy import desc, func, cast, String, text, Integer
from datetime import date
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.configs import ADMIN_ROLE, ROLES, ADMNISTRATIVE_ACCOUNTANT_ROLE
from sistemaOrdenes.app.models.Models import Marketplace, Order, DirectSale, OrderInternalStatus, DirectSaleInternalStatus, OrderStatus, \
    Client, ProductInOrder, ProofOfPayment, Product, ProductBase, ProductInDirectSale, \
    Product_Store, Product_SupplierStore, DirectSaleType, AmountChanger, ChangerGroup, CommercialTerm
from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.app.direct_sales.direct_sales_utils import direct_sales_all, direct_sales_count, search_query as direct_sale_search_query, no_search_query as direct_sale_no_search_query, search_query_core as direct_sale_search_query_core
from sistemaOrdenes.app.orders.orderUtils import orders_count, orders_all, search_query as order_search_query, no_search_query as order_no_search_query, search_query_core as order_search_query_core

sales = Blueprint("Sales", __name__, description="General Sales Operation")


@sales.route('/api/sales/generalSalesFiltro')
# @jwt_required()
@ownUtils.my_decorator_http_manage
def return_General_Sales_Filtro(session):
    # claims = get_jwt()
    # role = claims.get('role')
    role = ADMIN_ROLE
    if role in ROLES:
        filtro = request.args.get('filtro')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        search = request.args.get('search')
        offset = request.args.get('offset')
        nc = request.args.get('next')
        # Manage fields
        if not filtro:
            filtro = []
        else:
            filtro = filtro.split('-')
        scope_direct_sale = {
            'directSaleType': {},
            'client': {},
            'seller': {},
            'directSaleInternalStatus': {},
            'productsInDirectSale': {
                'productInDirectSale_amountChangers': {
                    'amountChanger': {
                        'amountChangerType': {},
                        'changerGroup': {}
                    }
                },
                'productAtStore': {}
            },
            'directSale_amountChangers': {
                'amountChanger': {
                    'amountChangerType': {},
                    'changerGroup': {}
                }
            },
            'directSaleStackableComments': {}
        }
        scope_order = {
            'basics': ['paidAmount', 'fee', 'shipping', 'receivedAmount'],
            'client': {},
            'orderStackableComments': {},
            'orderStatusChanges': {},
            'productsInOrder': {
                'basics': ['paidAmount', 'fee', 'shipping', 'receivedAmount'],
                'publicationProduct': {
                    'publicationProduct_product_stores': {
                        'poduct_store': {
                            'product': {
                                'productBase': {},
                            },
                            'store': {},
                            'locationLevelItems_product_store': {
                                'locationLevelItem': {}
                            }
                        }
                    },
                    'publicationProduct_product_supplierStores': {
                        'poduct_supplierStore': {}
                    }
                }
            }
        }
        if not filtro or "ds" in filtro:
            direct_sales_bool = True
        else:
            direct_sales_bool = False
        if not filtro or not (len(filtro) == 1 and filtro[0] == "ds"):
            orders_bool = True
        else:
            orders_bool = False
        if direct_sales_bool and orders_bool:
            colums_to_get_direct_sales = (DirectSale.DirectSaleId.label('DirectSaleId'), cast(sqlalchemy.null(), Integer).label('OrderId'), DirectSale.SaleDate.label("Date"))
            columns_to_get_orders = (sqlalchemy.null().label('DirectSaleId'), Order.OrderId.label('OrderId'), Order.CreationDate.label("Date"))
            if search is not None and search != '':
                query_direct_sale1, query_direct_sale2 = direct_sale_search_query_core(session, None, start_date, end_date, search, columns_to_get=colums_to_get_direct_sales)
                # print(query_direct_sale.all()[0].keys())
                if "ds" in filtro:
                    filtro.remove("ds")
                filtro = '-'.join(filtro)
                query_orders = order_search_query_core(session, filtro, None, None, None, start_date, end_date, None, None, search, columns_to_get=columns_to_get_orders)
                sales = sqlalchemy.union(query_direct_sale1, query_direct_sale2, query_orders)
                subquery_sales = sales.subquery()
                final_query = sqlalchemy.select(subquery_sales).order_by(desc(subquery_sales.c.greatest))
                if ((offset is not None and offset != "") and (nc is not None and nc != "")):
                    final_query = final_query.order_by(
                        desc('Date')).offset(offset).limit(nc)
                else:
                    final_query = final_query.order_by(
                        desc('Date')).offset(0).limit(30)
                sales = session.execute(final_query)
                direct_sales_prev = {}
                orders_prev = {}
                for sale in sales:
                    if sale.DirectSaleId:
                        direct_sales_prev[sale.DirectSaleId] = sale.greatest
                    else:
                        orders_prev[sale.OrderId] = sale.greatest
                direct_sales = session.query(DirectSale).filter(DirectSale.DirectSaleId.in_(tuple(direct_sales_prev.keys()))).all()
                orders = session.query(Order).filter(Order.OrderId.in_(tuple(orders_prev.keys()))).all()
                consolidated = []
                for direct_sale in direct_sales:
                    consolidated.append({**direct_sale.serialize(scope=scope_direct_sale), "greatest": direct_sales_prev[direct_sale.DirectSaleId]})
                for order in orders:
                    consolidated.append({**order.serialize(scope=scope_order), "greatest": orders_prev[order.OrderId]})
                consolidated = sorted(consolidated, key=lambda x: (x['greatest'], x['saleDate'] if x.get("directSaleId") else x["creationDate"]), reverse=True)
                sales = consolidated
            else:
                query_direct_sale = direct_sale_no_search_query(session, None, start_date, end_date, columns_to_get=colums_to_get_direct_sales)
                if "ds" in filtro:
                    filtro.remove("ds")
                filtro = '-'.join(filtro)
                query_orders = order_no_search_query(session, filtro, None, None, None, start_date, end_date, None, None, columns_to_get=columns_to_get_orders)
                sales = query_direct_sale.union(query_orders)
                if ((offset is not None and offset != "") and (nc is not None and nc != "")):
                    sales = sales.order_by(
                        desc('Date')).offset(offset).limit(nc)
                else:
                    sales = sales.order_by(
                        desc('Date')).offset(0).limit(30)
                sales = sales.all()
                direct_sales_prev = []
                orders_prev = []
                for sale in sales:
                    if sale.DirectSaleId:
                        direct_sales_prev.append(sale.DirectSaleId)
                    else:
                        orders_prev.append(sale.OrderId)
                direct_sales = session.query(DirectSale).filter(DirectSale.DirectSaleId.in_(tuple(direct_sales_prev))).all()
                orders = session.query(Order).filter(Order.OrderId.in_(tuple(orders_prev))).all()
                consolidated = []
                for direct_sale in direct_sales:
                    consolidated.append(direct_sale.serialize(scope=scope_direct_sale))
                for order in orders:
                    consolidated.append(order.serialize(scope=scope_order))
                consolidated = sorted(consolidated, key=lambda x: (x['saleDate'] if x.get("directSaleId") else x["creationDate"]), reverse=True)
                sales = consolidated
        elif direct_sales_bool:
            ventas_directas_filtradas = direct_sales_all(session, None, start_date, end_date, search, offset, nc)
            direct_sales_serialized = list(map(lambda venta_directa: venta_directa.serialize(scope=scope_direct_sale), ventas_directas_filtradas))
            sales = direct_sales_serialized
        else:
            filtro = "-".join(filtro)
            orders = orders_all(session, filtro, None, None, None, start_date, end_date, None, None, search, offset, nc)
            orders_serialized = list(map(lambda order: order.serialize(scope=scope_direct_sale), orders))
            sales = orders_serialized

        return make_response(jsonify({"sales": sales}), 200)
    else:
        return make_response(jsonify({"errores": "No tienes permisos para consultar las ventas"}), 403)


@sales.route('/api/sales/loadPageGeneralSales')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def return_numGeneral_Sales_Filtro(session):
    filtro = request.args.get('filtro')
    start_date = request.args.get('startDate')
    end_date = request.args.get('endDate')
    # Buscado
    if not filtro:
        filtro = []
    else:
        filtro = filtro.split('-')
    ultimateList = 0
    if not filtro or "ds" in filtro:
        directSales_count = direct_sales_count(session, None, start_date, end_date)
        print(directSales_count)
        ultimateList += directSales_count
    if not filtro or not (len(filtro) == 1 and filtro[0] == "ds"):
        if "ds" in filtro:
            filtro.remove("ds")
        filtro = '-'.join(filtro)
        order_count = orders_count(session, filtro, None, None, None, start_date, end_date, None, None)
        print(order_count)
        ultimateList += order_count
    query = session.query(Marketplace)
    marketplaces = query.all()
    marketplaces_serialized = [finalMarket.serialize(scope={'supportedMarketplace': {}})
                               for finalMarket in marketplaces]
    marketplaces_serialized.append({
        "marketplaceId": "ds",
        "supportedMarketplace": {
            "supportedMarketplaceId": "ds",
            "supportedMarketplaceName": "Venta Directa"
        }})
    orderInternalStatuses = session.query(OrderInternalStatus).all()
    orderInternalStatusSerialized = [finalInternalStatus.basic_data_serialize() for finalInternalStatus in orderInternalStatuses]
    direct_sale_internal_statuses = session.query(DirectSaleInternalStatus).all()
    direct_sale_internal_statuses_serialized = [direct_sale_internal_status.serialize() for direct_sale_internal_status in direct_sale_internal_statuses]
    query = session.query(OrderStatus)
    orderStatuses = query.all()
    order_statuses_serialized = [orderStatus.basic_data_serialize() for orderStatus in orderStatuses]
    query = session.query(DirectSaleType)
    direct_sale_types = query.all()
    direct_sale_types_serialized = [direct_sale_type.serialize() for direct_sale_type in direct_sale_types]
    query = session.query(AmountChanger).join(ChangerGroup).filter(ChangerGroup.Name == 'Impuesto')
    taxes = query.all()
    tax_scope = {
        'amountChangerType': {},
        'changerGroup': {}
    }
    taxes_serialized = [tax.serialize(scope=tax_scope) for tax in taxes]
    query = session.query(CommercialTerm)
    commercial_terms = query.all()
    commercial_terms_serialized = [commercial_term.serialize() for commercial_term in commercial_terms]
    return {
        'numConsolidate': ultimateList,
        'marketplaces': marketplaces_serialized,
        'directSaleInternalStatuses': direct_sale_internal_statuses_serialized,
        'orderInternalStatuses': orderInternalStatusSerialized,
        'orderStatuses': order_statuses_serialized,
        'commercialTerms': commercial_terms_serialized,
        'directSaleTypes': direct_sale_types_serialized,
        'taxes': taxes_serialized
    }
