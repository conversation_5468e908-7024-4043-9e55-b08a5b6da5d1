from .schemas import ProductsFiltroSchema
from sqlalchemy import func
from flask_smorest import Blueprint
from flask import jsonify, request, Response, \
    make_response, send_file, current_app
import traceback
from .productUtils import armarQueryFiltrosProductos, \
    armarQueryBuscadorProductos, procesar_productos_xlsx, \
    armarQueryFiltrosVariaciones, ObtenerVariacionesFiltradasConValores, \
    agregarBasicosProductBase, agregarDimensiones, \
    agregarProductBaseVariations, create_new_variation_product, \
    agregarImagenes, agregarAtributosProductoBase, \
    armar_query_marca_modelo, agregarTagsProductoBase, \
    fuzzyProductsBaseSearch, update_productos_variacion, \
    consolidated_info, product_stores_extended, productBase_extended, \
    products_extended, products_extended_publications, product_stores, \
    extended0productBase0extras, stores_zone, publications, extended_stores, \
    extended_publications, get_condition_conditionVariation, register_variation_product, \
    update_product_variation
from flask_jwt_extended import jwt_required, get_jwt
import sistemaOrdenes.app.ownUtils as ownUtils
import re
import os
import shutil
import json
from sistemaOrdenes.app.models.Models import ProductBase, Product_Store, \
    Store, ProductBaseShippingDimension, ProductBaseProductDimension, \
    Product, ProductBasePhoto, Product_SupplierStore, \
    SupplierStore, Supplier, Zone, CustomVariation, CustomVariationValue, \
    Marketplace, PublicationProduct, PublicationProduct_Product_Store, \
    PublicationBase
from sistemaOrdenes.app.models.db import ScopedSession
from werkzeug.datastructures import ImmutableMultiDict
from sistemaOrdenes.configs import ROLES, ADMIN_ROLE, WAREHOUSE_ROLE

current = os.path.dirname(os.path.realpath(__file__))

products = Blueprint("Products", __name__,
                     description="Operations on Products")


@products.route('/api/productsBase/customVariation', methods=['POST'])
@ownUtils.my_decorator_http_manage
def create_custom_variation(session):
    data = ownUtils.get_info_from_json(request)
    customVariationName = data.get('customVariationName')
    if not customVariationName:
        raise Exception('customVariationName obligatorio')
    else:
        customVariationName = customVariationName.lower()
    customVariation = CustomVariation(customVariationName)
    session.add(customVariation)
    ownUtils.commit_catching_unique_constraint(session=session)
    return make_response(
        jsonify({'mensaje': ('Variación personalizada registrada '
                             'exitosamente')}), 201)


@products.route('/api/productsBase/customVariation/customVariationValue', methods=['POST'])
@ownUtils.my_decorator_http_manage
def create_custom_variation_value(session):
    data = ownUtils.get_info_from_json(request)
    customVariation = ownUtils.validate_if_object_exists(session, 'customVariationId', 'dict', data, CustomVariation)
    customVariationValueR = data.get('customVariationValue')
    if not customVariationValueR:
        raise Exception('customVariationValue obligatorio')
    else:
        customVariationValueR = customVariationValueR.lower()
    customVariationValue = CustomVariationValue(customVariationValueR)
    customVariationValue.CustomVariation = customVariation
    session.add(customVariationValue)
    session.commit()
    return make_response(
        jsonify({'mensaje': ('Valor de variación personalizada registrada '
                             'exitosamente')}), 201)


@products.route('/api/products/loadPageProducts')
# @products.arguments(SearchSchema,location="query")
# @jwt_required()
@ownUtils.my_decorator_http_manage
def return_num_products_filtro(session):
    # Buscador
    # query = fuzzyProductsBaseSearch(session, search)
    # registros = query.count()
    registros = session.query(ProductBase).count()
    marketplaces = session.query(Marketplace).all()
    # print(registros)
    return jsonify({'numeroProductos': registros,
                    'marketplaces': [
                        finalMarket.serialize(scope={'supportedMarketplace': {}})
                        for finalMarket in marketplaces
                    ]})


@products.route('/api/products/obtenerPlantillaCargaMasiva')
# @jwt_required()
def return_plantilla_carga_masiva():
    try:
        pathPlantilla = os.path.join(
            current, 'plantillas\\Plantilla_Carga_Masiva.xlsx')
        createdResponse = send_file(pathPlantilla)
    except Exception as e:
        print(str(e))
        createdResponse = Response(status=500)
    finally:
        return createdResponse


@products.route('/api/products/getImage/<rutaArchivo>')
# @jwt_required()
def return_img_producto(rutaArchivo):
    try:
        print('*************************')
        print(rutaArchivo)
        rutaArchivoConDiagonal = ownUtils.ownUnquote(rutaArchivo)

        path = os.path.\
            join(current_app.config['PRODUCT_IMAGES_FOLDER'],
                 rutaArchivoConDiagonal)
        imageToReturn = send_file(path)
    except Exception as e:
        print(str(e))
        path = os.path.\
            join(current_app.config['PRODUCT_IMAGES_FOLDER'],
                 "_NotFoundImage\\404.png")
        imageToReturn = send_file(path)
    finally:
        return imageToReturn


@products.route('/api/products/productsBaseFiltroStocks')
@products.arguments(ProductsFiltroSchema, location="query")
# @jwt_required()
def return_products_base_filtro_stocks(ProductsFiltroSchema):  # ProductsFiltroSchema
    try:
        session = ScopedSession()
        search = request.args.get('search')
        offset = request.args.get('offset')
        nc = request.args.get('next')
        query = fuzzyProductsBaseSearch(session, search)
        if ((offset is not None and offset != "") and (nc is not None and nc != "")):
            query = query.order_by(
                ProductBase.InternalBaseSku).offset(offset).limit(nc)
        else:
            query = query.order_by(
                ProductBase.InternalBaseSku).offset(0).limit(30)
        productosFiltrados = query.all()
        # Obtener los SKU de los productos en la lista de ProductBase
        skus = [product_base.InternalBaseSku
                for product_base in productosFiltrados]
        # Definir alias para evitar conflictos de nombres
        ps = Product_SupplierStore
        p = Product
        pb = ProductBase
        ss = SupplierStore
        s = Supplier
        result = (
            session.query(
                s.SupplierName,
                pb.InternalBaseSku,
                func.sum(ps.Stock).label('TotalStock')
            )
            .join(p, p.InternalSku == ps.InternalSku)
            .join(pb, pb.InternalBaseSku == p.InternalBaseSku)
            .join(ss, ss.SupplierStoreId == ps.SupplierStoreId)
            .join(s, s.RFC == ss.RFC)
            .filter(pb.InternalBaseSku.in_(skus))
            .group_by(s.SupplierName, pb.InternalBaseSku)
            .having(func.sum(ps.Stock) > 0)
            .all()
        )
        info_por_sku = {}
        zone_numbers = session.query(Zone.ZoneNumber).all()
        for miniInternalBaseSku in skus:
            stock_por_zona = {}
            for zone_number in zone_numbers:
                zone_number = zone_number[0]
                stock_total = session.\
                    query(func.sum(Product_SupplierStore.Stock)).\
                    join(Product).\
                    join(SupplierStore).\
                    join(ProductBase).\
                    filter(ProductBase.InternalBaseSku == miniInternalBaseSku,
                           SupplierStore.ZoneNumber == zone_number).\
                    scalar()
                if stock_total is None:
                    stock_total = 0
                if stock_total > 0:
                    stock_por_zona[zone_number] = stock_total
            info_zona = []
            for zone_number, stock_total in stock_por_zona.items():
                info_zona.append(
                    {"zone": zone_number, "stock": int(stock_total)})
            info_supplier = []
            for supplier_name, internal_sku, total_stock in result:
                if internal_sku == miniInternalBaseSku:
                    info_supplier.append(
                        {"supplier": supplier_name, "stock": int(total_stock)})
            info_por_sku[miniInternalBaseSku] = {
                "stockByZone": info_zona, "stockBySupplier": info_supplier}
        productosFiltradosSerizalizados = [
            productoFiltrado.serializeWithStock(
                info_por_sku[productoFiltrado.InternalBaseSku])
            for productoFiltrado in productosFiltrados]
        createdResponse = {'productos': productosFiltradosSerizalizados}
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@products.route('/api/products/getProductByBrandAndModel')
# @products.arguments(ProductsFiltroSchema,location="query")
# @jwt_required()
def get_product_by_brand_model():
    try:
        session = ScopedSession()
        # Brand and model
        brand = request.args.get('brand')
        model = request.args.get('model')
        # Otros daros¿
        offset = request.args.get('offset')
        nc = request.args.get('next')
        query = armarQueryFiltrosProductos(session)
        query = armar_query_marca_modelo(query, brand, model)
        if query is False:
            raise Exception("Los datos de marca y modelo son obligatorios")
        if ((offset is not None and offset != "") and (nc is not None and nc != "")):
            query = query.order_by(
                ProductBase.InternalBaseSku).offset(offset).limit(nc)
        else:
            query = query.order_by(
                ProductBase.InternalBaseSku).offset(0).limit(30)
        productosFiltrados = query.all()
        productosFiltradosSerizalizados = [
            productoFiltrado.serialize()
            for productoFiltrado
            in productosFiltrados]
        createdResponse = {'productos': productosFiltradosSerizalizados}
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 202)
    finally:
        session.close()
        return createdResponse


def set_info_product_base(productBase, es_nuevo, data, files, session):
    registro, mensajeDeError = agregarBasicosProductBase(
        productBase, es_nuevo, data)
    productBaseProductDimension = data.get('productBaseProductDimensions')
    # Declaracion de variables para dimensiones producto
    registroDimensionesProducto, \
        mensajeErrorDimensionesProducto = agregarDimensiones(
            productBase, ProductBaseProductDimension,
            'ProductBaseProductDimensions',
            productBaseProductDimension, es_nuevo,
            'dimensiones de producto')
    if registroDimensionesProducto.lower() \
            == 'sin dimensiones de producto':
        registro = \
            ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(
                registro, f'{registroDimensionesProducto}')
    else:
        registro = \
            ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
                registro, 'dimensiones de producto',
                registroDimensionesProducto)
    mensajeDeError = \
        ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
            mensajeDeError, 'dimensiones de producto',
            mensajeErrorDimensionesProducto)
    # dimensiones de producto
    # dimensiones de envio
    productBaseShippingDimension = data.get('productBaseShippingDimensions')
    # Declaracion de variables para dimensiones de envio
    registroDimensionesEnvio, \
        mensajeErrorDimensionesEnvio = agregarDimensiones(
            productBase, ProductBaseShippingDimension,
            'ProductBaseShippingDimensions',
            productBaseShippingDimension, es_nuevo,
            'dimensiones de envio')
    if registroDimensionesEnvio.lower() == 'sin dimensiones de envio':
        registro = \
            ownUtils.concatenar_mensaje_retroalimentacion_sin_validacion(
                registro, f'{registroDimensionesEnvio}')
    else:
        registro = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
            registro, 'dimensiones de envio', registroDimensionesEnvio)
    mensajeDeError = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
        mensajeDeError, 'dimensiones de envio', mensajeErrorDimensionesEnvio)
    # dimensiones de envio
    # dimensiones-------------------
    # Atributes----------------------
    attributesProductBase = data.get('attributesProductBase')
    registroAtributos, erroresAtributos = agregarAtributosProductoBase(
        attributesProductBase, productBase)
    registro = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
        registro, 'Atributos de producto', registroAtributos)
    mensajeDeError = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
        mensajeDeError, 'Atributos de producto', erroresAtributos)
    # Atributes----------------------
    # Tags---------------------------
    if data.get('tagsProductBase[]') is None:
        tagsProductBase = None
    else:
        tagsProductBase = data.getlist('tagsProductBase[]')
    registroTags, erroresTags = agregarTagsProductoBase(
        tagsProductBase, productBase)
    registro = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
        registro, 'Atributos de producto', registroTags)
    mensajeDeError = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
        mensajeDeError, 'Tag de producto', erroresTags)
    # Tags---------------------------
    # Images base---------------------
    if data.get('urlBaseToAdd[]') is None:
        urlImagesBase = []
    else:
        urlImagesBase = data.getlist('urlBaseToAdd[]')
    if data.get('imageBaseToAdd[]') is None:
        fileImagesBase = []
        fileImagesBasePosition = []
    else:
        fileImagesBase = data.getlist('imageBaseToAdd[]')
        fileImagesBasePosition = []
        for file in fileImagesBase:
            fileImagesBasePosition.append(data.get(f'{file.filename}'))
    registroImgs, erroresImgs = agregarImagenes(
        fileImagesBase, urlImagesBase, [],
        productBase, ProductBasePhoto,
        'ProductBasePhotos', productBase.InternalBaseSku,
        es_nuevo, session, fileImagesBasePosition)
    registro = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
        registro, 'imagenes base', registroImgs)
    mensajeDeError = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
        mensajeDeError, 'imagenes base', erroresImgs)
    # Images base--------------------
    return registro, mensajeDeError


def update_product(productBase, data, files, session):
    try:
        registro, mensajeDeError = set_info_product_base(
            productBase, False, data, files, session)
        # Variations
        if (productBase.Products == [] and productBase.ProductBase_CustomVariations == []):
            registro = ownUtils.concatenarMensajeRetroalimentacion(
                registro, 'producto sin variaciones')
            product = Product()
            product.InternalSku = productBase.InternalBaseSku
            productBase.Products = [product,]
        else:
            if data.get('variationProduct[]') is None:
                variationProducts = None
            else:
                variationProducts = data.getlist('variationProduct[]')
            registro_variations, \
                mensajeDeError_variations = update_productos_variacion(variationProducts, productBase, files, data, False, session)
            registro = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
                registro, 'productos hijo', registro_variations)
            mensajeDeError = \
                ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
                    mensajeDeError, 'productos hijo',
                    mensajeDeError_variations)
    except Exception as e:
        traceback.print_exc()
        mensajeDeError = str(e)
    finally:
        return registro, mensajeDeError


def create_product(productBase, data, files, session):
    try:
        registro, mensajeDeError = set_info_product_base(
            productBase, True, data, files, session)
        # Variations
        if data.get('productBaseVariation[]') is None:
            variationsAssociatedWithTheProduct = None
        else:
            variationsAssociatedWithTheProduct = data.getlist(
                'productBaseVariation[]')
        if variationsAssociatedWithTheProduct:
            errorvariationAssociatedWithTheProduct = \
                agregarProductBaseVariations(
                    variationsAssociatedWithTheProduct,
                    productBase, session)
            mensajeDeError = \
                ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
                    mensajeDeError,
                    'variaciones de producto base',
                    errorvariationAssociatedWithTheProduct)
            # Asociate variation with product-------------------
            # Variations
            if data.get('variationProduct[]') is None:
                variationProducts = None
            else:
                variationProducts = data.getlist('variationProduct[]')
            registro_variations, \
                mensajeDeError_variations = update_productos_variacion(variationProducts, productBase, files, data, False, session)
            registro = ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
                registro, 'variaciones de producto base', registro_variations)
            mensajeDeError = \
                ownUtils.concatenar_mensaje_retroalimentacion_envuelto(
                    errorvariationAssociatedWithTheProduct,
                    'productos hijo', mensajeDeError_variations)
            # Variations
        else:
            # print('elssse')
            registro = ownUtils.concatenarMensajeRetroalimentacion(
                registro, 'producto sin variaciones')
            product = Product()
            product.InternalSku = productBase.InternalBaseSku
            productBase.Products = [product,]
        # Variations
    except Exception as e:
        mensajeDeError = str(e)
    finally:
        return registro, mensajeDeError


def validar_marca_modelo(brand, model):
    try:
        # Validating required data
        if not (brand and model):
            raise Exception("Marca y modelo son obligatorios")
        brand = str(brand)
        model = str(model)
        if len(brand) > ProductBase.Brand.property.columns[0].type.length:
            raise Exception(
                (f"La marca debe ser menor a "
                 f"{ProductBase.Brand.property.columns[0].type.length}"))
        if len(model) > ProductBase.Model.property.columns[0].type.length:
            raise Exception(
                (f"El modelo debe ser menor a "
                 f"{ProductBase.Model.property.columns[0].type.length}"))
        return None
    except Exception as e:
        return str(e)


@products.route('/api/products/product/sincronizeInfoProductForProductList', methods=['POST'])
def update_info_product_for_product_list():
    try:
        session = ScopedSession()
        try:
            data = request.get_json(force=True)
        except Exception as e:
            raise Exception(
                f'Los datos deben ingresar en formato JSON: {str(e)}')
        lista_producto = data.get('infoProductos')
        # print(json.dumps(lista_producto, indent=4))
        if type(lista_producto) is not list:
            raise Exception('"listaPreciosStock" debe ser una lista')
        added_products = 0
        updated_products = 0
        error_products = 0
        mensaje_error = ''
        # for-------------------------
        for index, product in enumerate(lista_producto, start=1):
            print(index)
            # print(index)
            product = ImmutableMultiDict(product)
            # try_except-----------------------------------------
            try:
                brand = product.get('brand')
                model = product.get('model')
                errors_brand_model = validar_marca_modelo(brand, model)
                if errors_brand_model:
                    raise Exception(errors_brand_model)
                # Generating internal base sku
                internalBaseSku = ownUtils.generate_internal_base_sku(
                    brand, model)
                # Checking if productBase notexist
                productBase = session.query(
                    ProductBase).get(internalBaseSku)
                if not productBase:
                    # Creating productBase object
                    productBase = ProductBase()
                    # Setting required data
                    productBase.InternalBaseSku = internalBaseSku
                    productBase.Brand = brand
                    productBase.Model = model
                    # print('***************')
                    registro, errores = create_product(
                        productBase, product, ImmutableMultiDict(), session)
                    # print('***************')
                    if not errores:
                        session.add(productBase)
                        added_products += 1
                    else:
                        raise Exception(errores)
                else:

                    registro, errores = update_product(
                        productBase, product, ImmutableMultiDict(), session)
                    if not errores:
                        session.add(productBase)
                        updated_products += 1
                        # print('---->Actualizado')
                    else:
                        # print(f'DDDDActualizado>>>{errores}')
                        raise Exception(errores)
            except Exception as e:
                if errors_brand_model:
                    mensaje = (f'{index}: '
                               f'marca o modelo erroneo ->'
                               f'{errors_brand_model}')
                else:
                    mensaje = f'{brand}->{model}: {str(e)}'
                mensaje_error = f'{mensaje_error} | {mensaje}'
                error_products += 1
            # try_except-----------------------------------------
        # for-------------------------
        session.commit()
        adds_updates_sumary = (f'{added_products} agregados,'
                               f'{updated_products} actualizados'
                               f' y {error_products} con error de '
                               f'{len(lista_producto)} productos totales')
        body = {
            'resumen': adds_updates_sumary,
            'errores': mensaje_error
        }
        createdResponse = make_response(jsonify(body), 200)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@products.route('/api/products/product/sincronizeStockAndPriceAtStoreForProductList', methods=['POST'])
def update_stock_cost_for_product_list():
    try:
        session = ScopedSession()
        try:
            add_prices_and_stocks_list = request.get_json(force=True)
        except Exception as e:
            raise Exception(
                f'Los datos deben ingresar en formato JSON: {str(e)}')
        lista_precios_stock = add_prices_and_stocks_list.get(
            'listaPreciosStock')
        updated_products = 0
        error_products = 0
        mensaje_error = ''
        if type(lista_precios_stock) is not list:
            raise Exception('"listaPreciosStock" debe ser una lista')
        for index, costs_stocks_product \
                in enumerate(lista_precios_stock, start=1):
            try:
                print('1')
                exito, info = update_stock_cost_for_product(
                    costs_stocks_product, session)
                print('2')
                if exito:
                    updated_products += 1
                    if info:
                        mensaje = (f'{costs_stocks_product.get("internalSku")}'
                                   f' {info}')
                        mensaje_error = f'{mensaje_error} | {mensaje}'
                else:
                    raise Exception(info)
            except Exception as e:
                if str(e) == 'Es necesario el internalSku':
                    mensaje = f'{index}: {str(e)}'
                else:
                    mensaje = (f'{costs_stocks_product.get("internalSku")}:'
                               f' {str(e)}')
                mensaje_error = f'{mensaje_error} | {mensaje}'
                error_products += 1
        session.commit()
        adds_updates_sumary = (
            f'{updated_products} '
            f'actualizados y {error_products}'
            f'con error de {len(lista_precios_stock)} '
            f'productos totales')
        body = {
            'resumen': adds_updates_sumary,
            'errores': mensaje_error
        }
        createdResponse = make_response(jsonify(body), 200)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': f'{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


def update_stock_cost_for_product(add_prices_and_stocks, session):
    try:
        registro = ''
        internal_sku = add_prices_and_stocks.get('internalSku')
        stocks_costs = add_prices_and_stocks.get('stocks_costs')
        if not internal_sku:
            raise Exception("Es necesario el internalSku")
        product = session.query(Product).get(internal_sku)
        if not product:
            raise Exception("El producto no existe")
        # Revisar back popilate product. productbase
        product.Product_SupplierStores = []
        if type(stocks_costs) is not list:
            raise Exception("sotcks_costs debe ser una lista")
        dict_stores = {}
        for stock_cost in stocks_costs:
            id_store = stock_cost.get('idStore')
            store = session.query(SupplierStore).get(id_store)
            if store:
                dict_stores[store.SupplierStoreId] = store
        product_supplier_stores = []
        for sotck_cost in stocks_costs:
            exito, info = manage_stock_cost_supplier_store(
                sotck_cost, dict_stores)
            if exito:
                product_supplier_stores.append(info)
            else:
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro, info)
        product.Product_SupplierStores = product_supplier_stores
        return True, registro
    except Exception as e:
        return False, str(e)


def manage_stock_cost_supplier_store(stock_cost, dict_stores):
    try:
        id_store = stock_cost.get('idStore')
        store = dict_stores.get(id_store)
        if not store:
            raise Exception("El almacén no existe")
        cost = stock_cost.get('cost')
        stock = stock_cost.get('stock')
        if not stock and not cost:
            raise Exception('Sin stock ni precio')
        else:
            if stock != 0 or stock is not None:
                error = ownUtils.revisarIntPositivo(stock)
                if error:
                    raise Exception(f'El stock debe ser entero: {error}')
            if cost is not None:
                error = ownUtils.revisarFloatPositivo(cost)
                if  error:
                    raise Exception('El costo debe ser flotante')

        product_supplier_store = Product_SupplierStore()
        # product_supplier_store.Product = product
        product_supplier_store.SupplierStore = store
        product_supplier_store.Stock = stock
        product_supplier_store.Cost = cost
        return True, product_supplier_store
    except Exception as e:
        return False, print(e)


# return str(e)
# return None


@products.route('/api/products/productBase/new', methods=['POST'])
# @products.arguments(ProductVariationSchema)
# @jwt_required()
def create_new_product():
    try:
        session = ScopedSession()
        # Get data and files from formData
        data = request.form
        files = request.files
        print(files)
        # print(type(files))
        print('oooooooooooo')
        print(data)
        print('oooooooooooo')
        print('oooooooooooo')
        # Validating required data
        brand = data.get('brand')
        model = data.get('model')
        # print(brand)
        # print(model)
        if not (brand and model):
            raise Exception("Marca y modelo son obligatorios")
        brand = str(brand)
        model = str(model)
        if len(brand) > ProductBase.Brand.property.columns[0].type.length:
            raise Exception(
                f"La marca debe ser menor a "
                f"{ProductBase.Brand.property.columns[0].type.length}")
        if len(model) > ProductBase.Model.property.columns[0].type.length:
            raise Exception(
                f"El modelo debe ser menor a "
                f"{ProductBase.Model.property.columns[0].type.length}")
        # Generating internal base sku
        internalBaseSku = ownUtils.generate_internal_base_sku(brand, model)
        # Checking if productBase notexist
        productBase = session.query(ProductBase).get(internalBaseSku)
        if not productBase:
            # Creating productBase object
            productBase = ProductBase()
            # Setting required data
            productBase.InternalBaseSku = internalBaseSku
            productBase.Brand = brand
            productBase.Model = model
            # Checking basic info
            # print('sssssssssssssssssssssssssssssss1')
            registro, mensajeDeError = agregarBasicosProductBase(
                productBase, True, data)
            # dimensiones-------------------
            # dimensiones de producto
            productBaseProductDimension = data.get(
                'productBaseProductDimensions')
            # Declaracion de variables para dimensiones producto
            # print(productBaseProductDimension)
            registroDimensionesProducto, \
                mensajeErrorDimensionesProducto = agregarDimensiones(
                    productBase, ProductBaseProductDimension,
                    'ProductBaseProductDimensions',
                    productBaseProductDimension,
                    True, 'dimensiones de producto')
            if registroDimensionesProducto != '':
                if registroDimensionesProducto.lower() \
                        == 'sin dimensiones de producto':
                    registro = ownUtils.concatenarMensajeRetroalimentacion(
                        registro, f'{registroDimensionesProducto}')
                else:
                    registro = ownUtils.concatenarMensajeRetroalimentacion(
                        registro, (f'dimensiones de producto('
                                   f'{registroDimensionesProducto})'))
            if mensajeErrorDimensionesProducto != '':
                mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                    mensajeDeError, (f'dimensiones de producto('
                                     f'{mensajeErrorDimensionesProducto})'))
            # dimensiones de producto
            # dimensiones de envio
            productBaseShippingDimension = data.get(
                'productBaseShippingDimensions')
            # Declaracion de variables para dimensiones de envio
            # print('sssssssssssssssssssssssssssssss3')

            registroDimensionesEnvio, \
                mensajeErrorDimensionesEnvio = agregarDimensiones(
                    productBase, ProductBaseShippingDimension,
                    'ProductBaseShippingDimensions',
                    productBaseShippingDimension,
                    True, 'dimensiones de envio')
            if registroDimensionesEnvio != '':
                if registroDimensionesEnvio.lower() \
                        == 'sin dimensiones de envio':
                    registro = ownUtils.concatenarMensajeRetroalimentacion(
                        registro, f'{registroDimensionesEnvio}')
                else:
                    registro = ownUtils.concatenarMensajeRetroalimentacion(
                        registro, (f'dimensiones de envio('
                                   f'{registroDimensionesEnvio})'))
            if mensajeErrorDimensionesEnvio != '':
                mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                    mensajeDeError, (f'dimensiones de envio('
                                     f'{mensajeErrorDimensionesEnvio})'))
            # dimensiones de envio
            # dimensiones-------------------
            # Atributes----------------------
            attributesProductBase = data.get('attributesProductBase')
            registroAtributos, erroresAtributos = agregarAtributosProductoBase(
                attributesProductBase, productBase)
            if registroAtributos != '':
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro, f'Atributos de producto({registroAtributos})')
            if erroresAtributos != '':
                mensajeDeError = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        mensajeDeError, (f'Atributos de producto('
                                         f'{erroresAtributos})'))
            # Atributes----------------------
            # Tags---------------------------
            tagsProductBase = data.getlist('tagsProductBase[]')
            registroTags, erroresTags = agregarTagsProductoBase(
                tagsProductBase, productBase)
            if registroTags != '':
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro, f'Atributos de producto({registroTags})')
            if erroresTags != '':
                mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                    mensajeDeError, f'Tag de producto({erroresTags})')
            # Tags---------------------------
            # Images base---------------------
            urlImagesBase = data.getlist('urlBaseToAdd[]')
            fileImagesBase = files.getlist('imageBaseToAdd[]')
            fileImagesBasePosition = []
            for file in fileImagesBase:
                fileImagesBasePosition.append(data.get(f'{file.filename}'))
            # print('sssssssssssssssssssssssssssssss4')
            # print(urlImagesBase)
            # print(fileImagesBase)
            # print('eeeeeeeee')
            print('...............................')
            registroImgs, erroresImgs = agregarImagenes(
                fileImagesBase, urlImagesBase, [],
                productBase, ProductBasePhoto, 'ProductBasePhotos',
                productBase.InternalBaseSku, True, session,
                fileImagesBasePosition)
            print('...............................')
            if registroImgs != '':
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro, f'imagenes base({registroImgs})')
            if erroresImgs != '':
                mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                    mensajeDeError, f'imagenes base({erroresImgs})')
            print('9999999999999999pppp')
            # Images base--------------------
            # Variations
            variationsAssociatedWithTheProduct = data.getlist(
                'productBaseVariation[]')
            if (variationsAssociatedWithTheProduct):
                # print('----------------------------------')
                # print(variationsAssociatedWithTheProduct)
                # print('-------------------------------------')
                # Asociate variation with product-------------------
                # print('sssssssssssssssssssssssssssssss5')
                errorvariationAssociatedWithTheProduct \
                    = agregarProductBaseVariations(
                        variationsAssociatedWithTheProduct, productBase, session)
                if errorvariationAssociatedWithTheProduct != '':
                    mensajeDeError = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            mensajeDeError,
                            (f'variaciones('
                             f'{errorvariationAssociatedWithTheProduct})'))
                # Asociate variation with product-------------------
                # Looping over variationProduct------------------
                variationProducts = data.getlist('variationProduct[]')
                if variationProducts:
                    print('iffffffffffff')
                    registroVariationProducts, \
                        errorVariationProducts = update_productos_variacion(variationProducts, productBase, files, data, True, session)
                    print('sssssssssssssssssssssssssssssss8')
                    if errorVariationProducts != '':
                        mensajeDeError = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeDeError,
                                (f'productos variación'
                                 f'({errorVariationProducts})'))
                    if registroVariationProducts != '':
                        registro = ownUtils.concatenarMensajeRetroalimentacion(
                            registro,
                            (f'productos variación'
                             f'({registroVariationProducts})'))
                    print('sssssssssssssssssssssssssssssss9')
                # Looping over variationProduct------------------
                else:
                    print('ellllllllllllse')
                    registro = ownUtils.concatenarMensajeRetroalimentacion(
                        registro, 'ningun producto variación dado de alta')
                # print('sssssssssssssssssssssssssssssss10')
            else:
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro, 'producto sin variaciones')
                product = Product()
                product.InternalSku = productBase.InternalBaseSku
                productBase.Products = [product,]
            # Variations
            # database ORM object
            if mensajeDeError == '':
                # insert product
                session.add(productBase)
                session.commit()
                createdResponse = make_response(
                    jsonify({'mensaje': (f'Producto registrado '
                                         f'exitosamente. {registro}')}), 200)
            else:
                createdResponse = make_response(jsonify(
                    {'errores': (f'No se registro el producto, '
                                 f'errores:{mensajeDeError}')}), 500)
        else:
            # returns 202 if user already exists
            createdResponse = make_response(
                jsonify({'errores': 'Producto ya existe'}), 202)
    except Exception as e:
        session.rollback()
        traceback.print_exc()
        print(str(e))
        createdResponse = make_response(jsonify({'errores': f'{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@products.route('/api/products/productBase/actualizar', methods=['PUT'])
# @products.arguments(ProductUpdateSchema)
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def update_product_end_point(session):
    # Get data and files from formData
    data = request.form
    files = request.files

    print('=======================================_________________________________________---')
    print(data)
    print('--')
    print(files)
    print('=======================================_________________________________________---')
    # Validating required data
    internalBaseSku = data.get('internalBaseSku')
    if not internalBaseSku:
        raise Exception("sku interno obligatorio (internalBaseSku)")
    # checking if productBase exists
    productBase = session.query(ProductBase).get(internalBaseSku)
    if productBase:
        # Checking productbasebasic info
        registro, mensajeDeError = agregarBasicosProductBase(
            productBase, False, data)
        # dimensiones-------------------
        # dimensiones de producto
        productBaseProductDimension = data.get(
            'productBaseProductDimensions')
        # Declaracion de variables para dimensiones producto
        registroDimensionesProducto, \
            mensajeErrorDimensionesProducto = agregarDimensiones(
                productBase, ProductBaseProductDimension,
                'ProductBaseProductDimensions',
                productBaseProductDimension,
                False, 'dimensiones de producto')
        if registroDimensionesProducto != '':
            if (registroDimensionesProducto.lower() == 'sin dimensiones de producto'):
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro,
                    f'{registroDimensionesProducto}')
            else:
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro,
                    (f'dimensiones de producto'
                     f'({registroDimensionesProducto})'))
        if mensajeErrorDimensionesProducto != '':
            mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                mensajeDeError,
                (f'dimensiones de producto'
                 f'({mensajeErrorDimensionesProducto})'))
        # dimensiones de producto
        # dimensiones de envio
        productBaseShippingDimension = data.get(
            'productBaseShippingDimensions')
        # Declaracion de variables para dimensiones de envio
        registroDimensionesEnvio, \
            mensajeErrorDimensionesEnvio = agregarDimensiones(
                productBase, ProductBaseShippingDimension,
                'ProductBaseShippingDimensions',
                productBaseShippingDimension,
                False, 'dimensiones de envio')
        if registroDimensionesEnvio != '':
            if (registroDimensionesEnvio.lower() == 'sin dimensiones de envio'):
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro,
                    f'{registroDimensionesEnvio}')
            else:
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro,
                    f'dimensiones de envio({registroDimensionesEnvio})')
        if mensajeErrorDimensionesEnvio != '':
            mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                mensajeDeError,
                f'dimensiones de envio({mensajeErrorDimensionesEnvio})')
        # dimensiones de envio
        # dimensiones-------------------
        # Atributes----------------------
        attributesProductBase = data.get('attributesProductBase')
        registroAtributos, erroresAtributos = agregarAtributosProductoBase(
            attributesProductBase, productBase)
        if registroAtributos != '':
            registro = ownUtils.concatenarMensajeRetroalimentacion(
                registro, f'Atributos de producto({registroAtributos})')
        if erroresAtributos != '':
            mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                mensajeDeError,
                f'Atributos de producto({erroresAtributos})')
        # Atributes----------------------
        # Tags---------------------------
        tagsProductBase = data.getlist('tagsProductBase[]')
        registroTags, erroresTags = agregarTagsProductoBase(
            tagsProductBase, productBase)
        if registroTags != '':
            registro = ownUtils.concatenarMensajeRetroalimentacion(
                registro, f'Atributos de producto({registroTags})')
        if erroresTags != '':
            mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                mensajeDeError, f'Tag de producto({erroresTags})')
        # Tags---------------------------
        # Images base---------------------
        urlImagesBase = data.getlist('urlBaseToAdd[]')
        fileImagesBase = files.getlist('imageBaseToAdd[]')
        fileImagesBasePosition = []
        for file in fileImagesBase:
            fileImagesBasePosition.append(data.get(f'{file.filename}'))
        registroImgs, erroresImgs = agregarImagenes(
            fileImagesBase, urlImagesBase,
            [], productBase, ProductBasePhoto,
            'ProductBasePhotos', productBase.InternalBaseSku,
            False, session, fileImagesBasePosition)
        if registroImgs != '':
            registro = ownUtils.concatenarMensajeRetroalimentacion(
                registro, f'imagenes base({registroImgs})')
        if erroresImgs != '':
            mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                mensajeDeError, f'imagenes base({erroresImgs})')
        # Images base--------------------
        # Variations
        # Looping over variationProduct------------------
        variationProducts = data.getlist('variationProduct[]')
        if variationProducts:
            # print('sssssssssssssssssssssssssssssss7')
            registroVariationProducts,\
                errorVariationProducts = update_productos_variacion(variationProducts, productBase, files, data, False, session)
            # print('sssssssssssssssssssssssssssssss8')
            if errorVariationProducts != '':
                mensajeDeError = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        mensajeDeError,
                        f'productos variación({errorVariationProducts})')
            if registroVariationProducts != '':
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro,
                    f'productos variación({registroVariationProducts})')
            # print('sssssssssssssssssssssssssssssss9')
        # Looping over variationProduct------------------
        else:
            registro = ownUtils.concatenarMensajeRetroalimentacion(
                registro, 'ningun producto variación dado de alta')
        # Variations
        if mensajeDeError == '':
            ownUtils.commit_catching_dependency_rule(session)
            return make_response(
                jsonify({'mensaje': (f'Producto actualizado '
                                     f'exitosamente. {registro}')}), 200)
        else:
            return make_response(jsonify(
                {'errores': (f'No se actualizo el producto,'
                             f' errores:{mensajeDeError}')}), 500)
    else:
        # returns 202 if user already exists
        return make_response(
            jsonify({'errores': 'Producto no existe'}), 202)


@products.route('/api/products/productBase/eliminar', methods=['DELETE'])
# @products.arguments(InternalSkuSchema)
# @jwt_required()
@ownUtils.my_decorator_http_manage
def delete_product(session):
    # gets atributtes from product
    data = ownUtils.get_info_from_json(request)
    # print(data)
    # gets internalSku

    internalSku = data.get('internalSku', None)
    if not internalSku:
        raise Exception("Petición sin sku interno base")
    # checking for existing user
    jsonP, status = ownUtils.manage_delete_product(session, ProductBase, internalSku)
    try:
        directory = os.path.\
            join(
                current_app.config['PRODUCT_IMAGES_FOLDER'],
                internalSku)
        if os.path.exists(directory):
            shutil.rmtree(directory)
    except Exception as e:
        print(e)
    return make_response(jsonify(jsonP), status)


@products.route('/api/products/product/addProductToStore', methods=['POST'])
# @products.arguments(ProductToStoreSchema)
# @jwt_required()
def add_product_to_store():  # ProductToStoreSchema
    try:
        session = ScopedSession()
        addPriceAndStock = request.get_json(force=True)
        internalSku = addPriceAndStock.get('internalSku', None)
        idStore = addPriceAndStock.get('idStore', None)
        if not (internalSku and idStore):
            raise Exception(
                "Es necesario el sku interno(internalSku)"
                " y el id de almacén(idStore)")

        stock = addPriceAndStock.get('stock')
        cost = addPriceAndStock.get('cost')
        internallocation = addPriceAndStock.get(
            'internalLocation')

        if not stock and not (stock.isnumeric()):
            raise Exception("El stock no es un numero")

        validate = re.match(r"^[1-9]\d*(\.\d+)?$", cost)
        if cost != "No Actualizar" and not (validate):
            raise Exception("El costo no es un numero valido")

        internallocation = re.sub(' +', ' ', internallocation)

        validate2 = re.match(
            r"^(([A-Z])+(\s+)?[-](\s+)?[0-9]\d*((([A-Z])+)?)"
            r"((([A-Z])+)?(\s+[-])?\s+([Z])[1-3])?)$",
            internallocation)
        if internallocation != "No Actualizar" and not (validate2):
            raise Exception(
                "La ubicacion del producto no tiene un formato valido")

        product = session.query(Product).get(internalSku)

        if not product:
            raise Exception("El producto no existe")

        store = session.query(Store).get(idStore)
        if not store:
            raise Exception("El almacén no existe")

        product_store = session.query(
            Product_Store).get((internalSku, idStore))

        if not product_store:
            product_store = Product_Store(stock, cost, internallocation)
            product_store.Product = product
            product_store.Store = store
            session.commit()
            createdResponse = make_response(
                jsonify({'mensaje': ('Stock,precio y '
                                     'ubicación agregado '
                                     'exitosamente.')}), 200)
        else:
            createdResponse = make_response(jsonify(
                {'mensaje': (
                    'El producto ya esta dado de alta en este almacen,'
                    ' si deseas actualizar utiliza products/product/'
                    'updateProductAtStore [PUT]')}), 202)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'errores':
                     f'Ocurrio un error inesperado: {str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@products.route('/api/products/product/setInternalLocationAtStore', methods=['PUT'])
# @products.arguments(ProductToStoreSchema)
# @jwt_required()
def set_internal_location_at_store():  # ProductToStoreSchema
    try:

        session = ScopedSession()
        product_store_info = request.get_json(force=True)
        internalSku = product_store_info.get('internalSku', None)
        idStore = product_store_info.get('idStore', None)
        if not (internalSku or idStore):
            raise Exception(
                "Es necesario el sku interno(internalSku)"
                " y el id de almacén(idStore)")
        internallocation = product_store_info.get('internallocation')
        internallocation = re.sub(' +', ' ', internallocation)
        validate2 = re.match(
            r"^(([A-Z])+(\s+)?[-](\s+)?[0-9]\d*((([A-Z])+)?)"
            r"((([A-Z])+)?(\s+[-])?\s+([Z])[1-3])?)$",
            internallocation
        )
        if internallocation != "No Actualizar" and not (validate2):
            raise Exception(
                "La ubicacion del producto no tiene un formato valido")
        product = session.query(Product).get(internalSku)
        if not product:
            raise Exception("El producto no existe")
        store = session.query(Store).get(idStore)
        if not store:
            raise Exception("El almacén no existe")
        product_store = session.query(
            Product_Store).get((internalSku, idStore))

        if product_store:
            session.commit()
            createdResponse = make_response(
                jsonify({'mensaje': (
                         'Stock,precio y ubicación '
                         'actualizado exitosamente.')}), 200)
        else:
            createdResponse = make_response(jsonify(
                {'mensaje': ('El producto no esta dado de '
                             'alta en este almacen,'
                             ' si deseas dar de alta en este almacen utiliza '
                             'products/product/updateProductAtStore '
                             '[POST]')}), 202)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'errores':
                     f'Ocurrio un error inesperado: {str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@products.route('/api/products/product/updateProductAtStore', methods=['PUT'])
# @products.arguments(ProductToStoreSchema)
# @jwt_required()
def update_product_at_store():  # ProductToStoreSchema
    try:
        session = ScopedSession()
        addPriceAndStockPrice = request.get_json(force=True)
        internalSku = addPriceAndStockPrice.get('internalSku', None)
        idStore = addPriceAndStockPrice.get('idStore', None)
        if not (internalSku and idStore):
            raise Exception(
                "Es necesario el sku interno(internalSku)"
                " y el id de almacén(idStore)")

        stock = addPriceAndStockPrice.get('stock')
        cost = addPriceAndStockPrice.get('cost')
        internallocation = addPriceAndStockPrice.get(
            'internallocation')

        if not stock and not (stock.isnumeric()):
            raise Exception("El stock no es un numero")

        validate = re.match(r"^[1-9]\d*(\.\d+)?$", cost)
        if cost != "No Actualizar" and not (validate):
            raise Exception("El costo no es un numero valido")

        internallocation = re.sub(' +', ' ', internallocation)

        validate2 = re.match(
            r"^(([A-Z])+(\s+)?[-](\s+)?[0-9]\d*((([A-Z])+)?)"
            r"((([A-Z])+)?(\s+[-])?\s+([Z])[1-3])?)$",
            internallocation
        )

        if internallocation != "No Actualizar" and not (validate2):
            raise Exception(
                "La ubicacion del producto no tiene un formato valido")

        product = session.query(Product).get(internalSku)
        if not product:
            raise Exception("El producto no existe")

        store = session.query(Store).get(idStore)
        if not store:
            raise Exception("El almacén no existe")

        product_store = session.query(
            Product_Store).get((internalSku, idStore))

        if product_store:
            product_store.Stock = stock
            product_store.Cost = cost
            product_store.InternalLocation = internallocation
            session.commit()
            createdResponse = make_response(
                jsonify({'mensaje': (
                         'Stock,precio y ubicación '
                         'actualizado exitosamente.')}), 200)
        else:
            createdResponse = make_response(jsonify(
                {'mensaje': ('El producto no esta dado de '
                             'alta en este almacen,'
                             ' si deseas dar de alta en este almacen utiliza '
                             'products/product/updateProductAtStore '
                             '[POST]')}), 202)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'errores':
                     f'Ocurrio un error inesperado: {str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@products.route('/api/products/subirProductosBaseMasivo', methods=['POST'])
# @jwt_required()
def procesar_xlsx_nuevos_productos():
    try:
        session = ScopedSession()
        # print(request.files)
        if 'file' not in request.files:
            # regresar error
            raise Exception('No enviaste ningun archivo con la clave file.')
        file = request.files.get('file')
        if file.filename == '':
            raise Exception('La clave file contiene archivo vacío')
        if (not (file and ownUtils.allowed_file_massive_update(file.filename) and ownUtils.allowed_name_massive_update(file.filename))):
            raise Exception('La extension o el nombre no es permitido')
        try:
            result, mensaje = procesar_productos_xlsx(file)
            if result:
                session.commit()
                createdResponse = make_response(jsonify(
                    {'mensaje':
                     (f'Los productos fueron subidos '
                      f'de manera correcta.{mensaje}')}), 200)
            else:
                raise Exception(
                    f'No se realizó ningún cambio, '
                    f'existen los siguientes errores: {mensaje}')
        except Exception as e:
            raise Exception(str(e))
    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@products.route('/api/products/getVariations')
# @products.arguments(ProductsFiltroSchema,location="query")
# @jwt_required()
def return_variations():
    try:
        session = ScopedSession()
        query = armarQueryFiltrosVariaciones(session)
        variaciones = query.all()
        variacionesSerizalizadas = [variacion.serialize()
                                    for variacion in variaciones]
        createdResponse = {'variaciones': variacionesSerizalizadas}
    except Exception as e:
        print(str(e))
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@products.route('/api/products/getPossibleValuesOfVariations')
# @products.arguments(ProductsFiltroSchema,location="query")
# @jwt_required()
def get_possible_values_of_variations():
    try:
        session = ScopedSession()
        variations = request.args.get('variations')
        query = armarQueryFiltrosVariaciones(session)
        query = ObtenerVariacionesFiltradasConValores(query, variations)
        variacionesFiltradasConValores = query.all()
        variacionesFiltradasConValoresSerializadas = [
            variacionFiltradaConValores.serialize(scope={'customVariationValues': {}})
            for variacionFiltradaConValores in variacionesFiltradasConValores]
        createdResponse = {
            'variacionesFiltradasConValores':
            variacionesFiltradasConValoresSerializadas}
    except Exception as e:
        print(str(e))
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


"""@products.route('/api/products/product/getInternalSkuDictFromModelList')
def get_internal_sku_dict_from_model_list():
    try:
        session = ScopedSession()
        try:
            data = request.json
            model_list = data['model_list']
        except Exception as e:
            raise Exception(
                f'El body debe ser un JSON '
                f'y contener la clave model_list: {str(e)}')
        dict_model_internalsku = {}
        for model in model_list:
            model = str(model).strip()
            query = session.query(ProductBase)
            query = query.filter(ProductBase.Model == model)
            products = query.all()
            if len(products) == 1:
                aux_internal_sku = products[0].InternalBaseSku
            else:
                aux_internal_sku = None
            dict_model_internalsku[model] = aux_internal_sku
        createdResponse = {'dict_from_model_list': dict_model_internalsku}
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 202)
    finally:
        session.close()
        return createdResponse


@products.route(
    '/api/products/product/sincronizeStockAndPriceForProductAtStore',
    methods=['POST'])
def sincronize_stock_cost_for_product():
    try:
        print('oooooooooooooooooooo')
        session = ScopedSession()
        try:
            add_prices_and_stocks = request.get_json(force=True)
        except Exception as e:
            raise Exception(
                f'Los datos deben ingresar en formato JSON: {str(e)}')

        exito, info = update_stock_cost_for_product(add_prices_and_stocks, session)
        if not exito:
            raise Exception(info)
        session.commit()

        createdResponse = make_response(
            jsonify({'mensaje': 'Stock y precio actualizados.'}), 200)
    except Exception as e:
        print('009')
        traceback.print_exc()
        print('009')
        session.rollback()
        createdResponse = make_response(jsonify({'errores': f'{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@products.route(
    '/api/products/product/setCalculatedCost',
    methods=['PUT'])
def set_calculated_cost():
    try:
        session = ScopedSession()
        try:
            new_cost = request.get_json(force=True)
        except Exception as e:
            raise Exception(
                f'Los datos deben ingresar en formato JSON: {str(e)}')
        internal_sku = new_cost.get('internalSku')
        if not internal_sku:
            raise Exception('internalSku obligatorio')
        product = session.query(Product).get(internal_sku)
        if not product:
            raise Exception('El producto no esta dado de alta')
        if new_cost.get('calculated_cost'):
            calculated_cost = new_cost.get('calculated_cost')
            product_cost = product.ProductCost
            # if not product_cost:
            #     product_cost = ProductCost()
            product_cost.CalculatedCost = calculated_cost
            product.ProductCost = product_cost
        session.commit()
        createdResponse = make_response(
            jsonify({'mensaje': 'Precios actualizados'}), 200)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': f'{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@products.route('/api/calculateInternalSkuTest')
@ownUtils.my_decorator_http_manage
def get_internalBaseSku(session):
    internalBaseSku = "CACUCA"
    variations = {"1": "2"}
    print(variations)
    internalSku = ownUtils.generate_internal_sku(internalBaseSku, variations)
    return {"internalSku": internalSku}


@products.route('/api/products/product/<string:internalSku>', methods=['POST'])
# @jwt_required()
@ownUtils.my_decorator_http_manage
def get_product_by_id(internalSku,session):
    product = session.query(Product).get(internalSku)
    if not product:
        return make_response(jsonify({'error': "sku interno no encontrado"}), 404)
    scope = request.args.get('scope')
    if not scope:
        scope = "basic"
    if scope == 'basic':
        scope_dict = {}
    if scope == 'extended':
        scope_dict = {}
    elif scope == 'stock':
        scope_dict = {
            "product_stores":{},
            "product_supplierStores": {}
        }
    elif scope == 'publications':
        scope_dict = {}
    else:
        return make_response(jsonify({'error': "scope no válido"}), 409)
    product_serialized = product.serialize(scope=scope_dict)
    return make_response(jsonify({'product': product_serialized}), 200)


@products.route('/api/products/product/<string:internalSku>', methods=['PUT'])
# @jwt_required()
@ownUtils.my_decorator_http_manage
def get_product_by_id(internalSku,session):
    product = session.query(Product).get(internalSku)
    if not product:
        return make_response(jsonify({'error': "sku interno no encontrado"}), 404)
    scope = request.args.get('scope')
    if not scope:
        scope = "basic"
    if scope == 'basic':
        scope_dict = {}
    if scope == 'extended':
        scope_dict = {}
    elif scope == 'stock':
        scope_dict = {
            "product_stores":{},
            "product_supplierStores": {}
        }
    elif scope == 'publications':
        scope_dict = {}
    else:
        return make_response(jsonify({'error': "scope no válido"}), 409)
    product_serialized = product.serialize(scope=scope_dict)
    return make_response(jsonify({'product': product_serialized}), 200)


@products.route('/api/products/product/<string:internalSku>', methods=['DELETE'])
# @jwt_required()
@ownUtils.my_decorator_http_manage
def get_product_by_id(internalSku,session):
    product = session.query(Product).get(internalSku)
    if not product:
        return make_response(jsonify({'error': "sku interno no encontrado"}), 404)
    scope = request.args.get('scope')
    if not scope:
        scope = "basic"
    if scope == 'basic':
        scope_dict = {}
    if scope == 'extended':
        scope_dict = {}
    elif scope == 'stock':
        scope_dict = {
            "product_stores":{},
            "product_supplierStores": {}
        }
    elif scope == 'publications':
        scope_dict = {}
    else:
        return make_response(jsonify({'error': "scope no válido"}), 409)
    product_serialized = product.serialize(scope=scope_dict)
    return make_response(jsonify({'product': product_serialized}), 200)

@products.route('/api/products/product/<string:internalSku>', methods=['PATCH'])
# @jwt_required()
@ownUtils.my_decorator_http_manage
def get_product_by_id(internalSku,session):
    product = session.query(Product).get(internalSku)
    if not product:
        return make_response(jsonify({'error': "sku interno no encontrado"}), 404)
    scope = request.args.get('scope')
    if not scope:
        scope = "basic"
    if scope == 'basic':
        scope_dict = {}
    if scope == 'extended':
        scope_dict = {}
    elif scope == 'stock':
        scope_dict = {
            "product_stores":{},
            "product_supplierStores": {}
        }
    elif scope == 'publications':
        scope_dict = {}
    else:
        return make_response(jsonify({'error': "scope no válido"}), 409)
    product_serialized = product.serialize(scope=scope_dict)
    return make_response(jsonify({'product': product_serialized}), 200)
"""


def return_scope_dict_productBase(scope):
    if not scope:
        scope = "basic"
    if scope == 'basic':  # basic
        scope_dict = {}
    elif scope == 'extended':  # extended
        scope_dict = productBase_extended
    elif scope == 'products':  # products
        scope_dict = {'products': {}}
    elif scope == 'extended-products_extended':  # products_extended
        scope_dict = products_extended
    elif scope == 'extended-products_extended-publications':  # products_extended_publications
        scope_dict = products_extended_publications
    elif scope == 'extended-products_extended-publications':  # products_extended_publications
        scope_dict = products_extended_publications()
    elif scope == 'products-stores':  # product_stores
        scope_dict = product_stores
    elif scope == 'extended-products_extended-stores':  # product_stores_extended
        scope_dict = product_stores_extended
    elif scope == 'consolidated_info':  # consolidated_info
        scope_dict = consolidated_info
    else:
        raise Exception('El scope no es valido')
    return scope_dict


def return_scope_dict_product(scope):
    if not scope:
        scope = "basic"
    if scope == 'basic':
        scope_dict = {}
    elif scope == 'extended-extras-productBase':
        scope_dict = extended0productBase0extras
    elif scope == 'stores':
        scope_dict = stores_zone
    elif scope == 'publications':
        scope_dict = publications
    elif scope == 'extended_stores':
        scope_dict = extended_stores
    elif scope == 'extended_publications':
        scope_dict = extended_publications
    else:
        raise Exception('El scope no es valido')
    return scope_dict


def validate_offset(offset, nc):
    if ((offset is not None and offset != "") and (nc is not None and nc != "")):
        if (ownUtils.revisar_int_0_o_mayor(offset) and not ownUtils.revisarIntPositivo(nc)):
            offset = int(offset)
            nc = int(nc)
        else:
            raise Exception("offset y nc deben ser enteros nc mayor que 0 y offset 0 o mayor")
    else:
        offset = 0
        nc = 30
    return offset, nc


@products.route('/api/pruebasss')
@ownUtils.my_decorator_http_manage
def pruebass(session):
    productsBase = session.query(ProductBase)
    print('---------------------------------------------------------')
    products_base_serialized = []
    for productBase in productsBase:
        # print(type(productBase.Products))
        productBase_serialized = {
            **productBase.serialize(),
            'products': [product.serialize() for product in productBase.Products[:2]]  # revisar
        }
        print('2222222222222222222')
        print(productBase_serialized)
        print('2222222222222222222')
        products_base_serialized.append(productBase_serialized)
    print('---------------------------------------------------------')
    print('9999999999999')
    print(products_base_serialized)
    print('9999999999999')
    return make_response(jsonify({'productos': 'productos_filtrados_json'}), 200)


@products.route('/api/products/fuzzyProductsBaseFiltro')
@ownUtils.my_decorator_http_manage
def return_products_base_filtrofuzzy(session):
    search = request.args.get('search')
    offset = request.args.get('offset')
    nc = request.args.get('next')
    scope = request.args.get('scope')
    offset, nc = validate_offset(offset, nc)
    productos_filtrados = fuzzyProductsBaseSearch(session, search).offset(offset).limit(nc)
    scope_dict = return_scope_dict_productBase(scope)
    productos_filtrados_json = [miniproduct.serialize(scope=scope_dict) for miniproduct in productos_filtrados]
    print('productos_filtrados_json----------------------------')
    print(productos_filtrados_json)
    print('productos_filtrados_json-----------------')
    return make_response(jsonify({'productos': productos_filtrados_json}), 200)


@products.route('/api/products/productBase/<internalBaseSku>')
# @jwt_required()
@ownUtils.my_decorator_http_manage
def get_info_product(internalBaseSku, session):
    scope = request.args.get('scope')
    product_base = ownUtils.validate_if_object_exists(session, 'internalBaseSku', 'native', internalBaseSku, ProductBase)
    scope_dict = return_scope_dict_productBase(scope)
    return make_response(jsonify({'producto': product_base.serialize(scope_dict)}), 200)


@products.route('/api/products/product/<string:internalSku>', methods=['GET'])
# @jwt_required()
@ownUtils.my_decorator_http_manage
def get_product_by_id(internalSku, session):
    product = ownUtils.validate_if_object_exists(session, 'internalSku', 'native', internalSku, Product)
    scope = request.args.get('scope')
    scope_dict = return_scope_dict_product(scope)
    product_serialized = product.serialize(scope=scope_dict)
    return make_response(jsonify({'product': product_serialized}), 200)


@products.route('/api/products/product', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def post_product_by_id(session):
    data = request.form
    files = request.files
    variationProduct_request, data = inmutableDictToDictPost(data)
    files = manageFiles(files)
    if not variationProduct_request:
        raise Exception("Sin producto variación")
    internal_base_sku = variationProduct_request.get('internalBaseSku')
    internal_sku = ownUtils.generate_internal_sku(internal_base_sku, variationProduct_request.get("variations"))
    condition_variation_id = get_condition_conditionVariation(session)
    product = session.get(Product, internal_sku)
    if product:
        return make_response(jsonify({'error': "Esta variación ya existe"}), 409)
    productBase = ownUtils.validate_if_object_exists(session, "internalBaseSku", "native", internal_base_sku, ProductBase)
    register_variation_product(session, 1, variationProduct_request, productBase, condition_variation_id, files, data, True)
    session.commit()
    return make_response(
        jsonify({'mensaje': ('Producto creado '
                             'exitosamente.')}), 200)


@products.route('/api/products/product/<string:internalSku>', methods=['PUT'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def put_product_by_id(internalSku, session):
    data = request.form
    files = request.files
    variationProduct_request, data = inmutableDictToDictPut(data)
    files = manageFiles(files)
    if not variationProduct_request:
        raise Exception("Sin producto variación")
    product = ownUtils.validate_if_object_exists(session, 'internalSku', 'native', internalSku, Product)
    update_product_variation(product, variationProduct_request, files, data, True, session)
    ownUtils.commit_catching_dependency_rule(session)
    return make_response(
        jsonify({'mensaje': ('Producto actualizado '
                             'exitosamente.')}), 200)


def inmutableDictToDictPost(inmutableDict_request):
    dict_return, data = inmutableDictToDictPut(inmutableDict_request)
    dict_return['internalBaseSku'] = inmutableDict_request.get('internalBaseSku')
    dict_return['variations'] = ownUtils.catched_json_loads(inmutableDict_request.get('variations'))
    return dict_return, data


def inmutableDictToDictPut(inmutableDict_request):
    dict_return = {}
    imageToAddPosition = []
    for key in inmutableDict_request:
        if key == 'urlToAdd[]':
            urls_to_add = []
            for url in inmutableDict_request.getlist(key):
                if url:
                    urls_to_add.append(ownUtils.catched_json_loads(url))
            dict_return[key] = urls_to_add
        elif 'imageToAdd' in key:
            imageToAddPosition.append((key.replace('imageToAdd', 'variationImagesToAdd_1'), inmutableDict_request.get(key)))
        else:
            dict_return[key] = inmutableDict_request.get(key)
    dict_return['index'] = 1
    data = ImmutableMultiDict(imageToAddPosition)
    return dict_return, data


def manageFiles(files):
    new_files = ImmutableMultiDict([('variationImagesToAdd_1[]', item) for item in files.getlist('imageToAdd[]')])
    return new_files


"""@products.route('/api/products/product/<string:internalSku>', methods=['PATCH'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def get_product_by_id(internalSku, session):
    product = ownUtils.validate_if_object_exists(session, 'internalSku', 'native', internalSku, Product)
    data = ownUtils.get_info_from_json(request)
    if 'title' in data:
        product.VariationTitle = data.get('title')
    if 'sku' in data:
        product.VariationSku = data.get('sku')
    if 'productDimensions' in data:
        product.ProductProductDimensions = data.get('sku')
    if 'shippingDimensions' in data:
        product.ProductShippingDimensions = data.get('sku')
    if 'photos' in data:
        product.ProductPhotos = ""
    return make_response(jsonify({'mensaje': "Producto modificado exitosamente"}), 200)"""


@products.route('/api/products/product/<string:internalSku>', methods=['DELETE'])
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE,])
def delete_product_by_id(internalSku, session):
    jsonP, status = ownUtils.manage_delete_product(session, Product, internalSku)
    try:
        directory = os.path.\
            join(
                current_app.config['PRODUCT_IMAGES_FOLDER'],
                internalSku)
        if os.path.exists(directory):
            shutil.rmtree(directory)
    except Exception as e:
        print(e)
    return make_response(jsonify(jsonP), status)


"""scope_dict = {
        "product_stores": {
            "publicationProducts_product_store": {
                "publicationProduct": {
                    "publicationBase": {
                        "marketplace":{
                            "supportedMarketplace": {
                                "marketplaceGroup": {}
                            }
                        }
                    }
                }
            }
        },
        "product_supplierStores": {
            "publicationProducts_product_supplierStore": {
                "publicationProduct": {
                    "publicationBase": {
                        "marketplace":{
                            "supportedMarketplace": {
                                "marketplaceGroup": {}
                            }
                        }
                    }
                }
            }
        }
    }"""


@products.route('/api/products/product/infoPublications/<internalSku>')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_product_info_publications(internalSku, session):
    publications = session.query(PublicationProduct).join(PublicationProduct_Product_Store).filter(PublicationProduct_Product_Store.InternalSku == internalSku)
    group_by_marketplace = request.args.get('groupByMarketplace')
    group_by_marketplace = bool(group_by_marketplace and group_by_marketplace.lower() == 'true')
    scope_dict = {
        "publicationBase": {
            "marketplace": {
                "supportedMarketplace": {}
            }
        },
        "publicationProduct_product_stores": {
            "product_store": {}
        },
        "publicationProduct_product_supplierStores": {
            "product_supplierStore": {}
        }
    }
    if group_by_marketplace:
        key_list = ['publicationBase', 'marketplace']
        publications_serialized = grouping_by_marketplace(publications, scope_dict, key_list)
    else:
        publications_serialized = list(map(lambda publication: publication.serialize(scope=scope_dict), publications))
    return make_response(jsonify(publications_serialized), 200)


@products.route('/api/products/product/infoPublicationsBase/<internalBaseSku>')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=ROLES)
def get_info_publications_base(internalBaseSku, session):
    publications_base = session.query(PublicationBase).join(PublicationProduct).join(PublicationProduct_Product_Store).join(Product_Store).join(Product).filter(Product.InternalBaseSku == internalBaseSku)
    group_by_marketplace = request.args.get('groupByMarketplace')
    group_by_marketplace = bool(group_by_marketplace and group_by_marketplace.lower() == 'true')
    scope_dict = {
        "marketplace": {
            "supportedMarketplace": {}
        },
        'publicationProducts': {
            "publicationProduct_product_stores": {
                "product_store": {}
            },
            "publicationProduct_product_supplierStores": {
                "product_supplierStore": {}
            }
        }
    }
    if group_by_marketplace:
        key_list = ['marketplace',]
        publications_base_serialized = grouping_by_marketplace(publications_base, scope_dict, key_list)
    else:
        publications_base_serialized = list(map(lambda publication_base: publication_base.serialize(scope=scope_dict), publications_base))
    return make_response(jsonify(publications_base_serialized), 200)


def grouping_by_marketplace(publications, scope_dict, key_list):
    publications_serialized = {}
    for publication in publications:
        publication_serialized = publication.serialize(scope=scope_dict)
        pre_marketplace = ownUtils.get_nested_value_dict(publication_serialized, key_list)
        marketplace = pre_marketplace['marketplace']
        del pre_marketplace['marketplace']
        if marketplace['id'] in publications_serialized:
            publications_serialized[marketplace['id']]['publications'].append(publication_serialized)
        else:
            publications_serialized[marketplace['id']] = {
                **marketplace,
                'publications': [
                    publication_serialized,
                ]
            }

    publication_serialized_list = list(publications_serialized.values())
    return publication_serialized_list
