from sistemaOrdenes.app.models.Models import ProductBase, ProductBaseTag, \
    ProductBaseProductDimension, ProductBaseShippingDimension, \
    ProductBasePhoto, CustomVariation, ProductBase_CustomVariation, \
    ProductVariationCustom, Product, CustomVariationValue, ProductPhoto, \
    ProductBaseAttribute, ProductProductDimension, ProductShippingDimension
from werkzeug.utils import secure_filename
from sqlalchemy import or_, desc, func
import uuid
import openpyxl
from sistemaOrdenes.app import ownUtils
import os
import shutil
import json
from sistemaOrdenes.app import config

import traceback
current = os.path.dirname(os.path.realpath(__file__))
RUTA_PHOTOS = config.Config.PRODUCT_IMAGES_FOLDER
BACKEND_URL = config.Config.BACKEND_URL
MASIVE_UPDATE_FILE_TITLE = 'Plantilla Carga masiva'
MASIVE_UPDATE_FILE_COLUMNS = ['MARCA', 'MODELO', 'DESCRIPCION',
                              'UPC', 'DESC<PERSON><PERSON><PERSON> LARGA', 'CODIGO SAT',
                              'CODIGO DE UNIDAD', 'CONDICION', 'VIDEO',
                              'IMAGENES', 'LARGO', 'ANCHO', 'ALTO', 'PESO',
                              'LARGO', 'ANCHO', 'ALTO', 'PESO']

noActualizarValue = "No Actualizar"


def armarQueryFiltrosProductos(session):
    query = session.query(ProductBase)
    return query


def armarQueryFiltrosVariaciones(session):
    query = session.query(CustomVariation)
    return query


def armar_query_marca_modelo(query, brand, model):
    if (not brand or not model):
        return False
    query = query.filter(ProductBase.Brand == brand,
                         ProductBase.Model == model)
    return query


def ObtenerVariacionesFiltradasConValores(query, customVariation):
    if customVariation:
        customVariationList = customVariation.split(',')
        query = query.filter(
            (CustomVariation.CustomVariationId.in_(customVariationList)))
    return query


def armarQueryBuscadorProductos(query, search):
    if search:
        search = "%{}%".format(search)
        query = query.filter(ProductBase.Description.like(search))
    return query


def fuzzyProductsBaseSearch(session, search):
    if search:
        query = session.query(ProductBase).order_by(desc(func.greatest(func.similarity(ProductBase.Brand, search), func.similarity(ProductBase.Model, search), func.similarity(ProductBase.Description, search))))
    else:
        query = session.query(ProductBase)
    return query


def procesar_productos_xlsx(file):
    try:
        filaTitulo = 1
        filaAtributos = 3
        file_excel = openpyxl.load_workbook(file)
        hojaPlantilla = file_excel["plantilla"]
        if hojaPlantilla.cell(filaTitulo, 9).value != MASIVE_UPDATE_FILE_TITLE:
            return False, 'La plantilla usada no es la correcta'
        for col in range(1, (len(MASIVE_UPDATE_FILE_COLUMNS) + 1)):
            if (hojaPlantilla.cell(filaAtributos, col).value != MASIVE_UPDATE_FILE_COLUMNS[col - 1]):
                return False, 'La plantilla usada no es la correcta'
        todoCorrecto = True
        lenLista = 0
        mensajesInfo = ''
        errores = ''
        for newProduct in hojaPlantilla.iter_rows(min_row=filaAtributos + 1):
            lenLista += 1
            brand = newProduct[0].value
            model = newProduct[1].value
            description = newProduct[2].value
            upc = newProduct[3].value
            longDescription = newProduct[4].value
            satCode = newProduct[5].value
            unitCode = newProduct[6].value
            condition = newProduct[7].value
            video = newProduct[8].value
            imagenesURL = newProduct[9].value
            lengthShippingDimention = newProduct[10].value
            widthShippingDimention = newProduct[11].value
            heightShippingDimention = newProduct[12].value
            weightShippingDimention = newProduct[13].value
            lengthProductDimention = newProduct[14].value
            widthProductDimention = newProduct[15].value
            heightProductDimention = newProduct[16].value
            weightProductDimention = newProduct[17].value
            print(brand)
            print(model)
            print(description)
            exitoso, mensaje = agregarDatosBasicosProducto(
                brand, model, description, upc,
                longDescription, satCode, unitCode,
                condition, video, imagenesURL,
                lengthShippingDimention, widthShippingDimention,
                heightShippingDimention, weightShippingDimention,
                lengthProductDimention, widthProductDimention,
                heightProductDimention, weightProductDimention)
            if exitoso:
                if mensaje != '':
                    mensajesInfo = ownUtils.logearMensaje(
                        mensajesInfo, str(newProduct[0].row), mensaje)
            else:
                todoCorrecto = False
                errores = ownUtils.logearMensaje(
                    errores, str(newProduct[0].row), mensaje)

        if lenLista == 0:
            return False, 'La lista de productos esta vacía'
        if todoCorrecto:
            return True, mensajesInfo
        else:
            return False, errores
    except Exception as e:
        return False, f'Error inesperado, no se ejecutaron cambios: {str(e)}'


def agregarDatosBasicosProducto(
        brand, model, description, upc,
        longDescription, satCode, unitCode,
        condition, video, imagenesURL,
        lengthShippingDimention, widthShippingDimention,
        heightShippingDimention, weightShippingDimention,
        lengthProductDimention, widthProductDimention,
        heightProductDimention, weightProductDimention, session):
    try:
        mensajeDeError = ''
        registro = ''

        if brand == '':
            brand = None
        if model == '':
            model = None
        if not (brand and model):
            raise Exception("Marca y modelo son obligatorios")
        column = ProductBase.Brand.property.columns[0]
        length = column.type.length
        if len(brand) > length:
            raise Exception(
                f"La marca debe ser menor a {length}")
        column = ProductBase.Model.property.columns[0]
        length = column.type.length
        if len(model) > length:
            raise Exception(
                f"El modelo debe ser menor a {length}")
        internalSku = ownUtils.generate_internal_base_sku(brand, model)

        product = session.query(ProductBase).get(internalSku)
        if not product:
            if (description == '' or description is None):
                description = noActualizarValue
            if description == noActualizarValue:
                registro = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        registro, 'sin descripción')
                raise Exception("Descripcion es obligatorio")
            else:
                column = ProductBase.Description.property.columns[0]
                length = column.type.length
                campoCorrecto, description = ownUtils.ajustarLongitud(
                    'descripción', description, length)
                if campoCorrecto is not True:
                    registro = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registro, campoCorrecto)
            # upc validarLongitud-------------
            if (upc == '' or upc is None):
                upc = noActualizarValue
            if upc == noActualizarValue:
                registro = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        registro, 'sin upc')
            else:
                column = ProductBase.Upc.property.columns[0]
                length = column.type.length
                campoCorrecto, upc = ownUtils.ajustarLongitud(
                    'upc', upc, length)
                if campoCorrecto is not True:
                    registro = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registro, campoCorrecto)
            # longDescription validarLongitud--------------------------
            if (longDescription == '' or longDescription is None):
                longDescription = noActualizarValue
            if longDescription == noActualizarValue:
                registro = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        registro, 'sin descripción larga')
            else:
                column = ProductBase.LongDescription.property.columns[0]
                length = column.type.length
                campoCorrecto, longDescription = ownUtils.ajustarLongitud(
                    'descripci´ón larga'.longDescription, length)
                if campoCorrecto is not True:
                    registro = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registro, campoCorrecto)
            # satCode validateSatCode-----------------------------
            if (satCode == '' or satCode is None):
                satCode = noActualizarValue
            if satCode == noActualizarValue:
                registro = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        registro, 'sin codigo SAT')
            else:
                satCodeValido = ownUtils.validateSatCode(satCode)
                if not satCodeValido:
                    mensajeDeError = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            mensajeDeError,
                            ("El código de sat debe"
                             " tener 8 caracteres y ser numérico"))
            # unitCode validateUnitCode-----------------------------
            if (unitCode == '' or unitCode is None):
                unitCode = noActualizarValue
            if unitCode == noActualizarValue:
                registro = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        registro, 'sin codigo de unidad')
            else:
                unitCodeValido = ownUtils.validateUnitCode(unitCode)
                if not unitCodeValido:
                    mensajeDeError = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            mensajeDeError,
                            ("El código de unidad debe tener"
                             " 2 o 3 caracteres y ser alfanumérico"))
            # condition validateCondition-------------------------
            if (condition == '' or condition is None):
                condition = noActualizarValue
            if condition == noActualizarValue:
                registro = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        registro, 'sin condición')
            else:
                conditionValido = ownUtils.validateCondition(condition)
                if not conditionValido:
                    mensajeDeError = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            mensajeDeError,
                            'La condicion debe ser "Nuevo" o "Usado"')
            # video validateVideo-----------------------------
            if (video == '' or video is None):
                video = noActualizarValue
            if video == noActualizarValue:
                registro = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        registro, 'sin video')
            else:
                videoValido = ownUtils.validateVideo(video)
                if not videoValido:
                    mensajeDeError = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            mensajeDeError,
                            "La cadena no corresponde a un enlace de YouTube")
            # dimensiones-------------------
        # validaciones
        # validaciones
            # dimensiones de producto
            # Declaracion de variables para dimensiones producto
            mensajeErrorDimensionesProducto = ''
            registroDimensionesProducto = ''
            # Revisar si todo esta vacio
            if ((lengthProductDimention == '' or lengthProductDimention is None) and (widthProductDimention == '' or widthProductDimention is None) and (heightProductDimention == '' or heightProductDimention is None) and (weightProductDimention == '' or weightProductDimention is None)):
                productDimentions = noActualizarValue
                registroDimensionesProducto = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        registroDimensionesProducto,
                        'sin dimensiones de producto')
            else:
                # Revisar si todo esta vacio
                if (lengthProductDimention == '' or lengthProductDimention is None):
                    lengthProductDimention = noActualizarValue
                    registroDimensionesProducto = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registroDimensionesProducto, 'sin largo')
                else:
                    error = ownUtils.revisarFloatPositivo(lengthProductDimention)
                    if error:
                        mensajeErrorDimensionesProducto = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeErrorDimensionesProducto,
                                'largo incorrecto')

                if (widthProductDimention == '' or widthProductDimention is None):
                    widthProductDimention = noActualizarValue
                    registroDimensionesProducto = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registroDimensionesProducto, 'sin ancho')
                else:
                    error = ownUtils.revisarFloatPositivo(widthProductDimention)
                    if error:
                        mensajeErrorDimensionesProducto = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeErrorDimensionesProducto,
                                'ancho incorrecto')
                if (heightProductDimention == '' or heightProductDimention is None):
                    heightProductDimention = noActualizarValue
                    registroDimensionesProducto = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registroDimensionesProducto,
                            'sin alto')
                else:
                    error = ownUtils.revisarFloatPositivo(heightProductDimention)
                    if error:
                        mensajeErrorDimensionesProducto = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeErrorDimensionesProducto,
                                'alto incorrecto')
                if (weightProductDimention == '' or weightProductDimention is None):
                    weightProductDimention = noActualizarValue
                    registroDimensionesProducto = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registroDimensionesProducto, 'sin peso')
                else:
                    error = ownUtils.revisarFloatPositivo(weightProductDimention)
                    if error:
                        mensajeErrorDimensionesProducto = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeErrorDimensionesProducto,
                                'peso incorrecto')
                if mensajeErrorDimensionesProducto == '':
                    productDimentions = ProductBaseProductDimension(
                        internalSku,
                        lengthProductDimention,
                        widthProductDimention,
                        heightProductDimention,
                        weightProductDimention
                    )
            # ´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´´
            if registroDimensionesProducto != '':
                if (registroDimensionesProducto.lower() == 'sin dimensiones de producto'):
                    registro = ownUtils.concatenarMensajeRetroalimentacion(
                        registro,
                        f'{registroDimensionesProducto}')
                else:
                    registro = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registro,
                            (f'dimensiones de producto('
                             f'{registroDimensionesProducto})'))
            if mensajeErrorDimensionesProducto != '':
                mensajeDeError = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        mensajeDeError,
                        (f'dimensiones de producto('
                         f'{mensajeErrorDimensionesProducto})'))
            # dimensiones de producto
            # dimensiones de envio
            # Declaracion de variables para dimensiones de envio
            mensajeErrorDimensionesEnvio = ''
            registroDimensionesEnvio = ''
            # Revisar si todo esta vacio
            if ((lengthShippingDimention == '' or lengthShippingDimention is None) and (widthShippingDimention == '' or widthShippingDimention is None) and (heightShippingDimention == '' or heightShippingDimention is None) and (weightShippingDimention == '' or weightShippingDimention is None)):
                shippingDimentions = noActualizarValue
                registroDimensionesEnvio = ownUtils.concatenarMensajeRetroalimentacion(
                    registroDimensionesEnvio, 'sin dimensiones de envio')
            else:
                if (lengthShippingDimention == '' or lengthShippingDimention is None):
                    lengthShippingDimention = noActualizarValue
                    registroDimensionesEnvio = ownUtils.concatenarMensajeRetroalimentacion(
                        registroDimensionesEnvio, 'sin largo')
                else:
                    error = ownUtils.revisarFloatPositivo(lengthShippingDimention)
                    if error:
                        mensajeErrorDimensionesEnvio = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeErrorDimensionesEnvio,
                                'largo incorrecto')
            if (widthShippingDimention == '' or widthShippingDimention is None):
                widthShippingDimention = noActualizarValue
                registroDimensionesEnvio = ownUtils.concatenarMensajeRetroalimentacion(
                    registroDimensionesEnvio, 'sin ancho')
            else:
                error = ownUtils.revisarFloatPositivo(widthShippingDimention)
                if error:
                    mensajeErrorDimensionesEnvio = ownUtils.concatenarMensajeRetroalimentacion(
                        mensajeErrorDimensionesEnvio, 'ancho incorrecto')
            if (heightShippingDimention == '' or heightShippingDimention is None):
                heightShippingDimention = noActualizarValue
                registroDimensionesEnvio = ownUtils.concatenarMensajeRetroalimentacion(
                    registroDimensionesEnvio, 'sin alto')
            else:
                error = ownUtils.revisarFloatPositivo(heightShippingDimention)
                if error:
                    mensajeErrorDimensionesEnvio = ownUtils.concatenarMensajeRetroalimentacion(
                        mensajeErrorDimensionesEnvio, 'alto incorrecto')
            if (weightShippingDimention == '' or weightShippingDimention is None):
                weightShippingDimention = noActualizarValue
                registroDimensionesEnvio = ownUtils.concatenarMensajeRetroalimentacion(
                    registroDimensionesEnvio, 'sin peso')
            else:
                error = ownUtils.revisarFloatPositivo(weightShippingDimention)
                if error:
                    mensajeErrorDimensionesEnvio = ownUtils.concatenarMensajeRetroalimentacion(
                        mensajeErrorDimensionesEnvio, 'peso incorrecto')
            if mensajeErrorDimensionesEnvio == '':
                shippingDimentions = ProductBaseShippingDimension(
                    internalSku,
                    lengthShippingDimention,
                    widthShippingDimention,
                    heightShippingDimention,
                    weightShippingDimention
                )
            else:
                shippingDimentions = noActualizarValue
                registroDimensionesEnvio = ownUtils.concatenarMensajeRetroalimentacion(
                    registroDimensionesEnvio, 'sin dimensiones de envio')
            if registroDimensionesEnvio != '':
                if (registroDimensionesEnvio.lower() == 'sin dimensiones de envio'):
                    registro = ownUtils.concatenarMensajeRetroalimentacion(
                        registro, f'{registroDimensionesEnvio}')
                else:
                    registro = ownUtils.concatenarMensajeRetroalimentacion(
                        registro,
                        (f'dimensiones de envio('
                         f'{registroDimensionesEnvio})'))
            if mensajeErrorDimensionesEnvio != '':
                mensajeDeError = ownUtils.concatenarMensajeRetroalimentacion(
                    mensajeDeError,
                    (f'dimensiones de envio('
                     f'{mensajeErrorDimensionesEnvio})'))
            if (imagenesURL == '' or imagenesURL is None):
                # regresar error
                photos = noActualizarValue
                registro = ownUtils.concatenarMensajeRetroalimentacion(
                    registro, 'sin imágenes')
            else:
                listaUbicaciones = addPhotosMasive(imagenesURL)
                if type(listaUbicaciones) != str:
                    photos = []
                    for itnumber, ubicacion in enumerate(listaUbicaciones):
                        photos.append(ProductBasePhoto(
                            internalSku,
                            ubicacion,  # -----------------
                            itnumber
                        ))
                else:
                    mensajeDeError = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            mensajeDeError, listaUbicaciones)
                # Images--------------------
            # database ORM object

            if mensajeDeError == '':
                product = ProductBase(
                    internalSku,
                    brand,
                    model,
                    description,
                    upc,
                    longDescription,
                    satCode,
                    unitCode,
                    condition,
                    video,
                    shippingDimentions,
                    productDimentions,
                    photos
                )
                # insert product
                session.add(product)
                return True, registro
            else:
                return False, mensajeDeError
        else:
            return False, 'Producto ya existe'
    except Exception as e:
        session.rollback()
        print(str(e))
        return False, str(e)


def addPhotosMasive(imageUrls):
    try:
        imageUrlsList = imageUrls.split(',')
        errorAddphotos = ''
        exito = ownUtils.checkUrlPhotos(imageUrlsList, 0, None, None)
        if type(exito) == str:
            errorAddphotos = ((f'{errorAddphotos}{exito}')
                              if errorAddphotos == ''
                              else (f'{errorAddphotos}, {exito}'))
        if errorAddphotos == '':
            success = exito
        else:
            success = errorAddphotos
        print(success)
    except Exception as e:
        print(e)
        success = 'Ocurrió un error al guardar las imágenes'
    finally:
        return success


def eliminarFotosSolicitadas(imageToDeleteList, productPhotos, internalSku, session):
    try:
        if imageToDeleteList == 'ELIMINAR TODAS':
            directory = os.path.join(RUTA_PHOTOS, internalSku)
            for file_name in os.listdir(directory):
                file_path = os.path.join(directory, file_name)
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                except Exception as e:
                    raise Exception(f'Error al borrar las imagenes: {str(e)}')
            for productPhoto in productPhotos:
                session.delete(productPhoto)
        else:
            directory = os.path.join(RUTA_PHOTOS, internalSku)
            imageToDeleteList = [ownUtils.imageURLToImagePath(imageToDelete, 'products')
                                 if (BACKEND_URL in imageToDelete and '/api/products/getImage/' in imageToDelete)
                                 else imageToDelete
                                 for imageToDelete in imageToDeleteList]
            for productPhoto in productPhotos:
                pathProductPhoto = os.path.realpath(productPhoto.URLPhoto)
                print('ssssssssss')
                print(pathProductPhoto)
                print(imageToDeleteList)
                print('ssssssssss')
                if pathProductPhoto in imageToDeleteList:
                    print('ifff')
                    if not ownUtils.is_valid_url(productPhoto.URLPhoto):
                        print('valid')
                        print(os.path.join(directory, productPhoto.URLPhoto))
                        if (os.path.exists(
                            os.path.join(RUTA_PHOTOS,
                                         productPhoto.URLPhoto))):
                            print('existe')
                            os.remove(os.path.join(
                                RUTA_PHOTOS, productPhoto.URLPhoto))
                    session.delete(productPhoto)
        return True
    except Exception as e:
        print(str(e))
        return str(e)


def create_folder_to_imgs(directory, newProduct):
    if (os.path.exists(directory) and newProduct):
        shutil.rmtree(directory)
        os.makedirs(directory)
    if not os.path.exists(directory):
        os.makedirs(directory)


def addPhotos(imagesFiles, imageUrls, internalSku, newProduct, fileImagesBasePosition=None):
    # print('addphotos')
    try:
        directory = os.path.join(RUTA_PHOTOS, internalSku)
        create_folder_to_imgs(directory, newProduct)
        errorAddphotos = ''
        if fileImagesBasePosition is None:
            fileImagesBasePosition = []
        else:
            fileImagesBasePosition = [int(position) for position in fileImagesBasePosition]
        print('iiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiii')
        print(fileImagesBasePosition)
        print("##################################")
        listOfStr = all(isinstance(elemento, str) for elemento in imageUrls)
        if listOfStr:
            try:
                imageUrls = [json.loads(item) for item in imageUrls]
            except json.JSONDecodeError:
                imageUrls = [{"img": url, "position": index} for index, url in enumerate(imageUrls)]
        print(imageUrls)
        posicionesUrl = [img["position"] for img in imageUrls]
        print(posicionesUrl)
        posiciones = fileImagesBasePosition + posicionesUrl
        posiciones_unicas = set(posiciones)
        duplicados = len(posiciones) != len(posiciones_unicas)
        if duplicados:
            errorAddphotos = ('Hay posiciones duplicadas')
        lenghtAllImgs = max(posiciones_unicas) + 1
        exito1 = saveFilePhotos(imagesFiles, directory, internalSku, lenghtAllImgs, fileImagesBasePosition)
        if type(exito1) == str:
            errorAddphotos = ((f'{errorAddphotos}{exito1}')
                              if errorAddphotos == ''
                              else (f'{errorAddphotos}, {exito1}'))
        exito2 = ownUtils.checkUrlPhotos(imageUrls, lenghtAllImgs, directory, internalSku)

        if type(exito2) == str:
            # print('dentro if')
            errorAddphotos = ((f'{errorAddphotos}{exito2}')
                              if errorAddphotos == ''
                              else (f'{errorAddphotos}, {exito2}'))
        # print(errorAddphotos)
        if errorAddphotos == '':
            # print('if')
            print(exito1)
            print(exito2)
            lista_combinada = []
            for valor1, valor2 in zip(exito1, exito2):
                if valor1 is not None:
                    lista_combinada.append(valor1)
                else:
                    lista_combinada.append(valor2)
            print("##################")
            success = lista_combinada
        else:
            # print('else')
            success = errorAddphotos
        # print(success)
    except Exception as e:
        print(e)
        success = 'Ocurrió un error al guardar las imágenes'
    finally:
        return success


def saveFilePhotos(imageFiles, directory, internalSku, lenghtAllImgs, fileImagesBasePosition=None):
    try:
        success = [None] * lenghtAllImgs
        imageNames = [imageFile.filename for imageFile in imageFiles]
        index = 0
        if not ownUtils.has_duplicate_in_list(imageNames):
            for image in imageFiles:
                if image.filename == '':
                    raise Exception("No se puede guardar imagenes sin nombre")
                imageReturn = ownUtils.checkValidImageFile(image)
                if imageReturn is False:
                    raise Exception(f'La imagen no es válida {image.filename}')
                if image and ownUtils.allowed_img_file(image.filename):
                    nombreSeguro = secure_filename(
                        f'{str(uuid.uuid4())}.{image.filename.rsplit(".", 1)[1].lower()}')
                    full_path = os.path.join(directory, nombreSeguro)
                    imageReturn.save(full_path)
                    success[fileImagesBasePosition[index]] = os.path.join(internalSku, nombreSeguro)
                    index = index + 1
                else:
                    raise Exception("La imagen no es válida")
        else:
            success = 'No puedes guardar la misma url mas de una vez'
    except Exception as e:
        try:
            if os.path.exists(directory):
                shutil.rmtree(directory)
        except Exception as e:
            print(str(e))
        success = str(e)
    finally:
        return success


def crearMensajeSinONoActualizado(dato, esNuevoProducto):
    if esNuevoProducto:
        mensaje = f'sin {dato}'
    else:
        mensaje = f'{dato} no actualizado'
    return mensaje


def agregarBasicosProductBase(productBase, esNuevoProducto, data):
    mensajeDeError = ''
    registro = ''
    # description validarLongitud-------------
    description = data.get('description')
    if not description:
        registro = \
            ownUtils.concatenarMensajeRetroalimentacion(
                registro,
                crearMensajeSinONoActualizado(
                    'descripción', esNuevoProducto))
    else:
        campoCorrecto, description = ownUtils.ajustarLongitud(
            'descripción',
            description,
            ProductBase.Description.property.columns[0].type.length)
        if campoCorrecto is not True:
            registro = \
                ownUtils.concatenarMensajeRetroalimentacion(
                    registro, campoCorrecto)
        productBase.Description = description
    upc = data.get('upc')
    if not upc:
        registro = ownUtils.concatenarMensajeRetroalimentacion(
            registro,
            crearMensajeSinONoActualizado(
                'upc', esNuevoProducto))
    else:
        campoCorrecto, upc = ownUtils.ajustarLongitud(
            'upc', upc, ProductBase.Upc.property.columns[0].type.length)
        if campoCorrecto is not True:
            registro = ownUtils.concatenarMensajeRetroalimentacion(
                registro, campoCorrecto)
        productBase.Upc = upc
    # longDescription validarLongitud--------------------------
    longDescription = data.get('longDescription')
    if not longDescription:
        registro = ownUtils.concatenarMensajeRetroalimentacion(
            registro,
            crearMensajeSinONoActualizado(
                'descripción larga', esNuevoProducto))
    else:
        campoCorrecto, longDescription = ownUtils.ajustarLongitud(
            'descripción larga',
            longDescription,
            ProductBase.LongDescription.property.columns[0].type.length)
        if campoCorrecto is not True:
            registro = \
                ownUtils.concatenarMensajeRetroalimentacion(
                    registro, campoCorrecto)
        productBase.LongDescription = longDescription
    # satCode validateSatCode-----------------------------
    satCode = data.get('satCode')
    if not satCode:
        registro = \
            ownUtils.concatenarMensajeRetroalimentacion(
                registro,
                crearMensajeSinONoActualizado(
                    'código SAT', esNuevoProducto))
    else:
        satCodeValido = ownUtils.validateSatCode(satCode)
        if not satCodeValido:
            mensajeDeError = \
                ownUtils.concatenarMensajeRetroalimentacion(
                    mensajeDeError,
                    "El código de sat debe tener 8 caracteres y ser numérico")
        else:
            productBase.SatCode = satCode
    # unitCode validateUnitCode----------------------------
    unitCode = data.get('unitCode')
    if not unitCode:
        registro = \
            ownUtils.concatenarMensajeRetroalimentacion(
                registro,
                crearMensajeSinONoActualizado(
                    'código de unidad', esNuevoProducto))
    else:
        unitCodeValido = ownUtils.validateUnitCode(unitCode)
        if not unitCodeValido:
            mensajeDeError = \
                ownUtils.concatenarMensajeRetroalimentacion(
                    mensajeDeError,
                    ("El código de unidad debe tener"
                     " 2 o 3 caracteres y ser alfanumérico"))
        else:
            productBase.UnitCode = unitCode
    # condition validateCondition-------------------------
    condition = data.get('condition')
    if not condition:
        registro = \
            ownUtils.concatenarMensajeRetroalimentacion(
                registro,
                crearMensajeSinONoActualizado(
                    'condición', esNuevoProducto))
    else:
        conditionValido = ownUtils.validateCondition(condition)
        if not conditionValido:
            mensajeDeError = \
                ownUtils.concatenarMensajeRetroalimentacion(
                    mensajeDeError,
                    'La condicion debe ser "Nuevo" o "Usado"')
        else:
            productBase.Condition = condition
    # video validateVideo-----------------------------
    video = data.get('video')
    if not video:
        registro = \
            ownUtils.concatenarMensajeRetroalimentacion(
                registro,
                crearMensajeSinONoActualizado(
                    'video', esNuevoProducto))
    else:
        videoValido = ownUtils.validateVideo(video)
        if not videoValido:
            mensajeDeError = \
                ownUtils.concatenarMensajeRetroalimentacion(
                    mensajeDeError,
                    "La cadena no corresponde a un enlace de YouTube")
        else:
            productBase.Video = video
    return registro, mensajeDeError


def agregarDimensiones(productBase, DimensionObjectClass,
                       objectAttributeName, dimensionJSON,
                       esNuevoProducto, tipoDeDimension):
    # Declaracion de variables para dimensiones producto
    mensajeErrorDimensiones = ''
    registroDimensiones = ''
    try:
        if dimensionJSON:
            if isinstance(dimensionJSON, dict):
                dimensionDict = dimensionJSON
            else:
                # Conversion de string a json
                dimensionDict = json.loads(dimensionJSON)

            length = dimensionDict.get('length')
            width = dimensionDict.get('width')
            height = dimensionDict.get('height')
            weight = dimensionDict.get('weight')

            # Revisar si todo esta vacio
            if ((not length) and (not width) and (not height) and (not weight)):
                registroDimensiones = ownUtils.concatenarMensajeRetroalimentacion(
                    registroDimensiones,
                    crearMensajeSinONoActualizado(
                        tipoDeDimension, esNuevoProducto))
            else:
                dimensionObject = DimensionObjectClass()
                # Revisar si cada uno esta vacio
                if (not length):
                    registroDimensiones = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registroDimensiones,
                            crearMensajeSinONoActualizado(
                                'largo', esNuevoProducto))
                else:
                    error = ownUtils.revisarFloatPositivo(length)
                    if error:
                        mensajeErrorDimensiones = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeErrorDimensiones,
                                'largo incorrecto')
                    else:
                        dimensionObject.Length = length
                if (not width):
                    registroDimensiones = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registroDimensiones,
                            crearMensajeSinONoActualizado(
                                'ancho', esNuevoProducto))
                else:
                    error = ownUtils.revisarFloatPositivo(width)
                    if error:
                        mensajeErrorDimensiones = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeErrorDimensiones,
                                'ancho incorrecto')
                    else:
                        dimensionObject.Width = width
                if (not height):
                    registroDimensiones = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registroDimensiones,
                            crearMensajeSinONoActualizado(
                                'alto', esNuevoProducto))
                else:
                    error = ownUtils.revisarFloatPositivo(height)
                    if error:
                        mensajeErrorDimensiones = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeErrorDimensiones, 'alto incorrecto')
                    else:
                        dimensionObject.Height = height
                if (not weight):
                    registroDimensiones = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            registroDimensiones,
                            crearMensajeSinONoActualizado(
                                'peso', esNuevoProducto))
                else:
                    error = ownUtils.revisarFloatPositivo(weight)
                    if error:
                        mensajeErrorDimensiones = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                mensajeErrorDimensiones, 'peso incorrecto')
                    else:
                        dimensionObject.Weight = weight
                if mensajeErrorDimensiones == '':
                    setattr(productBase, objectAttributeName, dimensionObject)
        else:
            registroDimensiones = \
                ownUtils.concatenarMensajeRetroalimentacion(
                    registroDimensiones, f'sin {tipoDeDimension}')
    except Exception as e:
        mensajeErrorDimensiones = \
            ownUtils.concatenarMensajeRetroalimentacion(
                mensajeErrorDimensiones,
                f"no fue posible leer las medidas: {str(e)}")
    return registroDimensiones, mensajeErrorDimensiones


def set_dimension(dimension, dimensionObject, attribute_name):
    if dimension:
        error = ownUtils.revisarFloatPositivo(dimension)
        if error:
            raise Exception(f"Debe ser un entero positivo: {error}")
        setattr(dimensionObject, attribute_name, float(dimension))


def agregarDimensiones2(productBase, DimensionObjectClass, objectAttributeName, dimensionJSON):
    if dimensionJSON:
        length = dimensionJSON.get('length')
        width = dimensionJSON.get('width')
        height = dimensionJSON.get('height')
        weight = dimensionJSON.get('weight')
        if (length or width or height or weight):
            dimensionObject = DimensionObjectClass()
            set_dimension(length, dimensionObject, "Length")
            set_dimension(length, dimensionObject, "Width")
            set_dimension(length, dimensionObject, "Height")
            set_dimension(length, dimensionObject, "Weight")
            setattr(productBase, objectAttributeName, dimensionObject)


def agregarProductBaseVariations(
        variationsAssociatedWithTheProduct,
        productBase, session):
    errorvariationAssociatedWithTheProduct = ''
    if (not ownUtils.has_duplicate_in_list(
        variationsAssociatedWithTheProduct) and all(ownUtils.revisarIntPositivo(elem) == None
                                                    for elem in variationsAssociatedWithTheProduct)):
        for variationAssociatedWithTheProduct \
                in variationsAssociatedWithTheProduct:
            customVariation = session.query(
                CustomVariation).get(variationAssociatedWithTheProduct)
            if not customVariation:
                errorvariationAssociatedWithTheProduct = \
                    ownUtils.concatenarMensajeRetroalimentacion(
                        errorvariationAssociatedWithTheProduct,
                        (f'La variación con Id '
                         f'{variationAssociatedWithTheProduct} no existe'))
            else:
                productBase_CustomVariation = session.\
                    query(ProductBase_CustomVariation).\
                    filter(
                        (ProductBase_CustomVariation.InternalBaseSku == productBase.InternalBaseSku),
                        (ProductBase_CustomVariation.CustomVariationId == variationAssociatedWithTheProduct)).\
                    first()
                if productBase_CustomVariation:
                    errorvariationAssociatedWithTheProduct = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            errorvariationAssociatedWithTheProduct,
                            ('Un producto no puede estar asociado'
                             ' más de una vez a la misma variación'))
                else:
                    productBase_CustomVariation = ProductBase_CustomVariation()
                    productBase_CustomVariation.CustomVariation = \
                        customVariation
                    productBase_CustomVariation.ProductBase = productBase
    else:
        errorvariationAssociatedWithTheProduct = \
            ownUtils.concatenarMensajeRetroalimentacion(
                errorvariationAssociatedWithTheProduct,
                ('Los id de la lista de variaciones'
                 ' deben ser enteros y no repetirse'))
    return errorvariationAssociatedWithTheProduct


def agregarVariacionesGeneral(product, session):
    listvariationNameValue = product.InternalSku.split('_')
    listvariationNameValue = listvariationNameValue[1:]
    for variationNameValue in listvariationNameValue:
        variationNameValueSplit = variationNameValue.split('-')
        variationNameId = variationNameValueSplit[0]
        variationValueId = variationNameValueSplit[1]
        productBase_CustomVariation = session.query(ProductBase_CustomVariation).filter(
            (ProductBase_CustomVariation.InternalBaseSku == product.ProductBase.InternalBaseSku),
            (ProductBase_CustomVariation.CustomVariationId == variationNameId)).first()
        if not productBase_CustomVariation:
            raise Exception('Este producto no esta relacionado con esa variación')
        customVariation = session.query(CustomVariation).get(variationNameId)
        customVariationValue = session.query(CustomVariationValue).get(variationValueId)
        if customVariationValue not in customVariation.CustomVariationValues:
            raise Exception('Esta variación no puede tomar ese valor')
        productVariationCustom = ProductVariationCustom()
        productVariationCustom.Product = product
        productVariationCustom.CustomVariationValue = customVariationValue
        productVariationCustom.ProductBase_CustomVariation = productBase_CustomVariation


def agregarImagenes(fileImagesBase, urlImagesBase,
                    imagetoDeleteList, ProductObject,
                    PhotoObjectClass, photoAtrribute,
                    generatedSku, esProductoNuevo, session,
                    fileImagesBasePosition=None,
                    orderfilesImgs=None, orderUrlImgs=None):
    erroresImgs = ''
    registroImgs = ''
    if (not fileImagesBase and not urlImagesBase and not imagetoDeleteList):
        # regresar error
        registroImgs = 'sin imágenes'
    else:
        oldPhotos = eliminarFotosSolicitadas(imagetoDeleteList, getattr(
            ProductObject, photoAtrribute), generatedSku, session)
        if oldPhotos is True:
            # urlImagesBase = list(set(urlImagesBase))
            listaUbicaciones = addPhotos(
                fileImagesBase, urlImagesBase, generatedSku, esProductoNuevo,
                fileImagesBasePosition)
        else:
            listaUbicaciones = oldPhotos
        if type(listaUbicaciones) != str:
            photos = []
            for itnumber, ubicacion in enumerate(listaUbicaciones):
                photos.append(PhotoObjectClass(
                    URLPhoto=ubicacion,
                    ItemNumber=itnumber
                ))
            setattr(ProductObject, photoAtrribute, photos)
        else:
            erroresImgs = listaUbicaciones
    return registroImgs, erroresImgs


def get_max_posible_combinations_variations(productBase):
    posible_variation_products = 1
    for productBase_CustomVariation in productBase.ProductBase_CustomVariations:
        len_values = len(productBase_CustomVariation.CustomVariation.CustomVariationValues)
        # Every CustoVariation should has at least 1 customVariationValue
        if len_values == 0:
            return 0
        posible_variation_products = posible_variation_products * len_values
    return posible_variation_products


def set_basic_info_product(product, request_product):
    product.VariationTitle = request_product['title']
    product.VariationSku = request_product['sku']


def get_condition_conditionVariation(session):
    custom_variations = session.query(CustomVariation).all()
    CustomVariation.CustomVariationId
    for custom_variation in custom_variations:
        if custom_variation.CustomVariationName == "Condición":
            return custom_variation.CustomVariationId
    raise Exception('La variación Condición no fue encontrada')


def find_product_by_internalBaseSku(leftover_variationProducts, previos_product):
    for i in range(len(leftover_variationProducts)):
        json_product = ownUtils.catched_json_loads(leftover_variationProducts[i])
        internalSku = ownUtils.generate_internal_sku(previos_product.InternalBaseSku, json_product["variations"])
        if internalSku == previos_product.InternalSku:
            return (i, json_product)
    return None


def update_product_variation(previos_product, json_variation_product, files, data, isNew, session):
    set_basic_info_product(previos_product, json_variation_product)
    agregarDimensionesGeneral(previos_product, json_variation_product)
    agregarImagenesGeneralProducto(previos_product, previos_product.ProductBase, json_variation_product, files, data, isNew, session)


def update_existed_products(previos_products, variationProducts, files, data, isNew, session):
    registro = ''
    error = ''
    if not variationProducts:
        error = "Todos los productos deben tener variaciones"
        return registro, error
    products_for_delete = []
    for previos_product in previos_products:
        product_request = find_product_by_internalBaseSku(variationProducts, previos_product)
        if product_request is None:
            products_for_delete.append(previos_product)
        else:
            product_num, json_variation_product = product_request
            update_product_variation(previos_product, json_variation_product, files, data, isNew, session)
            del variationProducts[product_num]
    return products_for_delete


def manage_condition_variations_for_product(productBase, variations, index, condition_variation_id):
    print('89999999999999999999999999999999999999999999999999999999999999999999999')
    print('89999999999999999999999999999999999999999999999999999999999999999999999')
    print('89999999999999999999999999999999999999999999999999999999999999999999999')
    print(variations)
    print(len(productBase.ProductBase_CustomVariations))
    print(len(variations))
    print('89999999999999999999999999999999999999999999999999999999999999999999999')
    print('89999999999999999999999999999999999999999999999999999999999999999999999')
    print('89999999999999999999999999999999999999999999999999999999999999999999999')
    if not len(productBase.ProductBase_CustomVariations) == len(variations):
        raise Exception(f'{index}: cada producto debe tener un solo valor para cada una de las variaciones')
    int_variations = return_keyAndValueInt(variations)
    if condition_variation_id not in int_variations:
        raise Exception(f'{index}: la variación Condición es obligatoria')


def create_new_variation_product(product_num, leftover_variationProduct, productBase, condition_variation_id):
    variations = leftover_variationProduct.get('variations')
    manage_condition_variations_for_product(productBase, variations, leftover_variationProduct.get('index'), condition_variation_id)
    internalSku = ownUtils.generate_internal_sku(productBase.InternalBaseSku, variations)
    product = Product(internalSku)
    return product


def agregarDimensionesGeneral(product, variationProduct):
    variationShippingDimension = variationProduct.get('variationShippingDimensions')
    agregarDimensiones2(product, ProductShippingDimension, 'ProductShippingDimensions', variationShippingDimension)
    variationProductDimension = variationProduct.get('variationProductDimensions')
    agregarDimensiones2(product, ProductProductDimension, 'ProductProductDimensions', variationProductDimension)


def agregarImagenesGeneralProducto(product, productBase, variationProduct_request, files, data, isNew, session):
    variationurlsToAdd = variationProduct_request.get('urlToAdd[]', [])
    variationfileImage = files.getlist(f'variationImagesToAdd_{variationProduct_request.get("index")}[]')
    variationfileImagePosition = []
    for file in variationfileImage:
        variationfileImagePosition.append(data.get(f'variationImagesToAdd_{variationProduct_request.get("index")}{file.filename}'))
    print('000000000000kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk')
    print(variationfileImagePosition)
    print('000000000000kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk')
    variationurlsToAddOrder = variationProduct_request.get('urlToAddOrder[]', [])
    variationfileImageOrder = files.getlist(f'variationImagesToAdd_{variationProduct_request.get("index")}Order[]')
    imagetoDeleteList = variationProduct_request.get('urlToAdd[]', [])
    ss = productBase.InternalBaseSku
    ss2 = product.InternalSku
    internalBaseSku_internalSku = os.path.join(ss, ss2)  # f'{ss}\\{ss2}'
    _, erroresImgs = agregarImagenes(variationfileImage, variationurlsToAdd, imagetoDeleteList, product, ProductPhoto, 'ProductPhotos', internalBaseSku_internalSku, isNew, session, variationfileImagePosition, variationurlsToAddOrder, variationfileImageOrder)
    if erroresImgs:
        raise Exception(erroresImgs)


def register_variation_product(session, product_num, variationProduct_request, productBase, condition_variation_id, files, data, isNew):
    product = create_new_variation_product(product_num, variationProduct_request, productBase, condition_variation_id)
    set_basic_info_product(product, variationProduct_request)
    product.ProductBase = productBase
    agregarVariacionesGeneral(product, session)
    agregarDimensionesGeneral(product, variationProduct_request)
    agregarImagenesGeneralProducto(product, productBase, variationProduct_request, files, data, isNew, session)


def update_productos_variacion(variationProducts, productBase, files, data, isNew, session):
    # revisar que los valores no se repitan
    max_combination = get_max_posible_combinations_variations(productBase)
    if max_combination == 0:
        raise Exception("Todos las variaciones deben tener por lo menos un producto al cual asociarse")
    condition_variation_id = get_condition_conditionVariation(session)
    products_for_delete = update_existed_products(productBase.Products, variationProducts, files, data, isNew, session)
    for product_num, variationProduct_request in enumerate(variationProducts, start=1):
        variationProduct_request = ownUtils.catched_json_loads(variationProduct_request)
        if not variationProduct_request.get('index'):
            raise Exception(f'El index es obligatorio: {str(product_num)} sin index')
        register_variation_product(session, product_num, variationProduct_request, productBase, condition_variation_id, files, data, isNew)
    for product_for_delete in products_for_delete:
        session.delete(product_for_delete)
    return "", ""


def agregarInfoBasicaVariacion(variationProductDict, product):
    registroInfoBasicaVariacion = ''
    title = variationProductDict.get('title')
    if not title:
        registroInfoBasicaVariacion = \
            ownUtils.concatenarMensajeRetroalimentacion(
                registroInfoBasicaVariacion, 'sin título')
    else:
        campoCorrecto, title = ownUtils.ajustarLongitud(
            'titulo',
            title, Product.VariationTitle.property.columns[0].type.length)
        if campoCorrecto is not True:
            registroInfoBasicaVariacion = \
                ownUtils.concatenarMensajeRetroalimentacion(
                    registroInfoBasicaVariacion, campoCorrecto)
        product.VariationTitle = title
    sku = variationProductDict.get('sku')
    if not sku:
        registroInfoBasicaVariacion = \
            ownUtils.concatenarMensajeRetroalimentacion(
                registroInfoBasicaVariacion, 'sin sku')
    else:
        campoCorrecto, sku = ownUtils.ajustarLongitud(
            'sku',
            sku, Product.VariationSku.property.columns[0].type.length)
        if campoCorrecto is not True:
            registroInfoBasicaVariacion = \
                ownUtils.concatenarMensajeRetroalimentacion(
                    registroInfoBasicaVariacion, campoCorrecto)
        product.VariationSku = sku
    return registroInfoBasicaVariacion


def return_keyAndValueInt(my_dict):
    key_value_int_dict = {}
    for key, value in my_dict.items():
        if (ownUtils.revisarIntPositivo(key) or ownUtils.revisarIntPositivo(value)):
            raise Exception("Los ids de variaciones y sus ids de los valores deben ser enteros")
        key_value_int_dict[int(key)] = int(value)
    return key_value_int_dict


def eliminarCarpetasSobrantes(listaDeCarpetas, rutaPadre):
    try:
        # Define the path of the directory to be searched
        directory = os.path.join(RUTA_PHOTOS, rutaPadre)
        # Get a list of all files and folders in the directory
        files_and_folders = os.listdir(directory)

        # Filter the list to include only folder names
        folders = [f for f in files_and_folders if os.path.isdir(
            os.path.join(directory, f))]

        # Print the list of folder names
        for folder in folders:
            if folder not in listaDeCarpetas:
                shutil.rmtree(os.path.join(directory, folder))
        return True
    except Exception as e:
        print(str(e))
        return True


def agregarAtributosProductoBase(attributes, productBase):
    registroAllAttributes = ''
    erroresAllAttributes = ''
    column = ProductBaseAttribute.AttributeName.property.columns[0]
    length = column.type.length
    column2 = ProductBaseAttribute.AttributeValue.property.columns[0]
    length2 = column2.type.length
    if attributes:
        try:
            attributesDict = json.loads(attributes)
            attributesProductBase = []
            for attributeName, attributeValue in attributesDict.items():
                try:
                    if not attributeName:
                        raise Exception(
                            'El nombre del atributo debe ser obligatorio')
                    if len(attributeName) > length:
                        raise Exception(
                            f"La marca debe ser menor a {length}")
                    if not ownUtils.is_valid_name(attributeName):
                        raise Exception(
                            f" Nombre no válido ({attributeName}), "
                            f"debe contener solo letras,- o '")
                    if not attributeValue:
                        raise Exception(
                            'El valor del atributo debe ser obligatorio')
                    if len(attributeValue) > length2:
                        registroAllAttributes = \
                            ownUtils.concatenarMensajeRetroalimentacion(
                                registroAllAttributes,
                                (f'El valor del atributo es muy largo'
                                 f' y fue recortado a '
                                 f'{length2} caracteres'))
                    attributeBaseProduct = ProductBaseAttribute(
                        attributeName,
                        attributeValue
                    )
                    attributesProductBase.append(attributeBaseProduct)
                except Exception as e:
                    erroresAllAttributes = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            erroresAllAttributes, f'{str(e)}')
                if erroresAllAttributes == '':
                    productBase.ProductBaseAttributes = attributesProductBase
        except Exception as e:
            print(str(e))
            erroresAllAttributes = 'Atributos debe ser de tipo dict'
    else:
        registroAllAttributes = 'Producto base sin atributos'
    return registroAllAttributes, erroresAllAttributes


def agregarTagsProductoBase(tags, productBase):
    registroAllTags = ''
    erroresAllTags = ''
    if tags:
        try:
            tagsProductBase = []
            for miniTagValue in tags:
                try:
                    if not miniTagValue:
                        raise Exception(
                            'El nombre del tag debe ser obligatorio')
                    column = ProductBaseTag.TagValue.property.columns[0]
                    length = column.type.length
                    if len(miniTagValue) > length:
                        raise Exception(
                            f"El Tag debe ser menor a "
                            f"{length}")
                    if not ownUtils.is_valid_name(miniTagValue):
                        raise Exception(
                            f"Nombre del Tag no válido"
                            f" ({miniTagValue}), debe "
                            "contener solo letras,- o '")

                    tagBaseProduct = ProductBaseTag(
                        miniTagValue
                    )
                    tagsProductBase.append(tagBaseProduct)
                except Exception as e:
                    erroresAllTags = \
                        ownUtils.concatenarMensajeRetroalimentacion(
                            erroresAllTags, f'{str(e)}')
                if erroresAllTags == '':
                    productBase.ProductBaseTags = tagsProductBase
        except Exception as e:
            print(str(e))
            erroresAllTags = 'Tag debe ser de tipo lista'
    else:
        registroAllTags = 'Producto base sin tags'
    return registroAllTags, erroresAllTags


product_extended = {
    'dimensions': {},
    'photos': {},
    'productVariationCustom': {
        'customVariationValue': {
            'customVariation': {}
        },
    }
}

publicationProducts = {
    'publicationProduct': {
        'publicationBase': {
            'marketplace': {
                'supportedMarketplace': {}
            }
        }
    }
}

extras = ['productImage', 'productDescription']
empty_store = {'store': {}}
store_zone = {"store": {"zone": {}}}
supplierStore_zone = {"supplierStore": {"zone": {}}}
publicationProducts_product_store = {'publicationProducts_product_store': publicationProducts}


def stores(inside_product_stores):
    return {
        'product_stores': inside_product_stores,
    }


stores_empty = stores(empty_store)
stores_zone = stores(store_zone)
publications = stores(publicationProducts_product_store)
# ProductBase
productBase_extended = {
    'dimensions': {},
    'photos': {},
    'attributes': {},
    'tags': {},
    'productBase_CustomVariations': {'customVariation': {'customVarionValues': {}}}
}


products_extended = {
    **productBase_extended,
    "products": product_extended
}


products_extended_publications = {
    **productBase_extended,
    "products": {
        **product_extended,
        **publications
    }
}


product_stores = {
    'products': stores_empty
}

product_stores_extended = {
    **productBase_extended,
    'products': {
        **product_extended,
        **stores_empty
    }
}


consolidated_info = {
    'products': {
        'extras': extras
    }
}


# Product
extended0productBase0extras = {
    **product_extended,
    "productBase": {},
    'extras': extras
}


extended_stores = {
    **extended0productBase0extras,
    **stores_zone
}


extended_publications = {
    **extended0productBase0extras,
    **publicationProducts
}
