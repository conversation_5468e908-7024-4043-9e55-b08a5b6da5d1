from sistemaOrdenes.configs import ADMIN_ROLE, \
    ADMNISTRATIVE_ACCOUNTANT_ROLE, SERVICES_ROLE, ROLES
from flask import jsonify, make_response, send_file
from flask_jwt_extended import jwt_required, get_jwt
from sistemaOrdenes.app.models.db import ScopedSession
from sqlalchemy import desc, or_
from flask_smorest import Blueprint
import traceback
from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.app.models.Models import Order
from sistemaOrdenes.marketplaces.marketplaceUtils import get_marketplace_obj

guides = Blueprint("Guides", __name__, description="Operations on Orders")


@guides.route('/api/guides/getPDF/<orderId>', methods=['GET'])
# @jwt_required()
@ownUtils.my_decorator_http_manage
def return_pdf(orderId, session):
    order = session.query(Order).get(orderId)
    publication = order.ProductsInOrder[0].PublicationProduct
    if not publication:
        raise Exception("La orden no esta relacionada a ninguna publicación")
    supported_market_name = publication.PublicationBase.Marketplace.SupportedMarketplace.Name
    shipping_number = order.OrderShippingInfo.ShippingNumber
    marketplace = get_marketplace_obj(session, supported_market_name)
    print(shipping_number)
    file, file_name = marketplace.get_shipping_label(shipping_number)
    return send_file(file, download_name=file_name, as_attachment=False)


@guides.route('/api/guides/getInfo/<orderId>', methods=['GET'])
# @jwt_required()
@ownUtils.my_decorator_http_manage
def return_infoShipping(orderId, session):
    order = session.query(Order).get(orderId)
    publication = order.ProductsInOrder[0].PublicationProduct
    if not publication:
        raise Exception("La orden no esta relacionada a ninguna publicación")
    supported_market_name = publication.PublicationBase.Marketplace.SupportedMarketplace.Name
    shipping_number = order.OrderShippingInfo.ShippingNumber
    marketplace = get_marketplace_obj(session, supported_market_name)
    shipping_info = marketplace.get_shipping_info(shipping_number)
    make_response(jsonify({'shipping_info': shipping_info}), 200)
