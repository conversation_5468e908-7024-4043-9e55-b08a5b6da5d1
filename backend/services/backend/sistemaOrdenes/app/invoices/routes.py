from flask import jsonify, request, make_response, abort
from flask_jwt_extended import jwt_required, get_jwt
import traceback

from sistemaOrdenes.app.models.Models import Invoice, EntryDocument, ProductInEntryDocument, Product, \
    Store, Product_Store, ProductBase, EntryDocumentStackableComment, \
    LocationLevelItem, LocationLevelItem_Product_Store, DirectInventoryMovement, User, ProductEnteredIntoStore
from sistemaOrdenes.app.models.db import ScopedSession
from sqlalchemy import desc
from flask_smorest import Blueprint
from .schemas import FacturasCommentsSchema, InvoiceSchema, InternalSkuSchema
from sistemaOrdenes.app import ownUtils
from sistemaOrdenes.configs import ADMIN_ROLE, \
    WAREHOUSE_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE, ROLES
from .invoiceUtils import armarQueryFiltrosFacturas, \
    armarQueryBuscadorFacturas, save_invoice_file, agregar_Products_Invoice, \
    fuzzyFacturasSearch
from sistemaOrdenes.app.ownUtils import checkInt, manage_request_field, \
    validate_if_object_exists, get_cost_from_string, revisarIntPositivo


invoices = Blueprint("Invoices", __name__,
                     description="Operations on Invoices")


@invoices.route('/api/invoices/numfacturasfiltro')
# @invoices.arguments(numFacturasFiltroSchema, location="query")
@jwt_required()
@ownUtils.my_decorator_http_manage2(ROLES)
def return_num_facturas_filtro(session):  # numFacturasFiltroSchema
    claims = get_jwt()
    role = claims.get('role')
    admite_roles = [ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE]
    admite_roles.append(WAREHOUSE_ROLE)
    if role in ROLES:
        proveedores = request.args.get('proveedores')
        fechaInicial = request.args.get('fechaInicial')
        fechaFinal = request.args.get('fechaFinal')
        # Buscador
        search = request.args.get('search')
        # Buscador
        query = armarQueryFiltrosFacturas(
            proveedores, fechaInicial, fechaFinal, session)
        query = armarQueryBuscadorFacturas(query, search)
        registros = query.count()
        return make_response(
            jsonify({'numeroFacturas': registros}), 200)
    else:
        # Return 403 if user role is not
        return make_response(
            'No tienes permisos para ver facturas', 403)


@invoices.route('/api/invoices/facturasFiltro')
# @invoices.arguments(FacturasFiltroSchema, location="query")
@jwt_required()
@ownUtils.my_decorator_http_manage2(ROLES)
def return_facturas_filtro(session):  # FacturasFiltroSchema
    claims = get_jwt()
    role = claims.get('role')
    if role in ROLES:
        proveedores = request.args.get('proveedores')
        fechaInicial = request.args.get('fechaInicial')
        fechaFinal = request.args.get('fechaFinal')
        # Buscador
        search = request.args.get('search')
        # Buscador
        offset = request.args.get('offset')
        nc = request.args.get('next')
        query = armarQueryFiltrosFacturas(
            proveedores, fechaInicial, fechaFinal, session)
        query = armarQueryBuscadorFacturas(query, search)
        if ((offset is not None and offset != "") and (nc is not None and nc != "")):
            query = query.order_by(
                desc(EntryDocument.IssueDate)).offset(offset).limit(nc)
        else:
            query = query.order_by(
                desc(EntryDocument.IssueDate)).offset(0).limit(30)
        facturasFiltradas = query.all()
        if (role == ADMIN_ROLE or role == ADMNISTRATIVE_ACCOUNTANT_ROLE or role == WAREHOUSE_ROLE):

            scopeDict = {
                'basics': ['paymentMethod', 'paymentForm', 'paymentConditions', 'currency'],
                'productsInEntryDocument': {
                    'basics': ['discountRate', 'unitPrice',],
                    'productInEntryDocumentMotions': {},
                    'productInEntryDocumentSeries': {},
                    'productInEntryDocumentTaxes': {},
                    'productBase': {},
                },
                'entryDocumentStackableComments': {},
                'invoice': {}
            }
        else:
            scopeDict = {
                'productsInEntryDocument': {
                    'productBase': {},
                },
                'entryDocumentStackableComments': {},
                'invoice': {}
            }
        facturasFiltradasSerizalizadas = [facturaFiltrada.serialize(scope=scopeDict) for facturaFiltrada in facturasFiltradas]
        return {'facturas': facturasFiltradasSerizalizadas}
    else:
        # Return 403 if not valid role user
        return make_response('No tienes permisos para ver la información de las facturas', 403)


@invoices.route('/api/invoices/upfile', methods=['POST'])
@jwt_required()
def process_xml_file():
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        request_files = request.files
        if (role == WAREHOUSE_ROLE or role == ADMIN_ROLE or role == ADMNISTRATIVE_ACCOUNTANT_ROLE):
            createdResponse = save_invoice_file(request_files, session)
        else:
            # Return 403 if user is valid user but it is not Admin
            createdResponse = make_response('No tienes permisos para dar subir facturas', 403)
    except Exception as e:
        session.rollback()
        createdResponse = make_response(jsonify({'errores': f'Ocurrio un error inesperado: {str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


def valid_info_invoice(data):
    uuid = ownUtils.manage_request_field(field_name='uuid', container_dict=data, validation_function=ownUtils.is_valid_uuid, isRequired=True)
    serie = ownUtils.manage_request_field(Invoice.Serie, field_name='serie', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=True)
    csd_serie = ownUtils.manage_request_field(Invoice.CsdSerie, field_name='csdSerie', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=True)
    cfdi_usage = ownUtils.manage_request_field(Invoice.CfdiUsage, field_name='cfdiUsage', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=True)
    branch_office = ownUtils.manage_request_field(Invoice.BranchOffice, field_name='branchOffice', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=False)
    return uuid, serie, csd_serie, cfdi_usage, branch_office


def valid_info_entryDocument(data, is_invoice):
    internalId = ownUtils.manage_request_field(EntryDocument.InternalId, field_name='internalId', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=True)
    # receiverRfc = ownUtils.manage_request_field(field_name='receiverRfc', container_dict=data, validation_function=ownUtils.is_valid_rfc, isRequired=False)
    issuePlace = ownUtils.manage_request_field(EntryDocument.IssuePlace, field_name='issuePlace', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=True)
    issueDate = ownUtils.manage_request_field(field_name='issueDate', container_dict=data, validation_function=ownUtils.is_valid_date, isRequired=True)
    certificationDate = ownUtils.manage_request_field(field_name='certificationDate', container_dict=data, validation_function=ownUtils.is_valid_date, isRequired=is_invoice)
    expirationDate = ownUtils.manage_request_field(field_name='expirationDate', container_dict=data, validation_function=ownUtils.is_valid_date, isRequired=is_invoice)
    paymentMethod = ownUtils.manage_request_field(field_name='paymentMethod', container_dict=data, validation_function=ownUtils.check_paymentMethod, isRequired=True)
    paymentForm = ownUtils.manage_request_field(EntryDocument.PaymentForm, field_name='paymentForm', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=True)
    currency = ownUtils.manage_request_field(field_name='currency', container_dict=data, validation_function=ownUtils.check_currency, isRequired=True)
    issuerRfc = ownUtils.manage_request_field(field_name='issuerRfc', container_dict=data, validation_function=ownUtils.is_valid_rfc, isRequired=is_invoice)
    saleOrder = ownUtils.manage_request_field(EntryDocument.SaleOrder, field_name='saleOrder', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=False)
    purchaseOrder = ownUtils.manage_request_field(EntryDocument.PurchaseOrder, field_name='purchaseOrder', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=False)
    paymentConditions = ownUtils.manage_request_field(EntryDocument.PaymentConditions, field_name='paymentConditions', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=False)
    salesRep = ownUtils.manage_request_field(EntryDocument.SalesRep, field_name='salesRep', container_dict=data, validation_function=ownUtils.check_len_sql_column, isRequired=False)
    return internalId, issuePlace, issueDate, certificationDate, expirationDate, paymentMethod, paymentForm, currency, issuerRfc, saleOrder, purchaseOrder, paymentConditions, salesRep  # receiverRfc


@invoices.route('/api/invoices/invoice/delete/<invoiceId>', methods=['DELETE'])
@ownUtils.my_decorator_http_manage
def delete_invoice(session, invoiceId):
    invoice = validate_if_object_exists(session, 'invoiceId', 'native', invoiceId, EntryDocument)
    session.delete(invoice)
    session.commit()
    return make_response(jsonify({'mensaje': 'Factura Eliminada Exitosamente'}), 200)


@invoices.route('/api/invoices/productBase/getPossibleProductToEnter/<int:productInEntryDocumentId>')
# @invoices.arguments(InternalSkuSchema)
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[WAREHOUSE_ROLE, ADMIN_ROLE])
def relacionSkuProduct_ProductInInvoice(session, productInEntryDocumentId):
    product_in_invoice = session.query(ProductInEntryDocument).get(productInEntryDocumentId)
    if not product_in_invoice:
        raise Exception('La partida no existe')
    if not product_in_invoice.ProductBase:
        raise Exception('La partida no esta relacionado con ningún producto base')
    productBaseProducts = product_in_invoice.ProductBase.Products
    product_skus = [{"product_sku": productBaseProduct.InternalSku, "brand": productBaseProduct.ProductBase.Brand, "model": productBaseProduct.ProductBase.Model, "variationDescription": productBaseProduct.VariationTitle if productBaseProduct.VariationTitle else productBaseProduct.ProductBase.Description} for productBaseProduct in productBaseProducts]
    productsEnteredIntoStore = product_in_invoice.ProductsEnteredIntoStore
    entered_variations = {}
    for productEnteredIntoStore in productsEnteredIntoStore:
        if productEnteredIntoStore.LocationLevelItem_Product_Store.InternalSku in entered_variations:
            if productEnteredIntoStore.LocationLevelItem_Product_Store.StoreId in entered_variations[productEnteredIntoStore.LocationLevelItem_Product_Store.InternalSku]['stores']:
                print('0-----------------------------31')
                print(productEnteredIntoStore)
                print('0-----------------------------32')
                entered_variations[productEnteredIntoStore.LocationLevelItem_Product_Store.InternalSku]["stores"][productEnteredIntoStore.LocationLevelItem_Product_Store.StoreId]['internalLocations'][productEnteredIntoStore.LocationLevelItem_Product_Store.LocationLevelItemId] = {
                    "locationLevelItem": productEnteredIntoStore.LocationLevelItem_Product_Store.LocationLevelItem.serialize(),
                    "amount": productEnteredIntoStore.Amount,
                    "id": productEnteredIntoStore.Id}
            else:
                print('0-----------------------------11')
                print(productEnteredIntoStore)
                print('0-----------------------------12')
                entered_variations[productEnteredIntoStore.LocationLevelItem_Product_Store.InternalSku]['stores'][productEnteredIntoStore.LocationLevelItem_Product_Store.StoreId] = {
                    "store": productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store.Store.serialize(),
                    "internalLocations": {
                        productEnteredIntoStore.LocationLevelItem_Product_Store.LocationLevelItemId: {
                            "locationLevelItem": productEnteredIntoStore.LocationLevelItem_Product_Store.LocationLevelItem.serialize(),
                            "amount": productEnteredIntoStore.Amount,
                            "id": productEnteredIntoStore.Id
                        }
                    }
                }
        else:
            print('0-----------------------------21')
            print(productEnteredIntoStore)
            print('0-----------------------------22')
            entered_variations[productEnteredIntoStore.LocationLevelItem_Product_Store.InternalSku] = {
                "variationDescription": productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store.Product.VariationTitle if productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store.Product.VariationTitle else productEnteredIntoStore.ProductInInvoice.ProductBase.Description,
                "brand": productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store.Product.ProductBase.Brand,
                "model": productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store.Product.ProductBase.Model,
                "stores": {
                    productEnteredIntoStore.LocationLevelItem_Product_Store.StoreId: {
                        "store": productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store.Store.serialize(),
                        "internalLocations": {
                            productEnteredIntoStore.LocationLevelItem_Product_Store.LocationLevelItemId: {
                                "locationLevelItem": productEnteredIntoStore.LocationLevelItem_Product_Store.LocationLevelItem.serialize(),
                                "amount": productEnteredIntoStore.Amount,
                                "id": productEnteredIntoStore.Id
                            }
                        }
                    },
                }
            }
    for product in product_skus:
        if product["product_sku"] not in entered_variations:
            entered_variations[product["product_sku"]] = {
                "variationDescription": product["variationDescription"],
                "brand": product["brand"],
                "model": product["model"],
                "stores": []
            }
    entries = {
        "internalBaseSku": product_in_invoice.ProductBase.InternalBaseSku,
        "entries_by_variation": entered_variations
    }
    return make_response(jsonify(entries), 200)


@invoices.route('/api/products/fuzzyFacturasFiltro')
def return_invoices_filtrofuzzy():
    session = ScopedSession()
    try:
        # Buscador
        search = request.args.get('search')
        # Buscador"""
        facturas_filtrados = fuzzyFacturasSearch(session, search).all()
        facturas_filtrados_json = [minifacturas.serialize_basic_data() for minifacturas in facturas_filtrados]
        createdResponse = {'facturas': facturas_filtrados_json}
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


@invoices.route('/api/invoices/product/relateProductReturnVariations', methods=['PUT'])
@jwt_required()
def relate_sku_productInInvoice_return_variations():
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role == WAREHOUSE_ROLE or role == ADMIN_ROLE:
            data = request.get_json()
            product_in_invoice = validate_if_object_exists(session, 'productFromEntryDocumentId', 'dict', data, ProductInEntryDocument)
            if not product_in_invoice.ProductBase:
                product_base = validate_if_object_exists(session, 'internalBaseSku', 'dict', data, ProductBase)
                product_in_invoice.ProductBase = product_base
                products = product_base.Products
                entered_variations = []
                for product in products:
                    entered_variation = {
                        "internalSku": product.InternalSku,
                        "variationDescription": product.VariationTitle if product.VariationTitle else product.ProductBase.Description,
                        "enters": []
                    }
                    entered_variations.append(entered_variation)
                session.commit()
                created_response = make_response(jsonify({'enteredVariations': entered_variations}), 200)
            else:
                raise Exception(f'La partida  ya esta relacionada con {product_in_invoice.InternalBaseSku}')
        else:
            created_response = make_response(jsonify({'login': 'No tienes los permisos necesarios'}), 202)
    except Exception as e:
        traceback.print_exc()
        session.rollback()
        created_response = make_response(jsonify({'errores': str(e)}), 500)
        print(str(created_response))
    finally:
        session.close()
        return created_response


def get_ProductEnteredIntoStore(productInEntryDocument_productsEnteredIntoStore, internalSku, locationLevelItemId):
    print('0999999999999999>>>>>>>>>>>>>>>>>>>>>>>>><')
    print(productInEntryDocument_productsEnteredIntoStore)
    print('0999999999999999>>>>>>>>>>>>>>>>>>>>>>>>><')
    for productInEntryDocument_productEnteredIntoStore in productInEntryDocument_productsEnteredIntoStore:
        print('11111111111111')
        print(productInEntryDocument_productEnteredIntoStore.LocationLevelItem_Product_Store)
        print(productInEntryDocument_productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store)
        print(productInEntryDocument_productEnteredIntoStore.LocationLevelItem_Product_Store.LocationLevelItem)
        print(productInEntryDocument_productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store.Product.InternalSku)
        print(productInEntryDocument_productEnteredIntoStore.LocationLevelItem_Product_Store.LocationLevelItem.Id)
        print('11111111111111')
        if productInEntryDocument_productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store.Product.InternalSku.upper() == internalSku.upper() and int(productInEntryDocument_productEnteredIntoStore.LocationLevelItem_Product_Store.LocationLevelItem.Id) == locationLevelItemId:
            return productInEntryDocument_productEnteredIntoStore
    return None


def update_stock_cost_product_store(product_store, amount, cost):
    new_stock = product_store.Stock + amount
    product_store.Stock = new_stock
    product_store.Cost = ((cost * amount) + (product_store.Cost * product_store.Stock)) / (new_stock)


def get_or_create_product_store(session, internalsku, storeId, amount, cost):
    product_store = session.query(Product_Store).get((internalsku, storeId))
    if product_store:
        update_stock_cost_product_store(product_store, amount, cost)
    else:
        product = validate_if_object_exists(session, 'internalsku', 'native', internalsku, Product)
        store = validate_if_object_exists(session, 'storeId', 'native', storeId, Store)
        product_store = Product_Store(stock=amount, cost=cost)
        product_store.Product = product
        product_store.Store = store
    return product_store


def get_or_create_locationLevelItem_product_store(session, location_level_item_id, internalsku, amount, cost):
    locationLevelItem_product_store = session.query(LocationLevelItem_Product_Store).filter(LocationLevelItem_Product_Store.LocationLevelItemId == location_level_item_id, LocationLevelItem_Product_Store.InternalSku == internalsku).first()
    if locationLevelItem_product_store:
        if locationLevelItem_product_store.Stock + amount < 0:
            raise Exception('No hay productos suficientes para realizar esta salida')
        locationLevelItem_product_store.Stock += amount
        update_stock_cost_product_store(locationLevelItem_product_store.Product_Store, amount, cost)
    else:
        locationLevelItem = validate_if_object_exists(session, 'locationLevelItemId', 'native', location_level_item_id, LocationLevelItem)
        product_store = get_or_create_product_store(session, internalsku, locationLevelItem.StoreId, amount, cost)
        if amount < 0:
            raise Exception('No hay productos suficientes para realizar esta salida')
        locationLevelItem_product_store = LocationLevelItem_Product_Store(stock=amount)
        locationLevelItem_product_store.LocationLevelItem = locationLevelItem
        locationLevelItem_product_store.Product_Store = product_store
    return locationLevelItem_product_store


def get_or_create_productEnteredIntoStore(session, location_level_item_id, internalsku, amount, cost, productInEntryDocument_productsEnteredIntoStore, productInEntryDocument):
    productEnteredIntoStore = get_ProductEnteredIntoStore(productInEntryDocument_productsEnteredIntoStore, internalsku, location_level_item_id)
    if productEnteredIntoStore:
        if productEnteredIntoStore.Amount + amount < 0:
            raise Exception('No se pueden retirar una cantidad mayor a la ingresada')
        productEnteredIntoStore.Amount += amount
        productEnteredIntoStore.LocationLevelItem_Product_Store.Stock += amount
        update_stock_cost_product_store(productEnteredIntoStore.LocationLevelItem_Product_Store.Product_Store, amount, cost)
    else:
        locationLevelItem_product_store = get_or_create_locationLevelItem_product_store(session, location_level_item_id, internalsku, amount, cost)
        if amount < 0:
            raise Exception('No se pueden hacer retiros sin ingresos en un producto de un documento de entrada')
        productEnteredIntoStore = ProductEnteredIntoStore(amount=amount)
        productEnteredIntoStore.LocationLevelItem_Product_Store = locationLevelItem_product_store
        productEnteredIntoStore.ProductInEntryDocument = productInEntryDocument
    return productEnteredIntoStore


def create_direct_inventory_movement(amount, entry, reasons, user, productEnteredIntoStore, locationLevelItem_product_store):
    directInventoryMovement = DirectInventoryMovement(Amount=amount, Entry=entry, Reasons=reasons)
    directInventoryMovement.User = user
    directInventoryMovement.ProductEnteredIntoStore = productEnteredIntoStore
    directInventoryMovement.LocationLevelItem_Product_Store = locationLevelItem_product_store


@invoices.route('/api/entryDocument/productInEntryDocument/directInventoryMovement', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage4(allowed_roles=[WAREHOUSE_ROLE, ADMIN_ROLE])
def post_productEnteredIntoStoreMovements(session, claims):
    """
        {
            "amount": 10,  # int
            "cost": $100  # float
            "entry": true  # boolean
            "locationLevelItemId":  # int (id)
            "internalSku":  # str (internalSku)
            "reasons": str
        }
    """
    # info for body request
    data = ownUtils.get_info_from_json(request)
    # user who made entry getting from credentials
    userNow = claims.get('userId')
    user = validate_if_object_exists(session, 'userId', 'native', userNow, User)
    # validate amount fpr movement
    amount_request = data.get('amount')
    error = ownUtils.revisarIntPositivo(amount_request)
    if error:
        raise Exception(f'La cantidad debe ser un entero: {error}')
    amount = int(amount_request)
    # validate cost for movement
    cost_request = data.get('cost')
    error = ownUtils.revisarFloatPositivo(cost_request)
    if error:
        raise Exception(f'El costo es incorrecto: {error}')
    cost = float(cost_request)
    # getting reasons for movement
    reasons_request = data.get('reasons')
    if not reasons_request:
        raise Exception('El campo reasons es obligatorio')
    reasons = str(reasons_request)
    # entry o exit
    entry_request = data.get('entry')
    if entry_request == 'true' or entry_request == 'True' or entry_request is True:
        entry = True
    elif entry_request == 'false' or entry_request == 'False' or entry_request is False:
        entry = False
    else:
        raise Exception('El campo entry debe ser un booleano')
    location_level_item_id = data.get('locationLevelItemId')
    internalsku = data.get('internalSku')
    locationLevelItem_Product_Store = get_or_create_locationLevelItem_product_store(session, location_level_item_id, internalsku, amount, cost)
    create_direct_inventory_movement(amount, entry, reasons, user, None, locationLevelItem_Product_Store)
    session.commit()
    return make_response(jsonify({'info': 'Movimiento dirercto de inventario realizado exitosamente'}), 200)


@invoices.route('/api/invoices/product/enterInventory', methods=['POST'])
# @invoices.arguments(InternalSkuSchema)
@jwt_required()
@ownUtils.my_decorator_http_manage4(allowed_roles=[WAREHOUSE_ROLE, ADMIN_ROLE])
def enterInventory(session, claims):
    """
    {
        "productFromEntryDocumentId": 26, # int(id)  -->  entryDocumentId
        "productsEnteredIntoStore": [
            {
                "internalSku":"3MSCOTHT_1-1",
                "enters":[
                    {
                        "locationLevelItemId":43,
                        "amount":5
                    },
                ]
            }
        ]
    }
    """
    # info for body request
    data = ownUtils.get_info_from_json(request)
    # user who made entry getting from credentials
    userNow = claims.get('userId')
    user = validate_if_object_exists(session, 'userId', 'native', userNow, User)
    # Getting data about productInEntryDocument (item from entryDocument)
    #   Validate if productInEntryDocument exists
    productInEntryDocument = validate_if_object_exists(session, 'productFromEntryDocumentId', 'dict', data, ProductInEntryDocument)
    #   Validate if productInEntryDocument is related with a product base already
    productBase = productInEntryDocument.ProductBase
    if not productBase:
        abort(make_response(jsonify({'error': "La partida no esta relacionada con ningún producto"}), 400))
    #   Validate if productInEntryDocument price
    internalSkus_for_productBase = [product.InternalSku.upper() for product in productBase.Products]
    productInEntryDocument_unitPrice = productInEntryDocument.UnitPrice
    cost = get_cost_from_string(productInEntryDocument_unitPrice)
    
    if not cost:
        abort(make_response(jsonify({'error': "El costo ingresado no es válido"}), 400))
    #    Getting stock entered so far (ProductsEnteredIntoStore represent the stock entered into internal location for each product variation  )
    
    productInEntryDocument_productsEnteredIntoStore = productInEntryDocument.ProductsEnteredIntoStore
    print('7777777777777777777777777777777777777777777777')
    print( productInEntryDocument.ProductsEnteredIntoStore)
    print('mmmmmmmmmmmmmmmmmmmm')
    print( productInEntryDocument)
    print('7777777777777777777777777777777777777777777777')
    entered_product_num = 0
    #   Getting total amount of products entered for this entry document item
    for p in productInEntryDocument_productsEnteredIntoStore:
        entered_product_num += p.Amount

        # Validate entryDocument units
    productInEntryDocument_units = productInEntryDocument.Units
    #       Validate if all products on the invoice have been entered
    if entered_product_num == productInEntryDocument_units:
        abort(make_response(jsonify({'error': "Este producto ya fue surtido"}), 400))
    if entered_product_num > productInEntryDocument_units:
        abort(make_response(jsonify({'error': "El producto ingreso más productos que los marcados en la factura. Consultar a soporte"}), 400))
    # Valdate product enters
    products_entered_into_store_request = data.get('productsEnteredIntoStore')
    if not products_entered_into_store_request:
        abort(make_response(jsonify({'error': "productsEnteredIntoStore obligartorio"}), 400))
    if type(products_entered_into_store_request) is not list:
        abort(make_response(jsonify({'error': "productsEnteredIntoStore debe ser una lista"}), 400))
    # Iterate over each product productEnteredIntoStore (sku and enters)
    for index, product_entered_into_store_aux in enumerate(products_entered_into_store_request):
        print(f'0.................................................{index}-i')
        print(productInEntryDocument_productsEnteredIntoStore)
        # validate if product_entered_into_store_aux is valid
        if type(product_entered_into_store_aux) is not dict:
            abort(make_response(jsonify({'error': f'{index}: Los productos dentro de la lista deben ser dicts'}), 400))
        internalsku = str(product_entered_into_store_aux.get('internalSku'))
        if not internalsku:
            abort(make_response(jsonify({'error': f'{index}: internalSku obligatorio para cada producto a ingresar'}), 400))
        if internalsku not in internalSkus_for_productBase:
            abort(make_response(jsonify({'error': f'Esta variación {internalsku} no corresponde al productoBase relacionado al documento de entrada'}), 400))
        enters = product_entered_into_store_aux.get('enters')
        if type(enters) is not list:
            abort(make_response(jsonify({'error': f'{index}: Las entradas para cada partida deben ser una lista'}), 400))
        # Iterate over each enter (locationLevelItemId and amount)
        for enter in enters:
            print('empter-1')
            print(enter)
            print('0')
            print(productInEntryDocument_productsEnteredIntoStore)
            
            # validate if enter is valid
            if type(enter) is not dict:
                abort(make_response(jsonify({'error': f'{index}: Las entradas para cada partida deben ser dicts'}), 400))
            # validate amount
            amount = enter.get('amount')
            if not checkInt(amount):
                abort(make_response(jsonify({'error': 'La cantidad debe ser un entero'}), 400))
            amount = int(amount)
            entered_product_num += amount
            if entered_product_num > productInEntryDocument_units:
                abort(make_response(jsonify({'error': 'La cantidad de productos total ingresada es superior a la expresada por la factura'}), 400))
            if entered_product_num < 0:
                abort(make_response(jsonify({'error': 'Se estan retirando más productos de los ingresados en la factura'}), 400))
            # validate locationLevelItemId
            location_level_item_id = enter.get('locationLevelItemId')
            productEnteredIntoStore = get_or_create_productEnteredIntoStore(session, location_level_item_id, internalsku, amount, cost, productInEntryDocument_productsEnteredIntoStore, productInEntryDocument)
            # -------------------cuak retornar entry y mensaje
            create_direct_inventory_movement(amount, True, 'Ingreso de producto por factura', user, productEnteredIntoStore, None)
            print(productInEntryDocument_productsEnteredIntoStore)
            
            print('emter')
        print(productInEntryDocument_productsEnteredIntoStore)
        print(f'0.................................................{index}-o')
        
    productInEntryDocument.TotalEntered = entered_product_num
    session.commit()
    return make_response(jsonify({'info': 'Productos ingresados exitosamente'}), 200)


@invoices.route('/api/entryDocument/productInEntryDocument/directInventoryMovement/<int:id>')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE])
def get_productEnteredIntoStoreMovement_by_id(session, id):
    directInventoryMovement = validate_if_object_exists(session, 'id', 'native', id, DirectInventoryMovement)
    directInventoryMovement_serialized = directInventoryMovement.serialize()
    return make_response(jsonify({'directInventoryMovement': directInventoryMovement_serialized}), 200)


@invoices.route('/api/entryDocument/productInEntryDocument/productsEnteredIntoStore/<int:id>/directInventoryMovement')
@jwt_required()
@ownUtils.my_decorator_http_manage2(allowed_roles=[ADMIN_ROLE])
def get_directInventoryMovements(session, id):
    productEnteredIntoStore = validate_if_object_exists(session, 'id', 'native', id, ProductEnteredIntoStore)
    print(productEnteredIntoStore)
    direct_inventory_movements = productEnteredIntoStore.DirectInventoryMovements
    print(direct_inventory_movements)
    scope = {
        'user': {
            'basics': ['alias']
        }
    }
    mdirect_inventory_movements_serialized = [movement.serialize(scope=scope) for movement in direct_inventory_movements]
    return make_response(jsonify({'direct_inventory_movements': mdirect_inventory_movements_serialized}), 200)


@invoices.route('/api/invoices/invoice/newComment', methods=['POST'])
@jwt_required()
def newComment_Invoice_by_ID():
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role not in ROLES:
            return make_response(jsonify({
                'error':
                "No tienes permiso para agregar un comentario"}), 202)
        data = request.get_json()
        print('0----------------------------->>>>>>>>>>>>>>>')
        print(data)
        print('0----------------------------->>>>>>>>>>>>>><')
        invoice = session.query(EntryDocument).get(data.get('entryDocumentId'))
        if invoice:
            if len(data.get('newComment')) <= 100:
                rightNow = ownUtils.get_time_zone_now()
                new_comment = EntryDocumentStackableComment(claims.get(
                    'userId'), data.get('newComment'), rightNow)
                invoice.EntryDocumentStackableComments.append(new_comment)
                session.commit()
                createdResponse = make_response(jsonify(
                    {'invoice':
                     "Comentario Agreado exitosamente",
                     "commentInfo": new_comment.serialize()}), 200)
            else:
                createdResponse = make_response(jsonify({
                    'commentInfo':
                    "Excede los 100 caracteres"}), 202)
        else:
            createdResponse = make_response(
                jsonify({'invoice': "UuId no encontrada"}), 404)

    except Exception as e:
        session.rollback()
        traceback.print_exc()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@invoices.route('/api/invoices/invoice/updateComment', methods=['PUT'])
@jwt_required()
def updateComments():
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if role not in ROLES:
            return make_response(jsonify({
                'error':
                "No tienes permiso para modificar comentarios"}), 202)

        data = request.get_json()
        print('ñsdfsjndsfkjkjfdkljfkfj->>>>>>>>>>>><<<=================')
        print(data)
        print('ñsdfsjndsfkjkjfdkljfkfj->>>>>>>>>>>><<<=================')
        comment = session.query(
            EntryDocumentStackableComment).get(data.get('commentId'))
        userNow = claims.get('userId')
        if not (role == ADMIN_ROLE or userNow == comment.UserId):
            return make_response(jsonify({
                'error':
                "No tienes permiso para modificar este comentario"}), 202)
        if comment:
            if len(data.get('commentInfo')) > 100:
                createdResponse = make_response(jsonify({
                    'commentInfo':
                    "Excede los 100 caracteres"}), 202)
            else:
                rightNow = ownUtils.get_time_zone_now()
                comment.Comment = data.get('commentInfo')
                comment.TimeStamp = rightNow

                session.commit()
                regression_comment = session.\
                    query(EntryDocumentStackableComment).\
                    filter(
                        EntryDocumentStackableComment.Comment == data.get('commentInfo'),
                        EntryDocumentStackableComment.TimeStamp == rightNow).first()

                createdResponse = make_response(jsonify(
                    {'invoice':
                     "Comentario Actualizado exitosamente",
                     "commentInfo":
                     regression_comment.serialize()}), 200)
        else:
            createdResponse = make_response(
                jsonify({'comment': "ID no encontrada"}), 404)

    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@invoices.route('/api/invoices/invoice/deleteComment', methods=['DELETE'])
@jwt_required()
def deleteComments():
    try:
        session = ScopedSession()
        claims = get_jwt()
        role = claims.get('role')
        if (not role == ADMIN_ROLE):
            return make_response(jsonify({
                'error':
                "No tienes permiso para eliminar un comentario"}),
                202)

        data = request.get_json()
        print('ñsdfsjndsfkjkjfdkljfkfj->>>>>>>>>>>><<<=================qq')
        print(data)
        print('ñsdfsjndsfkjkjfdkljfkfj->>>>>>>>>>>><<<=================qq')
        comment = session.query(
            EntryDocumentStackableComment).get(data.get('commentId'))
        if comment:
            session.delete(comment)
            session.commit()
            createdResponse = make_response(
                jsonify({'comment': "Comentario Eliminado exitosamente"}), 200)
        else:
            createdResponse = make_response(
                jsonify({'comment': "ID no encontrada"}), 404)

    except Exception as e:
        session.rollback()
        createdResponse = make_response(
            jsonify({'error': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@invoices.route('/api/invoices/factura/<int:id>')
# @invoices.arguments(FacturasFiltroSchema, location="query")
@jwt_required()
@ownUtils.my_decorator_http_manage2([ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE, WAREHOUSE_ROLE])
def returnFacturaById(session, id):
    entry_document = validate_if_object_exists(session, 'id', 'native', id, EntryDocument)
    scope_dict = {
        'basics': ['paymentMethod', 'paymentForm', 'paymentConditions', 'currency'],
        'productsInEntryDocument': {
            'basics': ['discountRate', 'unitPrice',],
            'productInEntryDocumentMotions': {},
            'productInEntryDocumentSeries': {},
            'productInEntryDocumentTaxes': {},
            'productBase': {},
        },
        'invoice': {}
    }

    entry_document_serialized = entry_document.serialize(scope=scope_dict)
    return {"entryDocument": entry_document_serialized}


@invoices.route('/api/invoices/factura/<int:id>', methods=['PUT'])
# @invoices.arguments(FacturasFiltroSchema, location="query")
@jwt_required()
@ownUtils.my_decorator_http_manage2([ADMIN_ROLE,])
def update_entry_document(session, id):
    entry_document = validate_if_object_exists(session, 'id', 'native', id, EntryDocument)
    # check if put is possible(according to business rules)
    data = ownUtils.get_info_from_json(request)
    invoice = data.get("invoice")
    internalId, issuePlace, issueDate, certificationDate, expirationDate, paymentMethod, paymentForm, currency, issuerRfc, saleOrder, purchaseOrder, paymentConditions, salesRep = valid_info_entryDocument(data, bool(invoice))  # receiverRfc
    entry_document.InternalId = internalId
    entry_document.IssuePlace = issuePlace
    entry_document.IssueDate = issueDate
    entry_document.CertificationDate = certificationDate
    entry_document.ExpirationDate = expirationDate
    entry_document.PaymentMethod = paymentMethod
    entry_document.PaymentForm = paymentForm
    entry_document.Currency = currency
    entry_document.IssuerRfc = issuerRfc
    entry_document.SaleOrder = saleOrder
    entry_document.purchaseOrder = purchaseOrder
    entry_document.paymentConditions = paymentConditions
    entry_document.salesRep = salesRep
    if invoice:
        if type(invoice) is not dict:
            raise Exception("invoice debe ser un json")
        uuid, serie, csd_serie, cfdi_usage, branch_office = valid_info_invoice(invoice)
        invoice = Invoice(
            uuid=uuid,
            serie=serie,
            csdSerie=csd_serie,
            branchOffice=branch_office,
            cfdiUsage=cfdi_usage
        )
        entry_document.Invoice = invoice
    else:
        entry_document.Invoice = None
    products_in_entry_document = data.get('productsInEntryDocument')
    entry_document.ProductsInEntryDocument = []
    ownUtils.commit_catching_dependency_rule(session)
    agregar_Products_Invoice(products_in_entry_document, entry_document)
    ownUtils.commit_catching_dependency_rule(session)
    return make_response(jsonify({'mensaje': 'Documento de ingreso actualizado exitosamente'}), 200)


@invoices.route('/api/invoices/invoice/upinvoice', methods=['POST'])
# @invoices.arguments(InvoiceSchema)
@jwt_required()
@ownUtils.my_decorator_http_manage2([WAREHOUSE_ROLE, ADMIN_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE])
def upinvoice(session):
    data = ownUtils.get_info_from_json(request)
    invoice = data.get("invoice")
    internalId, issuePlace, issueDate, certificationDate, expirationDate, paymentMethod, paymentForm, currency, issuerRfc, saleOrder, purchaseOrder, paymentConditions, salesRep = valid_info_entryDocument(data, bool(invoice))  # receiverRfc
    entryDocument = EntryDocument(
        internalId=internalId,
        # receiverRfc=receiverRfc,
        issuePlace=issuePlace,
        issueDate=issueDate,
        certificationDate=certificationDate,
        expirationDate=expirationDate,
        paymentMethod=paymentMethod,
        paymentForm=paymentForm,
        currency=currency,
        issuerRfc=issuerRfc,
        saleOrder=saleOrder,
        purchaseOrder=purchaseOrder,
        paymentConditions=paymentConditions,
        salesRep=salesRep
    )
    if invoice:
        if type(invoice) is not dict:
            raise Exception("invoice debe ser un json")
        uuid, serie, csd_serie, cfdi_usage, branch_office = valid_info_invoice(invoice)
        invoice = Invoice(
            uuid=uuid,
            serie=serie,
            csdSerie=csd_serie,
            branchOffice=branch_office,
            cfdiUsage=cfdi_usage
        )
        entryDocument.Invoice = invoice
    products_in_entry_document = data.get('productsInEntryDocument')
    agregar_Products_Invoice(products_in_entry_document, entryDocument)
    session.add(entryDocument)
    ownUtils.commit_catching_unique_constraint(session)
    return make_response(jsonify({'mensaje': 'Documento de ingreso registrado exitosamente'}), 201)
