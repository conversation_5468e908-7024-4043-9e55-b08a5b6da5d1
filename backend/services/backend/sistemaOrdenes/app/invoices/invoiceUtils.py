from sistemaOrdenes.app.models.Models import Invoice, ProductInEntryDocument, \
    ProductInEntryDocumentMotion, ProductInEntryDocumentTax, ProductInEntryDocumentSerie, \
    Product, EntryDocument
from sqlalchemy import or_
from flask import jsonify, make_response
import os
from sistemaOrdenes.app import ownUtils
from .suppliers.invoice_parser import add_or_update_invoice
from werkzeug.utils import secure_filename
from sistemaOrdenes.app import config
import traceback
RUTA_INVOICES = config.Config.INVOICES_FOLDER


def armarQueryFiltrosFacturas(proveedores, fechaInicial, fechaFinal, session):
    query = session.query(EntryDocument)
    if proveedores is not None and proveedores != '':
        proveedoresTuple = tuple(proveedores.split('-'))
        query = query.filter(EntryDocument.IssuerRfc.in_(proveedoresTuple))
    if (fechaInicial is not None and fechaFinal is not None and fechaInicial != '' and fechaFinal != ''):
        query = query.filter(
            EntryDocument.IssueDate.between(fechaInicial, fechaFinal))
    return query


def armarQueryBuscadorFacturas(query, search):
    if search is not None and search != '':
        search = "%{}%".format(search)
        query = query.join(ProductInEntryDocument, EntryDocument.Id == ProductInEntryDocument.EntryDocumentId).\
            join(Invoice, EntryDocument.Id == Invoice.EntryDocumentId).filter(
            or_(
                EntryDocument.InternalId.like(search),
                EntryDocument.IssuerRfc.like(search),
                EntryDocument.SalesRep.like(search),
                Invoice.Serie.like(search),
                ProductInEntryDocument.Model.like(search),
                ProductInEntryDocument.Description.like(search)))
    return query


def save_invoice_file(request_files, session):
    # params reques)_files
    if 'file[]' not in request_files:
        # regresar error
        createdResponse = make_response(
            'Los archivos de facturas deben tener la clave file[]', 500)
    else:
        files = request_files.getlist('file[]')
        errores = ""
        for file in files:
            if file.filename == '':
                errores = ownUtils.logearMensaje(
                    errores, file.filename, 'archivo vacío')
                continue
            if (file and ownUtils.allowed_file_invoices(file.filename) and ownUtils.allowed_name_invoices(file.filename)):
                nombreSeguro = secure_filename(file.filename)
                try:
                    result, supplierOrError = add_or_update_invoice(file, session)
                    if result:
                        directory = os.path.join(
                            RUTA_INVOICES, supplierOrError)
                        if not os.path.exists(directory):
                            os.makedirs(directory)
                        file.save(os.path.join(directory, nombreSeguro))
                    else:
                        errores = ownUtils.logearMensaje(
                            errores, file.filename, supplierOrError)
                except Exception as e:
                    errores = ownUtils.logearMensaje(
                        errores,
                        file.filename, f'El xml no es válido:{str(e)}')
                    continue
            else:
                errores = ownUtils.logearMensaje(
                    errores,
                    file.filename, 'la extension o el nombre no es permitido')
                continue
        if len(errores) == 0:
            createdResponse = make_response(
                'Facturas registradas con exito', 200)
        else:
            createdResponse = make_response(jsonify(
                {'errores':
                 'Los siguientes archivos no fueron subidos:' + str(errores)}), 500)
    return createdResponse


def agregar_Products_Invoice(dictProducts, entryDocument):
    if dictProducts and len(dictProducts) > 0:
        producstInEntryDocument = []
        for invoiceItemNumber, miniProduct in enumerate(dictProducts, start=1):
            model = ownUtils.manage_request_field(ProductInEntryDocument.Model, field_name='model', container_dict=miniProduct, validation_function=ownUtils.check_len_sql_column, isRequired=True)
            supplierSku = ownUtils.manage_request_field(ProductInEntryDocument.SupplierSku, field_name='supplierSku', container_dict=miniProduct, validation_function=ownUtils.check_len_sql_column, isRequired=False)
            # predialNum = ownUtils.manage_request_field(ProductInEntryDocument.PredialNum, field_name='predialNum', container_dict=miniProduct, validation_function=ownUtils.check_len_sql_column, isRequired=False)
            upc = ownUtils.manage_request_field(ProductInEntryDocument.Upc, field_name='upc', container_dict=miniProduct, validation_function=ownUtils.check_len_sql_column, isRequired=False)
            satKey = ownUtils.manage_request_field(ProductInEntryDocument.SatKey, field_name='satKey', container_dict=miniProduct, validation_function=ownUtils.check_len_sql_column, isRequired=True)
            units = ownUtils.manage_request_field(field_name='units', container_dict=miniProduct, validation_function=ownUtils.revisarIntPositivo, isRequired=True)
            discountRate = ownUtils.manage_request_field(field_name='discountRate', container_dict=miniProduct, validation_function=ownUtils.revisar_descuento_a_precio, isRequired=True)
            unitPrice = ownUtils.manage_request_field(field_name='unitPrice', container_dict=miniProduct, validation_function=ownUtils.revisarFloatPositivo, isRequired=True)
            unitKey = ownUtils.manage_request_field(ProductInEntryDocument.UnitKey, field_name='unitKey', container_dict=miniProduct, validation_function=ownUtils.check_len_sql_column, isRequired=True)
            description = ownUtils.manage_request_field(ProductInEntryDocument.Description, field_name='description', container_dict=miniProduct, validation_function=ownUtils.check_len_sql_column, isRequired=True)
            units = int(units)
            discountRate = float(discountRate)
            unitPrice = float(unitPrice)
            motions_r = miniProduct.get('motions')
            motions = []
            if motions_r:
                if type(motions_r) is not list:
                    raise Exception(f"series debe ser una lista del producto {invoiceItemNumber}")
                for indice, motion_r in enumerate(motions_r, start=1):
                    if type(motion_r) is not dict:
                        raise Exception(f"motion {indice} debe ser un dict para las motions del producto {invoiceItemNumber}")
                    Motion = ownUtils.manage_request_field(ProductInEntryDocumentMotion.Motion, field_name='motion', container_dict=motion_r, validation_function=ownUtils.check_len_sql_column, isRequired=True)
                    Customs = ownUtils.manage_request_field(ProductInEntryDocumentMotion.Customs, field_name='customs', container_dict=motion_r, validation_function=ownUtils.check_len_sql_column, isRequired=False)
                    MotionDate = ownUtils.manage_request_field(field_name='motionDate', container_dict=motion_r, validation_function=ownUtils.is_valid_date, isRequired=False)
                    motion = ProductInEntryDocumentMotion(motion=Motion, customs=Customs, motionDate=MotionDate)
                    motions.append(motion)
            taxes_r = miniProduct.get("taxes")
            taxes = []
            # if "taxes" in miniProduct and taxes_r is not None:
            if taxes_r:
                if type(taxes_r) is not list:
                    raise Exception(f"taxes debe ser una lista del producto {invoiceItemNumber}")
                for indice, tax_r in enumerate(taxes_r, start=1):
                    if type(tax_r) is not dict:
                        raise Exception(f"motion {indice} debe ser un dict para las series del producto {invoiceItemNumber}")
                    factorType = ownUtils.manage_request_field(ProductInEntryDocumentTax.FactorType, field_name='factorType', container_dict=tax_r, validation_function=ownUtils.check_len_sql_column, isRequired=True)
                    factorType = factorType.lower()
                    if factorType == "exento":
                        taxKey = None
                        taxRate = None
                    else:
                        taxKey = ownUtils.manage_request_field(ProductInEntryDocumentTax.TaxKey, field_name='taxKey', container_dict=tax_r, validation_function=ownUtils.check_len_sql_column, isRequired=True)
                        taxRate = ownUtils.manage_request_field(field_name='taxRate', container_dict=tax_r, validation_function=ownUtils.revisarFloatPositivo, isRequired=True)
                    tax = ProductInEntryDocumentTax(taxKey=taxKey, factorType=factorType, taxRate=taxRate)
                    taxes.append(tax)
            series_r = miniProduct.get('series')
            series = []
            if series_r:
                if type(series_r) is not list:
                    raise Exception(f"series debe ser una lista del producto {invoiceItemNumber}")
                for indice, serie_r in enumerate(series_r, start=1):
                    if ownUtils.check_len_sql_column(ProductInEntryDocumentSerie.Serie, serie_r):
                        serie = ProductInEntryDocumentSerie(serie_r)
                        series.append(serie)
                    else:
                        raise Exception(f"La serie #{indice} del producto {invoiceItemNumber} no es válido")
            print('000000000000000000000000000000000000000000000----------------------------------------------------------------')
            print(units)
            print(type(units))
            print('-----------------------')
            print(unitPrice)
            print(print(type(unitPrice)))
            print('-----------------------')
            print(discountRate)
            print(print(type(discountRate)))
            print('000000000000000000000000000000000000000000000----------------------------------------------------------------')
            conceptAmount = (units * unitPrice) * ((100 - discountRate) / 100)
            productInEntryDocument = ProductInEntryDocument(
                model=model,
                invoiceItemNumber=invoiceItemNumber,
                supplierSku=supplierSku,
                # predialNum=predialNum,
                upc=upc,
                satKey=satKey,
                units=units,
                discountRate=discountRate,
                unitPrice=unitPrice,
                description=description,
                unitKey=unitKey,
                conceptAmount=conceptAmount
            )
            productInEntryDocument.Product = None
            productInEntryDocument.ProductInEntryDocumentSeries = series
            productInEntryDocument.ProductInEntryDocumentMotions = motions
            productInEntryDocument.ProductInEntryDocumentTaxes = taxes
            producstInEntryDocument.append(productInEntryDocument)
        entryDocument.ProductsInEntryDocument = producstInEntryDocument
    else:
        raise Exception('La factura requiere registrar productos')


def fuzzyFacturasSearch(session, search):
    umbral = 0.75
    if search:
        query = session.query(EntryDocument).\
            join(Invoice, EntryDocument.Id == Invoice.EntryDocumentId).filter(
            or_(
                (Invoice.Uuid.op('<->')(search) < umbral),
                (Invoice.Serie.op('<->')(search) < umbral),
                (Invoice.CsdSerie.op('<->')(search) < umbral),
                (EntryDocument.SaleOrder.op('<->')(search) < umbral),
                (EntryDocument.ReceiverRfc.op('<->')(search) < umbral),
            )
        )
    return query
