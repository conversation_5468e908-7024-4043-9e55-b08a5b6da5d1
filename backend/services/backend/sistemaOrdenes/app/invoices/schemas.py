from marshmallow import Schema, fields


class numFacturasFiltroSchema(Schema):
    proveedores = fields.String(
        required=False,
        description="Supliers String")
    fechaInicial = fields.DateTime(
        format='%Y-%m-%d %H:%M:%S.%f',
        required=False,
        description="Start Date",
        example="2022-11-29 13:29:30.000")
    fechaFinal = fields.DateTime(
        format='%Y-%m-%d %H:%M:%S.%f',
        required=False,
        description="Final Date",
        example="2023-01-10 16:07:24.000")
    search = fields.String(
        required=False,
        description="Search String")


class FacturasFiltroSchema(numFacturasFiltroSchema):
    offset = fields.String(
        required=False,
        description="Offset String")
    next = fields.String(
        required=False,
        description="Next String")


class FacturasCommentsSchema(Schema):
    uuid = fields.String(
        required=False,
        description="Uuid String",
        example="003EECF0-851D-468C-9856-0EB61D99E08B")
    newComments = fields.String(
        required=False,
        description="Comment String",
        example="This is a comment")


class InternalSkuSchema(Schema):
    InternalSku = fields.String(
        required=True, description="InternalSku", example="ZET9019417")


class ProductInInvoiceSchema(InternalSkuSchema):
    Model = fields.String(
        required=True,
        description="Model String",
        example="RSA3474")
    SupplierSku = fields.String(
        required=False,
        description="SupplierSku",
        example="CB06AENS05")
    # PredialNum = fields.String(
    #    required=False,
    #    description="PredialNumber",
    #    example="1234567890123456")
    Upc = fields.String(
        required=False,
        description="Upc",
        example="123456789012")
    SatKey = fields.String(
        required=True,
        description="SatKey",
        example="46171619")
    Units = fields.Integer(
        required=True,
        description="Units Int",
        example=10)
    DiscountRate = fields.Float(
        required=True,
        description="Units Float",
        example=15.0)
    UnitPrice = fields.String(
        required=True,
        description="UnitPrice String",
        example="913.54")
    UnitKey = fields.String(
        required=True,
        description="UnitKey String",
        example="H87")
    ConceptAmount = fields.String(
        required=True,
        description="ConceptAmount String",
        example="804.28")
    Description = fields.String(
        required=False,
        description="Description String",
        example="HERRAJE TIPO D CHICO")

    Motion = fields.String(
        required=True,
        description="Motion String",
        example="23  07  3459  3001612")
    Customs = fields.String(
        required=False,
        description="Customs String",
        example="12/Ene/23")
    MotionDate = fields.DateTime(
        format='%Y-%m-%d %H:%M:%S',
        required=False,
        description="Motion Date",
        example="2021-11-29 13:29:30")

    TaxKey = fields.String(
        required=True,
        description="TaxKey String",
        example="002")
    FactorType = fields.String(
        required=False,
        description="FactorType String",
        example="Tasa")
    TaxRate = fields.Float(
        required=False,
        description="TaxRate String",
        example=0.16)

    Serie = fields.String(
        required=True,
        description="Serie String",
        example="************")


class InvoiceSchema(Schema):
    Uuid = fields.String(
        required=True,
        description="Uuid String",
        example="1052EA17-6F2D-4A0F-AD57-48B925E9345D")
    Folio = fields.String(
        required=True,
        description="Folio String",
        example="6044859")
    Serie = fields.String(
        required=True,
        description="Serie String",
        example="FA22")
    CsdSerie = fields.String(
        required=True,
        description="CsdSerie String",
        example="00001000000507742537")
    SaleOrder = fields.String(
        required=False,
        description="Venta/Remision String",
        example="0021925121")
    PurchaseOrder = fields.String(
        required=False,
        description="0rdenCompra String",
        example="sin orden")
    ReceiverRfc = fields.String(
        required=True,
        description="Receptor RFC String",
        example="CPL061212FJ3")
    IssuePlace = fields.String(
        required=True,
        description="Lugar De Expedicion String",
        example="02440")
    IssueDate = fields.DateTime(
        format='%Y-%m-%d %H:%M:%S',
        required=True,
        description="Fecha Date",
        example="2021-11-29 13:29:30")
    CertificationDate = fields.DateTime(
        format='%Y-%m-%d %H:%M:%S',
        required=True,
        description="Fecha Timbrado Date",
        example="2022-11-29 13:29:30")
    ExpirationDate = fields.DateTime(
        format='%Y-%m-%d %H:%M:%S',
        required=True,
        description="Fecha Timbradov2 Date",
        example="2022-11-29 13:29:30")
    BranchOffice = fields.String(
        required=False,
        description="Sucursal",
        example="Corporativo Steren")
    PaymentConditions = fields.String(
        required=False,
        description="Condiciones De Pago String",
        example="Credito Libre 30 días")
    PaymentMethod = fields.String(
        required=True,
        description="Metodo De Pago String",
        example="PUE")
    PaymentForm = fields.String(
        required=True,
        description="Forma De Pago String",
        example="03")
    Currency = fields.String(
        required=True,
        description="Moneda String",
        example="MXN")
    CfdiUsage = fields.String(
        required=True,
        description="UsoCFDI String",
        example="G01")
    SalesRep = fields.String(
        required=False,
        description="Vendedor String",
        example="AMR")
    IssuerRfc = fields.String(
        required=True,
        description="Emisor RFC String",
        example="EST850628K51")
    ProductsInInvoice = fields.Nested(
        ProductInInvoiceSchema,
        required=True,
        many=True)
