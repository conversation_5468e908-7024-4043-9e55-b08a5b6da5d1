# ---Variables configuración
# ------Variables para obtener ordenes
# ---------Cantidad de días previos a hoy para obtener los pedidos
# ------------ClaroShop
CANTIDAD_DE_DIAS_A_OBTENER_CLARO_SHOP = 10
# ------------Amazon
CANTIDAD_DE_DIAS_A_OBTENER_AMAZON = 4
# ------------Mercado Libre
CANTIDAD_DE_DIAS_A_OBTENER_ML = 1
# ------------Walmart
CANTIDAD_DE_DIAS_A_OBTENER_WALMART = 100
DIAS_ACTUALIZACION_1 = 2
DIAS_ACTUALIZACION_2 = 30
# ---------Tiempo entre cada actualización de nivel 1 * (Ordenes del dia para Amazon, Claro Shop, Walmart)
INTERVALO_DE_ACTUALIZACION_1 = 5 * 60
# ---------Tiempo entre cada actualización de nivel 2 * (Ordenes del dia para Mercado Libre)
INTERVALO_DE_ACTUALIZACION_2 = 10 * 60
# ---------Hora de actualización general
HORA_ACTUALIZACION_GRAL = 3
# ------Variables para enviar correo de alerta (error al obtener pedidos)
# ---------Puerto
PUERTO_SMTP = 465
# ---------Servidor SMTP
SERVIDOR_SMTP = "smtp.dreamhost.com"
# ---------Destinatario
DESTINARIO_EMAIL = "<EMAIL>"
# ------Horario de mantenimiento red
# ---------Inicio del horario de mantenimiento [hora, minuto, segundo]
INICIO_HORARIO_MANTENIMIENTO = {"hora": 0, "minuto": 0}
# ---------Final del horario de mantenimiento [hora, minuto, segundo]
FINAL_HORARIO_MANTENIMIENTO = {"hora": 0, "minuto": 30}
# ---------Horas de diferencia a ajustar para órdenes de ML
HORAS_DIFERENCIA_ML = -1
RUTA_DISCO_COMPARTIDO = "\\\\aaserver\\bots"
RUTA_SECRETS = "secrets.json"
RUTA_CERTIFICADOS = "certs"
LOGFILE = "log.txt"
ZONA_HORARIA_SISTEMA = "Mexico/General"
OFFSET_ZONA_HORARIA_ML = -4
