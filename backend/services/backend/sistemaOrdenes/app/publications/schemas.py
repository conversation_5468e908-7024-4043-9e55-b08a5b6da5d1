from marshmallow import Schema, fields


class PostSchema(Schema):
    title = fields.Str(
        required=True, description="Title of the pub", example="HONEYWELL HOME RESIDEO Portal de Acceso para Cámaras Honeywell Total Connect. MOD: WAP")
    description = fields.Str(
        required=True, description="Description of the product", example="El Honeywell WAP es un punto de acceso inalámbrico diseñado específicamente para las cámaras de seguridad Honeywell IPCamaras serie")
    model = fields.Str(required=True, description="Model of the product",
                       example="WAP")
    price = fields.Float(
        required=True, description="Pub price", example=999.0)
    stock = fields.Int(
        required=True, description="Pub stock", example=10)
    brand = fields.Str(
        required=True, description="Brand of the product", example="HONEYWELL")
    pictures = fields.List(
        fields.String(), required=False, description="photos")
    weight = fields.Float(
        required=True, description="weight of the product", example=60)
    width = fields.Float(
        required=True, description="weight of the product", example=60)
    height = fields.Float(
        required=True, description="height of the product", example=60)
    depth = fields.Float(
        required=True, description="depht of the product", example=60)
    marketplace = fields.Str(
        required=True, description="Marketplace", example="MercadoLibre")
