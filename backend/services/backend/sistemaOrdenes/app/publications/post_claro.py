import configs
import requests
import hashlib
import json
import traceback
from datetime import datetime, timedelta
# import datetime as dt
import file_manager
import sys
import os
current = os.path.dirname(os.path.realpath(__file__))
parent = os.path.dirname(current)
sys.path.append(parent)


def crearURLComun():
    secrets = file_manager.read_secrets()
    clave_publica = secrets['claro_shop']['clave_publica']
    clave_privada = secrets['claro_shop']['clave_privada']
    # url ambiente
    urlAmbiente = 'https://selfservice.claroshop.com/apicm/v1'
    # Llave pública
    # Fecha
    # Obtener la fecha y hora en el formato requerido
    now = datetime.now()
    # ajustando error de hora
    one_hour_later = now
    #
    fecha = one_hour_later.strftime("%Y-%m-%dT%H:%M:%S")
    # print('ffffffffffffffffffffffffffffffff')
    # print(fechaActual)
    # print(fecha)
    # print(type(fecha))
    # print('ffffffffffffffffffffffffffffffff')
    # Signature
    # Clave privada
    # Creamos signature

    prehash = f'{clave_publica}{fecha}{clave_privada}'
    signature = hashlib.sha256(prehash.encode()).hexdigest()
    # Creamos URL
    urlComun = f'{urlAmbiente}/{clave_publica}/{signature}/{fecha}'
    # Retornamos URL
    return urlComun


def status400():
    return "Bad request"


def status401_3():
    return "Authentication error"


def status404():
    return "Not found"


def status500():
    return "Internal Server Error"


def http_status(status):
    switcher = {
        400: status400(),
        401: status401_3(),
        403: status401_3(),
        404: status404(),
        500: status500()
    }
    return switcher.get(status, "Error desconocido")


def crearURLProducto():
    urlComun = crearURLComun()
    return urlComun + '/producto'


def regresarHeaderPOSTAndPUT():
    headerPOST = {'Content-Type': 'application/x-www-form-urlencoded'}
    return headerPOST


def agregarProducto(producto):
    # messagebox.showinfo("showinfo", productoString)
    if type(producto) is not dict:
        producto = json.loads(producto)
    # messagebox.showinfo("showinfo", producto)
    urlPOST = crearURLProducto()
    print(urlPOST)
    # producto = armarNuevoProducto()
    headersPOST = regresarHeaderPOSTAndPUT()
    try:
        r = requests.post(urlPOST, data=producto, headers=headersPOST)
        print(r.status_code)
        print(r)
        print(r.json())
        if r.status_code == 200:
            rJson = r.json()
            print(rJson)
            if rJson['estatus'] == 'error':
                strRJson = str(rJson['mensaje'])
                r = str(rJson['estatus']) + ': '
                if '{' in strRJson and '}' in strRJson:
                    if 'nombre' in strRJson:
                        r = r + 'revisar nombre, '
                    if 'descripcion' in strRJson:
                        r = r + 'revisar descripcion, '
                    if 'especificacionestecnicas' in strRJson:
                        r = r + 'revisar especificaciones tecnicas, '
                    if 'alto' in strRJson:
                        r = r + 'revisar alto, '
                    if 'ancho' in strRJson:
                        r = r + 'revisar ancho, '
                    if 'profundidad' in strRJson:
                        r = r + 'revisar profundidad, '
                    if 'peso' in strRJson:
                        r = r + 'revisar peso, '
                    if 'preciopublicobase' in strRJson:
                        r = r + 'revisar precio publico base, '
                    if 'preciopublicooferta' in strRJson:
                        r = r + 'revisar precio publico oferta, '
                    if 'cantidad' in strRJson:
                        r = r + 'revisar cantidad, '
                    if 'skupadre' in strRJson:
                        r = r + 'revisar skupadre, '
                    if 'ean' in strRJson:
                        r = r + 'revisar ean, '
                    if 'estatus' in strRJson:
                        r = r + 'revisar estatus, '
                    if 'embarque' in strRJson:
                        r = r + 'revisar embarque, '
                    if 'categoria' in strRJson:
                        r = r + 'revisar categoria, '
                    if 'fotos' in strRJson:
                        r = r + 'revisar fotos, '
                    if 'agregarmarca' in strRJson:
                        r = r + 'revisar amarca, '
                    if 'tag' in strRJson:
                        r = r + 'revisar tag, '
                    if 'garantia' in strRJson:
                        r = r + 'revisar garantia, '
                    if r[-2] == ',':
                        r = r[:-2]
                else:
                    r = r + str(rJson['mensaje'])
            elif rJson['estatus'] == 'success':
                # print(rJson['infoproducto']['transactionid'])
                idClaroShop = str(rJson['infoproducto']['transactionid'])
                # print(type(rJson['infoproducto']))
                r = f'{rJson["mensaje"]}: El producto ha sido subido exitosamente ->{idClaroShop}'
            else:
                r = str(rJson['estatus']) + ':' + str(rJson['mensaje'])
        else:
            r = http_status(r.status_code)
            print(r)
    except Exception as e:
        r = "No se pudo ejecutar la petición," + str(e)
        print(r)
    # messagebox.showinfo("showinfo", r)
    return print(r)


producto = {
    'nombre': 'TEST123',
    'descripcion': 'ESTO ES UN TEST',
    'especificacionestecnicas': 'MEDIDAS: Largo (cuello a cinturón): 70cms. Cruz (ancho, pecho): 46cms. Manga (hombro a puño): 61cms. Material: algodón',
    'alto': '1',
    'ancho': '1',
    'profundidad': '1',
    'peso': '1',
    'preciopublicobase': '99999',
    'preciopublicooferta': '99998',
    'cantidad': '10',
    'skupadre': 'TEST123',
    'ean': 'eanwebp1',
    'estatus': 'activo',
    'embarque': '5',
    'categoria': '20063',
    'fotos[1][url]': 'https://www.creativeplanet.com.mx/wp-content/uploads/2023/03/logo-LMO-no-Imagen.png',
    'fotos[1][orden]': '1',
    'agregarmarca': 'TEST'
}
agregarProducto(producto)
