import os
import json
import sistemaOrdenes.configs as configs


def read_secrets():
    filename = os.path.join(os.path.dirname(__file__), configs.RUTA_SECRETS)
    print(filename)
    try:
        with open(filename, mode='r') as f:
            return json.loads(f.read())
    except FileNotFoundError:
        print('err')
        return {}


def read_access_token():
    with open(f"{configs.RUTA_DISCO_COMPARTIDO}\\access_token.txt") as file:
        lines = file.readlines()
        try:
            return lines[0]
        except Exception as e:
            print(str(e))
            return ""


def save_access_token(access_token):
    with open(f"{configs.RUTA_DISCO_COMPARTIDO}\\access_token.txt", 'w') as file:
        file.write(access_token)
        return


def save_ml_notification(notification_body):
    with open("notification.txt", 'w') as file:
        file.write(notification_body)
        return


def log(message):
    with open(configs.LOGFILE, 'a') as file:
        file.write(message)
        return
