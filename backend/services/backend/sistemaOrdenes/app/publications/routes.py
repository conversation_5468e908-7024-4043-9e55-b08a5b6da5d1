from flask import Flask, Response, jsonify, request, make_response
from flask_jwt_extended import jwt_required, get_jwt
from flask.views import MethodView
from flask_smorest import abort, Blueprint
from sistemaOrdenes.app.models.Models import ProductBase, Product_SupplierStore, Marketplace, PublicationBase, WarrantyTimeFrame, Warranty, PublicationStatus, WarrantyType, \
    PublicationProduct, Product_Store, PublicationProduct_Product_Store, PublicationProduct_Product_SupplierStore
from sistemaOrdenes.configs import ROLES, ADMIN_ROLE
from sistemaOrdenes.app.models.db import ScopedSession
from .postUtilities import create_json_claro, create_json_ml, create_pub_ml, up_description_ml, agregarProducto, create_category, create_accessToken_amazon, create_pub_amazon
from sistemaOrdenes.app.ownUtils import revisarIntPositivo, validate_if_object_exists
from sistemaOrdenes.app import ownUtils
import requests
import json
from .schemas import PostSchema
import traceback


postProducts = Blueprint("postProducts", __name__,
                         description="Post products on marketplace")


@postProducts.route('/api/publication/loadPage')
# @jwt_required()
def load_page():
    try:
        session = ScopedSession()
        # claims = get_jwt()
        # role = claims.get('role')
        marketplaces = session.query(Marketplace).all()
        warranty_time_frames = session.query(WarrantyTimeFrame).all()
        publication_statuses = session.query(PublicationStatus).all()
        warranty_types = session.query(WarrantyType).all()
        createdResponse = jsonify({
            'marketplaces': [finalMarket.serialize() for finalMarket in marketplaces],
            'warrantyFrames': [warranty_time_frame.serialize() for warranty_time_frame in warranty_time_frames],
            'publicationStatuses': [publication_status.serialize() for publication_status in publication_statuses],
            'warrantyTypes': [warranty_type.serialize() for warranty_type in warranty_types]})
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(jsonify({'errores': str(e)}), 500)
    finally:
        session.close()
        return createdResponse


def create_warranties(warranty_dict_list):
    warranties = []
    for warranty_dict in warranty_dict_list:
        warranty = Warranty(amount=warranty_dict['amount'])
        warranty.WarrantyTimeFrame = warranty_dict['warrantyTimeFrame']
        warranty.WarrantyType = warranty_dict['warrantyType']
        warranties.append(warranty)
    return warranties


@postProducts.route('/api/postNewProducts/postProductByMarketAndInternalSku', methods=['POST'])
# @postProducts.arguments(PostSchema)
# @jwt_required()
def post_product():
    try:
        session = ScopedSession()
        data = request.get_json()
        marketplace_id = data.get('marketplaceId')
        internalBaseSku = data.get('internalBaseSku')
        warranties_request = data.get('warranty')
        publication_status_id = data.get('publicationStatusId')
        stock = data.get('stock')
        price = data.get('price')
        boarding_time = data.get('boardingTime')
        print('oooooooooooooooooooooo')
        print(marketplace_id)
        print(internalBaseSku)
        print('here')
        # revisar stock
        # revisar no valido
        # ----------------------------------
        # supplierStoreStock = session.query(Product_SupplierStore).filter(Product_SupplierStore.InternalSku == internalBaseSku).all()
        # -----------------------------------
        if revisarIntPositivo(boarding_time):
            raise Exception('El tiempo de embalaje debe ser expresado usando Int (boardingTime)')
        else:
            boarding_time = int(boarding_time)
        product = session.query(ProductBase).get(internalBaseSku)
        if not product:
            raise Exception("El producto no existe")
        if revisarIntPositivo(marketplace_id):
            raise Exception('El MarketPlaceId debe ser expresado usando Int')
        marketplace = session.query(Marketplace).get(marketplace_id)
        if not marketplace:
            raise Exception("Id de market no corresponde a ningún marketplace registrado")
        supported_marketplace = marketplace.SupportedMarketplace.SupportedMarketplaceName
        marketplace_credentials = marketplace.MarketplaceCredentials
        publication_status = session.query(PublicationStatus).get(publication_status_id)
        if not publication_status:
            raise Exception('Id de publicationStatus no corresponde a ningún estado de publicación registrado en el sistema')
        # ----------------------
        if warranties_request:
            if type(warranties_request) is not list:
                raise Exception("warranty debe ser un json")
            # *****
            warranty_dict_list = []
            list_of_warrantyTypes = []
            for index, warranty_request in enumerate(warranties_request):
                if type(warranty_request) is not dict:
                    raise Exception(f'la garantía {index} no es un dict')

                amount = warranty_request.get('amount')
                if revisarIntPositivo(amount):
                    raise Exception('La garantía debe ser expresado usando Int (amount)')

                warranty_time_frame_id = warranty_request.get('warrantyTimeFrameId')
                warrantyTimeFrame = session.query(WarrantyTimeFrame).get(warranty_time_frame_id)
                if not warrantyTimeFrame:
                    raise Exception('Id  de warrantyTimeFrame no corresponde a ningún tipo de tiempo de garantia registrado en el sistema')

                warranty_type_id = warranty_request.get('warrantyTypeId')
                warrantyType = session.query(WarrantyType).get(warranty_type_id)
                if not warrantyType:
                    raise Exception('Id  de warrantyType no corresponde a ningún tipo de garantia registrado en el sistema')
                if warranty_type_id in list_of_warrantyTypes:
                    raise Exception(f'{index}: No puede haber más de una garantía con el mismo tipo')
                else:
                    list_of_warrantyTypes.append(warranty_type_id)
                warranty_dict_list.append(
                    {
                        "amount": amount,
                        "warrantyTimeFrame": warrantyTimeFrame,
                        "warrantyType": warrantyType, })
        else:
            warranty_dict_list = []
        if supported_marketplace == "Mercado Libre":
            if not marketplace_credentials:
                raise Exception('Marketplace no tiene credenciales registradas en el sistema')
            access_token = marketplace_credentials.AccessToken
            if not access_token:
                raise Exception("Accesstoken no registrado en el sistema")
            category, status_cat = create_category(product['description'], access_token)
            try:
                print('here')
                print(status_cat)
                if status_cat == 200:
                    # datos invalidos
                    price = 100000
                    stock = 0
                    pictures_product = None
                    # datos invalidos
                    json_file = create_json_ml(
                        product['description'], product['model'], price, stock, product['brand'], pictures_product,
                        product['productBaseShippingDimensions']['weight'], product['productBaseShippingDimensions']['width'],
                        product['productBaseShippingDimensions']['height'], product['productBaseShippingDimensions']['length'], category)
                    print(json_file)
                    id, status = create_pub_ml(json_file, access_token)
                    if status == 201:
                        status_desc = up_description_ml(
                            id, product['longDescription'])
                        if status_desc == 201:
                            createdResponse = make_response(
                                "producto registrado exitosamente", 200)
                        else:
                            print("this_error_2")
                            createdResponse = make_response(
                                "producto parcialmente registrado, revisar la descripción", 202)
                        publication = PublicationBase(markeplacePublicationId=id, stock=stock, price=10000, boardingTime=boarding_time)
                        publication.Marketplace = marketplace
                        publication.PublicationStatus = publication_status
                        publication.Publications_Product_Store = []
                        publication.Publications_Product_SupplierStore = []
                        session.commit()
                    else:
                        print("this_error_3")
                        createdResponse = make_response(
                            jsonify({'mensaje': id, 'status': status}))
                        return createdResponse
                else:
                    print("this_error_4")
                    createdResponse = make_response(
                        "There was an error during product creation", 404)
                    return createdResponse
            except Exception as e:
                print("this_error_5")
                print(str(e))
                createdResponse = "There was an error during creating product process"
        elif supported_marketplace == "Claro Shop":
            if not marketplace_credentials:
                raise Exception('Marketplace no tiene credenciales registradas en el sistema')
            clave_publica = marketplace_credentials.ClientId
            if not clave_publica:
                raise Exception("ClientId no registrado en el sistema")
            clave_privada = marketplace_credentials.ClientSecret
            if not clave_privada:
                raise Exception("ClientSecret no registrado en el sistema")
            print('00000000000000099999999')
            stock = int(2)
            print('00000999999999999')
            warranties_request, publication_status_id, boarding_time
            json_file = create_json_claro(product, warranty_dict_list, publication_status, boarding_time)
            print('----.json()vvvvvvvvvvvvvvvvvvvvvvvvvvvv')
            print(json_file)
            print('----.json()vvvvvvvvvvvvvvvvvvvvvvvvvvvv')
            exito, info = agregarProducto(json_file, clave_publica, clave_privada)
            print('info-------------')
            print(info)
            print('info-------------')
            print('bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb000')
            print(stock)
            print('dddddddddddddddd')
            if exito:

                warranties = create_warranties(warranty_dict_list)
                publication = PublicationBase(markeplacePublicationId=info, stock=stock, price=10000, boardingTime=boarding_time)
                publication.Marketplace = marketplace
                publication.Warranties = warranties
                publication.PublicationStatus = publication_status
                publication.Publications_Product_Store = []
                publication.Publications_Product_SupplierStore = []
                session.commit()
                createdResponse = make_response(f"registrado exitosamente con id de publicación: {info}", 200)
            else:
                createdResponse = make_response(info, 500)
            return createdResponse
        elif supported_marketplace == 'Amazon':
            if not marketplace_credentials:
                raise Exception('Marketplace no tiene credenciales registradas en el sistema')
            clave_publica = marketplace_credentials.ClientId
            if not clave_publica:
                raise Exception("ClientId no registrado en el sistema")
            clave_privada = marketplace_credentials.ClientSecret
            if not clave_privada:
                raise Exception("ClientSecret no registrado en el sistema")
            refresh_token = marketplace_credentials.RefreshToken
            if not clave_privada:
                raise Exception("RefreshToken no registrado en el sistema")
            access_token = create_accessToken_amazon(clave_publica,
                                                     clave_privada,
                                                     refresh_token)
            create_pub_amazon(access_token)
            createdResponse = make_response("registrado exitosamente con id de publicación:", 200)
        elif supported_marketplace == 'Walmart':
            raise Exception("Las publicaciones para walmart no estan disponibles")
        else:
            raise Exception("La publicación de productos no es soportada para este marketplace")
    except Exception as e:
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(
            jsonify({'errorres': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


@postProducts.route('/api/publication/newPublication', methods=['POST'])
# @jwt_required()
def create_mundial_publication():
    try:
        session = ScopedSession()
        # data = request.get_json()
    except Exception as e:
        traceback.print_exc()
        session.rollback()
        createdResponse = make_response(
            jsonify({'errorres': f'Ocurrio un error inesperado:{str(e)}'}), 500)
    finally:
        session.close()
        return createdResponse


def is_type_like(object_to_check, object_name, object_type):
    if type(object_to_check) is not object_type:
        raise Exception(f'{object_name} debe ser {str(object_type.__name__)}')


@postProducts.route('/api/publication/relatePublicationProductToProductStores', methods=['POST'])
@jwt_required()
@ownUtils.my_decorator_http_manage
def change_internal_locations(session):
    # Get entire info about jwt token
    claims = get_jwt()
    # Get user role from claims specific key
    role = claims.get('role')
    if role not in ROLES:
        return make_response('No tienes permisos para realizar ventas', 403)
    data = ownUtils.get_info_from_json(request)
    # Publication
    print('dddddddddddddddddddata')
    print(data)
    print('dddddddddddddddddddata')
    publication_product = validate_if_object_exists(session, 'publicationProductId', 'dict', data, PublicationProduct)
    if publication_product.PublicationProduct_Product_Stores or publication_product.PublicationProduct_Product_SupplierStores:
        raise Exception('La publicación ya ha sido relacionada ')
    internal_sku = data.get('internalSku')
    if 'storeIds' not in data:
        store_list = []
    else:
        store_list = data.get('storeIds')
    if 'storeIds' not in data:
        supplierStore_list = []
    else:
        supplierStore_list = data.get('supplierStoreIds')
    if not store_list and not supplierStore_list:
        raise Exception('Ambas listas storeIds y supplierStoreIds no pueden estar vacias')
    is_type_like(store_list, 'storeIds', list)
    object_store_list = []
    for store_id in store_list:
        if revisarIntPositivo(store_id):
            raise Exception('storeId debe ser entero positivo')
        product_store = session.query(Product_Store).get((internal_sku, int(store_id)))
        if not product_store:
            raise Exception('El producto no se encuentra en el almacen')
        publicationProduct_Product_Store = PublicationProduct_Product_Store()
        publicationProduct_Product_Store.PublicationProduct = publication_product
        publicationProduct_Product_Store.Product_Store = product_store
        object_store_list.append(publicationProduct_Product_Store)
    is_type_like(supplierStore_list, 'supplierStoreIds', list)
    object_supplierStore_list = []
    for supplierStore_id in supplierStore_list:
        if revisarIntPositivo(supplierStore_id):
            raise Exception('supplierStoreId debe ser entero positivo')
        product_supplierStore = session.query(Product_SupplierStore).get((internal_sku, int(supplierStore_id)))
        if not product_supplierStore:
            raise Exception('El producto no se encuentra en el almacen')
        publicationProduct_PublicationProduct_Product_SupplierStore = PublicationProduct_Product_SupplierStore()
        publicationProduct_PublicationProduct_Product_SupplierStore.PublicationProduct = publication_product
        publicationProduct_PublicationProduct_Product_SupplierStore.Product_SupplierStore = product_supplierStore
        object_supplierStore_list.append(publicationProduct_PublicationProduct_Product_SupplierStore)
    session.add_all(object_store_list)
    session.add_all(object_supplierStore_list)
    ownUtils.commit_catching_unique_constraint(session)
    return make_response(jsonify({'mensaje': 'Publicación relacionada con el inventario de los almacenes'}), 200)
