import traceback
import requests
from flask_smorest import Blueprint
from flask import Response, request, jsonify, make_response
from sistemaOrdenes.app.tasks import manage_notification, create_test_task, manage_notification_dev
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.app.models.Models import Marketplace

meli = Blueprint("Meli", __name__, description="Meli operations")


@meli.route('/api/services/ml/testCelery', methods=['POST'])
def run_task():
    content = request.json
    task_type = content["type"]
    task = create_test_task.delay(int(task_type))
    return jsonify({"task_id": task.id}), 202

@meli.route('/api/services/ml/notification', methods=['POST'])
def process_ml_notification():
    request_data = request.get_json()
    if request_data.get("topic") in ["orders_v2","messages", "fbm_stock_operations", "flex-handshakes", "post_purchase"]:
        task = manage_notification.delay(request_data)
        print("Task ID:", task.id)
        #manage_notification_dev(request_data) 
    
    return Response(status=200)

