from flask import request, Response, make_response
from flask_jwt_extended import jwt_required, get_jwt
from sistemaOrdenes.app import models
from sistemaOrdenes.app.models.Models import Zone
from sistemaOrdenes.configs import ROLES, ADMIN_ROLE
from flask_smorest import Blueprint
from .schemas import ZoneSchema
zones = Blueprint("Zones", __name__, description="Operations on Zones")

noActualizarValue = 'No Actualizar'


@zones.route('/api/zones/getZones')
@jwt_required()
def return_todas_zonas():
    try:
        claims = get_jwt()
        role = claims.get('role')
        if role in ROLES:
            query = models.db.session.query(Zone)
            todasZonas = query.all()
            zonesSerializados = [zone.serialize() for zone in todasZonas]
            createdResponse = {'zonas': zonesSerializados}
        else:
            createdResponse = make_response(
                'No tienes permisos para observar las zonas', 500)
    except Exception as e:
        print(str(e))
        models.db.session.rollback()
        createdResponse = Response(status=500)
    finally:
        models.db.session.close()
        return createdResponse


@zones.route('/api/zones/zone/new', methods=['POST'])
@zones.arguments(ZoneSchema)
@jwt_required()
def create_zone():
    try:
        claims = get_jwt()
        role = claims.get('role')
        if (role == ADMIN_ROLE):
            # creates a dictionary of the form data
            data = request.form
            # gets atributtes from product
            zoneNumber = data.get('zoneNumber', None)
            if not zoneNumber:
                raise Exception('Numero de zona requerido')
            deliveryTime = data.get('deliveryTime', None)
            if not deliveryTime:
                raise Exception('DeliveryTime requerido')
            # checking for existing user
            zone = models.db.session.query(Zone).filter(
                Zone.ZoneNumber == zoneNumber).first()
            if not zone:
                # database ORM object
                zone = Zone(
                    zoneNumber,
                    deliveryTime
                )
                # insert product
                models.db.session.add(zone)
                models.db.session.commit()
                createdResponse = make_response(
                    'Zona registrada exitosamente.', 200)
            else:
                # returns 202 if user already exists
                createdResponse = make_response('La zona ya existe.', 202)
        else:
            # Return 403 if user is valid user but it is not Admin
            createdResponse = make_response(
                'No tienes permisos para dar de alta una zona', 403)
    except Exception as e:
        models.db.session.rollback()
        createdResponse = make_response(str(e), 500)
    finally:
        models.db.session.close()
        return createdResponse
