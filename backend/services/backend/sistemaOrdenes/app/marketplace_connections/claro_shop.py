import requests
import hashlib
import json
import traceback
from datetime import datetime, timedelta
from pytz import timezone

clave_publica = ''
clave_privada = ''
MARKETPLACE_ID = ''
seed = None


def set_credentials_and_id_marketplace(credentials_param=None, marketplace_id=None):
    global clave_publica
    global clave_privada
    global MARKETPLACE_ID
    clave_publica = credentials_param['clientId']
    clave_privada = credentials_param['clientSecret']
    MARKETPLACE_ID = marketplace_id


def crearURLComun():
    # url ambiente
    urlAmbiente = 'https://selfservice.claroshop.com/apicm/v1'
    # Llave pública
    # Fecha
    # Obtener la fecha y hora en el formato requerido
    # Define different timezones
    utc = timezone('UTC')
    mexico_city = timezone('America/Mexico_City')
    # Get the current time in UTC
    utc_now = datetime.now(utc)
    # Convert the current UTC time to US/Eastern time
    mexico_city_now = utc_now.astimezone(mexico_city)
    # one_hour_later = mexico_city_now + timedelta(hours=1)
    # ajustando error de hora
    fecha = mexico_city_now.strftime("%Y-%m-%dT%H:%M:%S")
    # print('ffffffffffffffffffffffffffffffff----------')
    # # print(fechaActual)
    # print('UTC:', utc_now)
    # print('America/Mexico_City:', mexico_city_now)
    # print('**')
    # print(fecha)
    # # print(type(fecha))
    # print('ffffffffffffffffffffffffffffffff-----')
    # Signature
    # Clave privada
    # Creamos signature
    prehash = clave_publica + fecha + clave_privada
    signature = hashlib.sha256(prehash.encode()).hexdigest()
    # Creamos URL
    urlComun = urlAmbiente + '/' + clave_publica + '/' + signature + '/' + fecha
    # Retornamos URL
    return urlComun


def crearURLProducto():
    urlComun = crearURLComun()
    return urlComun + '/producto'


def crearURLColores():
    urlComun = crearURLComun()
    return urlComun + '/colores'


def crearURLTallas():
    urlComun = crearURLComun()
    return urlComun + '/tallas'


def crearURLMarcas():
    urlComun = crearURLComun()
    return urlComun + '/marcas'


def crearURLCategorias():
    urlComun = crearURLComun()
    return urlComun + '/categorias'


def crearURLPedidos():
    urlComun = crearURLComun()
    return urlComun + '/pedidos'


def crearURLPedidosFiltros():
    urlComun = crearURLComun()
    return urlComun + '/pedidosfiltros'


def status400():
    return "Bad request"


def status401_3():
    return "Authentication error"


def status404():
    return "Not found"


def status500():
    return "Internal Server Error"


def http_status(status):
    switcher = {
        400: status400(),
        401: status401_3(),
        403: status401_3(),
        404: status404(),
        500: status500()
    }
    return switcher.get(status, "Error desconocido")


def regresarHeaderPOSTAndPUT():
    headerPOST = {'Content-Type': 'application/x-www-form-urlencoded'}
    return headerPOST


def regresarHeaderGET():
    headerGET = {'Content-Type': 'application/json'}
    return headerGET


def armarJSONActualizarProducto(precio='', stock=''):
    datos = {}
    if precio != '':
        datos['preciopublicobase'] = precio
    if stock != '':
        datos['cantidad'] = stock
    return datos


def obtenerMarcas():
    urlGET = crearURLMarcas()
    try:
        r = requests.get(urlGET)
        rJson = r.json()
        if r.status_code == 200:
            if rJson['estatus'] == 'success':
                return True, rJson['marcas']
            else:
                r = str(rJson['estatus']) + ':' + str(rJson['mensaje'])
                return False, r
        else:
            r = http_status(r.status_code)
            return False, r
    except Exception as e:
        # print(traceback.format_exc())
        r = "No se pudo ejecutar la petición," + str(e)
        return False, r


def obtenerDetalleCategoria(idCategoria):
    urlCategorias = crearURLCategorias()
    urlCategorias = urlCategorias + '/' + idCategoria
    r = requests.get(urlCategorias)
    rJson = r.json()
    # print('Detalles---------------------')
    # print(rJson)
    # print('Detalles---------------------')
    return rJson


def obtenerCategorias():
    urlGET = crearURLCategorias()
    try:
        r = requests.get(urlGET)
        rJson = r.json()
        if r.status_code == 200:
            if rJson['estatus'] == 'success':
                # print(rJson['categorias'])
                return True, rJson['categorias']
            else:
                r = str(rJson['estatus']) + ':' + str(rJson['mensaje'])
                return False, r
        else:
            r = http_status(r.status_code)
            return False, r
    except Exception as e:
        # print(traceback.format_exc())
        r = "No se pudo ejecutar la petición," + str(e)
        return False, r


def obtenerIDconNombreMarca(nombreMarca):
    exito, listaMarcas = obtenerMarcas()
    if exito is True:
        for idDeMarca, marca in listaMarcas.items():
            if marca.casefold() == nombreMarca.casefold():
                return idDeMarca
    return "No encontrado"


def obtenerIDconNombreCategoria(nombreCategoria):
    exito, listaCategorias = obtenerCategorias()
    # print(listaCategorias)
    # print(type(listaCategorias))
    if exito is True:
        """dictas=dict_generator(listaCategorias, pre=None)
        for d in dictas:
                print(d)
                print('<---------------------------->')"""
        for categoriasPadre in listaCategorias:
            # print(categoriasPadre)
            categoriaPadreAux = categoriasPadre['nombre']
            idDeCategoria = categoriasPadre['idcategoria']
            # print(categoriaPadreAux + '-' + str(idDeCategoria))
            # print("<-------------------------------->")
            if categoriaPadreAux.casefold() == nombreCategoria.casefold():
                return idDeCategoria
    return "No encontrado"


def agregarProducto(producto):
    # messagebox.showinfo("showinfo", productoString)
    if type(producto) is not dict:
        producto = json.loads(producto)
    # messagebox.showinfo("showinfo", producto)
    urlPOST = crearURLProducto()
    # producto = armarNuevoProducto()
    headersPOST = regresarHeaderPOSTAndPUT()
    try:
        r = requests.post(urlPOST, data=producto, headers=headersPOST)
        # print(r.status_code)
        # print(r)
        # print(r.json())
        if r.status_code == 200:
            rJson = r.json()
            # print(rJson)
            if rJson['estatus'] == 'error':
                strRJson = str(rJson['mensaje'])
                r = str(rJson['estatus']) + ': '
                if '{' in strRJson and '}' in strRJson:
                    if 'nombre' in strRJson:
                        r = r + 'revisar nombre, '
                    if 'descripcion' in strRJson:
                        r = r + 'revisar descripcion, '
                    if 'especificacionestecnicas' in strRJson:
                        r = r + 'revisar especificaciones tecnicas, '
                    if 'alto' in strRJson:
                        r = r + 'revisar alto, '
                    if 'ancho' in strRJson:
                        r = r + 'revisar ancho, '
                    if 'profundidad' in strRJson:
                        r = r + 'revisar profundidad, '
                    if 'peso' in strRJson:
                        r = r + 'revisar peso, '
                    if 'preciopublicobase' in strRJson:
                        r = r + 'revisar precio publico base, '
                    if 'preciopublicooferta' in strRJson:
                        r = r + 'revisar precio publico oferta, '
                    if 'cantidad' in strRJson:
                        r = r + 'revisar cantidad, '
                    if 'skupadre' in strRJson:
                        r = r + 'revisar skupadre, '
                    if 'ean' in strRJson:
                        r = r + 'revisar ean, '
                    if 'estatus' in strRJson:
                        r = r + 'revisar estatus, '
                    if 'embarque' in strRJson:
                        r = r + 'revisar embarque, '
                    if 'categoria' in strRJson:
                        r = r + 'revisar categoria, '
                    if 'fotos' in strRJson:
                        r = r + 'revisar fotos, '
                    if 'agregarmarca' in strRJson:
                        r = r + 'revisar amarca, '
                    if 'tag' in strRJson:
                        r = r + 'revisar tag, '
                    if 'garantia' in strRJson:
                        r = r + 'revisar garantia, '
                    if r[-2] == ',':
                        r = r[:-2]
                else:
                    r = r + str(rJson['mensaje'])
            elif rJson['estatus'] == 'success':
                # print(rJson['infoproducto']['transactionid'])
                idClaroShop = str(rJson['infoproducto']['transactionid'])
                # print(type(rJson['infoproducto']))
                r = str(
                    rJson['mensaje']) + ': El producto ha sido subido exitosamente ->' + idClaroShop
            else:
                r = str(rJson['estatus']) + ':' + str(rJson['mensaje'])
        else:
            r = http_status(r.status_code)
    except Exception as e:
        r = "No se pudo ejecutar la petición," + str(e)
    # messagebox.showinfo("showinfo", r)
    return r


def actualizarProducto(datos):
    idDelProducto = datos[0]
    precio = datos[1]
    stock = datos[2]
    urlPUT = crearURLProducto()
    urlPUT = urlPUT + '/' + idDelProducto
    # print(urlPUT)
    datosAModificar = armarJSONActualizarProducto(precio, stock)
    headersPUT = regresarHeaderPOSTAndPUT()
    try:
        r = requests.put(urlPUT, data=datosAModificar, headers=headersPUT)
        if r.status_code == 200:
            rJson = r.json()
            # print(rJson)
            r = str(rJson['estatus']) + ':' + str(rJson['mensaje'])
        else:
            r = http_status(r.status_code)
    except Exception as e:
        # print(traceback.format_exc())
        r = "No se pudo ejecutar la petición," + str(e)
    return r


def obtenerPedidosPendientesPrueba():
    urlPedidos = crearURLPedidos()
    entregadoPendienteembarcado = 'pendientes'
    parametros = {'action': entregadoPendienteembarcado,
                  'limit': '1', 'page': 1, 'search': ''}
    try:
        r = requests.get(urlPedidos, params=parametros)
        rJson = r.json()
        if r.status_code == 200:
            return None
        elif r.status_code == 400:
            return f"Credenciales incorrectas: {rJson.get('mensaje')}"
        else:
            return f"Error {r.status_code}"
    except Exception as e:
        # print(traceback.format_exc())
        r = "No se pudo realizar la petición" + str(e)
        return r


def obtenerPedidosPendientes():
    urlPedidos = crearURLPedidos()
    entregadoPendienteembarcado = 'pendientes'
    parametros = {'action': entregadoPendienteembarcado,
                  'limit': '10', 'page': 1, 'search': ''}
    pendientesARevisar = []
    try:
        r = requests.get(urlPedidos, params=parametros)
        rJson = r.json()
        # print(rJson)
        # print(rJson['estatus'])
        if r.status_code == 200:
            # print(r.text)
            totalPendientes = rJson['total' + entregadoPendienteembarcado]
            if totalPendientes > 0:
                pendientesListaAux = rJson['lista' + entregadoPendienteembarcado]
                for pendiente in pendientesListaAux:
                    pendientesARevisar.append(
                        crearJSONClaroShopParaBase(pendiente))
                paginasTotales = rJson['totalpaginas']
                for page in range(2, paginasTotales + 1, 1):
                    parametros['page'] = page
                    r = requests.get(urlPedidos, params=parametros)
                    rJson = r.json()
                    # print(rJson)
                    if r.status_code == 200:
                        pendientesListaAux = rJson['lista' + entregadoPendienteembarcado]
                        for pendiente in pendientesListaAux:
                            pendientesARevisar.append(
                                crearJSONClaroShopParaBase(pendiente))
                r = pendientesARevisar
                exito = True
            else:
                r = 'No hay pendientes'
                exito = False
        else:
            r = http_status(r.status_code)
            exito = False
    except Exception as e:
        # print(traceback.format_exc())
        r = "No se pudo ejecutar la petición," + str(e)
        exito = False
    return exito, r


def enviarPedidosPendientesABot():
    exito, mensaje = obtenerPedidosPendientes()
    if exito is True:
        return str(mensaje)
    else:
        return mensaje


def crearJSONClaroShopParaBase(noPedido):
    try:
        detallesPedido = obtenerDetallesPedido(noPedido)
        if type(detallesPedido) == str:
            raise Exception(detallesPedido)
        # print('++++++++++++------------------------------')
        # print(detallesPedido)
        # print('++++++----------------------------------')
        order = {}
        # order['orderId'] -> generado por sql server
        order['marketplaceId'] = MARKETPLACE_ID  # 2 -> claro shop
        # print('-----------------DetallesPedido')
        # print(json.dumps(detallesPedido, indent=4))
        # print('-----------------DetallesPedido')
        list_status = seed.get_list_status_Turtle()
        estatusPedido = detallesPedido['estatuspedido']
        urlOrder = 'https://selfservice.claroshop.com/pedidos/'
        order['orderStatusID'] = 1
        if estatusPedido['estatus'] == 'Pendiente' or estatusPedido['estatus'] == 'pendiente':
            order['status'] = list_status[0]
            urlOrder = urlOrder + 'pendientes'
        elif estatusPedido['estatus'] == 'Por embarcar con Proveedor' or estatusPedido['estatus'] == 1 or estatusPedido['estatus'] == 'Validacion Guia' or estatusPedido['estatus'] == 'Parcialmente surtido':
            order['status'] = list_status[2]
            urlOrder = urlOrder + 'embarcados'
        elif estatusPedido['estatus'] == 'Entregado Parcial':
            order['status'] = list_status[3]
            urlOrder = urlOrder + 'entregados'
        elif estatusPedido['estatus'] == 'Entregado':
            order['status'] = list_status[4]
            urlOrder = urlOrder + 'entregados'
        else:
            print('-----------Nuevo status descubierto--------\n' + estatusPedido['estatus'] + '\n-----------------------')

        order['creationDate'] = estatusPedido['fechacolocado']
        order['marketplaceOrderId'] = noPedido

        order['orderURL'] = urlOrder
        order['operationIds'] = ''

        productosDePedido = detallesPedido['productos']
        productosSinRepetir = {}
        paidAmound = 0.0
        envios = 0.0
        for productoDePedido in productosDePedido:
            transactionid = productoDePedido['transactionid']
            guia = productoDePedido['guia']
            # print('muuuu')
            if transactionid in productosSinRepetir:
                # print('===========')
                # print(productosSinRepetir)
                # print('++++++++++++')
                productosSinRepetir[transactionid]['units'] = productosSinRepetir[transactionid]['units'] + 1
                paidAmound = paidAmound + float(productoDePedido['importe'])
                # print('oooooo')
            else:
                productoJSON = obtenerDetallesProducto(transactionid)
                if type(detallesPedido) == str:
                    raise Exception(productoJSON)
                # print('productoJSON*****************************')
                # print(json.dumps(productoJSON, indent=4))
                # print('*****************************')
                if productoJSON['fulfillment'] is True:
                    order['FulfillmentChannelId'] = 0
                else:
                    order['FulfillmentChannelId'] = 1
                if productoDePedido['skuhijo'] == '0':
                    skuAux = productoJSON['skupadre']
                    modelAux = productoJSON['skupadre']
                    variations = []
                else:
                    skuAux = productoDePedido['skuhijo']
                    modelAux = productoDePedido['skuhijo']
                    variaciones = productoDePedido['producto'].replace(
                        productoJSON['nombre'], "")
                    # print('variaciones=========================')
                    # print(variaciones)
                    # print('variaciones=========================')
                    variaciones = variaciones.split(':')[1].strip().split('/')
                    color = variaciones[0]
                    talla = variaciones[1]
                    variacionesDict = {
                        'talla': talla,
                        'color': color
                    }
                    variations = variacionesDict
                titleAux = productoJSON['nombre']
                fotosAux = productoJSON['fotos']
                fotosAuxJSON = fotosAux[0]
                photoAux = fotosAuxJSON['url']
                brandAux = productoJSON['marca']
                importeAux = float(productoDePedido['importe'])
                envioAux = float(productoDePedido['envio'])
                unitAux = 1
                idpedidorelacion = productoDePedido['idpedidorelacion']
                # obtenerColoresDeProducto('3847314')
                paidAmound = importeAux
                envios = envioAux
                receivedAmount = paidAmound - envios
                publicationId = productoJSON["transactionid"]
                publicationStock = productoJSON["cantidad"]
                publicationPrice = productoJSON["preciopublicobase"]
                publicationPrice = productoJSON["preciopublicobase"]
                publicationTime = productoJSON["embarque"]
                publicationStatus = productoJSON["estatus"]
                productoNuevo = {'sku': skuAux, 'title': titleAux, 'photo': photoAux, 'brand': brandAux, 'model': modelAux, 'units': unitAux, 'operationId': idpedidorelacion, 'variations': variations,
                                 'paidAmount': paidAmound, 'shipping': envios, 'fee': 0, 'receivedAmount': receivedAmount,
                                 "publicationId": publicationId,
                                 "publicationStock": publicationStock,
                                 "publicationPrice": publicationPrice,
                                 "publicationTime": publicationTime,
                                 "publicationStatus": publicationStatus}
                productosSinRepetir[transactionid] = productoNuevo

        order['products'] = productosSinRepetir.values()
        order['ShippingInfoNumber'] = guia
        order['paidAmount'] = round(paidAmound + envios, 2)
        order['fee'] = round(paidAmound * 0.14, 2)
        order['shipping'] = round(envios, 2)
        order['receivedAmount'] = round(
            (order['paidAmount'] - envios) * 0.86, 2)
        order['comments'] = ''
        datosEnvio = detallesPedido['datosenvio']
        # print(datosEnvio)
        direccionCompleta = f'{datosEnvio["direccion"]}, {datosEnvio["colonia"]}, {datosEnvio["del/municipio"]}'
        direccionCompleta = direccionCompleta[:250]
        clientDict = {
            "marketplaceId": MARKETPLACE_ID,
            "marketplaceClientId": "",
            "registrationDate": "",
            "city": datosEnvio['ciudad'],
            "state": datosEnvio['estado'],
            "nickname": '',
            "name": datosEnvio['entregara'],
            "score": "",
            "zipCode": datosEnvio["cp"],
            "phoneNumber": "",
            "email": "",
            "address": direccionCompleta,
        }
        order['client'] = clientDict
        # print("============================================")
        # print(order)
        # print("============================================")
        return order
    except Exception as e:
        print("ZZZZZZZZZZZZZZZZZZZZZZZZZZZZ")
        print(str(e))
        print(detallesPedido)
        print('pppppppppppppppppppppppppppp')
        return None


def obtenerDetallesProducto(idProducto):
    try:
        urlProducto = crearURLProducto()
        headerGET = regresarHeaderGET()
        urlGET = urlProducto + '/' + str(idProducto)
        r = requests.get(urlGET, headers=headerGET)
        if r.status_code != 200:
            raise Exception(f'Status code: {str(r.status_code)}')
        rJson = r.json()
        return rJson['producto']
    except Exception as e:
        return str(e)


def obtenerDetallesPedido(noPedido):
    try:
        urlPedidos = crearURLPedidos()
        headerGET = regresarHeaderGET()
        parametros = {'action': 'detallepedido', 'nopedido': noPedido}
        r = requests.get(urlPedidos, params=parametros, headers=headerGET)
        if r.status_code != 200:
            raise Exception(f'Status code: {str(r.status_code)}')
        rJson = r.json()
        return rJson
    except Exception as e:
        return str(e)


def retornarPedidosUltimosDias(days):
    urlFiltros = crearURLPedidosFiltros()
    headersPedidosFechas = headersFiltros()
    parametrosFiltroPendientes = parametrosFiltros('pendientes', days)
    parametrosFiltroEmbarcados = parametrosFiltros('embarcados', days)
    parametrosFiltroEntregados = parametrosFiltros('entregados', days)
    noSPedidosPendientes = retornarPedidosPorFechaPendientes(
        urlFiltros, headersPedidosFechas, parametrosFiltroPendientes)
    # print(noSPedidosPendientes)
    noSPedidosEmbarcados = retornarPedidosPorFechaEmbarcados(
        urlFiltros, headersPedidosFechas, parametrosFiltroEmbarcados)
    # print(noSPedidosEmbarcados)
    noSPedidosEntregados = retornarPedidosPorFechaEntregados(
        urlFiltros, headersPedidosFechas, parametrosFiltroEntregados)
    # print(noSPedidosEntregados)
    pedidosGeneral = []
    pedidosGeneral.extend(noSPedidosPendientes)
    pedidosGeneral.extend(noSPedidosEmbarcados)
    pedidosGeneral.extend(noSPedidosEntregados)
    return pedidosGeneral


def headersFiltros():
    return {'Pragma': 'akamai-x-cache-on, akamai-x-cache-remote-on, akamai-x-check-cacheable, akamai-x-get-cache-key, akamai-x-get-extracted-values, akamai-x-get-nonces, akamai-x-get-ssl-client-session-id, akamai-x-get-true-cache-key, akamai-x-serial-no', 'X-Akamai-Debug': 'ON'}


def parametrosFiltros(action, days):
    fechaFinal, fechaInicio = obtenerUltimosDiasFormatoYYYYMMDD(days)
    # fechaInicio = dt.datetime(2022, 8, 23)
    # fechaFinal = dt.datetime(2022, 8, 25)
    parametrosPedidosFecha = {'action': action,
                              'date_start': fechaInicio, 'date_end': fechaFinal}
    return parametrosPedidosFecha


def obtenerUltimosDiasFormatoYYYYMMDD(days):
    hoy = datetime.today()
    hoyFormato = hoy.strftime("%Y-%m-%d")
    cincoDias = hoy - timedelta(days=days)
    CincoDiasFormato = cincoDias.strftime("%Y-%m-%d")
    return hoyFormato, CincoDiasFormato


def retornarPedidosPorFechaPendientes(urlFiltros, headersPedidosFechas, parametrosFiltroPendientes):
    try:
        noSPedidos = []
        r = requests.get(urlFiltros, headers=headersPedidosFechas,
                         params=parametrosFiltroPendientes)
        # print('eeeeeeeeeeeeeeeeeee')
        # print(r)
        # print(r.text)
        # print('eeeeeeeeeeeeeeeeeee')
        if r.status_code != 200:
            print(r.text)
            raise Exception(f'status code {str(r.status_code)}')
        rJson = r.json()
        listaPedidos = rJson['0']['listapendientes']
        # print(listaPedidos)
        for pedido in listaPedidos:
            noSPedidos.append(pedido['nopedido'])
    except Exception as e:
        noSPedidos = []
        print(
            f'Ocurrio un error al obtener los pedidos pendientes de claro shop: {str(e)}')
    finally:
        return noSPedidos


def retornarPedidosPorFechaEmbarcados(urlFiltros, headersPedidosFechas, parametrosFiltroEmbarcados):
    try:
        noSPedidos = []
        r = requests.get(urlFiltros, headers=headersPedidosFechas,
                         params=parametrosFiltroEmbarcados)
        # print('eeeeeeeeeeeeeeeeeee')
        # print(r)
        # print(r.text)
        # print('eeeeeeeeeeeeeeeeeee')
        if r.status_code != 200:
            raise Exception(f'status code {str(r.status_code)}')
        rJson = r.json()
        listaPedidos = rJson['0']['listaguiasautomaticas']
        # print(listaPedidos)
        for pedido in listaPedidos:
            noSPedidos.append(pedido['nopedido'])
    except Exception as e:
        noSPedidos = []
        print(f'Ocurrio un error al obtener los pedidos embarcados: {str(e)}')
    finally:
        return noSPedidos


def retornarPedidosPorFechaEntregados(urlFiltros, headersPedidosFechas, parametrosFiltroEntregados):
    try:
        noSPedidos = []
        r = requests.get(urlFiltros, headers=headersPedidosFechas,
                         params=parametrosFiltroEntregados)
        # print('eeeeeeeeeeeeeeeeeee')
        # print(r)
        # print(r.text)
        # print('eeeeeeeeeeeeeeeeeee')
        if r.status_code != 200:
            raise Exception(f'status code {str(r.status_code)}')
        rJson = r.json()
        listaPedidos = rJson['0']['listaentregados']
        # print(listaPedidos)
        for pedido in listaPedidos:
            noSPedidos.append(pedido['nopedido'])
    except Exception as e:
        noSPedidos = []
        print(f'Ocurrio un error al obtener los pedidos entregados: {str(e)}')
    finally:
        return noSPedidos


def obtenerListaJSONSParaBase(days):
    listaPedidos = retornarPedidosUltimosDias(days)
    listaPedidos = list(dict.fromkeys(listaPedidos))
    # print(listaPedidos)
    listaJSONParaBase = []
    for noPedido in listaPedidos:
        jsonAux = crearJSONClaroShopParaBase(noPedido)
        if jsonAux is not None:
            listaJSONParaBase.append(jsonAux)
    return listaJSONParaBase

# En la API de claroShop todos los colores se asocian con productos y las tallas se asocian con colores,
# y las tallas se asocian con colores


def agregarColorAProducto(idDelProducto, nombreColor, estatus):
    # POST para crear colores
    # nuevoColor = json.loads(productoString)
    nuevoColor = {
        'idproducto': idDelProducto,
        'nombre': nombreColor,
        'estatus': estatus
    }
    urlPOST = crearURLColores()
    # producto = armarNuevoProducto()
    headersPOST = regresarHeaderPOSTAndPUT()
    try:
        r = requests.post(urlPOST, data=nuevoColor, headers=headersPOST)
        # print(r.status_code)
        if r.status_code == 200:
            r = r.json()
        else:
            r = http_status(r.status_code)
    except Exception as e:
        r = "No se pudo ejecutar la petición," + str(e)
    # messagebox.showinfo("showinfo", r)
    return r


def obtenerColoresDeProducto(idProducto):
    urlColores = crearURLColores()
    headerGET = regresarHeaderGET()
    urlColores = urlColores + '/' + idProducto
    r = requests.get(urlColores, headers=headerGET)
    rJson = r.json()
    # print('Detalles---------------------')
    # print(rJson)
    # print('Detalles---------------------')
    return rJson


def obtenerTallasDeColor(idColor):
    urlTallas = crearURLTallas()
    urlTallas = urlTallas + '/' + idColor
    r = requests.get(urlTallas)
    rJson = r.json()
    # print('Detalles---------------------')
    # print(rJson)
    # print('Detalles---------------------')
    return rJson


def agregarTallaAColor(idColor, talla, sku, ean, estatus, stock):
    # POST para crear colores
    # nuevoColor = json.loads(productoString)
    nuevaTalla = {
        'idcolor': idColor,
        'talla': talla,
        'sku': sku,
        'ean': ean,
        'estatus': estatus,
        'stock': stock,
    }
    urlTallas = crearURLTallas()
    # producto = armarNuevoProducto()
    headersPOST = regresarHeaderPOSTAndPUT()
    try:
        r = requests.post(urlTallas, data=nuevaTalla, headers=headersPOST)
        # print(r.status_code)
        if r.status_code == 200:
            r = r.json()
            # print(rJson)
        else:
            r = http_status(r.status_code)
    except Exception as e:
        r = "No se pudo ejecutar la petición," + str(e)
    # messagebox.showinfo("showinfo", r)
    return r


if __name__ == "__main__":
    print(obtenerListaJSONSParaBase(3))
