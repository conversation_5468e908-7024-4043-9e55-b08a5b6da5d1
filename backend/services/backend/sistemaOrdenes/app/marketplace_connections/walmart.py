import requests
import uuid

client_id = ''
client_secret = ''
MARKETPLACE_ID = ''


def set_credentials_and_id_marketplace(credentials_param=None, marketplace_id=None):
    global client_id
    global client_secret
    global MARKETPLACE_ID
    client_id = credentials_param.get('clientId')
    client_secret = credentials_param.get('clientSecret')
    MARKETPLACE_ID = marketplace_id


def status400():
    return "Bad request"


def status401_3():
    return "Authentication error"


def status404():
    return "Not found"


def status500():
    return "Internal Server Error"


def http_status(status):
    switcher = {
        400: status400(),
        401: status401_3(),
        403: status401_3(),
        404: status404(),
        500: status500()
    }
    return switcher.get(status, "Error desconocido")


def obtenerLimite():
    return 100


def getUrlBase():
    return 'https://marketplace.walmartapis.com/v3'


def GetURLAccessTokenWalmart():
    return getUrlBase() + '/token'


def returnGrantType():
    return {'grant_type': 'client_credentials'}


def getHeaderCommonToken():
    """
    Return dict that contains common elements for each requests calculating uuid and setting info for walmart mx

    Arguments:

    Keyword arguments:

    Return:
        dict -- Return dict that contains common elements for each requests
    """
    guid = uuid.uuid4()
    guid = str(guid)
    headerToken = {
        'WM_MARKET': 'mx',
        'WM_QOS.CORRELATION_ID': guid,
        'WM_SVC.NAME': 'Walmart Marketplace',
    }
    return headerToken


def obtenerTokenJSON():
    """
    Gets access token at JSON format using global variables client_id and client_secret

    Arguments:

    Keyword arguments:

    Return:
        Tuple -- Return a tuple that include a boolean (True if getting token was success or false if  not)
        and a string (access token if getting token was success or info about error if not)
    """
    url = GetURLAccessTokenWalmart()
    headers = getHeaderCommonToken()
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
    headers['Accept'] = 'application/json'
    datos = returnGrantType()
    try:
        r = requests.post(url, auth=(client_id, client_secret),
                          data=datos, headers=headers)
        print(r.text)
        if r.status_code != 200:
            return False, 'Las credenciales son incorrectas'
        else:
            return True, r.json()['access_token']
    except Exception as e:
        return False, f'Ocurrió un error al realizar la autenticación: {str(e)}'
