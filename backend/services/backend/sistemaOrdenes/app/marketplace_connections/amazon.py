import json
from dataclasses import replace
from sp_api.api import CatalogItems, Orders, ListingsItems, ProductFees, Finances, Products
from sp_api.base import SellingApiException, Marketplaces
from datetime import datetime, date, timedelta, timezone
import pytz
from urllib import parse
from time import time
import requests

credentials = dict(
    refresh_token='',
    lwa_app_id='',
    lwa_client_secret='',
    aws_secret_key='',
    aws_access_key='',
    role_arn=''
)
MARKETPLACE_ID = ''


def set_credentials_and_id_marketplace(credentials_param=None, marketplace_id=None):
    global credentials
    global MARKETPLACE_ID
    credentials = dict(

        refresh_token=credentials_param['refreshToken'],
        lwa_app_id=credentials_param['clientId'],
        lwa_client_secret=credentials_param['clientSecret'],
    )
    MARKETPLACE_ID = marketplace_id


def getcatalogitemsbykeyword(keywords):
    catalogclient = CatalogItems(credentials=credentials, marketplace=Marketplaces.MX)
    query = catalogclient.search_catalog_items(keywords=keywords, includedData="images")
    print(query)


def get_orders(dias):
    try:
        res = Orders(credentials=credentials).get_orders(CreatedAfter=(date.today(
        ) - timedelta(days=dias)).isoformat(), MarketplaceIds=["A1AM78C64UM0Y8"])
        # print("--------$$$------------")
        # print(res)
        return res.payload  # json data
    except SellingApiException as ex:
        print(ex)
    return


def get_order(order_id):
    try:
        res = Orders(credentials=credentials).get_order(order_id=order_id)
        return res.payload  # json data
    except SellingApiException as ex:
        print(ex)
    return


def get_order_items(order_id):
    try:
        res = Orders(credentials=credentials).get_order_items(
            order_id, MarketplaceIds=["A1AM78C64UM0Y8"])
        return res.payload  # json data
    except SellingApiException as ex:
        print(ex)
    return


def get_product_info(asin):
    try:
        res = CatalogItems(credentials=credentials).get_catalog_item(
            asin=asin, marketplaceIds=["A1AM78C64UM0Y8"], ItemCondition="New")
        data = res.payload
        return data
    except SellingApiException as ex:
        print(ex)
    return


def estimate_product_fees(sku, price, shipping=None, is_fba=False):
    # print(f"Sku: {sku}, price: {price}")
    try:
        res = ProductFees(credentials=credentials, marketplace=Marketplaces.MX).get_product_fees_estimate_for_sku(
            seller_sku=sku, price=price, shipping_price=shipping, currency='MXN', is_fba=is_fba)  # , marketplaceIds =["A1AM78C64UM0Y8"])
        data = res.payload
        # print(json.dumps(data, indent=4))
        return data['FeesEstimateResult']['FeesEstimate']['TotalFeesEstimate']['Amount']
    except SellingApiException as ex:
        print(ex)
    return


def get_order_fees(order_id, is_fba=False):
    financial_events = get_financial_events(order_id)['FinancialEvents']
    product_event_list = financial_events['ShipmentEventList'][0]['ShipmentItemList']
    total_amount = 0
    shipping = 0
    commision = 0
    for product_events in product_event_list:
        charge_list = product_events['ItemChargeList']
        fee_list = product_events['ItemFeeList']
        for charge in charge_list:
            total_amount += charge['ChargeAmount']['CurrencyAmount']
            # if charge['ChargeType'] == 'Principal':
            #     total_amount += charge['ChargeAmount']['CurrencyAmount']
            # if charge['ChargeType'] == 'Tax':
            #     total_amount += charge['ChargeAmount']['CurrencyAmount']
        if is_fba:
            for charge in charge_list:
                if charge['ChargeType'] == 'ShippingCharge':
                    shipping += charge['ChargeAmount']['CurrencyAmount'] * -1
            for fee in fee_list:
                if fee['FeeType'] == "FBAPerUnitFulfillmentFee":
                    commision += fee['FeeAmount']['CurrencyAmount'] * \
                        product_events['QuantityShipped'] * -1
                if fee['FeeType'] == "Commission":
                    commision += fee['FeeAmount']['CurrencyAmount'] * \
                        product_events['QuantityShipped'] * -1
        else:
            for fee in fee_list:
                commision += fee['FeeAmount']['CurrencyAmount'] * \
                    product_events['QuantityShipped'] * -1
                # if fee['FeeType'] == "Commission":
                #    commision += fee['FeeAmount']['CurrencyAmount'] * product_events['QuantityShipped'] * -1

    received_amount = total_amount - commision
    if not is_fba:
        adjustment_list = financial_events['AdjustmentEventList']
        for adjustment in adjustment_list:
            shipping += adjustment['AdjustmentAmount']['CurrencyAmount'] * -1
    else:
        received_amount -= shipping

    return {
        'total_amount': total_amount,
        'commision': commision,
        'shipping': shipping,
        'received_amount': received_amount
    }


def get_financial_events(order_id):
    try:
        res = Finances(
            Marketplaces.MX, credentials=credentials).get_financial_events_for_order(order_id)
        return res.payload  # json data
    except SellingApiException as ex:
        print(ex)
    return


def get_listing_info(sku):
    try:
        res = ListingsItems(credentials=credentials).get_listings_item(
            sellerId='A3KOVF9Y1J92CX', sku=sku, marketplaceIds=["A1AM78C64UM0Y8"])
        return res.payload  # json data
    except SellingApiException as ex:
        print(f"sku con error: {sku}. \n error:\n{ex}")
        return {
            "summaries": [{
                'mainImage': {
                    'link': ""
                }
            }]
        }


def get_client_info(order_info):
    try:
        shipping_info = order_info['ShippingAddress']
        state = shipping_info['StateOrRegion']
        city = shipping_info['City']
        client_info = {
            "id": "",
            "marketplaceId": MARKETPLACE_ID,
            "marketplaceClientId": "",
            "registrationDate": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
            "city": city,
            "state": state,
            "address": "",
            "zipCode": "",
            "phoneNumber": "",
            "email": "",
            "nickname": "",
                        "name": "",
                        "score": 0,
        }
    except Exception as e:
        print(str(e))
        client_info = {
            "id": "",
            "marketplaceId": MARKETPLACE_ID,
            "marketplaceClientId": "",
            "registrationDate": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
            "city": "",
            "state": "",
            "address": "",
            "zipCode": "",
            "phoneNumber": "",
            "email": "",
            "nickname": "",
                        "name": "",
                        "score": 0,
        }
    return client_info


"""def get_new_orders(dias=configs.CANTIDAD_DE_DIAS_A_OBTENER_AMAZON, last_order=None):
    final_orders = {"orders": []}
    orders = get_orders(dias)['Orders']
    # print(json.dumps(orders,indent=4))
    list_status=seed.get_list_status_Turtle()
    for o in orders:
        final_order = form_final_order_info(o, list_status)
        final_orders['orders'].append(final_order)
    return final_orders"""


def form_products_json(products):
    clean_products = []
    for p in products:
        # print(json.dumps(p, indent=4))
        # print("----------------------------------------------------")
        asin = p['ASIN']
        sku = str(p['SellerSKU'])
        title = p['Title']
        units = p['QuantityOrdered']
        publicationStock = None
        publicationStatus = "PUBLISHED"
        publicationTime = None
        publicationId = asin
        if 'ItemPrice' in p:
            receivedAmount = p['ItemPrice']['Amount']
            publicationPrice = receivedAmount
        else:
            receivedAmount = None
            publicationPrice = None
        if 'ShippingTax' in p:
            fee = float(p['ShippingTax']['Amount'])
            if 'ItemTax' in p:
                fee = fee + float(p['ItemTax']['Amount'])
        else:
            fee = None
        if 'ShippingPrice' in p:
            shipping = p['ShippingPrice']['Amount']
        else:
            shipping = None
        if receivedAmount is None or fee is None or shipping is None:
            paidAmount = None
        else:
            paidAmount = float(receivedAmount) + float(fee) + float(shipping)

        try:
            product_info = get_product_info(asin)['summaries'][0]
            # print(json.dumps(product_info, indent=4))
            # print("----------------------------------------------------")
            brand = product_info['brandName']
        except Exception as e:
            print(str(e))
            brand = ""
        try:
            listing_info = get_listing_info(parse.quote(sku, safe=''))[
                'summaries'][0]
            photo = listing_info['mainImage']['link']
        except Exception as e:
            print(str(e))
            photo = None
        try:
            model = str(product_info['modelNumber'])
        except Exception as e:
            print(str(e))
            # print(json.dumps(p, indent=4))
            # print("----------------------------------------------------")
            model = ""
        if units > 0:
            clean_products.append({
                "sku": sku,
                "title": title,
                "photo": photo,
                "brand": brand,
                "variations": [],
                "operationId": 0,
                "model": model,
                "units": units,
                "paidAmount": paidAmount,
                "shipping": shipping,
                "fee": fee,
                "receivedAmount": receivedAmount,
                "publicationId": publicationId,
                "publicationStock": publicationStock,
                "publicationPrice": publicationPrice,
                "publicationTime": publicationTime,
                "publicationStatus": publicationStatus
            })
    return clean_products


def form_final_order_info(order, list_status):
    paidAmount = 0.0
    fee = 0.0
    shipping = 0.0
    received_amount = 0.0
    status_dict = {
        "Pending": list_status[0],
        "PendingAvailability": list_status[1],
        "Unshipped": list_status[2],
        "PartiallyShipped": list_status[2],
        "Shipped": list_status[3],
        "Cancelled": list_status[5],
        "Canceled": list_status[5],
        "Unfulfillable": list_status[7]
    }
    status = status_dict[order['OrderStatus']]
    canalFulfillment = 2 if order['FulfillmentChannel'] == 'AFN' else 1
    # print(json.dumps(o, indent=4))
    marketplaceOrderId = order['AmazonOrderId']
    products = get_order_items(marketplaceOrderId)['OrderItems']
    # print(json.dumps(order, indent=4))
    clean_products = form_products_json(products)
    try:
        # print(order)
        # print("---------")
        # paidAmount = order['OrderTotal']['Amount']
        # fees = get_order_fees(marketplaceOrderId)
        # print(fees)
        for miniProduct in clean_products:
            paidAmount = paidAmount + float(miniProduct["paidAmount"])
            fee = fee + float(miniProduct["fee"])
            shipping = shipping + float(miniProduct["shipping"])
        received_amount = paidAmount - fee - shipping
    except Exception as e:
        print(str(e))

    client = get_client_info(order)

    return {
        "marketplaceId": MARKETPLACE_ID,
        "FulfillmentChannelId": canalFulfillment,
        "status": status,
        "orderStatusID": 1,
        "creationDate": order['PurchaseDate'],
        "marketplaceOrderId": marketplaceOrderId,
        "orderURL": f'https://sellercentral.amazon.com.mx/orders-v3/order/{marketplaceOrderId}',
        "operationIds": [],
        "products": clean_products,
        "paidAmount": paidAmount,
        "fee": fee,
        "shipping": shipping,
        "receivedAmount": received_amount,
        "ShippingInfoNumber": None,
        "client": client
    }


def get_access_token(client_id, client_secret, refresh_token):
    try:
        # Verificar si el client_id de autorización ha sido proporcionado, si no, lanzar una excepción
        if not client_id:
            raise Exception('client_id necesario')
        # Verificar si el client_secret de autorización ha sido proporcionado, si no, lanzar una excepción
        if not client_secret:
            raise Exception('client_secret necesario')
        # Verificar si el client_secret de autorización ha sido proporcionado, si no, lanzar una excepción
        if not refresh_token:
            raise Exception('refresh_token necesario')
        # URL para solicitar un Refresh Token de ml
        URLRefreshToken = 'https://api.amazon.com/auth/o2/token'
        # Configurar los parámetros para la solicitud POST
        params = {
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "client_id": client_id,
            "client_secret": client_secret
        }
        # Realizar la solicitud POST al endpoint de Mercado Libre
        r = requests.post(URLRefreshToken, json=params)
        resk = r.json()
        # print('oooooooooooooooooooooooooooooooooo')
        # print(f'info {resk}')
        # print(r)
        # print(r.status_code)
        # print('oooooooooooooooooooooooooooooooooo')
        # Verificar si la solicitud fue exitosa
        if r.status_code == 200:
            return True, resk["access_token"]
        elif r.status_code > 399 and r.status_code < 400:
            return False, "Credenciales incorrectas"
        else:
            # Si la solicitud POST no fue exitosa, imprimir el error
            # print('Token con Errores')
            # print(f'info {resk}')
            return False, "No fue posible validar tus credenciales"
    except Exception as e:
        # En caso de cualquier excepción, imprimir el error
        print(str(e))
        return False, str(e)


if __name__ == "__main__":
    # print(json.dumps(get_listing_info('C-HM%2FHM-15'), indent=4))
    # print(get_product_info('B092SVN2W4'))
    # print(json.dumps(get_order('702-6548793-1057003'), indent=4))
    print(json.dumps(form_final_order_info(
        get_order('701-3480592-1141047')), indent=4))
    # print(json.dumps(get_new_orders(), indent=4))
    # get_new_orders()
    # print(json.dumps(get_financial_events('702-6701230-1481026'), indent=4))
    # print(get_order_fees('702-6701230-1481026'))
    # print(parse.quote("C-HM/HM-15"))
    # print(json.dumps(get_listing_info('FBACP000145'), indent=4))
