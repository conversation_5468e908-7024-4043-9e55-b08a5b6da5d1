import requests

GRANT_TYPE = 'refresh_token'
ACCESS_TOKEN = ''
CLIENT_ID = ''
CLIENT_SECRET = ''
REFRESH_TOKEN = ''
MARKETPLACE_ID = ''
ML_SELLER_ID = ''


def set_credentials_and_id_marketplace(credentials_param=None, marketplace_id=None):
    print('//////////////////////////*********************')
    print(credentials_param)
    print('--')
    print(marketplace_id)
    print('//////////////////////////*********************')
    global CLIENT_ID
    global CLIENT_SECRET
    global REFRESH_TOKEN
    global ACCESS_TOKEN
    global MARKETPLACE_ID
    global ML_SELLER_ID
    CLIENT_ID = credentials_param.get('clientId')
    CLIENT_SECRET = credentials_param.get('clientSecret')
    REFRESH_TOKEN = credentials_param.get('refreshToken')
    ACCESS_TOKEN = credentials_param.get('accessToken')
    MARKETPLACE_ID = marketplace_id
    ML_SELLER_ID = REFRESH_TOKEN.split('-')[-1] if (type(REFRESH_TOKEN) is str) else None


def get_refreshtoken_by_code(client_id, client_secret, redirect_url, code):
    try:
        # Verificar si el client_id de autorización ha sido proporcionado, si no, lanzar una excepción
        if not client_id:
            raise Exception('client_id necesario')
        # Verificar si el client_secret de autorización ha sido proporcionado, si no, lanzar una excepción
        if not client_secret:
            raise Exception('client_secret necesario')
        # Verificar si el client_secret de autorización ha sido proporcionado, si no, lanzar una excepción
        if not redirect_url:
            raise Exception('redirect_url necesario')
        # Verificar si el código de autorización ha sido proporcionado, si no, lanzar una excepción
        if not code:
            raise Exception('code necesario')
        # URL para solicitar un Refresh Token de ml
        URLRefreshToken = 'https://api.mercadolibre.com/oauth/token'
        # Configurar los parámetros para la solicitud POST
        params = {
            "grant_type": "authorization_code",
            "client_id": client_id,
            "client_secret": client_secret,
            "redirect_uri": redirect_url,
            "code": code,
        }
        # Realizar la solicitud POST al endpoint de Mercado Libre
        r = requests.post(URLRefreshToken, json=params)
        resk = r.json()
        print(f'info {resk}')
        # Verificar si la solicitud fue exitosa
        if r.status_code == 200:
            return True, resk["refresh_token"]
        elif r.status_code == 400:
            return False, "Credenciales incorrectas, puede que tu code haya expirado"
        else:
            # Si la solicitud POST no fue exitosa, imprimir el error
            print('Token con Errores')
            print(f'info {resk}')
            return False, "No fue posible validar tus credenciales"
    except Exception as e:
        # En caso de cualquier excepción, imprimir el error
        print(str(e))
        return False, str(e)


def renovar_access_token():
    global ACCESS_TOKEN
    print('jjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV')
    print(GRANT_TYPE)
    print(CLIENT_ID)
    print(CLIENT_SECRET)
    print(REFRESH_TOKEN)
    print('jjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV')
    url = f'https://api.mercadolibre.com/oauth/token?grant_type={GRANT_TYPE}&client_id={CLIENT_ID}&client_secret={CLIENT_SECRET}&refresh_token={REFRESH_TOKEN}'
    try:
        r = requests.post(url)
        print('r')
        print(r)
        print(r.text)
        print('r')
        if r.status_code != 200:
            return False, 'Las credenciales son incorrectas'
        else:
            return True, r.json()['access_token']
    except Exception as e:
        return False, f'Ocurrió un error al realizar la autenticación: {str(e)}'
