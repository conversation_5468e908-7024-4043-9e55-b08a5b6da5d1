# Built in modules
import os
import json
# Third-party libraries
# Custom libraries
from sistemaOrdenes import configs


def read_secrets():
    filename = os.path.join(os.path.dirname(__file__), configs.RUTA_SECRETS)
    try:
        with open(filename, mode='r') as f:
            return json.loads(f.read())
    except FileNotFoundError:
        print('err')
        return {}


def save_ml_notification(notification_body):
    with open("notification.txt", 'w') as file:
        file.write(notification_body)
        return


def log(message):
    with open(configs.LOGFILE, 'a') as file:
        file.write(message)
        return


def get_pending_invoices():
    path = os.path.realpath(__file__)
    path = '\\'.join(path.split('\\')[:-1])
    pending_invoices = os.listdir(f'{path}\\..\\porProcesar')
    return (list(
        map(
            lambda invoice: f'{path}\\..\\porProcesar\\{invoice}',
            pending_invoices)))


def move_invoice(invoice_path, supplier):
    split_path = invoice_path.split('\\')
    path = '\\'.join(split_path[:-1])
    filename = split_path[-1]
    os.rename(invoice_path, f'{path}\\..\\suppliers\\{supplier}\\{filename}')

# print(get_pending_invoices())
