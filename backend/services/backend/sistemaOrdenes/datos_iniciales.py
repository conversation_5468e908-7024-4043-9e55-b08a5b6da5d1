import uuid
import traceback
from .app.models.db import Base, engine
from .app.models.Models import OrderStatusFlag, OrderInternalStatusType, \
    FulfillmentChannel, OrderInternalStatus, OrderStatus, \
    DirectSaleInternalStatus, DirectSaleType, Role, Zone, Store, \
    Supplier, SupplierStore, CustomVariation, CustomVariationValue, \
    SupplierPlatform, User, AmountChangerType, AmountChanger, \
    ChangerGroup, PublicationStatus, WarrantyTimeFrame, WarrantyType, \
    ProductBase, ProductBase_CustomVariation, Product, \
    ProductVariationCustom, Kit_Product, Kit, ProductInEntryDocument, \
    EntryDocument, Invoice, Product_Store, DirectSale, \
    ProductInDirectSale, ProductInDirectSale_AmountChanger, \
    Client, DirectSale_AmountChanger, LocationLevel, LocationLevelType, \
    LocationLevelItem, LocationLevelItem_Product_Store, \
    Marketplace, MarketplaceGroupCredentials, SupportedMarketplace, \
    MarketplaceGroup, SupportedMarketplaceGroup
from .app.ownUtils import generate_hashedsalt, generate_internal_base_sku, generate_internal_sku, get_time_zone_now, return_amount_changer
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.xml.products.stockAtenea import load_stock_products_atenea
session = ScopedSession()


# funtion to create or delete entire db model()
def create_turtle():
    Base.metadata.create_all(engine)


def drop_turtle():
    Base.metadata.drop_all(engine)


# util functions
def register_supplier_store(storeName=None, supplierAppId=None,
                            address=None, phone=None, urlMaps=None,
                            zone=None, proveedor=None, session=None):
    supplier_store_aux = SupplierStore(
        storeName=storeName, supplierAppId=supplierAppId,
        address=address, phone=phone, urlMaps=urlMaps)
    supplier_store_aux.Zone = zone
    supplier_store_aux.Supplier = proveedor
    session.add(supplier_store_aux)


def register_supplier(supplier_param=None, supplier_platform_param=None,
                      zone_param=None, session=None):
    supplier_aux = Supplier(RFC=supplier_param.get('RFC'),
                            supplierName=supplier_param.get('supplierName'),
                            email=supplier_param.get('email'),
                            salesExecutive=supplier_param.get('salesExecutive'))
    if supplier_platform_param:
        supplier_platform_aux = SupplierPlatform(
            url=supplier_platform_param.get('url'),
            user=supplier_platform_param.get('user'),
            password=supplier_platform_param.get('password'),
            token=supplier_platform_param.get('token'))
        supplier_aux.SupplierPlatform = supplier_platform_aux
    supplier_store_aux = SupplierStore(storeName='Desconocido')
    supplier_store_aux.Zone = zone_param
    supplier_aux.SupplierStores = [supplier_store_aux,]
    session.add(supplier_aux)
    return supplier_aux


# initial settings info funtions
def insert_supported_marketplaces(session=session):
    supported_marketplace_group_aux = SupportedMarketplaceGroup(name='Amazon', urlPhoto='groups/amazon.png')
    supported_marketplace_aux = SupportedMarketplace(name='Amazon', urlPhoto='marketplaces/amazon.png')
    supported_marketplace_group_aux.SupportedMarketplaces = [supported_marketplace_aux,]
    session.add(supported_marketplace_group_aux)

    supported_marketplace_group_aux = SupportedMarketplaceGroup(name='Mercado Libre', urlPhoto='groups/mercado_libre.png')
    supported_marketplace_aux = SupportedMarketplace(name='Mercado Libre', urlPhoto='marketplaces/mercado_libre.png')
    supported_marketplace_group_aux.SupportedMarketplaces = [supported_marketplace_aux,]
    session.add(supported_marketplace_group_aux)

    supported_marketplace_group_aux = SupportedMarketplaceGroup(name='Walmart', urlPhoto='groups/walmart.png')
    supported_marketplace_aux = SupportedMarketplace(name='Walmart', urlPhoto='marketplaces/walmart.png')
    supported_marketplace_group_aux.SupportedMarketplaces = [supported_marketplace_aux,]
    session.add(supported_marketplace_group_aux)

    supported_marketplace_group_aux = SupportedMarketplaceGroup(name='T1comercios', urlPhoto='groups/t1Comercios.png')
    supported_marketplace_claro = SupportedMarketplace(name='Claro Shop', urlPhoto='marketplaces/claro_shop.png')
    supported_marketplace_sears = SupportedMarketplace(name='Sears', urlPhoto='marketplaces/sears.png')
    supported_marketplace_sanborns = SupportedMarketplace(name='Sanborns', urlPhoto='marketplaces/sanborns.png')
    supported_marketplace_group_aux.SupportedMarketplaces = [supported_marketplace_claro, supported_marketplace_sears, supported_marketplace_sanborns]
    session.add(supported_marketplace_group_aux)


def insert_amount_changers(session=None):
    percent_amount_changer_type = AmountChangerType(name='percent', description='Utiliza un porcentaje del total previo')
    nominal_amount_changer_type = AmountChangerType(name='nominal', description='Utiliza una cantidad directa')
    utilidad_amount_changer_percent = AmountChanger(name='utilidad', operation='+')
    descuento_amount_changer_percent = AmountChanger(name='descuento', operation='-')
    IVA_amount_changer_percent = AmountChanger(name='IVA', operation='+', currentValue=16.00)
    utilidad_amount_changer_nominal = AmountChanger(name='utilidad', operation='+')
    descuento_amount_changer_nominal = AmountChanger(name='descuento', operation='-')
    charger_group_tax = ChangerGroup(name="Impuesto", description="Cuota obligatoria impuesta por el gobierno")
    charget_group_specific_each_sale = ChangerGroup(name="Propia de la venta", description="Cuota que es aplicable solo a la venta en curso")
    utilidad_amount_changer_percent.ChangerGroup = charget_group_specific_each_sale
    descuento_amount_changer_percent.ChangerGroup = charget_group_specific_each_sale
    IVA_amount_changer_percent.ChangerGroup = charger_group_tax
    utilidad_amount_changer_nominal.ChangerGroup = charget_group_specific_each_sale
    descuento_amount_changer_nominal.ChangerGroup = charget_group_specific_each_sale
    percent_amount_changer_type.AmountChangers = [utilidad_amount_changer_percent, descuento_amount_changer_percent, IVA_amount_changer_percent]
    nominal_amount_changer_type.AmountChangers = [utilidad_amount_changer_nominal, descuento_amount_changer_nominal]
    session.add(percent_amount_changer_type)
    session.add(nominal_amount_changer_type)


def insert_FulfillmentChannels(session=None):
    fulfillment_Channel_aux = FulfillmentChannel(
        fulfillmentChannel='Marketplace')
    session.add(fulfillment_Channel_aux)
    fulfillment_Channel_aux = FulfillmentChannel(fulfillmentChannel='Seller')
    session.add(fulfillment_Channel_aux)


def insert_OrderInternalStatus(session=None):
    order_internal_status_type_general = OrderInternalStatusType(
        orderInternalStatusType='Flujo general', orderNum=1)
    order_internal_status_type_general.OrderInternalStatus = [
        OrderInternalStatus(
            orderInternalStatus='No revisado',
            description=('En espera de que se revise'
                         ' y se documente un estado inicial'),
            orderNum=1)
    ]
    session.add(order_internal_status_type_general)

    order_internal_status_type_tipico = OrderInternalStatusType(
        orderInternalStatusType='Pedido típico', orderNum=2)
    order_internal_status_type_tipico.OrderInternalStatus = [
        OrderInternalStatus(
            orderInternalStatus='Pedido al proveedor',
            description='Cuando el producto ya fue solicitado al proveedor',
            orderNum=1),
        OrderInternalStatus(
            orderInternalStatus='En camino(proveedor)',
            description=('Cuando el producto viene de un Almacén'
                         ' de una Zona distinta a la 0'),
            orderNum=2),
        OrderInternalStatus(
            orderInternalStatus='Recibido parcialmente',
            description=('El Pedido aún no está completo,'
                         ' se recibió parcialmente de parte del proveedor'),
            orderNum=3),
        OrderInternalStatus(
            orderInternalStatus='Stock Atenea',
            description=('El producto fue localizado'
                         ' en el Stock del almacén Atenea'),
            orderNum=4),
        OrderInternalStatus(
            orderInternalStatus='Listo para empaquetar',
            description='Ya se registró la salida y esta en manos de Logística',
            orderNum=5),
        OrderInternalStatus(
            orderInternalStatus='Enviado al cliente',
            description=('El producto fue entregado a paquetería '
                         ' y está en tránsito al cliente'),
            orderNum=6),
        OrderInternalStatus(
            orderInternalStatus='Entregado al cliente',
            description='El producto ya fue recibido por el cliente',
            orderNum=7)
    ]
    session.add(order_internal_status_type_tipico)

    order_internal_status_type_atipico = OrderInternalStatusType(
        orderInternalStatusType='Pedido atípico', orderNum=3)
    order_internal_status_type_atipico.OrderInternalStatus = [
        OrderInternalStatus(
            orderInternalStatus='En espera... No surtir',
            description=('Existen problemas con el pedido,'
                         ' no surtir y consultar con el líder'),
            orderNum=1),
        OrderInternalStatus(
            orderInternalStatus='Finalizado: No concretado',
            description='La operación se canceló',
            orderNum=2)
    ]
    session.add(order_internal_status_type_atipico)


def insert_OrderStatus(session=None):
    order_status_flag_red = OrderStatusFlag(orderStatusFlag='Red')
    order_status_flag_red.OrderStatus = [
        OrderStatus(
            orderStatus='Pendiente de pago',
            description='Esperando que el pago sea reflejado en la cuenta'),
        OrderStatus(
            orderStatus='Cancelado no procesado',
            description='La operación se canceló antes de hacerse el envío'),
        OrderStatus(
            orderStatus='Error en el pedido',
            description='Existe algún error con el pedido, consultar al Líder'),
        OrderStatus(
            orderStatus='Inconsistencia de datos',
            description='Datos erróneos, Revisar operación'),
        OrderStatus(
            orderStatus='Reembolsado',
            description='Cancelado y Reembolsado al cliente'),
        OrderStatus(
            orderStatus='Devuelto',
            description='Devuelto por el cliente y ya llegó con nosotros'),
        OrderStatus(
            orderStatus='Rechazado', description='Pedido no aceptado'),
        OrderStatus(
            orderStatus='Contracargo',
            description='Operación no reconocida por el comprador')
    ]
    session.add(order_status_flag_red)

    order_status_flag_yellow = OrderStatusFlag(orderStatusFlag='Yellow')
    order_status_flag_yellow.OrderStatus = [
        OrderStatus(
            orderStatus='Enviado',
            description=('El producto fue entregado'
                         ' a paquetería y está en tránsito al cliente'))
    ]
    session.add(order_status_flag_yellow)

    order_status_flag_green = OrderStatusFlag(orderStatusFlag='Green')
    order_status_flag_green.OrderStatus = [
        OrderStatus(
            orderStatus='Entregado',
            description='El producto ya fue recibido por el cliente'),
        OrderStatus(
            orderStatus='Cancelado ya procesado',
            description=('La operación se canceló '
                         ' cuando el producto ya se había enviado')),
        OrderStatus(
            orderStatus='Entregado sin posiblidad de cambios',
            description='Producto que no permite cambios ni devoluciones')
    ]
    session.add(order_status_flag_green)
    order_status_flag_orange = OrderStatusFlag(orderStatusFlag='Orange')
    order_status_flag_orange.OrderStatus = [
        OrderStatus(
            orderStatus='Esperando stock',
            description='Esperando a que se tenga listo el producto'),
        OrderStatus(
            orderStatus='Pendiente de envío',
            description='En espera de ser enviado'),
        OrderStatus(
            orderStatus='En Devolución',
            description='En espera de la devolución por parte del comprador'),
        OrderStatus(
            orderStatus='Acordar con el comprador',
            description='En espera de ser enviado conforme al comprobador')
    ]
    session.add(order_status_flag_orange)

    order_status_flag_grey = OrderStatusFlag(orderStatusFlag='Grey')
    order_status_flag_grey.OrderStatus = [
        OrderStatus(
            orderStatus='Desconocido',
            description=('El estado del pedido no es conocido, consultar directamente en la plataforma')),
    ]
    session.add(order_status_flag_grey)


def insert_DirectSaleType(session=None):
    direct_sale_type_aux = DirectSaleType(
        directSaleType='externa',
        description=('Venta mediante contacto directo '
                     ' (Whatsapp, correo, telefono)'))
    session.add(direct_sale_type_aux)
    direct_sale_type_aux = DirectSaleType(
        directSaleType='interna',
        description=('Venta a un empleado de la empresa a precio especial'))
    session.add(direct_sale_type_aux)


def insert_DirectSaleInternalStatus(session=None):
    direct_sale_internal_status_aux = DirectSaleInternalStatus(
        directSaleInternalStatus='En cotización',
        description=('El cliente se pone en contacto '
                     ' con la tienda para solicitar una cotización'),
        orderNum=1)
    session.add(direct_sale_internal_status_aux)
    direct_sale_internal_status_aux = DirectSaleInternalStatus(
        directSaleInternalStatus='Cerrada(Pago verificado)',
        description='El pago ha sido realizado',
        orderNum=4)
    session.add(direct_sale_internal_status_aux)
    direct_sale_internal_status_aux = DirectSaleInternalStatus(
        directSaleInternalStatus='Pedido a mayorista',
        description='Se solicita la cantidad de producto al mayorista',
        orderNum=5)
    session.add(direct_sale_internal_status_aux)
    direct_sale_internal_status_aux = DirectSaleInternalStatus(
        directSaleInternalStatus='En camino (proveedor)',
        description=('El producto esta en camino'
                     ' de las instalaciones del proveedor a las nuestras'),
        orderNum=6)
    session.add(direct_sale_internal_status_aux)
    direct_sale_internal_status_aux = DirectSaleInternalStatus(
        directSaleInternalStatus='Generando factura para el cliente',
        description='La factura de compra para el cliente se esta realizando',
        orderNum=7)
    session.add(direct_sale_internal_status_aux)
    direct_sale_internal_status_aux = DirectSaleInternalStatus(
        directSaleInternalStatus='En camino a cliente',
        description='El producto esta en ruta al domicilio del cliente',
        orderNum=8)
    session.add(direct_sale_internal_status_aux)
    direct_sale_internal_status_aux = DirectSaleInternalStatus(
        directSaleInternalStatus='Finalizado',
        description='El producto se encuentra con el cliente',
        orderNum=9)
    session.add(direct_sale_internal_status_aux)


def insert_Roles(session=None):
    role_admin = Role(roleName='Admin')
    session.add(role_admin)
    role_warehouse_manager = Role(roleName='Warehouse_manager')
    session.add(role_warehouse_manager)
    role_logistics_employee = Role(roleName='Logistics_employee')
    session.add(role_logistics_employee)
    role_administrative_accountant = Role(roleName='Administrative_accountant')
    session.add(role_administrative_accountant)
    role_services = Role(roleName='Services')
    session.add(role_services)


def insert_Zones(session=None):
    zone_aux = Zone(zoneNumber=0, deliveryTime='Mismo Día')
    session.add(zone_aux)
    zone_aux = Zone(zoneNumber=1, deliveryTime='24 a 48 horas')
    session.add(zone_aux)
    zone_aux = Zone(zoneNumber=2, deliveryTime='48 a 72 horas')
    session.add(zone_aux)
    zone_aux = Zone(zoneNumber=3, deliveryTime='72 a 96 horas')
    session.add(zone_aux)
    zone_aux = Zone(zoneNumber=4, deliveryTime='Mas de 96 horas')
    session.add(zone_aux)
    zone_aux = Zone(zoneNumber=5, deliveryTime='Tiempo desconocido')
    session.add(zone_aux)


def insert_PublicationStatus(session=None):
    publication_status_aux = PublicationStatus(name="Activa")
    session.add(publication_status_aux)
    publication_status_aux = PublicationStatus(name="Inactiva")
    session.add(publication_status_aux)
    publication_status_aux = PublicationStatus(name="Pausada")
    session.add(publication_status_aux)


def insert_WarrantyTimeFrames(session=None):
    warranty_time_frame_aux = WarrantyTimeFrame(name="Día")
    session.add(warranty_time_frame_aux)
    warranty_time_frame_aux = WarrantyTimeFrame(name="Mes")
    session.add(warranty_time_frame_aux)
    warranty_time_frame_aux = WarrantyTimeFrame(name="Año")
    session.add(warranty_time_frame_aux)


def insert_initial_variations(session=None):
    variacion_condicion = CustomVariation('Condición')
    variacion_condicion.CustomVariationValues = [CustomVariationValue('nuevo'), CustomVariationValue('usado'), CustomVariationValue('reacondicionado')]
    session.add(variacion_condicion)


def insert_WarrantyTypes(session=None):
    garantia_del_vendedor = WarrantyType('Garantía del vendedor')
    session.add(garantia_del_vendedor)
    garantia_de_fabrica = WarrantyType('Garantía de fábrica')
    session.add(garantia_de_fabrica)


def insert_location_level_types(session=None):
    locationLevel_1 = LocationLevel("Lugar", "Ubicación particular dentro de un inmueble")
    locationLevel_2 = LocationLevel("Mueble", "Objetos que sirven para facilitar los usos y actividades habituales en la oficina")
    locationLevel_3 = LocationLevel("División", "Porción individual de un mueble")
    locationLevel_4 = LocationLevel("Subdivisión", "División de una división")
    locationLevelType_bodega = LocationLevelType("Bodega", "Almacén es un lugar o espacio físico para el almacenaje de bienes dentro de la cadena de suministro")  # 1
    locationLevelType_bodega.LocationLevel = locationLevel_1
    locationLevelType_oficina = LocationLevelType("Oficina", "Salón destinado al trabajo")  # 2
    locationLevelType_oficina.LocationLevel = locationLevel_1
    locationLevelType_azotea = LocationLevelType("Azotea", "Parte superior de un edificio cuando esta es plana")  # 3
    locationLevelType_azotea.LocationLevel = locationLevel_1
    locationLevelType_rack = LocationLevelType("Rack", "Soporte metálico destinado a guardar, almacenar y conservar las mercancías")  # 4
    locationLevelType_rack.LocationLevel = locationLevel_2
    locationLevelType_tarima = LocationLevelType("Tarima", "Soportes utilizados para la manipulación de los productos, generalmente de madera")  # 5
    locationLevelType_tarima.LocationLevel = locationLevel_2
    locationLevelType_estante = LocationLevelType("Estante", "Pieza plana (como de madera o metal) colocada paralela y por encima de un piso para sostener objetos")  # 6
    locationLevelType_estante.LocationLevel = locationLevel_3
    # locationLevelType_
    session.add(locationLevelType_bodega)
    session.add(locationLevelType_oficina)
    session.add(locationLevelType_azotea)
    session.add(locationLevelType_rack)
    session.add(locationLevelType_tarima)
    session.add(locationLevelType_estante)
    session.add(locationLevel_4)


# general initial data funtion
def insert_initial_data(session=None, name=None, email=None, password=None):
    insert_OrderStatus(session=session)
    insert_OrderInternalStatus(session=session)
    insert_FulfillmentChannels(session=session)
    insert_supported_marketplaces(session=session)
    insert_DirectSaleType(session=session)
    insert_DirectSaleInternalStatus(session=session)
    insert_Roles(session=session)
    insert_Zones(session=session)
    insert_amount_changers(session=session)
    insert_PublicationStatus(session=session)
    insert_WarrantyTimeFrames(session=session)
    insert_WarrantyTypes(session=session)
    insert_initial_variations(session=session)
    insert_location_level_types(session=session)
    insert_initial_users(session=session, name=name, email=email, password=password)
    session.commit()


def add_ubication_to_store(locationLevelType, store, locationName, parent):
    new_locationLevelItem = LocationLevelItem(locationName)  # 1
    new_locationLevelItem.Store = store
    new_locationLevelItem.LocationLevelType = locationLevelType
    new_locationLevelItem.LocationLevelItemParent = parent
    return new_locationLevelItem


def insert_location_level_items_atenea(session=None):
    atenea = session.query(Store).get(1)
    locationLevelType_bodega = session.query(LocationLevelType).get(1)
    locationLevelType_oficina = session.query(LocationLevelType).get(2)
    locationLevelType_azotea = session.query(LocationLevelType).get(3)
    locationLevelType_rack = session.query(LocationLevelType).get(4)
    locationLevelType_tarima = session.query(LocationLevelType).get(5)
    locationLevelType_estante = session.query(LocationLevelType).get(6)
    bodega = add_ubication_to_store(locationLevelType_bodega, atenea, "bodega", None)
    oficina = add_ubication_to_store(locationLevelType_oficina, atenea, "oficina", None)
    azotea = add_ubication_to_store(locationLevelType_azotea, atenea, "azotea", None)
    # Oficina
    start_upper_letters_ascci = 65
    num_of_items = 6
    for rack_num in range(1, 31):
        rack = add_ubication_to_store(locationLevelType_rack, atenea, str(rack_num), oficina)
        for estante_num in range(start_upper_letters_ascci, start_upper_letters_ascci + num_of_items):
            add_ubication_to_store(locationLevelType_estante, atenea, chr(estante_num), rack)
    for tam_num in range(1, 9):
        add_ubication_to_store(locationLevelType_tarima, atenea, str(tam_num), oficina)
    # Bodega
    add_ubication_to_store(locationLevelType_tarima, atenea, str(1), bodega)
    # Azotea
    add_ubication_to_store(locationLevelType_tarima, atenea, str(1), azotea)
    session.add(bodega)
    session.add(oficina)
    session.add(azotea)
    session.commit()


def insert_initial_stockAtenea(session=None, nameStore="Stock CP.xlsm"):
    load_stock_products_atenea(session, nameStore)


def insert_initial_users(session=None, name=None, email=None, password=None):
    role_admin = session.query(Role).filter(Role.RoleName == "Admin").first()
    role_services = session.query(Role).filter(Role.RoleName == "Services").first()
    user = User(
        public_id=str(uuid.uuid4()),
        name="Services Account",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "SqTs2594!"))
    user.Role = role_services
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name=name,
        email=email,
        password=generate_hashedsalt(password)
    )
    user.Role = role_admin
    session.add(user)
    session.commit()


# internal companies specific information
def insert_initial_users_internal_companies(session=None):
    role_admin, role_warehouse_manager, role_logistics_employee, role_administrative_accountant = getting_system_user_roles(session=session)
    user = User(
        public_id=str(uuid.uuid4()),
        name="Eric Montesinos",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "Moee0583!"))
    user.Role = role_admin
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name="Fabiola Velez",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "RE%&k8Wg"))
    user.Role = role_administrative_accountant
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name="Alberto Alvirde",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "hT3H9iM&"))
    user.Role = role_warehouse_manager
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name="Carlos Torres",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "BR3MaQ>)"))
    user.Role = role_logistics_employee
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name="Robert Hernandez",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "eC%2@LGR"))
    user.Role = role_logistics_employee
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name="Wendy Alvirde",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "4U*26wiw"))
    user.Role = role_administrative_accountant
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name="Skarlet Mendoza",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "dzdPGM{Z"))
    user.Role = role_administrative_accountant
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name="Emiliano Monroy",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "L3^fiTO{"))
    user.Role = role_administrative_accountant
    session.add(user)
    session.commit()


def getting_system_user_roles(session=None):
    role_admin = session.query(Role).filter(Role.RoleName == "Admin").first()
    role_warehouse_manager = session.query(Role).filter(Role.RoleName == "Warehouse_manager").first()
    role_logistics_employee = session.query(Role).filter(Role.RoleName == "Logistics_employee").first()
    role_administrative_accountant = session.query(Role).filter(Role.RoleName == "Administrative_accountant").first()
    return role_admin, role_warehouse_manager, role_logistics_employee, role_administrative_accountant


# Create user for test
def insert_initial_users_test(session=None):
    _, role_warehouse_manager, role_logistics_employee, role_administrative_accountant = getting_system_user_roles(session=session)
    user = User(
        public_id=str(uuid.uuid4()),
        name="administrative accountant test user",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "administrative_accountant_test_pass"))
    user.Role = role_administrative_accountant
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name="warehouse manager test user",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "warehouse_manager_test_pass"))
    user.Role = role_warehouse_manager
    session.add(user)
    user = User(
        public_id=str(uuid.uuid4()),
        name="logistics employee test user",
        email="<EMAIL>",
        password=generate_hashedsalt(
            "logistics_employee_test_pass"))
    user.Role = role_logistics_employee
    session.add(user)

    session.commit()


def insert_suppliers_info_internal_companies(session=None):
    # -----[Store]
    zone_0 = session.query(Zone).get(0)
    zone_1 = session.query(Zone).get(1)
    zone_2 = session.query(Zone).get(2)
    zone_3 = session.query(Zone).get(3)
    # zone_4 = session.query(Zone).get(4)
    zone_5 = session.query(Zone).get(5)
    store_aux = Store(
        storeName='Atenea',
        storeDescription='Almacen principal',
        address=('Atenea #26	Plaza de las rosas	'
                 ' Tlalnepantla De Baz	Estado De México 54069'),
        phone='5585261853', urlMaps='https://goo.gl/maps/8CYy4ieXPgZ18Cuy5')
    store_aux.Zone = zone_0
    session.add(store_aux)
    store_aux = Store(
        storeName='Atenea 2',
        storeDescription='ALMACEN FULL ML TEPOZ',
        address=('CARRETERA LIBRAMIENTO NORTE '
                 'ESQ. CAMINO ANTIGUO A QUERETARO 87, NAVE 2 TEPOTZOTLÁN	'
                 'TEPOTZOTLÁN	Estado De México 54607'),
        phone=None,
        urlMaps='https://www.google.com/maps?q=19.738483,-99.217793')
    store_aux.Zone = zone_0
    session.add(store_aux)
    # -----[Supplier]
    # -----Kramer
    supplier_dict_aux = {
        'RFC': 'KEL0504041W9',
        'supplierName': 'KRAMER',
        'email': '<EMAIL>',
        'salesExecutive': 'Jose Zacarias',

    }
    supplier_platform_dict_aux = {
        'url': 'https://www1.kramerav.com/'
    }
    proveedor_kramer = register_supplier(supplier_param=supplier_dict_aux,
                                         supplier_platform_param=supplier_platform_dict_aux,
                                         zone_param=zone_5, session=session)
    # -----CT
    supplier_dict_aux = {
        'RFC': 'CIN960904FQ2',
        'supplierName': 'CT',
        'email': '<EMAIL>',
        'salesExecutive': 'Eliezer Santos Martinez',
    }
    proveedor_ct = register_supplier(supplier_param=supplier_dict_aux,
                                     zone_param=zone_5, session=session)
    # -------------------------------DC
    supplier_dict_aux = {
        'RFC': 'DMA980313MW7',
        'supplierName': 'DC MAYORISTAS',
        'email': '<EMAIL>',
        'salesExecutive': 'Adriana Fernanda Rivera Rojas',
    }
    proveedor_dc_mayoristas = register_supplier(supplier_param=supplier_dict_aux,
                                                zone_param=zone_5, session=session)
    # -------------------------------EXEL
    supplier_dict_aux = {
        'RFC': 'ENO8910131AA',
        'supplierName': 'EXEL DEL NORTE',
        'email': '<EMAIL>',
        'salesExecutive': 'Luis Juarez Gonzalez',
    }
    proveedor_exel = register_supplier(supplier_param=supplier_dict_aux,
                                       zone_param=zone_5, session=session)
    # -------------------------------INgGRAM
    supplier_dict_aux = {
        'RFC': 'IMM9304016Z4',
        'supplierName': 'INGRAM',
    }
    proveedor_ingram = register_supplier(supplier_param=supplier_dict_aux,
                                         zone_param=zone_5, session=session)
    # -------------------------------INTCOMEX
    supplier_dict_aux = {
        'RFC': 'CEN980619FU4',
        'supplierName': 'INTCOMEX',
        'email': '<EMAIL>',
        'salesExecutive': 'Yazmín Calzada Espinoza',
    }
    proveedor_intcomex = register_supplier(supplier_param=supplier_dict_aux,
                                           zone_param=zone_5, session=session)
    # -------------------------------
    supplier_dict_aux = {
        'RFC': 'PMA14021043A',
        'supplierName': 'PCH MAYOREO',
        'email': '<EMAIL>',
        'salesExecutive': 'David Zamudio Sanchez',
    }
    proveedor_pch = register_supplier(supplier_param=supplier_dict_aux,
                                      zone_param=zone_5, session=session)
    # -------------------------------
    supplier_dict_aux = {
        'RFC': 'EST850628K51',
        'supplierName': 'STEREN',
        'email': '<EMAIL>',
        'salesExecutive': 'Adriana Macias',
    }
    supplier_platform_dict_aux = {
        'url': 'http://steren-intranet.com.mx',
        'user': '402730',
        'password': 'CP730'
    }
    proveedor_steren = register_supplier(supplier_param=supplier_dict_aux,
                                         supplier_platform_param=supplier_platform_dict_aux,
                                         zone_param=zone_5, session=session)
    # ---------------------------------------------------------------
    supplier_dict_aux = {
        'RFC': 'SSC840823JT3',
        'supplierName': 'SYSCOM',
        'email': '<EMAIL>',
        'salesExecutive': 'Ibis Hernandez',
    }
    supplier_platform_dict_aux = {
        'url': 'https://developers.syscom.mx/',
        'user': 'eric.montesinos',
        'password': 'TWCKY',
        'token': ('eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImp0aSI6ImQzM'
                  '2I5NTM5NGM4M2VlYzRlMjRhOTg4MzYyMjc0ZTQzNTlmMWVjZD'
                  'gzNWRlZjk1NmZiZjNlZjliMWQ0ZjkzMzg0MmUyZDA4MjkwMDc'
                  'yNzIzIn0.eyJhdWQiOiJOYmczR0pvaW1vVUE0aXdJcXpTZVpi'
                  'TGRuTjFzajN1ciIsImp0aSI6ImQzM2I5NTM5NGM4M2VlYzRlM'
                  'jRhOTg4MzYyMjc0ZTQzNTlmMWVjZDgzNWRlZjk1NmZiZjNlZj'
                  'liMWQ0ZjkzMzg0MmUyZDA4MjkwMDcyNzIzIiwiaWF0IjoxNjg'
                  '4MTM4NzMzLCJuYmYiOjE2ODgxMzg3MzMsImV4cCI6MTcxOTY3'
                  'NDczMiwic3ViIjoiIiwic2NvcGVzIjpbXX0.UxZHgSjyka7wB'
                  'qiEg6zQPi_RvAqB_lSP2YTaYVGnelZwMcP4-sZFga5zAYUz-L'
                  'JfoL1_p1fXQaqaqO0Ze0iVDi4ySShToo44fgnRvzwk-iZ4T5O'
                  'cyrfSPCVVN3uMlRALRBbNQJh-YRRL2q0-OnC6Q5T-uF6sJlwT'
                  'EsniIQy9QVuw3lo4Cfh6d3MobaCDZckIaqBZp2P2WH1zHW6_i'
                  'CXDYxo4l2VhXyTAPNRSY5l3QTZeaWUba9KAXIhXnEhr9Yrqpm'
                  'XCv3iBj4G7VN9O1Z1-0opZZoLGECEFC29Js8B2UvCa2gyCa2c'
                  'LhY73-kSxmgDrY33VSQiHaVvAPsm8QhnlwmWt_RZpNt8Ay9Kj'
                  'lAd4hDyWnoHk_7iBJPiBkxYyUMiEnc97Fbqn7kmr1dVYNhO5U'
                  'B9nqeTtIQo6z_5VdYwO5_LMfTbtuZNIWj7Z8Y7fjp0qnHm0Ti'
                  'GJzmZUA8Wy8JxCiBO-mdCGdgwqH810nlYhzqtJumm5j5qdnyK'
                  'hyww_Xqz-n1N0NY41gV4XGn9AVvRjI5I9AZlEd7bdtOTINA4u'
                  'F5EXnFU7YnxekTe5OOP7XcE-t0bmJ1L4Au88wxXOBXUAmYqdP'
                  'f8TnbnvbgwBHMkzcQSwsqCc5xhWymuOlPR8wCkcTPhSBPxYsU'
                  'DeLSejz46F3Fby_0T15nVx7f4gDqo3WAY')
    }
    proveedor_syscom = register_supplier(supplier_param=supplier_dict_aux,
                                         supplier_platform_param=supplier_platform_dict_aux,
                                         zone_param=zone_5, session=session)
    # --------------------------------------
    supplier_dict_aux = {
        'RFC': 'TEC060605PM1',
        'supplierName': 'TECNOSINERGIA',
        'email': '<EMAIL>',
        'salesExecutive': 'Rodrigo Adrian Rubio Rodriguez',
    }
    supplier_platform_dict_aux = {
        'url': 'https://api.tecnosinergia.info/v3',
        'user': '',
        'password': 'Cre@tive#1',
        'token': '$2y$10$yXJqLq49eFpUm7iNjb2PXe4CIQaSe4IwG1ehPhfS0zEX86cyhr0me'
    }
    proveedor_tecnosinergia = register_supplier(supplier_param=supplier_dict_aux,
                                                supplier_platform_param=supplier_platform_dict_aux,
                                                zone_param=zone_5, session=session)
    # ----------------------------------
    supplier_dict_aux = {
        'RFC': 'TVC060802NE4',
        'supplierName': 'TVC',
        'email': '<EMAIL>',
        'salesExecutive': 'Ruben Guzman',
    }
    proveedor_tvc = register_supplier(supplier_param=supplier_dict_aux,
                                      zone_param=zone_5, session=session)
    # -------------------------------
    supplier_dict_aux = {
        'RFC': 'RAU870128TH3',
        'supplierName': 'Representaciones de Audio',
        'email': '<EMAIL>',
        'salesExecutive': 'Marco Hernandez',
    }
    supplier_platform_dict_aux = {
        'url': 'https://dealers.rda.com.mx/',
        'user': '<EMAIL>',
        'password': '0!mejS$Lxw2C',
    }
    proveedor_representaciones = register_supplier(supplier_param=supplier_dict_aux,
                                                   supplier_platform_param=supplier_platform_dict_aux,
                                                   zone_param=zone_5, session=session)
    register_supplier_store(
        storeName='México Norte',
        supplierAppId='México Norte',
        address=('Calle Lauro Villar 120 '
                 ' Col. Providencia, Deleg. Azcapotzalco, '
                 ' Cd. de México. C.P. 02440'),
        phone='(55) 5541-4999',
        urlMaps='https://goo.gl/maps/EtFsZTKkm4NkS7xW9',
        zone=zone_0, proveedor=proveedor_syscom, session=session)
    register_supplier_store(
        storeName='México Sur',
        supplierAppId='México Sur',
        address=('Eje Vial 6 Sur Manzana 4 '
                 ' Lotes C.D.E Col. Ejido del Moral, '
                 ' Delegación Iztapalapa, Cd. de México. C.P 09040'),
        phone='(55) 5640-2330 Ext. 2201',
        urlMaps='https://goo.gl/maps/EL4y4MMtxbxdTdeX6',
        zone=zone_1, proveedor=proveedor_syscom, session=session)
    register_supplier_store(
        storeName='Tepotzotlan',
        supplierAppId='Tepotzotlan',
        address=('06 de Enero, No. 04, Nave 16 (Las Ánimas), '
                 ' Barrio Las Ánimas Tepotzotlán, Estado de México, '
                 ' Méx. C.P. 54616'),
        phone='(55) 9245-4480',
        urlMaps='https://goo.gl/maps/RPYW8naYs5g7Rs7h6',
        zone=zone_1, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Guadalajara',
                            supplierAppId='Guadalajara',
                            zone=zone_2, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Puebla', supplierAppId='Puebla',
                            zone=zone_2, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Querétaro', supplierAppId='Querétaro',
                            zone=zone_2, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Toluca', supplierAppId='Toluca',
                            zone=zone_2, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Veracruz', supplierAppId='Veracruz',
                            zone=zone_2, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='León', supplierAppId='León',
                            zone=zone_2, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Cd. Juarez', supplierAppId='Cd. Juarez',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Chihuahua', supplierAppId='Chihuahua',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Chihuahua CEDIS',
                            supplierAppId='Chihuahua CEDIS',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Hermosillo', supplierAppId='Hermosillo',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Los mochis', supplierAppId='Los mochis',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Mérida', supplierAppId='Mérida',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Monterrey', supplierAppId='Monterrey',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='San Luis Potosí',
                            supplierAppId='San Luis Potosí',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Tijuana', supplierAppId='Tijuana',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Torreón', supplierAppId='Torreón',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Villahermosa',
                            supplierAppId='Villahermosa',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    register_supplier_store(storeName='Culiacán', supplierAppId='Culiacán',
                            zone=zone_3, proveedor=proveedor_syscom, session=session)
    # --CT CIN960904FQ2=============================
    register_supplier_store(
        storeName='CDMX Suc. Azcapotzalco',
        address='AV. Acalotenco No.94 Col. San Sebastian C.P. 02040',
        phone='(55)556-16110',
        urlMaps='https://goo.gl/maps/NYpQS6VdVStwoyv27',
        zone=zone_0, proveedor=proveedor_ct, session=session)
    register_supplier_store(
        storeName='Tlalneplantla',
        address=('Antonio M. Rivera #26 Bodega 8Entre Filiberto Gomez '
                 ' y Rio Lerma. Col. Industrial, CP 54030'),
        phone='(55) 556-52210',
        urlMaps='https://goo.gl/maps/kfFfPFfBVwBvpnKR8',
        zone=zone_0, proveedor=proveedor_ct, session=session)
    register_supplier_store(
        storeName='CDMX Suc. Acoxpa',
        address=('Calzada Acoxpa #895 Entre Canal de Miramontes '
                 ' y Anillo Periferico Residencial Villa Acoxpa '
                 ' Delegación Tlalpan CP 14390'),
        phone='(55) 5603-7748',
        urlMaps='https://goo.gl/maps/BeTLje9iRWvbyyj66',
        zone=zone_1, proveedor=proveedor_ct, session=session)
    register_supplier_store(
        storeName='CDMX suc. Palacio',
        address=('Centeno N.833, entre Resina y Río Churubusco, '
                 ' Col. Granjas, Ciudad de México, '
                 ' Delegación Iztacalco C.P. 08400'),
        phone='(55) 5649-8918',
        urlMaps='https://goo.gl/maps/SiL9FujaNSdDFuRL6',
        zone=zone_1, proveedor=proveedor_ct, session=session)
    register_supplier_store(
        storeName='Coacalco',
        address=('Av. Zarzaparrillas N. 2, '
                 ' esq. Vía José López Portillo Col Villa de las Flores, '
                 ' CP 55710'),
        phone='(55) 1542-0000',
        urlMaps='https://goo.gl/maps/mCrPyG88WpRKmAhVA',
        zone=zone_1, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Acapulco',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Celaya', zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Coatzacoalcos',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Cuernavaca',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Guadalajara',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Irapuato',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='León', zone=zone_2,
                            proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Pachuca',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Puebla',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Querétaro',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Tlaxcala',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Toluca',
                            zone=zone_2, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Aguascalientes',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Campeche',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Cancún',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Centro de Distribución Hermosillo',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Chetumal',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Chihuhua',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Ciudad Juarez',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Ciudad Obregón',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Ciudad Victoria',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Colima',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Culiacán',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Durango',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Ensenada',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Hermosillo',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='La paz',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Los Mochis',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Mazatlán',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Mérida',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Mexicali',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Monterrey',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Morelia',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Oaxaca',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Saltillo',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='San Luis Potosí',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Tampico',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Tapachula',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Tepic',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Tijuana',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Torreón',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Tuxtla Gutiérrez',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Uruapan',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Veracruz',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Villahermosa',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Xalapa',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    register_supplier_store(storeName='Zacatecas',
                            zone=zone_3, proveedor=proveedor_ct, session=session)
    # --DC MAYORISTAS DMA980313MW7=============================
    register_supplier_store(
        storeName='Cedis Ceylan',
        address=('Av. Jesús Reyes Heroles #69, '
                 ' col. San Juan Ixtacala, Tlalnepantla, '
                 ' Estado de México, CP 54160'),
        phone='(55) 5262-5700',
        urlMaps='https://goo.gl/maps/61rfnr5cwNERSNzL7',
        zone=zone_0, proveedor=proveedor_dc_mayoristas, session=session)
    register_supplier_store(
        storeName='Cedis Anahuac',
        address=('Laguna de Mayrán #300, col. Anáhuac, '
                 ' Miguel Hidalgo, México CDMX, CP 11320'),
        phone='(55) 5262-6800',
        urlMaps='https://goo.gl/maps/NpS9sXh8MCKnGot16',
        zone=zone_1, proveedor=proveedor_dc_mayoristas, session=session)
    # --EXEL DEL NORTE ENO8910131AA=============================
    register_supplier_store(
        storeName='México',
        address=('Poniente 134 No.580 Letra B, entre Norte 35 y 45 '
                 ' Col. Industrial Vallejo Delegación Azcapotzalco México, '
                 ' D.F, C.P. 02300'),
        phone='(55) 5001-0050',
        urlMaps='https://goo.gl/maps/HyYJ7b4P2cwFNzYH7',
        zone=zone_0, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Guadalajara',
                            zone=zone_2, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='León', zone=zone_2,
                            proveedor=proveedor_exel, session=session)
    register_supplier_store(
        storeName='Puebla', zone=zone_2, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Veracruz',
                            zone=zone_2, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Querétaro',
                            zone=zone_2, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Oficinas generales',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Monterrey',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Chihuahua',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='CD Juarez',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Culiacan',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Saltillo',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Mérida',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Torreón',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Tijuana',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Tampico',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    register_supplier_store(storeName='Hermosillo',
                            zone=zone_3, proveedor=proveedor_exel, session=session)
    # --INGRAM IMM9304016Z4=============================
    register_supplier_store(
        storeName='México DF',
        address=('Av. 16 de Septiembre 225, '
                 ' Sta Ines, Azcapotzalco, 02140 Ciudad de México, CDMX'),
        phone='(55) 5318-9900',
        urlMaps='https://goo.gl/maps/9fWwZtVfuPaEoMLv6',
        zone=zone_0, proveedor=proveedor_ingram, session=session)
    register_supplier_store(storeName='León', zone=zone_2,
                            proveedor=proveedor_ingram, session=session)
    register_supplier_store(storeName='Querétaro',
                            zone=zone_2, proveedor=proveedor_ingram, session=session)
    register_supplier_store(storeName='Guadalajara',
                            zone=zone_2, proveedor=proveedor_ingram, session=session)
    register_supplier_store(storeName='Mérida', zone=zone_3,
                            proveedor=proveedor_ingram, session=session)
    register_supplier_store(storeName='Monterrey',
                            zone=zone_3, proveedor=proveedor_ingram, session=session)
    register_supplier_store(storeName='Tijuana',
                            zone=zone_3, proveedor=proveedor_ingram, session=session)
    # --INTCOMEX CEN980619FU4=============================
    register_supplier_store(
        storeName='CDMX/Vallejo',
        address=('Nte 35 1000, Industrial Vallejo,'
                 ' Gustavo A. Madero, 02300 Ciudad de México, CDMX'),
        phone='(55) 5278-9901',
        urlMaps='https://goo.gl/maps/ADTE8Q6XQpKxzdoj6',
        zone=zone_0, proveedor=proveedor_intcomex, session=session)
    register_supplier_store(
        storeName='Tacubaya',
        address=('Av Progreso 172, Escandón I Secc,'
                 ' Miguel Hidalgo, 11800 Ciudad de México, CDMX'),
        phone='(55) 5278-9901',
        urlMaps='https://g.page/INTCOMEX-ESCANDON?share',
        zone=zone_1, proveedor=proveedor_intcomex, session=session)
    register_supplier_store(storeName='Queretaro',
                            zone=zone_2, proveedor=proveedor_intcomex, session=session)
    register_supplier_store(storeName='Puebla', zone=zone_2,
                            proveedor=proveedor_intcomex, session=session)
    register_supplier_store(storeName='Cancún', zone=zone_3,
                            proveedor=proveedor_intcomex, session=session)
    # --PCH MAYOREO PMA14021043A=============================
    register_supplier_store(
        storeName='CDMX Aereopuerto',
        address=('Iztaccihuatl # 322 Col. '
                 ' Santa Cruz Aviación Delegación Venustiano Carranza '
                 ' C.P. 15540 México, Ciudad de México'),
        phone='(55) 2456-1414',
        urlMaps='https://goo.gl/maps/qFmWfiqwQpjJWnFn9',
        zone=zone_1, proveedor=proveedor_pch, session=session)
    register_supplier_store(
        storeName='CDMX Plaza de la Tecnología',
        address=('República de Uruguay # 13 4o. Piso, '
                 ' local 46 Col. Centro C.P. 06000 México, '
                 ' Ciudad de México.'),
        phone='(55) 2456-2458',
        urlMaps='https://goo.gl/maps/QmsckEDUt46z9Q997',
        zone=zone_1, proveedor=proveedor_pch, session=session)
    register_supplier_store(storeName='León',
                            zone=zone_2, proveedor=proveedor_pch, session=session)
    register_supplier_store(storeName='Puebla',
                            zone=zone_2, proveedor=proveedor_pch, session=session)
    register_supplier_store(storeName='Guadalajara',
                            zone=zone_3, proveedor=proveedor_pch, session=session)
    register_supplier_store(storeName='Monterrey',
                            zone=zone_3, proveedor=proveedor_pch, session=session)
    register_supplier_store(storeName='Mérida',
                            zone=zone_3, proveedor=proveedor_pch, session=session)
    register_supplier_store(storeName='Chihuahua',
                            zone=zone_3, proveedor=proveedor_pch, session=session)
    # --STEREN EST850628K51=============================
    register_supplier_store(
        storeName='CDMX',
        address=('Biólogo Maximino Martínez '
                 ' No. 3408 Ciudad de México,CDMX México'),
        phone='(55) 5354-2200',
        urlMaps='https://goo.gl/maps/zLUyCQXLYNygM5n97',
        zone=zone_1, proveedor=proveedor_steren, session=session)
    register_supplier_store(
        storeName='Cuautitlán',
        address='Solo envios, no recolección', phone='(55) 5354-2200',
        urlMaps='https://goo.gl/maps/zLUyCQXLYNygM5n97',
        zone=zone_1, proveedor=proveedor_steren, session=session)
    # --TECNOSINERGIA TEC060605PM1=============================
    register_supplier_store(
        storeName='CDMX', supplierAppId='stock_CDMX',
        address=('Santo Domingo #220, '
                 ' Col. Industrial San Antonio, '
                 ' Azcapotzalco, CDMX, CP 02760'),
        phone='(55) 1204-8000',
        urlMaps='https://g.page/TecnosinergiaCDMX?share',
        zone=zone_0, proveedor=proveedor_tecnosinergia, session=session)
    register_supplier_store(storeName='Querétaro', supplierAppId='stock_QRO',
                            zone=zone_2, proveedor=proveedor_tecnosinergia, session=session)
    register_supplier_store(storeName='Puebla', supplierAppId='stock_PUE',
                            zone=zone_2, proveedor=proveedor_tecnosinergia, session=session)
    register_supplier_store(storeName='Guadalajara', supplierAppId='stock_GDL',
                            zone=zone_2, proveedor=proveedor_tecnosinergia, session=session)
    register_supplier_store(storeName='Veracruz', supplierAppId='stock_VER',
                            zone=zone_2, proveedor=proveedor_tecnosinergia, session=session)
    register_supplier_store(storeName='Monterrey', supplierAppId='stock_MTY',
                            zone=zone_3, proveedor=proveedor_tecnosinergia, session=session)
    register_supplier_store(storeName='Mérida', supplierAppId='stock_MER',
                            zone=zone_3, proveedor=proveedor_tecnosinergia, session=session)
    # --TVC TVC060802NE4=============================
    register_supplier_store(
        storeName='Azcapotzalco',
        address=('C. Lauro Villar 176, Providencia,'
                 ' Azcapotzalco, 02440 Ciudad de México, CDMX'),
        phone='(55) 6266-4000',
        urlMaps='https://goo.gl/maps/3rNWdYyd3kQfrc3s6',
        zone=zone_0, proveedor=proveedor_tvc, session=session)
    register_supplier_store(
        storeName='Iztacalco',
        address=('Cafetal 445, Granjas México, '
                 ' Iztacalco, 08400 Ciudad de México, CDMX'),
        phone='(55) 3626-1777',
        urlMaps='https://goo.gl/maps/9RBrahWW7EcRDgaX8',
        zone=zone_1, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Guadalajara',
                            zone=zone_2, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Zapopan',
                            zone=zone_2, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Puebla',
                            zone=zone_2, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Querétaro',
                            zone=zone_2, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Toluca',
                            zone=zone_2, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Veracruz',
                            zone=zone_2, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Monterrey Sur',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='San Nicolás',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Cancún',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Cd. Juárez',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Chihuahua',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Culiacán',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Hermosillo',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Mérida',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Morelia',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Tijuana',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Torreón',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    register_supplier_store(storeName='Villahermosa',
                            zone=zone_3, proveedor=proveedor_tvc, session=session)
    # --KRAMER KEL0504041W9=============================
    register_supplier_store(
        storeName='Insurgentes Sur',
        address=('Cda. Perpetua 22, San José Insurgentes, '
                 ' Benito Juárez, 03900 Ciudad de México, CDMX'),
        phone='(55) 5523-0604', zone=zone_1,
        proveedor=proveedor_kramer, session=session)
    # --Representaciones de Audio RAU870128TH3=============================
    register_supplier_store(
        storeName='Iztapalapa', supplierAppId='Iztapalapa',
        address=('Cal. 10 No 133, C. Granjas San Antonio, '
                 ' Iztapalapa, C.P. 09070'),
        phone='5533004550', zone=zone_1,
        proveedor=proveedor_representaciones, session=session)
    session.commit()


# specific info for each internal company instance
def agregar_marketplaces_cp(session=None):
    supportedMarketplace_amazon = session.query(SupportedMarketplace).filter(SupportedMarketplace.Name == "Amazon").first()
    marketplaceGroup_credentials_aux = MarketplaceGroupCredentials(
        clientId=('amzn1.application-oa2-client.01e23e0da6ca4bafa380e8cd65505575'),
        clientSecret=('amzn1.oa2-cs.v1.3ce8d5dcbdc68245e61890d34b81798166aa05e0a6560128bbca461e0f010ba1'),
        refreshToken=('Atzr|IwEBIFHj156yKaD65Avd6Ie3Zo6dfNvBlI0pdKFq05ZKZsWi4Z8U8'
                      'csw3y5RN0ssELsy1ITXLSnFULLkfU3XsaBMfeFFd3HXlMqPJbvORFTViIJm'
                      'If9UABPNtdAYyitIFGErHY_1TwiY6Fe7rF8r_nUllXV5yCoc3i0iCUi3BIQZ'
                      'fUYBwzvbZm67Z54Q_qAJ71IFUZfSxdfu3eg1O0wmWeTwWRouKRE-pIeQ4yQi'
                      'A30Bp-wANF__WK4EtBTqk28ictN-q1-d59UVW9mcEs7VVANFD4Ahr2gCFXqVI'
                      'axRPd7iO5msqZZ_xiRvaI8BxF7yE2KuPPmzdQDfbr5Y_yIxuKdotCP6'),
        accessToken=None)
    marketplaceGroup_aux = MarketplaceGroup()
    marketplaceGroup_aux.MarketplaceGroupCredentials = marketplaceGroup_credentials_aux
    marketplaceGroup_aux.SupportedMarketplaceGroup = supportedMarketplace_amazon.SupportedMarketplaceGroup
    marketplace_aux = Marketplace()
    marketplace_aux.MarketplaceGroup = marketplaceGroup_aux
    supportedMarketplace_amazon.Marketplace = marketplace_aux

    marketplaceGroup_credentials_aux = MarketplaceGroupCredentials(
        clientId='7403ae554efffd2bab103ff16c7a475f',
        clientSecret='69840344175456528a563af64489b65c',
        refreshToken=None, accessToken=None)
    marketplaceGroup_aux = MarketplaceGroup()
    marketplaceGroup_aux.MarketplaceGroupCredentials = marketplaceGroup_credentials_aux
    supportedMarketplace_claro_shop = session.query(SupportedMarketplace).filter(SupportedMarketplace.Name == "Claro Shop").first()
    supportedMarketplace_sears = session.query(SupportedMarketplace).filter(SupportedMarketplace.Name == "Sears").first()
    supportedMarketplace_sanborns = session.query(SupportedMarketplace).filter(SupportedMarketplace.Name == "Sanborns").first()
    supportedMarketplaceGroup_t1 = session.query(SupportedMarketplaceGroup).filter(SupportedMarketplaceGroup.Name == "T1comercios").first()
    marketplaceGroup_aux.SupportedMarketplaceGroup = supportedMarketplaceGroup_t1
    marketplace_aux = Marketplace()
    marketplace_aux.MarketplaceGroup = marketplaceGroup_aux
    supportedMarketplace_claro_shop.Marketplace = marketplace_aux
    marketplace_aux = Marketplace()
    marketplace_aux.MarketplaceGroup = marketplaceGroup_aux
    supportedMarketplace_sears.Marketplace = marketplace_aux
    marketplace_aux = Marketplace()
    marketplace_aux.MarketplaceGroup = marketplaceGroup_aux
    supportedMarketplace_sanborns.Marketplace = marketplace_aux

    supportedMarketplace_mercado_libre = session.query(SupportedMarketplace).filter(SupportedMarketplace.Name == "Mercado Libre").first()
    marketplaceGroup_credentials_aux = MarketplaceGroupCredentials(
        clientId='7033618764711339',
        clientSecret='JBevsEjvVTsMmqrOP6mPCzNEEqQUG1AJ',
        refreshToken='TG-679903beb15aeb0001f75ba6-32435319',
        accessToken=None)
    marketplaceGroup_aux = MarketplaceGroup()
    marketplaceGroup_aux.MarketplaceGroupCredentials = marketplaceGroup_credentials_aux
    marketplaceGroup_aux.SupportedMarketplaceGroup = supportedMarketplace_mercado_libre.SupportedMarketplaceGroup
    marketplace_aux = Marketplace()
    marketplace_aux.MarketplaceGroup = marketplaceGroup_aux
    supportedMarketplace_mercado_libre.Marketplace = marketplace_aux

    supportedMarketplace_walmart = session.query(SupportedMarketplace).filter(SupportedMarketplace.Name == "Walmart").first()
    marketplaceGroup_credentials_aux = MarketplaceGroupCredentials(
        clientId='b185e12b-970c-4e09-81bf-4967c62f36bc',
        clientSecret=('d6oQa2vbwugfZL3FbeJI8IhJizDHZQMnCNd_RykoV_n7O8wl5z1MnvCwG-NAfXtoRMHSnK_ZXAdwNU3Df6t3bA'),
        refreshToken=None,
        accessToken=None)
    marketplaceGroup_aux = MarketplaceGroup()
    marketplaceGroup_aux.MarketplaceGroupCredentials = marketplaceGroup_credentials_aux
    marketplaceGroup_aux.SupportedMarketplaceGroup = supportedMarketplace_walmart.SupportedMarketplaceGroup
    marketplace_aux = Marketplace()
    marketplace_aux.MarketplaceGroup = marketplaceGroup_aux
    supportedMarketplace_walmart.Marketplace = marketplace_aux

    session.commit()


def agregar_marketplaces_lmo(session=None):
    supportedMarketplace_mercado_libre = session.query(SupportedMarketplace).filter(SupportedMarketplace.Name == "Mercado Libre").first()
    marketplaceGroup_credentials_aux = MarketplaceGroupCredentials(
        clientId='7033618764711339',
        clientSecret='JBevsEjvVTsMmqrOP6mPCzNEEqQUG1AJ',
        refreshToken='TG-679903beb15aeb0001f75ba6-32435319',
        accessToken=None)
    marketplaceGroup_aux = MarketplaceGroup()
    marketplaceGroup_aux.MarketplaceGroupCredentials = marketplaceGroup_credentials_aux
    marketplaceGroup_aux.SupportedMarketplaceGroup = supportedMarketplace_mercado_libre.SupportedMarketplaceGroup
    marketplace_aux = Marketplace()
    marketplace_aux.MarketplaceGroup = marketplaceGroup_aux
    supportedMarketplace_mercado_libre.Marketplace = marketplace_aux
    session.commit()


def insert_test_data(session=None):
    try:
        # Datos de prueba-----------------------
        #   Variation size(Tamaño)
        variacion_tamanio = CustomVariation('Tamaño')
        #       Variation values
        variacion_tamanio.CustomVariationValues = [CustomVariationValue('Grande'), CustomVariationValue('Mediano'), CustomVariationValue('Chico')]
        session.add(variacion_tamanio)
        #   Variation color(Color)
        variacion_color = CustomVariation('Color')
        #       Variation values
        verde = CustomVariationValue('Verde')
        rojo = CustomVariationValue('Rojo')
        variacion_color.CustomVariationValues = [verde, rojo, CustomVariationValue('Azul')]
        #   +++
        #   ProductBase
        internal_base_sku = generate_internal_base_sku(brand="brand", model="model")
        product_base = ProductBase(internalBaseSku=internal_base_sku, brand="brand", model="model", description="description")
        #       Relating custom variation with productbase
        productBase_CustomVariation = ProductBase_CustomVariation()
        productBase_CustomVariation.CustomVariation = variacion_color
        product_base.ProductBase_CustomVariations = [productBase_CustomVariation,]
        #   products
        #       Product 1
        product_1 = Product()
        variaciones_p1 = {"1": "5"}
        product_1.InternalSku = generate_internal_sku(internal_base_sku, variaciones_p1)
        product_1.ProductBase = product_base
        #           Relating product with variation and variation values
        productVariationCustomV = ProductVariationCustom()
        productVariationCustomV.CustomVariationValue = verde
        productVariationCustomV.ProductBase_CustomVariation = productBase_CustomVariation
        product_1.ProductVariationCustom = [productVariationCustomV,]
        #    Product 2
        product_2 = Product()
        variaciones_p2 = {"1": "4"}
        product_2.InternalSku = generate_internal_sku(internal_base_sku, variaciones_p2)
        product_2.ProductBase = product_base
        #           Relating product with variation and variation values
        productVariationCustomR = ProductVariationCustom()
        productVariationCustomR.CustomVariationValue = rojo
        productVariationCustomR.ProductBase_CustomVariation = productBase_CustomVariation
        product_2.ProductVariationCustom = [productVariationCustomR,]
        session.add(product_base)
        #   Kit
        kit = Kit(title="kit verde y rojo", descripcion="Paquete de productos verde y rojo")
        #       Adding products to kit
        product_kit_1 = Kit_Product(amount=3)
        product_kit_1.Product = product_1
        product_kit_2 = Kit_Product(amount=7)
        product_kit_2.Product = product_2
        kit.Kit_Products = [product_kit_1, product_kit_2]
        session.add(kit)
        #   Invoice
        #       Invoice
        invoice = EntryDocument(uuid="uuid", internalId="internalId", serie="serie", csdSerie="csdSerie", saleOrder=None, purchaseOrder=None, receiverRfc="receiverRfc", issuePlace="issuePlace", issueDate=get_time_zone_now(), certificationDate=get_time_zone_now(), expirationDate=get_time_zone_now(), branchOffice=None, paymentConditions=None, paymentMethod="paymentMethod", paymentForm="paymentMethod", currency="paymentMethod", cfdiUsage="cfdiUsage", salesRep=None, issuerRfc="issuerRfc")
        #       Products in invoice: setting products to invoice
        product_in_invoice_1 = ProductInEntryDocument(model="model", supplierSku="supplierSku", invoiceItemNumber=1, upc="upc", satKey="satKey", units=4, discountRate=15, unitPrice=100, description="description", unitKey="UKT", conceptAmount=400, enteredAmount=3)  # predialNum=None,
        product_in_invoice_2 = ProductInEntryDocument(model="model", supplierSku="supplierSku", invoiceItemNumber=2, upc="upc", satKey="satKey", units=2, discountRate=10, unitPrice=150, description="description", unitKey="UKT", conceptAmount=300, enteredAmount=2)  # predialNum=None,
        invoice.ProductsInInvoice = [product_in_invoice_1, product_in_invoice_2]
        #   Relating product with invoice items
        product_in_invoice_1.ProductBase = product_base
        product_in_invoice_2.ProductBase = product_base
        session.add(invoice)
        #   adding stock from products in invoice
        #       Creating stock at store Atenea
        atenea = session.query(Store).get(1)
        product_store_1_1 = Product_Store(stock=10, cost=100)
        product_store_1_1.Store = atenea
        product_store_1_1.Product = product_1
        product_store_1_2 = Product_Store(stock=25, cost=150)
        product_store_1_2.Store = atenea
        product_store_1_2.Product = product_2
        #   DirectSale
        #       client
        client_1 = Client(marketplaceClientId=None, registrationDate=None, city=None, state=None, nickname="nickname Prueba", name="name Prueba", score=None, zipCode=None, phoneNumber=None, email=None, address=None, orders=[])
        #       DirectSaleInternalStatus quote (cotizacion)
        directSaleInternalStatus_cotizacion = session.query(DirectSaleInternalStatus).filter(DirectSaleInternalStatus.DirectSaleInternalStatus == 'En cotización').first()
        #       DirectSaleType external (externa)
        direct_sale_type_externa = session.query(DirectSaleType).filter(DirectSaleType.DirectSaleType == 'externa').first()
        #       Seller
        user_eric = session.query(User).filter(User.Email == "<EMAIL>").first()
        #       Amount_changers
        amountChangers = session.query(AmountChanger).all()
        amountChangerIVA = return_amount_changer(amountChangers, 'IVA', 'percent')
        amount_changer_utility_percent = return_amount_changer(amountChangers, "utilidad", "percent")
        amount_changer_discount_percent = return_amount_changer(amountChangers, "descuento", "percent")
        iva = ProductInDirectSale_AmountChanger(amountChangerIVA.CurrentValue)
        iva.AmountChanger = amountChangerIVA
        utility_10 = ProductInDirectSale_AmountChanger(10)
        utility_10.AmountChanger = amount_changer_utility_percent
        discount_5 = ProductInDirectSale_AmountChanger(2)
        discount_5.AmountChanger = amount_changer_discount_percent
        utility_15 = ProductInDirectSale_AmountChanger(15)
        utility_15.AmountChanger = amount_changer_utility_percent
        discount_4 = ProductInDirectSale_AmountChanger(4)
        discount_4.AmountChanger = amount_changer_discount_percent

        #       DirectSale
        direct_sale = DirectSale()
        direct_sale.Seller = user_eric
        direct_sale.DirectSaleInternalStatus = directSaleInternalStatus_cotizacion
        direct_sale.DirectSaleType = direct_sale_type_externa
        direct_sale.Client = client_1
        direct_sale.Shipping = 300
        direct_sale.SaleDate = get_time_zone_now()
        #           Amount_changers
        total_discount = DirectSale_AmountChanger(1)
        total_discount.AmountChanger = amount_changer_discount_percent
        direct_sale.DirectSale_AmountChanger = [total_discount,]
        #       Products in DirectSale
        #           Amount_changers
        product_in_direct_sale_1 = ProductInDirectSale(units=5, costAtTimeOfSale=100)
        product_in_direct_sale_1.Product_Store = product_store_1_1
        product_in_direct_sale_1.ProductInDirectSale_AmountChanger = [iva, utility_10, discount_5]

        product_in_direct_sale_2 = ProductInDirectSale(units=8, costAtTimeOfSale=200)
        product_in_direct_sale_2.Product_Store = product_store_1_2
        product_in_direct_sale_2.ProductInDirectSale_AmountChanger = [iva, utility_15, discount_4]

        direct_sale.ProductsInDirectSale = [product_in_direct_sale_1, product_in_direct_sale_2]
        session.add(direct_sale)

        locationLevelItem_estante_1 = session.query(LocationLevelItem).get(3)
        locationLevelItem_estante_2 = session.query(LocationLevelItem).get(4)
        locationLevelItem_tarima_1 = session.query(LocationLevelItem).get(5)

        locationLevelItem_Product_Store_1 = LocationLevelItem_Product_Store(13)
        locationLevelItem_Product_Store_1.LocationLevelItem = locationLevelItem_estante_1
        locationLevelItem_Product_Store_1.Product_Store = product_store_1_1
        session.add(locationLevelItem_Product_Store_1)

        locationLevelItem_Product_Store_2 = LocationLevelItem_Product_Store(32)
        locationLevelItem_Product_Store_2.LocationLevelItem = locationLevelItem_estante_2
        locationLevelItem_Product_Store_2.Product_Store = product_store_1_2
        session.add(locationLevelItem_Product_Store_2)

        locationLevelItem_Product_Store_3 = LocationLevelItem_Product_Store(3)
        locationLevelItem_Product_Store_3.LocationLevelItem = locationLevelItem_tarima_1
        locationLevelItem_Product_Store_3.Product_Store = product_store_1_2
        session.add(locationLevelItem_Product_Store_3)
        session.commit()
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
    finally:
        session.close()


# Function to create a complete instance
def create_lmo(name=None, email=None, password=None):
    try:
        session = ScopedSession()
        insert_initial_data(session=session, name=name, email=email, password=password)
        insert_initial_users_internal_companies(session=session)
        insert_suppliers_info_internal_companies(session=session)
        agregar_marketplaces_lmo(session=session)
        # insert_location_level_items_atenea(session=session)
        # insert_initial_stockAtenea(session=session, nameStore="Stock LMO.xlsm")
    except Exception as e:
        print(str(e))
        session.rollback()
    finally:
        session.close()


def create_cp(name=None, email=None, password=None):
    try:
        session = ScopedSession()
        insert_initial_data(session=session, name=name, email=email, password=password)
        insert_initial_users_internal_companies(session=session)
        insert_suppliers_info_internal_companies(session=session)
        agregar_marketplaces_cp(session=session)
        insert_location_level_items_atenea(session=session)
        # insert_initial_stockAtenea(session=session, nameStore="Stock CP.xlsm")
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
    finally:
        session.close()


def create_cp_test(name=None, email=None, password=None):
    try:
        session = ScopedSession()
        insert_initial_data(session=session, name=name, email=email, password=password)
        insert_initial_users_test(session=session)
        insert_suppliers_info_internal_companies(session=session)
        agregar_marketplaces_cp(session=session)
        insert_location_level_items_atenea(session=session)
        # insert_initial_stockAtenea(session=session, nameStore="Stock CP.xlsm")
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
    finally:
        session.close()


def create_new_instance(name=None, email=None, password=None):
    try:
        session = ScopedSession()
        insert_initial_data(session=session, name=name, email=email, password=password)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
    finally:
        session.close()


def create_test_cp():
    try:
        session = ScopedSession()
        insert_initial_data(session=session)
        insert_initial_users_internal_companies(session=session)
        insert_suppliers_info_internal_companies(session=session)
        agregar_marketplaces_cp(session=session)
        insert_location_level_items_atenea(session=session)
        insert_initial_stockAtenea(session=session)
        insert_test_data(session=session)
    except Exception as e:
        print(str(e))
        traceback.print_exc()
        session.rollback()
    finally:
        session.close()
