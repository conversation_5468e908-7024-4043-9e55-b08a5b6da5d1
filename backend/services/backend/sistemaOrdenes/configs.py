EXTENSIONES_PERMITIDAS_FACTURAS = {'xml'}
EXTENSIONES_PERMITIDAS_IMAGENES_PRODUCTOS = {
    'pjp', 'jpg', 'pjpeg', 'jpeg', 'jfif', 'png', 'webp'}
EXTENSIONES_PERMITIDAS_IMAGENES_USER = {
    'pjp', 'jpg', 'pjpeg', 'jpeg', 'jfif', 'png', 'webp'}
EXTS_PERMITIDAS_IMAGENES_COMPANY = {
    'pjp', 'jpg', 'pjpeg', 'jpeg', 'jfif', 'png', 'webp'}
ALLOWED_IMG_ENCODING = {'base64'}
EXTS_PERM_ARCHIVO_CARGA_MASIVA = {'xlsx'}
NOMBRE_ARCHIVO_CARGA_MASIVA = 'Plantilla_Carga_Masiva'
CARPETA_PLANTILLAS_XSLX = 'plantillas'
# ------Variables para obtener ordenes
# ---------<PERSON>ti<PERSON> de días previos a hoy para obtener los pedidos
# ------------ClaroShop
CANTIDAD_DE_DIAS_A_OBTENER_CLARO_SHOP = 10
# ------------Amazon
CANTIDAD_DE_DIAS_A_OBTENER_AMAZON = 4
# ------------Mercado Libre
CANTIDAD_DE_DIAS_A_OBTENER_ML = 1
# ------------Walmart
CANTIDAD_DE_DIAS_A_OBTENER_WALMART = 10

DIAS_ACTUALIZACION_1 = 4
DIAS_ACTUALIZACION_2 = 30
# ---------Tiempo entre cada actualización de nivel 1 * (Ordenes del dia para Amazon, Claro Shop, Walmart)
INTERVALO_DE_ACTUALIZACION_1 = 5 * 60
# ---------Tiempo entre cada actualización de nivel 2 * (Ordenes del dia para Mercado Libre)
INTERVALO_DE_ACTUALIZACION_2 = 10 * 60
# ---------Hora de actualización general
HORA_ACTUALIZACION_GRAL = 3
# ------Variables para enviar correo de alerta (error al obtener pedidos)
# ---------Puerto
PUERTO_SMTP = 465
# ---------Servidor SMTP
SERVIDOR_SMTP = "smtp.dreamhost.com"
# ---------Destinatario
DESTINARIO_EMAIL = "<EMAIL>"
# ------Horario de mantenimiento red
# ---------Inicio del horario de mantenimiento [hora, minuto, segundo]
INICIO_HORARIO_MANTENIMIENTO = {"hora": 0, "minuto": 0}
# ---------Final del horario de mantenimiento [hora, minuto, segundo]
FINAL_HORARIO_MANTENIMIENTO = {"hora": 0, "minuto": 30}
# ---------Horas de diferencia a ajustar para órdenes de ML
HORAS_DIFERENCIA_ML = -1

RUTA_SECRETS = "secrets.json"

RUTA_CERTIFICADOS = "certs"

LOGFILE = "log.txt"

TIME_ZONE = "America/Mexico_City"
# Roles
ADMIN_ROLE = "Admin"
WAREHOUSE_ROLE = "Warehouse_manager"
LOGISTICS_ROLE = "Logistics_employee"
ADMNISTRATIVE_ACCOUNTANT_ROLE = "Administrative_accountant"
SERVICES_ROLE = "Services"

HUMAN_ROLES= [ADMIN_ROLE, WAREHOUSE_ROLE,
         LOGISTICS_ROLE, ADMNISTRATIVE_ACCOUNTANT_ROLE]

ROLES = [*HUMAN_ROLES,
         SERVICES_ROLE]

# Constantes financieras
IVA = 0.16



# ML_SELLER_ID = "********" # cp
# ML_SELLER_ID = "**********" # lmo 
OFFSET_ZONA_HORARIA_ML = -4

LOG_FILE_PATH = "log/info_errors.log"