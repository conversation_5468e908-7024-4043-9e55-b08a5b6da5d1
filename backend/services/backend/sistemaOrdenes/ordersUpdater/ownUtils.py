import logging
import traceback
from .correo.enviarMail import enviarCorreoDeAlerta
#from customExceptions import Scope<PERSON>llException, ScopeUSerException
from sistemaOrdenes.ordersUpdater.customExceptions import ScopeUSerException, ScopeAllException
from sistemaOrdenes.app.marketplaces.marketplaceUtils import register_error_in_orders_updater
from sistemaOrdenes.app.models.db import ScopedSession
import os
import sys

logger = logging.getLogger(__name__)
logging.basicConfig(handlers=[
        logging.FileHandler("ordersUpdater.log"),
        logging.StreamHandler()
    ], level=logging.INFO)


def element_to_return(element_to_return_if_except, messageToSend):
    return element_to_return_if_except if element_to_return_if_except != None else messageToSend


def catcher():
    def decorator(func):
        def wrapper_func(*args, defaultReturn=None, **kwargs):
            enviroment_name = os.getenv('APP_NAME', "development")
            session = ScopedSession()
            try:
                kwargs['session'] = session
                return func(*args, **kwargs)
            except ScopeAllException as sae:
                session.rollback()
                traceback.print_exc()
                report = sae.success_report
                report['error'] = {
                    'amount': len(sae.error_report['ids']), 
                    'ids': ', '.join(sae.error_report['ids']),
                    'details': ', '.join(sae.error_report['details']),
                }
                userMessage = f'Actualización con errores: {report["inserted"]} nuevas, {report["updated"]} actualizadas, {report["error"]["amount"]} con error: {report["error"]["ids"]}'
                devMessage = f'{sae.marketplace_id}> Actualización con errores: {report["inserted"]} nuevas, {report["updated"]} actualizadas, {report["error"]["amount"]} con error: {report["error"]["details"]} | ScopeAllException ({sae.message})'
                try:
                    register_error_in_orders_updater(session, sae.marketplace_id, userMessage)
                    logger.info(f"Estado de última actualización de marketplace {sae.marketplace_id} actualizado: {devMessage}")
                except Exception as e:
                    logger.error(f'Error al registrar el estado de actualización de marketplace {sae.marketplace_id} del sistema (/api/marketplaces/registerErrorInOrdersUpdater) | ScopeAllException: ({str(e)})') #revisar
                try:
                    logger.error(devMessage)
                    enviarCorreoDeAlerta(f'{enviroment_name}: {devMessage}')
                    logger.info(f"Correo enviado:{devMessage}")
                except Exception as e:
                    logger.error(f'Error al enviar el correo marketplace {sae.marketplace_id}| ScopeAllException: {str(e)}') #revisar
                return element_to_return(report, devMessage)
            except ScopeUSerException as sue:
                print('9-----------------------------------------------------------1')
                session.rollback()
                print('9-----------------------------------------------------------2')
                traceback.print_exc()
                print('9-----------------------------------------------------------3')
                try:
                    print('9-----------------------------------------------------------4')
                    devMessage = f'ScopeUSerException: {sue.message}'
                    register_error_in_orders_updater(session, sue.marketplace_id, sue.message)
                    logger.info(f"Estado de última actualización de marketplace {sue.marketplace_id} actualizado: {devMessage}")
                    print('9-----------------------------------------------------------5')

                except Exception as e:
                    logger.error(f'Error al registrar el estado de actualización de marketplace {sue.marketplace_id} del sistema (/api/marketplaces/registerErrorInOrdersUpdater) | ScopeAllException: ({str(e)})')
                print('9-----------------------------------------------------------2')
                
                return element_to_return(sue.objetToReturn, devMessage)
            except Exception as e:
                print('9--------------------------------------------------------------E')
                session.rollback()
                traceback.print_exc()
                #
                exc_type, exc_obj, exc_tb = sys.exc_info()
                fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
                print('000000000000000000000000000000000000')
                print(exc_type, fname, exc_tb.tb_lineno)
                print(type(exc_type), type(fname), type(exc_tb.tb_lineno))
                print('000000000000000000000000000000000000')

                #
                message = str(e)
                try:
                    logger.error(message)
                    enviarCorreoDeAlerta(f'{enviroment_name}: {message}')
                    logger.info(f"Correo enviado:{message}")
                except Exception as e:
                    logger.error(f'Error al enviar correo | Exception: {str(e)}')
                return element_to_return(defaultReturn, message)
            finally:
                session.close()
        return wrapper_func
    return decorator
