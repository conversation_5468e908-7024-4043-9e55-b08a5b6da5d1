class ScopeAllException(Exception):
    """Exception raised for auth error in marketplace conection.

    Attributes:
        message -- explanation of the error
    """

    def __init__(self, marketplace_id=None, message="", success_report=None, error_report=None):
        self.message = message
        self.marketplace_id = marketplace_id
        self.success_report = success_report
        self.error_report = error_report
        super().__init__(self.message)

class ScopeUSerException(Exception):
    """Exception raised for error realted to orders updater flow.

    Attributes:
        message -- explanation of the error
   """

    def __init__(self, marketplace_id=None, message="", objetToReturn=None):
        self.message = "Credenciales vencidas, actualizar en configuraciones"
        self.marketplace_id = marketplace_id
        self.objetToReturn = objetToReturn
        super().__init__(self.message)

#solo al usuario?