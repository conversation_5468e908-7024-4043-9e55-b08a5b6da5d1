import os
import sys
sys.path.append(os.getcwd())
# ---+++Generales
from datetime import datetime
# ---+++ClaroShop
#from sistemaOrdenes.marketplaces.claro_shop import T1Comercios
from sistemaOrdenes.app.models.Models import OrderStatus, OrderConversation, OrderMessage, Order, MarketplaceGroup, SupportedMarketplaceGroup, OrderMessageAtachment
from sistemaOrdenes.marketplaces.mercado_libre import Mercado_libre
from sistemaOrdenes.app.orders.orderUtils import is_valid_Required, is_valid_Order
# ---+++App
from sistemaOrdenes.ordersUpdater import file_manager
from sistemaOrdenes.ordersUpdater import ownUtils
from customExceptions import ScopeAllException
from sistemaOrdenes.marketplaces.marketplaceUtils import get_marketplace_class
from sistemaOrdenes.app.models.db import ScopedSession
from sistemaOrdenes.app.marketplaces.marketplaceUtils import register_error_in_orders_updater, get_marketplace_credentials_raw, get_marketplace_credentials_by_name_raw
from sistemaOrdenes.app.orders.orderUtils import load_massive_orders
from sistemaOrdenes.configs import SERVICES_ROLE
import traceback
import shutil  # For decoding.
from sistemaOrdenes.app import config

secrets = file_manager.read_secrets()
check_ml = True
EMAIL_API_TORTUGA = secrets['usuarioAPI']['email']
PASSWORD_API_TORTUGA = secrets['usuarioAPI']['password']

SCOPE_CREDENTIALS = 'marketplacegroupcredentials_supportedMarketplaceGroup'

def verificar_formato_fecha(fecha, formato):
    try:
        datetime.strptime(fecha, formato)
        return True
    except Exception as e:
        # print(str(e))
        return False
    
def create_dic_Order(pedido):
    dataOrder = {}
    marketplace_order_id = pedido.get('marketplaceOrderId',"desconocido")
    if marketplace_order_id == "desconocido":
        raise Exception(marketplace_order_id)
    dataOrder['MarketplaceOrderId'] = marketplace_order_id
    dataOrder['MarketplaceId'] = pedido['marketplaceId']
    dataOrder['OrderStatusId'] = pedido['status']
    dataOrder['FulfillmentChannelId'] = pedido['FulfillmentChannelId']

    time_format = "%Y-%m-%dT%H:%M:%S.%fZ"
    if verificar_formato_fecha(pedido['creationDate'], time_format):
        time_obj = datetime.strptime(pedido['creationDate'], time_format)
        formatted_time = time_obj.strftime("%Y-%m-%d %H:%M:%S")
    else:
        formatted_time = pedido['creationDate']

    dataOrder['CreationDate'] = str(formatted_time)
    if dataOrder['CreationDate'] == '':
        dataOrder['CreationDate'] = None
        
    dataOrder['OrderUrl'] = pedido['orderURL']

    if pedido['paidAmount'] == '':
        dataOrder['PaidAmount'] = None
    else:
        dataOrder['PaidAmount'] = pedido['paidAmount']

    if pedido['fee'] == '':
        dataOrder['Fee'] = None
    else:
        dataOrder['Fee'] = pedido['fee']

    if pedido['shipping'] == '':
        dataOrder['Shipping'] = None
    else:
        dataOrder['Shipping'] = pedido['shipping']

    if pedido['receivedAmount'] == '':
        dataOrder['ReceivedAmount'] = None
    else:
        dataOrder['ReceivedAmount'] = pedido['receivedAmount']

    if pedido['shippingInfoNumber'] == '':
        dataOrder['ShippingInfoNumber'] = None
        dataOrder['MessageGuide'] = "Guia no disponible"
        dataOrder['StatusGuide'] = False
    else:
        dataOrder['ShippingInfoNumber'] = pedido['shippingInfoNumber']
        dataOrder['MessageGuide'] = pedido.get('messageGuide', 'sin mensaje')  # pedido['messageGuide']
        dataOrder['StatusGuide'] = pedido.get('statusGuide', False)  # pedido['statusGuide']

    ProductsInOrder = []

    for product in pedido['products']:
        productInOrder = {}
        if isinstance(product['variations'], dict):
            ProductVariations = product['variations']
        else:
            ProductVariations = None
        productInOrder['seller_sku'] = product['seller_sku']
        productInOrder['Title'] = product['title']
        productInOrder['Brand'] = product['brand']
        productInOrder['Model'] = product['model']
        productInOrder['Units'] = product['units']
        productInOrder['Photo'] = product['photo']
        productInOrder['publicationId'] = product['publicationId']
        productInOrder['publicationStock'] = product['publicationStock']
        productInOrder['publicationPrice'] = product['publicationPrice']
        productInOrder['publicationStatus'] = product['publicationStatus']
        if productInOrder['publicationStatus'] == '' or productInOrder['publicationStatus'] == None:
            productInOrder['publicationId'] = None
        productInOrder['publicationTime'] = product['publicationTime']

        if product['operationId'] == '':
            productInOrder['OperationId'] = 0
        else:
            productInOrder['OperationId'] = product['operationId']

        if product['paidAmount'] == '':
            productInOrder['PaidAmount'] = 0
        else:
            productInOrder['PaidAmount'] = product['paidAmount']

        if product['fee'] == '':
            productInOrder['Fee'] = 0
        else:
            productInOrder['Fee'] = product['fee']

        if product['shipping'] == '':
            productInOrder['Shipping'] = 0
        else:
            productInOrder['Shipping'] = product['shipping']

        if product['receivedAmount'] == '':
            productInOrder['ReceivedAmount'] = 0
        else:
            productInOrder['ReceivedAmount'] = product['receivedAmount']

        productInOrder['ProductVariations'] = ProductVariations

        ProductsInOrder.append(productInOrder)
    dataOrder['ProductsInOrder'] = ProductsInOrder

    Client = {}
    miniClient = pedido['client']

    Client['MarketplaceClientId'] = miniClient['marketplaceClientId']
    if verificar_formato_fecha(miniClient['registrationDate'], time_format):
        time_obj = datetime.strptime(
            miniClient['registrationDate'], time_format)
        formatted_time = time_obj.strftime("%Y-%m-%d %H:%M:%S")
    else:
        formatted_time = miniClient['registrationDate']
    Client['RegistrationDate'] = str(formatted_time)
    if Client['RegistrationDate'] == '':
        Client['RegistrationDate'] = None

    Client['City'] = miniClient['city']
    Client['State'] = miniClient['state']
    Client['Nickname'] = miniClient['nickname']
    Client['Name'] = miniClient['name']

    if miniClient['score'] == '':
        Client['Score'] = 0
    else:
        Client['Score'] = miniClient['score']

    Client['ZipCode'] = miniClient['zipCode']
    Client['PhoneNumber'] = miniClient['phoneNumber']
    Client['Address'] = miniClient['address']

    dataOrder['Client'] = Client
    return dataOrder


def create_dic_Order_record(pedido, errorListWrapper):
    try:
        marketplace_order_id = pedido.get('marketplaceOrderId',"desconocido")
        dataOrder = create_dic_Order(pedido)
        errorListWrapper["arrayOrders"].append(dataOrder)
    except Exception as e:
        print('9999999999999999qqqqqqqqqqQ1')
        print(str(e))
        print('9999999999999999qqqqqqqqqqQ2')
        errorListWrapper["errorOrders"]["ids"].append(marketplace_order_id)
        errorListWrapper["errorOrders"]["details"].append(f'{marketplace_order_id} => {str(e)}')

#'ids': [],
#'details': []
def process_orders(orders: list, error_orders: dict, report: dict, marketplace_group_id: int, session):
    if not orders:
        if error_orders['ids']:
            raise ScopeAllException(marketplace_id = marketplace_group_id,
                                    message=f'Error al obtener todas las ordenes desde marketplace',
                                    success_report=report,
                                    error_report=error_orders
                                    )
        else:
            report['error'] = {
                    'amount': 0, 
                    'ids': "",
                    'details': "",
                }
            register_error_in_orders_updater(session, marketplace_group_id, 'Actualización sin ordenes pendientes')
    else:
        lists_wrapper = {
            "errorOrders": error_orders,
            "arrayOrders": []
        }
        for pedido in orders:
            create_dic_Order_record(pedido, lists_wrapper)

        insert_report = {
            "inserted": 0,
            "updated": 0,
        }
        len_before = len(lists_wrapper['errorOrders']['ids'])
        try:
            load_massive_orders(session, lists_wrapper['arrayOrders'], lists_wrapper['errorOrders'], insert_report, marketplace_group_id)
            
        except Exception as e:
            print('00000000000000000ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZz')
            print(str(e))
            print('00000000000000000ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZz')
            for order in lists_wrapper['arrayOrders']:
                lists_wrapper['errorOrders']['ids'].append(order['MarketplaceOrderId'])
                lists_wrapper['errorOrders']['details'].append(f"{order['MarketplaceOrderId']}: error servidor")
            raise ScopeAllException(marketplace_id = marketplace_group_id,
                                    message=f'Error al actualizar todas las ordenes',
                                    success_report=report,
                                    error_report=error_orders
                                    )
        else:
            origen = report['marketplace']
            report = insert_report
            report['marketplace'] = origen
            error_orders = lists_wrapper['errorOrders']
            if error_orders['ids']: #ssssssssssssssssssssssssssssssssssssssssssssssssssssssssss
                message = ""
                error_pickup_orders = len_before
                if error_pickup_orders:
                    message = "errores individuales al recolectar ordenes"
                total_errors_after_request = len(lists_wrapper["errorOrders"]["ids"])
                if total_errors_after_request > error_pickup_orders:
                    message = f"{message}, errores individuales de ordenes en servidor" if message else "errores individuales de ordenes en servidor"
                raise ScopeAllException(marketplace_id = marketplace_group_id,
                                    message=message,
                                    success_report=report,
                                    error_report=error_orders
                                    )            
    return report



def get_order_statuses(session):
    miniStatus = session.query(OrderStatus)
    order_statuses = [
        finalStatus.basic_data_serialize()
        for finalStatus
        in miniStatus]
    list_status=[None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None]
    for miniinfo in order_statuses:
        if miniinfo['orderStatus'] == "Pendiente de pago":
            list_status[0] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Esperando stock":
            list_status[1] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Pendiente de envío":
            list_status[2] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Enviado":
            list_status[3] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Entregado":
            list_status[4] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Cancelado no procesado":
            list_status[5] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Cancelado ya procesado":
            list_status[6] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Error en el pedido":
            list_status[7] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Inconsistencia de datos":
            list_status[8] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Reembolsado":
            list_status[9] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Rechazado":
            list_status[10] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Contracargo":
            list_status[11] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Entregado sin posiblidad de cambios":
            list_status[12] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Acordar con el comprador":
            list_status[13] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "En Devolución":
            list_status[14] = miniinfo['orderStatusId']
        elif miniinfo['orderStatus'] == "Devuelto":
            list_status[15] = miniinfo['orderStatusId']
        else:
            pass
    return list_status


def sum_equal_jsons(json_list):
    result = {}
    for key in json_list[0].keys():
        sum_aux = 0
        for json_item in json_list:
            sum_aux += json_item[key]
        result[key] = sum_aux
    return result




@ownUtils.catcher()
def update_orders_marketplaceGroup(markeplaceGroup, days, initial_report, session):
    supported_marketplace_group_name = markeplaceGroup['supportedMarketplaceGroup']['name'] #c
    marketplace_group_id = markeplaceGroup['id'] #c
    supported_marketplace_credentials = markeplaceGroup['marketplaceGroupCredentials'] #c
    # marketplaces = markeplaceGroup['marketplaces']
    marketplaceGroup_obj = get_marketplace_class(supported_marketplace_group_name) #c
    if not marketplaceGroup_obj:
        return 'Marketplace no soportado por orders_updater'
    marketplaceGroup_obj.set_credentials_and_id_marketplace(credentials_param=supported_marketplace_credentials, marketplace_id=marketplace_group_id) #c
    marketplaceGroup_obj.set_session(session)
    list_status = get_order_statuses(session)    
    orders, error_orders = marketplaceGroup_obj.update_orders_last_days(list_status, days=days) #d revisar no enviar report defaultt
    print("orders")
    print(orders)
    print(error_orders)
    print("orders")
    return process_orders(orders, error_orders, initial_report, marketplace_group_id, session) #class method Marketplace


@ownUtils.catcher()
def actualizar_ordenes_marketplaces(days, session):
    """
    Update orders from all sincronized marketplaces

    Args:
        days (int): The first number.
    KArgs:
    Returns:
            str | list: The sum of the two numbers.
    """
    "marketplace order update lists"
    json_list = []
    markeplaceGroups = get_marketplace_credentials_raw(session, SCOPE_CREDENTIALS, SERVICES_ROLE)
    for markeplaceGroup in markeplaceGroups:
        supported_marketplace_group_name = markeplaceGroup['supportedMarketplaceGroup']['name']
        if markeplaceGroup['marketplaceGroupCredentials']:
            # errores seran calcudos al final
            initial_report = {
                "inserted": 0,
                "updated": 0,
                "marketplace": supported_marketplace_group_name,
            }
            report = update_orders_marketplaceGroup(markeplaceGroup, days, initial_report)
        else:
            report = f"{supported_marketplace_group_name}: no sincronizado"
        json_list.append(report)
    return json_list


#  1 | Amazon        
#  2 | Mercado Libre | groups/mercado_libre.png
#  3 | Walmart       | groups/walmart.png
#  4 | T1comercios 
def test_order_marketplace(marketplace_order_id, name):
    """
    Test function to get a specific order by its ID.
    """
    try:
        session = ScopedSession()
        marketplace_credentials = get_marketplace_credentials_by_name_raw(session, SCOPE_CREDENTIALS, SERVICES_ROLE, name)
        marketplace_group_id = marketplace_credentials['id'] #c
        supported_marketplace_credentials = marketplace_credentials['marketplaceGroupCredentials'] #c
        supported_marketplace_group_name = marketplace_credentials['supportedMarketplaceGroup']['name'] #c
        marketplaceGroup_obj = get_marketplace_class(supported_marketplace_group_name) #c
        if not marketplaceGroup_obj:
            return 'Marketplace no soportado por orders_updater'
        marketplaceGroup_obj.set_credentials_and_id_marketplace(credentials_param=supported_marketplace_credentials, marketplace_id=marketplace_group_id) #c
        list_status = get_order_statuses(session)
        marketplaceGroup_obj.set_session(session)
        order = marketplaceGroup_obj.get_turtle_format_order_info(marketplace_order_id, list_status)
        return order
    except Exception as e:
        print("exception")
        print(str(e))
        print("exception")
        session.rollback()
    finally:
        print("finally")
        session.close()

###################################################
def prepare_meli_session(func):
    def wrapper_func(*args, **kwargs):
        try:
            session = ScopedSession()
            marketplace_credentials = get_marketplace_credentials_by_name_raw(session, SCOPE_CREDENTIALS, SERVICES_ROLE, "Mercado Libre")
            marketplace_group_id = marketplace_credentials['id'] #c
            supported_marketplace_credentials = marketplace_credentials['marketplaceGroupCredentials'] #c
            supported_marketplace_group_name = marketplace_credentials['supportedMarketplaceGroup']['name'] #c
            marketplaceGroup_obj = get_marketplace_class(supported_marketplace_group_name) #c
            if not marketplaceGroup_obj:
                return 'Marketplace no soportado por orders_updater'
            marketplaceGroup_obj.set_credentials_and_id_marketplace(credentials_param=supported_marketplace_credentials, marketplace_id=marketplace_group_id) #c
            marketplaceGroup_obj.set_session(session)
            return func(marketplaceGroup_obj, session, *args, **kwargs)
        except Exception as e:
            print("exception")
            traceback.print_exc()
            print("exception")
            session.rollback()
        finally:
            print("finally")
            session.close()
    return wrapper_func

@prepare_meli_session
def getConversartionByPackIdML(marketplaceGroup_obj, session, packId):
    conversation = marketplaceGroup_obj.get_conversation_by_pack_id(packId)
    return conversation

@prepare_meli_session
def getAtachment(marketplaceGroup_obj, session, atachment_name):
        atachment_raw = marketplaceGroup_obj.get_attachment(atachment_name)
        with open("image.jpg", 'wb') as file:  # Choose your desired filename
            shutil.copyfileobj(atachment_raw, file)
            print("Image downloaded successfully!")

@prepare_meli_session
def get_action_guide_for_pack(marketplaceGroup_obj, session, pack_id):
    action_guide_for_pack = marketplaceGroup_obj.get_action_guide_for_pack(pack_id)
    return action_guide_for_pack

@prepare_meli_session
def send_message_using_action_guide(marketplaceGroup_obj, session, pack_id, option_type, option_id, template_id=None, text=None):
    return marketplaceGroup_obj.send_message_using_action_guide(pack_id, option_type, option_id, template_id, text)

@prepare_meli_session
def send_message_to_client(marketplaceGroup_obj, session, pack_id, to_user_id, text):
    return marketplaceGroup_obj.send_message_to_client(pack_id, to_user_id,text)

@prepare_meli_session
def create_test_user(marketplaceGroup_obj, session):
    return marketplaceGroup_obj.create_test_user()

scope = {
    'marketplaceGroupCredentials': {},
    'supportedMarketplaceGroup': {},
    'marketplaces': {
        'supportedMarketplace': {}
    }
}
def get_marketplace_obj(session, name):
    marketplace_db = session.query(MarketplaceGroup).join(SupportedMarketplaceGroup).filter(SupportedMarketplaceGroup.Name == name).first()
    if not marketplace_db:
        raise Exception("No se encontro el grupo de mercado libre")
    marketplace_db_serialized = marketplace_db.serialize(scope=scope)
    marketplace_group_id = marketplace_db_serialized['id']
    supported_marketplace_credentials = marketplace_db_serialized['marketplaceGroupCredentials'] #c
    supported_marketplace_group_name = marketplace_db_serialized['supportedMarketplaceGroup']['name']
    marketplace_obj = Mercado_libre(marketplace_group_name=supported_marketplace_group_name)
    marketplace_obj.set_credentials_and_id_marketplace(credentials_param=supported_marketplace_credentials, marketplace_id=marketplace_group_id)
    marketplace_obj.set_session(session)
    return marketplace_obj

def get_string_between(original_string, start, end):
    idx1 = original_string.find(start)
    len_start = len(start)
    # Find the index of the end substring, starting after the start substring
    idx2 = original_string.find(end, idx1 + len_start)
    return original_string[idx1 + len(start):idx2]

def get_order_info(order_id, session):
    list_status = get_order_statuses(session)
    mercado_libre_obj = get_marketplace_obj(session, "Mercado Libre")
    order = mercado_libre_obj.get_turtle_format_order_info(order_id, list_status)
    order = create_dic_Order(order)   
    return order



def create_or_return(order_id, session):
    order = get_order_info(order_id, session)
    MarketplaceOrderId = is_valid_Required('MarketplaceOrderId', order)
    order_db = session.query(Order).filter(Order.MarketplaceOrderId == str(MarketplaceOrderId)).first()
    if not order_db:
        order_db = is_valid_Order(order, 0, session)
    return order_db


@prepare_meli_session
def update_conversation(marketplaceGroup_obj, session, pack_id):
    conversation = marketplaceGroup_obj.get_conversation_by_pack_id(pack_id)
    path = conversation['conversation_status']['path']
    pack_id = get_string_between(path, "packs/", "/sellers")
    try:
        pack = marketplaceGroup_obj.get_an_order_id_from_pack_id(pack_id)
        order_id = pack['orders'][0]['id']
    except Exception as e:
        if get_string_between(str(e), '<', '>') != "404":
            raise e
        order_id = pack_id
    print('ññññññññññññññññññññññññññññññññ--1')  
    order = create_or_return(order_id, session)
    print('ññññññññññññññññññññññññññññññññ--2')  

    manage_db_order_convesation(conversation, marketplaceGroup_obj, order, session)
    print('ññññññññññññññññññññññññññññññññ--3')  
    
    #session.commit()

"""FormatText
TextTemplate

TemplateOptionOrderMessage
OptionOrderMessage"""
ATACHMENTS_FOLDER = config.Config.MESSAGE_ATTACHMENTS_FOLDER
def create_message_folder(message_id):
    # Verificar si la ruta completa existe y, si no, crearla
    print("Creating message folder for message_id:", message_id)
    path = os.path.join(ATACHMENTS_FOLDER, message_id)
    if not os.path.exists(path):
        os.makedirs(path)
    print("Message folder created at:", os.path.join(ATACHMENTS_FOLDER, message_id))

def addAtachemntToMessageFolder(message_id, filename, atachment):
    create_message_folder(message_id)
    path = os.path.join(ATACHMENTS_FOLDER, message_id, filename)
    with open(path, 'wb') as file:  # Choose your desired filename
        file.write(atachment)
    print("Image downloaded successfully!")

def manage_db_order_convesation(conversation, mercado_libre_obj, order, session):
    print('conversation----------------')
    print(conversation)
    print('conversation----------------')
    if not order.OrderConversation:
        orderConversation = OrderConversation(
            Status = conversation['conversation_status']['status'],
            Substatus = conversation['conversation_status']['substatus'],
        )
    else:
        order.OrderConversation.Status = conversation['conversation_status']['status']
        order.OrderConvesation.Substatus = conversation['conversation_status']['substatus']
    
    action_guide_for_pack = marketplaceGroup_obj.get_action_guide_for_pack(pack_id)
    for message in conversation['messages']:
        message_db = session.query(OrderMessage).filter(OrderMessage.MarketplaceId == message['id']).first()
        if not message_db:
            message_db = OrderMessage(
                TimeStamp = message['message_date']['received'],
                Message = message['text'],
                MarketplaceId = message['id'],
                FromClient = not (str(Mercado_libre.get_message_resource_by_name(message, 'sellers')) == str(message['from']['user_id']))
            )
            message_attachments = message['message_attachments']
            if message_attachments:
                attachments_db = []
                for attachment in message_attachments:
                    attachment_raw = mercado_libre_obj.get_attachment(attachment['filename'])
                    addAtachemntToMessageFolder(message['id'], attachment['filename'], attachment_raw)
                    attachment_db = OrderMessageAtachment(
                        MarketplaceId = attachment['filename'],
                        Type = attachment['type'],
                        URLPhoto = os.path.join(message['id'], attachment['filename']),
                    )
                    attachments_db.append(attachment_db)
                message_db.OrderMessageAtachments = attachments_db
            orderConversation.OrderMessages.append(message_db)
    order.OrderConversation = orderConversation
        
if __name__ == "__main__":
    #print(alimentarBase(1))
    #print(get_action_guide_for_pack('2000008521653087'))#2000008462211609#2000008454270975 #2000012318425522
    
    #print(get_action_guide_for_pack('2000012384317922'))#2000008462211609#2000008454270975 #2000012318425522
    #print(send_message_using_action_guide('2000012334016230','TEMPLATE', 'REQUEST_VARIANTS', 'TEMPLATE___REQUEST_VARIANTS___1'))#2000008462211609#2000008454270975 #2000012318425522
    #print('mmmmmmmmmmm')
    #print(getConversartionByPackIdML('2000012384317922'))

    #print('wwwwwwwwwwwwww')
    #print('91-------------------------------------------------------------1')
    print(update_conversation("2000012384317922"))
    #print('91-------------------------------------------------------------2')
    #print(get_action_guide_for_pack('2000012334016230'))#2000008462211609#2000008454270975 #2000012318425522


    #print(send_message_to_client("2000012318425522", "2564704254", "hola claro")) -----
    
    #print(actualizar_ordenes_marketplaces(10))
    #print(create_test_user())
    # print(update_walmart(4))
    # print(update_amazon(1))
    # test_order_marketplace("600000018657208", "Walmart")
    #getAtachment("799205342_877c0b7f-fcbf-48f5-9df1-15aea02efec1.jpeg")