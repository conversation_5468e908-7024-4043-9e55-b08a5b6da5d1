import logging
from sistemaOrdenes import configs
# Create a logger
logger = logging.getLogger('product_info_updater')
logger.setLevel(logging.DEBUG)

# Create a console handler for printing logs to the console
#console_handler = logging.StreamHandler()
#console_handler.setLevel(logging.DEBUG)

# Create a file handler for writing error logs to a file
file_handler = logging.FileHandler(configs.LOG_FILE_PATH)
file_handler.setLevel(logging.INFO)

# Create a formatter for the log entries
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# Add the formatter to the file handler
file_handler.setFormatter(formatter)
#console_handler.setFormatter(formatter)
# Add the file handler to the logger
logger.addHandler(file_handler)
#logger.addHandler(console_handler)
# Example usage - log an error
"""try:
    # Some code that might raise an exception
    raise ValueError("Something went wrong!")
except Exception as e:
    logger.error("An error occurred: %s", str(e), exc_info=False)
    logger.info('fecha de inicio')"""
def write_info(info):
    logger.info(info)

def write_warning(warning):
    logger.warning(f'Revisar: {warning}')

def write_error(error):
    logger.error(f'Ocurio un error: {error}')


#logger.write_info('No se ejectuto ninguna actualización')
#logger.write_error(f'No fue posible obtener los almancenes:{info}')
#logger.write_warning(errores)
