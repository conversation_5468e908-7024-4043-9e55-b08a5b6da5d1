import os
import json
from sistemaOrdenes import configs

def read_secrets():
    filename = os.path.join(os.path.dirname(__file__), configs.RUTA_SECRETS)
    print(filename)
    try:
        with open(filename, mode='r') as f:
            return json.loads(f.read())
    except FileNotFoundError:
        print('err')
        return {}


def save_ml_notification(notification_body):
    with open("notification.txt", 'w') as file:
        file.write(notification_body)
        return
