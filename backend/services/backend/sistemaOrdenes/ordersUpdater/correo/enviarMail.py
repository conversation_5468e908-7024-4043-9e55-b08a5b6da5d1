from sistemaOrdenes.ordersUpdater import file_manager
from sistemaOrdenes import configs
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
import sys

current = os.path.dirname(os.path.realpath(__file__))
parent = os.path.dirname(current)
sys.path.append(parent)

secrets = file_manager.read_secrets()
cuentaRemitente = secrets['correo_sistema']['cuenta_system_lamejoropcion']
contraseniaRemitente = secrets['correo_sistema']['password_system_lamejoropcion']



def createMesage(info):
    message = MIMEMultipart("alternative")
    message['Subject'] = "Alerta: Error al consultar los pedidos"
    message['From'] = cuentaRemitente
    message['To'] = configs.DESTINARIO_EMAIL

    text = f"""\
    Hola,
    Surgió un error al consultar los pedidos: {info}"""

    html = f"""\
    <html>
      <body>
        <p><PERSON><PERSON>,<br>
          Surgió un error al consultar los pedidos:{info}
        </p>
      </body>
    </html>
    """

    part1 = MIMEText(text, "plain")
    part2 = MIMEText(html, "html")

    message.attach(part1)
    message.attach(part2)
    return message
      
def enviarCorreoDeAlerta(info):
    context = ssl.create_default_context()
    with smtplib.SMTP_SSL(configs.SERVIDOR_SMTP, configs.PUERTO_SMTP, context=context) as server:
        server.login(cuentaRemitente, contraseniaRemitente)
        server.sendmail(cuentaRemitente,
                        configs.DESTINARIO_EMAIL, createMesage(info).as_string())

