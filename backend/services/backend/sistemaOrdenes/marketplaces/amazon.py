from sp_api.api import CatalogItems, Orders, ListingsItems
from sp_api.auth.exceptions import AuthorizationError
from sp_api.base import SellingApiException
from sp_api.base.exceptions import SellingApiRequestThrottledException
from datetime import datetime, date, timedelta
from urllib import parse
from sistemaOrdenes.ordersUpdater.customExceptions import ScopeUSerException
from .Marketplace import Marketplace
from sistemaOrdenes import configs
from flask import abort, make_response, jsonify
import time

class Amazon(Marketplace):
    # attributes-------------------------------------------------------------------------------
    # abstacts implementation--------------------------------------------------------------------------------------------------------------------
    def __init__(self, marketplace_group_name):
        super().__init__(marketplace_group_name)


    def autenticate(self):
        pass    
    

    def validate_credentials(self):
        pass


    def set_credentials_and_id_marketplace(self, credentials_param=None, marketplace_id=None,  marketplaces=None):
        self.credentials = dict(
            refresh_token=credentials_param['refreshToken'],
            lwa_app_id=credentials_param['clientId'],
            lwa_client_secret=credentials_param['clientSecret'],
        )
        self.MARKETPLACE_ID = marketplace_id


    def manage_bunch_of_orders(self, orders, list_status, listaJSONParaBase, error_orders):
        for order in orders:
            try:
                if order['SalesChannel'] == 'Amazon.com.mx':
                    final_order = self.form_final_order_info(order, list_status)
                    listaJSONParaBase.append(final_order)
            except Exception as e:
                id_pedido = str(order.get('AmazonOrderId', "Desconocido"))
                error_orders['ids'].append(id_pedido)
                error_orders['details'].append(f'{id_pedido} => {str(e)}')
    
    def update_orders_last_days(self, list_status, days: int = configs.CANTIDAD_DE_DIAS_A_OBTENER_AMAZON) -> tuple[list, dict]: 
        # Getting orders from API
        order_response = self.get_orders(days=days)
        orders = order_response['Orders']
        next_token = order_response.get('NextToken')
        print('**************************')
        print(next_token)
        print(len(orders))
        print('**************************')
        listaJSONParaBase = []
        error_orders = {
            'ids': [],
            'details': []
        }

        # Iterate over orders for set format
        self.manage_bunch_of_orders(orders, list_status, listaJSONParaBase, error_orders)
        while next_token is not None:
            order_response = self.get_orders(next_token=next_token)
            orders = order_response['Orders']
            next_token = order_response.get('NextToken')
            print('**************************')
            print(next_token)
            print(len(orders))
            print('**************************')
            self.manage_bunch_of_orders(orders, list_status, listaJSONParaBase, error_orders)
        
        return listaJSONParaBase, error_orders

    #others metods-------------------------------------------------------------------------------------------------------- 
    def form_final_order_info(self, order, list_status):
        #print('00000000000000000000--ORder')
        #print(order)
        #print('00000000000000000000--ORder')

        status_dict = {
            "Pending": list_status[0],
            "PendingAvailability": list_status[1],
            "Unshipped": list_status[2],
            "PartiallyShipped": list_status[2],
            "InvoiceUnconfirmed": list_status[2],
            "Shipped":list_status[3],
            "Cancelled": list_status[5],
            "Canceled": list_status[5],
            "Unfulfillable": list_status[7]
        }
        easy_ship_shipment_status_dict = {
            "PendingSchedule": list_status[2], # (The package is awaiting the schedule for pick-up.)
            "PendingPickUp": list_status[2], #  (Amazon has not yet picked up the package from the seller.)
            "PendingDropOff": list_status[2], #  (The seller will deliver the package to the carrier.)
            "LabelCanceled": list_status[2], #  (The seller canceled the pickup.)
            "PickedUp": list_status[3], #  (Amazon has picked up the package from the seller.)
            "DroppedOff": list_status[3], #  (The package is delivered to the carrier by the seller.)
            "AtOriginFC": list_status[2], #  (The packaged is at the origin fulfillment center.)
            "AtDestinationFC": list_status[3], #  (The package is at the destination fulfillment center.)
            "Delivered": list_status[4], #  (The package has been delivered.)
            "RejectedByBuyer": list_status[10], #  (The package has been rejected by the buyer.)
            "Undeliverable": list_status[7], #  (The package cannot be delivered.)
            "ReturningToSeller": list_status[14], #  (The package was not delivered and is being returned to the seller.)
            "ReturnedToSeller": list_status[15], #  (The package was not delivered and was returned to the seller.)
            "Lost": list_status[7], #  (The package is lost.)
            "OutForDelivery": list_status[3], #  (The package is out for delivery.)
            "Damaged": list_status[7] #  (The package was damaged by the carrier.)
        }
        easy_ship_shipment_status = order.get('EasyShipShipmentStatus', None)
        if easy_ship_shipment_status:
            status = easy_ship_shipment_status_dict[easy_ship_shipment_status]
        else:
            status = status_dict[order['OrderStatus']]
        canalFulfillment = 1 if order['FulfillmentChannel'] == 'AFN' else 2
        # print(json.dumps(o, indent=4))
        marketplaceOrderId = order['AmazonOrderId']
        products = self.get_order_items(marketplaceOrderId)['OrderItems']
        #print('00000000000000000000--products')
        #print(products)
        #print('00000000000000000000--products')
        # print(json.dumps(order, indent=4))
        clean_products = self.form_products_json(products)
        paidAmount = 0.0
        fee = 0.0
        shipping = 0.0
        received_amount = 0.0
        for miniProduct in clean_products:
            paidAmount = paidAmount + float(miniProduct["paidAmount"]) if (miniProduct["paidAmount"] is not None and paidAmount is not None) else None
            fee = fee + float(miniProduct["fee"]) if (miniProduct["fee"] is not None and fee is not None) else None
            shipping = shipping + float(miniProduct["shipping"])  if (miniProduct["shipping"] is not None and shipping is not None) else None
        
        received_amount = paidAmount-fee-shipping if (paidAmount is not None and fee is not None and shipping is not None) else None


        client = self.get_client_info(order)
        
        
        order_to_return = {
            "marketplaceId": self.MARKETPLACE_ID,
            "FulfillmentChannelId": canalFulfillment,
            "status": status,
            "creationDate": order['PurchaseDate'],
            "marketplaceOrderId": marketplaceOrderId,
            "orderURL": f'https://sellercentral.amazon.com.mx/orders-v3/order/{marketplaceOrderId}',
            "operationIds": [],
            "products": clean_products,
            "paidAmount": paidAmount,
            "fee": fee,
            "shipping": shipping,
            "receivedAmount": received_amount,
            "shippingInfoNumber": None,
            "client": client
        }
        return order_to_return

    
    def validate_request_amazon(func):
        def wrapper_func(self, *args, **kwargs):
            for i in range(7):
                try:
                    res = func(self, *args, **kwargs)
                    return res
                except AuthorizationError as ae:
                    raise ScopeUSerException(marketplace_id=self.MARKETPLACE_ID)
                except SellingApiRequestThrottledException  as se:
                    if i == 6:
                        raise se
                    seconds_for_wait = 2**i
                    print(f"Waiting {seconds_for_wait} seconds")
                    time.sleep(seconds_for_wait)
                except Exception as e:
                    raise e
        return wrapper_func


    @validate_request_amazon
    def get_orders(self, days=None, next_token=None):
        if next_token:
            res = Orders(credentials=self.credentials).get_orders(NextToken=next_token)
        else:
            res = Orders(credentials=self.credentials).get_orders(CreatedAfter=(date.today( # CreatedAfert or ModifiedAfter?
                ) - timedelta(days=days)).isoformat(), MarketplaceIds=["A1AM78C64UM0Y8"])
        return res.payload  # json data


    @validate_request_amazon
    def get_order_items(self, order_id):
        res = Orders(credentials=self.credentials).get_order_items(
            order_id, MarketplaceIds=["A1AM78C64UM0Y8"])
        return res.payload  # json data


    def form_products_json(self, products):
        #print('products---1')
        #print(products)
        #print('products---2')
        clean_products = []
        
        for p in products:
            # print(json.dumps(p, indent=4))
            # print("----------------------------------------------------")
            asin = p['ASIN']
            seller_sku = str(p['SellerSKU'])
            title = p['Title']
            units = p['QuantityOrdered']
            publicationStock = None
            publicationStatus = "PUBLISHED"
            publicationTime = None
            publicationId = asin
            
           
            item_price = p['ItemPrice']['Amount'] if 'ItemPrice' in p else None
            shipping = p['ShippingPrice']['Amount'] if 'ShippingPrice' in p else (0 if item_price else None)      
            if item_price:
                item_tax = float(p['ItemTax']['Amount']) if 'ItemTax' in p else 0
                shipping_tax = float(p['ShippingTax']['Amount']) if 'ShippingTax' in p else 0
                fee = item_tax + shipping_tax
                paidAmount = float(item_price) + float(fee) + float(shipping)
            else:
                fee = None
                paidAmount = None        

            try:
                product_info = self.get_product_info(asin)['summaries'][0]
                # print(json.dumps(product_info, indent=4))
                # print("----------------------------------------------------")
                brand = product_info.get('brandName', "")
                model = str(product_info.get('modelNumber', ""))
            except Exception as e:
                print(197)
                print(str(e))
                print(197)
                brand = ""
                model = ""
            try:
                listing_info = self.get_listing_info(parse.quote(seller_sku, safe=''))[
                    'summaries'][0]
                photo = listing_info['mainImage']['link']
            except Exception as e:
                print(207)
                print(str(e))
                print(207)
                photo = None
            publicationPrice = None #revisar
            clean_products.append({
                "seller_sku": seller_sku,
                "title": title,
                "photo": photo,
                "brand": brand,
                "variations": [],
                "operationId": 0,
                "model": model,
                "units": units,
                "paidAmount": paidAmount,
                "receivedAmount": item_price,
                "shipping": shipping,
                "fee": fee,
                "publicationId": publicationId,
                "publicationStock": publicationStock,
                "publicationPrice": publicationPrice,
                "publicationTime": publicationTime,
                "publicationStatus": publicationStatus
            })
        return clean_products


    @validate_request_amazon
    def get_product_info(self, asin):
        res = CatalogItems(credentials=self.credentials).get_catalog_item(
            asin=asin, marketplaceIds=["A1AM78C64UM0Y8"], ItemCondition="New")
        data = res.payload
        return data


    @validate_request_amazon
    def get_listing_info(self, sku):
        try:
            res = ListingsItems(credentials=self.credentials).get_listings_item(
                sellerId='A3KOVF9Y1J92CX', sku=sku, marketplaceIds=["A1AM78C64UM0Y8"])
            return res.payload  # json data
        except SellingApiException as ex:
            print(f"sku con error: {sku}. \n error:\n{ex}")
            return {
                "summaries": [{
                    'mainImage': {
                        'link': ""
                    }
                }]
            }


    def get_client_info(self, order_info):
        try:
            shipping_info = order_info['ShippingAddress']
            state = shipping_info['StateOrRegion']
            city = shipping_info['City']
            client_info = {
                "id": "",
                "marketplaceId": self.MARKETPLACE_ID,
                "marketplaceClientId": "",
                "registrationDate": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                "city": city,
                "state": state,
                "address": "",
                "zipCode": "",
                "phoneNumber": "",
                "email": "",
                "nickname": "",
                            "name": "",
                            "score": 0,
            }
        except Exception as e:
            #print(str(e))
            client_info = {
                "id": "",
                "marketplaceId": self.MARKETPLACE_ID,
                "marketplaceClientId": "",
                "registrationDate": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                "city": "",
                "state": "",
                "address": "",
                "zipCode": "",
                "phoneNumber": "",
                "email": "",
                "nickname": "",
                            "name": "",
                            "score": 0,
            }
        return client_info

    def get_shipping_label(self, shipping_number):
        abort(make_response(jsonify({'errores': 'Aun no es posible descargar guias de Amazon mediante mercaleader'}), 501))

    def get_shipping_info(self, shipping_number):
        abort(make_response(jsonify({'errores': 'Aun no es posible obtener la información de las guias de Amazon mediante mercaleader'}), 501))

    def get_turtle_format_order_info(self):
        pass
    """def get_order(order_id):
        try:
            res = Orders(credentials=credentials).get_order(order_id=order_id)
            return res.payload  # json data
        except SellingApiException as ex:
            print(ex)
        """
"""
def estimate_product_fees(sku, price, shipping=None, is_fba=False):
    # print(f"Sku: {sku}, price: {price}")
    try:
        res = ProductFees(credentials=credentials, marketplace=Marketplaces.MX).get_product_fees_estimate_for_sku(
            seller_sku=sku, price=price, shipping_price=shipping, currency='MXN', is_fba=is_fba)  # , marketplaceIds =["A1AM78C64UM0Y8"])
        data = res.payload
        # print(json.dumps(data, indent=4))
        return data['FeesEstimateResult']['FeesEstimate']['TotalFeesEstimate']['Amount']
    except SellingApiException as ex:
        print(ex)
    return


def get_order_fees(order_id, is_fba=False):
    financial_events = get_financial_events(order_id)['FinancialEvents']
    product_event_list = financial_events['ShipmentEventList'][0]['ShipmentItemList']
    total_amount = 0
    shipping = 0
    commision = 0
    for product_events in product_event_list:
        charge_list = product_events['ItemChargeList']
        fee_list = product_events['ItemFeeList']
        for charge in charge_list:
            total_amount += charge['ChargeAmount']['CurrencyAmount']
            # if charge['ChargeType'] == 'Principal':
            #     total_amount += charge['ChargeAmount']['CurrencyAmount']
            # if charge['ChargeType'] == 'Tax':
            #     total_amount += charge['ChargeAmount']['CurrencyAmount']
        if is_fba:
            for charge in charge_list:
                if charge['ChargeType'] == 'ShippingCharge':
                    shipping += charge['ChargeAmount']['CurrencyAmount'] * -1
            for fee in fee_list:
                if fee['FeeType'] == "FBAPerUnitFulfillmentFee":
                    commision += fee['FeeAmount']['CurrencyAmount'] * \
                        product_events['QuantityShipped'] * -1
                if fee['FeeType'] == "Commission":
                    commision += fee['FeeAmount']['CurrencyAmount'] * \
                        product_events['QuantityShipped'] * -1
        else:
            for fee in fee_list:
                commision += fee['FeeAmount']['CurrencyAmount'] * \
                    product_events['QuantityShipped'] * -1
                # if fee['FeeType'] == "Commission":
                #    commision += fee['FeeAmount']['CurrencyAmount'] * product_events['QuantityShipped'] * -1

    received_amount = total_amount - commision
    if not is_fba:
        adjustment_list = financial_events['AdjustmentEventList']
        for adjustment in adjustment_list:
            shipping += adjustment['AdjustmentAmount']['CurrencyAmount'] * -1
    else:
        received_amount -= shipping

    return {
        'total_amount': total_amount,
        'commision': commision,
        'shipping': shipping,
        'received_amount': received_amount
    }


def get_financial_events(order_id):
    try:
        res = Finances(
            Marketplaces.MX, credentials=credentials).get_financial_events_for_order(order_id)
        return res.payload  # json data
    except SellingApiException as ex:
        print(ex)
    return

"""