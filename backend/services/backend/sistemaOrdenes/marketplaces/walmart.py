import requests
import re
import uuid
import untangle
import math
from bs4 import BeautifulSoup
from io import BytesIO
from datetime import datetime, timedelta
from .Marketplace import Marketplace
from sistemaOrdenes.ordersUpdater.customExceptions import ScopeUSerException
from sistemaOrdenes import configs
from flask import abort, make_response, jsonify

def getUrlBase():
    return 'https://marketplace.walmartapis.com/v3'

def getUrlOrders():
    return getUrlBase() + '/orders'

def getHeaderCommon():
    guid = uuid.uuid4()
    guid = str(guid)
    header = {
        'WM_MARKET': 'mx',
        'WM_QOS.CORRELATION_ID': guid,
        'WM_SVC.NAME': 'Walmart Marketplace',
    }
    return header


def GetURLAccessTokenWalmart():
    return getUrlBase() + '/token'

def getURLItems():
    return getUrlBase() + '/items'

def returnGrantType():
    return {'grant_type': 'client_credentials'}

def getParamsOrders(offset, days):
    fechaFinal, fechaInicio = obtenerUltimosDiasFormatoYYYYMMDD(days)
    limite = obtenerLimiteOrders()
    body = {
        'createdStartDate': fechaInicio,
        'createdEndDate': fechaFinal,
        'offset': offset,
        'limit': limite
    }
    return body

def obtenerLimiteOrders():
    return 100


def obtenerUltimosDiasFormatoYYYYMMDD(days):
    hoy = datetime.today() + timedelta(days=1)
    hoyFormato = hoy.strftime("%Y-%m-%d")
    cincoDias = hoy - timedelta(days=(days + 1))
    CincoDiasFormato = cincoDias.strftime("%Y-%m-%d")
    return hoyFormato, CincoDiasFormato

def checkStatus(listaStatus):
    if -1 in listaStatus:
        return 8
    return min(listaStatus)

def status400():
    return "Bad request"


def status401_3():
    return "Authentication error"


def status404():
    return "Not found"


def status500():
    return "Internal Server Error"

def http_status(status):
    switcher = {
        400: status400(),
        401: status401_3(),
        403: status401_3(),
        404: status404(),
        500: status500()
    }
    return switcher.get(status, "Error desconocido")



class Walmart(Marketplace):

    #abstacts--------------------------------------------------------------------------------------------------------------------
    def __init__(self, marketplace_group_name):
        super().__init__(marketplace_group_name)
    
    
    def autenticate(self):
        pass
    
    
    def validate_credentials(self):
        pass

    
    def set_credentials_and_id_marketplace(self, credentials_param=None, marketplace_id=None,  marketplaces=None):
        self.CLIENT_ID = credentials_param['clientId']
        self.CLIENT_SECRET = credentials_param['clientSecret']
        self.ACCESS_TOKEN = credentials_param['accessToken']
        self.MARKETPLACE_ID = marketplace_id
    
    
    # definir este metodo en todas las clases que hereden de Marketplace, retornar lista con ordenes exitosas y string con ordenes no exitosas
    # tener cuidado con los try except que solo imprimen 
    def update_orders_last_days(self, list_status, days: int = configs.CANTIDAD_DE_DIAS_A_OBTENER_WALMART) -> tuple[list, dict]:
        # Getting orders from API
        listaPedidos = self.retornarPedidosUltimosDias(days)
        # Decaring list for success orders and list for orders with errors
        listaJSONParaBase = []
        error_orders = {
            'ids': [],
            'details': []
        }
        # Iterate over orders for set format
        for pedido in listaPedidos:
            try:
                jsonAux = self.crearJSONWalmartParaBase(pedido, list_status)
                listaJSONParaBase.append(jsonAux)
            except Exception as e:
                id_pedido = str(pedido.get('customerOrderId', "Desconocido"))
                error_orders['ids'].append(id_pedido)
                error_orders['details'].append(f'{id_pedido} => {str(e)}')
        return listaJSONParaBase, error_orders
    
    
    #others metods--------------------------------------------------------------------------------------------------------
    def get_header_commond_auth(self):
        header_auth = getHeaderCommon()
        header_auth['WM_SEC.ACCESS_TOKEN'] = self.ACCESS_TOKEN
        return header_auth
    
    def retornarPedidosUltimosDias(self, days):
        pedidosUltimosDias = []
        # url
        url = getUrlOrders()
        # params
        parametros = getParamsOrders(0, days)
        r = self.ejecutar_con_validacion_de_token(requests.get, url, auth=(self.CLIENT_ID, self.CLIENT_SECRET), params=parametros, extra_headers={'Accept': 'application/json'})
        productoJson = r.json()
        orders = productoJson['order']
        pedidosUltimosDias.extend(orders)
        totalOrders = productoJson['meta']['totalCount']
        print('totalOrders: ' + str(totalOrders))
        limiteInt = int(parametros['limit'])
        totalOrdersInt = int(totalOrders)
        if totalOrdersInt > limiteInt:
            paginasTotales = int(math.ceil(totalOrdersInt / limiteInt))
            for page in range(1, paginasTotales, 1):
                offsetAux = page * limiteInt
                # header
                parametros['offset'] = offsetAux
                r = self.ejecutar_con_validacion_de_token(requests.get, url, auth=(self.CLIENT_ID, self.CLIENT_SECRET), params=parametros, extra_headers={'Accept': 'application/json'})
                productoJson = r.json()
                pedidosUltimosDias.extend(productoJson['order'])
        return pedidosUltimosDias
    
    def crearJSONWalmartParaBase(self, pedido, list_status):
        print('09999999999999999999999>>>>>>>>>>>>>>>>>>>>>>><<<')
        print(pedido)
        print('09999999999999999999999>>>>>>>>>>>>>>>>>>>>>>><<<')
        order = {}
        # OrderId
        # Order['orderId'] -> generado por sql server
        # marketplace
        order['marketplaceId'] = self.MARKETPLACE_ID
        dbdt = pedido['orderDate']
        order['creationDate'] = dbdt
        order['marketplaceOrderId'] = pedido['customerOrderId']
        order['orderURL'] = 'https://seller.walmart.com/order-management/details'
        shippingInfo = pedido['shippingInfo']
        client = {}
        client['marketplaceId'] = 4
        verifiedPhone = re.sub(r'\W+', '', shippingInfo['phone'])
        client['marketplaceClientId'] = ""
        client['registrationDate'] = ""
        client['city'] = shippingInfo['postalAddress']['city']
        client['state'] = shippingInfo['postalAddress']['state']
        client['nickname'] = ""
        client['name'] = shippingInfo['postalAddress']['name']
        client['score'] = ""
        client['zipCode'] = shippingInfo['postalAddress']['postalCode']
        client['phoneNumber'] = verifiedPhone[: 15]
        if not 'customerEmailId' in pedido:
            pedido['customerEmailId'] = ''
        client['email'] = pedido['customerEmailId']
        direccionCompleta = f'{shippingInfo["postalAddress"]["address1"]} num {shippingInfo["postalAddress"]["address2"]}, {shippingInfo["postalAddress"]["address3"]}, {shippingInfo["postalAddress"]["address4"]}'
        direccionCompleta = direccionCompleta[:250]
        client['address'] = direccionCompleta

        order['client'] = client
        order['operationIds'] = []
        orderLines = pedido['orderLines']
        variations = []
        productosSinRepetir = {}
        lineStatusList = []

        for orderLine in orderLines:
            lineStatus = orderLine['orderLineStatus'][0]['status']
            if lineStatus == 'OnHold':
                lineStatusList.append(list_status[0])
            elif lineStatus == 'Created':
                lineStatusList.append(list_status[1])
            elif lineStatus == 'Acknowledged':
                lineStatusList.append(list_status[2])
            elif lineStatus == 'Shipped':
                lineStatusList.append(list_status[3])
            elif lineStatus == 'Delivered':
                lineStatusList.append(list_status[4])
            elif lineStatus == 'Cancelled':
                lineStatusList.append(list_status[5])
            else:
                lineStatusList.append(list_status[7])
                print(f'-------------------\n{lineStatus}\n-----------------------')
            item = orderLine['item']
            sku = item['sku']
            if sku in productosSinRepetir:
                productosSinRepetir[sku]['units'] += int(orderLine['orderLineQuantity']['amount'])
                productosSinRepetir[sku]['receivedAmount'] += float(orderLine['charges'][0]['chargeAmount']['amount'])
                #falta por revisar segun categoria
                fee = None
                shipping = 0.0
                receivedAmount = None
                #falta por revisar segun categoria
            else:
                # print('PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPp-DetallesPedido')
                # print(json.dumps(item, indent=4))
                # print('PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPp-DetallesPedido')
                if orderLine['shippingMethod'] != 'STANDARD':
                    order['FulfillmentChannelId'] = 1
                else:
                    order['FulfillmentChannelId'] = 2

                skuAux = item['sku']
                modelAux = item['sku']
                variations = []

                titleAux = item['productName']
                photoAux = item['imageUrl'].split('?')[0]
                brandAux = ''

                
                unitAux = int(orderLine['orderLineQuantity']['amount'])
                paidAmound = float(orderLine['charges'][0]['chargeAmount']['amount'])
                #falta por revisar segun categoria
                fee = None
                shipping = 0.0
                receivedAmount = None
                #falta por revisar segun categoria
                idpedidorelacion = None
                
                bool, pubWalmart = self.getItemBySKU(item["sku"])
                if bool:
                    publicationId = item["sku"]
                    publicationStock = pubWalmart["publicationStock"]
                    publicationPrice = pubWalmart["publicationPrice"]
                    publicationTime = pubWalmart["publicationTime"]
                    publicationStatus = pubWalmart["publicationStatus"]
                else:
                    publicationId = item["sku"]
                    publicationStock = None
                    publicationPrice = paidAmound
                    publicationTime = None
                    publicationStatus = None
                productoNuevo = {'seller_sku': skuAux,
                                 'title': titleAux,
                                 'variations': variations,
                                 'photo': photoAux, 
                                 'brand': brandAux, 
                                 'model': modelAux,
                                 'units': unitAux,
                                 'operationId': idpedidorelacion, 
                                 'paidAmount': paidAmound,
                                 'fee': fee, 
                                 'shipping': shipping,
                                 'receivedAmount': receivedAmount,
                                "publicationId": publicationId,
                                "publicationStock": publicationStock,
                                "publicationPrice": publicationPrice,
                                "publicationTime": publicationTime,
                                "publicationStatus": publicationStatus}
                productosSinRepetir[sku] = productoNuevo

        order['products'] = productosSinRepetir.values()

        order['status'] = checkStatus(lineStatusList)
        if order['status'] == 5 or order['status'] == 6:
            order['paidAmount'] = 0
            order['fee'] = 0
            order['shipping'] = 0
            order['receivedAmount'] = 0
        else:
            order['paidAmount'] = float(pedido['orderTotal']['amount'])
            order['fee'] = None #round(comisionWalmart, 2) #rrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrevisar
            # revisar envios gratis cuando exista la opcion
            if orderLine['shippingMethod'] != 'STANDARD':
                order['shipping'] = 0
            else:
                order['shipping'] = 0
            # round(envios,2)
            order['receivedAmount'] = None #round(
                #(order['paidAmount']) - comisionWalmart, 2) #revisaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaar
        order['comments'] = ''
        if 'trackingNumber' in pedido["shipments"][0]:
            order['shippingInfoNumber'] = pedido["shipments"][0]["trackingNumber"]
            order['messageGuide'] = "Guia disponible"
            order['statusGuide'] = True
        else:
            order['shippingInfoNumber'] = None
            order['messageGuide'] = "Guia no disponible"
            order['statusGuide'] = False
        return order

    def get_turtle_format_order_info(self):
        pass
    

    def getItemBySKU(self, sku):
        url = f"{getURLItems()}/{sku}"
        try:
            r = self.ejecutar_con_validacion_de_token(requests.get, url, auth=(self.CLIENT_ID, self.CLIENT_SECRET))
            # print(r)
            # print(r.text)
            status_code = r.status_code
            # print('oooooooooooooo')
            # print(r)
            # print('--')
            # print(r.status_code)
            # print('--')
            # print(r.text)
            # print('oooooooooooooo')
            if status_code == 200:
                # print('if 200 ---')
                soup = BeautifulSoup(r.text, 'xml')
                # print('llllllllllllllllllllllllllll')
                # print(r.text)
                # print('llllllllllllllllllllllllllll')

                # Ahora puedes extraer cada elemento que necesites
                sku = soup.find('ns2:sku').text
                price = soup.find('ns2:price').find('ns2:amount').text
                publishedStatus = soup.find('ns2:publishedStatus').text

                r = {
                    "publicationStock": None,
                    "publicationPrice": price,
                    "publicationTime": None,
                    "publicationStatus": publishedStatus
                }
            else:
                print('else')
                print(http_status(r.status_code))
                print(r.text)
                return False, 'Estatus no exitoso: ' + http_status(r.status_code)
        except Exception as e:
            print('---------------------x')
            print(str(e))
            print('---------------------x')
            return False, f'Error en la petición: {str(e)}'
        return True, r


      
    def renovar_access_token(self):
        url = GetURLAccessTokenWalmart()
        headers = getHeaderCommon()
        headers['Content-Type'] = 'application/x-www-form-urlencoded'
        data = returnGrantType()
        r = self.manage_responses(requests.post, url, auth=(self.CLIENT_ID, self.CLIENT_SECRET), data=data, headers=headers)
        objetoToken = untangle.parse(r.text)
        self.ACCESS_TOKEN = objetoToken.OAuthTokenDTO.accessToken.cdata
        self.update_marketplace_credentials_patch()

    
    def manage_responses(self, request, url, auth=None, params=None, data=None, headers=None):
        res = request(url, auth=auth, params=params, data=data, headers=headers)
        if 400 <= res.status_code <= 403:
            raise ScopeUSerException(marketplace_id=self.MARKETPLACE_ID)
        if not 200 >= res.status_code <= 202:
            raise Exception(f"Walmart: Error al realizar la petición: {url}")
        return res
    
    def ejecutar_con_validacion_de_token(self, request, url, stream=False, auth=None, params=None, data=None, extra_headers={}):
        headers = self.get_header_commond_auth()
        headers.update(extra_headers)
        res = request(url, auth=auth, stream=stream, params=params, data=data, headers=headers)
        #print('uuuuuuuuuuuuuuuuuuuuuuu')
        #print('res.status_code: ' + str(res.status_code))
        #print('res.text: ' + str(res.text))
        #print('uuuuuuuuuuuuuuuuuuuuuuu')

        if 400 <= res.status_code <= 403:
            self.renovar_access_token()
            headers = self.get_header_commond_auth()
            headers.update(extra_headers)
            res = self.manage_responses(request, url, auth=auth, params=params, data=data, headers=headers)
        if not 200 >= res.status_code <= 202:
            raise Exception(f"Walmart: Error al realizar la petición: {url}")
        return res


    def get_shipping_label(self, tracking_id):
        url = f"{getUrlOrders()}/label/{str(tracking_id)}"
        response = self.ejecutar_con_validacion_de_token(requests.get, url, auth=(self.CLIENT_ID, self.CLIENT_SECRET))
        shipping_guide = BytesIO(response.content)
        return shipping_guide, f'{tracking_id}.png'


    def get_shipping_info(self, order_id):
        abort(make_response(jsonify({'errores': 'Aun no es posible obtener la información de las guias de Claro Shop mediante mercaleader'}), 501))
        url = getUrlOrders() + "/" + str(order_id)
        response = self.ejecutar_con_validacion_de_token(requests.get, url, auth=(self.CLIENT_ID, self.CLIENT_SECRET), extra_headers={'Accept': 'application/json'})
        return response.json()