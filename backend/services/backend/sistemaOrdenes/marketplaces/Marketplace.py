from abc import ABC, abstractmethod
from sistemaOrdenes.app.marketplaces.marketplaceUtils import set_group_credentials_patch
from sistemaOrdenes.app.models.Models import MarketplaceGroupCredentials
from sistemaOrdenes.app import ownUtils
class Marketplace(ABC):

    @abstractmethod
    def __init__(self, marketplace_group_name):
        self.marketplace_group_name = marketplace_group_name
        
    def set_session(self, session):
        self.session = session
    @abstractmethod
    def set_credentials_and_id_marketplace():
        pass

    @abstractmethod
    def autenticate():
        pass

    @abstractmethod
    def update_orders_last_days():
        pass
    
    @abstractmethod
    def get_turtle_format_order_info():
        pass
    
    @abstractmethod
    def validate_credentials():
        pass
    
    @abstractmethod
    def get_shipping_label():
        pass

    @abstractmethod
    def get_shipping_info():
        pass

    def update_marketplace_credentials_patch(self):
        print('update_marketplace_credentials_patch')
        marketplace_group_credentials_obj = ownUtils.validate_if_object_exists(self.session, 'id', 'native', self.MARKETPLACE_ID, MarketplaceGroupCredentials)
        exito, info = set_group_credentials_patch({"accessToken": self.ACCESS_TOKEN}, marketplace_group_credentials_obj)
        if not exito: raise Exception(info)
        self.session.commit()

if __name__ == "__main__":
    class walmart_test(Marketplace):
        print('cuat')
        pass
    

    class amanzon_test(Marketplace):
        pass
    walmart_test.get_orders_by_date()
    #walmart_test.set_credentials("w")
    #walmart = walmart_test()