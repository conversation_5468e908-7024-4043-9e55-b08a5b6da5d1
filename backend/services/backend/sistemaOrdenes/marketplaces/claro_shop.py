import configs
import requests
import hashlib
import json
import traceback
from datetime import datetime, timedelta
from pytz import timezone
# import datetime as dt
import sys
import os
import seed2
from .Marketplace import Marketplace
from flask import abort, make_response, jsonify

current = os.path.dirname(os.path.realpath(__file__))
parent = os.path.dirname(current)
sys.path.append(parent)

class T1Comercios(Marketplace):
    def __init__(self, marketplace_group_name):
        super().__init__(marketplace_group_name)

    def autenticate(self):
        pass
    def set_credentials_and_id_marketplace(self, credentials_param=None, marketplace_id=None,  marketplaces=None):
        self.clave_publica = credentials_param['clientId']
        self.clave_privada = credentials_param['clientSecret']
        self.MARKETPLACES = marketplaces

    def update_orders_last_days(self, days= configs.CANTIDAD_DE_DIAS_A_OBTENER_CLARO_SHOP):
        error_order_id_list = []
        listaPedidos, msg = self.retornarPedidosUltimosDias(days)

        listaPedidos = list(dict.fromkeys(listaPedidos))
        # print(listaPedidos)
        listaJSONParaBase = []
        for noPedido in listaPedidos:
            jsonAux = self.crearJSONClaroShopParaBase(noPedido)
            if jsonAux is not None:
                listaJSONParaBase.append(jsonAux)
        return listaJSONParaBase, error_order_id_list
    
    def validate_credentials(self):
        pass


    def  get_id_marketplace_by_name(self, marketplaces, name):
        for marketplace in marketplaces:
            if marketplace['supportedMarketplace']['name'] == name:
                return marketplace['id']
        return None

        

    def crearURLComun(self):
        # url ambiente
        urlAmbiente = 'https://selfservice.claroshop.com/apicm/v1'
        # Llave pública
        # Fecha
        # Obtener la fecha y hora en el formato requerido
        # Define different timezones
        utc = timezone('UTC')
        mexico_city = timezone('America/Mexico_City')
        # Get the current time in UTC
        utc_now = datetime.now(utc)
        # Convert the current UTC time to US/Eastern time
        print('0000000000000000000000000000000000000000000000000000______________________________---2000006575278843')
        print(utc_now)
        mexico_city_now = utc_now.astimezone(mexico_city)
        # one_hour_later = mexico_city_now + timedelta(hours=1)
        # ajustando error de hora
        fecha = mexico_city_now.strftime("%Y-%m-%dT%H:%M:%S")
        print(fecha)
        print('0000000000000000000000000000000000000000000000000000______________________________---2000006575278843')
        # print('ffffffffffffffffffffffffffffffff----------')
        # # print(fechaActual)
        # print('UTC:', utc_now)
        # print('America/Mexico_City:', mexico_city_now)
        # print('**')
        # print(fecha)
        # # print(type(fecha))
        # print('ffffffffffffffffffffffffffffffff-----')
        # Signature
        # Clave privada
        # Creamos signature
        prehash = self.clave_publica + fecha + self.clave_privada
        signature = hashlib.sha256(prehash.encode()).hexdigest()
        # Creamos URL
        urlComun = f"{urlAmbiente}/{self.clave_publica}/{signature}/{fecha}"
        # Retornamos URL
        return urlComun


    def crearURLProducto(self):
        urlComun = self.crearURLComun()
        return urlComun + '/producto'


    def crearURLColores(self):
        urlComun = self.crearURLComun()
        return urlComun + '/colores'


    def crearURLTallas(self):
        urlComun = self.crearURLComun()
        return urlComun + '/tallas'


    def crearURLMarcas(self):
        urlComun = self.crearURLComun()
        return urlComun + '/marcas'


    def crearURLCategorias(self):
        urlComun = self.crearURLComun()
        return urlComun + '/categorias'


    def crearURLPedidos(self):
        urlComun = self.crearURLComun()
        return urlComun + '/pedidos'


    def crearURLPedidosFiltros(self):
        urlComun = self.crearURLComun()
        return urlComun + '/pedidosfiltros'


    def status400(self):
        return "Bad request"


    def status401_3(self):
        return "Authentication error"


    def status404(self):
        return "Not found"


    def status500(self):
        return "Internal Server Error"


    def http_status(self, status):
        switcher = {
            400: self.status400(),
            401: self.status401_3(),
            403: self.status401_3(),
            404: self.status404(),
            500: self.status500()
        }
        return switcher.get(status, "Error desconocido")


    def regresarHeaderPOSTAndPUT(self):
        headerPOST = {'Content-Type': 'application/x-www-form-urlencoded'}
        return headerPOST


    def regresarHeaderGET(self):
        headerGET = {'Content-Type': 'application/json'}
        return headerGET


    def armarJSONActualizarProducto(self, precio='', stock=''):
        datos = {}
        if precio != '':
            datos['preciopublicobase'] = precio
        if stock != '':
            datos['cantidad'] = stock
        return datos


    def obtenerMarcas(self):
        urlGET = self.crearURLMarcas()
        try:
            r = requests.get(urlGET)
            rJson = r.json()
            if r.status_code == 200:
                if rJson['estatus'] == 'success':
                    return True, rJson['marcas']
                else:
                    r = str(rJson['estatus']) + ':' + str(rJson['mensaje'])
                    return False, r
            else:
                r = self.http_status(r.status_code)
                return False, r
        except Exception as e:
            # print(traceback.format_exc())
            r = "No se pudo ejecutar la petición," + str(e)
            return False, r


    def obtenerDetalleCategoria(self, idCategoria):
        urlCategorias = self.crearURLCategorias()
        urlCategorias = urlCategorias + '/' + idCategoria
        r = requests.get(urlCategorias)
        rJson = r.json()
        # print('Detalles---------------------')
        # print(rJson)
        # print('Detalles---------------------')
        return rJson


    def obtenerCategorias(self):
        urlGET = self.crearURLCategorias()
        try:
            r = requests.get(urlGET)
            rJson = r.json()
            if r.status_code == 200:
                if rJson['estatus'] == 'success':
                    # print(rJson['categorias'])
                    return True, rJson['categorias']
                else:
                    r = str(rJson['estatus']) + ':' + str(rJson['mensaje'])
                    return False, r
            else:
                r = self.http_status(r.status_code)
                return False, r
        except Exception as e:
            # print(traceback.format_exc())
            r = "No se pudo ejecutar la petición," + str(e)
            return False, r


    def obtenerIDconNombreMarca(self, nombreMarca):
        exito, listaMarcas = self.obtenerMarcas()
        if exito is True:
            for idDeMarca, marca in listaMarcas.items():
                if marca.casefold() == nombreMarca.casefold():
                    return idDeMarca
        return "No encontrado"


    def obtenerIDconNombreCategoria(self, nombreCategoria):
        exito, listaCategorias = self.obtenerCategorias()
        # print(listaCategorias)
        # print(type(listaCategorias))
        if exito is True:
            """dictas=dict_generator(listaCategorias, pre=None)
            for d in dictas:
                    print(d)
                    print('<---------------------------->')"""
            for categoriasPadre in listaCategorias:
                # print(categoriasPadre)
                categoriaPadreAux = categoriasPadre['nombre']
                idDeCategoria = categoriasPadre['idcategoria']
                # print(categoriaPadreAux + '-' + str(idDeCategoria))
                # print("<-------------------------------->")
                if categoriaPadreAux.casefold() == nombreCategoria.casefold():
                    return idDeCategoria
        return "No encontrado"


    def agregarProducto(self, producto):
        # messagebox.showinfo("showinfo", productoString)
        if type(producto) is not dict:
            producto = json.loads(producto)
        # messagebox.showinfo("showinfo", producto)
        urlPOST = self.crearURLProducto()
        # producto = armarNuevoProducto()
        headersPOST = self.regresarHeaderPOSTAndPUT()
        try:
            r = requests.post(urlPOST, data=producto, headers=headersPOST)
            # print(r.status_code)
            # print(r)
            # print(r.json())
            if r.status_code == 200:
                rJson = r.json()
                # print(rJson)
                if rJson['estatus'] == 'error':
                    strRJson = str(rJson['mensaje'])
                    r = str(rJson['estatus']) + ': '
                    if '{' in strRJson and '}' in strRJson:
                        if 'nombre' in strRJson:
                            r = r + 'revisar nombre, '
                        if 'descripcion' in strRJson:
                            r = r + 'revisar descripcion, '
                        if 'especificacionestecnicas' in strRJson:
                            r = r + 'revisar especificaciones tecnicas, '
                        if 'alto' in strRJson:
                            r = r + 'revisar alto, '
                        if 'ancho' in strRJson:
                            r = r + 'revisar ancho, '
                        if 'profundidad' in strRJson:
                            r = r + 'revisar profundidad, '
                        if 'peso' in strRJson:
                            r = r + 'revisar peso, '
                        if 'preciopublicobase' in strRJson:
                            r = r + 'revisar precio publico base, '
                        if 'preciopublicooferta' in strRJson:
                            r = r + 'revisar precio publico oferta, '
                        if 'cantidad' in strRJson:
                            r = r + 'revisar cantidad, '
                        if 'skupadre' in strRJson:
                            r = r + 'revisar skupadre, '
                        if 'ean' in strRJson:
                            r = r + 'revisar ean, '
                        if 'estatus' in strRJson:
                            r = r + 'revisar estatus, '
                        if 'embarque' in strRJson:
                            r = r + 'revisar embarque, '
                        if 'categoria' in strRJson:
                            r = r + 'revisar categoria, '
                        if 'fotos' in strRJson:
                            r = r + 'revisar fotos, '
                        if 'agregarmarca' in strRJson:
                            r = r + 'revisar amarca, '
                        if 'tag' in strRJson:
                            r = r + 'revisar tag, '
                        if 'garantia' in strRJson:
                            r = r + 'revisar garantia, '
                        if r[-2] == ',':
                            r = r[:-2]
                    else:
                        r = r + str(rJson['mensaje'])
                elif rJson['estatus'] == 'success':
                    # print(rJson['infoproducto']['transactionid'])
                    idClaroShop = str(rJson['infoproducto']['transactionid'])
                    # print(type(rJson['infoproducto']))
                    r = str(
                        rJson['mensaje']) + ': El producto ha sido subido exitosamente ->' + idClaroShop
                else:
                    r = str(rJson['estatus']) + ':' + str(rJson['mensaje'])
            else:
                r = self.http_status(r.status_code)
        except Exception as e:
            r = "No se pudo ejecutar la petición," + str(e)
        # messagebox.showinfo("showinfo", r)
        return r


    def actualizarProducto(self, datos):
        idDelProducto = datos[0]
        precio = datos[1]
        stock = datos[2]
        urlPUT = self.crearURLProducto()
        urlPUT = urlPUT + '/' + idDelProducto
        # print(urlPUT)
        datosAModificar = self.armarJSONActualizarProducto(precio, stock)
        headersPUT = self.regresarHeaderPOSTAndPUT()
        try:
            r = requests.put(urlPUT, data=datosAModificar, headers=headersPUT)
            if r.status_code == 200:
                rJson = r.json()
                # print(rJson)
                r = str(rJson['estatus']) + ':' + str(rJson['mensaje'])
            else:
                r = self.http_status(r.status_code)
        except Exception as e:
            # print(traceback.format_exc())
            r = "No se pudo ejecutar la petición," + str(e)
        return r


    def obtenerPedidosPendientes(self):
        urlPedidos = self.crearURLPedidos()
        entregadoPendienteembarcado = 'pendientes'
        parametros = {'action': entregadoPendienteembarcado,
                    'limit': '10', 'page': 1, 'search': ''}
        pendientesARevisar = []
        try:
            r = requests.get(urlPedidos, params=parametros)
            rJson = r.json()
            # print(rJson)
            # print(rJson['estatus'])
            if r.status_code == 200:
                # print(r.text)
                totalPendientes = rJson['total' + entregadoPendienteembarcado]
                if totalPendientes > 0:
                    pendientesListaAux = rJson['lista' + entregadoPendienteembarcado]
                    for pendiente in pendientesListaAux:
                        pendientesARevisar.append(
                            self.crearJSONClaroShopParaBase(pendiente))
                    paginasTotales = rJson['totalpaginas']
                    for page in range(2, paginasTotales + 1, 1):
                        parametros['page'] = page
                        r = requests.get(urlPedidos, params=parametros)
                        rJson = r.json()
                        # print(rJson)
                        if r.status_code == 200:
                            pendientesListaAux = rJson['lista' + entregadoPendienteembarcado]
                            for pendiente in pendientesListaAux:
                                pendientesARevisar.append(
                                    self.crearJSONClaroShopParaBase(pendiente))
                    r = pendientesARevisar
                    exito = True
                else:
                    r = 'No hay pendientes'
                    exito = False
            else:
                r = self.http_status(r.status_code)
                exito = False
        except Exception as e:
            # print(traceback.format_exc())
            r = "No se pudo ejecutar la petición," + str(e)
            exito = False
        return exito, r


    def enviarPedidosPendientesABot(self):
        exito, mensaje = self.obtenerPedidosPendientes()
        if exito is True:
            return str(mensaje)
        else:
            return mensaje


    def crearJSONClaroShopParaBase(self, noPedido):
        try:
            detallesPedido = self.obtenerDetallesPedido(noPedido)
            if type(detallesPedido) == str:
                raise Exception(detallesPedido)
            print('++++++++++++------------------------------')
            print(detallesPedido)
            print('++++++----------------------------------')
            order = {}
            # order['orderId'] -> generado por sql server
            order['marketplaceId'] = 1  # 2 -> claro shop
            # print('-----------------DetallesPedido')
            # print(json.dumps(detallesPedido, indent=4))
            # print('-----------------DetallesPedido')
            list_status = seed2.get_list_status_Turtle()
            estatusPedido = detallesPedido['estatuspedido']
            urlOrder = 'https://selfservice.claroshop.com/pedidos/'
            if estatusPedido['estatus'] == 'Pendiente' or estatusPedido['estatus'] == 'pendiente':
                order['status'] = list_status[0]
                urlOrder = urlOrder + 'pendientes'
            elif estatusPedido['estatus'] == 'Por embarcar con Proveedor' or estatusPedido['estatus'] == 1 or estatusPedido['estatus'] == 'Validacion Guia' or estatusPedido['estatus'] == 'Parcialmente surtido':
                order['status'] = list_status[2]
                urlOrder = urlOrder + 'embarcados'
            elif estatusPedido['estatus'] == 'Entregado Parcial':
                order['status'] = list_status[3]
                urlOrder = urlOrder + 'entregados'
            elif estatusPedido['estatus'] == 'Entregado':
                order['status'] = list_status[4]
                urlOrder = urlOrder + 'entregados'
            else:
                print('-----------Nuevo status descubierto--------\n' + estatusPedido['estatus'] + '\n-----------------------')

            order['creationDate'] = estatusPedido['fechacolocado']
            order['marketplaceOrderId'] = noPedido

            order['orderURL'] = urlOrder
            order['operationIds'] = ''

            productosDePedido = detallesPedido['productos']
            productosSinRepetir = {}
            paidAmound = 0.0
            envios = 0.0
            for productoDePedido in productosDePedido:
                transactionid = productoDePedido['transactionid']
                guia = productoDePedido['guia']
                # print('muuuu')
                if transactionid in productosSinRepetir:
                    # print('===========')
                    # print(productosSinRepetir)
                    # print('++++++++++++')
                    productosSinRepetir[transactionid]['units'] = productosSinRepetir[transactionid]['units'] + 1
                    paidAmound = paidAmound + float(productoDePedido['importe'])
                    # print('oooooo')
                else:
                    productoJSON = self.obtenerDetallesProducto(transactionid)
                    if type(detallesPedido) == str:
                        raise Exception(productoJSON)
                    # print('productoJSON*****************************')
                    # print(json.dumps(productoJSON, indent=4))
                    # print('*****************************')
                    if productoJSON['fulfillment'] is True:
                        order['FulfillmentChannelId'] = 0
                    else:
                        order['FulfillmentChannelId'] = 1
                    if productoDePedido['skuhijo'] == '0':
                        skuAux = productoJSON['skupadre']
                        modelAux = productoJSON['skupadre']
                        variations = []
                    else:
                        skuAux = productoDePedido['skuhijo']
                        modelAux = productoDePedido['skuhijo']
                        variaciones = productoDePedido['producto'].replace(
                            productoJSON['nombre'], "")
                        # print('variaciones=========================')
                        # print(variaciones)
                        # print('variaciones=========================')
                        variaciones = variaciones.split(':')[1].strip().split('/')
                        color = variaciones[0]
                        talla = variaciones[1]
                        variacionesDict = {
                            'talla': talla,
                            'color': color
                        }

                        variations = variacionesDict
                    titleAux = productoJSON['nombre']
                    fotosAux = productoJSON['fotos']
                    fotosAuxJSON = fotosAux[0]
                    photoAux = fotosAuxJSON['url']
                    brandAux = productoJSON['marca']
                    importeAux = float(productoDePedido['importe'])
                    envioAux = float(productoDePedido['envio'])
                    unitAux = 1
                    idpedidorelacion = productoDePedido['idpedidorelacion']
                    # obtenerColoresDeProducto('3847314')
                    paidAmound = importeAux
                    envios = envioAux
                    receivedAmount = paidAmound - envios
                    publicationId = productoJSON["transactionid"]
                    publicationStock = productoJSON["cantidad"]
                    publicationPrice = productoJSON["preciopublicobase"]
                    publicationPrice = productoJSON["preciopublicobase"]
                    publicationTime = productoJSON["embarque"]
                    publicationStatus = productoJSON["estatus"]
                    productoNuevo = {'sku': skuAux, 'title': titleAux, 'photo': photoAux, 'brand': brandAux, 'model': modelAux, 'units': unitAux, 'operationId': idpedidorelacion, 'variations': variations,
                                    'paidAmount': paidAmound, 'shipping': envios, 'fee': 0, 'receivedAmount': receivedAmount,
                                    "publicationId": publicationId,
                                    "publicationStock": publicationStock,
                                    "publicationPrice": publicationPrice,
                                    "publicationTime": publicationTime,
                                    "publicationStatus": publicationStatus}
                    productosSinRepetir[transactionid] = productoNuevo

            order['products'] = productosSinRepetir.values()
            order['shippingInfoNumber'] = guia
            order['paidAmount'] = round(paidAmound + envios, 2)
            order['fee'] = round(paidAmound * 0.14, 2)
            order['shipping'] = round(envios, 2)
            order['receivedAmount'] = round(
                (order['paidAmount'] - envios) * 0.86, 2)
            order['comments'] = ''
            datosEnvio = detallesPedido['datosenvio']
            # print(datosEnvio)
            direccionCompleta = f'{datosEnvio["direccion"]}, {datosEnvio["colonia"]}, {datosEnvio["del/municipio"]}'
            direccionCompleta = direccionCompleta[:250]
            clientDict = {
                "marketplaceId": 1,
                "marketplaceClientId": "",
                "registrationDate": "",
                "city": datosEnvio['ciudad'],
                "state": datosEnvio['estado'],
                "nickname": '',
                "name": datosEnvio['entregara'],
                "score": "",
                "zipCode": datosEnvio["cp"],
                "phoneNumber": "",
                "email": "",
                "address": direccionCompleta,
            }
            order['client'] = clientDict
            # print("============================================")
            # print(order)
            # print("============================================")
            return order
        except Exception as e:
            
            print("ZZZZZZZZZZZZZZZZZZZZZZZZZZZZ")
            traceback.print_exc()
            print('pppppppppppppppppppppppppppp')
            return None


    def obtenerDetallesProducto(self, idProducto):
        try:
            urlProducto = self.crearURLProducto()
            headerGET = self.regresarHeaderGET()
            urlGET = urlProducto + '/' + str(idProducto)
            r = requests.get(urlGET, headers=headerGET)
            if r.status_code != 200:
                raise Exception(f'Status code: {str(r.status_code)}')
            rJson = r.json()
            return rJson['producto']
        except Exception as e:
            return str(e)


    def obtenerDetallesPedido(self, noPedido):
        try:
            urlPedidos = self.crearURLPedidos()
            headerGET = self.regresarHeaderGET()
            parametros = {'action': 'detallepedido', 'nopedido': noPedido}
            print('09999999999999999999999999999999999999999999999999991111111111111111111')
            print(urlPedidos)
            print(headerGET)
            print(parametros)
            print('09999999999999999999999999999999999999999999999999991111111111111111111')
            r = requests.get(urlPedidos, params=parametros, headers=headerGET)
            print('dddddddddddddddddddddddddd')
            print(r)
            print(r.text)
            print('dddddddddddddddddddddddddd')
            if r.status_code != 200:
                raise Exception(f'Status code: {str(r.status_code)}')
            rJson = r.json()
            return rJson
        except Exception as e:
            return str(e)


    def retornarPedidosUltimosDias(self, days):
        msg = None
        urlFiltros = self.crearURLPedidosFiltros()
        headersPedidosFechas = self.headersFiltros()
        parametrosFiltroPendientes = self.parametrosFiltros('pendientes', days)
        parametrosFiltroEmbarcados = self.parametrosFiltros('embarcados', days)
        parametrosFiltroEntregados = self.parametrosFiltros('entregados', days)
        noSPedidosPendientes, msg = self.retornarPedidosPorFechaPendientes(
            urlFiltros, headersPedidosFechas, parametrosFiltroPendientes)
        # print(noSPedidosPendientes)
        noSPedidosEmbarcados, msg = self.retornarPedidosPorFechaEmbarcados(
            urlFiltros, headersPedidosFechas, parametrosFiltroEmbarcados)
        # print(noSPedidosEmbarcados)
        noSPedidosEntregados, msg = self.retornarPedidosPorFechaEntregados(
            urlFiltros, headersPedidosFechas, parametrosFiltroEntregados)
        # print(noSPedidosEntregados)
        pedidosGeneral = []
        pedidosGeneral.extend(noSPedidosPendientes)
        pedidosGeneral.extend(noSPedidosEmbarcados)
        pedidosGeneral.extend(noSPedidosEntregados)
        return pedidosGeneral, msg


    def headersFiltros(self):
        return {'Pragma': 'akamai-x-cache-on, akamai-x-cache-remote-on, akamai-x-check-cacheable, akamai-x-get-cache-key, akamai-x-get-extracted-values, akamai-x-get-nonces, akamai-x-get-ssl-client-session-id, akamai-x-get-true-cache-key, akamai-x-serial-no', 'X-Akamai-Debug': 'ON'}


    def parametrosFiltros(self, action, days):
        fechaFinal, fechaInicio = self.obtenerUltimosDiasFormatoYYYYMMDD(days)
        # fechaInicio = dt.datetime(2022, 8, 23)
        # fechaFinal = dt.datetime(2022, 8, 25)
        parametrosPedidosFecha = {'action': action,
                                'date_start': fechaInicio, 'date_end': fechaFinal}
        return parametrosPedidosFecha


    def obtenerUltimosDiasFormatoYYYYMMDD(self, days):
        hoy = datetime.today()
        hoyFormato = hoy.strftime("%Y-%m-%d")
        cincoDias = hoy - timedelta(days=days)
        CincoDiasFormato = cincoDias.strftime("%Y-%m-%d")
        return hoyFormato, CincoDiasFormato


    def retornarPedidosPorFechaPendientes(self, urlFiltros, headersPedidosFechas, parametrosFiltroPendientes):
        try:
            msg = None
            noSPedidos = []
            r = requests.get(urlFiltros, headers=headersPedidosFechas,
                            params=parametrosFiltroPendientes)
            if r.status_code != 200:
                print(r.text)
                msg = "Ocurrio un error al obtener los pedidos pendientes de claro shop"
                raise Exception(f'status code {str(r.status_code)}')
            rJson = r.json()
            listaPedidos = rJson['0']['listapendientes']
            # print(listaPedidos)
            for pedido in listaPedidos:
                noSPedidos.append(pedido['nopedido'])
        except Exception as e:
            noSPedidos = []
            print(
                f'Ocurrio un error al obtener los pedidos pendientes de claro shop: {str(e)}')
            msg = "Ocurrio un error al obtener los pedidos pendientes de claro shop"
        finally:
            return noSPedidos, msg


    def retornarPedidosPorFechaEmbarcados(self, urlFiltros, headersPedidosFechas, parametrosFiltroEmbarcados):
        try:
            msg = None
            noSPedidos = []
            r = requests.get(urlFiltros, headers=headersPedidosFechas,
                            params=parametrosFiltroEmbarcados)
            print('eeeeeeeeeeeeeeeeeee22222222222')
            print(r)
            print(r.text)
            print('eeeeeeeeeeeeeeeeeee222222222222222')
            if r.status_code != 200:
                msg = "Ocurrio un error al obtener los pedidos pendientes de claro shop"
                raise Exception(f'status code {str(r.status_code)}')
            rJson = r.json()
            listaPedidos = rJson['0']['listaguiasautomaticas']
            # print(listaPedidos)
            for pedido in listaPedidos:
                noSPedidos.append(pedido['nopedido'])
        except Exception as e:
            noSPedidos = []
            print(f'Ocurrio un error al obtener los pedidos embarcados: {str(e)}')
            msg = "Ocurrio un error al obtener los pedidos pendientes de claro shop"
        finally:
            return noSPedidos, msg


    def retornarPedidosPorFechaEntregados(self, urlFiltros, headersPedidosFechas, parametrosFiltroEntregados):
        try:
            msg = None
            noSPedidos = []
            r = requests.get(urlFiltros, headers=headersPedidosFechas,
                            params=parametrosFiltroEntregados)
            print('eeeeeeeeeeeeeeeeeee33333333333333')
            print(r)
            print(r.text)
            print('eeeeeeeeeeeeeeeeeee333333333333333333333')
            if r.status_code != 200:
                msg = "Ocurrio un error al obtener los pedidos pendientes de claro shop"
                raise Exception(f'status code {str(r.status_code)}')
            rJson = r.json()
            listaPedidos = rJson['0']['listaentregados']
            # print(listaPedidos)
            for pedido in listaPedidos:
                noSPedidos.append(pedido['nopedido'])
        except Exception as e:
            noSPedidos = []
            print(f'Ocurrio un error al obtener los pedidos entregados: {str(e)}')
            msg = "Ocurrio un error al obtener los pedidos pendientes de claro shop"
        finally:
            return noSPedidos, msg

    # En la API de claroShop todos los colores se asocian con productos y las tallas se asocian con colores,
    # y las tallas se asocian con colores


    def agregarColorAProducto(self, idDelProducto, nombreColor, estatus):
        # POST para crear colores
        # nuevoColor = json.loads(productoString)
        nuevoColor = {
            'idproducto': idDelProducto,
            'nombre': nombreColor,
            'estatus': estatus
        }
        urlPOST = self.crearURLColores()
        # producto = armarNuevoProducto()
        headersPOST = self.regresarHeaderPOSTAndPUT()
        try:
            r = requests.post(urlPOST, data=nuevoColor, headers=headersPOST)
            # print(r.status_code)
            if r.status_code == 200:
                r = r.json()
            else:
                r = self.http_status(r.status_code)
        except Exception as e:
            r = "No se pudo ejecutar la petición," + str(e)
        # messagebox.showinfo("showinfo", r)
        return r


    def obtenerColoresDeProducto(self, idProducto):
        urlColores = self.crearURLColores()
        headerGET = self.regresarHeaderGET()
        urlColores = urlColores + '/' + idProducto
        r = requests.get(urlColores, headers=headerGET)
        rJson = r.json()
        # print('Detalles---------------------')
        # print(rJson)
        # print('Detalles---------------------')
        return rJson


    def obtenerTallasDeColor(self, idColor):
        urlTallas = self.crearURLTallas()
        urlTallas = urlTallas + '/' + idColor
        r = requests.get(urlTallas)
        rJson = r.json()
        # print('Detalles---------------------')
        # print(rJson)
        # print('Detalles---------------------')
        return rJson


    def agregarTallaAColor(self, idColor, talla, sku, ean, estatus, stock):
        # POST para crear colores
        # nuevoColor = json.loads(productoString)
        nuevaTalla = {
            'idcolor': idColor,
            'talla': talla,
            'sku': sku,
            'ean': ean,
            'estatus': estatus,
            'stock': stock,
        }
        urlTallas = self.crearURLTallas()
        # producto = armarNuevoProducto()
        headersPOST = self.regresarHeaderPOSTAndPUT()
        try:
            r = requests.post(urlTallas, data=nuevaTalla, headers=headersPOST)
            # print(r.status_code)
            if r.status_code == 200:
                r = r.json()
                # print(rJson)
            else:
                r = self.http_status(r.status_code)
        except Exception as e:
            r = "No se pudo ejecutar la petición," + str(e)
        # messagebox.showinfo("showinfo", r)
        return r


    def get_shipping_label(self, market, id_pedido, id_guia):
        abort(make_response(jsonify({'errores': 'Aun no es posible obtener la información de las guias de Claro Shop mediante mercaleader'}), 501))
        """print('here')
        urlGuia = self.crearURLGuia_claro(market)
        print(id_guia)
        # dinamizar mensajeria
        if len(id_guia) == 13:
            mensajeria = "FEDEX"
        else:
            mensajeria = "DHL"
        parametros = {
            "guia": id_guia,
            "mensajeria": mensajeria
        }

        print('here1')
        headerURL = f"/{id_pedido}"
        urlGuia = urlGuia + headerURL
        print(urlGuia)
        headerGet = self.regresarHeaderGET()
        r = requests.get(urlGuia, params=parametros, headers=headerGet)
        # print(r.content)
        rJson = r.json()
        print(json.dumps(rJson, indent=3))
        print(r.status_code)
        if r.status_code != 200:
            return make_response('Proceso incorrecto, id erróneo', 400)
        else:
            if rJson['guia']['base64'] is None:
                return make_response('Proceso incorrecto, guía no encontrada', 400)
            else:
                contenido = base64.b64decode(rJson['guia']['base64'])
                # with open(f'{path}/{id_guia}.pdf', 'wb') as file:
                #     file.write(contenido)
                # rJson = r.json()
                # return make_response('Proceso exitoso', 200)
                mem = io.BytesIO(contenido)
                return send_file(mem, download_name=f'{id_guia}.pdf', as_attachment=False)"""

    def get_shipping_info(self, shipping_number):
        abort(make_response(jsonify({'errores': 'Aun no es posible obtener la información de las guias de Claro Shop mediante mercaleader'}), 501))
