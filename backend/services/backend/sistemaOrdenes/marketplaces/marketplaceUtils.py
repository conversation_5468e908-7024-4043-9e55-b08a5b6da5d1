from .amazon import Amazon
# from .claro_shop import T1Comercios
from .mercado_libre import Mercado_libre
from .walmart import Walmart
from sistemaOrdenes.app.models.Models import MarketplaceGroup, SupportedMarketplaceGroup

scope = {
    'marketplaceGroupCredentials': {},
    'supportedMarketplaceGroup': {},
    'marketplaces': {
        'supportedMarketplace': {}
    }
}

def get_marketplace_class(supported_marketplace_group_name):

    # constructor con credenciales
    if supported_marketplace_group_name == 'Mercado Libre':
        marketplace_obj = Mercado_libre(marketplace_group_name=supported_marketplace_group_name)
    elif supported_marketplace_group_name == 'Amazon':
        marketplace_obj = Amazon(marketplace_group_name=supported_marketplace_group_name)
    #elif supported_marketplace_group_name == 'T1comercios':
    #    marketplace_obj = T1Comercios(marketplace_group_name=supported_marketplace_group_name)
    elif supported_marketplace_group_name == 'Walmart':
        marketplace_obj = Walmart(marketplace_group_name=supported_marketplace_group_name)
    else:
        print(f'{supported_marketplace_group_name}: marketplace no reconocido')
        marketplace_obj = None
    return marketplace_obj


def get_marketplace_obj(session, supported_marketplace_group_name):
    marketplace_db = session.query(MarketplaceGroup).join(SupportedMarketplaceGroup).filter(SupportedMarketplaceGroup.Name == supported_marketplace_group_name).first()
    if not marketplace_db:
        raise Exception("No se encontro el grupo de mercado libre")
    marketplace_db_serialized = marketplace_db.serialize(scope=scope)
    marketplace_group_id = marketplace_db_serialized['id']
    supported_marketplace_credentials = marketplace_db_serialized['marketplaceGroupCredentials'] #c
    supported_marketplace_group_name = marketplace_db_serialized['supportedMarketplaceGroup']['name']
    marketplace_obj = get_marketplace_class(supported_marketplace_group_name)
    marketplace_obj.set_credentials_and_id_marketplace(credentials_param=supported_marketplace_credentials, marketplace_id=marketplace_group_id)
    marketplace_obj.set_session(session)
    return marketplace_obj