import json
import requests
from sistemaOrdenes.ordersUpdater.customExceptions import Scope<PERSON>erException
from datetime import datetime, timedelta
import pytz
import math
from .Marketplace import Marketplace
from sistemaOrdenes import configs
from io import BytesIO

def format_date_days_ago(days, timezone=None, keep_hour=False):
    if timezone is not None:
        now = datetime.now(pytz.timezone(timezone))
    else:
        now = datetime.now()
    result = now - timedelta(days=days)
    # print(result.strftime("%Y-%m-%dT%H:%M:%S.000-00:00"))
    if keep_hour:
        return result.isoformat()  # strftime("%Y-%m-%dT%H:%M:%S.000%Z")
    return result.isoformat()  # strftime("%Y-%m-%dT00:00:00.000%Z")

class Mercado_libre(Marketplace):
    
    # attributes-------------------------------------------------------------------------------
    GRANT_TYPE = 'refresh_token'
    # abstacts implementation--------------------------------------------------------------------------------------------------------------------

    def __init__(self, marketplace_group_name):
        super().__init__(marketplace_group_name)


    def autenticate(self):
        pass


    def validate_credentials(self):
        pass
    


    def set_credentials_and_id_marketplace(self, credentials_param=None, marketplace_id=None,  marketplaces=None):
        #self.CLIENT_ID = credentials_param['clientId']
        #self.CLIENT_SECRET = credentials_param['clientSecret']
        #self.REFRESH_TOKEN = credentials_param['refreshToken']
        #self.ACCESS_TOKEN = credentials_param['accessToken']
        #self.AUTHORIZATION = {"Authorization": f"Bearer {self.ACCESS_TOKEN}"}
        #self.MARKETPLACE_ID = marketplace_id
        #self.ML_SELLER_ID = self.REFRESH_TOKEN.split('-')[-1] if (type(self.REFRESH_TOKEN) is str) else None 
        #self.REGION = "mlm"

        self.CLIENT_ID = "7976131859386925"
        self.CLIENT_SECRET = "jBcg9lOQ1jPaxCHxKhHovcWzTqmPSYC3"
        self.REFRESH_TOKEN = None
        self.ACCESS_TOKEN = "APP_USR-7976131859386925-071701-376d33ff49bd2d9596af53095d1dfe87-2564790276"
        self.AUTHORIZATION = {"Authorization": f"Bearer {self.ACCESS_TOKEN}"}
        self.MARKETPLACE_ID = marketplace_id
        self.ML_SELLER_ID = "2564790276"
        self.REGION = "mlm"

    def update_orders_last_days(self, list_status, days: int = configs.CANTIDAD_DE_DIAS_A_OBTENER_ML) -> tuple[list, dict]:
        # Getting orders from API
        api_orders = self.get_api_orders(days)
        print(f"procesando {len(api_orders)} órdenes")
        different_operations = []
        different_orders = []
        for order in api_orders:
            order_id = self.get_order_id(order)
            if order_id not in different_orders:
                different_orders.append(order_id)
                different_operations.append(order['id'])
        print(f"órdenes distintas: {len(different_orders)}")
        # Getting status list from turttle api
        print('list_status')
        print(list_status)
        print('list_status')
        # Decaring list for success orders and list for orders with errors
        arrayOrders = []
        error_orders = {
            'ids': [],
            'details': []
        }
        # Iterate over orders for set format
        for order_id in different_operations:
            try:
                order_id = str(order_id)
                order_info = self.get_turtle_format_order_info(order_id, list_status, "")
                arrayOrders.append(order_info)
            except Exception as e:
                print("00Mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm")
                print(str(e))
                print("00Mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm")
                
                error_orders['ids'].append(str(order_id))
                error_orders['details'].append(f'{str(order_id)} => {str(e)}')
        return arrayOrders, error_orders
    
    #others metods--------------------------------------------------------------------------------------------------------
    def get_api_orders(self, days):
        date_from = format_date_days_ago(days, configs.TIME_ZONE)
        date_to = format_date_days_ago(-1, configs.TIME_ZONE, keep_hour=True)
        print(f"Ordenes desde {date_from} hasta {date_to}")
        offset = 0
        total_pages = 1
        total_results = []
        print("Consiguiendo las paginas de ordenes...")
        while offset < total_pages:
            url = f"https://api.mercadolibre.com/orders/search?seller={self.ML_SELLER_ID}&order.date_closed.from={date_from}&order.date_closed.to={date_to}&offset={offset}&sort=date_desc"
            response = self.ejecutar_con_validacion_de_token(requests.get, url)
            total_pages = response['paging']['total']
            total_results.extend(response['results'])
            offset += 1
        return total_results


    def get_order_id(self, order_info):
        order_id = order_info['pack_id']
        if order_id is None:
            order_id = order_info['id']
        return order_id


    def get_order_items_format(self, order_items, operation_id):
        order_items_format = []
        for order_item in order_items:
            item_in_shipment = {
                "item": order_item['item']['id'],
                "variation_id": order_item['item']['variation_id'],
                "quantity": order_item['quantity'],
                "operation_id": operation_id,
                "fee_per_unit": order_item['sale_fee'],
                "paidAmount_Oder": order_item['unit_price'],
                "seller_sku": order_item['item']['seller_sku'],
            }
            order_items_format.append(item_in_shipment)
        return order_items_format

    def get_items_in_shipment(self, shipment_id, order_id):
        items = []
        url = f"https://api.mercadolibre.com/shipments/{shipment_id}/items"
        response = self.ejecutar_con_validacion_de_token(requests.get, url)
        for item in response:
            url = f"https://api.mercadolibre.com/orders/{item['order_id']}"
            order_info = self.ejecutar_con_validacion_de_token(requests.get, url)
            fee_per_unit = order_info['order_items'][0]['sale_fee']
            paidAmount_Oder = order_info['order_items'][0]['unit_price']
            seller_sku = order_info['order_items'][0]['item']['seller_sku']
            try:
                variation_id = item['variation_id']
            except Exception as e:
                print(str(e))
                variation_id = ""
            if variation_id is None:
                variation_id = ""
            items.append({
                "item": item['item_id'],
                "variation_id": variation_id,
                "quantity": item['quantity'],
                "operation_id": item['order_id'],
                "fee_per_unit": fee_per_unit,
                "paidAmount_Oder": paidAmount_Oder,
                "seller_sku": seller_sku,
            })
        return items


    def get_turtle_format_order_info(self, operation_id, list_status, comments=""):
        final_order_info = {}
        url = f"https://api.mercadolibre.com/orders/{operation_id}"
        order_info = self.ejecutar_con_validacion_de_token(requests.get, url)
        order_id = self.get_order_id(order_info)
        shipping_id = order_info['shipping']['id']
        if shipping_id is not None:
            url = f'https://api.mercadolibre.com/shipments/{shipping_id}'
            try:
                shipment_info = self.ejecutar_con_validacion_de_token(requests.get, url)
                messageGuide = "Guia disponible"
                statusGuide = True
                items_in_shipment = self.get_items_in_shipment(shipping_id, order_id)
            except  Exception as e:
                shipment_info =    None
                messageGuide = "Guia no disponible"
                statusGuide = False
                items_in_shipment = self.get_order_items_format(order_info['order_items'], operation_id)
             #message_status_guide********************************** 
             
            
        else:
            items_in_shipment = self.get_order_items_format(order_info['order_items'], operation_id)
            shipment_info = None #ddddddddddddddddd
            messageGuide = "Guía no disponible"
            statusGuide = False
        if 'no_shipping' in order_info['tags']:
            print('mmmmmmmmmmmmmmmmmm-1')
            print(order_id)
            print('mmmmmmmmmmmmmmmmmm-2')
            print('0000000000000000000000000000---1')
            print(order_info)
            print('0000000000000000000000000000---2')
        shipping = self.get_shipment_cost(shipment_info)['shipping']
        status = self.define_status(order_info, shipment_info, list_status)
        if status == 5:
            # print(order_info["mediations"])
            try:
                url = f"https://api.mercadolibre.com/v1/claims/{order_info['mediations'][0]['id']}"
                claim_details = self.ejecutar_con_validacion_de_token(requests.get, url)
                #######3
                url = f"https://api.mercadolibre.com/post-purchase/sites/MLM/v2/reasons/{claim_details['reason_id']}"
                reason = self.ejecutar_con_validacion_de_token(requests.get, url)
                comments += reason['detail']
            except Exception as e:
                print(str(e))
                comments += ""
        clientInfo = self.get_client_info(order_info, shipment_info)
        creationDate = order_info['date_closed']
        orderUrl = f'https://www.mercadolibre.com.mx/ventas/{order_id}/detalle'
        operation_ids = self.get_operation_ids(order_info)
        products = self.form_products_json(items_in_shipment, shipping)
        fee = self.sum_order_fee(products)
        paid_amount = self.get_paid_amount(products)
        fulfillmentChannel = products[0]['fulfillmentChannel']
        for p in products:
            p.pop("fulfillmentChannel", None)
            p.pop("fee_per_unit", None)
        receivedAmount = (paid_amount - fee - shipping) if (shipping is not None)  else None
        final_order_info = {
            "marketplaceId": self.MARKETPLACE_ID,
            "FulfillmentChannelId": fulfillmentChannel,
            "status": status,
            "creationDate": creationDate,
            "marketplaceOrderId": str(order_id),
            "orderURL": orderUrl,
            "operationIds": operation_ids,
            "products": products,
            "paidAmount": paid_amount,
            "fee": fee,
            "shipping": shipping,
            "receivedAmount": receivedAmount,
            "shippingInfoNumber": shipping_id,
            "messageGuide": messageGuide,
            "statusGuide": statusGuide,
            "comments": comments,
            "client": clientInfo
        }
        return final_order_info


    def ejecutar_con_validacion_de_token(self, request, url, auth=None, params=None, data=None):
        res = request(url, auth=auth, params=params, data=data, headers=self.AUTHORIZATION)
        if 400 < res.status_code <= 403:
            self.renovar_access_token()
            res = self.manage_responses(request, url, auth=auth, params=params, data=data, headers=self.AUTHORIZATION)
        if not 200 <= res.status_code <= 202:
            raise Exception(f"Mercado libre: Error al realizar la petición {url}: -{res.text}- <{res.status_code}>")
        return res.json()

    def ejecutar_con_validacion_de_token_content(self, request, url, auth=None, params=None, data=None):
        res = request(url, auth=auth, params=params, data=data, headers=self.AUTHORIZATION)
        if 400 < res.status_code <= 403:
            self.renovar_access_token()
            res = self.manage_responses(request, url, auth=auth, params=params, data=data, headers=self.AUTHORIZATION)
        if not 200 <= res.status_code <= 202:
            raise Exception(f"Mercado libre: Error al realizar la petición {url}: -{res.text}- <{res.status_code}>")
        return res.content

    def renovar_access_token(self):
        url = f'https://api.mercadolibre.com/oauth/token?grant_type={self.GRANT_TYPE}&client_id={self.CLIENT_ID}&client_secret={self.CLIENT_SECRET}&refresh_token={self.REFRESH_TOKEN}'
        response = self.manage_responses(requests.post, url)
        response = response.json()
        self.ACCESS_TOKEN = response['access_token']
        self.AUTHORIZATION = {"Authorization": f"Bearer {self.ACCESS_TOKEN}"}
        #self.AUTHORIZATION = {"Authorization": f"Bearer APP_USR-7976131859386925-071701-376d33ff49bd2d9596af53095d1dfe87-2564790276"}
        self.update_marketplace_credentials_patch()
        


    def manage_responses(self, request, url, stream=False, auth=None, params=None, data=None, headers=None):
        res = request(url, stream=stream,auth=auth, params=params, data=data, headers=headers)
        if 400 < res.status_code <= 403:
            raise ScopeUSerException(marketplace_id=self.MARKETPLACE_ID)
        if not 200 >= res.status_code <= 202:
            print('pppppppppppppppppppppññññ')
            print(res.text)
            print('pppppppppppppppppppppññññ')
            raise Exception(f"Mercado libre: Error al realizar la petición: {url}")
        return res


    def get_shipment_cost(self, shipment_info):
        try:
            costo_envio = shipment_info['shipping_option']['list_cost']
        except Exception as e:
            #print('eexeeeption1')
            #print(str(e))
            #print('eexeeeption2')

            return {
                "shipping": None,
                "order_cost": None
            }

        pagado_ml = shipment_info['shipping_option']['cost']
        costo_a_tu_cargo = costo_envio - pagado_ml
        order_cost = shipment_info['order_cost']
        #print("qqqqqqqqqq1")
        #print(costo_envio)
        #print(pagado_ml)
        #print(costo_a_tu_cargo)
        #print("qqqqqqqqqq2")
        
        return {
            "shipping": costo_a_tu_cargo,
            "order_cost": order_cost
        }



    def form_products_json(self, products, shippingCost):
        # print(json.dumps(products, indent=4))
        # publication_ids = map(lambda p :p['item'], products)
        publications_info = self.get_publications_info(products)
        final_json = []
        #skus_set = set()
        divValue = len(publications_info)
        #print(divValue)
        for index, pub in enumerate(publications_info):
            # print("############################")
            # print(json.dumps(pub, indent=4))
            # pub = pub['body']
            seller_sku = pub['seller_sku']
            brand = ""
            model = ""
            title = pub['title']
            photo = pub['pictures'][0]["url"]

            paidAmount = float(products[index]["paidAmount_Oder"])*float(products[index]["quantity"])
            fee = float(products[index]["fee_per_unit"])*float(products[index]["quantity"]) if products[index]["fee_per_unit"] else None
            if shippingCost is not None:
                shipping = shippingCost / divValue if shippingCost > 0 else 0
                receivedAmount = paidAmount - shipping - fee if fee else None
            else:
                shipping = None
                receivedAmount = None
            publicationPrice = pub["price"]
            publicationStock = pub["available_quantity"]
            publicationId = pub["id"]
            # publicationTime = pub["start_time"]
            publicationTime = None
            publicationStatus = pub["status"]
            variations = []
            fulfillment_channel = 1 if pub['shipping']['logistic_type'] == 'fulfillment' else 2
            for att in pub['attributes']:
                if att['id'] == 'SELLER_SKU':
                    sku = str(att['value_name'])
                elif att['id'] == "BRAND":
                    brand = att['value_name']
                elif att['id'] == "MODEL":
                    model = str(att['value_name'])
            try:
                variations = pub['variationInfo']
                # print(json.dumps(variations, indent=4))
            except Exception as e:
                #print(str(e))
                variations = []
            # sku = self.generate_sku(sku, model, brand, index)
            #if sku in skus_set:
            #    sku = f'{sku}__{index}'
            #skus_set.add(sku)

            final_json.append({
                "seller_sku": seller_sku,
                "title": title,
                "variations": variations,
                "photo": photo,
                "brand": brand,
                "model": model,
                "units": products[index]["quantity"],
                "operationId": products[index]["operation_id"],
                "paidAmount": paidAmount,
                "fee": fee,
                # "fee": products[index]["sale_fee"],
                "shipping": shipping,
                "receivedAmount": receivedAmount,
                "fee_per_unit": products[index]["fee_per_unit"],
                "fulfillmentChannel": fulfillment_channel,
                "publicationId": publicationId,
                "publicationStock": publicationStock,
                "publicationPrice": publicationPrice,
                "publicationTime": publicationTime,
                "publicationStatus": publicationStatus
            })
        return final_json


    def define_status(self, order_info, shipment_info, list_status):
        # print(json.dumps(order_info, indent=4))
        # print(json.dumps(shipment_info, indent=4))

        shipment_status_arr = {
            'to_be_agreed': list_status[7],
            'pending': list_status[1],
            'handling': list_status[1],
            'ready_to_ship': list_status[2],
            'shipped': list_status[3],
            'delivered': list_status[4],
            'not_delivered': list_status[6],
            'cancelled': list_status[6]
        }

        shipment_substatus_arr = {
            "in_packing_list": list_status[3],
            "dropped_off": list_status[3],
            "picked_up": list_status[3],
            "in_hub": list_status[3],
        }
        status_arr = {
            "confirmed": list_status[0],
            "payment_required": list_status[0],
            "payment_in_process": list_status[0],
            "partially_paid": list_status[0],
            "approved": list_status[3],
            "cancelled": list_status[5],
            "partially_refunded": list_status[9],
            "refunded": list_status[9],
            "rejected": list_status[10],
            "charged_back": list_status[11]
        }
        order_status = order_info['status']

        if order_status == 'paid':
            #
            try:
                shipment_status = shipment_info['status']
                shipment_substatus = shipment_info['substatus']
                # print(shipment_substatus)
            except Exception as e:
                return 10 if order_info['fulfilled'] else 16
            status = shipment_status_arr[shipment_status]
            if status == 4:
                if order_info['mediations'] != []:
                    url = f"https://api.mercadolibre.com/v1/claims/{order_info['mediations'][0]['id']}"
                    claim_details = self.ejecutar_con_validacion_de_token(requests.get, url)
                    if claim_details['type'] == 'returns':
                        status = list_status[6]
        elif order_status == 'cancelled' and "delivered" in order_info['tags']:
            status = list_status[6]
        else:
            status = status_arr[order_status]
        if status == 2:
            #
            try:
                shipment_status = shipment_info['status']
                shipment_substatus = shipment_info['substatus']
                # print(shipment_substatus)
            except Exception as e:
                    return 2
            #
            try:
                status = shipment_substatus_arr[shipment_substatus]
            except Exception as e:
                print(str(e))
        return status        


    def get_client_info(self, order_info, shipment_info):
        # print(order_info['buyer']['id'])
        url = f"https://api.mercadolibre.com/users/{order_info['buyer']['id']}"
        user_info = self.ejecutar_con_validacion_de_token(requests.get, url)
        # print(json.dumps(user_info, indent=4))
        client_address_info = self.parse_client_address_info(shipment_info)
        try:
            city = user_info['address']['city']
            state = user_info['address']['state']
        except Exception as e:
            print(str(e))
            city = ""
            state = ""
        try:
            registrationDate = user_info['registration_date'].split('.')[0] + 'Z'
        except Exception as e:
            registrationDate = ''
        try:
            score = user_info['points']
            score = int(score)
        except Exception as e:
            score = ''
        adress = f"{client_address_info['calle_numero']}, {client_address_info['neighborhood']}"
        name = f"{order_info['buyer']['first_name']} {order_info['buyer']['last_name']}"
        client_info = {
            "id": "",
            "marketplace": self.MARKETPLACE_ID,
            "marketplaceClientId": str(order_info['buyer']['id']),
            "registrationDate": registrationDate,
            "city": city,
            "state": state,
            "address": adress,
            "zipCode": client_address_info['zip_code'],
            "phoneNumber": "",
            "email": "",
            "nickname": order_info['buyer']['nickname'],
            "name": name,
            "score": score,
        }
        # print(json.dumps(client_info, indent=4))
        return client_info

    
    def get_operation_ids(self, order_info):
        operation_ids = []
        for op in order_info['payments']:
            operation_ids.append(op['id'])
        return operation_ids


    

    def sum_order_fee(self, products):
        # print(json.dumps(products, indent=4))
        try:
            fee = 0
            for prod in products:
                fee += prod['fee_per_unit'] * prod['units']
        except Exception as e:
            print(str(e))
            return -1
        return fee


    def get_paid_amount(self, products,):
        invalid_states = ['cancelled', 'rejected']
        paid_amount = 0
        for p in products:

            url = f"https://api.mercadolibre.com/orders/{p['operationId']}"
            order_info = self.ejecutar_con_validacion_de_token(requests.get, url)
            for payment in order_info['payments']:
                if payment['status'] not in invalid_states:
                    paid_amount += payment['transaction_amount']
        return paid_amount


    

    def get_publications_info(self, products):
        publications_info = []
        for product in products:
            url = f"https://api.mercadolibre.com/items/{product['item']}"
            response = self.ejecutar_con_validacion_de_token(requests.get, url)
            response['seller_sku'] = product['seller_sku']
            if product["variation_id"] != "" and product["variation_id"] is not None:
                variation_id = product["variation_id"]
                url = f"https://api.mercadolibre.com/items/{product['item']}/variations/{variation_id}"
                # print(json.dumps(variation_response['attribute_combinations'], indent=4))
                variation_response = self.ejecutar_con_validacion_de_token(requests.get, url)
                response["variationInfo"] = {
                    "variation_id" : variation_id,
                    "price" : variation_response['price'],
                    "stock" : variation_response['available_quantity']
                }

            publications_info.append(response)
        return publications_info


    def parse_client_address_info(self, shipment_info):
        client_address_info = {}
        # print(json.dumps(shipment_info, indent=4))
        shipment_keys = [
            {
                'ml_keys': ['address_line'],
                'inside_key': 'calle_numero'
            },
            {
                'ml_keys': ['zip_code'],
                'inside_key': 'zip_code'
            },
            {
                'ml_keys': ['neighborhood', 'name'],
                'inside_key': 'neighborhood'
            }
        ]
        for key_value in shipment_keys:
            try:
                value = shipment_info['receiver_address']
                for key in key_value['ml_keys']:
                    value = value[key]
                client_address_info[key_value['inside_key']] = value
            except Exception as e:
                print(str(e))
                client_address_info[key_value['inside_key']] = ''
        return client_address_info


    def get_conversation_by_pack_id(self, pack_id):
        limit = 10
        url = f"https://api.mercadolibre.com/messages/packs/{pack_id}/sellers/{self.ML_SELLER_ID}?limit={limit}&offset=0&tag=post_sale"
        conversation = self.ejecutar_con_validacion_de_token(requests.get, url)
        total = conversation['paging']['total']
        if total > limit:
            total_pages = math.ceil(total / limit)
            for page in range(1, total_pages, 1):
                offset = page * limit
                url = f"https://api.mercadolibre.com/messages/packs/{pack_id}/sellers/{self.ML_SELLER_ID}?limit={limit}&offset={offset}&tag=post_sale"
                conversation_aux = self.ejecutar_con_validacion_de_token(requests.get, url)
                conversation['messages'].extend(conversation_aux['messages'])
        del conversation['paging']
        return conversation

    def get_conversation_from_message_id(self, message_id):
        url = f"https://api.mercadolibre.com/messages/{message_id}?tag=post_sale"
        message = self.ejecutar_con_validacion_de_token(requests.get, url)
        pack_id = Mercado_libre.get_message_resource_by_name(message['messages'][0], 'packs')
        conversation = self.get_conversation_by_pack_id(pack_id)
        return conversation
    
    def get_claims_info(self, resource):
        url =  f"https://api.mercadolibre.com{resource}"
        claims = self.ejecutar_con_validacion_de_token(requests.get, url)
        return claims
    
    def get_an_order_id_from_pack_id(self, pack_id):
        url = f"https://api.mercadolibre.com/packs/{pack_id}"
        pack = self.ejecutar_con_validacion_de_token(requests.get, url)
        return pack

    def get_attachment(self, atachment_name):
        url = f'https://api.mercadolibre.com/messages/attachments/{atachment_name}?tag=post_sale&site_id=MLM'
        attachment = self.ejecutar_con_validacion_de_token_content(requests.get, url)
        return attachment
    
    def create_test_user(self):
        url = 'https://api.mercadolibre.com/users/test_user'
        site_id = { "site_id": "MLM" }
        response = self.ejecutar_con_validacion_de_token(requests.post, url, data=json.dumps(site_id))
        return response

    def get_shipping_label(self, shipping_id):
        url = f'https://api.mercadolibre.com/shipment_labels?shipment_ids={shipping_id}&savePdf=Y'
        response = self.ejecutar_con_validacion_de_token_content(requests.get, url)
        shipping_guide = BytesIO(response)
        return shipping_guide, f'{shipping_id}.pdf'

    def get_shipping_info(self, order_id):
        url = f'https://api.mercadolibre.com/shipments/{order_id}'
        shipping_info = self.ejecutar_con_validacion_de_token(requests.get, url)
        return shipping_info
    

    def get_action_guide_for_pack(self, pack_id):
        #url = f"https://api.mercadolibre.com/messages/action_guide/packs/{pack_id}"
        url = f"https://api.mercadolibre.com/messages/action_guide/packs/{pack_id}?tag=post_sale"
        response = self.ejecutar_con_validacion_de_token(requests.get, url)
        return response
    
    def send_message_using_action_guide(self, pack_id, option_type, option_id, template_id=None, text=None):
        
        if option_type == 'TEMPLATE':
            body = {
                "option_id": option_id,
                "template_id": template_id
            }
        elif option_type == 'FREE_TEXT':
            body = {
                "option_id": "OTHER",
                "text": text
            }
        else:
            return None
        url = f"https://api.mercadolibre.com/messages/action_guide/packs/{pack_id}/option?tag=post_sale"
        response = self.ejecutar_con_validacion_de_token(requests.post, url, data=json.dumps(body))
        return response

    def send_message_to_client(self, pack_id, to_user_id, text):
       
        data = {
            "from": {
                "user_id": self.ML_SELLER_ID,
            },
            "to": {
                "user_id": to_user_id,
            },
            "text": text,
            #"attachments": ["415460047_a96d8dea-38cd-4402-938e-80a1c134fc5d.pdf"]
        }
        print(pack_id, to_user_id, text)
        url = f"https://api.mercadolibre.com/messages/packs/{pack_id}/sellers/{self.ML_SELLER_ID}?tag=post_sale"
        response = self.ejecutar_con_validacion_de_token(requests.post, url, data=json.dumps(data))
        return response

    @staticmethod
    def get_message_resource_by_name(message, name):
        for resource in message['message_resources']:
            if resource['name'] == name:
                return resource['id']
        return None
