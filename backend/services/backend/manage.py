from flask.cli import FlaskGroup
import sys
from sistemaOrdenes.app import create_app
from sistemaOrdenes.app.models.db import Base, engine
from sistemaOrdenes.datos_iniciales import create_cp, create_lmo, create_new_instance, create_cp_test

app = create_app()
cli = FlaskGroup(create_app=create_app)


@cli.command("create_db")
def create_db():
    Base.metadata.drop_all(engine)
    Base.metadata.create_all(engine)


@cli.command("seed_db_cp")
def seed_db_cp():
    create_cp(name="<PERSON>", email="<EMAIL>", password="44xcUi%S")


@cli.command("seed_db_cp_test")
def seed_db_cp_test():
    create_cp_test(name="admin test user", email="<EMAIL>", password="admin_test_pass")


@cli.command("seed_db_lmo")
def seed_db_lmo():
    create_lmo(name="<PERSON>", email="<EMAIL>", password="44xcUi%S")


@cli.command("seed_new_db")
def seed_new_db():
    create_new_instance(name="<PERSON>", email="<EMAIL>", password="44xcUi%S")


@cli.command("prueba")
def prueba():
    print(sys.argv)


if __name__ == "__main__":
    cli()
