# Guía de Depuración de Rendimiento React

## Resumen del Problema Resuelto

### Síntomas Iniciales
- El componente `Row` se renderizaba infinitas veces
- Degradación del rendimiento en la tabla de pedidos
- Posible bucle de actualizaciones de Redux

### Metodología de Depuración Aplicada

#### 1. Diagnóstico Inicial
```jsx
// ❌ Problema: Componente sin optimización
export const Row = (props) => {
  // Renderizados infinitos
}

// ✅ Solución: Envolver en React.memo
export const Row = React.memo((props) => {
  // Solo re-renderiza si las props cambian
});
```

#### 2. Hook de Depuración Implementado
```jsx
const useWhyDidYouUpdate = (name, props) => {
  const previous = useRef();
  useEffect(() => {
    if (previous.current) {
      const allKeys = Object.keys({...previous.current, ...props});
      const changedProps = {};
      allKeys.forEach(key => {
        if (previous.current[key] !== props[key]) {
          changedProps[key] = {
            from: previous.current[key],
            to: props[key]
          };
        }
      });
      if (Object.keys(changedProps).length) {
        console.log('[why-did-you-update]', name, changedProps);
      } else {
        console.log('[why-did-you-update]', name, 'No se detectaron cambios en las props, pero el componente se renderizó de nuevo.');
      }
    }
    previous.current = props;
  });
};
```

#### 3. Estabilización Interna
```jsx
// ❌ Problema: Funciones recreadas en cada render
const handleClick = (event) => {
  setAnchorEl(event.currentTarget);
};

// ✅ Solución: useCallback
const handleClick = useCallback((event) => {
  setAnchorEl(event.currentTarget);
}, []);

// ❌ Problema: Arrays derivados recreados
const transformedNamesStatus = orderStatuses.map((statusObj) => ({
  ...statusObj,
  orderStatusFlag: getVariableByName(statusObj.orderStatusFlag),
}));

// ✅ Solución: useMemo
const transformedNamesStatus = useMemo(() => 
  orderStatuses.map((statusObj) => ({
    ...statusObj,
    orderStatusFlag: getVariableByName(statusObj.orderStatusFlag),
  })), [orderStatuses]
);
```

#### 4. Corrección de useEffect
```jsx
// ❌ Problema: useEffect con dependencias vacías
useEffect(() => {
  // Cargar datos
}, []); // No se actualiza cuando cambian los filtros

// ✅ Solución: Dependencias correctas
useEffect(() => {
  const fetchData = () => {
    dispatch(obtenerPedidosFiltradosAccion({
      proveedor, offset, status, statusInterno,
      initialDate, finalDate, openCloseAlignment,
      sellerMarketplaceAlignment, search
    }));
  };
  fetchData();
}, [dispatch, proveedor, offset, status, statusInterno, 
    initialDate, finalDate, openCloseAlignment, 
    sellerMarketplaceAlignment, search]);
```

## Herramientas de Monitoreo

### 1. Hook de Monitoreo de Rendimiento
```jsx
import { usePerformanceMonitor } from '../hooks/usePerformanceMonitor';

const MyComponent = (props) => {
  const monitor = usePerformanceMonitor('MyComponent', {
    warningThreshold: 10,
    timeWindow: 1000,
    logEveryRender: false
  });
  
  // Tu componente...
};
```

### 2. Monitor de Cambios en Props
```jsx
import { usePropsChangeMonitor } from '../hooks/usePerformanceMonitor';

const MyComponent = (props) => {
  const propsMonitor = usePropsChangeMonitor(props, 'MyComponent', {
    ignoreProps: ['timestamp'], // Props a ignorar
    deepCompare: false
  });
  
  // Tu componente...
};
```

## Mejores Prácticas

### 1. Optimización de Componentes
- **Usar React.memo** para componentes que reciben props complejas
- **Estabilizar funciones** con useCallback
- **Memoizar cálculos** con useMemo
- **Evitar objetos inline** en props

### 2. Gestión de useEffect
- **Incluir todas las dependencias** en el array de dependencias
- **Usar useCallback** para funciones que son dependencias
- **Separar efectos** por responsabilidad

### 3. Redux y Estado Global
- **Evitar selectors que retornan nuevos objetos** en cada llamada
- **Usar reselect** para memoizar selectors complejos
- **Normalizar el estado** para evitar actualizaciones innecesarias

### 4. Debugging
- **Usar React DevTools Profiler** para identificar componentes lentos
- **Implementar hooks de monitoreo** en desarrollo
- **Loggear cambios de estado** relevantes

## Checklist de Depuración

### Cuando un componente se renderiza demasiado:

1. **¿Está envuelto en React.memo?**
   - [ ] Sí, y las props son primitivas o estables
   - [ ] No, debería estarlo

2. **¿Las funciones están estabilizadas?**
   - [ ] useCallback aplicado a todas las funciones
   - [ ] Dependencias correctas en useCallback

3. **¿Los valores derivados están memoizados?**
   - [ ] useMemo para cálculos complejos
   - [ ] useMemo para arrays/objetos derivados

4. **¿Los useEffect tienen dependencias correctas?**
   - [ ] Todas las variables usadas están en dependencias
   - [ ] No hay dependencias faltantes o innecesarias

5. **¿Los selectors de Redux son eficientes?**
   - [ ] No retornan nuevos objetos en cada llamada
   - [ ] Usan reselect si es necesario

## Herramientas Recomendadas

1. **React DevTools Profiler** - Para identificar componentes lentos
2. **Redux DevTools** - Para monitorear cambios de estado
3. **why-did-you-render** - Librería para detectar re-renders innecesarios
4. **Hooks personalizados** - Para monitoreo continuo en desarrollo

## Casos de Uso Comunes

### Tabla con muchas filas
- Virtualización con react-window o react-virtualized
- Memoización de filas individuales
- Paginación o carga lazy

### Formularios complejos
- Separar en sub-componentes
- Usar useCallback para handlers
- Memoizar validaciones

### Listas dinámicas
- Keys estables y únicos
- Componentes de item memoizados
- Evitar índices como keys

---

**Nota**: Esta guía se basa en la resolución exitosa del problema de renderizado infinito en el componente Row de la tabla de pedidos.
