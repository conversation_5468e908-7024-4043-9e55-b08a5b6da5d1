<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="2490.092" height="1473.985" viewBox="0 0 2490.092 1473.985">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectángulo_222" data-name="Rectángulo 222" width="2490.092" height="1473.985" transform="translate(0 0.005)" fill="none"/>
    </clipPath>
  </defs>
  <g id="Grupo_309" data-name="Grupo 309" transform="translate(0 -0.005)">
    <g id="Grupo_306" data-name="Grupo 306">
      <g id="Grupo_305" data-name="Grupo 305" clip-path="url(#clip-path)">
        <path id="Trazado_9738" data-name="Trazado 9738" d="M1843.477,1223.169c63.882-23.629,128.8-45.054,195.7-58.368q25.849-5.145,51.981-8.7l-15.951,2.143c45.019-6.048,90.614-8.382,135.975-5.436q18.953,1.23,37.791,3.735l-15.951-2.144c40.751,5.5,80.842,15.724,118.771,31.708l-14.333-6.049a401.035,401.035,0,0,1,62.348,33.123,44.661,44.661,0,0,0,22.308,7.12,61.642,61.642,0,0,0,59.784-28.645l6.048-14.333a60.42,60.42,0,0,0,0-31.9l-6.048-14.333a60.3,60.3,0,0,0-21.526-21.525,396.713,396.713,0,0,0-51.341-28.277c-22.02-10.084-44.591-19.187-67.783-26.219a474.143,474.143,0,0,0-57.173-13.454,695.583,695.583,0,0,0-73.383-8.837,673.3,673.3,0,0,0-119.206,3.441,986.045,986.045,0,0,0-133.458,24.092c-44.019,11.282-87.229,25.632-129.937,41.1q-8.269,3-16.518,6.049a67.137,67.137,0,0,0-35.858,27.574,44.659,44.659,0,0,0-7.12,22.308,45.457,45.457,0,0,0,1.072,23.926l6.048,14.332a60.314,60.314,0,0,0,21.525,21.526l14.333,6.048a60.418,60.418,0,0,0,31.9,0" fill="#ffcdea"/>
        <path id="Trazado_9739" data-name="Trazado 9739" d="M1490.2,1348.192c23.446-5.79,46.685-12.389,69.735-19.592,22.94-7.169,45.692-14.927,68.31-23.055,22.757-8.178,45.377-16.729,67.936-25.438,23.124-8.927,46.183-18.018,69.267-27.05,23.118-9.045,46.262-18.028,69.516-26.72q4.256-1.592,8.517-3.168a67.124,67.124,0,0,0,35.858-27.574,44.659,44.659,0,0,0,7.12-22.308,45.453,45.453,0,0,0-1.072-23.925l-6.048-14.333a60.313,60.313,0,0,0-21.526-21.525l-14.332-6.048a60.438,60.438,0,0,0-31.9,0c-23.279,8.62-46.44,17.552-69.564,26.578-23.021,8.985-46.008,18.06-69.047,27-22.584,8.761-45.22,17.392-67.983,25.675-22.694,8.259-45.517,16.172-68.524,23.518s-46.184,14.119-69.578,20.1q-4.287,1.1-8.585,2.157a67.13,67.13,0,0,0-35.858,27.573,44.659,44.659,0,0,0-7.12,22.308,45.457,45.457,0,0,0,1.072,23.926l6.048,14.332a60.323,60.323,0,0,0,21.525,21.526l14.333,6.048a60.418,60.418,0,0,0,31.9,0" fill="#ffcdea"/>
        <path id="Trazado_9740" data-name="Trazado 9740" d="M650.9,719.782a951.162,951.162,0,0,1,147.4-31.651l-15.95,2.144c155.455-20.667,312.768-7.742,467.984,8.523,44.391,4.652,88.816,9.213,133.063,15.107l-15.951-2.143c70.429,9.512,141.5,22.754,207.334,50.389l-14.333-6.048c26.231,11.208,51.28,24.842,73.995,42.169L1622.3,788.89a240.937,240.937,0,0,1,42.505,42.2l-9.382-12.143a190.279,190.279,0,0,1,22.413,38.4l-6.048-14.332a188.322,188.322,0,0,1,12.8,45.331q-1.071-7.974-2.143-15.95a163.806,163.806,0,0,1,.163,43.7q1.071-7.976,2.143-15.95a141.593,141.593,0,0,1-8.664,31.871l6.048-14.333a132.449,132.449,0,0,1-15.586,27.051l9.382-12.143a163.677,163.677,0,0,1-28.721,28.474l12.144-9.382c-26.482,20.156-57.126,33.886-87.6,46.815l14.332-6.049a989.532,989.532,0,0,1-250.749,68.279l15.95-2.143c-71.751,9.473-146.272,13.068-218.206,3.465l15.95,2.143c-26.884-3.79-53.279-10.045-78.435-20.384l14.333,6.048c-12.059-5.139-23.495-11.224-33.916-19.223l12.143,9.382a99.97,99.97,0,0,1-15.561-15.61l9.382,12.143a76.844,76.844,0,0,1-9.048-15.849q3.024,7.167,6.049,14.333a76.941,76.941,0,0,1-4.032-14.975q1.072,7.976,2.144,15.95a66.738,66.738,0,0,1-.149-15.254q-1.073,7.975-2.143,15.951a58.634,58.634,0,0,1,3.212-12.143q-3.025,7.166-6.049,14.332a54.772,54.772,0,0,1,6.542-10.966l-9.382,12.143a69.365,69.365,0,0,1,9.227-8.981l-12.144,9.382c8.6-6.472,18.159-11.06,28.034-15.247l-14.333,6.049c32.583-13.448,66.8-21.62,101.684-26.307l-15.951,2.143c45.04-5.778,90.5-5.981,135.82-4.469,39.512,1.318,78.983,4.015,118.18,9.264l-15.951-2.143c52.358,7.18,104.211,19.01,153.037,39.563l-14.333-6.048a383.76,383.76,0,0,1,81.671,46.987l-12.143-9.382c23.937,18.508,44.767,40.11,63.293,63.995l-9.382-12.144c30.465,39.768,54.607,83.778,82.85,125.063,16.374,23.934,33.986,47.441,53.936,68.529,19.451,20.56,41.741,38.492,64.9,54.686,92.845,64.918,209.245,88.5,320.605,92.375,67.373,2.343,134.711-2.581,201.86-7.511,16.069-1.18,30.748-5.9,42.426-17.574,10.407-10.406,18.243-27.48,17.574-42.426-1.368-30.539-26.478-62.461-60-60-92.408,6.784-185.506,13.38-277.782,1.364l15.95,2.143c-48.033-6.5-95.2-18.29-139.924-37.131l14.333,6.049c-31.683-13.577-61.694-30.572-89.024-51.623q6.07,4.692,12.143,9.382a365.04,365.04,0,0,1-62.886-63.549l9.382,12.144c-29.991-38.96-53.836-82.052-81.336-122.712-16.237-24.008-33.716-47.538-53.406-68.839-18.625-20.148-39.994-37.579-62.31-53.478-83.145-59.238-186.866-81.269-286.611-91.393a1246.841,1246.841,0,0,0-151.227-6.214c-57.7,1.191-117.24,7.463-171.905,26.841-15.768,5.589-31.676,11.755-46.482,19.578-17.954,9.487-32.025,22.787-44.011,38.924-11.937,16.072-16.892,36.235-18.213,55.909-1.377,20.518,4.612,39.538,12.639,58,12.761,29.344,40.416,52.9,68.593,66.972a325.8,325.8,0,0,0,87.139,28.95c58.216,10.59,117.944,11.783,176.9,8.455A1078.3,1078.3,0,0,0,1454.8,1160.67a975.993,975.993,0,0,0,157.321-50.05c27.212-11.282,54.755-22.6,80.382-37.242,23.717-13.552,47.431-30.5,64.916-51.688,19.845-24.043,35.557-51.16,41.75-82.1,6.315-31.548,5.791-61.932-.653-93.437-10.987-53.71-43.775-103.5-86.023-137.879-92.925-75.627-213.3-98.974-329.1-114.37-56.03-7.449-112.332-13.124-168.57-18.75-56.075-5.61-112.245-10.464-168.54-13.2-116.283-5.662-233.5-1.56-347.813,21.958q-40.181,8.266-79.473,20.16c-30.008,9.079-52.059,42.432-41.906,73.807,9.806,30.3,41.632,51.641,73.807,41.906" fill="#41644a"/>
      </g>
    </g>
    <text id="L" transform="matrix(0.539, 0.842, -0.842, 0.539, 1689.211, 1150.191)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">L</tspan></text>
    <text id="o" transform="matrix(0.543, 0.84, -0.84, 0.543, 1720.969, 1200.675)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">o</tspan></text>
    <text id="_" data-name=" " transform="translate(1759.25 1258.837) rotate(53.258)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0"> </tspan></text>
    <text id="m" transform="translate(1777.74 1288.521) rotate(42.607)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">m</tspan></text>
    <text id="a" transform="translate(1859.97 1359.131) rotate(27.888)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">a</tspan></text>
    <text id="l-2" data-name="l" transform="translate(1927.422 1392.922) rotate(20.747)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">l</tspan></text>
    <text id="o-2" data-name="o" transform="translate(1959.58 1405.797) rotate(14.785)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">o</tspan></text>
    <text id="L-3" data-name="L" transform="matrix(0.984, -0.177, 0.177, 0.984, 662.106, 671.185)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">L</tspan></text>
    <text id="o-3" data-name="o" transform="translate(719.684 660.714) rotate(-6.792)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">o</tspan></text>
    <text id="_2" data-name=" " transform="translate(785.125 653.325) rotate(-4.533)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0"> </tspan></text>
    <text id="b" transform="matrix(0.999, -0.042, 0.042, 0.999, 810.221, 650.937)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">b</tspan></text>
    <text id="u" transform="translate(879.11 648.088) rotate(0.115)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">u</tspan></text>
    <text id="e" transform="translate(943.584 648.246) rotate(2.138)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">e</tspan></text>
    <text id="n" transform="translate(1008.343 650.698) rotate(3.738)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">n</tspan></text>
    <text id="o-4" data-name="o" transform="translate(1072.797 654.936) rotate(4.939)" fill="#ffcdea" font-size="106" font-family="FilsonProBold, Filson Pro"><tspan x="0" y="0">o</tspan></text>
    <g id="Grupo_308" data-name="Grupo 308">
      <g id="Grupo_307" data-name="Grupo 307" clip-path="url(#clip-path)">
        <path id="Trazado_9741" data-name="Trazado 9741" d="M1962.424,1183.5a931.994,931.994,0,0,1,100.1-23.021q14.358-2.453,28.793-4.4l-15.95,2.143c45.008-6.043,90.6-8.364,135.951-5.4q19.1,1.246,38.085,3.784l-15.95-2.143a477.027,477.027,0,0,1,93.172,21.954q12.734,4.445,25.165,9.695l-14.333-6.049a400.049,400.049,0,0,1,62.348,33.123,44.655,44.655,0,0,0,22.308,7.12,61.642,61.642,0,0,0,59.784-28.645l6.048-14.333a60.42,60.42,0,0,0,0-31.9l-6.048-14.332a60.294,60.294,0,0,0-21.525-21.525c-31.156-20.308-65.373-35.988-100.393-48.356a475.359,475.359,0,0,0-93.854-22.36c-21.233-2.907-42.4-5.482-63.815-6.55q-24.62-1.228-49.282-.5c-38.121,1.067-76.111,5.511-113.744,11.538a913.127,913.127,0,0,0-97.021,21.156q-5.882,1.613-11.744,3.3a67.138,67.138,0,0,0-35.858,27.574,44.659,44.659,0,0,0-7.12,22.308,45.457,45.457,0,0,0,1.072,23.926l6.048,14.332a60.314,60.314,0,0,0,21.525,21.526l14.333,6.048a60.418,60.418,0,0,0,31.9,0" fill="#ffcdea"/>
        <path id="Trazado_9742" data-name="Trazado 9742" d="M75.989,318.689C234.717,263.122,391.964,202.906,553.326,155.2c44.169-13.059,88.287-24.844,134-31.087l-15.951,2.143c66.378-8.782,133.555-7.9,199.881.89L855.308,125c77.345,10.524,154.34,30.844,226.4,60.976l-14.332-6.048c26.865,11.4,52.718,24.981,75.927,42.791l-12.144-9.382a279.2,279.2,0,0,1,48.883,49.319l-9.382-12.144A329.927,329.927,0,0,1,1211.1,320.5q-3.026-7.166-6.049-14.333c14.338,33.978,23.56,69.787,28.608,106.285q-1.071-7.976-2.143-15.95a488.29,488.29,0,0,1,.247,128.351q1.072-7.975,2.144-15.95c-3.232,23.083-8.547,45.726-17.347,67.352q3.024-7.166,6.049-14.333a213.9,213.9,0,0,1-26.96,46.526l9.382-12.143a255.19,255.19,0,0,1-44.833,44.382l12.144-9.382c-64.533,49.811-143.89,74.735-214.693,113.719-39.534,21.767-78.422,48.524-108.35,82.606-37.88,43.138-65.763,96.143-76.292,152.848-10.452,56.3-8.4,115.266,12.053,169.22,19.742,52.082,50.547,100.463,93.552,136.486,22.347,18.72,46.095,35.179,72.743,47.127,29.078,13.038,58.709,23.254,89.929,29.7,60.561,12.512,123.5,17.167,185.272,17.228,123.788.123,246.44-22.13,363.6-61.444q21.181-7.107,42.185-14.73c14.92-5.383,27.633-13.519,35.857-27.574,7.526-12.86,10.715-31.815,6.049-46.233-4.7-14.518-13.695-28.525-27.574-35.858-13.645-7.209-31.158-11.488-46.233-6.049-81.427,29.38-164.409,52.96-250.318,64.707l15.95-2.143c-88.764,11.876-179.272,12.74-268.113,1.059l15.951,2.143c-38.085-5.156-75.8-13.336-111.323-28.289l14.332,6.049a270.98,270.98,0,0,1-58.287-34.028l12.143,9.382a273.587,273.587,0,0,1-46.38-46.475q4.692,6.07,9.382,12.143a277.283,277.283,0,0,1-35.566-60.709l6.049,14.333a263.245,263.245,0,0,1-17.877-65.673q1.071,7.974,2.143,15.95a264.906,264.906,0,0,1,.378-68.3q-1.071,7.974-2.143,15.95a277.955,277.955,0,0,1,18.881-68.227L899.6,970.56a271.986,271.986,0,0,1,34.579-58.917L924.8,923.786a281.831,281.831,0,0,1,49.434-48.438l-12.143,9.382c43.528-33.241,93.815-55.8,143.243-78.667,52.209-24.157,102.713-49.579,147.228-86.553,53.376-44.333,86.6-107.609,96.675-175.933,10.618-71.979,8.225-145.707-10.4-216.293-17.411-65.983-50.2-129.789-99.08-178.007-57.167-56.391-133.835-85.588-209.328-108.506C953.564,17.438,873.138,3.276,792.817.457,712-2.38,630.955,8.189,553.229,30.263,399.565,73.9,249.837,129.9,99.446,183.425q-27.655,9.841-55.358,19.55c-14.88,5.21-27.708,13.648-35.858,27.574C.7,243.41-2.484,262.364,2.182,276.783c4.7,14.518,13.695,28.525,27.573,35.857,13.726,7.252,31.053,11.363,46.234,6.049" fill="#ffcdea"/>
      </g>
    </g>
    <text id="_3" data-name="¿" transform="matrix(0.986, -0.164, 0.164, 0.986, 627.68, 89.124)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">¿</tspan></text>
    <text id="Q" transform="translate(655.633 84.141) rotate(-5.969)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">Q</tspan></text>
    <text id="u-2" data-name="u" transform="translate(703.756 79.308) rotate(-2.308)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">u</tspan></text>
    <text id="i" transform="translate(739.531 78.053) rotate(-0.198)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">i</tspan></text>
    <text id="e-2" data-name="e" transform="translate(754.59 77.82) rotate(1.813)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">e</tspan></text>
    <text id="r" transform="translate(789.896 79.065) rotate(3.991)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">r</tspan></text>
    <text id="e-3" data-name="e" transform="matrix(0.994, 0.105, -0.105, 0.994, 810.8, 80.387)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">e</tspan></text>
    <text id="s" transform="matrix(0.99, 0.142, -0.142, 0.99, 845.899, 84.176)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">s</tspan></text>
    <text id="_4" data-name=" " transform="translate(871.839 87.965) rotate(9.577)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0"> </tspan></text>
    <text id="s-2" data-name="s" transform="matrix(0.982, 0.19, -0.19, 0.982, 887.248, 90.5)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">s</tspan></text>
    <text id="a-2" data-name="a" transform="matrix(0.975, 0.224, -0.224, 0.975, 912.9, 95.347)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">a</tspan></text>
    <text id="b-2" data-name="b" transform="translate(950.107 103.903) rotate(15.233)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">b</tspan></text>
    <text id="e-4" data-name="e" transform="matrix(0.955, 0.298, -0.298, 0.955, 987.01, 113.978)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">e</tspan></text>
    <text id="r-2" data-name="r" transform="translate(1020.812 124.619) rotate(18.889)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">r</tspan></text>
    <text id="_5" data-name=" " transform="translate(1039.997 131.194) rotate(19.821)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0"> </tspan></text>
    <text id="p" transform="matrix(0.928, 0.373, -0.373, 0.928, 1054.655, 136.205)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">p</tspan></text>
    <text id="o-5" data-name="o" transform="translate(1089.773 150.227) rotate(26.34)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">o</tspan></text>
    <text id="r-3" data-name="r" transform="translate(1121.344 166.089) rotate(31.066)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">r</tspan></text>
    <text id="_6" data-name=" " transform="translate(1138.352 176.356) rotate(34.434)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0"> </tspan></text>
    <text id="q-2" data-name="q" transform="matrix(0.764, 0.645, -0.645, 0.764, 1150.98, 184.259)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">q</tspan></text>
    <text id="u-3" data-name="u" transform="matrix(0.67, 0.742, -0.742, 0.67, 1179.423, 208.304)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">u</tspan></text>
    <text id="é" transform="matrix(0.576, 0.818, -0.818, 0.576, 1202.911, 234.254)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">é</tspan></text>
    <text id="_7" data-name="?" transform="translate(1222.795 262.639) rotate(60.844)" fill="#e86a33" font-size="60" font-family="FilsonProRegular, Filson Pro"><tspan x="0" y="0">?</tspan></text>
  </g>
</svg>
