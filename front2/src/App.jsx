import React from "react";
import SignInSidePexels from "./components/loginComponentes/SignInSidePexels";
import { ThemeProvider } from "@mui/material/styles";
import theme from "./temaConfig";
import Contenedor from "./components/menusYBarrasComponentes/Contenedor";
import { useSelector } from "react-redux";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
  useLocation,
} from "react-router-dom";
import { ManejarErrores } from "./components/ManejarErrores";
import { Box, CircularProgress, CssBaseline } from "@mui/material";
import CargandoLista from "./components/CargandoLista";
import { CrearPass } from "./components/cuentasComponentes/CrearPass";
import ModeProvider from "./context/ModeProvider";
import { Integrations } from "./components/landing/Integrations/Integrations";
import { LandingLayout } from "./components/landing/LandingLayout";
import { HeroLanding } from "./components/landing/components/HeroLanding";
import { IntegrationsDynamic } from "./components/landing/Integrations/IntegrationsDynamic";
import { IntegrationsDynamicWrapper } from "./components/landing/Integrations/IntegrationsDynamicWrapper";

function App() {
  const user = useSelector((store) => store.usuario.user);
const role = useSelector((store) => store.usuario.role);
  const esperandoUsuario = useSelector((store) => store.usuario.loading);

  const PrivateRoute = ({ children }) => {
    const location = useLocation();

    if (user === "status 401") {
      // Redirige al login y pasa la URL previa como estado
      return <Navigate to="/login" state={{ from: location }} />;
    } else if (user?.startsWith("status")) {
      return <ManejarErrores errorCode={user} />;
    } else {
      return children;
    }
  };

  const NavigateLogin = ({ children }) => {
    if (user === "status 401") {
      return children;
    } else {
      return <Navigate to="/" />;
    }
  };

  if (esperandoUsuario === false) {
    return (
      <Router>
        <ModeProvider>
          <Routes>
            <Route
              path="/crearPassword"
              element={
                <NavigateLogin>
                  <CrearPass />
                </NavigateLogin>
              }
            />

            <Route
              path="/login"
              element={
                <NavigateLogin>
                  <SignInSidePexels />
                </NavigateLogin>
              }
            />

            <Route path="landing/*" element={<LandingLayout />}>
              <Route index element={<HeroLanding />} />
              <Route path="integraciones" element={<Integrations />} />
              <Route path="tienda/:id" element={<IntegrationsDynamicWrapper />} />
            </Route>

            <Route
              path="/notAutorized"
              element={<ManejarErrores errorCode={"status 401"} />}
            />
            <Route
              path="/*"
              element={
                <PrivateRoute>
                  <Contenedor />
                </PrivateRoute>
              }
            />
          </Routes>
        </ModeProvider>
      </Router>
    );
  } else {
    return <CargandoLista />;
  }
}

export default App;
