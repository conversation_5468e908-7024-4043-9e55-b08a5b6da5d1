import React, { useState, useRef, useEffect } from 'react'
import { <PERSON>, But<PERSON>, Card, CardContent, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import { Container, Paper } from '@mui/material';
import { Typography } from '@mui/material';
import img401 from './img/img401.gif';
import img403 from './img/img403.gif';
import img404 from './img/img404.gif';
import img405 from './img/img405.gif';
import img422 from './img/img422.gif';
import img500 from './img/img500.gif';

export const ManejarErrores = ({ errorCode }) => {
    const statusCode = errorCode.toLowerCase(); // Convertimos a minúsculas para hacer coincidir las claves del objeto
    const [open, setOpen] = useState(true); // Estado para controlar si el modal está abierto o cerrado
    const codeMatch = statusCode.match(/\d+/); // Buscar uno o más dígitos en la cadena
    const code = codeMatch ? parseInt(codeMatch[0], 10) : NaN;
    const handleClose = () => {
        setOpen(false); // Cuando se haga clic en el botón de cerrar, establecer el estado a false para cerrar el modal
    };
    const [drawing, setDrawing] = useState(false);
  const [coordinates, setCoordinates] = useState([]);
  const paperRef = useRef(null);
  const animationFrameRef = useRef(null);

  useEffect(() => {
    return () => {
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, []);

  const handleMouseDown = (event) => {
    setDrawing(true);
    const { left, top } = paperRef.current.getBoundingClientRect();
    setCoordinates([...coordinates, { x: event.clientX - left, y: event.clientY - top }]);
  };

  const handleMouseMove = (event) => {
    if (!drawing) return;
    const { left, top } = paperRef.current.getBoundingClientRect();
    const newCoordinates = { x: event.clientX - left, y: event.clientY - top };
    cancelAnimationFrame(animationFrameRef.current);
    animationFrameRef.current = requestAnimationFrame(() => {
      setCoordinates((prevCoordinates) => [...prevCoordinates, newCoordinates]);
    });
  };

  const handleMouseUp = () => {
    setDrawing(false);
  };

  const handleClear = () => {
    setCoordinates([]);
  };

  const handleRefreshClick = () => {
    window.location.reload(); // Esto recargará la página
  };

  switch (statusCode) {
    case 'token 202':
        return (
            <Box display="flex" justifyContent='center' alignItems="center" marginTop="50px">
                <Card variant='outlined' sx={{width:'60%', borderRadius:'20px', borderColor:'#003876', borderWidth:'3'}}>
                    <Box sx={{backgroundColor:''}}>
                        <CardContent>
                            <Box display="flex" flexDirection="column" alignItems="center">
                                <Typography variant="h3" maxWidth='80%' textAlign='center' marginBottom='20px'><span style={{ color: '#003876', fontWeight:'bold' }}>Token Inválido</span></Typography>
                                <Typography variant="h5" maxWidth='80%' textAlign='center' >¡Vaya! Parece que el <span style={{ color: '#003876', fontWeight:'bold' }}>token</span> que estás usando no es válido</Typography>
                                <Typography variant="h5" maxWidth='80%' textAlign='center' >Probablemente ya haya <span style={{ color: '#003876', fontWeight:'bold' }}>expirado</span></Typography>
                                <Typography variant="h5" maxWidth='80%' textAlign='center' >Si se trata de un error, <span style={{ color: '#003876', fontWeight:'bold' }}>contacta a soporte o solicita un nuevo correo de registro</span></Typography>
                                <img src={img401} alt="Error"  style={{ height:'264px'}}/>
                                <Button variant="contained" color="primary" onClick={handleRefreshClick} sx={{marginTop:'10px'}}>
                                    Actualizar página
                                </Button>
                            </Box>
                        </CardContent>
                    </Box>
                </Card>
            </Box>
        );

    case 'status 401':
      return (
        <Box display="flex" justifyContent='center' alignItems="center" marginTop="50px">
            <Card variant='outlined' sx={{width:'60%', borderRadius:'20px', borderColor:'#003876', borderWidth:'3'}}>
                <Box sx={{backgroundColor:''}}>
                    <CardContent>
                        <Box display="flex" flexDirection="column" alignItems="center">
                            <Typography variant="h3" maxWidth='80%' textAlign='center' marginBottom='20px'><span style={{ color: '#003876', fontWeight:'bold' }}>Error 401</span></Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center'>¡Hey! No tan rápido </Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center'>Para poder pasar aquí necesitas  <span style={{ color: '#003876', fontWeight:'bold' }}>iniciar sesión</span></Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center'>Si se trata de un error, <span style={{ color: '#003876', fontWeight:'bold' }}>contacta a soporte</span></Typography>
                            <img src={img401} alt="Error"  style={{ height:'264px'}}/>
                            <Button variant="contained" color="primary" href="/">
                                Volver al inicio
                            </Button>
                        </Box>
                    </CardContent>
                </Box>
            </Card>
        </Box>
      );
    case 'status 403':
      return (
        <Box display="flex" justifyContent='center' alignItems="center" marginTop="50px">
            <Card variant='outlined' sx={{width:'60%', borderRadius:'20px', borderColor:'#003876', borderWidth:'3'}}>
                <Box sx={{backgroundColor:''}}>
                    <CardContent>
                        <Box display="flex" flexDirection="column" alignItems="center">
                            <Typography variant="h3" maxWidth='80%' textAlign='center' marginBottom='20px'><span style={{ color: '#003876', fontWeight:'bold' }}>Error 403</span></Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center'>¡Alto ahí! Este camino está <span style={{ color: '#003876', fontWeight:'bold' }}>bloqueado</span></Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center'>Parece que no tienes los <span style={{ color: '#003876', fontWeight:'bold' }}>permisos</span> suficientes</Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center'>Si se trata de un error, <span style={{ color: '#003876', fontWeight:'bold' }}>comunícate con el administrador</span></Typography>
                            <img src={img403} alt="Error"  style={{ height:'264px'}}/>
                            
                            <Button variant="contained" color="primary" href="/">
                                Volver al inicio
                            </Button>
                        </Box>
                    </CardContent>
                </Box>
            </Card>
        </Box>
      );
    case 'status 404':
      return (
        <Box display="flex" justifyContent='center' alignItems="center" marginTop="50px">
            <Card variant='outlined' sx={{width:'60%', borderRadius:'20px', borderColor:'#003876', borderWidth:'3'}}>
                <Box sx={{backgroundColor:''}}>
                    <CardContent>
                        <Box display="flex" flexDirection="column" alignItems="center">
                            <Typography variant="h3" maxWidth='80%' textAlign='center' marginBottom='20px'><span style={{ color: '#003876', fontWeight:'bold' }}>Error 404</span></Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center' >La página que buscas parece haberse <span style={{ color: '#003876', fontWeight:'bold' }}>extraviado</span></Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center' >Pero no te preocupes, nuestro equipo de expertos la localizará antes de que puedas decir <span style={{ color: '#003876', fontWeight:'bold' }}>"marketplace"</span></Typography>
                            <img src={img404} alt="Error" />
                            <Button variant="contained" color="primary" href="/">
                                Volver al inicio
                            </Button>
                        </Box>
                    </CardContent>
                </Box>
            </Card>
        </Box>
      );
    case 'status 405':
      return (
        <Box display="flex" justifyContent='center' alignItems="center" marginTop="50px">
        <Card variant='outlined' sx={{width:'60%', borderRadius:'20px', borderColor:'#003876', borderWidth:'3'}}>
            <Box sx={{backgroundColor:''}}>
                <CardContent>
                    <Box display="flex" flexDirection="column" alignItems="center">
                        <Typography variant="h3" maxWidth='80%' textAlign='center' marginBottom='20px'><span style={{ color: '#003876', fontWeight:'bold' }}>Error 405</span></Typography>
                        <Typography variant="h5" maxWidth='80%' textAlign='center' >¡Alto, alto! Ese método <span style={{ color: '#003876', fontWeight:'bold' }}>no está permitido</span> en este territorio.</Typography>
                        <Typography variant="h5" maxWidth='80%' textAlign='center' >No te preocupes, nuestro equipo de expertos lo <span style={{ color: '#003876', fontWeight:'bold' }}>solucionará</span> tan rápido como un rayo</Typography>
                        <img src={img405} alt="Error" style={{height:'264px'}}/>
                        <Button variant="contained" color="primary" href="/">
                            Volver al inicio
                        </Button>
                    </Box>
                </CardContent>
            </Box>
        </Card>
    </Box>
      );
    case 'status 422':
      return (
        <Dialog open={open} onClose={handleClose}>
            <DialogTitle>
                <Box display="flex" justifyContent="center">
                    <Typography variant="h3" style={{ color: '#003876', fontWeight: 'bold' }}>
                        Error al iniciar sesión
                    </Typography>
                </Box>
            </DialogTitle>
            <DialogContent>
                <Box sx={{ backgroundColor: '' , borderRadius:'10px'}}>
                    <CardContent>
                        <Box display="flex" flexDirection="column" alignItems="center">
                            <Typography variant="h5" maxWidth='90%' textAlign='center' >¡Oh no! No ha sido posible <span style={{ color: '#003876', fontWeight:'bold' }}>iniciar sesión</span></Typography>
                            <Typography variant="h5" maxWidth='90%' textAlign='center' >Es posible que tengas otra sesión abierta</Typography>
                            <Typography variant="h5" maxWidth='90%' textAlign='center' > Te sugerimos <span style={{ color: '#003876', fontWeight:'bold' }}>cerrar sesión en otra instancia de la plataforma</span> y volver a intentarlo</Typography>
                            <img src={img422} alt="Error"  style={{ height:'264px'}}/>
                        </Box>
                </CardContent>
                </Box>
            </DialogContent>
            <DialogActions>
                <Box display="flex" justifyContent="center">
                    <Button variant="contained" color="primary" onClick={handleClose}>
                        Cerrar
                    </Button>
                </Box>
            </DialogActions>
        </Dialog>
      );
    case 'status 500':
      return (
        <Box display="flex" justifyContent='center' alignItems="center" marginTop="50px">
            <Card variant='outlined' sx={{width:'60%', borderRadius:'20px', borderColor:'#003876', borderWidth:'3'}}>
                <Box sx={{backgroundColor:''}}>
                    <CardContent>
                        <Box display="flex" flexDirection="column" alignItems="center">
                            <Typography variant="h3" maxWidth='80%' textAlign='center' marginBottom='20px'><span style={{ color: '#003876', fontWeight:'bold' }}>Error 500</span></Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center' >¡Oops! Error interno del <span style={{ color: '#003876', fontWeight:'bold' }}>servidor</span></Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center' >No te preocupes, estamos trabajando para solucionarlo lo antes posible</Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center' >Mientras tanto, asegurate de <span style={{ color: '#003876', fontWeight:'bold' }}>revisar tu conexión</span></Typography>
                            <img src={img500} alt="Error"  style={{ height:'264px'}}/>
                            
                            <Button variant="contained" color="primary" href="/">
                                Volver al inicio
                            </Button>
                        </Box>
                    </CardContent>
                </Box>
            </Card>
        </Box>
      );
    case 'status network error':
        return (
            <Box display="flex" justifyContent='center' alignItems="center" marginTop="50px">
                <Card variant='outlined' sx={{width:'60%', borderRadius:'20px', borderColor:'#003876', borderWidth:'3'}}>
                    <Box sx={{backgroundColor:''}}>
                        <CardContent>
                            <Box display="flex" flexDirection="column" alignItems="center">
                                <Typography variant="h3" maxWidth='80%' textAlign='center' marginBottom='20px'><span style={{ color: '#003876', fontWeight:'bold' }}>Error de conexión</span></Typography>
                                <Typography variant="h5" maxWidth='80%' textAlign='center' marginBottom='10px'>¡Vaya! Parece que no tienes <span style={{ color: '#003876', fontWeight:'bold' }}>conexión al servidor</span></Typography>
                                <Typography variant="h5" maxWidth='80%' textAlign='center' >Puedes probar las siguientes <span style={{ color: '#003876', fontWeight:'bold' }}>soluciones:</span></Typography>
                                <Typography variant="h5" maxWidth='80%' textAlign='center' ><span style={{ color: '#003876', fontWeight:'bold' }}>Acepta</span> los riesgos del <span style={{ color: '#003876', fontWeight:'bold' }}>certificado</span></Typography>
                                <Typography variant="h5" maxWidth='80%' textAlign='center' marginBottom='10px'>Revisa que tu<span style={{ color: '#003876', fontWeight:'bold' }}> VPN esté conectada</span></Typography>
                                <Typography variant="h5" maxWidth='80%' textAlign='center' >Si eso no funciona, <span style={{ color: '#003876', fontWeight:'bold' }}>revisa tu conexión</span> a internet</Typography>
                                <img src={img500} alt="Error"  style={{ height:'264px'}}/>
                                <Button variant="contained" color="primary" onClick={handleRefreshClick} sx={{marginTop:'10px'}}>
                                    Actualizar página
                                </Button>
                            </Box>
                        </CardContent>
                    </Box>
                </Card>
            </Box>
            );






    default:
      return (
        <Box display="flex" justifyContent='center' alignItems="center">
            <Card variant='outlined' sx={{width:'60%', borderRadius:'20px', borderColor:'#003876', borderWidth:'3'}}>
                <Box sx={{backgroundColor:''}}>
                    <CardContent>
                        <Box display="flex" flexDirection="column" alignItems="center">
                            <Typography variant="h3" maxWidth='80%' textAlign='center' marginBottom='20px'><span style={{ color: '#003876', fontWeight:'bold' }}>{isNaN(code) ? 'Error Desconocido' : `Error ${code}`}</span></Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center' >¡Vaya! Ocurrió un error inesperado</Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center' >No te preocupes, nuestro equipo de expertos lo <span style={{ color: '#003876', fontWeight:'bold' }}>arreglará</span> en un momento</Typography>
                            <Typography variant="h5" maxWidth='80%' textAlign='center' >Mientras tanto, puedes dejar fluir tu creatividad en el espacio de abajo</Typography>
                            <Container maxWidth="sm" style={{ marginTop: '20px', height: '50vh', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                
                                <Paper
                                    ref={paperRef}
                                    style={{
                                    border: '1px solid #ccc',
                                    cursor: 'crosshair',
                                    userSelect: 'none',
                                    width: '100%',
                                    flex: 1,
                                    position: 'relative',
                                    }}
                                    onMouseDown={handleMouseDown}
                                    onMouseMove={handleMouseMove}
                                    onMouseUp={handleMouseUp}
                                >
                                    {coordinates.map((coord, index) => (
                                    <div
                                        key={index}
                                        style={{
                                        position: 'absolute',
                                        background: '#000',
                                        width: '5px',
                                        height: '5px',
                                        left: `${coord.x - 2}px`,
                                        top: `${coord.y - 2}px`,
                                        }}
                                    />
                                    ))}
                                </Paper>
                                <Button variant="outlined" color="secondary" onClick={handleClear} style={{ marginTop: '10px' }}>
                                    Limpiar
                                </Button>
                            </Container>
                            
                            <Button variant="contained" color="primary" href="/" sx={{marginTop:'10px'}}>
                                Volver al inicio
                            </Button>
                        </Box>
                    </CardContent>
                </Box>
            </Card>
        </Box>
      );     
  }


};

