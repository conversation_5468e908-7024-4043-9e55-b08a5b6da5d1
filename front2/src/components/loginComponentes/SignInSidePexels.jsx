import React from "react";
import { instanceAxios } from "../../redux/axiosInstance";
import TextField from "@mui/material/TextField";
import Alert from "@mui/material/Alert";
import Paper from "@mui/material/Paper";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";

import { LoadingButton } from "@mui/lab";
import { createTheme, ThemeProvider } from "@mui/material/styles";

import { useDispatch, useSelector } from "react-redux";
import { setMensajeError, setUsuario } from "../../redux/usersDucks";
import { useNavigate, useLocation } from "react-router-dom";
import { getImageLogin } from "../../redux/configuracionesDucks";
import { URLSERVER } from "../../Utils/config";
import { Button, IconButton, InputAdornment, InputLabel, useMediaQuery, useTheme } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import CircularProgress from "@mui/material/CircularProgress";
import { useState, useEffect } from "react";

const theme = createTheme();

const SignInSidePexels = (props) => {
  const [load, setLoad] = React.useState(false);
  const errorBack = useSelector((store) => store.usuario.mensajeError);
  const user = useSelector((store) => store.usuario.user);

  const theme = useTheme();

  const [mode, setMode] = React.useState(
    localStorage.getItem('mode') || 'dark'
  );
  const [email, setEmail] = React.useState("");
  const [pass, setPass] = React.useState("");
  const [error, setError] = React.useState(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const [showAlert, setShowAlert] = useState(false);

  // Obtén la URL previa desde el estado de React Router
  const from = location.state?.from?.pathname || "/";

  useEffect(() => {
    if (error) {
      setShowAlert(true);
      const timer = setTimeout(() => {
        setShowAlert(false);
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, [error]);

  // Variable para controlar todos los espaciados
  const SPACING = "5vh";  // Puedes ajustar este valor según necesites

  React.useEffect(() => {
    setError(errorBack);
  }, [errorBack, user]);

  React.useEffect(() => {
    dispatch(getImageLogin());
  }, []);

  const procesarDatos = (e) => {
    e.preventDefault();
    if (!email.trim()) {
      setError(() => ({
        message: "Ingrese Correo",
        severity: "error"
      }));
      setLoad(false);
      return;
    }
    if (!pass.trim()) {
      setError(() => ({
        message: "Ingrese Contraseña",
        severity: "error"
      }));
      setLoad(false);
      return;
    }
    if (pass.length < 6) {
      setError(() => ({
        message: "La contraseña debe tener al menos 6 caracteres",
        severity: "error"
      }));
      setLoad(false);
      return;
    }
    setError(null);
    loguear(email, pass);
  };


  const loguear = async () => {
    try {
      let url = "/loginAuth";
      const res = await instanceAxios.post(url, {
        email: email,
        password: pass,
      });
      setEmail("");
      setPass("");
      setError({
        message: "Inicio de sesión exitoso",
        severity: "success"
      });
      dispatch(setUsuario(res));

      // Redirige a la URL previa o al home si no hay una URL previa
      navigate(from, { replace: true });
    } catch (error) {
      let mensajeSimplificado;
      let severity = "error";
      const errorOriginal = error?.response?.data?.errores || error.message || error || "Error de conexión";
      
      if (typeof errorOriginal === 'string') {
        if (errorOriginal.includes('connection to server') || 
            errorOriginal.includes('Operation timed out') ||
            errorOriginal.includes('psycopg2')) {
          mensajeSimplificado = "Error de conexión con el servidor. Por favor, intente más tarde.";
          severity = "warning";
        } else if (errorOriginal.includes('password')) {
          mensajeSimplificado = "Contraseña incorrecta";
          severity = "error";
        } else if (errorOriginal.includes('email')) {
          mensajeSimplificado = "Email no válido";
          severity = "error";
        } else {
          mensajeSimplificado = "Ocurrió un error. Por favor, intente nuevamente.";
          severity = "error";
        }
      } else {
        mensajeSimplificado = "Ocurrió un error. Por favor, intente nuevamente.";
        severity = "error";
      }

      setError({
        message: mensajeSimplificado,
        severity: severity
      });
    } finally {
      setLoad(false);
    }
  };

  const handleClick = (event) => {
    event.preventDefault();
    setLoad(true);
    procesarDatos(event);
  };

  const [showPassword, setShowPassword] = React.useState(false);

  const isMobile = useMediaQuery(theme.breakpoints.down(700));


  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        color: theme.palette.colorGreen.main

      }}
    >
      {/* Contenedor principal */}
      <Box
        sx={{
          width: "100%",
          height: "100vh",
          margin: "auto",
          display: "flex",
          backgroundColor: mode === "dark" ? "#151e21" : "#ffffff",
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
          overflow: "hidden",
          background: theme.palette.primary.backgroundPink,
          backgroundImage: `url(/bg-login.png)`,
          backgroundSize: "cover",
          backgroundPosition: "-74px 123px",
          backgroundRepeat: "no-repeat",

        }}
      >
        {/* Sección de imagen */}
        <Box
          sx={{
            flex: "0 0 40%",
            display: isMobile ? "none" : "block",
            position: "relative",
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              position: "absolute",
              maxWidth: "600px",
              minWidth: "330px",
              top: 0,
              left: "50%",
              transform: "translateX(-50%)",
              right: 0,
              bottom: 0,
              backgroundImage: `url(${URLSERVER}/api/getLoginPhoto)`,
              backgroundSize:  "100%",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
          />
        </Box>

        {/* Sección de formulario */}
        <Box
          sx={{
            flex: isMobile ? "1" : "0 0 60%", // Cambiado a 65% fijo
            padding: "40px",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            maxHeight: "900px",
            margin: "auto",
            width: "100%", // Asegura que tome todo el ancho disponible
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontFamily: "Noto Sans",
              fontWeight: "800",
              marginBottom: SPACING,
              fontSize: "40px",
              color: theme.palette.colorGreen.main,
              width: "80%", // Aumentado
              textAlign: "center",
            }}
          >
            ¡Bienvenido!
          </Typography>

          <Box
            component="form"
            noValidate
            onSubmit={procesarDatos}
            sx={{
              width: "100%",
              maxWidth: "500px",
              display: "flex",
              flexDirection: "column",
              gap: SPACING,
              margin: "0 auto", // Centrado
            }}
          >
          
              <TextField
                margin="none"
                required
                fullWidth
                id="email"
                name="email"
                placeholder="Correo"
                autoComplete="email"
                autoFocus
                onChange={(e) => setEmail(e.target.value)}
                value={email}
                sx={{
                  backgroundColor: 'transparent',
                  '& .MuiInputBase-root': {
                    borderRadius: "27px",
                    backgroundColor: 'transparent',
                  },
                  '& .MuiInputBase-input': {
                    fontSize: "18px",
                    fontFamily: "Noto Sans",
                    padding: "15px",
                    color: theme.palette.colorGreen.main,
                    fontWeight: 700,
                    borderRadius: "27px",
                    backgroundColor: 'transparent',
                    '&::placeholder': {
                      color: theme.palette.colorGreen.main,
                      opacity: 0.7,
                      fontWeight: 700,
                    },
                    '&:-webkit-autofill, &:-webkit-autofill:hover, &:-webkit-autofill:focus': {
                      "-webkit-text-fill-color": theme.palette.colorGreen.main,
                      "-webkit-box-shadow": "0 0 0px 1000px transparent inset",
                      transition: "background-color 5000s ease-in-out 0s",
                      borderRadius: "27px",
                    },
                  },
                  '& .MuiOutlinedInput-root': {
                    borderRadius: "27px",
                    backgroundColor: 'transparent',
                    '& fieldset': {
                      borderColor: theme.palette.colorGreen.main,
                      borderWidth: "2px",
                      borderRadius: "27px",
                    },
                    '&:hover fieldset': {
                      borderColor: theme.palette.colorGreen.main,
                      borderWidth: "2px",
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: theme.palette.colorGreen.main,
                      borderWidth: "2px",
                    },
                  }
                }}
              />

              <TextField
                margin="none"
                required
                fullWidth
                name="password"
                type={showPassword ? "text" : "password"}
                id="password"
                placeholder="Contraseña"
                autoComplete="current-password"
                onChange={(e) => setPass(e.target.value)}
                value={pass}
                sx={{
                  backgroundColor: 'transparent',
                  '& .MuiInputBase-root': {
                    borderRadius: "27px",
                    backgroundColor: 'transparent',
                  },
                  '& .MuiInputBase-input': {
                    fontSize: "18px",
                    fontFamily: "Noto Sans",
                    padding: "15px",
                    color: theme.palette.colorGreen.main,
                    fontWeight: 700,
                    borderRadius: "27px",
                    backgroundColor: 'transparent',
                    '&::placeholder': {
                      color: theme.palette.colorGreen.main,
                      opacity: 0.7,
                      fontWeight: 700,
                    },
                    '&:-webkit-autofill, &:-webkit-autofill:hover, &:-webkit-autofill:focus': {
                      "-webkit-text-fill-color": theme.palette.colorGreen.main,
                      "-webkit-box-shadow": "0 0 0px 1000px transparent inset",
                      transition: "background-color 5000s ease-in-out 0s",
                      borderRadius: "27px",
                    },
                  },
                  '& .MuiOutlinedInput-root': {
                    borderRadius: "27px",
                    backgroundColor: 'transparent',
                    '& fieldset': {
                      borderColor: theme.palette.colorGreen.main,
                      borderWidth: "2px",
                      borderRadius: "27px",
                    },
                    '&:hover fieldset': {
                      borderColor: theme.palette.colorGreen.main,
                      borderWidth: "2px",
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: theme.palette.colorGreen.main,
                      borderWidth: "2px",
                    },
                  }
                }}
              />

            <Button
              fullWidth
              variant="text"
              sx={{ 
                justifyContent: "flex-end",
                color: theme.palette.colorGreen.main,
                textTransform: "none",
                '&:hover': {
                  color: "#E86A33",
                  backgroundColor: "transparent"
                }
              }}
            >
              Olvidé mi contraseña
            </Button>

            {error && showAlert && (
              <Alert
                severity={error.severity}
                variant="filled"
                sx={{
                  position: "fixed",
                  bottom: "30px",
                  left: "10px",
                  zIndex: 9999,
                }}
                onClose={() => setShowAlert(false)}
              >
                {error.message}
              </Alert>
            )}

            <LoadingButton
              loading={load}
              type="submit"
              fullWidth
              variant="contained"
              color="primary"
              onClick={handleClick}
              sx={{
                fontWeight: 600,
                fontSize: "16px",
                borderRadius: "27px",
                padding: "12px",
                backgroundColor: "#41644A",
                color: theme.palette.primary.backgroundPink,
                visibility: "visible", // Aseguramos que siempre sea visible
                '&.MuiLoadingButton-loading': {
                  backgroundColor: "#41644A",
                  opacity: 0.8,
                },
                '&:hover': {
                  backgroundColor: "#41644A",
                  opacity: 0.9,
                },
              }}
              loadingIndicator={
                <CircularProgress 
                  size={24} 
                  sx={{ 
                    color: theme.palette.primary.backgroundPink 
                  }} 
                />
              }
            >
              Iniciar Sesión
            </LoadingButton>

            <Button
              fullWidth
              variant="outlined"
              sx={{
                fontWeight: 700,
                fontSize: "18px",
                borderRadius: "27px",
                padding: "12px",
                borderColor: "#E86A33",
                color: "#E86A33",
                '&:hover': {
                  borderColor: "#E86A33",
                  backgroundColor: "rgba(232, 106, 51, 0.04)",
                },
              }}
            >
              No tienes cuenta? <strong style={{ marginLeft: "4px" }}>Registrate</strong>
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default SignInSidePexels;
