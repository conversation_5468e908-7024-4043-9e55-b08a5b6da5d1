import React, { useState, useEffect } from 'react';
import { Button, TextField, Grid, useTheme, useMediaQuery, Tooltip } from '@mui/material';
import Box from "@mui/material/Box";
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import Fab from '@mui/material/Fab';
import { darAltaProveedor, limpiarMensajeProveedor, obtenerProveedores } from '../../redux/proveedoresDucks';
import { useDispatch } from "react-redux";
import { useCookies } from "react-cookie";
import { useSelector } from 'react-redux';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import InfoIcon from '@mui/icons-material/Info';
import { Table, TableBody, TableContainer, TableHead, Paper, Typography } from '@mui/material';
import { IconButton } from '@mui/material';
import CustomDialog from '../componentesGenerales/CustomDialog';
import { AlertComponent } from '../componentesGenerales/Alert';

import { SUPPLIEREMAIL, SUPPLIEREXECUTIVE, SUPPLIERNAME, SUPPLIERRFC } from '../../Utils/config';
import { checkValidEmail, checkValidName, checkValidRFC, checkValid3Char } from '../../Utils/generalFunctions';
import { useNavigate } from 'react-router';
import { colocarProveedor } from '../../redux/almacenesDucks';
import { StyledTableCell, StyledTableRow } from '../StyledTableComponents';

const TablaProveedores = ({ proveedores }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProveedor, setSelectedProveedor] = useState(null);

  // Media queries para diferentes tamaños de pantalla
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm")); // < 600px
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md")); // < 900px

  const handleWarehouse = (nombre, rfc) => {
    dispatch(colocarProveedor(nombre, rfc));
    navigate('/proveedores/almacenes');
  }

  const handleOpenDetails = (proveedor) => {
    setSelectedProveedor(proveedor);
    setOpenDialog(true);
  }

  const handleCloseDetails = () => {
    setOpenDialog(false);
  }

  return (
    <Box display="flex" justifyContent="center" sx={{ marginBottom: '60px', width: '100%' }}>
      <TableContainer component={Paper} sx={{
        maxWidth: '95%',
        overflowX: 'auto',
        margin: { xs: '0 auto', sm: '0 auto' }
      }}>
        <Table size={isSmallScreen ? "small" : "medium"}>
          <TableHead>
            <StyledTableRow>
              <StyledTableCell align="center">NOMBRE</StyledTableCell>
              {!isSmallScreen && <StyledTableCell align="center">RFC</StyledTableCell>}
              {!isMediumScreen && <StyledTableCell align="center">EJECUTIVO DE VENTA</StyledTableCell>}
              {!isSmallScreen && <StyledTableCell align="center">E-MAIL</StyledTableCell>}
              <StyledTableCell align="center">ADMINISTRAR</StyledTableCell>
            </StyledTableRow>
          </TableHead>
          <TableBody>
            {proveedores.map((proveedor, index) => (
              <StyledTableRow key={index}>
                <StyledTableCell align="center">{proveedor.supplierName}</StyledTableCell>
                {!isSmallScreen && <StyledTableCell align="center">{proveedor.RFC}</StyledTableCell>}
                {!isMediumScreen && <StyledTableCell align="center">{proveedor.salesExecutive}</StyledTableCell>}
                {!isSmallScreen && <StyledTableCell align="center">{proveedor.email}</StyledTableCell>}
                <StyledTableCell align="center">
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: '5px', flexWrap: 'wrap' }}>
                    {isSmallScreen && (
                      <Tooltip title="Ver detalles">
                        <IconButton
                          variant="contained"
                          color="primary"
                          size="small"
                          onClick={() => handleOpenDetails(proveedor)}
                        >
                          <InfoIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="Editar">
                      <IconButton variant="contained" color="buttonGreenPink" size={isSmallScreen ? "small" : "medium"}>
                        <EditIcon fontSize={isSmallScreen ? "small" : "medium"} />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Eliminar">
                      <IconButton variant="outlined" color="buttonGreenPink" size={isSmallScreen ? "small" : "medium"}>
                        <DeleteIcon fontSize={isSmallScreen ? "small" : "medium"} />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Almacenes">
                      <IconButton
                        variant="outlined"
                        color="buttonGreenPink"
                        onClick={() => handleWarehouse(proveedor.supplierName, proveedor.RFC)}
                        size={isSmallScreen ? "small" : "medium"}
                      >
                        <WarehouseIcon fontSize={isSmallScreen ? "small" : "medium"} />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </StyledTableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog para mostrar detalles en pantallas pequeñas */}
      <CustomDialog
        open={openDialog}
        onClose={handleCloseDetails}
        title="Detalles del Proveedor"
        maxWidth="sm"
        width="100%"
      >
        {selectedProveedor && (
          <Box sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {selectedProveedor.supplierName}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" color="text.secondary">
                  RFC:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {selectedProveedor.RFC}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" color="text.secondary">
                  Ejecutivo de Venta:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {selectedProveedor.salesExecutive}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" color="text.secondary">
                  Email:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {selectedProveedor.email}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        )}
      </CustomDialog>
    </Box>
  );
};


const ModalFormularioRegistro = ({ openModal, handleOpenModal, handleCloseModal }) => {
  const [supplierName, setSupplierName] = useState("");
  const [supplierExecutiveName, setSupplierExecutiveName] = useState("");
  const [supplierEmail, setSupplierEmail] = useState("");
  const [supplierRFC, setSupplierRFC] = useState("");
  const [isSupplierNameDirty, setIsSupplierNameDirty] = useState(false);
  const [error, setError] = useState("");
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));

  const [isSupplierRFCDirty, setIsSupplierRFCDirty] = useState(false);
  const [isSupplierEmailDirty, setIsSupplierEmailDirty] = useState(false);
  const [isSupplierExecutiveNameDirty, setIsSupplierExecutiveNameDirty] = useState(false);


  const validateForm = () => {
    const isNameValid = checkValid3Char(supplierName);
    const isEmailValid = checkValidEmail(supplierEmail);
    const isRFCValid = checkValidRFC(supplierRFC);
    const isExecValid = checkValidName(supplierExecutiveName);

    return isNameValid && isEmailValid && isRFCValid && isExecValid;
  };
  const mensaje = useSelector((store) => store.proveedores.mensaje);
  const severity = useSelector((store) => store.proveedores.severity);

  const procesarDatos = async (supplierName, supplierExecutiveName, supplierEmail, supplierRFC, handleCloseModal) => {
    const formDataSuppliers = new FormData();
    formDataSuppliers.append("salesExecutive", supplierExecutiveName);
    formDataSuppliers.append("rfc", supplierRFC);
    formDataSuppliers.append("email", supplierEmail);
    formDataSuppliers.append("supplierName", supplierName);
    const result = await dispatch(darAltaProveedor(formDataSuppliers, cookies.csrf_access_token));
    if (result) {
      setSupplierName("");
      setSupplierExecutiveName("");
      setSupplierEmail("");
      setSupplierRFC("");
      setIsSupplierNameDirty(false);
      setIsSupplierEmailDirty(false);
      setIsSupplierRFCDirty(false);
      setIsSupplierExecutiveNameDirty(false);
      /* handleCloseModal(); */
    }
  }

  const handleSubmit = (e) => {
    e.preventDefault();

    // Marcar todos los campos como "dirty"
    setIsSupplierNameDirty(true);
    setIsSupplierEmailDirty(true);
    setIsSupplierRFCDirty(true);
    setIsSupplierExecutiveNameDirty(true);

    if (validateForm()) {
      procesarDatos(
        supplierName,
        supplierExecutiveName,
        supplierEmail,
        supplierRFC,
        handleCloseModal
      );
    } else {
      setError("Por favor, revise los campos e intente de nuevo.");
    }
  };


  return (
    <CustomDialog
      open={openModal}
      onClose={() => {
        setSupplierName("");
        setSupplierExecutiveName("");
        setSupplierEmail("");
        setSupplierRFC("");
        setIsSupplierNameDirty(false);
        setIsSupplierEmailDirty(false);
        setIsSupplierRFCDirty(false);
        setIsSupplierExecutiveNameDirty(false);
        handleCloseModal();
      }
      }
      title="Agregar un Proveedor"
      maxWidth="md"
      maxHeight='80vh'
      width={isSmallScreen ? "95%" : "800px"}
      actions={
        <Button
          fullWidth
          variant="contained"
          color="buttonGreenPink"
          sx={{ mt: 2, borderRadius: '8px' }}
          onClick={handleSubmit}
        >
          Registrar
        </Button>
      }
    >
      <Box sx={{ p: 2 }}>
        <Grid container spacing={2} justifyContent="center">
          <Grid item xs={12} sm={6} md={6}>
            <TextField
              fullWidth
              label={SUPPLIERRFC.label}
              value={supplierRFC}
              autoComplete='rfc'
              onChange={(e) => {
                setSupplierRFC(e.target.value.toUpperCase());
                setIsSupplierRFCDirty(true);
              }}
              error={isSupplierRFCDirty && !checkValidRFC(supplierRFC)}
              helperText={
                isSupplierRFCDirty && !checkValidRFC(supplierRFC)
                  ? "El RFC debe tener el formato correcto (ejemplo: XAXX010101000)"
                  : ""
              }
              inputProps={{
                maxLength: SUPPLIERRFC.longitud,
                pattern: "^[A-Za-zñÑ&]{3,4}\\d{6}\\w{3}$"
              }}
              margin="normal"
              required
            />
          </Grid>

          <Grid item xs={12} sm={6} md={6}>
            <TextField
              fullWidth
              label={SUPPLIERNAME.label}
              value={supplierName}
              autoComplete='name'
              onChange={(e) => {
                setSupplierName(e.target.value);
                setIsSupplierNameDirty(true);
              }}
              error={isSupplierNameDirty && !checkValid3Char(supplierName)}
              helperText={
                isSupplierNameDirty && !checkValid3Char(supplierName)
                  ? "El nombre debe tener al menos 3 caracteres"
                  : ""
              }
              inputProps={{
                maxLength: SUPPLIERNAME.longitud,
                minLength: 3
              }}
              margin="normal"
              required
            />
          </Grid>

          <Grid item xs={12} sm={6} md={6}>
            <TextField
              fullWidth
              label={SUPPLIEREMAIL.label}
              value={supplierEmail}
              autoComplete='email'
              onChange={(e) => {
                setSupplierEmail(e.target.value);
                setIsSupplierEmailDirty(true);
              }}
              error={isSupplierEmailDirty && !checkValidEmail(supplierEmail)}
              helperText={
                isSupplierEmailDirty && !checkValidEmail(supplierEmail)
                  ? "Ingrese un correo electrónico válido (ejemplo: <EMAIL>)"
                  : ""
              }
              inputProps={{
                maxLength: SUPPLIEREMAIL.longitud,
                type: "email",
                pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
              }}
              margin="normal"
              required
            />
          </Grid>

          <Grid item xs={12} sm={6} md={6}>
            <TextField
              fullWidth
              label={SUPPLIEREXECUTIVE.label}
              value={supplierExecutiveName}
              autoComplete='name'
              onChange={(e) => {
                setSupplierExecutiveName(e.target.value);
                setIsSupplierExecutiveNameDirty(true);
              }}
              error={isSupplierExecutiveNameDirty && !checkValidName(supplierExecutiveName)}
              helperText={
                isSupplierExecutiveNameDirty && !checkValidName(supplierExecutiveName)
                  ? "El nombre debe contener solo letras, espacios y guiones"
                  : ""
              }
              inputProps={{
                maxLength: SUPPLIEREXECUTIVE.longitud,
                pattern: "^[a-zA-Z\\s\\-]+$"
              }}
              margin="normal"
              required
            />
          </Grid>
        </Grid>

        {error ? (
          <AlertComponent
            color="error"
            message={error}
            cleanMessage={() => setError("")}
            time={2000}
          />
        ) : mensaje ? (
          <AlertComponent
            color={severity}
            message={mensaje}
            cleanMessage={() => {
              dispatch(limpiarMensajeProveedor());
            }}
            time={3000}
          />
        ) : null}
      </Box>
    </CustomDialog>
  );
}

const FormularioRegistroProveedores = () => {
  const provider = useSelector((store) => store.proveedores.proveedores);
  const dispatch = useDispatch();
  const [proveedores, setProveedores] = useState([]);

  const [openModal, setOpenModal] = useState(false);
  const handleOpenModal = () => {
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  // const loading = useSelector((store) => store.proveedores.loading);

  useEffect(() => {
    dispatch(obtenerProveedores());
    setProveedores(provider);
  }, []);
  useEffect(() => {
    setProveedores(provider);
  }, [provider]);



  return (
    <>
      <TablaProveedores proveedores={proveedores} />

      <Fab variant='extended' className='buttonGreenPink'
        aria-label="add" style={{ position: 'fixed', bottom: 20, right: 20, marginBottom: "5px" }} onClick={handleOpenModal} >
        <AddCircleOutlineIcon sx={{ mr: 1 }} />
        Agregar Proveedor
      </Fab>

      <ModalFormularioRegistro
        openModal={openModal}
        handleOpenModal={handleOpenModal}
        handleCloseModal={handleCloseModal}
      />
    </>
  );
}

export default FormularioRegistroProveedores
