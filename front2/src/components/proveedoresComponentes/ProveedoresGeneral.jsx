import React, { Fragment } from 'react'
import {
  Routes,
  Route,
} from "react-router-dom";

import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import FormularioRegistroProveedores from './FormularioRegistroProveedores';
import AlmacenesVisualizar from '../almacenesComponentes/AlmacenesVisualizar';
import StockVisual from '../almacenesComponentes/StockVisual';
import { TitleModule } from '../componentesGenerales/TitleModule';

const Item = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  textAlign: 'center',
  color: '#000000',
}));


const ProveedoresGeneral = () => {
  return (
    <div>

      <TitleModule title="Módulo de Proveedores" />
      <Routes>
        <Route path="/registrar" element={<FormularioRegistroProveedores />} />
        <Route path="/almacenes" element={<AlmacenesVisualizar />} />
      </Routes>

    </div>
  )
}

export default ProveedoresGeneral


