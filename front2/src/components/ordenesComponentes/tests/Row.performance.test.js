/**
 * Tests de rendimiento para el componente Row
 * Estos tests ayudan a prevenir regresiones en el rendimiento
 */

import React from 'react';
import { render, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { Row } from '../TablaDesplegable';
import theme from '../../../temaConfig';

// Mock store para testing
const createMockStore = (initialState = {}) => ({
  getState: () => ({
    usuario: {
      info: { email: '<EMAIL>', name: 'Test User' },
      role: 'Admin'
    },
    pedidos: {
      guiaContenido: null,
      guiaType: null,
      guiaEspera: false,
      guiaError: null
    },
    ...initialState
  }),
  subscribe: jest.fn(),
  dispatch: jest.fn()
});

// Mock data para pedido
const createMockPedido = (overrides = {}) => ({
  id: 'test-123',
  marketplaceOrderId: 'ML-123456',
  orderURL: 'https://example.com',
  orderStatusId: 1,
  creationDate: new Date().toISOString(),
  fulfillmentChannel: 1,
  client: {
    name: 'Cliente Test',
    nickname: 'test_user',
    score: 100
  },
  products: [{
    sku: 'TEST-SKU-001',
    title: 'Producto de prueba para testing',
    brand: 'Marca Test',
    model: 'Modelo Test',
    units: 2,
    shippedUnits: 0,
    photo: 'https://via.placeholder.com/100',
    publicationProduct: {
      skuMarketplaceVariation: 'VAR-001',
      publicationProduct_product_stores: [],
      publicationProduct_product_supplierStores: []
    }
  }],
  paidAmount: 1500,
  fee: 150,
  shipping: 100,
  receivedAmount: 1250,
  comments: [],
  guideInfo: { status: false, message: 'Guía no disponible' },
  orderInternalStatus_Order: {
    orderInternalStatusChange: {
      orderInternalStatus: {
        orderInternalStatusId: 1,
        internalStatusId: 1
      }
    }
  },
  pendingToResponse: false,
  ...overrides
});

// Wrapper para providers
const TestWrapper = ({ children, store }) => (
  <Provider store={store}>
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  </Provider>
);

describe('Row Performance Tests', () => {
  let mockStore;
  let mockProps;
  let renderSpy;

  beforeEach(() => {
    mockStore = createMockStore();
    mockProps = {
      pedido: createMockPedido(),
      id: 'test-123',
      index: 0,
      marketplaces: [],
      marketplaceLogos: [],
      isSmallScreen: false,
      isMediumScreen: false,
      isDownLargeScreen: false,
      detailOperationScreen: false,
      isSxScreen: false,
      ordenesSeleccionadas: [],
      setOrdenesSeleccionadas: jest.fn(),
      setOpenSurtir: jest.fn(),
      type: 'normal',
      namesStatus: [],
      namesStatusInternoV2: []
    };

    // Spy para contar renders
    renderSpy = jest.spyOn(React, 'createElement');
  });

  afterEach(() => {
    renderSpy.mockRestore();
    jest.clearAllMocks();
  });

  test('no debe renderizar excesivamente con props estables', () => {
    const { rerender } = render(
      <TestWrapper store={mockStore}>
        <Row {...mockProps} />
      </TestWrapper>
    );

    const initialRenderCount = renderSpy.mock.calls.length;

    // Re-render con las mismas props
    act(() => {
      rerender(
        <TestWrapper store={mockStore}>
          <Row {...mockProps} />
        </TestWrapper>
      );
    });

    const finalRenderCount = renderSpy.mock.calls.length;
    
    // Debería haber muy pocos renders adicionales debido a React.memo
    expect(finalRenderCount - initialRenderCount).toBeLessThan(10);
  });

  test('debe renderizar solo cuando las props relevantes cambien', () => {
    const { rerender } = render(
      <TestWrapper store={mockStore}>
        <Row {...mockProps} />
      </TestWrapper>
    );

    // Cambiar una prop irrelevante (función que debería estar memoizada)
    const newProps = {
      ...mockProps,
      setOrdenesSeleccionadas: jest.fn() // Nueva función
    };

    act(() => {
      rerender(
        <TestWrapper store={mockStore}>
          <Row {...newProps} />
        </TestWrapper>
      );
    });

    // Con React.memo y useCallback, esto no debería causar re-render
    expect(mockProps.setOrdenesSeleccionadas).not.toHaveBeenCalled();
  });

  test('debe manejar cambios en pedido sin renderizados excesivos', () => {
    const { rerender } = render(
      <TestWrapper store={mockStore}>
        <Row {...mockProps} />
      </TestWrapper>
    );

    const initialRenderCount = renderSpy.mock.calls.length;

    // Cambiar datos del pedido
    const updatedPedido = createMockPedido({
      orderStatusId: 2, // Cambio relevante
      paidAmount: 2000
    });

    act(() => {
      rerender(
        <TestWrapper store={mockStore}>
          <Row {...mockProps} pedido={updatedPedido} />
        </TestWrapper>
      );
    });

    const finalRenderCount = renderSpy.mock.calls.length;
    
    // Debería renderizar, pero no excesivamente
    expect(finalRenderCount - initialRenderCount).toBeLessThan(20);
  });

  test('funciones callback deben estar memoizadas', () => {
    let capturedCallbacks = {};

    const TestComponent = (props) => {
      // Capturar callbacks en el primer render
      if (Object.keys(capturedCallbacks).length === 0) {
        Object.keys(props).forEach(key => {
          if (typeof props[key] === 'function') {
            capturedCallbacks[key] = props[key];
          }
        });
      }

      return <Row {...props} />;
    };

    const { rerender } = render(
      <TestWrapper store={mockStore}>
        <TestComponent {...mockProps} />
      </TestWrapper>
    );

    // Re-render y verificar que las funciones son las mismas
    rerender(
      <TestWrapper store={mockStore}>
        <TestComponent {...mockProps} />
      </TestWrapper>
    );

    // Las funciones deberían ser las mismas referencias (memoizadas)
    Object.keys(capturedCallbacks).forEach(key => {
      if (typeof mockProps[key] === 'function') {
        expect(mockProps[key]).toBe(capturedCallbacks[key]);
      }
    });
  });

  test('debe manejar listas grandes sin degradación', async () => {
    const startTime = performance.now();
    
    // Simular una lista de 100 pedidos
    const largePedidosList = Array.from({ length: 100 }, (_, index) => 
      createMockPedido({ id: `test-${index}` })
    );

    const { container } = render(
      <TestWrapper store={mockStore}>
        <div>
          {largePedidosList.slice(0, 10).map((pedido, index) => (
            <Row 
              key={pedido.id}
              {...mockProps}
              pedido={pedido}
              id={pedido.id}
              index={index}
            />
          ))}
        </div>
      </TestWrapper>
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // El render de 10 filas no debería tomar más de 100ms
    expect(renderTime).toBeLessThan(100);
    expect(container.children).toHaveLength(1);
  });
});

describe('Row Memory Leaks Tests', () => {
  test('debe limpiar event listeners y timeouts', () => {
    const addEventListenerSpy = jest.spyOn(document, 'addEventListener');
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
    const setTimeoutSpy = jest.spyOn(global, 'setTimeout');
    const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');

    const mockStore = createMockStore();
    const mockProps = {
      pedido: createMockPedido(),
      id: 'test-123',
      index: 0,
      marketplaces: [],
      marketplaceLogos: [],
      isSmallScreen: false,
      isMediumScreen: false,
      isDownLargeScreen: false,
      detailOperationScreen: false,
      isSxScreen: false,
      ordenesSeleccionadas: [],
      setOrdenesSeleccionadas: jest.fn(),
      setOpenSurtir: jest.fn(),
      type: 'normal',
      namesStatus: [],
      namesStatusInternoV2: []
    };

    const { unmount } = render(
      <TestWrapper store={mockStore}>
        <Row {...mockProps} />
      </TestWrapper>
    );

    // Desmontar el componente
    unmount();

    // Verificar que se limpiaron los recursos
    // (Esto dependería de la implementación específica del componente)
    expect(removeEventListenerSpy).toHaveBeenCalledTimes(
      addEventListenerSpy.mock.calls.length
    );

    addEventListenerSpy.mockRestore();
    removeEventListenerSpy.mockRestore();
    setTimeoutSpy.mockRestore();
    clearTimeoutSpy.mockRestore();
  });
});
