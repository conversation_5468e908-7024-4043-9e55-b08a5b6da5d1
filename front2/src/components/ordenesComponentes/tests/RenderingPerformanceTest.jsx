import React, { useState, useEffect, useRef } from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { Row } from '../TablaDesplegable';
import theme from '../../../temaConfig';

/**
 * Hook personalizado para monitorear renderizados de componentes
 * Útil para detectar renderizados innecesarios durante el desarrollo
 */
export const useRenderCounter = (componentName) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());
  
  useEffect(() => {
    renderCount.current += 1;
    const currentTime = Date.now();
    const timeSinceLastRender = currentTime - lastRenderTime.current;
    
    console.log(`🔄 ${componentName} renderizado #${renderCount.current} (${timeSinceLastRender}ms desde último render)`);
    
    if (renderCount.current > 10 && timeSinceLastRender < 100) {
      console.warn(`⚠️ ${componentName}: Posible renderizado excesivo detectado!`);
    }
    
    lastRenderTime.current = currentTime;
  });
  
  return renderCount.current;
};

/**
 * Hook para detectar qué props cambiaron entre renders
 * Versión simplificada del useWhyDidYouUpdate
 */
export const usePropsChangeDetector = (props, componentName) => {
  const prevProps = useRef();
  
  useEffect(() => {
    if (prevProps.current) {
      const changedProps = Object.keys(props).filter(key => {
        const prevValue = prevProps.current[key];
        const currentValue = props[key];
        
        // Comparación básica (no deep)
        return prevValue !== currentValue;
      });
      
      if (changedProps.length > 0) {
        console.log(`📝 ${componentName} - Props que cambiaron:`, changedProps);
        changedProps.forEach(prop => {
          console.log(`  ${prop}:`, prevProps.current[prop], '→', props[prop]);
        });
      } else {
        console.log(`✅ ${componentName} - No se detectaron cambios en props`);
      }
    }
    
    prevProps.current = props;
  });
};

/**
 * Componente de prueba para verificar el rendimiento del Row
 */
export const RowPerformanceTest = () => {
  const [forceUpdate, setForceUpdate] = useState(0);
  
  // Mock data para el pedido
  const mockPedido = {
    id: 'test-123',
    marketplaceOrderId: 'ML-123456',
    orderURL: 'https://example.com',
    orderStatusId: 1,
    creationDate: new Date().toISOString(),
    fulfillmentChannel: 1,
    client: {
      name: 'Cliente Test',
      nickname: 'test_user',
      score: 100
    },
    products: [{
      sku: 'TEST-SKU-001',
      title: 'Producto de prueba para testing',
      brand: 'Marca Test',
      model: 'Modelo Test',
      units: 2,
      shippedUnits: 0,
      photo: 'https://via.placeholder.com/100',
      publicationProduct: {
        skuMarketplaceVariation: 'VAR-001',
        publicationProduct_product_stores: [],
        publicationProduct_product_supplierStores: []
      }
    }],
    paidAmount: 1500,
    fee: 150,
    shipping: 100,
    receivedAmount: 1250,
    comments: [],
    guideInfo: { status: false, message: 'Guía no disponible' },
    orderInternalStatus_Order: {
      orderInternalStatusChange: {
        orderInternalStatus: {
          orderInternalStatusId: 1,
          internalStatusId: 1
        }
      }
    },
    pendingToResponse: false
  };
  
  const mockProps = {
    pedido: mockPedido,
    id: 'test-123',
    index: 0,
    marketplaces: [],
    marketplaceLogos: [],
    isSmallScreen: false,
    isMediumScreen: false,
    isDownLargeScreen: false,
    detailOperationScreen: false,
    isSxScreen: false,
    ordenesSeleccionadas: [],
    setOrdenesSeleccionadas: () => {},
    setOpenSurtir: () => {},
    type: 'normal',
    namesStatus: [],
    namesStatusInternoV2: []
  };
  
  return (
    <div style={{ padding: '20px' }}>
      <h2>Test de Rendimiento - Componente Row</h2>
      
      <button 
        onClick={() => setForceUpdate(prev => prev + 1)}
        style={{ marginBottom: '20px', padding: '10px' }}
      >
        Forzar Re-render ({forceUpdate})
      </button>
      
      <div style={{ border: '1px solid #ccc', padding: '10px' }}>
        <p>Revisa la consola para ver los logs de renderizado</p>
        {/* Aquí iría el componente Row envuelto en los providers necesarios */}
        <div>Row Component Test Area</div>
      </div>
      
      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <h3>Instrucciones:</h3>
        <ul>
          <li>Abre las herramientas de desarrollador (F12)</li>
          <li>Ve a la pestaña Console</li>
          <li>Haz clic en "Forzar Re-render" varias veces</li>
          <li>Observa si hay renderizados excesivos o warnings</li>
        </ul>
      </div>
    </div>
  );
};

/**
 * Utilidad para crear un store mock para testing
 */
export const createMockStore = () => {
  return {
    getState: () => ({
      usuario: {
        info: { email: '<EMAIL>', name: 'Test User' },
        role: 'Admin'
      },
      pedidos: {
        guiaContenido: null,
        guiaType: null,
        guiaEspera: false,
        guiaError: null
      }
    }),
    subscribe: () => {},
    dispatch: () => {}
  };
};

export default RowPerformanceTest;
