const animales = [
    "jirafa", "pinguino", "foca", "leon", "tigre", "elefante", "rinoceronte", "hipopotamo", "canguro", "koala",
    "zorro", "lobo", "oso", "panda", "camello", "dromedario", "caballo", "burro", "mula", "vaca",
    "toro", "bisonte", "bufalo", "ciervo", "venado", "alce", "reno", "cabrito", "oveja", "caballo",
    "gato", "perro", "raton", "conejo", "ardilla", "huron", "hamster", "cobaya", "chinchilla", "mapache",
    "zorrillo", "comadreja", "nutria", "castor", "marmota", "erizo", "murcielago", "tortuga", "cocodrilo", "caiman",
    "serpiente", "iguana", "camaleon", "gecko", "lagarto", "salamandra", "rana", "sapo", "triton", "pez",
    "delfin", "ballena", "tiburon", "manta", "pulpo", "calamar", "langosta", "cangrejo", "gamba", "medusa",
    "estrella", "erizo", "anemona", "coral", "esponja", "caracol", "babosa", "lombriz", "hormiga", "abeja",
    "avispa", "mosca", "mosquito", "mariposa", "polilla", "escarabajo", "grillo", "saltamontes", "cigarra", "libelula",
    "cucaracha", "mantis", "araña", "escorpion", "ciempies", "milpies", "caracol", "babosa", "lombriz", "hormiga"
];

const adjetivosNeutros = [
    "espacial", "fuerte", "gigante", "valiente", "brillante", "sutil", "rápido", "inmenso", "intenso", "dulce",
    "veloz", "suave", "navegante", "ancestral", "gris", "naranja", "azul",
    "sorprendente", "natural", "excelente", "enorme", "frágil", "apacible", "fluctuante",
    "circular", "indestructible", "vibrante", "agradable",
    "luminiscente", "comestible", "inquietante", "floreciente", "radical", "imponente", "elegante",
    "incomparable", "deslumbrante", "leve", "verde", "flexible", "espejeante",
    "celeste", "inteligente", "radiante", "imparable", "invisible", "fragante", "incandescente", "neutro",
    "sutil", "transparente", "abismal", "original", "eficiente", "sublime",
];

export const  getRandomName = () => {
    const animal = animales[Math.floor(Math.random() * animales.length)];
    const adjetivo = adjetivosNeutros[Math.floor(Math.random() * adjetivosNeutros.length)];
    const capitalize = (word) => word.charAt(0).toUpperCase() + word.slice(1);
    return `${capitalize(animal)} ${capitalize(adjetivo)}`;
}
