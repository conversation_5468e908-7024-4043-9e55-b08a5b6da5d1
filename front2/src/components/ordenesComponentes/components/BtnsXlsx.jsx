import React from "react";
import LoadingButton from "@mui/lab/LoadingButton"
import { Button, Menu, MenuItem } from "@mui/material"
import IosShareIcon from '@mui/icons-material/IosShare';
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { getXLSX } from "../../../redux/ordenesConsolidadas";



export const BtnsXlsx = ({
  proveedor = "",
  filtrosStatus,
  filtrosStatusInterno,
  finalDate,
  initialDate,
  abiertosCerrados,
  sellerMarketplace,
  search,
  isSmallScreen
}) => {

  const dispatch = useDispatch();
  const loadingXlsx = useSelector((state) => { return state.ordenesConsolidadas.loadingXlsx });
  const messageXlsx = useSelector((state) => state.ordenesConsolidadas.messageXlsx);
  const okXlsx = useSelector((state) => state.ordenesConsolidadas.okXlsx);




  const handleClickXls = () => {
    // setLoadingXls(true);
    // setOpenDialog(true);
    dispatch(getXLSX(
      {
        proveedor: proveedor,
        filtrosStatus: filtrosStatus,
        filtrosStatusInterno: filtrosStatusInterno,
        finalDate: finalDate,
        initialDate: initialDate,
        abiertosCerrados: abiertosCerrados,
        sellerMarketplace: sellerMarketplace,
        search: search,
      }

    ));
  };

  // useEffect(() => {
  //   if (okXlsx.okXlsx) {

  //     // setMensajeModal("Se ha generado el archivo correctamente");
  //     // handleOpen();
  //   }
  // }, [okXlsx]);

  const [anchorElMenu, setAnchorElMenu] = React.useState(null);
  const openMenu = Boolean(anchorElMenu);
  const handleClick = (event) => {
    setAnchorElMenu(event.currentTarget);
  };
  const handleCloseMenu = () => {
    setAnchorElMenu(null);
  };
  return (
    <div>
      <LoadingButton
        size="small"
        variant="outlined"
        color="buttonGreenPink"
        onClick={handleClickXls}
        loading={loadingXlsx}
        sx={{
          height: "100%",
        }}
        loadingPosition="start"
        startIcon={<IosShareIcon size={10} sx={{ fontSize: "1rem" }} />}
      >
         {!isSmallScreen && <span> Exp Ordenes </span>}
      </LoadingButton>
    </div>
  )
}