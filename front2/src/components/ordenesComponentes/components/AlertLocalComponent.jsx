import { Alert, I<PERSON><PERSON><PERSON>on, Tooltip, Typography } from "@mui/material";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import InfoIcon from '@mui/icons-material/Info';
import TaskAltIcon from '@mui/icons-material/TaskAlt';
import DoneAllIcon from '@mui/icons-material/DoneAll';
import CommentIcon from '@mui/icons-material/Comment';
import { useEffect } from "react";
import { orderStatuses } from "../../utils/utils";

/**
 * Componente que muestra alertas personalizadas para diferentes estados de órdenes
 * @param {Object} props - Propiedades del componente
 * @param {Object} props.pedido - Información de la orden
 * @param {string} props.status - Estado actual de la orden
 * @param {Array} props.namesStatus - Lista de nombres de estados posibles
 * @param {boolean} props.isComment - Indica si es un comentario
 * @param {Array} props.comments - Lista de comentarios
 * @param {Object} props.lastComment - Último comentario realizado
 * @param {boolean} props.isCustom - Indica si es una alerta personalizada
 */
export const AlertLocalComponent = ({ pedido, status, namesStatus, isComment, comments, lastComment, tooltipOpen, handleTooltipClose, handleTooltipToggle, handleTooltipOpen, isCustom, namesStatusInternoV2 }) => {


  /**
   * Función que determina los estilos y el icono basado en el ID del estado
   * @param {number} id - ID del estado de la orden
   * @returns {Object} Objeto con la severidad, icono y estilos personalizados
   */
  const stylee = (id) => {
    let color = "";

    // Busca el color de la bandera correspondiente al ID del estado
    color = orderStatuses.find(
      (statusObj) => statusObj.orderStatusId === id
    )?.orderStatusFlag;

    let icon = "";
    let customStyle = {};


    // Define estilos y iconos según el color del estado
    if (color === "Yellow") {
      color = "info";
      icon = <TaskAltIcon />;
      customStyle = {
        backgroundColor: "#FFF3CD", // Amarillo suave
        color: "#856404",          // Marrón oscuro para contraste
      };
    } else if (color === "Red") {
      color = "error";
      icon = <InfoIcon />;
      customStyle = {
        backgroundColor: "#DC3545", // Rojo vibrante
        color: "#FFFFFF",          // Texto blanco para contraste
      };
    } else if (color === "Green") {
      color = "success";
      icon = <DoneAllIcon />;
      customStyle = {
        backgroundColor: "#28A745", // Verde vibrante
        color: "#FFFFFF",          // Texto blanco para contraste
      };
    } else if (color === "Orange") {
      color = "warning";
      icon = <InfoIcon />;
      customStyle = {
        backgroundColor: "#FF9800", // Naranja
        color: "#000000",          // Texto negro para contraste
      };
    } else {
      // Estilo por defecto
      color = "info";
      icon = <TaskAltIcon />;
      customStyle = {
        backgroundColor: "#17A2B8", // Azul claro
        color: "#FFFFFF",          // Texto blanco para contraste
      };
    }

    // Retorna objeto con todas las configuraciones de estilo
    return {
      severity: color,
      icon,
      styles: {
        borderRadius: "0.5em",
        fontWeight: "bold",
        padding: "0 1em",
        margin: "auto",
        height: "30px",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        ...customStyle,
        // Asegura que todos los elementos internos hereden el color del texto
        "& .MuiAlert-icon": {
          color: customStyle.color
        },
        "& .MuiAlert-action": {
          color: customStyle.color
        },
        "& .MuiIconButton-root": {
          color: customStyle.color
        },
        "& .MuiGrid-item": {
          padding: "0 !important",
        },
      },
    };
  };
  // Longitud máxima para truncar el texto del estado
  const maxLength = 15;
  // Renderiza diferentes tipos de alertas según las props
  return (
    isComment ? (
      // Renderiza alerta para comentarios
      <Alert
        sx={{
          backgroundColor: "#DAE2DC",
          height: "30px",
          display: "flex",
          alignItems: "center",
          padding: "0 1em !important",
          textOverflow: "ellipsis",
          width: "100%",
          minWidth: "200px",
          margin: "auto !important",
          '& .MuiAlert-message': {
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          },
          '& .MuiTypography-root': {
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            maxWidth: "100%",
          },
        }}
        variant="filled"
        icon={false}
        action={
          <IconButton
            color="inherit"
            size="small"
            sx={{
              padding: 0,
              "&:hover": {
                backgroundColor: "transparent",
              },
            }}
          >
            <CommentIcon />
          </IconButton>
        }
      >
        <Tooltip
          title={
            <Typography
              variant="body2"
              component="div"
              dangerouslySetInnerHTML={{
                __html:
                  comments && comments.length > 0
                    ? comments
                        .map((comment) => `- ${comment.orderStackableCommentRelevantRecords?.lastCommentRecord?.user.name?.split(" ")[0]}: ${comment.orderStackableCommentRelevantRecords.lastCommentRecord?.comment}`)
                        .join("<br/>")
                    : "sin comentarios",
              }}
            ></Typography>
          }
          sx={{
            width: "100%",
          }}
          placement="top"
          open={tooltipOpen}
          onClose={handleTooltipClose}
          disableTouchListener
          arrow
          PopperProps={{
            modifiers: [
              {
                name: "preventOverflow",
                options: {
                  boundary: "viewport",
                },
              },
            ],
          }}
        >
          <span
            onClick={handleTooltipToggle}
            onMouseEnter={handleTooltipOpen}
            style={{
              display: "block",
              width: "100%",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              minWidth: "100px",
              maxWidth: "80%",
            }}
          >
            { lastComment?.comment
              ? (
                lastComment?.comment.length > 50
                  ? `${lastComment?.comment.slice(0, 50)}...`
                  : lastComment?.comment
              )
              : "sin comentarios"
            }
          </span>
        </Tooltip>
      </Alert>
    ) : isCustom ? (

      // Renderiza alerta personalizada
      <Tooltip title={status ? status : "Desconocido"} arrow>
        <Alert
          sx={{
            backgroundColor: "#DAE2DC",
            height: "30px",
            display: "flex",
            alignItems: "center",
            padding: "0 1em",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            width: "100%",
            maxWidth: {
              xs: "100%",
              sm: "500px",
            },
            minWidth: "200px",
            cursor: "default",
            '& .MuiAlert-message': {
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            },
          }}
          variant="filled"
          icon={false}
          action={
            <IconButton color="inherit" size="small">
              {stylee(pedido.orderStatusId).icon || <CheckCircleIcon />}
            </IconButton>
          }
        >
          {!status && "Desconocido"}
          {status?.length > maxLength ? `${status.slice(0, maxLength)}...` : status}
        </Alert>
      </Tooltip>
    ) : (

      // Renderiza alerta estándar
      <Tooltip title={status ? status : "Desconocido"} arrow>
        <Alert
          severity={stylee(pedido.orderStatusId).severity}
          sx={{
            ...stylee(pedido.orderStatusId).styles,
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            maxWidth: {
              xs: "100%",
              sm: "500px",
            },
            minWidth: "200px",
            cursor: "default",
            '& .MuiAlert-message': {
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            },
          }}
          variant="filled"
          icon={false}
          action={
            <IconButton color="inherit" size="small">
              {stylee(pedido.orderStatusId).icon || <CheckCircleIcon />}
            </IconButton>
          }
        >
          {/* {status.length > maxLength ? `${status.slice(0, maxLength)}...` : status} */}
          {status}
        </Alert>
      </Tooltip>
    )
  );



}
