import React, { useState } from "react";
import {
  Popover,
  IconButton,
  Typography,
  Box,
  Divider,
  Tooltip,
} from "@mui/material";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { useCustomDateTime } from "../../menusYBarrasComponentes/HookDate";
import { useTheme } from "@emotion/react";

const InfoUpdate = ({ marketplaceGroup, colorConst, getMarketplacesList }) => {
  const date = useCustomDateTime(marketplaceGroup.lastUpdate);
  const fecha = date.adjustedDateTime;
    const theme = useTheme();

  return (
    <Box sx={{ mb: 2 ,  bgcolor: theme.palette.background.default, borderRadius: 4, padding: 1 }}>
      <Typography variant="subtitle1" fontWeight="bold" color="colorGreenPink">
        {`${marketplaceGroup.supportedMarketplaceGroup.name} ${getMarketplacesList(marketplaceGroup.marketplaces)}`}
      </Typography>
      <Typography variant="body2" color="colorGreenPink" sx={{ mt: 0.5 }}>
        Última actualización: {fecha}
      </Typography>
      <Typography variant="body2" color="colorGreenPink" sx={{ mt: 0.5 }}>
        {marketplaceGroup.commentUpdate || "Nunca actualizado"}
      </Typography>
    </Box>
  );
};

export const LastedUpdate = ({ dataActualizacion, colorConst, getMarketplacesList, isSmallScreen }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleUpdateClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClosePopover = () => {
    setAnchorEl(null);
  };

  const showUpdate = Boolean(anchorEl);

  return (
    <div>
      <Tooltip title="Última Actualización" arrow>
      <IconButton edge="end" color="inherit" onClick={handleUpdateClick}>
        <VisibilityIcon />
        <Typography
          variant="body2"
          marginLeft="5px"
          sx={(theme) => ({
            color: theme.palette.text.main,
            fontSize: "clamp(0.9rem, 1vw + 0.1rem, 1.3rem)",
          })}
        >
          {isSmallScreen ? "últ act." : "Última Actualización"}
          {/* Última Actualización */}
        </Typography>
      </IconButton>
      </Tooltip>

      <Popover
        open={showUpdate}
        anchorEl={anchorEl}
        onClose={handleClosePopover} // ✅ se cierra al hacer clic fuera
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
        transformOrigin={{ vertical: "bottom", horizontal: "left" }}
        sx={{
          "& .MuiPopover-paper": {
            // backgroundColor: "background.default",
            borderRadius: "20px",
            boxShadow: 3,
          },
        }}
      >
        <Box sx={{ padding: "10px", maxWidth: "300px" }}>
          {dataActualizacion?.message ? (
            <Typography variant="body2" fontWeight="bold">
              {dataActualizacion.message}
            </Typography>
          ) : (
            dataActualizacion?.marketplaceGroups?.map((marketplaceGroup) => (
              <InfoUpdate
                key={marketplaceGroup.id}
                marketplaceGroup={marketplaceGroup}
                colorConst={colorConst}
                getMarketplacesList={getMarketplacesList}
              />
            ))
          )}
        </Box>
      </Popover>
    </div>
  );
};
