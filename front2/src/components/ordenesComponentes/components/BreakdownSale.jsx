import React from "react";
import { Box, Chip, Grid2, Typography } from "@mui/material"
import { getfullFilmentChannelListPropertyUsingfullFiltmentNumber, getStatusInternoPropertyUsingStatusId2, getStatusPropertyUsingStatusId2, Green, Grey, <PERSON>, Red } from "../../../Utils/atributtesHandler"
import { useSelector } from "react-redux";
import { AlertLocalComponent } from "./AlertLocalComponent";
import { addNewCommentFunctionGeneral, returnCommentTypeUrl } from "../../utils/utils";
import { FullWidthTextField } from "./CommentsComponent";
import DetalleDesplegable from "../DetalleDesplegable";
import { useCookies } from "react-cookie";
export const noComment = { comment: 'Sin comentarios', id: null, timeStamp: null, userName: null }




export const BreakdownSale = ({ pedido, type = "consolidate", disabled = false, comments, setComments, updateComment, deleteComment }) => {

    // const [comments, setComments] = React.useState(pedido.comments);

    const commentType = "order";
    const commentTypeUrlAdd = returnCommentTypeUrl(commentType, "add");

    const [lastComment, setLastComment] = React.useState(
        comments.length > 0 ? comments[comments.length - 1] : noComment
    );

    const [cookies, setCookie] = useCookies();
    const csrf_access_token = cookies.csrf_access_token;

    const addNewCommentFunction = (commentToAdd) => {
        console.log('00000000000000000000-csrf_access_token')
        console.log(cookies)
        console.log(csrf_access_token)
        console.log('00000000000000000000-csrf_access_token')
        return addNewCommentFunctionGeneral(
            pedido.id,
            commentToAdd,
            "order",
            csrf_access_token,
            comments,
            setComments,
            setLastComment,
            "/api/orders/order/newComment"
        );
    };



    const marketplaces = useSelector((store) => store.pedidos.marketplaces);
    const namesStatus = useSelector((store) => {
        // debugger
        return store.pedidos.orderStatus;
    })
    const namesStatuss = useSelector((store) => store.ordenesConsolidadas.status);



    const getVariableByName = (name) => {
        switch (name) {
            case "Red":
                return Red;
            case "Orange":
                return Orange
            case "Yellow":
                return "rgba(227,227,0)";
            case "Green":
                return Green
            case "Grey":
                return Grey
            default:
                return Grey; // Valor predeterminado en caso de que no se encuentre ninguna coincidencia
        }
    };

    const transformedNamesStatus = namesStatus.length > 0
        ? namesStatus.map((status) => ({
            ...status,
            orderStatusFlag: getVariableByName(status.orderStatusFlag),
        }))
        : namesStatus.map((status) => ({
            ...status,
            orderStatusFlag: getVariableByName(status.orderStatusFlag),
        }));


    const status = getStatusPropertyUsingStatusId2(
        pedido.orderStatusId,
        "orderStatus",
        transformedNamesStatus
    );



    const namesStatusInternoV2 = useSelector(
        (store) => store.pedidos.orderInternalStatus
    );

    const [namesStatusInterno, setNamesStatusInterno] = React.useState([]);

    React.useEffect(() => {
        const orderInternalStatusTypesMap = {};
        namesStatusInternoV2.forEach((status) => {
            orderInternalStatusTypesMap[status.orderInternalStatusTypeId] =
                status.orderInternalStatusType;
        });

        // Crear el arreglo final
        const namesStatusInternoFinal = [];
        let currentType = "";
        namesStatusInternoV2.forEach((status) => {
            if (status.orderInternalStatusType !== currentType) {
                currentType = status.orderInternalStatusType;
                namesStatusInternoFinal.push({
                    type: "listSubheader",
                    subHeaderName: currentType,
                });
            }
            namesStatusInternoFinal.push({
                type: "menuItem",
                orderInternalStatus: status.orderInternalStatus,
                orderInternalStatusId: status.orderInternalStatusId,
                orderInternalStatusType: status.orderInternalStatusType,
                orderInternalStatusTypeId: status.orderInternalStatusTypeId,
            });
        });
        setNamesStatusInterno(namesStatusInternoFinal);
    }, [namesStatusInternoV2]);



    const getMarketplaceName = (pedidoMarketplaceId) => {
        const matchedMarketplace = marketplaces.find(
            (marketplace) =>
                marketplace.supportedMarketplace.id ===
                pedidoMarketplaceId
        );
        if (matchedMarketplace) {
            return matchedMarketplace.supportedMarketplace.name;
        } else {
            return "Marketplace desconocido";
        }
    };
    const styleProp = {
        fontSize: 14,
        color: theme => theme.palette.text.greenGrey,
        marginBottom: 1.5,
        fontWeight: "400",
    };

    return (
        <Box>
            <Box
                sx={{
                    width: "100%",
                    backgroundColor: (theme) => theme.palette.background.default,
                    padding: "1em",
                    borderRadius: "20px",
                    overflow: "hidden",
                    "& .MuiGrid2-item": {
                        margin: "auto",
                    },
                }}
            >
                <Grid2
                    container
                    spacing={2}
                    wrap="wrap" // permite que bajen de línea en pantallas chicas
                    sx={{
                        // flexDirection: 
                        justifyContent: "space-between",
                    }}
                >
                    {/* Primera columna */}
                    <Grid2 item xs={12} sm={6} md={3}>
                        <Typography sx={{ fontSize: 14, marginBottom: 1 }}>
                            <span style={styleProp}>Pedido con ID: </span>
                            {type === "consolidate" ? pedido.id : pedido.orderId}
                        </Typography>
                        <Typography sx={{ fontSize: 14, marginBottom: 1 }}>
                            <span style={styleProp}>Marketplace: </span>
                            {getMarketplaceName(pedido.marketplaceId)}
                        </Typography>
                        <Typography sx={{ fontSize: 14, marginBottom: 1 }}>
                            <span style={styleProp}>Canal: </span>
                            {getfullFilmentChannelListPropertyUsingfullFiltmentNumber(
                                pedido.fulfillmentChannel.toString(),
                                "fullFilmentChannelName"
                            )}
                        </Typography>
                    </Grid2>

                    {/* Segunda columna */}
                    <Grid2 item xs={12} sm={6} md={3}>
                        <Typography sx={{ fontSize: 14, marginBottom: 1 }}>
                            <span style={styleProp}>CreationDate: </span>
                            {type === "consolidate" ? pedido.date : crearFechaFormato(pedido.creationDate)}
                        </Typography>
                        <Typography sx={{ fontSize: 14 }} component="div">
                            OrderID:
                            <a target="_blank" href={pedido.orderURL}>
                                {pedido.marketplaceOrderId}
                            </a>
                        </Typography>
                    </Grid2>

                    {/* Tercera columna */}
                    <Grid2 item xs={12} sm={6} md={3}
                        sx={{
                            maxWidth: "200px",
                        }}
                    >
                        <Typography sx={{ fontSize: 14, marginBottom: 1 }} color="text.secondary">
                            <AlertLocalComponent namesStatus={namesStatus} pedido={pedido} status={status} />
                        </Typography>
                        <Typography sx={{ fontSize: 14, marginBottom: 1 }}>
                            <AlertLocalComponent
                                isCustom={true}
                                namesStatus={namesStatus}
                                pedido={pedido}
                                status={getStatusInternoPropertyUsingStatusId2(
                                    pedido.orderInternalStatusId ? pedido.orderInternalStatusId : pedido.internalStatusId,
                                    "orderInternalStatus",
                                    namesStatusInterno
                                )}
                            />
                        </Typography>
                    </Grid2>

                    {/* Cuarta columna (campo de comentarios) */}
                    {!disabled && (
                        <Grid2 item xs={12} sm={6} md={3}>
                            <FullWidthTextField
                                addNewCommentFunction={addNewCommentFunction}
                                comments={comments}
                                isMessage={"Comentarios"}
                                deleteComment={deleteComment}
                                updateComment={updateComment}
                            />
                        </Grid2>
                    )}
                </Grid2>
                
            </Box>

            {/* Tabla de desglose */}
            <DetalleDesplegable
                pedido={pedido}
                marketplaces={marketplaces}
                addNewCommentFunction={addNewCommentFunction}
                type="consolidate"
                index={0}
                disabled={disabled}
            />
        </Box>


    )
}