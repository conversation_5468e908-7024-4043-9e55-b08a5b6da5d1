import React from "react";
import { Tooltip, Chip } from "@mui/material";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import FaceIcon from "@mui/icons-material/Face";

export const DynamicChipWithTooltip = ({
  type,
  value,
  tooltipTitle ,
  maxLabelLength = 8,
  icon,
  color = "default",
}) => {
  let displayValue = value;

  if (type === "units") {
    displayValue = value > 99 ? "99+" : value;
  }

  if (type === "label") {
    displayValue = value.length > maxLabelLength ? value.substring(0, 7) + "..." : value;
  }
  if (type === "price") {
    displayValue = value !== null ? value : "N/D";
  }
  if (type === "other") {
    displayValue = value !== null ? value : "N/D";
  }

  return (
    <Tooltip title={tooltipTitle !== undefined ? tooltipTitle : displayValue} arrow>
      <Chip 
      color={color}
        size="small"
        sx={{
          padding: "3px",
        }}
       variant="outlined"  icon={icon && icon}
       label={displayValue} />
    </Tooltip>
  );
};

