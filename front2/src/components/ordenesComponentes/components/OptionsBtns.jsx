
import React from "react";
import { Box, Button } from "@mui/material";
import LinkIcon from "@mui/icons-material/Link";
import LinkOff from "@mui/icons-material/LinkOff";
import { FullWidthTextField } from "../../ordenesComponentes/components/CommentsComponent";
import RowMenu  from "../../ventasComponentes/SurtidoVentaDirecta/RowMenu";


export const OptionsBtns = ({ pedido, addNewCommentFunction, updateComment, deleteComment, comments, anchorEl, handleClick, handleClose, openAnchor, setOpen, listbotons, whatColor, hasNonEmptyStores, isSxScreen, isSmallScreen ,getOrderCommentsWithDeletedFunction}) => {
    return (
        <Box
            sx={{
                display: "flex",
                justifyContent: isSxScreen ? "space-between" : "space-between",
                gap: isSxScreen  || isSmallScreen ? "0px" : "10px",
                flexDirection: isSxScreen || isSmallScreen ? "column" : "row",
                // width: isSxScreen || isSmallScreen ? "50%" : "100%",
                alignItems: isSxScreen || isSmallScreen ? "flex-end" : "center",
                maxWidth: {
                    xs: "100%",
                    sm: "300px",
                    lg: "400px",
                },
            }}
        >
            <Button
                size="small"
                variant="contained"
                sx={{ flexBasis: "30%", marginBottom: "5px" }}
                disabled={whatColor(pedido.orderStatusId) === "error" ? true : false}
                color={
                    hasNonEmptyStores(pedido) === true ? "success" : "error"
                }
                onClick={(e) => {
                    if (hasNonEmptyStores(pedido)) {
                        e.stopPropagation(); // Detiene la propagación del evento si la condición es verdadera
                        return; // No ejecuta ninguna otra acción
                    }
                    e.stopPropagation(); // Detiene la propagación del evento en caso de que se abra
                    setOpen(true); // Ejecuta la acción si la condición es falsa
                }}
            // onClick={
            //   hasNonEmptyStores(pedido) === true
            //     ? null
            //     : () => setOpen(true)
            // }
            >
                {hasNonEmptyStores(pedido) === true ? (
                    <LinkIcon />

                ) : (
                    <LinkOff />
                )}
            </Button>
            <FullWidthTextField
                addNewCommentFunction={addNewCommentFunction}
                updateComment={updateComment}
                deleteComment={deleteComment}
                comments={comments}
                disabled={whatColor(pedido.orderStatusId) === "error" ? true : false}
                orderId={pedido.orderId}    
                getOrderCommentsWithDeletedFunction={getOrderCommentsWithDeletedFunction}
            />
            <RowMenu listbotons={listbotons()} anchorEl={anchorEl} handleClick={handleClick} handleClose={handleClose} open={openAnchor}
                disabled={whatColor(pedido.orderStatusId) === "error" ? true : false} />

        </Box>
    )
}