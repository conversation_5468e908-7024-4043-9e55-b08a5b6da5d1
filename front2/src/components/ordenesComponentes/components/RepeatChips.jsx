import { Box } from "@mui/material";
import ShoppingCart from "@mui/icons-material/ShoppingCart";
import FaceIcon from "@mui/icons-material/Face";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import RequestQuoteIcon from "@mui/icons-material/RequestQuote";
import VolunteerActivismIcon from "@mui/icons-material/VolunteerActivism";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import ReceiptIcon from "@mui/icons-material/Receipt";
import { DynamicChipWithTooltip } from "./DynamicChipWithTooltip";
import { RadioComponent } from "../components/RadioComponent";

export const RepeatChips = ({ pedido, checked, handleChangeChecked, id, label, fecha, tooltipTitle, isAdmin, isMediumScreen, isDownLargeScreen,
    isSmallScreen }) => {
    return (
        <Box
            sx={{
                justifyContent: "start",
                display: "flex",
                flexWrap: "wrap",
                width: "100%",
                padding: " 0 0 10px 0",
                gap: "5px",
            }}
        >

            {isSmallScreen && (
                <RadioComponent
                    checked={checked}
                    handleChangeChecked={handleChangeChecked}
                    id={id}
                    padding={"0"}
                />
            )}


            <DynamicChipWithTooltip
                type="units"
                value={pedido.products[0].units}
                tooltipTitle={`Unidades: ${pedido.products[0].units}`}
                icon={<ShoppingCart />}
            />
            <DynamicChipWithTooltip
                type="label"
                value={label}
                tooltipTitle={tooltipTitle}
                icon={<FaceIcon />}
            />

            <DynamicChipWithTooltip
                type="label"
                value={fecha}
                color="info"
                tooltipTitle={fecha}
                icon={<CalendarMonthIcon />}
            />


            {/* añado tambien mis operaciones de factura si este es admin y este esta en una pantalla mediana */}
            {isAdmin() && isMediumScreen ? (
                <>
                    <DynamicChipWithTooltip
                        type="price"
                        value={pedido?.paidAmount}
                        tooltipTitle={`Total: ${pedido?.paidAmount ? pedido?.paidAmount : "No disponible"}`}
                        icon={<RequestQuoteIcon />}
                    />
                    <DynamicChipWithTooltip
                        type="price"
                        value={pedido?.fee}
                        tooltipTitle={`Comisión: ${pedido?.fee ? pedido?.fee : "No disponible"}`}
                        icon={<VolunteerActivismIcon />}
                    />
                    <DynamicChipWithTooltip
                        type="price"
                        value={pedido?.shipping}
                        tooltipTitle={`Envío: ${pedido?.shipping ? pedido?.shipping : "No disponible"}`}
                        icon={<LocalShippingIcon />}
                    />
                    <DynamicChipWithTooltip
                        type="price"
                        value={pedido?.receivedAmount}
                        tooltipTitle={`Recibido: ${pedido?.receivedAmount ? pedido?.receivedAmount : "No disponible"}`}
                        icon={<ReceiptIcon />}
                    />
                </>
            ) : (
                ""
            )}
        </Box>

    );
};
