import { Button } from "@mui/material"
import AddCommentIcon from '@mui/icons-material/AddComment';
import { FullWidthTextField } from "./CommentsComponent";

export const ButtonComment = ({
    addNewCommentFunction,
    updateComment,
    deleteComment,
    comments,
}) => {

    return (
        <>
            {/* <Button
        size="small"
        variant="outlined"
        sx={{ flexBasis: "15%", marginBottom: "5px",  width: "10%" }}
        // color={
        //   hasNonEmptyStores(pedido) === true ? "success" : "error"
        // }
        color="buttonGreenPink"
      >
        <AddCommentIcon />
    
      </Button> */}
            <FullWidthTextField />
        </>

    )
}