import { Box, Button, Grid } from "@mui/material";
import React from "react";
import { ListComents } from "../../ventasComponentes/ListComents";
import { NewComment } from "../../ventaDirectaComponentes/NewComment";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import AddCommentIcon from '@mui/icons-material/AddComment';


export const FullWidthTextField = ({ addNewCommentFunction, deleteComment, updateComment, comments, isMessage, disabled = false, orderId, getOrderCommentsWithDeletedFunction }) => {

    const [isAddingComment, setIsAddingComment] = React.useState(false); // Estado para controlar si se está agregando un comentario

    const handleAddComment = () => {
        setIsAddingComment(true); // Activar estado de agregar comentario
    };


    return (
        <Box
            onClick={e => e.stopPropagation()}

        >
            {/* <ListComents
                sx={{ width: "100%", bgcolor: "background.paper" }}
                comments={comments}
                deleteComment={deleteComment}
                updateComment={updateComment}
            /> */}

            {isAddingComment ? (
                <NewComment
                    addNewCommentFunction={addNewCommentFunction}
                    commentType={'order'}
                    setIsAddingComment={setIsAddingComment}
                    orderId={orderId}
                    comments={comments}
                    deleteComment={deleteComment}
                    updateComment={updateComment}
                    getOrderCommentsWithDeletedFunction={getOrderCommentsWithDeletedFunction}
                />
            ) : (
                <Grid container spacing={2}>
                    <Grid item sx={{ textAlign: "right" }}>
                        <Button
                            // size="small"
                            variant="outlined"
                            sx={{
                                flexBasis: "15%", marginBottom: "5px", width: "auto",
                            }}
                            disabled={disabled}
                            // color={
                            //   hasNonEmptyStores(pedido) === true ? "success" : "error"
                            // }
                            onClick={handleAddComment}
                            color="buttonGreenPink"
                        >
                            <AddCommentIcon />
                            {isMessage && <p style={{ margin: "0", paddingLeft: "5px" }}>{isMessage}</p>}
                        </Button>
                    </Grid>
                </Grid>
            )}
        </Box>


    );
};