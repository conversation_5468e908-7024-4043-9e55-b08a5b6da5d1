
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AlertLocalComponent } from "./AlertLocalComponent";
import { useCookies } from "react-cookie";
import { Box, Grid, Typography, FormControl, InputLabel, Select, MenuItem, CircularProgress, ClickAwayListener, ListSubheader, IconButton, Popover } from "@mui/material";
import { useEffect } from "react";
import { changeOrderInternalStatus } from "../../utils/utils";
import { setMsgVentas } from "../../../redux/pedidosDucks";
import { obtenerHistorialStatusInterno } from "../../../redux/pedidosDucks";
import { History } from "@mui/icons-material";
import { useTheme } from "@emotion/react";

/**
 * Componente que muestra una cuadrícula con información de estado y comentarios de una orden
 * 
 * @component
 * @param {Object} props
 * @param {boolean} props.smallScreen - Indica si se está en pantalla pequeña
 * @param {Object} props.alerta - Datos de la alerta a mostrar
 * @param {string} props.status - Estado actual de la orden
 * @param {string|number} props.id - Identificador de la orden
 * @param {Object} props.pedido - Datos completos del pedido
 * @param {Object} props.lastComment - Último comentario de la orden
 * @param {Array} props.comments - Lista de comentarios de la orden
 * @param {number} props.currentOrderInternalStatusId - ID del estado interno actual
 * @param {Function} props.setCurrentOrderInternalStatusId - Función para actualizar el estado interno
 * @param {Array} props.namesStatusInternoV2 - Lista de estados internos disponibles
 */
export const AutoGridNoWrap = ({
    smallScreen,
    alerta,
    status,
    id,
    pedido,
    lastComment,
    comments,
    currentOrderInternalStatusId,
    setCurrentOrderInternalStatusId,
    namesStatusInternoV2,
}) => {
    // Hooks y estado local
    const [cookies] = useCookies();
    const dispatch = useDispatch();
    const [loadingInternalStatus, setLoadingInternalStatus] = React.useState(false);
    const csrf_access_token = cookies.csrf_access_token;


    // Estado para el tooltip de comentarios
    const [tooltipOpen, setTooltipOpen] = useState(false);
    const [namesStatusInterno, setNamesStatusInterno] = React.useState([]);
    // Estados para el popover de historial
    const [anchorEl, setAnchorEl] = useState(null);
    const [historialMovimientos, setHistorialMovimientos] = useState(null);
    const [loadingHistorial, setLoadingHistorial] = useState(false);

    // Estilos para el componente Select
    const SelectStyle = {
        maxWidth: smallScreen ? "140px" : "none",
        width: "100%",
    };

    /**
     * Maneja el cambio de estado interno de la orden
     * @param {Object} event - Evento del cambio de select
     */
    const changeIntentalStatus = async (event) => {
        const newInteralStatus = event.target.value;
        setLoadingInternalStatus(true);

        // Llamada a la API para cambiar el estado
        const [message, severity] = await changeOrderInternalStatus(
            id,
            newInteralStatus,
            csrf_access_token
        );

        // Actualiza el estado si la llamada fue exitosa
        if (severity === "success") {
            setCurrentOrderInternalStatusId(newInteralStatus);
        }

        // Muestra mensaje de resultado
        dispatch(setMsgVentas(message, severity));
        setLoadingInternalStatus(false);
    };

    /**
     * Funciones para manejar el tooltip de comentarios
     */
    const handleTooltipToggle = () => setTooltipOpen(!tooltipOpen);
    const handleTooltipClose = () => setTooltipOpen(false);
    const handleTooltipOpen = () => setTooltipOpen(true);

    // Función para manejar el click en el ícono de historial
    const handleHistorialClick = async (event) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
        setLoadingHistorial(true);
        try {
            const data = await dispatch(obtenerHistorialStatusInterno(id, csrf_access_token));
            setHistorialMovimientos(data);
            setLoadingHistorial(false);
        } catch (error) {
            setHistorialMovimientos({ error: "Error al obtener historial" });
            setLoadingHistorial(false);
        }
    };
    const handleClosePopover = () => {
        setAnchorEl(null);
    };

    // Obtiene los estados desde el store de Redux
    const namesStatus = useSelector((store) => store.pedidos.orderStatus);

    /**
     * Efecto para procesar y organizar los estados internos
     * Agrupa los estados por tipo y los prepara para el select
     */
    React.useEffect(() => {
        // Crea un mapa de tipos de estado
        const orderInternalStatusTypesMap = {};
        namesStatusInternoV2.forEach((status) => {
            orderInternalStatusTypesMap[status.orderInternalStatusTypeId] =
                status.orderInternalStatusType;
        });

        // Organiza los estados con sus encabezados
        const namesStatusInternoFinal = [];
        let currentType = "";
        namesStatusInternoV2.forEach((status) => {
            if (status.orderInternalStatusType !== currentType) {
                currentType = status.orderInternalStatusType;
                namesStatusInternoFinal.push({
                    type: "listSubheader",
                    subHeaderName: currentType,
                });
            }
            namesStatusInternoFinal.push({
                type: "menuItem",
                orderInternalStatus: status.orderInternalStatus,
                orderInternalStatusId: status.orderInternalStatusId,
                orderInternalStatusType: status.orderInternalStatusType,
                orderInternalStatusTypeId: status.orderInternalStatusTypeId,
            });
        });
        setNamesStatusInterno(namesStatusInternoFinal);
    }, [namesStatusInternoV2]);


    const theme = useTheme();
    return (
        <Box sx={{ maxWidth: {
            xs: "100%",
            sm: "300px",
            lg: "400px",
        },
         }} onClick={(e) => e.stopPropagation()}>
            <Grid container spacing={2} sx={{
                display: "flex",
                gap: "10px",
                width: "100% !important",
                margin: "0 !important",
                padding: "0 !important",
            }}>
                {/* Componente de Alerta */}
                <Grid item xs={12}
                    sx={{
                        padding: "0 !important",
                    }}>
                    <AlertLocalComponent
                        namesStatus={namesStatus}
                        pedido={pedido}
                        status={status}
                        namesStatusInternoV2={namesStatusInternoV2}
                    />
                </Grid>
                {/* Componente de Comentarios */}
                <Grid item xs={12} zeroMinWidth
                 sx={{
                    padding: "0 !important",
                }}>
                    <Typography >
                        <ClickAwayListener onClickAway={handleTooltipClose}>
                            <AlertLocalComponent
                                isComment={true}
                                comments={comments}
                                tooltipOpen={tooltipOpen}
                                handleTooltipToggle={handleTooltipToggle}
                                handleTooltipOpen={handleTooltipOpen}
                                handleTooltipClose={handleTooltipClose}
                                lastComment={lastComment}
                            />
                        </ClickAwayListener>
                    </Typography>
                </Grid>

                {/* Selector de Estado Interno */}
                <Grid item xs={12} sx={{
                        padding: "0 !important",
                    }}>
                    <Box sx={{
                        minWidth: 200,
                        paddingBottom: "15px",

                    }}>
                        <FormControl
                            fullWidth
                            size="small"
                            disabled={loadingInternalStatus}
                        >
                            <InputLabel>Status Interno</InputLabel>
                            <Select
                                style={SelectStyle}
                                value={loadingInternalStatus ? "circularProgress" : currentOrderInternalStatusId}
                                label="Status Interno"
                                onChange={(event) => {
                                    event.preventDefault();
                                    event.stopPropagation();
                                    changeIntentalStatus(event);
                                }}
                                sx={{
                                    maxWidth: "200px",
                                }}
                                inputProps={{
                                    sx: {
                                      paddingRight: '18px !important', // el !important ayuda a sobrescribir el estilo por defecto
                                    }
                                  }}
                                endAdornment = {
                                    <IconButton size="small" color="buttonGreenPink" sx={{right: ".9rem"}}
                                     onClick={handleHistorialClick} tabIndex={-1}>
                                        <History sx={{ fontSize: '1.2rem' }} />
                                    </IconButton>
                                }
                                MenuProps={{
                                    PaperProps: {
                                        sx: {
                                            "& .Mui-selected": {
                                                backgroundColor: theme.palette.mode === "dark"
                                                    ? theme.palette.action.selected
                                                    : "#e0e0e0",
                                                color: theme.palette.text.primary,
                                            },
                                            "& .Mui-selected:hover": {
                                                backgroundColor: theme.palette.mode === "dark"
                                                    ? theme.palette.action.hover
                                                    : "#bdbdbd",
                                            },
                                        }
                                    }
                                }}
                            >
                                {/* Renderiza el spinner de carga o la lista de estados */}
                                {loadingInternalStatus ? (
                                    <MenuItem value="circularProgress" style={{ backgroundColor: "red" }}>
                                        <CircularProgress size={18} />
                                    </MenuItem>
                                ) : (
                                    namesStatusInterno.map((statusInterno) => {
                                        if (statusInterno["type"] === "menuItem") {
                                            return (
                                                <MenuItem  key={statusInterno["orderInternalStatusId"]}
                                                    value={statusInterno["orderInternalStatusId"]}>
                                                    {statusInterno["orderInternalStatus"]}
                                                </MenuItem>
                                            );
                                        } else {
                                            return (
                                                <ListSubheader key={statusInterno["subHeaderName"]}>
                                                    <i style={{ color: "red" }}>
                                                        {statusInterno["subHeaderName"]}
                                                    </i>
                                                </ListSubheader>
                                            );
                                        }
                                    })
                                )}
                            </Select>
                        </FormControl>

                        {/* Popover para mostrar el historial */}
                        <Popover
                            open={Boolean(anchorEl)}
                            anchorEl={anchorEl}
                            onClose={handleClosePopover}
                            anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
                            transformOrigin={{ vertical: "top", horizontal: "left" }}
                            sx={{
                                "& .MuiPopover-paper": {
                                    borderRadius: "20px",
                                    boxShadow: 3,
                                },
                            }}
                        >
                            <Box sx={{ padding: "10px", maxWidth: "300px", maxHeight: "400px", minWidth: "150px", overflowY: "auto" }}>
                                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, textAlign: "center", fontSize: "1.2rem" }}>
                                    Historial de cambios de estado
                                </Typography>
                                {loadingHistorial ? (
                                    <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", py: 4 }}>
                                        <CircularProgress size={40} />
                                    </Box>
                                ) : historialMovimientos && Array.isArray(historialMovimientos.internalStatusChanges) ? (
                                    historialMovimientos.internalStatusChanges.length > 0 ? (
                                        [...historialMovimientos.internalStatusChanges]
                                            .sort((a, b) => {
                                                const dateA = new Date(a.timeStamp);
                                                const dateB = new Date(b.timeStamp);
                                                return dateB - dateA; // descendente: más nuevo primero
                                            })
                                            .map((cambio) => (
                                            <Box key={cambio.id} sx={{ mb: 2, bgcolor: theme.palette.background.default
                                            , borderRadius: 4, padding: 1, minWidth: '150px' }}>
                                                <Box display="flex" justifyContent="space-between" alignItems="center">
                                                    <Typography variant="subtitle1" fontWeight="bold" color="textPrimary" sx={{ fontSize: '.8rem' }}>
                                                        {cambio.user?.name || 'Usuario desconocido'}
                                                    </Typography>
                                                    <Typography variant="subtitle1" fontWeight="semibold" color="secondary" sx={{ fontSize: '.8rem', px: 1, py: .3, borderRadius: 100, opacity: 0.9 }}>
                                                        {cambio.orderInternalStatus?.orderInternalStatus || cambio.orderInternalStatus?.description || 'Sin estado'}
                                                    </Typography>
                                                </Box>
                                                <Typography variant="caption" sx={{ mt: 0.5 }}>
                                                    {cambio.orderInternalStatus?.description || 'Sin descripción'}
                                                </Typography>
                                                <Typography variant="body2" color="secondary" sx={{ mt: 0.5,  }}>
                                                    {cambio.timeStamp ? new Date(cambio.timeStamp).toLocaleString() : ''}
                                                </Typography>
                                               
                                            </Box>
                                        ))
                                    ) : (
                                        <Typography variant="body2" sx={{ textAlign: "center", py: 2 }}>
                                            No hay cambios registrados
                                        </Typography>
                                    )
                                ) : (
                                    <Typography variant="body2" sx={{ textAlign: "center", py: 2 }}>
                                        Haz clic en el ícono para cargar el historial
                                    </Typography>
                                )}
                            </Box>
                        </Popover>
                    </Box>
                </Grid>
            </Grid>
        </Box>
    );
};


