import React from 'react';
import { Radio } from '@mui/material';

export const RadioComponent = ({ checked, handleChangeChecked, id, padding}) => {
    return (
        <div
            style={{
                padding: "0",
                margin: "0",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
            }}
        >
            <Radio
            sx={{
                padding: padding ? padding : "9px",
            }}
                checked={checked}
                // onChange={handleChangeChecked}
                onClick={(e) => {
                    e.stopPropagation(); // Evita que el clic en el radio active el evento de la fila
                    handleChangeChecked(e);
                }}
                inputProps={{ 'aria-label': 'A' + id }}
            />

        </div>
    );
}