import React from "react";

import { useDispatch, useSelector } from "react-redux";

import List from "@mui/material/List";
import Collapse from "@mui/material/Collapse";
import { ListItemButton } from "@mui/material";
import ListItemText from "@mui/material/ListItemText";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";

import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import CargandoDetalle from "./CargandoDetalle";

import {
  colocarPedidosStatus500
} from '../../redux/pedidosDucks'
const Detalle = () => {
  const pedido = useSelector((store) => store.pedidos.unPedido);
  const [estadosProductos, setEstadosProductos] = React.useState([]);
  const esperaCargando = useSelector((store) => store.pedidos.esperaCargando);
  const dispatch = useDispatch();
  React.useEffect(() => {
    if (esperaCargando !== null && pedido !== null) {
      if (!esperaCargando && pedido !== "status 500") {
        var skuDespliegue = [];
        pedido.products.forEach((item) => {
          var claveValor = {};
          claveValor["sku"] = item.sku;
          claveValor["open"] = false;
          skuDespliegue.push(claveValor);
          setEstadosProductos(skuDespliegue);
        });
      }
    }
  }, [esperaCargando, pedido]);

  const manejarClick = (sku) => {
    setEstadosProductos((state) =>
      state.map((item) =>
        item.sku === sku ? { ...item, open: !item.open } : item
      )
    );
  };

  if (esperaCargando === null) {
    return <div></div>;
  } else if (esperaCargando) {
    return <CargandoDetalle />;
  } else {
    if (pedido !== null) {
      if (pedido === "status 500") {
        dispatch(colocarPedidosStatus500());
        return null;
      } else{
        return(
          <React.Fragment >
          <CardContent style={{ border: "1px solid " }}>
            <Typography sx={{ fontSize: 14 }} color="text.secondary" gutterBottom>
            Pedido con ID: {pedido.orderId} | CreationDate: {pedido.creationDate}
            </Typography>
            <Typography variant="h5" component="div">
              {pedido.marketplace} {`=>`} OrderID: <a target="_blank" href={pedido.orderURL}>{pedido.marketplaceOrderId}</a>
            </Typography>
            <Typography sx={{ mb: 1.5 }} color="text.secondary">
            FulfillmentChannel: {pedido.fulfillmentChannel}
            </Typography>
            <Typography variant="body2">
            Status: {pedido.status} | InternalStatus: {pedido.internalStatus}
            <br />
            OperationIds: {pedido.operationIds}
            </Typography>
            <Typography variant="h6" component="div">
              Productos
            </Typography>
            {pedido.products.map((item) => {
              if(estadosProductos.find(producto => producto.sku == item.sku)){
                return(
                  <div>
                  <ListItemButton onClick={() => manejarClick(item.sku)} >
                  <ListItemText primary={item.sku} />
                  {/*true*/estadosProductos.find(producto => producto.sku == item.sku).open ? <ExpandLess /> : <ExpandMore />}
                </ListItemButton>
                  <Collapse style={{ border: "1px solid " }} in=/*{true}*/{estadosProductos.find(producto => producto.sku === item.sku).open} timeout="auto" unmountOnExit>
                      <List component="div" disablePadding>
                        <ListItemText primary={"Sku: "+ item.sku} />
                        <ListItemText primary={"Title: "+ item.title} />
                        <img src={item.photo} style={{ height: '400px', width: '400px' }}  alt=""/>
                        <ListItemText primary={"Brand: "+ item.brand} />
                        <ListItemText primary={"Model: "+ item.model} />
                        <ListItemText primary={"Units: "+ item.units} />
                      </List>
                  </Collapse>
                  </div>
                )
              }
               })}
              <Typography variant="body2">
                PaidAmount: {pedido.paidAmount}
                <br />
                Fee: {pedido.fee}
                <br />
                Shipping: {pedido.shipping}
                <br />
                ReceivedAmount: {pedido.receivedAmount}
              </Typography>
              <Typography sx={{ mb: 1.5 }} color="text.secondary">
                Comments: {pedido.comments}
              </Typography>
            </CardContent>
          </React.Fragment>
        );
      }
    } else {
      return <div></div>;
    }
  }
};

export default Detalle;
