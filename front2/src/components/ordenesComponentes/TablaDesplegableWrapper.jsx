import React from "react";
import CargandoLista from "../CargandoLista";
import { useDispatch, useSelector } from "react-redux";
import TablaDesplegable from "./TablaDesplegable";
import { obtenerPedidosFiltradosAccion } from "../../redux/pedidosDucks";
import { ManejarErrores } from "../ManejarErrores";
import { SkeletonTables } from "../componentesGenerales/SkeletonTables";
import { obtenerAlmacenesInternos } from "../../redux/almacenesDucks";

const TablaDesplegableWrapper = (props) => {
  const pedidos = useSelector((store) => store.pedidos.orders);
  const dispatch = useDispatch();
  const marketplaces = props.marketplaces;
  const marketplaceLogos = props.marketplaceLogos;
  const proveedor = props.proveedor;
  const offset = props.offset;
  const status = props.status;
  const statusInterno = props.statusInterno;
  const initialDate = props.initialDate;
  const finalDate = props.finalDate;
  const openCloseAlignment = props.openCloseAlignment;
  const sellerMarketplaceAlignment = props.sellerMarketplaceAlignment;
  const search = props.search;
  
  React.useEffect(() => {
    const fetchData = () => {
      dispatch(
        obtenerPedidosFiltradosAccion({
          proveedor: proveedor,
          offsetActual: offset,
          filtrosStatus: status,
          filtrosStatusInterno: statusInterno,
          initialDate: initialDate,
          finalDate: finalDate,
          abiertosCerrados: openCloseAlignment,
          sellerMarketplace: sellerMarketplaceAlignment,
          search: search,
        })
      );
    };
    fetchData();
  dispatch(obtenerAlmacenesInternos());

  }, [dispatch, proveedor, offset, status, statusInterno, initialDate, finalDate, openCloseAlignment, sellerMarketplaceAlignment, search]);


  if (pedidos) {
    if (typeof pedidos === "string" && pedidos.startsWith("status")) {
      return <ManejarErrores errorCode={pedidos} />;
    } else {
      return (
        <>
          <TablaDesplegable
            pedidos={pedidos}
            marketplaces={marketplaces}
            marketplaceLogos={marketplaceLogos}
          />
        </>
      );
    }
  } else {
    return <SkeletonTables />;
  }
};

export default TablaDesplegableWrapper;
