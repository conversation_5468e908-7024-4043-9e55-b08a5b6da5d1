import React, { useEffect } from "react";

import List from "@mui/material/List";
import Collapse from "@mui/material/Collapse";
import {
  Alert,
  Checkbox,
  Chip,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  ListItemButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  useMediaQuery,
} from "@mui/material";
import ListItemText from "@mui/material/ListItemText";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import {
  getStatusInternoPropertyUsingStatusId2,
  getfullFilmentChannelListPropertyUsingfullFiltmentNumber,
  getStatusPropertyUsingStatusId2,
  crearFechaFormato,
} from "../../Utils/atributtesHandler";
import {
  RelacionarProductosConOrdenes,
  limpiarMensajeRelacion,
} from "../../redux/pedidosDucks";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import { useDispatch, useSelector } from "react-redux";
import Grid from "@mui/material/Grid";
import { useCookies } from "react-cookie";
import productosReducer from "../../redux/productosDucks";
import { useCallback } from "react";
import { useRef } from "react";
import SyncIcon from "@mui/icons-material/Sync";
import DeleteIcon from "@mui/icons-material/Delete";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import SendIcon from "@mui/icons-material/Send";
import moment from "moment";
import CancelIcon from "@mui/icons-material/Cancel";
import { useTheme } from "@emotion/react";
import { pink } from "@mui/material/colors";
import RequestQuoteIcon from "@mui/icons-material/RequestQuote";
import VolunteerActivismIcon from "@mui/icons-material/VolunteerActivism";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import ReceiptIcon from "@mui/icons-material/Receipt";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import Badge from "@mui/material/Badge";
import Popover from "@mui/material/Popover";
import { formatDate } from "../../Utils/generalFunctions";
import { ListComents } from "../ventasComponentes/ListComents";
import { NewComment } from "../ventaDirectaComponentes/NewComment";
import { Cancel, LinkOff } from "@mui/icons-material";
import BuscarProducto from "../productosComponentes/BuscarProducto";
import LinkIcon from '@mui/icons-material/Link';
import CloseIcon from '@mui/icons-material/Close';
import { FullWidthTextField } from "./components/CommentsComponent";
import { DynamicChipWithTooltip } from "./components/DynamicChipWithTooltip";


// export const FullWidthTextField = ({addNewCommentFunction, deleteComment, updateComment, comments}) => {
//   const [isAddingComment, setIsAddingComment] = React.useState(false); // Estado para controlar si se está agregando un comentario

//   const handleAddComment = () => {
//     setIsAddingComment(true); // Activar estado de agregar comentario
//   };

//   return (
//     <Box>
//       <ListComents
//         sx={{ width: "100%", bgcolor: "background.paper" }}
//         comments = {comments}
//         deleteComment = {deleteComment}
//         updateComment = {updateComment}
//       />

//       {isAddingComment ? (
//         <NewComment
//           addNewCommentFunction={addNewCommentFunction} 
//           commentType={'order'}
//           setIsAddingComment= {setIsAddingComment}
//           />
//       ) : (
//         <Grid container spacing={2}>
//           <Grid item sx={{ textAlign: "right" }}>
//             <Button
//               // fullWidth
//               variant="contained"
//               size="small"
//               color="buttonGreen"
//               onClick={handleAddComment}
//               // disabled={role !== "Admin"} // Desactivar botón de agregar comentario si el rol no es 'Admin'
//             >
//               <AddCircleOutlineIcon />
//               Comentario
//             </Button>
//           </Grid>
//         </Grid>
//       )}
//     </Box>
//   );
// };

const DesplegableProducto = ({ pedido, index, disabled = false }) => {
  //productos que hicieron en el pedido
  const [producto] = pedido.products;
  let skuVariationsPrint = "";
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  //ponemos en una linea las diferentes variaciones de un producto
  const mensajeRelacion = useSelector((store) => store.pedidos.msgRelacionProductos)
  const severityRelacion = useSelector((store) => store.pedidos.severityRelacionProductos)

  const getVariations = (data) => {
    if (producto?.variations?.length !== 0) {
      skuVariationsPrint = data?.map((variante) => variante.value).join(", ");
      return skuVariationsPrint;
    }
  };
  // ------------> funcion para manejar el status <--------
  const handleChangeStatus = (idPedido) => {
    // console.log("cambio el status, Id del pedido: ", idPedido);
  };

  const role = useSelector((store) => store.usuario.role);

  const isAdminn = () => {
    if (role === "Admin" || role === "Administrative_accountant") return true;
    return false;
  };

  const isWarehouseManager = () => {
    if (role === "Warehouse_manager") return true;
    return false;
  };

  const theme = useTheme();

  // abajo de 1280
  const isDownLargeScreen = useMediaQuery(theme.breakpoints.down("lg"));

  // entre 960 y 1060
  const detailOperationScreen = useMediaQuery(
    theme.breakpoints.down("detailOperationScreenNumber")
  );

  // pantallas chicas
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const isSxScreen = useMediaQuery(theme.breakpoints.down("xs"));

  // arriba de 1060
  const up1060 = useMediaQuery(
    theme.breakpoints.up("detailOperationScreenNumber")
  );

  // recorta el label si es muy largo
  const getShortenedLabel = (label) => {
    const maxLength = 20;
    if (label?.length > maxLength) {
      return `${label.substring(0, maxLength)}...`;
    }
    return label;
  };

  const getDetailsOperacionChip = (pedido, key) => {
    return (

      // <Box
      //   sx={{
      //     display: "flex",
      //     flexWrap: "wrap",
      //     justifyContent: "space-around",
      //     width: "100%",
      //   }}
      // >
      <>

        <DynamicChipWithTooltip
          type="price"
          value={` ${pedido.products[key]?.paidAmount !== null
            ? pedido.products[key].paidAmount
            : "N/D"
            }`}
          tooltipTitle={`Total: ${pedido.products[key]?.paidAmount !== null
            ? pedido.products[key].paidAmount
            : "No disponible"
            }`}
          icon={<RequestQuoteIcon />}
        />

        <DynamicChipWithTooltip

          type="price"
          value={` ${pedido.products[key]?.fee !== null 
            ? pedido.products[key].fee
            : "N/D"
            }`}
          tooltipTitle={`Tarifa: ${pedido.products[key]?.fee !== null
            ? pedido.products[key].fee
            : "No disponible"
            }`}
          icon={<VolunteerActivismIcon />}
        />

        <DynamicChipWithTooltip
          type="price"
          value={` ${pedido.products[key]?.shipping !== null
            ? pedido.products[key].shipping
            : "N/D"
            }`}
          tooltipTitle={`Envío: ${pedido.products[key]?.shipping !== null
            ? pedido.products[key].shipping
            : "No disponible"
            }`}
          icon={<LocalShippingIcon />}
        />

        <DynamicChipWithTooltip
          type="price"
          value={` ${pedido.products[key]?.receivedAmount !== null
            ? pedido.products[key].receivedAmount
            : "N/D"
            }`}
          tooltipTitle={`Recibido: ${pedido.products[key]?.receivedAmount !== null
            ? pedido.products[key].receivedAmount
            : "No disponible"
            }`}
          icon={<ReceiptIcon />}
        />

        <DynamicChipWithTooltip
          type="label"
          value={`  0`}
          tooltipTitle={`Utilidad: 0`}
          icon={<TrendingUpIcon />}
        />


      </>
    );
  };

  const [openLink, setOpenLink] = React.useState(false);
  const [selectedProduct, setSelectedProduct] = React.useState([]);
  const [variationOption, setVariationOption] = React.useState(null);
  const [selectedStores, setSelectedStores] = React.useState([]);
  const stores = useSelector((store) => store.almacenes.almacenes);
  // const { brand, model, operationId, photo, sku, title, units } = producto;

  const handleOpenLink = (product) => {
    setOpenLink(true);
  }

  const handleCloseLink = () => {
    setOpenLink(false);
  }

  const handleLinkProducts = (selectedProduct, variationOption, product, pedido) => {

    const publicationProductId = product.publicationProduct?.id;
    const internalSku = variationOption.internalSku;
    const storeIds = stores
      .filter(store => selectedStores.includes(store.storeName))
      .map(store => store.id);
    const supplierStoreIds = product.publicationProduct.publicationProduct_product_supplierStores
      .length === 0 ? [] : product.publicationProduct.publicationProduct_product_supplierStores.map((store) => store.store.id);
    const data = {
      publicationProductId: publicationProductId,
      internalSku: internalSku,
      storeIds: storeIds,
      supplierStoreIds: supplierStoreIds,
    }
    dispatch(RelacionarProductosConOrdenes(data, pedido.orderId, cookies.csrf_access_token));
  }

  React.useEffect(() => {
    if (mensajeRelacion) {
      if (severityRelacion === "success") {
        setTimeout(() => {
          dispatch(limpiarMensajeRelacion());
          handleCloseLink();
          window.location.reload();
        }, 2000);
      }
    }
  }, [mensajeRelacion, severityRelacion])

  // si no hay productos no muestra la tabla solo el detalle de venta
  if (pedido.products.length === 0) {
    return null;
  }

  const whatColor = (id) => {
    let color = "";
    color = orderStatuses.find(
      (statusObj) => statusObj.orderStatusId === id
    )?.orderStatusFlag;
    if (color === "Red") {
      return "error";
    } else {
      return "success";
    }


  }

  return (
    <React.Fragment>
      <div
        style={{
          marginBottom: "10px",
          marginTop: "10px",
          width: "100%",
        }}
      >
        <TableContainer
          sx={{
            backgroundColor: theme.palette.background.default,
            borderRadius: "20px",
            
            '& .MuiTableCell-root': {
              padding: '0px !important',
            },
          }}
        >
          <Table
          >
            <TableHead>
              <TableRow
                sx={{
                  '& .MuiTableCell-root': {
                    padding: '16px 8px !important',
                  },
                }}>
                {isSxScreen ? null : (
                  <TableCell align="center">Imagen</TableCell>
                )}
                {/* isDowmLargeScrenn -> 960 - 1060 */}
                {isDownLargeScreen && !up1060 ? (
                  <TableCell align="center">Datos Producto</TableCell>
                ) : up1060 ? (
                  <>
                    <TableCell align="center">Sku</TableCell>
                    <TableCell align="center">Modelo</TableCell>
                    <TableCell align="center">Marca</TableCell>

                    <TableCell align="center">Variacion</TableCell>
                  </>
                ) : null}
                {/* depende del tamaña de mi pantalla muestro una celda o la otra */}
                {isAdminn() ? (
                  <>
                    {isMediumScreen ? null : isDownLargeScreen ? (
                      <TableCell align="center">Operaciones</TableCell>
                    ) : (
                      <>
                        <TableCell align="center">Cantidad</TableCell>
                        <TableCell align="center">Total</TableCell>
                        <TableCell align="center">Tarifa</TableCell>
                        <TableCell align="center">Envio</TableCell>
                        <TableCell align="center">Recibido</TableCell>
                        <TableCell align="center">Utilidad</TableCell>
                      </>
                    )}
                  </>
                ) : null}
                {/* en pantalla chica el status se esconde y se pone arriba junto a la imagen*/}
                {isSxScreen ? null : (
                  <TableCell
                    align="center"
                    sx={isMediumScreen ? { padding: "0" } : {}}
                  >
                    Status
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody
              sx={{
                '& .MuiTableCell-root': {
                  padding: '8px !important',
                },
              }}>
              {/* iteramos en los productos para rellenar la tabla */}
              {/* se iran acomodando u ocultando los elementos conforme las pantallas cambien */}
              {pedido.products?.map((product, key) => (
                <TableRow key={key}>
                  {isSxScreen ? null : (
                    <TableCell align="center" sx={{ paddingLeft: "10px" }}>
                      <img src={product.photo} width="100%" height="auto" />
                    </TableCell>
                  )}
                  {detailOperationScreen ? (
                    <TableCell align="justify" sx={{ padding: "1em 0" }}>
                      {/* se acomoda la imagen junto con el status */}
                      {isSxScreen ? (
                        <Box sx={{ display: "flex", flexDirection: "row" }}>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "flex-start",
                              marginRight: "20px",
                            }}
                          >
                            {/* <Checkbox
                            // checked={checked}
                            onChange={() => handleChangeStatus(pedido.orderId)}
                            inputProps={{ "aria-label": "controlled" }}
                            disabled={!isWarehouseManager()}
                            sx={{ color: pink[800] }}
                          /> */}
                            <Badge
                              badgeContent={
                                <Checkbox
                                  //checked={checked}
                                  onChange={() =>
                                    handleChangeStatus(pedido.orderId)
                                  }
                                  inputProps={{ "aria-label": "controlled" }}
                                  disabled={!isWarehouseManager()}
                                  sx={{ color: pink[800], zIndex: "100" }}
                                />
                              }
                            >
                              <img
                                src={product.photo}
                                width="100px"
                                height="max-content"
                              />
                            </Badge>
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "right",
                              width: " 100%",
                              gap: "5px"
                            }}
                          >
                            <DynamicChipWithTooltip
                              type="label"
                              value={` ${product.publicationProduct.skuMarketplaceVariation}`}
                              tooltipTitle={`sku: ${product.publicationProduct.skuMarketplaceVariation}`}
                            />

                            <DynamicChipWithTooltip
                              type="label"
                              value={` ${getShortenedLabel(
                                product.model
                              )}`}
                              tooltipTitle={`Modelo: ${getShortenedLabel(
                                product.model
                              )}`}
                            />
                            <DynamicChipWithTooltip
                              type="label"
                              value={` ${getShortenedLabel(
                                product.brand
                              )}`}
                              tooltipTitle={`Marca: ${getShortenedLabel(
                                product.brand
                              )}`}
                            />
                            <DynamicChipWithTooltip
                              type="label"
                              value={`Variacion: ${getVariations(product.variations)
                                ? getVariations(product.variations)
                                : "N/A"
                                }`}
                              tooltipTitle={`Variacion: ${getVariations(product.variations)
                                ? getVariations(product.variations)
                                : "N/A"
                                }`}
                            />

                          </Box>
                        </Box>
                      ) : null}
                      {/* se muestra titulo, sku y demas info */}
                      <Box sx={{ display: "flex", flexWrap: "wrap" }}>
                        <Tooltip
                          placement="top"
                          title={
                            product.title
                          }

                        >
                          <Typography
                            style={{
                              margin: "auto",
                              fontSize:
                                isMediumScreen || isSxScreen ? "12px" : "14px",
                              // fontSize: "12px",
                            }}
                          >
                            {product.title.substring(0, 20) + "..."}
                          </Typography>
                        </Tooltip>
                        <br />
                        {!isSxScreen ? (
                          <Box
                            sx={{
                              display: "flex",
                              flexWrap: "wrap",
                              // justifyContent: "space-around",
                              gap: "5px",
                            }}
                          >
                            <DynamicChipWithTooltip
                              type="label"
                              value={` ${product.publicationProduct.skuMarketplaceVariation}`}
                              tooltipTitle={`sku: ${product.publicationProduct.skuMarketplaceVariation}`}
                            />

                            <DynamicChipWithTooltip
                              type="label"
                              value={` ${getShortenedLabel(
                                product.model
                              )}`}
                              tooltipTitle={`Modelo: ${getShortenedLabel(
                                product.model
                              )}`}
                            />
                            <DynamicChipWithTooltip
                              type="label"
                              value={` ${getShortenedLabel(
                                product.brand
                              )}`}
                              tooltipTitle={`Marca: ${getShortenedLabel(
                                product.brand
                              )}`}
                            />
                            <DynamicChipWithTooltip
                              type="label"
                              value={`Variacion: ${getVariations(product.variations)
                                ? getVariations(product.variations)
                                : "N/A"
                                }`}
                              tooltipTitle={`Variacion: ${getVariations(product.variations)
                                ? getVariations(product.variations)
                                : "N/A"
                                }`}
                            />

                            {/* me hara responsivo y solo se mostrara en un campo esta info */}
                            {/* si es admin puede ver la informacion finanxiera ye sta sera desde
                           una pantalla mediana en mi tabla  desplegable*/}
                            {isMediumScreen && isAdminn()
                              ? getDetailsOperacionChip(pedido, key)
                              : null}
                          </Box>
                        ) : (
                          <Box
                            sx={{
                              display: "flex",
                              flexWrap: "wrap",
                              // justifyContent: "space-around",
                              gap: "5px",
                            }}
                          >
                            {isAdminn()
                              ? getDetailsOperacionChip(pedido, key)
                              : null}
                          </Box>
                        )}
                      </Box>
                    </TableCell>
                  ) : (
                    <>
                      <TableCell align="center">
                        <Box sx={{ display: "flex", flexDirection: "column" }}>
                          <TableCell align="center" sx={{ height: '80px', overflow: 'auto', alignContent: 'center' }}>
                            {product.publicationProduct.skuMarketplaceVariation ? product.publicationProduct.skuMarketplaceVariation : "N/A"}
                          </TableCell>
                          <TableCell align="center" sx={{ height: '80px', overflow: 'auto', alignContent: 'center' }}>
                            {product.publicationProduct.publicationProduct_product_stores[0]?.poduct_store?.product.internalSku ?
                              product.publicationProduct.publicationProduct_product_stores[0]?.poduct_store?.product.internalSku :
                              product.publicationProduct.publicationProduct_product_supplierStores[0]?.product_supplierStore?.product.internalSku ?
                                product.publicationProduct.publicationProduct_product_supplierStores[0]?.product_supplierStore?.product.internalSku : "N/A"}
                          </TableCell>
                        </Box>
                      </TableCell>

                      <TableCell align="center">
                        <Box sx={{ display: "flex", flexDirection: "column" }}>
                          <TableCell align="center" sx={{ height: '80px', overflow: 'auto', alignContent: 'center' }}>
                            {product.model ? product.model : "N/A"}
                          </TableCell>
                          <TableCell align="center" sx={{ height: '80px', overflow: 'auto', alignContent: 'center' }}>
                            {product?.publicationProduct?.publicationProduct_product_stores[0]?.poduct_store?.product?.productBase?.model ?
                              product?.publicationProduct?.publicationProduct_product_stores[0]?.poduct_store?.product?.productBase?.model :
                              product?.publicationProduct?.publicationProduct_product_supplierStores[0]?.product_supplierStore?.product?.productBase?.model ?
                                product?.publicationProduct?.publicationProduct_product_supplierStores[0]?.product_supplierStore?.product?.productBase?.model : "N/A"}
                          </TableCell>
                        </Box>
                      </TableCell>

                      <TableCell align="center">
                        <Box sx={{ display: "flex", flexDirection: "column" }}>
                          <TableCell align="center" sx={{ height: '80px', overflow: 'auto', alignContent: 'center' }}>
                            {product.brand ? product.brand : "N/A"}
                          </TableCell>
                          <TableCell align="center" sx={{ height: '80px', overflow: 'auto', alignContent: 'center' }}>
                            {product?.publicationProduct?.publicationProduct_product_stores[0]?.poduct_store?.product?.productBase?.brand ?
                              product?.publicationProduct?.publicationProduct_product_stores[0]?.poduct_store?.product?.productBase?.brand :
                              product?.publicationProduct?.publicationProduct_product_supplierStores[0]?.product_supplierStore?.product?.productBase?.brand ?
                                product?.publicationProduct?.publicationProduct_product_supplierStores[0]?.product_supplierStore?.product?.productBase?.brand : "N/A"}
                          </TableCell>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: "flex", flexDirection: "column" }}>
                          <TableCell align="center" sx={{ height: '80px', overflow: 'auto', alignContent: 'center' }}>
                            {product.title ? product.title : "N/A"}
                          </TableCell>
                          <TableCell align="center" sx={{ height: '80px', overflow: 'auto', alignContent: 'center' }}>
                            {product?.publicationProduct?.publicationProduct_product_stores[0]?.poduct_store?.product?.variationDescription ?
                              product?.publicationProduct?.publicationProduct_product_stores[0]?.poduct_store?.product?.variationDescription :
                              product?.publicationProduct?.publicationProduct_product_supplierStores[0]?.product_supplierStore?.product?.variationDescription ?
                                product?.publicationProduct?.publicationProduct_product_supplierStores[0]?.product_supplierStore?.product?.variationDescription : "N/A"}
                          </TableCell>
                        </Box>
                      </TableCell>
                    </>
                  )}
                  {/*le enviamos las variaciones de cada producto para que nos regrese estas */}
                  {getVariations(product.variations) ? (
                    <TableCell align="center">
                      {getVariations(product.variations)
                        ? getVariations(product.variations)
                        : null}
                    </TableCell>
                  ) : null}
                  {/* {getVariations(product.variations) ?  <TableCell  align="center"> {getVariations(product.variations)}</TableCell> : null} */}
                  {isAdminn() ? (
                    <>
                      {/* depende del tipo de pantalla se pintaran los chips o la tabla completa */}
                      {/* si es admin se pintara la informacion financiera */}
                      {isMediumScreen ? null : isDownLargeScreen &&
                        isAdminn() ? (
                        <TableCell>
                          {getDetailsOperacionChip(pedido, key)}
                        </TableCell>
                      ) : (
                        <>
                          <TableCell align="center">
                            {pedido.products?.[key].units}
                          </TableCell>
                          <TableCell align="center">
                            ${pedido.products?.[key].paidAmount}
                          </TableCell>
                          <TableCell align="center">
                            ${pedido.products[key].fee}
                          </TableCell>
                          <TableCell align="center">
                            ${pedido.products[key].shipping}
                          </TableCell>
                          <TableCell align="center">
                            ${pedido.products[key].receivedAmount}
                          </TableCell>
                          <TableCell align="center">0</TableCell>
                        </>
                      )}
                    </>
                  ) : null}
                  {isSxScreen ? null : (
                    <TableCell
                      align="center"
                      sx={isMediumScreen ? { padding: "0" } : {}}
                    >
                      <Button
                        variant="contained"
                        disabled={disabled}
                        title={pedido.products[key].publicationProduct.publicationProduct_product_stores.length > 0
                          || pedido.products[key].publicationProduct.publicationProduct_product_supplierStores.length > 0
                          ? "Producto relacionado" : "Relacionar producto"}
                        color={pedido.products[key].publicationProduct.publicationProduct_product_stores.length > 0
                          || pedido.products[key].publicationProduct.publicationProduct_product_supplierStores.length > 0
                          ? "success" : "error"}
                        sx={{ height: "30%" }}
                        onClick={() => {
                          pedido.products[key].publicationProduct.publicationProduct_product_stores.length > 0
                            || pedido.products[key].publicationProduct.publicationProduct_product_supplierStores.length > 0
                            ? null :
                            handleOpenLink(pedido.products[key])
                        }
                        }
                      >
                        {pedido.products[key].publicationProduct.publicationProduct_product_stores.length > 0
                          || pedido.products[key].publicationProduct.publicationProduct_product_supplierStores.length > 0
                          ? <LinkIcon /> : <LinkOff />}
                      </Button>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
      <Dialog
        open={openLink}
        onClose={handleCloseLink}
        PaperProps={{
          classes: {
            width: "100%",
            height: "95%",
          },
        }}
      >
        <DialogTitle>Relacionando el producto {producto.title}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12} sx={{ marginBottom: "1%" }}>
              <BuscarProducto selectedStores={selectedStores} setSelectedStores={setSelectedStores} selectedProduct={selectedProduct} listProductStores={true} direcSale={true} fullW={true} setSelectedProduct={setSelectedProduct} setVariationOption={setVariationOption} />
            </Grid>
          </Grid>
          <Grid container spacing={2}>
            <Grid item xs={8}>
            </Grid>
            <Grid item xs={2}>
              <Button
                variant="contained"
                color="error"
                onClick={() => {
                  handleCloseLink();
                }}
                size="small"
              >
                Cancelar
              </Button>
            </Grid>
            <Grid item xs={2}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => {
                  handleLinkProducts(selectedProduct, variationOption, producto, pedido);
                }}
                size="small"
              >
                Relacionar
              </Button>
            </Grid>
          </Grid>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <>
                {mensajeRelacion &&
                  <Alert
                    severity={severityRelacion}
                    sx={{ margin: "1rem" }}
                    action={
                      <IconButton
                        aria-label="close"
                        color="inherit"
                        size="small"
                        onClick={() => {
                          dispatch(limpiarMensajeRelacion());
                        }
                        }
                      >
                        <CloseIcon fontSize="inherit" />
                      </IconButton>
                    }>
                    {mensajeRelacion.toString()}
                  </Alert>
                }
              </>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>
      {/* {producto.variations == 0 ? "SIN VARIACIONES" : <MostarVariaciones variaciones={producto.variations} />  }  */}
    </React.Fragment>
  );
};

const DetalleDesplegable = ({ marketplaces, addNewCommentFunction, deleteComment, updateComment, pedido, comments, type, index, disabled = false }) => {
  const namesStatus = useSelector((store) => store.pedidos.orderStatus);
  const namesStatusInternoV2 = useSelector(
    (store) => store.pedidos.orderInternalStatus
  );
  const [namesStatusInterno, setNamesStatusInterno] = React.useState([]);


  React.useEffect(() => {
    const orderInternalStatusTypesMap = {};
    namesStatusInternoV2.forEach((status) => {
      orderInternalStatusTypesMap[status.orderInternalStatusTypeId] =
        status.orderInternalStatusType;
    });

    // Crear el arreglo final
    const namesStatusInternoFinal = [];
    let currentType = "";
    namesStatusInternoV2.forEach((status) => {
      if (status.orderInternalStatusType !== currentType) {
        currentType = status.orderInternalStatusType;
        namesStatusInternoFinal.push({
          type: "listSubheader",
          subHeaderName: currentType,
        });
      }
      namesStatusInternoFinal.push({
        type: "menuItem",
        orderInternalStatus: status.orderInternalStatus,
        orderInternalStatusId: status.orderInternalStatusId,
        orderInternalStatusType: status.orderInternalStatusType,
        orderInternalStatusTypeId: status.orderInternalStatusTypeId,
      });
    });
    setNamesStatusInterno(namesStatusInternoFinal);
  }, [namesStatusInternoV2]);
  const theme = useTheme();
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const isSxScreen = useMediaQuery(theme.breakpoints.down("xs"));

  const getMarketplaceName = (pedidoMarketplaceId) => {
    const matchedMarketplace = marketplaces.find(
      (marketplace) =>
        marketplace.supportedMarketplace.id ===
        pedidoMarketplaceId
    );
    if (matchedMarketplace) {
      return matchedMarketplace.supportedMarketplace.name;
    } else {
      return "Marketplace desconocido";
    }
  };

  return (
    <React.Fragment>
      <CardContent
        // sx={{
        //   backgroundColor: theme => theme.palette.background.default,
        //   borderRadius: "20px",
        // }}
        sx={{
          '& .MuiCardContent-root': {
            padding: "0  !important",
          },
        }}
        style={{
          width: "100%",
          marginBottom: "10px",
          marginTop: "10px",
          padding: "10px 0",
        }}>
        <Box sx={{ width: "100%" }}>
          {/* <CardContent style={{ border: "1px solid " }}> */}
          {/* <Typography sx={{ fontSize: 14 }} color="text.secondary" gutterBottom>
            {isSxScreen || isMediumScreen ? (
              <span>ID: {pedido.orderId} | </span>
            ) : (
              <span>Pedido con ID: {type === "consolidate" ? pedido.id : pedido.orderId} | CreationDate: </span>
            )}

            { type === "consolidate" ? pedido.date : crearFechaFormato(pedido.creationDate)}
          </Typography>

          <Typography sx={{ fontSize: 14 }} component="div">
            {getMarketplaceName(pedido.marketplaceId)} - OrderID:
            <a target="_blank" href={pedido.orderURL}>
              {pedido.marketplaceOrderId}
            </a>
          </Typography> */}
          {/* <Typography sx={{ mb: 1.5 }} color="text.secondary">
            Canal:{" "}
            {getfullFilmentChannelListPropertyUsingfullFiltmentNumber(
              pedido.fulfillmentChannel.toString(),
              "fullFilmentChannelName"
            )}
          </Typography> */}
          {/* <Typography variant="body2">
            <Chip
              variant="outlined"
              sx={{ margin: "0.5em" }}
              size="small"
              label={`Status: ${getStatusPropertyUsingStatusId2(
                pedido.orderStatusId,
                "orderStatus",
                namesStatus
              )}`}
            />
            <Chip
              variant="outlined"
              size="small"
              sx={{ margin: "0.5em" }}
              label={`Status Inter: ${getStatusInternoPropertyUsingStatusId2(
                pedido.orderInternalStatusId,
                "orderInternalStatus",
                namesStatusInterno
              )}`}
            />
            <br />
            {pedido.operationIds ? (
              <Chip
                variant="outlined"
                size="small"
                sx={{ margin: "0.5em" }}
                label={`OperationIds: ${pedido.operationIds}`}
              />
            ) : null}
          </Typography> */}

        </Box>
        {/* <Box>
          <Chip
            variant="outlined"
            size="small"
            color="primary"
            label={`Numero de productos: ${pedido.products.length}`}
          />
        </Box> */}
        <DesplegableProducto pedido={pedido} index={index} disabled={disabled} />
        {pedido.reasonsForCancellation === "" ||
          pedido.reasonsForCancellation === null ||
          pedido.reasonsForCancellation === undefined ? null : (
          <Typography sx={{ mb: 1.5 }} color="text.secondary">
            Razón de cancelación: {pedido.reasonsForCancellation}
          </Typography>
        )}
      </CardContent>
    </React.Fragment >
  );
};

export default DetalleDesplegable;
