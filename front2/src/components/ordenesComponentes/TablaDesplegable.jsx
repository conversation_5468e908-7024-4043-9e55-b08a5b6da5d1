import * as React from "react";
import Box from "@mui/material/Box";
import Collapse from "@mui/material/Collapse";
import IconButton from "@mui/material/IconButton";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import Typography from "@mui/material/Typography";
import Paper from "@mui/material/Paper";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import Grid from "@mui/material/Grid";
import Avatar from "@mui/material/Avatar";
import PlusOneIcon from "@mui/icons-material/PlusOne";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import ListSubheader from "@mui/material/ListSubheader";
import { styled, useTheme } from "@mui/material/styles";
import { useDispatch, useSelector } from "react-redux";
import EditAttributesIcon from "@mui/icons-material/EditAttributes";
import {
  imprimirGuiaDeUnaOrden,
  surtiendoPedido,
} from "../../redux/pedidosDucks";
import DetalleDesplegable from "./DetalleDesplegable";
import {
  Red,
  Green,
  Orange,
  Yellow,
  Grey,
  getfullFilmentChannelListPropertyUsingfullFiltmentNumber,
  getStatusPropertyUsingStatusId2,
  crearFechaFormato,
  checkPaidAmount,
  // IsAdmin
} from "../../Utils/atributtesHandler";
import { useCookies } from "react-cookie";
import { useCallback } from "react";
import {
  Alert,
  Badge,
  Button,
  Checkbox,
  Chip, Radio,
  Skeleton,
  TableCell,
  TableRow,
  TextField,
  useMediaQuery,
} from "@mui/material";
import { useEffect } from "react";
import { Tooltip } from "@mui/material";
import { ClickAwayListener } from "@mui/material";
import { useState } from "react";
import FaceIcon from "@mui/icons-material/Face";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import RequestQuoteIcon from "@mui/icons-material/RequestQuote";
import VolunteerActivismIcon from "@mui/icons-material/VolunteerActivism";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import ReceiptIcon from "@mui/icons-material/Receipt";
import PrintIcon from "@mui/icons-material/Print";
import { Dialog, DialogTitle, DialogContent } from "@mui/material";
import {
  CheckBox,
  Email,
  LinkOff,
  Message,
  ShoppingCart,
  Visibility,
  VisibilityOff,
} from "@mui/icons-material";
import CargandoLista from "../CargandoLista";
import { SurtidoVenta } from "../ventasComponentes/SurtidoVentaDirecta/SurtidoVentaDirectaWrapper";
import LinkIcon from "@mui/icons-material/Link";
import Link from '@mui/material/Link';
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import { CircularProgress } from "@mui/material";
import {
  addNewCommentFunctionGeneral,
  deleteCommentGeneral,
  updateCommentGeneral,
  returnCommentTypeUrl,
  noComment,
  changeOrderInternalStatus,
  orderStatuses,
  getOrderCommentsWithDeleted,
} from "../utils/utils";
import { setMsgVentas } from "../../redux/pedidosDucks";
import { StyledTableCell, StyledTableRow } from "../StyledTableComponents";
import RowMenu from "../ventasComponentes/SurtidoVentaDirecta/RowMenu";
import { AlertLocalComponent } from "./components/AlertLocalComponent";
import { ButtonComment } from "./components/ButtonComment";
import { FullWidthTextField } from "./components/CommentsComponent";
import CustomDialog from "../componentesGenerales/CustomDialog";
import { BreakdownSale } from "./components/BreakdownSale";
import { AlertComponent } from "../componentesGenerales/Alert";
import { AutoGridNoWrap } from "./components/AutoGridNoWrap";
import { useCustomDateTime } from "../menusYBarrasComponentes/HookDate";
import { getRandomName } from "./names";
import Messages from "../mensajesComponentes/SendMessages";
import SendMessages from "../mensajesComponentes/SendMessages";
import MessagesList from "../mensajesComponentes/MessagesList";
import DefaultMessages from "../mensajesComponentes/DefaultMessages";
import { OptionsBtns } from "./components/OptionsBtns";
import { RadioComponent } from "./components/RadioComponent";
import { DynamicChipWithTooltip } from "./components/DynamicChipWithTooltip";
import { RepeatChips } from "./components/RepeatChips";
import { clearOrderMessages, markAsRead, obtenerMensajesOrden } from "../../redux/mensajesDucks";
///////////////
const BoldText = ({ children }) => {
  return <span style={{ fontWeight: "bold" }}>{children}</span>;
};

const ItalicText = ({ children }) => {
  return <span style={{ fontStyle: "italic" }}>{children}</span>;
};

const Cliente = (props) => {
  const { setVisibleMessage, pendingToResponse } = props;
  const cliente = props.cliente;
  const theme = useTheme();
  return cliente === null || cliente === "" || cliente === undefined ? (
    <React.Fragment>
      <Typography variant="body2">Sin cliente registrado</Typography>
    </React.Fragment>
  ) : (
    <React.Fragment>
      <Typography sx={{ fontSize: 11 }} color="text.secondary" gutterBottom>
        {(cliente.nickname === null ||
          cliente.nickname === "" ||
          cliente.nickname === undefined
          ? "Sin nickName"
          : cliente.nickname) +
          "(" +
          (cliente.score !== 0 ? cliente.score : "Sin") +
          " puntos)"}
      </Typography>
      <Typography variant="body2" component="div">
        {cliente.name === null ||
          cliente.name === "" ||
          cliente.name === undefined
          ? "Sin nombre"
          : cliente.name}
      </Typography>
      {/* <IconButton
        size="small"
        onClick={() => {
          setVisibleMessage(true);
        }}
        sx={{
          textAlign: "left",
          width: "30px",
        }}
      >
        <Badge
          color="buttonGreen"
          variant="dot"
          invisible={!pendingToResponse}
          sx={{
            textAlign: "left",
            width: "30px",
          }}
        >
          <Email />
        </Badge>
      </IconButton> */}
    </React.Fragment>
  );
};

const OutlinedCard = (props) => {
  const fecha = props.fecha;
  const fulfillmentChannel = props.fulfillmentChannel;
  const { setVisibleMessage, pendingToResponse } = props;


  const handleOpenSendMessages = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setVisibleMessage(true);
  };
  return (
    <Box sx={{
      minWidth: 170,
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-between",
      width: "100%",
      margin: "0",
      gap: "10px",
      padding: "16px 0",
    }}>
      <Typography
        sx={{
          fontWeight: "400",
          lineHeight: "16px",
          fontSize: "12px",
        }}
      >
        {fecha}
      </Typography>
      <Typography
        sx={{
          fontWeight: "400",
          lineHeight: "20px",
          fontSize: "14px",
          display: "flex",
          alignItems: "center",
        }}
      >
        {fulfillmentChannel}

        <IconButton
          size="small"
          onClick={(e) => {
            handleOpenSendMessages(e)
          }}
          sx={{
            textAlign: "left",
            width: "30px",
            marginLeft: "15px",
          }}
        >
          <Badge
            color="buttonGreen"
            variant="dot"
            invisible={!pendingToResponse}
            sx={{
              textAlign: "left",
              width: "30px",
            }}
          >
            <Email />
          </Badge>
        </IconButton>

      </Typography>
      {/* <Card variant="outlined"> */}

      <Cliente
        cliente={props.cliente}
        setVisibleMessage={setVisibleMessage}
        pendingToResponse={pendingToResponse}
      />

    </Box>
  );
};

const getVariableByName = (name) => {
  switch (name) {
    case "Red":
      return Red;
    case "Orange":
      return Orange;
    case "Yellow":
      return "rgba(227,227,0)";
    case "Green":
      return Green;
    case "Grey":
      return Grey;
    default:
      return Grey; // Valor predeterminado en caso de que no se encuentre ninguna coincidencia
  }
};

/**
 * Genera los estilos para las filas que tienen estado de error.
 * Incluye efectos de hover y animaciones para mejor feedback visual.
 * 
  * @param {object} theme - El tema actual de Material-UI.
 * @returns {object} - Un objeto con los estilos para las filas que tienen estado de error.
 */
const getErrorStyles = (theme) => ({
  backgroundColor: theme.palette.mode === 'light' ? '#FFA1A1' : '#2D1717',
  "&::after": {
    content: '"Esta orden presenta un problema, haz clic para saber más."',
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: theme.palette.mode === "light"
      ? "rgba(179, 91, 91, 0.21)"
      : "rgba(89, 42, 42, 0.21)",
    backdropFilter: "blur(2px)",
    zIndex: 10,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    color: theme.palette.mode === "light" ? "black" : "white",
    fontWeight: "bold",
    opacity: 0,
    transition: "opacity 0.3s ease-in-out",
  },
  "&:hover::after": {
    opacity: 1,
    animation: "fadeIn 0.3s ease-in-out",
  },
  "@keyframes fadeIn": {
    "0%": { opacity: 0, transform: "scale(0.95)" },
    "100%": { opacity: 1, transform: "scale(1)" },
  },
  "@keyframes fadeOut": {
    "0%": { opacity: 1, transform: "scale(1)" },
    "100%": { opacity: 0, transform: "scale(0.95)" },
  },
});

/**
* Genera los estilos para las filas alternadas de la tabla.
* 
* @param {Object} theme - Tema actual de MUI
* @param {number} index - Índice de la fila
* @returns {string} Color de fondo para la fila
*/
const getAlternateRowStyle = (theme, index) => {
  // debugger
  const indexx = index % 2;
  return (
    index % 2 === 1
      ? theme.palette.mode === "light"
        ? theme.palette.primary.backgroundPink
        : theme.palette.primary.greenMainTable
      : theme.palette.mode === "light"
        ? theme.palette.primary.backgroundPink
        : theme.palette.primary.greenMainTable
  );
};

/**
 * Componente que representa una fila en la tabla de pedidos.
 * Maneja la visualización y comportamiento de cada pedido individual.
 * 
 * @component
 * @param {Object} props - Propiedades del componente
 * @param {Object} props.pedido - Datos del pedido a mostrar
 * @param {string|number} props.id - Identificador único del pedido
 * @param {number} props.index - Índice de la fila en la tabla
 * @param {Object} props.theme - Tema actual de MUI
 * @param {Function} props.handleShowDetails - Función para mostrar detalles del pedido
 * @returns {JSX.Element} Fila de la tabla con los datos del pedido
 */

export const Row = (props) => {
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const {
    id,
    marketplaces,
    marketplaceLogos,
    pedido,
    isSmallScreen,
    isMediumScreen,
    isDownLargeScreen,
    detailOperationScreen,
    isSxScreen,
    ordenesSeleccionadas,
    setOrdenesSeleccionadas,
    setOpenSurtir,
    type, index,
    namesStatus,
    namesStatusInternoV2,
  } = props;


  const commentType = "order";

  console.log(pedido, "pedido")
  // const [comments, setComments] = React.useState(pedido.comments == undefined ? [] : pedido.comments);
  const [comments, setComments] = React.useState(pedido.comments);
  const [lastComment, setLastComment] = React.useState(
    comments?.length > 0 ? comments[comments.length - 1].orderStackableCommentRelevantRecords.lastCommentRecord : noComment
  );

  const [currentOrderInternalStatusId, setCurrentOrderInternalStatusId] =
    React.useState(
      type === "consolidate"
        ? pedido.orderInternalStatus_Order.orderInternalStatusChange.orderInternalStatus.internalStatusId
        : pedido.orderInternalStatus_Order.orderInternalStatusChange.orderInternalStatus.orderInternalStatusId
    );
  const commentTypeUrlAdd = returnCommentTypeUrl(commentType, "add");
  const commentTypeUrlDelete = returnCommentTypeUrl(commentType, "delete");
  const commentTypeUrlUpdate = returnCommentTypeUrl(commentType, "update");
  const csrf_access_token = cookies.csrf_access_token;

  const [VisibleMessage, setVisibleMessage] = React.useState(false);

  const esURLPDF = (url) => {
    // Verifica si la URL es not found
    return url.endsWith("NotFoundPage");
  };
  const [anchorEl, setAnchorEl] = React.useState(null);
  const openAnchor = Boolean(anchorEl);
  const handleClick = React.useCallback((event) => {
    setAnchorEl(event.currentTarget);
  }, []);
  const handleClose = React.useCallback(() => {
    setAnchorEl(null);
  }, []);

  const handleOpenSurtir = React.useCallback((pedido) => {
    dispatch(surtiendoPedido(pedido));
    setOpenSurtir(true);
  }, [dispatch, setOpenSurtir]);

  const hasNonEmptyStores = React.useCallback((pedido) => {
    // Revisamos si todos los productos tienen al menos uno de los arreglos no vacíos
    const allNonEmpty = pedido.products?.every(
      (product) =>
        product.publicationProduct.publicationProduct_product_stores.length >
        0 ||
        product.publicationProduct.publicationProduct_product_supplierStores
          .length > 0
    );

    // Revisamos si al menos un producto tiene ambos arreglos vacíos
    const anyEmpty = pedido.products?.some(
      (product) =>
        product.publicationProduct.publicationProduct_product_stores.length ===
        0 &&
        product.publicationProduct.publicationProduct_product_supplierStores
          .length === 0
    );

    // Determinamos qué icono mostrar
    if (allNonEmpty && !anyEmpty) {
      return true;
    } else if (anyEmpty) {
      return false;
    }
  }, []);

  const hasShippedUnits = React.useCallback((pedido) => {
    const allShipped = pedido.products?.every(
      (product) => product.units === product.shippedUnits
    );
    const anyShipped = pedido.products?.some(
      (product) => product.units !== product.shippedUnits
    );

    if (allShipped && !anyShipped) {
      return true;
    } else if (anyShipped) {
      return false;
    }
  }, []);

  const handleOpenGuide = React.useCallback((orderId) => {
    dispatch(imprimirGuiaDeUnaOrden(orderId, csrf_access_token));
    setOpenGuide(true);
  }, [dispatch, csrf_access_token]);

  const listbotons = React.useCallback(() => {
    return [
      <MenuItem
        onClick={() =>
          hasNonEmptyStores(pedido) === true
            ? hasShippedUnits(pedido) === false
              ? handleOpenSurtir(pedido)
              : null
            : null
        }
        disableRipple
        disabled={
          hasNonEmptyStores(pedido) === true
            ? hasShippedUnits(pedido) === false
              ? false
              : true
            : true
        }
        style={{ pointerEvents: "auto" }}
      >
        <Tooltip
          title={
            hasNonEmptyStores(pedido) === false
              ? "Debes relacionar la orden con productos del sistema"
              : hasShippedUnits(pedido) === true
                ? "Ya se han surtido todas las unidades"
                : ""
          }
        >
          <span
            style={{ display: "flex", alignItems: "center", width: "100%" }}
          >
            <LocalShippingIcon />
            Surtir
          </span>
        </Tooltip>
      </MenuItem>,

      <MenuItem
        onClick={() => {
          if (pedido.guideInfo.status) {
            handleOpenGuide(id);
          }
        }}
        disableRipple
        disabled={!pedido.guideInfo?.status}
        style={{ pointerEvents: "auto" }}
      >
        <Tooltip
          title={
            !pedido.guideInfo?.status
              ? pedido.guideInfo?.message
                ? pedido.guideInfo.message
                : "guía no disponible"
              : ""
          }
        >
          <span
            style={{ display: "flex", alignItems: "center", width: "100%" }}
          >
            <PrintIcon />
            Imprimir guía
          </span>
        </Tooltip>
      </MenuItem>,
    ];
  }, [pedido, id, hasNonEmptyStores, hasShippedUnits, handleOpenSurtir, handleOpenGuide]);


  // Transformas el arreglo recibido del endpoint
  const transformedNamesStatus = React.useMemo(() => orderStatuses.map((statusObj) => ({
    ...statusObj,
    orderStatusFlag: getVariableByName(statusObj.orderStatusFlag),
  })), [orderStatuses]);

  const [open, setOpen] = React.useState(false);
  const infoUser = useSelector((store) => {
    return store.usuario.info;
  });

  const addNewCommentFunction = React.useCallback((commentToAdd) => {

    return addNewCommentFunctionGeneral(
      id,
      commentToAdd,
      commentType,
      csrf_access_token,
      comments,
      setComments,
      setLastComment,
      commentTypeUrlAdd,
      "add",
      infoUser.email,
      infoUser.name
    );
  }, [id, commentType, csrf_access_token, comments, commentTypeUrlAdd, infoUser.email, infoUser.name]);
  const getOrderCommentsWithDeletedFunction = React.useCallback(async (idOrder, showDeletedComments = false) => {
    return getOrderCommentsWithDeleted(idOrder, showDeletedComments, setComments);
  }, []);
  const deleteComment = React.useCallback((id) => {
    return deleteCommentGeneral(
      id,
      commentTypeUrlDelete,
      csrf_access_token,
      comments,
      setComments,
      setLastComment,
      lastComment,
      noComment,
      "delete"
    );
  }, [commentTypeUrlDelete, csrf_access_token, comments, lastComment]);

  const updateComment = React.useCallback(async (id, commentToUpdate) => {
    return updateCommentGeneral(
      id,
      commentToUpdate,
      csrf_access_token,
      commentTypeUrlUpdate,
      comments,
      setComments,
      setLastComment,
      "update",
      infoUser.email,
      infoUser.name
    );
  }, [commentTypeUrlUpdate, csrf_access_token, comments, infoUser.email, infoUser.name]);

  const [checked, setChecked] = React.useState(false);

  const handleChangeChecked = React.useCallback((event) => {
    if (!ordenesSeleccionadas.includes(id)) {
      const newOrdenesSeleccionadas = [...ordenesSeleccionadas, id];
      setOrdenesSeleccionadas(newOrdenesSeleccionadas);
      setChecked(true);
    } else if (ordenesSeleccionadas.includes(id)) {
      const newOrdenesSeleccionadas = ordenesSeleccionadas.filter(
        (orden) => orden !== id
      );
      setOrdenesSeleccionadas(newOrdenesSeleccionadas);
      setChecked(false);
    }
  }, [id, ordenesSeleccionadas, setOrdenesSeleccionadas]);

  React.useEffect(() => {
    if (ordenesSeleccionadas.includes(id)) {
      setChecked(true);
    } else {
      setChecked(false);
    }
  }, [ordenesSeleccionadas, id]);

  const [openGuide, setOpenGuide] = useState(false);
  const guiaContenido = useSelector((store) => store.pedidos.guiaContenido);
  const guiaType = useSelector((store) => store.pedidos.guiaType);
  const guiaEspera = useSelector((store) => store.pedidos.guiaEspera);
  const guiaError = useSelector((store) => store.pedidos.guiaError);

  const handleCloseGuide = React.useCallback(() => {
    setOpenGuide(false);
  }, []);
  const fulfillmentChannel =
    getfullFilmentChannelListPropertyUsingfullFiltmentNumber(
      pedido.fulfillmentChannel.toString(),
      "fullFilmentChannelName"
    );

  const date = useCustomDateTime(pedido.creationDate);

  const fecha = date.adjustedDateTime


  // const fecha = type === "consolidate" ? pedido.creationDate : crearFechaFormato(pedido.creationDate);

  const status = getStatusPropertyUsingStatusId2(
    pedido.orderStatusId,
    "orderStatus",
    transformedNamesStatus
  );
  const alerta = getStatusPropertyUsingStatusId2(
    pedido.orderStatusId,
    "orderStatusFlag",
    transformedNamesStatus
  );

  const [marketplaceLogo, setMarketplaceLogo] = useState(null);

  useEffect(() => {
    // Buscar el marketplace correspondiente al pedido

    const marketplaceEncontrado = marketplaces.find(
      (marketplace) =>
        marketplace.supportedMarketplace.id === pedido.marketplaceId
    );
    // Si se encontró el marketplace, establecer la imagen correspondiente
    if (marketplaceEncontrado) {
      const imagenMarketplace = marketplaceLogos?.find(
        (item) =>
          item.supportedMarketplaceId ===
          marketplaceEncontrado.supportedMarketplace.id
      );

      if (imagenMarketplace) {
        setMarketplaceLogo(imagenMarketplace.imagen);
      } else {
        setMarketplaceLogo(null);
      }
    } else {
      setMarketplaceLogo(null);
    }
  }, [marketplaceLogos, marketplaces, pedido]);

  const role = useSelector((store) => store.usuario.role);

  const isAdmin = useCallback(() => {
    if (role === "Admin" || role === "Administrative_accountant") return true;
    return false;
  }, [role]);

  const label = pedido.client?.name ? pedido.client.name : "";
  const tooltipTitle = label.length > 20 ? label : null;
  const [statusGuide, setStatusGuide] = useState(
    currentOrderInternalStatusId === 6 ? false : true
  );
  useEffect(() => {
    currentOrderInternalStatusId === 6
      ? setStatusGuide(false)
      : setStatusGuide(true);
  }, [currentOrderInternalStatusId]);

  const [showDefaultMessages, setShowDefaultMessages] = useState(false);
  const theme = useTheme();

  const handleCloseMessages = React.useCallback(() => {
    dispatch(clearOrderMessages());
    setVisibleMessage(false);
    setShowDefaultMessages(false);
  }, [dispatch]);

  const handleShowDefaultMessages = React.useCallback(() => {
    setShowDefaultMessages(!showDefaultMessages);
  }, [showDefaultMessages]);

  const [randomName] = useState(getRandomName);


  const FormatMoneyDetails = ({ type, amount }) => {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >

        <Typography
          variant="body2" color="text.secondary"
          sx={{
            fontWeight: "400",
            color: theme.palette.text.greenGrey,
            whiteSpace: "nowrap",
          }}>{type}: </Typography>


        <Typography component="span" variant="body2" sx={{
          fontWeight: "400",
          lineHeight: "24px",
          fontSize: "16px",
          color: theme.palette.text.primary,
          whiteSpace: "nowrap",
        }} >
          {amount !== null ? "$ " + amount : "N/D"}
        </Typography>
      </Box>
    );
  };


  // const addNewCommentFunction = (commentToAdd) => {
  //   return addNewCommentFunctionGeneral(
  //     id,
  //     commentToAdd,
  //     commentType,
  //     csrf_access_token,
  //     comments,
  //     setComments,
  //     setLastComment,
  //     commentTypeUrlAdd
  //   );
  // };
  // const deleteComment = (id) => {
  //   return deleteCommentGeneral(
  //     id,
  //     commentTypeUrlDelete,
  //     csrf_access_token,
  //     comments,
  //     setComments,
  //     setLastComment,
  //     lastComment,
  //     noComment
  //   );
  // };

  // const updateComment = async (id, commentToUpdate) => {
  //   return updateCommentGeneral(
  //     id,
  //     commentToUpdate,
  //     csrf_access_token,
  //     commentTypeUrlUpdate,
  //     comments,
  //     setComments,
  //     setLastComment
  //   );
  // };


  const handleShowDetails = React.useCallback((e, pedido) => {
    setOpen(!open);
  }, [open]);

  const handleCloseDesglose = React.useCallback(() => {
    setOpen(false);
  }, []);

  const [alertMessage, setAlertMessage] = useState("");

  const handleCopyToClipboard = React.useCallback((e, text) => {
    e.preventDefault();
    e.stopPropagation();

    navigator.clipboard.writeText(text);

    // Mostrar el mensaje de copiado
    setAlertMessage("Copiado al portapapeles");

    // Limpiar mensaje después de 5 segundos
    setTimeout(() => setAlertMessage(""), 4000);
  }, []);

  const returnIconCopy = (onClick) => {
    return (
      <IconButton onClick={onClick} sx={{ padding: "0" }}>
        <ContentCopyIcon fontSize="inherit" sx={{ fontSize: "14px" }} />
      </IconButton>
    );
  };

  const whatColor = React.useCallback((id) => {
    let color = "";
    color = orderStatuses.find(
      (statusObj) => statusObj.orderStatusId === id
    )?.orderStatusFlag;
    if (color === "Red") {
      return "error";
    } else {
      return "success";
    }


  }, [orderStatuses]);



  const isErrorStatus = whatColor(pedido.orderStatusId) === "error";

  /**
   * Maneja el click en la fila.
   * Si es un estado de error, abre la URL del pedido en una nueva pestaña.
   * Si no, muestra los detalles del pedido.
   * 
   * @param {React.MouseEvent} e - Evento del click
   */
  const handleClickTableRow = React.useCallback((e) => {
    if (isErrorStatus) {
      window.open(pedido.orderURL, "_blank", "noopener,noreferrer");
      return;
    }
    handleShowDetails(e, pedido);
  }, [isErrorStatus, pedido, handleShowDetails]);


  return (
    <React.Fragment>
      <StyledTableRow
        onClick={handleClickTableRow}
        key={id}
        style={{
          backgroundColor: isErrorStatus
            ? getErrorStyles(theme).backgroundColor
            : null,
          position: "relative",
        }}
        sx={isErrorStatus ? getErrorStyles(theme) : {}}
      >
        <StyledTableCell
          key={id}
          sx={{
            padding: "0 !important",
            display: isSmallScreen || isSxScreen ? "none" : "table-cell",
          }}
        >
          {/* <StyledTableCell
            sx={isDownLargeScreen ? { padding: " 0 !important", width: "24px" } : {}}
          > */}
          <RadioComponent checked={checked} handleChangeChecked={handleChangeChecked} id={id} />

          {/* </StyledTableCell> */}
        </StyledTableCell>
        {/* muestro el logo en caso de que este en una pantalla mas chica */}


        {isDownLargeScreen && isAdmin() && detailOperationScreen && (
          <StyledTableRow
            sx={{
              justifyContent: "space-evenly",
              display: "flex",
              flexDirection: "column",
              padding: "0px 0 5px 5px",
              position: "absolute",
              bottom: "0",
              backgroundColor: "transparent !important",
            }}
          >

            <Box
              sx={{
                justifyContent: "space-evenly",
                display: "flex",
                flexWrap: "wrap",
                gap: "5px",
                width: "100%",
              }}
            >
              <DynamicChipWithTooltip
                type="oter"
                value={`Total: ${pedido.paidAmount !== null ? pedido.paidAmount : "No disponible"}`}
                tooltipTitle={`Total: ${pedido.paidAmount !== null ? pedido.paidAmount : "No disponible"}`}
                icon={<RequestQuoteIcon />}
              />
              <Chip
                variant="outlined"
                size="small"
                label={`Total: $${pedido.paidAmount !== null ? pedido.paidAmount : "No disponible"}`}
              />
              <Chip
                variant="outlined"
                size="small"
                label={`Comisión: $${pedido.fee !== null ? pedido.fee : "No disponible"}`}
              />
              <Chip
                variant="outlined"
                size="small"
                label={`Envío: $${pedido.shipping !== null ? pedido.shipping : "No disponible"}`}
              />
              <Chip
                variant="outlined"
                size="small"
                label={`Recibido: $${pedido.receivedAmount !== null ? pedido.receivedAmount : "No disponible"}`}
              />
            </Box>
          </StyledTableRow>
        )}



        {!isDownLargeScreen ? (
          <StyledTableCell scope="row">
            <Avatar
              style={{ height: "100px", width: "100px" }}
              src={marketplaceLogo}
              alt="Logo"
            />
          </StyledTableCell>
        ) : null}

        {/* fecha canal y nombre  se ocultara cuando este en pantalla de tablet*/}
        {isMediumScreen ? null : (
          <StyledTableRow
            style={{
              // backgroundColor: "transparent",
              // display: "flex",
              // flexDirection: "column",
            }}
            sx={{
              padding: "0px 1em !important",
              backgroundColor: "transparent !important",
              display: "flex !important",
              flexDirection: "column !important",
              margin: "auto",
            }}
          >

            <OutlinedCard
              fecha={fecha}
              fulfillmentChannel={fulfillmentChannel}
              cliente={pedido.client}
              setVisibleMessage={setVisibleMessage}
              pendingToResponse={pedido.pendingToResponse}
            />

            {/* EN CASO DE QUE SE ACHIQUE LA PANTALLA PASARA ESTO ->
            CONVINO EL PEDIDO CON EL DETALLE DE LA OPERACION
            solo si es admin
            */}


          </StyledTableRow>
        )}
        {/* mostrando costos de operacion en la tabla normal */}
        {detailOperationScreen || isMediumScreen ? null : isAdmin() ? (
          <StyledTableCell
            sx={{
            }}
          >
            {/* <a target="_blank" href={pedido.orderURL}>
              {pedido.macketplaceOrderId}
            </a> */}
            <Box sx={{
              justifyContent: "space-evenly",
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}>
              <FormatMoneyDetails type="Total" amount={pedido.paidAmount} />
              <FormatMoneyDetails type="Comisión" amount={pedido.fee} />
              <FormatMoneyDetails type="Envío" amount={pedido.shipping} />
              <FormatMoneyDetails type="Recibido" amount={pedido.receivedAmount} />
            </Box>
          </StyledTableCell>
        ) : null}

        {pedido.products?.length > 0 ? (
          <React.Fragment>
            {/* si es en celular quitamos la imagen */}
            {isSxScreen ? null : (
              <StyledTableCell sx={{
                position: "relative", padding: {
                  sm: "5px 0 5px 10px !important",
                },
              }}>
                {pedido.products.length > 1 ? (
                  <PlusOneIcon />
                ) : (
                  <>
                    {/* mi avaatr es mi market pero esta se pondra cuando haya vista responsiva */}
                    {isSxScreen ? null : isDownLargeScreen ? (
                      <Avatar
                        style={{
                          height: "50px",
                          width: "50px",
                          position: "absolute",
                          top: "0",
                          left: "0",
                        }}
                        src={marketplaceLogo}
                        alt="Logo"
                      />
                    ) : null}
                    {isSxScreen ? null : (
                      <img
                        src={pedido.products[0].photo}
                        style={
                          isSmallScreen
                            ? { height: "80px", width: "80px" }
                            : { height: "100px", width: "100px" }
                        }
                        alt=""
                      />
                    )}
                  </>
                )}
              </StyledTableCell>
            )}
            <StyledTableCell
              align="left"
              sx={{
                ...(isSxScreen
                  ? { padding: "0" }
                  : {
                    padding: {
                      sm: "5px 0 5px 10px !important",
                    },
                  }),
              }}

            >
              <Box
                sx={{
                  position: "relative",
                  display: "flex",
                }}
              >

                {/* mi avaatr es mi market pero esta se pondra cuando haya vista responsiva */}
                {isSxScreen ? (
                  <>


                    <Avatar
                      style={{
                        // height: "50px",
                        // width: "50px",
                        position: "absolute",
                        top: "0",
                        left: "0",
                      }}
                      src={marketplaceLogo}
                      alt="Logo"
                    />

                    <img
                      src={pedido.products[0].photo}
                      style={{
                        height: "100px", width: "100px",

                        padding: "10px",
                      }}
                      alt=""
                    />
                  </>
                ) : null}
                {isSxScreen ? (
                  <RepeatChips
                    pedido={pedido}
                    checked={checked}
                    handleChangeChecked={handleChangeChecked}
                    id={id}
                    label={label}
                    fecha={fecha}
                    tooltipTitle={tooltipTitle}
                    isAdmin={isAdmin}
                    isMediumScreen={isMediumScreen}
                    isDownLargeScreen={isDownLargeScreen}
                    isSmallScreen={isSmallScreen}
                  />
                )

                  : null}

              </Box>
              <Box sx={{
                display: "flex",
                flexDirection: "column",
                gap: "5px",
              }}>
                <Typography variant="body2" sx={{ width: "95%" }}>
                  {/* <Typography
                    sx={{ fontSize: "17px", display: "flex" }}
                    color="primary"
                  > */}
                  <Grid container alignItems="center">
                    {isMediumScreen || isSxScreen ? (
                      <>
                        <Grid
                          item
                          sx={{
                            maxHeight: "35px",
                            maxWidth: "33%",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            textAlign: "center",
                            padding: "5px",
                          }}
                        >


                          <Tooltip
                            title={"SKU: " + pedido?.products[0]?.publicationProduct?.skuMarketplaceVariation ? pedido?.products[0]?.publicationProduct?.skuMarketplaceVariation : "No disponible"}
                            enterTouchDelay={10}
                            enterDelay={300}
                          >
                            <span
                              style={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                display: "inline-block",
                                maxWidth: "100%",
                              }}
                            >
                              {pedido?.products[0]?.publicationProduct?.skuMarketplaceVariation ? pedido?.products[0]?.publicationProduct?.skuMarketplaceVariation : ""}
                            </span>
                          </Tooltip>
                        </Grid>
                        <Grid
                          item
                          sx={{
                            maxHeight: "35px",
                            maxWidth: "33%",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            textAlign: "center",
                            borderRight: "1px solid",
                            borderLeft: "1px solid",
                            borderColor: "primary.main",
                            padding: "5px",
                          }}
                        >
                          <Tooltip
                            title={"Marca: " + pedido.products[0].brand}
                            enterTouchDelay={10}
                            enterDelay={300}
                          >
                            <span
                              style={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                display: "inline-block",
                                maxWidth: "100%",
                              }}
                            >
                              {pedido.products[0].brand}
                            </span>
                          </Tooltip>
                        </Grid>
                        <Grid
                          item
                          sx={{
                            maxHeight: "35px",
                            maxWidth: "35%",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            textAlign: "center",
                            padding: "5px",
                          }}
                        >
                          <Tooltip
                            title={
                              "Modelo: " + pedido.products[0].model.toString()
                            }
                            enterTouchDelay={10}
                            enterDelay={300}
                          >
                            <span
                              style={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                display: "inline-block",
                                maxWidth: "100%",
                              }}
                            >
                              {pedido.products[0].model.toString()}
                            </span>
                          </Tooltip>
                        </Grid>
                      </>
                    ) : (
                      <>
                        <Grid
                          item
                          sx={{
                            width: "100%",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            textAlign: "left",
                          }}

                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >

                          <Tooltip
                            title={"SKU: " + pedido.products[0].sku}
                            enterTouchDelay={10}
                            enterDelay={300}
                          >
                            <span
                              style={{
                                marginTop: "-5px",
                                marginBottom: "-5px",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                display: "inline-block",
                                maxWidth: "100%",
                              }}
                            >
                              {pedido.products[0].sku}
                            </span>
                          </Tooltip>

                          <Tooltip
                            title={"Orden: #" + pedido.marketplaceOrderId}
                            enterTouchDelay={10}
                            enterDelay={300}
                          >
                            <span
                              style={{
                                marginTop: "-5px",
                                marginBottom: "-5px",
                                display: "inline-block",
                                maxWidth: "100%",
                                fontSize: "16px",
                                color: theme.palette.text.primary,
                              }}
                            >

                              <Link
                                sx={{ color: theme.palette.text.primary }}
                                target="_blank"
                                rel="noopener"
                                href={pedido.orderURL}>
                                {"#" + pedido.marketplaceOrderId}

                              </Link>

                            </span>
                          </Tooltip>
                        </Grid>
                      </>
                    )}
                  </Grid>
                </Typography>
                {isMediumScreen || isSxScreen ? (
                  <>
                    {/* //para pantallas pequenas */}
                    <Tooltip
                      title={"Orden: #" + pedido.marketplaceOrderId}
                      enterTouchDelay={10}
                      enterDelay={300}
                    >
                      <span
                        style={{
                          marginTop: "0px",
                          marginBottom: "-5px",
                          display: "inline-block",
                          maxWidth: "100%",
                          fontSize: "17px",
                        }}
                      >
                        {"#" + pedido.marketplaceOrderId}
                      </span>
                    </Tooltip>
                  </>
                ) : null}

                <Tooltip title={pedido.products[0].title}
                  enterTouchDelay={10}
                  enterDelay={300}
                >
                  <Typography variant="body2" sx={{ fontSize: "12px", color: theme.palette.text.primary, fontWeight: "400", padding: "10px 0" }}
                    onClick={(e) => { handleCopyToClipboard(e, pedido.products[0].title) }}
                  >
                    {returnIconCopy(() => handleCopyToClipboard(e, pedido.products[0].title))} {" "}
                    <ItalicText>{pedido.products[0].title.substring(0, 20) + "..."}</ItalicText>

                  </Typography>
                </Tooltip>

                {/* se mostarra el modelo y marca en un chip en patalla media 
                  y chica */}
                {isMediumScreen || isSxScreen ? null : (
                  <>
                    <Typography sx={{
                      fontWeight: "400",
                      lineHeight: "20px",
                      fontSize: "13px",
                      color: theme.palette.text.primary,
                      display: "flex",
                      // flexDirection: "column",
                      gap: "5px",
                      minWidth: "167px"
                    }} onClick={(e) => { handleCopyToClipboard(e, pedido.products[0].brand) }} >
                      {returnIconCopy(() => handleCopyToClipboard(e, pedido.products[0].brand))} {" "}
                      Marca: {pedido.products[0].brand}
                    </Typography>

                    <Typography sx={{
                      fontWeight: "400",
                      lineHeight: "20px",
                      fontSize: "13px",
                      color: theme.palette.text.primary,
                      display: "flex",
                      // flexDirection: "column",
                      gap: "5px",
                      minWidth: "167px"
                    }}
                      onClick={(e) => { handleCopyToClipboard(e, pedido.products[0].model.toString()) }}
                    >
                      {returnIconCopy(() => handleCopyToClipboard(e, pedido.products[0].model.toString()))} {" "}
                      Modelo: {pedido.products[0].model.toString()}

                    </Typography>
                  </>
                )}

                {/* //el icono raro */}
                {/* {pedido?.products[0]?.variations?.length === 0 ? null : (
                  <EditAttributesIcon fontSize="large" />
                )} */}
                {isMediumScreen || isSxScreen ? null : (
                  <>
                    <Typography sx={{
                      fontWeight: "400",
                      lineHeight: "20px",
                      fontSize: "13px",
                      color: theme.palette.text.primary,
                      // marginBottom: "80px",

                    }}
                    >
                      Cantidad: {pedido.products[0].units}
                    </Typography>
                  </>
                )}
                {/* </Typography> */}
              </Box>

              <Box
                sx={{
                  position: "relative",
                  display: "flex",
                  flexDirection: "column",
                }}
              >

                {isMediumScreen && !isSxScreen ? (
                  <StyledTableCell
                    sx={{
                      padding: ".4em !important",
                    }}
                  >
                    <>
                      {/* EN CASO DE QUE SE ACHIQUE LA PANTALLA PASARA ESTO 
                CONVINO EL PEDIDO CON EL DETALLE DE LA OPERACION */}
                      <RepeatChips
                        pedido={pedido}
                        checked={checked}
                        handleChangeChecked={handleChangeChecked}
                        id={id}
                        label={label}
                        fecha={fecha}
                        tooltipTitle={tooltipTitle}
                        isAdmin={isAdmin}
                        isMediumScreen={isMediumScreen}
                        isDownLargeScreen={isDownLargeScreen}
                        isSmallScreen={isSmallScreen}
                      />
                    </>
                  </StyledTableCell>
                ) : null}
                {isSmallScreen ? (
                  <StyledTableCell
                    sx={{
                      padding: "0 !important",
                      width: "100% !important",
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      gap: "10px",
                      flexWrap: "wrap",
                    }}
                  >
                    <Box sx={{ display: "flex", flexDirection: "column", width: "60%" }}>

                      <AutoGridNoWrap
                        smallScreen={isSxScreen}
                        alerta={alerta}
                        status={status}
                        id={id}
                        pedido={pedido}
                        lastComment={lastComment}
                        comments={comments}
                        currentOrderInternalStatusId={
                          currentOrderInternalStatusId
                        }
                        setCurrentOrderInternalStatusId={
                          setCurrentOrderInternalStatusId
                        }
                        namesStatusInternoV2={namesStatusInternoV2}
                      />
                    </Box>


                    <OptionsBtns pedido={pedido} addNewCommentFunction={addNewCommentFunction}
                      updateComment={updateComment} deleteComment={deleteComment} comments={comments}
                      anchorEl={anchorEl} handleClick={handleClick} handleClose={handleClose} openAnchor={openAnchor}
                      setOpen={setOpen} listbotons={listbotons} whatColor={whatColor} hasNonEmptyStores={hasNonEmptyStores}
                      isSxScreen={isSxScreen} isSmallScreen={isSmallScreen} getOrderCommentsWithDeletedFunction={getOrderCommentsWithDeletedFunction}
                    />
                  </StyledTableCell>
                ) : null}
              </Box>
            </StyledTableCell>
          </React.Fragment>
        ) : (
          <React.Fragment>
            <StyledTableCell align="center">"No productos"</StyledTableCell>
            <StyledTableCell align="left">
              <Typography variant="body2">
                SKU: "No Productos"
                <br />
                Producto: "No Productos"
                <br />
                Marca: "No Productos"
                <br />
                Modelo: "No Productos"
                <br />
                Unidades: "No Productos"
              </Typography>
            </StyledTableCell>
          </React.Fragment>
        )
        }

        {
          isSmallScreen ? null : (
            <StyledTableCell
              sx={{
                // display: "flex",
                // flexDirection: "column",
                // gap: "10px",
                padding: {
                  md: "10px 10px 35px 10px !important",
                  lg: "15px !important",
                }
              }}
            >
              <AutoGridNoWrap
                smallScreen={isSxScreen}
                alerta={alerta}
                status={status}
                id={id}
                pedido={pedido}
                lastComment={lastComment}
                comments={comments}
                currentOrderInternalStatusId={currentOrderInternalStatusId}
                setCurrentOrderInternalStatusId={setCurrentOrderInternalStatusId}
                namesStatusInternoV2={namesStatusInternoV2}
              />
              <OptionsBtns pedido={pedido} addNewCommentFunction={addNewCommentFunction}
                updateComment={updateComment} deleteComment={deleteComment} comments={comments}
                anchorEl={anchorEl} handleClick={handleClick} handleClose={handleClose} openAnchor={openAnchor}
                setOpen={setOpen} listbotons={listbotons} whatColor={whatColor} hasNonEmptyStores={hasNonEmptyStores} 
                getOrderCommentsWithDeletedFunction={getOrderCommentsWithDeletedFunction}
              />

            </StyledTableCell>
          )
        }
      </StyledTableRow >

      {alertMessage && <AlertComponent color="success" message={alertMessage} time={4000} />}
   
      <CustomDialog
        open={openGuide}
        onClose={handleCloseGuide}
        title="Guía"
        maxWidth="80%"
        maxHeight="80%"
        width="80%"
      >
        {/* Aquí debes cargar el PDF desde la URL */}
        {/* Puedes usar un iframe para visualizar el PDF o una librería como react-pdf */}
        {/* Por ejemplo, con un iframe */}
        {guiaEspera ? (
          <Skeleton variant="rectangular" width="80%" height={400} sx={{ borderRadius: "12px", margin: "1em auto" }} />
        ) : guiaContenido ? (
          esURLPDF(guiaContenido) ? (
            <Typography>No se obtuvo una url</Typography>
          ) : (
            <Box sx={{
              maxHeight: '80vh', width: '100%', padding: '20px',
              marginBottom: '20px',
            }}>
              {guiaType && guiaType.includes("image") ? (
                <img src={guiaContenido} alt="Guía" style={{ width: '100%', height: 'auto' }} />
              ) : (
                <iframe
                  src={guiaContenido}
                  title="PDF Viewer"
                  style={{
                    width: '100%',
                    height: '100%',   // ocupa todo el contenedor
                    border: 'none',
                  }}
                />
              )}
            </Box>
          )
        ) : (
          <Typography>{guiaError}</Typography>
        )}
      </CustomDialog>

      <CustomDialog
        open={open}
        onClose={handleCloseDesglose}
        title="Desglose de venta"
        maxHeight="70vh"
        maxWidth="80%"
        width="100%"
      // actions={
      //   <NewCommentComponent
      //     commentType={commentType}
      //     addNewCommentFunction={addNewCommentFunction}
      //     setIsAddingComment={setIsAddingComment}
      //     isResponsive={isResponsive}
      //     user={user}
      //     isErrorComment={isErrorComment}
      //     newComment={newComment}
      //     handleSetNewComment={handleSetNewComment}
      //     handleSendComment={handleSendComment}
      //     loading={loading}
      //   />
      // }
      >
        <BreakdownSale pedido={pedido} disabled={whatColor(pedido.orderStatusId) === "error" ? true : false} comments={comments} setComments={setComments} updateComment={updateComment} deleteComment={deleteComment} />
      </CustomDialog>



      <CustomDialog
        open={VisibleMessage}
        onClose={handleCloseMessages}
        title="Mensajes"
        maxWidth="600px"
        width="100%"
        maxHeight="80%"
        dialogStyles={{ borderRadius: '24px' }}

        actions={
          !showDefaultMessages && (
            <SendMessages
              pedido={pedido}
              defaultMessage={handleShowDefaultMessages}
            />
          )
        }
      >
        <Box>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              gap: "10px",
              margin: "0px 0 20px 0",
            }}
          >
            {showDefaultMessages ? null : (
              <>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  <Avatar
                    sx={{
                      width: "50px",
                      height: "50px",
                      backgroundColor: Green,
                    }}
                    src={pedido.client.photo}
                  >
                    {pedido.client?.name
                      ? pedido.client.name[0]
                      : randomName[0]}
                  </Avatar>
                  <Box>
                    <Typography>
                      {pedido.client?.name ? pedido.client.name : randomName}
                    </Typography>
                    <Typography>
                      {pedido.client?.email ? pedido.client.email : "Sin email"}
                    </Typography>
                  </Box>
                </Box>

                <Box>
                  <IconButton
                    size="small"
                    onClick={() => {
                      dispatch(markAsRead(id, pedido.pendingToResponse, csrf_access_token))
                    }}
                    sx={{
                      textAlign: "left",
                      width: "100%",
                      display: "flex",
                    }}
                  >
                    {pedido.pendingToResponse === true ? (
                      <>
                        <Visibility />
                        <Typography variant="body2" color="text.secondary">
                          Marcar como leído
                        </Typography>
                      </>
                    ) : (
                      <>
                        <VisibilityOff />
                        <Typography variant="body2" color="text.secondary">
                          Marcar como no leído
                        </Typography>
                      </>
                    )}
                  </IconButton>
                </Box>
              </>
            )}
          </Box>

          {showDefaultMessages ? (
            <DefaultMessages pedido={pedido} defaultMessage={handleShowDefaultMessages} notEdit={false} forceShowCreationFields={true} />
          ) : (
            <MessagesList id={id} />
          )}
        </Box>
      </CustomDialog>
    </React.Fragment >
  );
  // }
};

const TablaDesplegable = (props) => {
  const marketplaces = props.marketplaces;
  const marketplaceLogos = props.marketplaceLogos;
  const pedidos = props.pedidos;
  const role = useSelector((store) => store.usuario.role);
  const theme = useTheme();
  const namesStatus = useSelector((store) => {
    return store.pedidos.orderStatus;
  })
  const namesStatusInternoV2 = useSelector(
    (store) => store.pedidos.orderInternalStatus
  );
  // son mis constantes de media query ->->-<-<-<
  const themeBreak = useTheme();
  // console.log("themeBreak", props);

  const isMediumScreen = useMediaQuery(themeBreak.breakpoints.down("md"));

  // abajo de 700
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  // abajo de 1280
  const isDownLargeScreen = useMediaQuery(theme.breakpoints.down("lg"));
  // entre 960 y 1060
  const detailOperationScreen = useMediaQuery(
    theme.breakpoints.between("md", "detailOperationScreenNumber")
  );

  const isSxScreen = useMediaQuery(theme.breakpoints.down("xs"));

  const isAdminn = () => {
    if (role === "Admin" || role === "Administrative_accountant") return true;
    return false;
  };

  const [ordenesSeleccionadas, setOrdenesSeleccionadas] = useState([]);
  const [checked, setChecked] = React.useState(false);

  const handleChangeChecked = (event) => {
    if (ordenesSeleccionadas.length === 0) {
      const ordenesSeleccionadasNuevas = [];
      pedidos.forEach((pedido) => {
        ordenesSeleccionadasNuevas.push(pedido.orderId);
      });
      setOrdenesSeleccionadas(ordenesSeleccionadasNuevas);
      setChecked(true);
    } else if (
      ordenesSeleccionadas.length > 0 &&
      ordenesSeleccionadas.length < pedidos.length
    ) {
      const ordenesSeleccionadasNuevas = [];
      pedidos.forEach((pedido) => {
        ordenesSeleccionadasNuevas.push(pedido.orderId);
      });
      setOrdenesSeleccionadas(ordenesSeleccionadasNuevas);
      setChecked(true);
    } else {
      setOrdenesSeleccionadas([]);
      setChecked(false);
    }
  };

  const [openSurtir, setOpenSurtir] = useState(false);
  const messageSurtido = useSelector((store) => store.pedidos.messageSurtido);
  const severitySurtido = useSelector((store) => store.pedidos.severitySurtido);
  const surtido = useSelector((store) => store.pedidos.pedidoSurtir);

  const handleCloseSurtir = () => {
    setOpenSurtir(false);
  };



  return (
    <div className="App">

      <Box sx={{ overflow: "auto" }}>
        <Box sx={{
          width: {
            xss: "95%",
            sm: "100%",
            md: "100%",
            lg: "100%",
            xl: "100%",
          },
          margin: "auto",

          display: "table", tableLayout: "fixed"
        }}>
          <TableContainer sx={{
            borderRadius: "25px",
          }}>
            <Table aria-label="collapsible table"
            >
              <TableHead
                sx={{
                  borderBottom: "1px solid",
                }}>

                <StyledTableRow>

                  {isSmallScreen || isSxScreen ? null : (
                    <StyledTableCell
                      sx={{
                        display: {
                          xs: "block",
                          md: "block",


                        }

                      }}
                    >

                      {/* TODO: SE CAMBIARA DE LEUGAR */}
                      {/* <Radio
                      checked={checked}
                      onClick={handleChangeChecked}
                    /> */}
                    </StyledTableCell>
                  )}

                  {/* DEPENDIENDO DE QUE TAMAÑO DE PANTALLA SE MOSTRARAN
                  LAS CELDAS ADECUADAS O SI ES EL CASO SE OCULTARAN CON UN NULL*/}
                  {!isDownLargeScreen ? (
                    <StyledTableCell  >
                      Marketplace
                    </StyledTableCell>
                  ) : null}

                  {isMediumScreen ? null : (
                    <StyledTableCell >
                      Datos del pedido
                    </StyledTableCell>
                  )}
                  {/* precio etc */}
                  {isAdminn() ? (
                    detailOperationScreen || isMediumScreen ? null : (
                      <StyledTableCell >
                        Detalles de la operación
                      </StyledTableCell>
                    )
                  ) : null}
                  {isSxScreen ? null : (
                    <StyledTableCell  >Imagen</StyledTableCell>
                  )}

                  <StyledTableCell  >
                    Detalles del producto
                  </StyledTableCell>
                  {isSmallScreen ? null : (
                    <StyledTableCell  >
                      Estado del pedido
                    </StyledTableCell>
                  )}
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {pedidos?.map((pedido, i) => (
                  <Row
                    key={pedido.orderId}
                    id={pedido.orderId}
                    marketplaceLogos={marketplaceLogos}
                    pedido={pedido}
                    isMediumScreen={isMediumScreen}
                    isSmallScreen={isSmallScreen}
                    isDownLargeScreen={isDownLargeScreen}
                    detailOperationScreen={detailOperationScreen}
                    isSxScreen={isSxScreen}
                    marketplaces={marketplaces}
                    ordenesSeleccionadas={ordenesSeleccionadas}
                    setOrdenesSeleccionadas={setOrdenesSeleccionadas}
                    setOpenSurtir={setOpenSurtir}
                    index={i}
                    namesStatus={namesStatus}
                    namesStatusInternoV2={namesStatusInternoV2}
                  />
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Box>
      <Dialog
        open={openSurtir}
        onClose={handleCloseSurtir}
        maxWidth="xl" // Ajusta el ancho máximo al valor que necesites
        fullWidth={true} // Hace que el diálogo ocupe todo el ancho
      >
        <DialogTitle>Surtido</DialogTitle>
        <DialogContent>
          <SurtidoVenta
            message={messageSurtido}
            severity={severitySurtido}
            origin={"marketplace"}
            surtido={surtido}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TablaDesplegable;
