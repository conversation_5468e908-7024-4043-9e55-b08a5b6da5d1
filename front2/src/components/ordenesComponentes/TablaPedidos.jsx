import React, { useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import Collapse from "@mui/material/Collapse";
import {
  colocarDataActualizacion,
  obtenerImagenesMarketplaces,
  obtenerTotalPedidos,
} from "../../redux/pedidosDucks";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import { useDispatch, useSelector } from "react-redux";
import Pagination from "@mui/material/Pagination";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import CargandoLista from "../CargandoLista";
import ButtonGroup from "@mui/material/ButtonGroup";
import Fab from "@mui/material/Fab";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import Alert from "@mui/material/Alert";
import Backdrop from "@mui/material/Backdrop";
import Fade from "@mui/material/Fade";
import Modal from "@mui/material/Modal";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import ListSubheader from "@mui/material/ListSubheader";
import { useTheme, styled } from "@mui/material/styles";
import OutlinedInput from "@mui/material/OutlinedInput";
import TextField from "@mui/material/TextField";
import Chip from "@mui/material/Chip";
import { DesktopDatePicker } from "@mui/x-date-pickers/DesktopDatePicker";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import {
  getStatusPropertyUsingStatusId2,
  getStatusInternoPropertyUsingStatusId2,
} from "../../Utils/atributtesHandler";
import ToggleButton from "@mui/material/ToggleButton";
import ToggleButtonGroup from "@mui/material/ToggleButtonGroup";
import Snackbar from "@mui/material/Snackbar";
import IconButton from "@mui/material/IconButton";
import VisibilityIcon from "@mui/icons-material/Visibility";
import RefreshIcon from "@mui/icons-material/Refresh";
import ClearAllIcon from "@mui/icons-material/ClearAll";

import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import { useNavigate } from "react-router-dom";
import TablaDesplegableWrapper from "./TablaDesplegableWrapper";

import SearchField from "../SearchField";

import ModalGeneral from "../ModalGeneral";


//breakpoints
import {
  Avatar,
  Link,
  Menu,
  Popover,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableSortLabel,
  Tooltip,
  useMediaQuery,
} from "@mui/material";
import { TableRows } from "@mui/icons-material";
import { ManejarErrores } from "../ManejarErrores";
import { set } from "date-fns";
import { useCookies } from "react-cookie";
import { ContadorTooltip } from "../componentesGenerales/Contador";
import { fi } from "date-fns/locale";
import { DialogLoading } from "./components/DialogLoading";
import LoadingButton from '@mui/lab/LoadingButton';
import { changeOkXlsx, getXLSX } from "../../redux/ordenesConsolidadas";
import { AlertComponent } from "../componentesGenerales/Alert";
import { BtnsXlsx } from "./components/BtnsXlsx";
import { SkeletonTables } from "../componentesGenerales/SkeletonTables";
import { useCustomDateTime } from "../menusYBarrasComponentes/HookDate";
import { LastedUpdate } from "./components/LastedUpdate";
import LocalShippingIcon from "@mui/icons-material/LocalShipping"; // Recolección
import InventoryIcon from "@mui/icons-material/Inventory"; // Empaquetado
import PublicIcon from "@mui/icons-material/Public"; // Externo

//const
const ITEM_HEIGHT = 30;
const ITEM_PADDING_TOP = 4;

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 600,
  bgcolor: "background.paper",
  border: "2px solid #000",
  boxShadow: 24,
  p: 4,
};

const MenuProps = {
  PaperProps: {
    style: {
      // maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP
      maxHeight: 250,
      width: 250,
    },
  },
};

//functions

const getStyles = (status, listaStatus, theme) => {
  return {
    fontWeight:
      listaStatus?.indexOf(status) === -1
        ? theme.typography.fontWeightRegular
        : theme.typography.fontWeightMedium,
  };
};

const TablaPedidos = (props) => {
  /*Hooks*/
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const theme = useTheme();
  const [searchParams] = useSearchParams();
  /*Obtener la fecha actual */
  const today = new Date();
  /*url params */
  const namesStatusInternoV2 = useSelector(
    (store) => store.pedidos.orderInternalStatus
  );
  const namesStatus = useSelector((store) => store.pedidos.orderStatus);
  const dataActualizacion = useSelector(
    (store) => store.pedidos.dataActualizacion
  );
  const [showUpdate, setShowUpdate] = React.useState(false);
  const [namesStatusInterno, setNamesStatusInterno] = React.useState([]);
  const [cookies, setCookie] = useCookies();
  const [anchorEl, setAnchorEl] = React.useState(null);
  const abrir = props.abrir;
  const orderSellerType = [
    {
      orderSellerTypeId: 1,
      orderSellerTypeName: "Marketplace",
    },
    {
      orderSellerTypeId: 2,
      orderSellerTypeName: "Seller",
    },
  ];

  const openCloseType = [
    {
      openCloseTypeId: 1,
      openCloseTypeName: "Abiertos",
    },
    {
      openCloseTypeId: 2,
      openCloseTypeName: "Cerrados",
    },
    // {
    //   openCloseTypeId: 3,
    //   openCloseTypeName: "Recoleccion",
    // },
    // {
    //   openCloseTypeId: 4,
    //   openCloseTypeName: "Empaquetado",
    // },
    // {
    //   openCloseTypeId: 5,
    //   openCloseTypeName: "Externo",
    // },
  ];

  React.useEffect(() => {
    dispatch(colocarDataActualizacion(cookies.csrf_access_token));
  }, []);

  const getMarketplacesList = (marketplaces) => {
    return `(${marketplaces.map(marketplace => marketplace.supportedMarketplace.name).join(", ")})`
  }

  // React.useEffect(() => {
  //   if (dataActualizacion !== null) {
  //     if (abrir === false) {
  //       setShowUpdate(true);
  //       const timer = setTimeout(() => {
  //         setShowUpdate(false);
  //       }, 5000);
  //       return () => {
  //         clearTimeout(timer);
  //       };
  //     }
  //   }
  // }, [dataActualizacion]);

  const handleUpdateClick = (event) => {
    dispatch(colocarDataActualizacion(cookies.csrf_access_token));
    setAnchorEl(event.currentTarget);
  };

  const handleClosePopover = () => {
    setShowUpdate(false);
    setAnchorEl(null);
  };

  const oneMonthAgo = new Date(
    today.getFullYear(),
    today.getMonth() - 1,
    today.getDate()
  );
  React.useEffect(() => {
    const orderInternalStatusTypesMap = {};
    namesStatusInternoV2?.forEach((status) => {
      orderInternalStatusTypesMap[status.orderInternalStatusTypeId] =
        status.orderInternalStatusType;
    });

    // Crear el arreglo final
    const namesStatusInternoFinal = [];
    let currentType = "";
    namesStatusInternoV2?.forEach((status) => {
      if (status.orderInternalStatusType !== currentType) {
        currentType = status.orderInternalStatusType;
        namesStatusInternoFinal.push({
          type: "listSubheader",
          subHeaderName: currentType,
        });
      }
      namesStatusInternoFinal.push({
        type: "menuItem",
        orderInternalStatus: status.orderInternalStatus,
        orderInternalStatusId: status.orderInternalStatusId,
        orderInternalStatusType: status.orderInternalStatusType,
        orderInternalStatusTypeId: status.orderInternalStatusTypeId,
      });
    });
    setNamesStatusInterno(namesStatusInternoFinal);
  }, [namesStatusInternoV2]);

  /**Nombres globales para los parametros de los filtros */
  const proveedor =
    searchParams.get("proveedores") === null
      ? "1-2-3-4"
      : searchParams.get("proveedores");
  const status =
    searchParams.get("orderStatusId") === null
      ? namesStatus?.map((status) => status.orderStatusId)?.join("-")
      : searchParams.get("orderStatusId");
  const statusInterno =
    searchParams.get("orderStatusInternoId") === null
      ? namesStatusInternoV2
        ?.map((statusInterno) => statusInterno.orderInternalStatusId)
        ?.join("-")
      : searchParams.get("orderStatusInternoId");
  const initialDate =
    searchParams.get("fechaInicial") === null
      ? oneMonthAgo.toISOString().split("T")[0]
      : searchParams.get("fechaInicial");
  const finalDate =
    searchParams.get("fechaFinal") === null
      ? today.toISOString().split("T")[0]
      : searchParams.get("fechaFinal");
  const openCloseToggle =
    searchParams.get("abiertosCerrados") === null
      ? "Abiertos"
      : searchParams.get("abiertosCerrados");
  const sellerMarketplaceToggle =
    searchParams.get("sellerMarketplace") === null
      ? "2"
      : searchParams.get("sellerMarketplace");
  const page = searchParams.get("page") === null ? 1 : searchParams.get("page");
  const searchText =
    searchParams.get("search") === null ? "" : searchParams.get("search");

  /*  Use selector -> variables desde redux */
  const logosEspera = useSelector((store) => store.pedidos.logosEspera);
  const cantidadDePedidosPorPagina = useSelector(
    (store) => store.pedidos.nextC
  );
  const cantidadDePaginas = useSelector(
    (store) => store.pedidos.cantidadDePaginas
  );

  // total de resultados por pagina
  const totalProductosPedidos = useSelector(
    (store) => store.pedidos.totalProductosPedidos
  );

  // trayendo el todal de ordenes
  const totalOrders = useSelector((store) => store.pedidos.totalOrders);
  const marketplaces = useSelector((store) => store.pedidos.marketplaces);


  // Estado para almacenar los logos de los marketplaces
  const marketplaceLogos = useSelector(
    (store) => store.pedidos.marketplaceLogos
  );
  const [banderaLogos, setBanderaLogos] = React.useState(false);
  const [buttons, setButtons] = React.useState([]);

  const [marketPlaceButtonDisableAmazon, setMarketPlaceButtonDisableAmazon] =
    React.useState(false);
  const [
    marketPlaceButtonDisableClaroShop,
    setMarketPlaceButtonDisableClaroShop,
  ] = React.useState(false);
  const [marketPlaceButtonDisableML, setMarketPlaceButtonDisableML] =
    React.useState(false);
  const [marketPlaceButtonDisableWalmart, setMarketPlaceButtonDisableWalmart] =
    React.useState(false);

  const [existeAmazon, setExisteAmazon] = React.useState(false);
  const [existeClaroShop, setExisteClaroShop] = React.useState(false);
  const [existeMercadoLibre, setExisteMercadoLibre] = React.useState(false);
  const [existeWalmart, setExisteWalmart] = React.useState(false);
  const [amazonId, setAmazonId] = React.useState("");
  const [claroShopId, setClaroShopId] = React.useState("");
  const [mercadoLibreId, setMercadoLibreId] = React.useState("");
  const [walmartId, setWalmartId] = React.useState("");


  // Obtener los logos de los marketplaces
  React.useEffect(() => {
    if (marketplaces?.length > 0) {
      if (banderaLogos === false) {
        dispatch(obtenerImagenesMarketplaces(marketplaces));
        setBanderaLogos(true);
      }
      setButtons(
        marketplaces?.map((marketplace) => ({
          id: marketplace.id,
          name: marketplace.supportedMarketplace.name,
        }))
      );

      const estaMercadoLibre = marketplaces.find(
        (marketplace) =>
          marketplace.supportedMarketplace.name.toLowerCase() ===
          "mercado libre"
      );
      const estaClaroShop = marketplaces.find(
        (marketplace) =>
          marketplace.supportedMarketplace.name.toLowerCase() ===
          "claro shop"
      );
      const estaWalmart = marketplaces.find(
        (marketplace) =>
          marketplace.supportedMarketplace.name.toLowerCase() ===
          "walmart"
      );
      const estaAmazon = marketplaces.find(
        (marketplace) =>
          marketplace.supportedMarketplace.name.toLowerCase() ===
          "amazon"
      );
      if (estaMercadoLibre) {
        const supportedMarketplaceId =
          estaMercadoLibre.supportedMarketplace.id;
        setMarketPlaceButtonDisableML(
          proveedor.includes(supportedMarketplaceId.toString())
        );
        setExisteMercadoLibre(true);
        setMercadoLibreId(supportedMarketplaceId);
      }
      if (estaClaroShop) {
        const supportedMarketplaceId =
          estaClaroShop.supportedMarketplace.id;
        setMarketPlaceButtonDisableClaroShop(
          proveedor.includes(supportedMarketplaceId.toString())
        );
        setExisteClaroShop(true);
        setClaroShopId(supportedMarketplaceId);
      }
      if (estaWalmart) {
        const supportedMarketplaceId =
          estaWalmart.supportedMarketplace.id;
        setMarketPlaceButtonDisableWalmart(
          proveedor.includes(supportedMarketplaceId.toString())
        );
        setExisteWalmart(true);
        setWalmartId(supportedMarketplaceId);
      }
      if (estaAmazon) {
        const supportedMarketplaceId =
          estaAmazon.supportedMarketplace.id;
        setMarketPlaceButtonDisableAmazon(
          proveedor.includes(supportedMarketplaceId.toString())
        );
        setExisteAmazon(true);
        setAmazonId(supportedMarketplaceId);
      }
    }
  }, [marketplaces]);

  const handleButtonClick = (button) => {
    const lowerCaseName = button.name.toLowerCase();
    switch (lowerCaseName) {
      case "walmart":
        return {
          variant: marketPlaceButtonDisableWalmart ? "contained" : "outline",
          onClick: () => handleMarketPlaceButtonDisableWalmart(),
        };
      case "mercado libre":
        return {
          variant: marketPlaceButtonDisableML ? "contained" : "outline",
          onClick: () => handleMarketPlaceButtonDisableML(),
        };
      case "claro shop":
        return {
          variant: marketPlaceButtonDisableClaroShop ? "contained" : "outline",
          onClick: () => handleMarketPlaceButtonDisableClaroShop(),
        };
      case "amazon":
        return {
          variant: marketPlaceButtonDisableAmazon ? "contained" : "outline",
          onClick: () => handleMarketPlaceButtonDisableAmazon(),
        };
      default:
        return {
          variant: "outline",
          onClick: () => { },
        };
    }
  };

  /*Mensajes de alerta */
  const mensajeAlertaStatus =
    "Debes seleccionar al menos un status para buscar";
  const mensajeAlertaStatusInterno =
    "Debes seleccionar al menos un status interno para buscar";
  /* variables useState */
  /*Para filtros*/

  const fecha = new Date();

  const añoActual = fecha.getFullYear();
  const hoy = fecha.getDate();
  const mesActual = fecha.getMonth();

  const mesFormateado = mesActual < 10 ? `0${mesActual}` : mesActual;
  const diaFormateado = hoy < 10 ? `0${hoy}` : hoy;

  // Combinar en el formato deseado
  const fechaFormateada = `${añoActual}-${mesFormateado}-${diaFormateado}`;

  // Inicialización de los estados con React.useState
  const [initialDatePicker, setInitialDatePicker] = React.useState(
    searchParams.get("fechaInicial") === null
      ? oneMonthAgo // Fecha de hace un mes
      : fechaFormateada
  );

  // const year = searchParams.get("fechaFinal").split("-")[0];
  // const month = parseInt(searchParams.get("fechaFinal").split("-")[1]) - 1; // Los meses en JavaScript son base 0
  // const day = searchParams.get("fechaFinal").split("-")[2];
  // let fechaInicialString = searchParams.get("fechaInicial");
  // let partesFecha = fechaInicialString.split("-");
  // let fechaInicial = new Date(
  //   partesFecha[0],
  //   parseInt(partesFecha[1]) - 1, // Los meses en JavaScript son de 0 a 11
  //   partesFecha[2]
  // );

  // // Ajustar a la zona horaria de Ciudad de México
  // let fechaMexico = fechaInicial.toLocaleDateString("es-MX", {
  //   timeZone: "America/Mexico_City",
  //   year: "numeric",
  //   month: "2-digit",
  //   day: "2-digit",
  // });

  // console.log(
  //   today,
  //   "INITIAL DATE PICKER",
  //   new Date(Date.UTC(year, month, day)),
  //   "DFSDVd",
  //   new Date(
  //     searchParams.get("fechaInicial").split("-")[0],
  //     parseInt(searchParams.get("fechaInicial").split("-")[1]) - 1,
  //     searchParams.get("fechaInicial").split("-")[2]
  //   ),
  //   "DSFF0",
  //   fechaMexico,
  //   fechaFormateada
  // );

  const [finalDatePicker, setFinalDatePicker] = React.useState(
    searchParams.get("fechaFinal") === null
      ? today // Fecha de hoy
      : fechaFormateada
  );

  /**Otros states */
  const [mensajeModal, setMensajeModal] = React.useState("");
  const [showingFilters, setShowingFilters] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  /**Lista de status actuales */

  const [statusInternosFiltro, setStatusInternoFiltro] = React.useState([]);

  React.useEffect(() => {
    setStatusInternoFiltro(
      searchParams.get("orderStatusInternoId") === null
        ? namesStatusInterno
          .filter(
            (statusInternoObj) => statusInternoObj["type"] === "menuItem"
          )
          .map((statusInterno) => statusInterno.orderInternalStatusId)
        : searchParams
          .get("orderStatusInternoId")
          .split("-")
          ?.map((id) => parseInt(id))
    );
  }, [namesStatusInterno]);

  const [statusFiltro, setStatusFiltro] = React.useState([]);

  React.useEffect(() => {
    setStatusFiltro(
      searchParams.get("orderStatusId") === null
        ? namesStatus?.map((status) => status.orderStatusId)
        : searchParams
          .get("orderStatusId")
          .split("-")
          ?.map((id) => parseInt(id))
    );
  }, [namesStatus]);

  const offset = cantidadDePedidosPorPagina * (page - 1);
  /*funciones para modificar los states*/
  const handleShowingFilters = () => {
    setShowingFilters(!showingFilters);
  };
  const handleMarketPlaceButtonDisableAmazon = () => {
    setMarketPlaceButtonDisableAmazon(!marketPlaceButtonDisableAmazon);
  };
  const handleMarketPlaceButtonDisableClaroShop = () => {
    setMarketPlaceButtonDisableClaroShop(!marketPlaceButtonDisableClaroShop);
  };
  const handleMarketPlaceButtonDisableML = () => {
    setMarketPlaceButtonDisableML(!marketPlaceButtonDisableML);
  };
  const handleMarketPlaceButtonDisableWalmart = () => {
    setMarketPlaceButtonDisableWalmart(!marketPlaceButtonDisableWalmart);
  };
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const manejarCambioDeStatusInternoFiltros = (event) => {
    var nombre = event.target.name;
    var valor = event.target.value;
    setStatusInternoFiltro(
      // On autofill we get a stringified value.
      typeof valor === "string" ? valor.split(",") : valor
    );
  };

  const manejarCambioDeStatusFiltros = (event) => {
    var nombre = event.target.name;
    var valor = event.target.value;
    setStatusFiltro(
      // On autofill we get a stringified value.
      typeof valor === "string" ? valor.split(",") : valor
    );
  };
  //Handle status filter for "select all" and "select none" buttons
  const allStatusFiltros = namesStatus?.map((status) => status.orderStatusId);
  const handleSelectAllStatusFiltros = (event) => {
    setStatusFiltro(
      // On autofill we get a stringified value.
      typeof allStatusFiltros === "string"
        ? allStatusFiltros.split(",")
        : allStatusFiltros
    );
  };
  const noneStatusFiltros = [];
  const handleSelectNoneStatusFiltros = (event) => {
    setStatusFiltro(
      // On autofill we get a stringified value.
      typeof noneStatusFiltros === "string"
        ? noneStatusFiltros.split(",")
        : noneStatusFiltros
    );
  };

  //Handle Status Interno filter for "select all" and "select none" buttons
  const allStatusInternoFiltros = namesStatusInterno
    .filter((statusInternoObj) => statusInternoObj["type"] === "menuItem")
    .map((statusInterno) => statusInterno.orderInternalStatusId);
  const handleSelectAllStatusInternoFiltros = (event) => {
    setStatusInternoFiltro(
      // On autofill we get a stringified value.
      typeof allStatusInternoFiltros === "string"
        ? allStatusInternoFiltros.split(",")
        : allStatusInternoFiltros
    );
  };

  const noneStatusInternoFiltros = [];
  const handleSelectNoneStatusInternoFiltros = (event) => {
    setStatusInternoFiltro(
      // On autofill we get a stringified value.
      typeof noneStatusInternoFiltros === "string"
        ? noneStatusInternoFiltros.split(",")
        : noneStatusInternoFiltros
    );
  };

  const formatDateYYYYMMDD = (input) => {
    // 1. Si ya es string "YYYY-MM-DD", la devuelves sin tocarla
    if (typeof input === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(input)) {
      return input;
    }
  
    // 2. En cualquier otro caso intentas convertir a Date
    const d = new Date(input);
    if (isNaN(d)) throw new Error('Fecha inválida');
  
    // 3. Formateas
    return d.toISOString().slice(0, 10);      // "YYYY-MM-DD"
  };
  /*Accion para aplicar todos los filtros del embudo */
  const aplicarFiltros = (numFilsterFast) => {

    let mensaje = "";
    if (
      !marketPlaceButtonDisableAmazon &&
      !marketPlaceButtonDisableClaroShop &&
      !marketPlaceButtonDisableML &&
      !marketPlaceButtonDisableWalmart
    ) {
      mensaje = mensaje + "*Debes seleccionar al menos un marketplace \n";
    }
    if (statusFiltro.length === 0) {
      mensaje = mensaje + mensajeAlertaStatus + "\n";
    }
    if (statusInternosFiltro.length === 0) {
      mensaje = mensaje + mensajeAlertaStatusInterno + "\n";
    }
    if (initialDatePicker > today) {
      mensaje = mensaje + "La fecha de inicio no es valida\n";
    }
    if (finalDatePicker > today) {
      mensaje = mensaje + "La fecha de final no es valida\n";
    }
    if (finalDatePicker - initialDatePicker < 0) {
      mensaje = mensaje + "El rango de fechas seleccionados no es válido \n";
    }

    if (mensaje !== "") {
      setMensajeModal(mensaje);
      handleOpen();
      return;
    }
    let filtrosMarketPlace = "";
    if (existeAmazon) {
      if (marketPlaceButtonDisableAmazon) {
        if (filtrosMarketPlace !== "") {
          filtrosMarketPlace = filtrosMarketPlace + "-";
        }
        filtrosMarketPlace = filtrosMarketPlace + amazonId.toString();
      }
    }
    if (existeClaroShop) {
      if (marketPlaceButtonDisableClaroShop) {
        if (filtrosMarketPlace !== "") {
          filtrosMarketPlace = filtrosMarketPlace + "-";
        }
        filtrosMarketPlace = filtrosMarketPlace + claroShopId.toString();
      }
    }

    if (existeMercadoLibre) {
      if (marketPlaceButtonDisableML) {
        if (filtrosMarketPlace !== "") {
          filtrosMarketPlace = filtrosMarketPlace + "-";
        }
        filtrosMarketPlace = filtrosMarketPlace + mercadoLibreId.toString();
      }
    }

    if (existeWalmart) {
      if (marketPlaceButtonDisableWalmart) {
        if (filtrosMarketPlace !== "") {
          filtrosMarketPlace = filtrosMarketPlace + "-";
        }
        filtrosMarketPlace = filtrosMarketPlace + walmartId.toString();
      }
    }

    let filtrosStatus = "";
    statusFiltro.forEach((element) => {
      if (filtrosStatus !== "") {
        filtrosStatus = filtrosStatus + "-";
      }
      filtrosStatus = filtrosStatus + element.toString();
    });

    let filtrosStatusInternos = "";
    statusInternosFiltro.forEach((element) => {
      if (filtrosStatusInternos !== "") {
        filtrosStatusInternos = filtrosStatusInternos + "-";
      }
      filtrosStatusInternos = filtrosStatusInternos + element.toString();
    });

    let statusInternosFiltroConditional;

    if (numFilsterFast === 1) {
      // recoleecion
      setStatusInternoFiltro([2, 3, 4]); // Asumiendo que esta es una función que necesitas llamar
      statusInternosFiltroConditional = "2-3-4";
    } else if (numFilsterFast === 2) {
      // empaquetado
      setStatusInternoFiltro([5, 6]);
      statusInternosFiltroConditional = "5-6";
    } else if (numFilsterFast === 3) {
      setStatusInternoFiltro([7, 8]);
      setStatusFiltro([13]);
      statusInternosFiltroConditional = "7-8";
    } else {
      statusInternosFiltroConditional = filtrosStatusInternos;
    }
    console.log('000000000000000000000000000')
    console.log(formatDateYYYYMMDD(finalDatePicker))
    console.log(formatDateYYYYMMDD(finalDatePicker))
    console.log('000000000000000000000000000')
    const dataSend = {
      proveedor: filtrosMarketPlace,
      filtrosStatus: numFilsterFast == 3 ? "13" : filtrosStatus,
      // filtrosStatusInterno: filtrosStatusInternos,
      filtrosStatusInterno: statusInternosFiltroConditional,
      finalDate: formatDateYYYYMMDD(finalDatePicker),
      initialDate: formatDateYYYYMMDD(initialDatePicker),
      abiertosCerrados: numFilsterFast ? "Abiertos" : openCloseToggle,
      sellerMarketplace: sellerMarketplaceToggle,
      search: searchText,
    };
    dispatch(obtenerTotalPedidos(dataSend));
    setShowingFilters(false);

    navigate(
      `?&search=${searchText}&proveedores=${filtrosMarketPlace}&fechaInicial=${dataSend.initialDate
      }&fechaFinal=${dataSend.finalDate}&orderStatusId=${dataSend.filtrosStatus
      }&orderStatusInternoId=${dataSend.filtrosStatusInterno
      }&abiertosCerrados=${dataSend.abiertosCerrados
      }&sellerMarketplace=${sellerMarketplaceToggle}&page=${1}`
    );
  };

  // const handleFastFilter = (event, numberFilter) => {
  //   let filtrosMarketPlace = "";
  //   filtrosMarketPlace =
  //     walmartId.toString() +
  //     "-" +
  //     mercadoLibreId.toString() +
  //     "-" +
  //     claroShopId.toString() +
  //     "-" +
  //     amazonId.toString();
  //   let filtrosStatus = "";
  //   console.log(statusFiltro, ",i,isi,simdmidm", statusInternosFiltro);
  //   let filtrosStatusInternos = "2-3-4";
  //   let fechaInicial = "";
  //   let fechaFinal = "";
  //   let today = new Date();

  //   // Crear una nueva fecha incrementando el mes en uno
  //   let oneMonthBefore = new Date(
  //     today.getFullYear(),
  //     today.getMonth() - 1,
  //     today.getDate()
  //   )
  //     .toISOString()
  //     .split("T")[0];
  //   let dateToday = today.toISOString().split("T")[0];
  //   const openClosed = "Abiertos";
  //   sellerMarketplaceToggle = "2";
  // };

  // const handleCleanFilters = () => {
  //   setStatusFiltro(
  //     namesStatus?.map((status) => status.orderStatusId) || []
  //   );
  //   setStatusInternoFiltro(
  //     namesStatusInterno
  //       .filter((statusInternoObj) => statusInternoObj["type"] === "menuItem")
  //       .map((statusInterno) => statusInterno.orderInternalStatusId) || []
  //   );
  //   setInitialDatePicker(new Date(today.getFullYear(), today.getMonth(), 1));
  //   setFinalDatePicker(today);
  //   setMarketPlaceButtonDisableAmazon(false);
  //   setMarketPlaceButtonDisableClaroShop(false);
  //   setMarketPlaceButtonDisableML(false);
  //   setMarketPlaceButtonDisableWalmart(false);
  // };

  /*Componente toggle  para filtro de pedidos abiertos y cerrados*/
  const OpenCloseToggleButton = () => {
    const manejarCambioOpenClose = (event, newAlignment) => {


      let filtrosStatus = "";
      statusFiltro.forEach((element) => {
        if (filtrosStatus !== "") {
          filtrosStatus = filtrosStatus + "-";
        }
        filtrosStatus = filtrosStatus + element.toString();
      });

      let filtrosStatusInternos = "";
      statusInternosFiltro.forEach((element) => {
        if (filtrosStatusInternos !== "") {
          filtrosStatusInternos = filtrosStatusInternos + "-";
        }
        filtrosStatusInternos = filtrosStatusInternos + element.toString();
      });

      let statusInternosFiltroConditional;

      if (newAlignment === "Recoleccion") {
        // recoleecion
        setStatusInternoFiltro([2, 3, 4]); // Asumiendo que esta es una función que necesitas llamar
        statusInternosFiltroConditional = "2-3-4";
      } else if (newAlignment === "Empaquetado") {
        // empaquetado
        setStatusInternoFiltro([5, 6]);
        statusInternosFiltroConditional = "5-6";
      }
      // else if (newAlignment === "") {
      //   setStatusInternoFiltro([7, 8]);
      //   setStatusFiltro([13]);
      //   statusInternosFiltroConditional = "7-8";
      // }
      else {
        statusInternosFiltroConditional = filtrosStatusInternos;
      }

      const data = {
        proveedor: proveedor,
        filtrosStatus: filtrosStatus,
        filtrosStatusInterno: statusInternosFiltroConditional
          ? statusInternosFiltroConditional
          : filtrosStatusInternos,
        finalDate: finalDate,
        initialDate: initialDate,
        // abiertosCerrados: newAlignment,
        sellerMarketplace: sellerMarketplaceToggle,
        search: searchText,
      };

      dispatch(obtenerTotalPedidos(data));
      navigate(
        `?&search=${searchText}&proveedores=${proveedor}&fechaInicial=${initialDate}&fechaFinal=${finalDate}&orderStatusId=${status}&orderStatusInternoId=${data.filtrosStatusInterno
        }&abiertosCerrados=${newAlignment}&sellerMarketplace=${sellerMarketplaceToggle}&page=${1}`
      );
    };

    return (
      <ToggleButtonGroup
        // color="buttonGreen"
        value={openCloseToggle}
        exclusive
        onChange={manejarCambioOpenClose}
      >
        {openCloseType.map((openClose) => (
          <ToggleButton key={openClose.openCloseTypeId} value={openClose.openCloseTypeName.toString()}
          // color="buttonGreen"
          ><Typography
            sx={{
              fontSize: "clamp(.6rem, .8vw + 0.3rem, .9rem)"
            }}
          >
              {openClose.openCloseTypeName}
            </Typography>
          </ToggleButton>
        ))}
        <ToggleButton value=""

        >
          <Typography
            sx={{
              fontSize: "clamp(0.6rem, 1vw + 0.3rem, 1rem)",
            }}
          >
            Todos
          </Typography>
        </ToggleButton>
      </ToggleButtonGroup>
    );
  };

  /*Componente toggle  para filtro de pedidos seller y marketplace*/
  const SellerMarketplaceToggleButton = () => {
    const manejarCambioSellerMarketplace = (event, newAlignment) => {
      let filtrosStatus = "";
      statusFiltro.forEach((element) => {
        if (filtrosStatus !== "") {
          filtrosStatus = filtrosStatus + "-";
        }
        filtrosStatus = filtrosStatus + element.toString();
      });

      let filtrosStatusInternos = "";
      statusInternosFiltro.forEach((element) => {
        if (filtrosStatusInternos !== "") {
          filtrosStatusInternos = filtrosStatusInternos + "-";
        }
        filtrosStatusInternos = filtrosStatusInternos + element.toString();
      });
      dispatch(
        obtenerTotalPedidos({
          proveedor: proveedor,
          filtrosStatus: filtrosStatus,
          filtrosStatusInterno: filtrosStatusInternos,
          finalDate: finalDate,
          initialDate: initialDate,
          abiertosCerrados: openCloseToggle,
          sellerMarketplace: newAlignment,
          search: searchText,
        })
      );
      navigate(
        `?&search=${searchText}&proveedores=${proveedor}&fechaInicial=${initialDate}&fechaFinal=${finalDate}&orderStatusId=${status}&orderStatusInternoId=${statusInterno}&abiertosCerrados=${openCloseToggle}&sellerMarketplace=${newAlignment}&page=${1}`
      );
    };

    return (
      <ToggleButtonGroup
        // color="buttonGreen"
        value={sellerMarketplaceToggle}
        exclusive
        onChange={manejarCambioSellerMarketplace}
      >
        {orderSellerType.map((sellerType) => (
          <ToggleButton key={sellerType.orderSellerTypeId} value={sellerType.orderSellerTypeId.toString()}
          // color="buttonGreen"
          >
            <Typography
              sx={{
                fontSize: "clamp(.6rem, .8vw + 0.3rem, .9rem)",
              }}
            >
              {sellerType.orderSellerTypeName}
            </Typography>
          </ToggleButton>
        ))}
        <ToggleButton value="">
          <Typography
            sx={{
              fontSize: "clamp(.6rem, .8vw + 0.3rem, .9rem)",
            }}
          >
            Todos
          </Typography>
        </ToggleButton>
      </ToggleButtonGroup>
    );
  };

  /*Componentes con estilo */
  const ItemCenter = styled(Paper)(() => ({
    textAlign: "center",
    boxShadow: "none",
  }));
  const ItemLeft = styled(Paper)(() => ({
    textAlign: "left",
    // boxShadow: "none",
    minWidth: "1px",
    height: "0px",
    backgroundColor: "transparent",
    // marginTop: "1em",
  }));

  //manejo de breakpoints
  const themeBreak = useTheme();
  const isMediumScreen = useMediaQuery(
    themeBreak.breakpoints.between("sm", "md")
  );

  const colorConst = themeBreak.palette.primary.main;

  const isSmallScreen = useMediaQuery(themeBreak.breakpoints.down("sm"));
  /*Componente grid donde se encuentran los filtros */
  const FiltersGrid = () => {
    return (
      <Box sx={{ flexGrow: 1, mb: 1, width: "100%", padding: "0 1rem" }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            margin: "1rem 0",
            flexWrap: "wrap",
            gap: "1rem",
            textAlign: "left",
            // flexDirection: 
            flexWrap: "wrap"
          }}
        >
          <LastedUpdate
            dataActualizacion={dataActualizacion}
            handleUpdateClick={handleUpdateClick}
            showUpdate={showUpdate}
            anchorEl={anchorEl}
            handleClosePopover={handleClosePopover}
            colorConst={colorConst}
            getMarketplacesList={getMarketplacesList}
            isSmallScreen={isSmallScreen}
          />

          {totalOrders > 1 && (
            <ContadorTooltip
              //numero de pedidos por pagina
              totalProductosPedidos={totalProductosPedidos}
              //numero total de pedidos
              totalOrders={totalOrders}
              isOrders={true}
              page={page}
            />
          )}

        </Box>
        <Grid container spacing={2} sx={{ justifyContent: "space-evenly" }}>

          <Grid item xs={{ minWidth: "60px" }}>
            <ItemLeft sx={{
              height: "max-content",
              backgroundImage: "none",
              boxShadow: "none",
            }}>
              <Fab
                size="medium"
                color="buttonGreen"
                aria-label="add"
                style={{
                  marginBottom: "5px",
                  ...(showingFilters
                    ? {}
                    : {
                      position: "inherit",
                    }),
                  zIndex: 0,
                }}
                onClick={() => handleShowingFilters()}
              >
                <FilterAltIcon />
              </Fab>
            </ItemLeft>
          </Grid>
          {/* abiertos cerrados todps */}
          <Grid item alignItems="center" xs={{ minWidth: "100%" }}>
            {/* botones de abiertos cerrados  */}
            <OpenCloseToggleButton />
            {/* </ItemCenter> */}
          </Grid>
          {/* seller  todos */}
          <Grid item xs={{ minWidth: "100%" }}>
            <ItemCenter>
              <SellerMarketplaceToggleButton />
            </ItemCenter>
          </Grid>
          {/* aqui se llama el componente que refleja el numero de ordenes por partida */}
        </Grid>
      </Box>
    );
  };
  const enviarSearch = (event) => {
    event.preventDefault();
    var nuevoSearch = event.target[1].value;
    dispatch(
      obtenerTotalPedidos({
        proveedor: proveedor,
        filtrosStatus: status,
        filtrosStatusInterno: statusInterno,
        finalDate: finalDate,
        initialDate: initialDate,
        abiertosCerrados: openCloseToggle,
        sellerMarketplace: sellerMarketplaceToggle,
        search: nuevoSearch,
      })
    );
    navigate(
      `?&search=${nuevoSearch}&proveedores=${proveedor}&fechaInicial=${initialDate}&fechaFinal=${finalDate}&orderStatusId=${status}&orderStatusInternoId=${statusInterno}&abiertosCerrados=${openCloseToggle}&sellerMarketplace=${sellerMarketplaceToggle}&page=${1}`
    );
  };
  const cambioDePagina = (evento, valor) => {
    dispatch(
      obtenerTotalPedidos({
        proveedor: proveedor,
        filtrosStatus: status,
        filtrosStatusInterno: statusInterno,
        finalDate: finalDate,
        initialDate: initialDate,
        abiertosCerrados: openCloseToggle,
        sellerMarketplace: sellerMarketplaceToggle,
        search: searchText,
      })
    );
    navigate(
      `?&search=${searchText}&proveedores=${proveedor}&fechaInicial=${initialDate}&fechaFinal=${finalDate}&orderStatusId=${status}&orderStatusInternoId=${statusInterno}&abiertosCerrados=${openCloseToggle}&sellerMarketplace=${sellerMarketplaceToggle}&page=${valor}`
    );
  };
  /*UseEffect */
  React.useEffect(() => {
    const fetchData = () => {
      dispatch(
        obtenerTotalPedidos({
          proveedor: proveedor,
          filtrosStatus: status,
          filtrosStatusInterno: statusInterno,
          finalDate: finalDate,
          initialDate: initialDate,
          abiertosCerrados: openCloseToggle,
          sellerMarketplace: sellerMarketplaceToggle,
          search: searchText,
        })
      );
    };
    fetchData();
  }, [dispatch, proveedor, status, statusInterno, finalDate, initialDate, openCloseToggle, sellerMarketplaceToggle, searchText]);

  // stylos de mis autocomplete y date
  const getSxAutoYDate = (isSmallScreen, isMediumScreen) => {
    if (isSmallScreen) {
      return {
        paddingBottom: "2em",
        display: "flex",
        flexDirection: "column",
        width: "90%",
        margin: "auto",
      };
    }

    if (isMediumScreen) {
      return {
        display: "flex",
        flexDirection: "column",
        width: "90%",
      };
    }
    return {
      display: "flex",
      width: "90%",
      margin: "auto",
    };
  };

  const getSxContentFiltroDepoyable = (isSmallScreen) => {
    if (isSmallScreen) {
      return {
        display: "flex",
        flexDirection: "column",
        width: "90%",
        // backgroundColor: "green",
        margin: "auto",
        alignItems: "center",
        justifyContent: "center",
        paddingBottom: "2em",
      };
    }

    return {
      margin: "5px",
      width: "97%",
      position: "relative",
      paddingBottom: "2em",
    };
  };
  const getSxDate = (isSmallScreen, isMediumScreen) => {
    if (isMediumScreen) {
      return {
        width: "60%",
        margin: "5px",
        marginTop: "7px",
        minWidth: "150px",
        maxWidth: "60%",
        display: "flex",
        flexDirection: "column",
        columnGap: "10px",
      };
    }

    if (isSmallScreen) {
      return {
        margin: "1em auto auto auto",
        width: "100%",
        columnGap: "10px",
        bacground: "green",
        minWidth: "150px",
        maxWidth: "200px",
        display: "flex",
        flexDirection: "row",
      };
    }

    return {
      width: "100%",
      minWidth: "150px",
      maxWidth: "200px",
      display: "flex",
      flexDirection: "row",
      margin: "auto",
    };
  };

  const getSxDateConst = getSxDate(isSmallScreen, isMediumScreen);

  const getSxFiltroDepoyable = getSxContentFiltroDepoyable(isSmallScreen);

  // dentro de tu componente
  const sxPropsAutoYDate = getSxAutoYDate(isSmallScreen, isMediumScreen);

  //stilo botonoes de marketplaces pero en  small
  const getStylesMarketPlaceButton = () => {
    if (isSmallScreen) {
      return {
        marginBottom: "2px",
        width: "auto",
        borderRadius: "50px",
        // background: "green",
      };
    }
    return { marginBottom: "2px", width: "160px" };
  };

  const ComponentBtn = isSmallScreen ? Box : ButtonGroup;

  const [openDialog, setOpenDialog] = React.useState(false);

  // const loadingXlsx = useSelector((state) => { return state.ordenesConsolidadas.loadingXlsx });
  const messageXlsx = useSelector((state) => state.ordenesConsolidadas.messageXlsx);
  const okXlsx = useSelector((state) => state.ordenesConsolidadas.okXlsx);

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  // const handleClickXls = () => {
  //   // setLoadingXls(true);
  //   // setOpenDialog(true);
  //   dispatch(getXLSX());
  // };

  // useEffect(() => {
  //   if (okXlsx.okXlsx) {

  //     // setMensajeModal("Se ha generado el archivo correctamente");
  //     // handleOpen();
  //   }
  // }, [okXlsx]);

  // const [anchorElMenu, setAnchorElMenu] = React.useState(null);
  // const openMenu = Boolean(anchorElMenu);
  // const handleClick = (event) => {
  //   setAnchorElMenu(event.currentTarget);
  // };
  // const handleCloseMenu = () => {
  //   setAnchorElMenu(null);
  // };

  // Add this function to handle page refresh
  const handleRefreshPage = () => {
    window.location.reload();
  };

  // Add this function to clear all filters
  const handleClearFilters = () => {
    dispatch(
      obtenerTotalPedidos({
        proveedor: "1-2-3-4",
        filtrosStatus: status,
        filtrosStatusInterno: statusInterno,
        finalDate: finalDate,
        initialDate: initialDate,
        abiertosCerrados: "Abiertos",
        sellerMarketplace: sellerMarketplaceToggle,
        search: "",
      })
    );
    navigate(`?page=1`);
  };

  const returnDate = (dateProp) => {
    const date = useCustomDateTime(dateProp);

    const fecha = date.adjustedDateTime

    return fecha;
  };

  /*Manejar el return */
  if (logosEspera === true) {
    return <SkeletonTables />;
  } else if (cantidadDePaginas !== null) {
    if (typeof cantidadDePaginas === "string") {
      return <ManejarErrores errorCode={cantidadDePaginas} />;
    } else {
      return (
        <Box sx={{ padding: "0" }}>
          <ModalGeneral
            open={open}
            handleClose={handleClose}
            mensajeModal={mensajeModal}
          />

          {/* lasted update and btn export */}
          <Box sx={{ display: "flex", alignContent: "center", justifyContent: "space-between", marginTop: "1rem", ...(isSmallScreen && { width: "90%", margin: "1em auto" }), }}>


            {/* 6 botones de marketplace */}

            <Box sx={{
              display: "flex", gap: isSmallScreen ? "6px" : "1em", flexWrap: "wrap", justifyContent: "center", alignItems: "center",
              '& .MuiButtonBase-root': {
                minWidth: "40px",
              },

              '& .MuiTypography-root': {
                fontSize: "calc(0.5rem + 1.5vw)",
              },


            }}>
              <Tooltip title="Filtrar por recolección" placement="top" arrow>
                <Button
                  variant="outlined"
                  color="buttonGreenPink"
                  size="small"
                  onClick={() => aplicarFiltros(1)}
                  startIcon={<LocalShippingIcon />}
                >
                  {!isSmallScreen && "Recolección"}
                </Button>
              </Tooltip>

              <Tooltip title="Filtrar por empaquetado" placement="top" arrow>
                <Button
                  variant="outlined"
                  color="buttonGreenPink"
                  size="small"
                  onClick={() => aplicarFiltros(2)}
                  startIcon={<InventoryIcon />}
                >
                  {!isSmallScreen && "Empaquetado"}
                </Button>
              </Tooltip>

              <Tooltip title="Filtrar por externo" placement="top" arrow>
                <Button
                  variant="outlined"
                  color="buttonGreenPink"
                  size="small"
                  onClick={() => aplicarFiltros(3)}
                  startIcon={<PublicIcon />}
                >
                  {!isSmallScreen && "Externo"}
                </Button>
              </Tooltip>

              <Tooltip title="Actualizar página" placement="top" arrow>
                <Button
                  variant="outlined"
                  color="buttonGreenPink"
                  onClick={handleRefreshPage}
                  startIcon={<RefreshIcon />}
                  size="small"
                >
                  {!isSmallScreen && "Actualizar"}
                </Button>
              </Tooltip>

              <Tooltip title="Limpiar filtros" placement="top" arrow>
                <Button
                  variant="outlined"
                  color="buttonGreenPink"
                  onClick={handleClearFilters}
                  startIcon={<ClearAllIcon />}
                  size="small"
                >
                  {!isSmallScreen && "Limpiar filtros"}
                </Button>
              </Tooltip>

              <Tooltip
                title={
                  messageXlsx !== "Generando archivo excel, por favor espere..."
                    ? "Exportar Órdenes"
                    : messageXlsx
                }
                placement="top"
                arrow
              >
                <BtnsXlsx
                  proveedor={proveedor}
                  filtrosStatus={status}
                  filtrosStatusInterno={statusInterno}
                  finalDate={finalDate}
                  initialDate={initialDate}
                  abiertosCerrados={openCloseToggle}
                  sellerMarketplace={sellerMarketplaceToggle}
                  search={searchText}
                  isSmallScreen={isSmallScreen}
                />
              </Tooltip>
            </Box>

          </Box>

          <Box
            sx={
              isSmallScreen
                ? {
                  position: "relative",
                  height: showingFilters ? "auto" : "auto",
                  zIndex: "12",
                  // height: "100%",
                  top: "0",
                  left: "0",
                  right: "0",
                  bottom: "0",
                  padding: showingFilters ? "0 0 2em 0" : "0",
                  width: "90%",
                  margin: "auto",
                }
                : !isMediumScreen
                  ? {
                    flexGrow: "1",
                    width: "80%",
                    margin: "auto",
                    position: "relative",
                  }
                  : {}
            }
          >
            <FiltersGrid />
            <Collapse
              in={showingFilters}
              timeout="auto"
              sx={{ position: "relative" }}
            >
              <Box display="flex" flexDirection="row" sx={getSxFiltroDepoyable}>
                <Box sx={{ width: "minWidth", margin: "5px" }}>
                  {/* <Box> */}
                  <Box
                    display="flex"
                    flexDirection="column"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <b style={{ marginBottom: "4px" }}>Marketplace</b>
                    <ComponentBtn
                      sx={
                        isSmallScreen
                          ? {
                            display: "flex",
                            flexDirection: "row",
                            flexWrap: "wrap",
                            columnGap: "15px",
                            rowGap: "15px",
                            justifyContent: "space-around",
                          }
                          : {}
                      }
                      // color="buttonGreenPink"
                      orientation="vertical"
                      aria-label="vertical contained button group"
                      variant="contained"
                    >
                      {buttons.map((button) => {
                        const { variant, onClick } = handleButtonClick(button);
                        return (
                          <Button
                            key={button.id}
                            variant={variant}
                            color="buttonGreenPink"
                            onClick={onClick}
                          >
                            {button.name}
                          </Button>
                        );
                      })}
                    </ComponentBtn>
                    {!marketPlaceButtonDisableAmazon &&
                      !marketPlaceButtonDisableClaroShop &&
                      !marketPlaceButtonDisableML &&
                      !marketPlaceButtonDisableWalmart && (
                        <Alert
                          variant="filled"
                          severity="error"
                          justifyContent="center"
                          style={{ marginTop: "6px", width: "160px" }}
                        >
                          Seleccionar Marketplaces
                        </Alert>
                      )}
                  </Box>
                </Box>
                {/* aqui encierro mis busquedas en un div */}
                <Box sx={sxPropsAutoYDate}>
                  {/* autocomplete */}
                  <Box
                    sx={
                      isSmallScreen
                        ? {
                          display: "flex",
                          width: "70%",
                          flexDirection: "column",
                          margin: "auto",
                          // backgroundColor: "red",
                        }
                        : { display: "flex", width: "100%" }
                    }
                  >
                    <Box sx={{ width: "100%", margin: "5px" }}>
                      {/* <Box> */}
                      <Box
                        sx={{
                          justifyContent: "center",
                          width: "100%",
                          maxWidth: "330px",
                        }}
                      >
                        <FormControl sx={{ marginTop: "2px", width: "100%" }}>
                          <InputLabel id="demo-multiple-chip-label">
                            Status
                          </InputLabel>
                          <Select
                            labelId="demo-multiple-chip-label"
                            id="demo-multiple-chip"
                            multiple
                            value={statusFiltro}
                            onChange={manejarCambioDeStatusFiltros}
                            input={
                              <OutlinedInput
                                id="select-multiple-chip"
                                label="Status"
                              />
                            }
                            renderValue={(selected) =>
                              selected.length === namesStatus.length ? (
                                <Chip
                                  key={namesStatus.length}
                                  label="Todos seleccionados"
                                />
                              ) : (
                                <Box
                                  sx={{
                                    display: 'flex',
                                    flexWrap: 'wrap',
                                    gap: 0.5,
                                    maxWidth: "100%",
                                    width: "100%",
                                    margin: 0,
                                    paddingTop: "6px",
                                    paddingBottom: "7px",
                                  }}
                                >
                                  {selected.map((value) => (
                                    <Chip
                                      key={value} 
                                      label={getStatusPropertyUsingStatusId2(
                                        parseInt(value),
                                        "orderStatus",
                                        namesStatus
                                      )}
                                      size="small"
                                    // color="primary"
                                    />
                                  ))}
                                </Box>
                              )
                            }
                            MenuProps={MenuProps}
                          >
                            {namesStatus.map((name) => (
                              <MenuItem
                                key={name.orderStatusId}
                                value={name.orderStatusId}
                                style={getStyles(
                                  name.orderStatusId,
                                  statusFiltro,
                                  theme
                                )}
                              >
                                {name.orderStatus}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Box>
                      <Box
                        display="flex"
                        flexDirection="row"
                        sx={{ justifyContent: "space-evenly" }}
                      >
                        {/* <Box display="flex" sx={{ width: "160px" }}> */}
                        <Box display="flex">
                          <Button
                            variant="contained"
                            color="buttonGreen"
                            size="small"
                            sx={{
                              marginTop: "6px",
                              width: "95%",
                              marginRight: "auto",
                            }}
                            onClick={handleSelectAllStatusFiltros}
                          >
                            Todos
                          </Button>
                        </Box>
                        {/* <Box display="flex" sx={{ width: "160px" }}> */}
                        <Box display="flex">
                          <Button
                            variant="outlined"
                            color="buttonGreen"
                            size="small"
                            sx={{
                              marginTop: "6px",
                              width: "95%",
                              marginLeft: "auto",
                            }}
                            onClick={handleSelectNoneStatusFiltros}
                          >
                            Ninguno
                          </Button>
                        </Box>
                      </Box>

                      {statusFiltro?.length === 0 && (
                        <Alert
                          variant="filled"
                          severity="error"
                          style={{ marginTop: "6px" }}
                        >
                          {mensajeAlertaStatus}!
                        </Alert>
                      )}
                    </Box>

                    <Box sx={{ width: "100%", margin: "5px" }}>
                      {/* <Box> */}
                      <Box
                        sx={{
                          justifyContent: "center",
                          width: "100%",
                          maxWidth: "330px",
                        }}
                      >
                        <FormControl sx={{ marginTop: "2px", width: "100%" }}>
                          <InputLabel id="demo-multiple-chip-label">
                            Status Interno
                          </InputLabel>
                          <Select
                            labelId="demo-multiple-chip-label"
                            id="demo-multiple-chip"
                            multiple
                            value={statusInternosFiltro}
                            onChange={manejarCambioDeStatusInternoFiltros}
                            input={
                              <OutlinedInput
                                id="select-multiple-chip"
                                label="Status interno"
                              />
                            }
                            renderValue={(selected) =>
                              selected.length ===
                                namesStatusInterno.filter(
                                  (statusInterno) =>
                                    statusInterno["type"] === "menuItem"
                                ).length ? (
                                <Chip
                                  key={
                                    namesStatusInterno.filter(
                                      (statusInterno) =>
                                        statusInterno["type"] === "menuItem"
                                    ).length
                                  }
                                  label="Todos seleccionados"
                                />
                              ) : (
                                <Box
                                  sx={{
                                    display: 'flex',
                                    flexWrap: 'wrap',
                                    gap: 0.5,
                                    maxWidth: "100%",
                                    width: "100%",
                                    margin: 0,
                                    paddingTop: "6px",
                                    paddingBottom: "7px",
                                  }}
                                >
                                  {selected.map((value) => (
                                    <Chip
                                      key={value}
                                      label={getStatusInternoPropertyUsingStatusId2(
                                        parseInt(value),
                                        "orderInternalStatus",
                                        namesStatusInterno
                                      )}
                                      size="small"
                                    // color="primary"
                                    />
                                  ))}
                                </Box>
                              )
                            }
                            MenuProps={MenuProps}
                          >
                            {namesStatusInterno.map((statusInterno) => {
                              if (statusInterno["type"] === "menuItem") {
                                return (
                                  <MenuItem
                                    key={statusInterno.orderInternalStatusId}
                                    value={statusInterno.orderInternalStatusId}
                                    style={getStyles(
                                      statusInterno.orderInternalStatusId,
                                      statusInternosFiltro,
                                      theme
                                    )}
                                  >
                                    {statusInterno.orderInternalStatus}
                                  </MenuItem>
                                );
                              } else {
                                return (
                                  <ListSubheader key={statusInterno.subHeaderName}>
                                    <i style={{ color: "red" }}>
                                      {statusInterno["subHeaderName"]}
                                    </i>
                                  </ListSubheader>
                                );
                              }
                            })}
                          </Select>
                        </FormControl>
                      </Box>
                      <Box
                        display="flex"
                        flexDirection="row"
                        sx={{ justifyContent: "space-evenly" }}
                      >
                        {/* <Box display="flex" sx={{ width: "160px" }}> */}
                        <Box display="flex">
                          <Button
                            variant="contained"
                            color="buttonGreen"
                            size="small"
                            sx={{
                              marginTop: "6px",
                              width: "95%",
                              marginRight: "auto",
                            }}
                            onClick={handleSelectAllStatusInternoFiltros}
                          >
                            Todos
                          </Button>
                        </Box>
                        {/* <Box display="flex" sx={{ width: "160px" }}> */}
                        <Box display="flex">
                          <Button
                            variant="outlined"
                            color="buttonGreen"
                            size="small"
                            sx={{
                              marginTop: "6px",
                              width: "95%",
                              marginLeft: "auto",
                            }}
                            onClick={handleSelectNoneStatusInternoFiltros}
                          >
                            Ninguno
                          </Button>
                        </Box>
                      </Box>
                      {statusInternosFiltro.length === 0 && (
                        <Alert
                          variant="filled"
                          severity="error"
                          style={{ marginTop: "6px" }}
                        >
                          {mensajeAlertaStatusInterno}!
                        </Alert>
                      )}
                    </Box>
                  </Box>
                  {/* fechas */}
                  <Box sx={getSxDateConst}>
                    <Stack spacing={1.52}>
                      <LocalizationProvider dateAdapter={AdapterDateFns}>
                        <Box
                          sx={
                            isMediumScreen
                              ? {
                                display: "flex",
                                width: "100%",
                                // margin: "1em auto auto auto",
                                margin: "1em",
                                marginX: "auto",
                                columnGap: "10px",
                              }
                              : {
                                display: "flex",
                                width: "100%",
                                flexDirection: "column",
                                margin: "auto",
                                rowGap: "10px",
                              }
                          }
                        >
                          <Box
                            sx={
                              isMediumScreen
                                ? { display: "flex", flexDirection: "row" }
                                : {
                                  display: "flex",
                                  width: "100%",
                                  flexDirection: "column",
                                }
                            }
                          >
                            <DesktopDatePicker
                              sx={{ display: "flex", flexDirection: "row" }}
                              label="Desde:"
                              inputFormat="MM/dd/yyyy"
                              value={initialDatePicker}
                              onChange={(date) => setInitialDatePicker(date)}
                              renderInput={(params) => (
                                <TextField {...params} />
                              )}
                            />
                            {initialDatePicker > today && (
                              <Alert
                                variant="filled"
                                severity="error"
                                style={{ marginTop: "6px" }}
                              >
                                Fecha incorrecta!
                              </Alert>
                            )}
                          </Box>
                          <Box
                            sx={
                              isMediumScreen
                                ? { display: "flex", flexDirection: "colum" }
                                : {
                                  display: "flex",
                                  width: "100%",
                                  flexDirection: "row",
                                }
                            }
                          >
                            <DesktopDatePicker
                              label="Hasta:"
                              inputFormat="MM/dd/yyyy"
                              value={finalDatePicker}
                              onChange={(date) => setFinalDatePicker(date)}
                              renderInput={(params) => (
                                <TextField {...params} />
                              )}
                            />
                            {finalDatePicker > today && (
                              <Alert
                                variant="filled"
                                severity="error"
                                style={{ marginTop: "6px" }}
                              >
                                Fecha incorrecta!
                              </Alert>
                            )}
                          </Box>
                        </Box>
                      </LocalizationProvider>
                    </Stack>
                    {finalDatePicker - initialDatePicker < 0 && (
                      <Alert
                        variant="filled"
                        severity="error"
                        style={{ marginTop: "6px" }}
                      >
                        Rango incorrecto!
                      </Alert>
                    )}
                  </Box>
                </Box>
              </Box>

              <Box
                sx={{
                  display: "flex", // Utiliza Flexbox para el layout
                  flexDirection: "row", // Organiza los botones en columna
                  alignItems: "flex-end", // Alinea los botones a la derecha
                  position: "absolute", // Posición basada en el tamaño de pantalla
                  justifyContent: "center", // Centra los botones horizontalmente
                  right: "0", // Derecha 115px en todos los casos
                  bottom: isSmallScreen ? "5%" : "1rem", // Ajusta la posición inferior según el tamaño de pantalla
                  gap: "1rem", // Espacio entre botones
                }}
              >
                {/* <Button variant="contained" onClick={(e) => aplicarFiltros(1)}>
                  Recoleccion
                </Button>
                <Button variant="contained" onClick={(e) => aplicarFiltros(2)}>
                  Empaquetado
                </Button>
                <Button variant="contained" onClick={(e) => aplicarFiltros(3)}>
                  Externo
                </Button> */}
                <Button variant="contained"
                  color="buttonGreen"
                  onClick={() => aplicarFiltros()}>
                  Filtrar
                </Button>
                {/* Repite el Button según necesites */}
              </Box>
            </Collapse>
          </Box>
          {/*filtros*/}
          {cantidadDePaginas === 0 ? (
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              No hay productos que cumplan con estas especificiaciones
            </Box>
          ) : (
            <React.Fragment>
              {/* // aqui se despilita la tabla de pedidos */}
              {showingFilters && isSmallScreen ? null : (
                <>
                  <TablaDesplegableWrapper
                    marketplaceLogos={marketplaceLogos}
                    marketplaces={marketplaces}
                    proveedor={proveedor}
                    offset={offset}
                    status={status}
                    statusInterno={statusInterno}
                    initialDate={initialDate}
                    finalDate={finalDate}
                    openCloseAlignment={openCloseToggle}
                    sellerMarketplaceAlignment={sellerMarketplaceToggle}
                    search={searchText}
                  />
                </>
              )}

              <Box sx={{ justifyContent: "center", display: "flex" }}>
                <Stack spacing={2}>
                  <Pagination
                    count={cantidadDePaginas}
                    page={parseInt(page)} d
                    onChange={cambioDePagina}
                  />
                </Stack>
              </Box>
            </React.Fragment>
          )}
          <DialogLoading openDialog={openDialog} handleCloseDialog={handleCloseDialog} loadingText={"Esta exportacion puede tardan unos segundos"} />
          {
            okXlsx?.okXlsx &&
            // <Alert severity={okXlsx.severityXlsx} sx={{ marginTop: "1em", position:"absolute", bottom:"10px" }} onClose={() => dispatch(changeOkXlsx()) }>
            //   {okXlsx.messageXlsx}
            // </Alert>
            <AlertComponent color={okXlsx.severityXlsx} message={okXlsx.messageXlsx} cleanMessage={changeOkXlsx} />
          }
        </Box>
      );
    }
  } else {
    return <SkeletonTables />;
  }
};
/*Exportar el componente*/
export default TablaPedidos;
