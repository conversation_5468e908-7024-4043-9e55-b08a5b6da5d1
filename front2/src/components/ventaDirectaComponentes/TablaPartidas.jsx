import React from 'react'
import PartidaVentaDirecta from './PartidaVentaDirecta'
import {
  useMediaQuery,
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import sales from "../css/ventas/sales.module.css";
import AccordionViewPartida from './Accoredon';

const TablaPartidas = ({
  partidas,
  setPartidas,
  handleValueQuantity,
  handleChangePrice,
  handleUtilityChange,
  handleTypeMoney,
  handleAcceptDiscount,
  handleDelete,
  enCotizacion
}) => {
  const isMediumScreen = useMediaQuery("(max-width:1413px)");
  const isAccordion = useMediaQuery("(max-width:950px)")


  return (
    <>
      <Box
        className={sales.tableProducts}
        sx={{
          '& .TextField-root': {
            width: '150px'
          }
        }}
      >
        <TableContainer>
          <Table
            aria-label="simple table"
            component={Paper}
          >
            <TableHead>
              <TableRow>
                <TableCell align="center" width="100px">
                  Productos
                </TableCell>
                {!isAccordion && (
                  <>
                    <TableCell align="center" width="100px">
                      {isMediumScreen ? "Stock" : "Cantidad"}
                    </TableCell>
                    <TableCell
                      align="center"
                      width="100px"
                      className={`${isMediumScreen ? sales.hiddenCell : ''}`}
                    >
                      Precio Prov
                    </TableCell>

                    <TableCell align="center" width="100px">
                      {isMediumScreen ? "Rendimiento" : "Utilidad"}
                    </TableCell>
                    <TableCell
                      align="center"
                      width="100px"
                      className={sales.hiddenCell}
                    >
                      Precio Cliente
                    </TableCell>
                    <TableCell align="center" width="100px">
                      Importe
                    </TableCell>
                  </>
                )}
              </TableRow>
            </TableHead>
            <TableBody>

              {isAccordion ? (
                partidas?.map((partida) => (
                  // para vistas pequeñas, se usa el accordion
                  <AccordionViewPartida
                    key={partida.id}
                    partida={partida}
                    setPartidas={setPartidas}
                    handleValueQuantity={handleValueQuantity}
                    handleChangePrice={handleChangePrice}
                    handleUtilityChange={handleUtilityChange}
                    handleTypeMoney={handleTypeMoney}
                    handleAcceptDiscount={handleAcceptDiscount}
                    handleDelete={handleDelete}
                    enCotizacion={enCotizacion}
                  />
                ))
              ) : (
                partidas?.map((partida) => (
                  <PartidaVentaDirecta
                    key={partida.id}
                    partidas={partidas}
                    setPartidas={setPartidas}
                    partida={partida}
                    handleValueQuantity={handleValueQuantity}
                    handleChangePrice={handleChangePrice}
                    handleUtilityChange={handleUtilityChange}
                    handleTypeMoney={handleTypeMoney}
                    handleAcceptDiscount={handleAcceptDiscount}
                    handleDelete={handleDelete}
                    enCotizacion={enCotizacion}
                    isMediumScreen = {isMediumScreen}
                  />
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </>

  )
}

export default TablaPartidas