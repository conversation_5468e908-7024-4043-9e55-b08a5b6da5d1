import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import { useState } from "react";
import {
  getInternalStatusDirectSale,
  getNewDirectSale,
} from "../../redux/configuracionesDucks";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";

export default function SelectStatusInterno({
  setSelectedtatus,
  selectedtatus,
}) {
  const [age, setAge] = useState("");

  const handleChange = (event) => {
    setSelectedtatus(event.target.value);
  };

  const internalStatus = useSelector(
    (store) => store.ConfiguracionSistema.newDirectSale
  );

  return (
    <FormControl sx={{ m: 1, minWidth: 120 }} size="small">
      <InputLabel id="demo-select-small-label">Age</InputLabel>
      <Select
        labelId="demo-select-small-label"
        id="demo-select-small"
        value={selectedtatus}
        label="Age"
        onChange={handleChange}
        // sx={{ width: "130px" }}
      >
        <MenuItem value="">
          <em>None</em>
        </MenuItem>

        {internalStatus?.loadPageNewDirectSale?.directSaleInternalStatuses?.map(
          (item) => (
            <MenuItem
              key={item.directSaleInternalStatusId}
              value={item.directSaleInternalStatusId}
            >
              {item.directSaleInternalStatus}
            </MenuItem>
          )
        )}
      </Select>
    </FormControl>
  );
}
