import * as React from "react";
import Accordion from "@mui/material/Accordion";
import AccordionActions from "@mui/material/AccordionActions";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import Button from "@mui/material/Button";
import BuscarProducto from "../productosComponentes/BuscarProducto";
import { BuscarProductoComponenteExterno } from "./BuscarProductoComponente";
import { Cantidad } from "./componentesProducts/Cantidad";
import { PriceProvedor } from "./componentesProducts/PriceProvedor";
import { Utility } from "./componentesProducts/Utility";
import { PriceTotal } from "./componentesProducts/PriceTotal";
import { Box } from "@mui/material";
import { ResumeSale } from "./componentesProducts/ResumeSale";
import DeleteIcon from "@mui/icons-material/Delete";

export const AccordionProducts = ({
  products,
  setProducts,
  handleUtilidadTypeMoney,
  handleValueQuantity,
  product,
  handleChangePrice,
  truncateToTwoDecimalsString,
  truncateToTwoDecimals,
  value,
  handleUtilityChange,
  handleTypeMoney,
  calculatePriceClient,
  handleClickOpen,
  sales,
  id,
  getPriceWithDiscountProduct,
  getIvaProduct,
  getFinalPrice,
  handleDelete,
  showDialog,
}) => {
  return (
    <>
      <Accordion>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1-content"
          id="panel1-header"
        // sx={{ display: "flex", alignItems: "center" }}
        >
          <Box className={sales.header}>
            <Button
              onClick={() => handleDelete(product.id)}
              style={{ margin: "0", width: "auto" }}
            >
              <DeleteIcon color="error" />
            </Button>
            Producto
          </Box>
        </AccordionSummary>
        <AccordionDetails
          sx={{ gap: "1rem", display: "flex", flexDirection: "column" }}
        >

          <Box className={sales.AccordionContainInfo}>
            <Box className={sales.AccordionContainInfoSale}>
              <BuscarProductoComponenteExterno
                products={products}
                setProducts={setProducts}
                direcSale={true}
                handleUtilidadTypeMoney={handleUtilidadTypeMoney}
                id={id}
                isAccordion={true}
              />
              <Cantidad
                handleValueQuantity={handleValueQuantity}
                product={product}
                isResponsive={true}
              />
              <PriceProvedor
                product={product}
                handleChangePrice={handleChangePrice}
                truncateToTwoDecimalsString={truncateToTwoDecimalsString}
                truncateToTwoDecimals={truncateToTwoDecimals}
                value={value}
                isResponsive={true}
              />
              <Utility
                product={product}
                handleUtilityChange={handleUtilityChange}
                handleTypeMoney={handleTypeMoney}
                isResponsive={true}
              />
              <PriceTotal
                product={product}
                calculatePriceClient={calculatePriceClient}
                handleClickOpen={handleClickOpen}
                sales={sales}
                isResponsive={true}
              />
            </Box>
            <Box className={sales.AccordionContainInfoResume}>
              <ResumeSale
                truncateToTwoDecimalsString={truncateToTwoDecimalsString}
                handleUtilidadTypeMoney={handleUtilidadTypeMoney}
                product={product}
                getPriceWithDiscountProduct={getPriceWithDiscountProduct}
                getIvaProduct={getIvaProduct}
                getFinalPrice={getFinalPrice}
                isResponsive={true}
              />
              {showDialog(product.id)}
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>
    </>
  );
};
