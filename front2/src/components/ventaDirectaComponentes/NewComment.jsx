import {
  CircularProgress,
  Grid,
  IconButton,
  TextField,
  List,
  ListItem,
  ListItemAvatar,
  Avatar,
  Typography,
  ThemeProvider,
  FormControl,
  InputAdornment,
  createTheme,
  Divider,
  Switch,
  FormControlLabel,
  Skeleton
} from "@mui/material";
import { Box } from "@mui/system";
import { useDispatch, useSelector } from "react-redux";
import { useState } from "react";
import SendIcon from "@mui/icons-material/Send";
import CancelIcon from "@mui/icons-material/Cancel";
import CloseIcon from '@mui/icons-material/Close';
import { useEffect } from "react";
import { orange, purple } from "@mui/material/colors";
import {
  setMsgVentas,
  setShowDeletedComments
} from "../../redux/pedidosDucks";
import * as React from 'react';
import { ListComents } from "../ventasComponentes/ListComents";
import CustomDialog from "../componentesGenerales/CustomDialog";
import { useTheme } from "@emotion/react";
import { CustomInput } from "../componentesGenerales/CustomInput";
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import { useRef } from 'react';
import { Alert } from "@mui/material";
import { AlertComponent } from "../componentesGenerales/Alert";
import { getOrderCommentsWithDeleted } from '../utils/utils';

const NewCommentComponent = ({ addNewCommentFunction,
  commentType, setIsAddingComment, isResponsive, user,
  isErrorComment, newComment, handleSetNewComment, handleSendComment, loading, maxLength }) => {


  const styleList = () => ({
    width: "100% !important",
    '& .MuiFormControl-root': {
      width: '100%',
    },
  })

  const theme = useTheme();

  const inputRef = useRef(null);
  React.useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);



  return (

    <List sx={styleList()}
    >
      <ListItem
        sx={{
          padding: "0",
          width: "100%",
        }}
      >
        <Grid
          container
          sx={{
            width: "100%",
            // flexWrap: commentType === 'direct_sale' || isResponsive ? "nowrap" : "wrap",
            display: "flex",
            justifyContent: "space-evenly",
            flexDirection: "row",
          }}
        >
          {/*     <ListItemAvatar>
            <Avatar
              sx={{ bgcolor: purple[500], padding: "0" }}
              alt={user ? user.userName : "Usuario"}
              src={user ? user.URLphoto : "/broken-image.jpg"}
            />
          </ListItemAvatar>
 */}

          <Box
            sx={{
              width: "100%",
              color: `${theme.palette.text.primary} !important`,
            }}>

            <CustomInput
              inputValue={newComment}
              setInputValue={handleSetNewComment}
              onSend={handleSendComment}
              loading={loading}
              isError={isErrorComment}
              placeholder="Escribe un mensaje"
              addNewCommentFunction={addNewCommentFunction}
              maxLength={maxLength}
              ref={inputRef}
              onMaxLengthAlert={() => setShowMaxLengthAlert(true)}
            />
           
          </Box>


        </Grid>
      </ListItem>
    </List>
    //   </Box>
    // </Grid>
  )
}



export const NewComment = ({
  commentType,
  isResponsive,
  addNewCommentFunction,
  setIsAddingComment,
  comments,
  deleteComment,
  updateComment,
  orderId,
  getOrderCommentsWithDeletedFunction
}) => {
  const dispatch = useDispatch();
  const user = useSelector((store) => store.usuario);
  const [newComment, setNewComment] = useState("");
  const [isErrorComment, setIsErrorComment] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showMaxLengthAlert, setShowMaxLengthAlert] = useState(false);
  const [loadingComments, setLoadingComments] = React.useState(false);

  const handleSetNewComment = (comment) => {
    setNewComment(comment);
    
    // Mostrar alerta cuando se llegue al máximo de caracteres
    if (comment.length >= maxLength && !showMaxLengthAlert) {
      setShowMaxLengthAlert(true);
      // Ocultar la alerta después de 3 segundos
     
    }
  }


  // Función para manejar el envío de un nuevo comentario
  const handleSendComment = async () => {

    // Verifica si el nuevo comentario está vacío
    if (newComment === "") {
      // Si el comentario está vacío, establece un estado de error para el comentario
      setIsErrorComment(true);
      // Termina la ejecución de la función para evitar enviar un comentario vacío
      return;
    }

    // Verifica si el comentario está en el máximo de caracteres
    if (newComment.length >= maxLength) {
      setShowMaxLengthAlert(true);
      // Ocultar la alerta después de 3 segundos
      setTimeout(() => {
        setShowMaxLengthAlert(false);
      }, 3000);
      return; // No enviar el comentario si está en el máximo
    }

    // Realiza un dispatch de la acción correspondiente para agregar el comentario
    // Verifica si se trata de una orden o una factura para llamar a la función apropiada
    setLoading(true)
    const [message, severity] = await addNewCommentFunction(newComment)
    dispatch(setMsgVentas(message, severity))
    setLoading(false)
    // setIsAddingComment(false)
    setNewComment("")
     // Refresca la lista de comentarios eliminados si el switch está activado
 
  };



  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setIsAddingComment(false)
  };

  const descriptionElementRef = React.useRef(null);
  React.useEffect(() => {
    if (open) {
      const { current: descriptionElement } = descriptionElementRef;
      if (descriptionElement !== null) {
        descriptionElement.focus();
      }
    }
  }, [open]);

  useEffect(() => {
    setOpen(true)
  }, [])

  const commentsEndRef = React.useRef(null); // Este ref se usará para hacer scroll

  // Función para hacer scroll al final
  const scrollToBottom = () => {
    commentsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Usamos useEffect para hacer el scroll cada vez que cambian los comentarios
  useEffect(() => {
    scrollToBottom();
  }, [comments]);

  const maxLength = useSelector((store) => store.pedidos.commentMaxLength);
  const showDeletedComments = useSelector((store) => store.pedidos.showDeletedComments);

  useEffect(() => {
    if (showMaxLengthAlert) {
      setTimeout(() => {
        setShowMaxLengthAlert(false);
      }, 3000);
    }
  }, [showMaxLengthAlert]);

  // Supón que tienes una variable para saber si el usuario es admin
  const isAdmin = user.role === "Admin";
  const [commentsDeleted, setCommentsDeleted] = React.useState(comments);

  const handleShowDeletedCommentsChange = async (checked) => {
    dispatch(setShowDeletedComments(checked));
    setLoadingComments(true);
    await getOrderCommentsWithDeletedFunction(orderId, checked)
    
    setTimeout(() => {
      setLoadingComments(false);
    }, 1000);

  };  




  return (

    <CustomDialog
      open={open}
      onClose={handleClose}
      title="Comentarios"
      maxWidth="590px"
      width="100%"
      maxHeight="85%"
      dialogStyles={{ borderRadius: '24px' }}
      actions={
        <NewCommentComponent
          commentType={commentType}
          addNewCommentFunction={addNewCommentFunction}
          setIsAddingComment={setIsAddingComment}
          isResponsive={isResponsive}
          user={user}
          isErrorComment={isErrorComment}
          newComment={newComment}
          handleSetNewComment={handleSetNewComment}
          handleSendComment={handleSendComment}
          loading={loading}
          maxLength={maxLength}
          orderId={orderId}
        />
      }
    >
      {comments?.length === 0 ?
        <Box
          className="messages-list"
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: 360,
            my: 2,
          }}
        >
          <ChatBubbleOutlineIcon sx={{ fontSize: 48, color: "text.disabled", mb: 1 }} />
          <Typography variant="body2" color="text.secondary">
            No hay comentarios todavía
          </Typography>
        </Box>
        :

        <Box sx={{ overflowY: 'auto', height: '380px' }}>
          {isAdmin && (
            <FormControlLabel
              control={
                <Switch
                  checked={showDeletedComments}
                  onChange={(_, checked) => {
                    handleShowDeletedCommentsChange(checked)
                  }}
                  color="primary"
                />
              }
              label="Ver comentarios eliminados"
              sx={{ mb: 1, height: "10%" }}
            />
          )}
          {loadingComments ? (
            <Skeleton variant="rectangular" width="100%" height="80%" sx={{ borderRadius: 2, mb: 2 }} />
          ) : (
            <ListComents
              sx={{ width: '100%', padding: '0' }}
              comments={comments}
              deleteComment={deleteComment}
              updateComment={updateComment}
            />
          )}

          {/* Este div actúa como el punto final para el scroll */}
          <div ref={commentsEndRef} />
        </Box>
      }
      
      {/* Alerta cuando se llega al máximo de caracteres */}
      {showMaxLengthAlert && (
        <AlertComponent
          color="warning"
          message={`Has llegado al límite máximo de ${maxLength} caracteres`}
          cleanMessage={() => setShowMaxLengthAlert(false)}
        />
      )}
    </CustomDialog>


  );
};
