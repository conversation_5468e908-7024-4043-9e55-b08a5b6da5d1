import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from "@mui/material";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { leerClientesAccion } from "../../redux/usersDucks";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import theme from "../../temaConfig";
import sales from "../css/ventas/sales.module.css";
import { FormularioDatos } from "../componentesGenerales/clientes/Register";

const BuscarCliente = ({ selectedClient, setSelectedClient, enCotizacion }) => {
  const [open, setOpen] = React.useState(false);
  const [results, setResults] = React.useState([]);
  const [timer, setTimer] = React.useState(null);
  const [value, setValue] = React.useState("");
  const newCliwnt = useSelector((store) => store.usuario.onlyClients);
  const dispatch = useDispatch();

  const getSearch = useSelector((store) => {
    return store.usuario.clientes;
  });

  let loading = open && getSearch?.length === 0;

  useEffect(() => {
    loading = true;
    if (getSearch) {
      setResults(getSearch.clients);
      loading = false;
    }
  }, [getSearch, dispatch, loading]);

  const setData = () => {
    let productosBase = [];
    if (results?.length > 0) {
      productosBase = [...results];
    }
    return productosBase;
  };


  const handleChangeValue = (event, value) => {
    //objeto del producto seleccionado
    setSelectedClient(value);
    setOpen(false)
    dispatch(nullOnlyClients());
  };

  //si creamos un nuevo cliente se selecciona automaticamente
  useEffect(() => {
    if (newCliwnt) {
      setSelectedClient(newCliwnt);
    }
  }, [newCliwnt]);

  useEffect(() => {
    if (!open) {
      setResults([]);
    }
    if (timer) {
      clearTimeout(timer);
    }
    loading = true;
    setTimer(
      setTimeout(() => {
        dispatch(leerClientesAccion());
      }, 1000)
    );
  }, []);

  const [openDialog, setOpenDialog] = React.useState(false);

  return (
    <Box className={sales.cliente}
      style={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "space-around" }}
    >
      {/* <FormularioDatos /> */}

      <Autocomplete
        id="asynchronous-demo"
        sx={{ minWidth: "150px",  maxWidth: enCotizacion ? "90%" : "300" , width: enCotizacion ? "90%" : "300px" }}
        open={open}
        value={selectedClient}
        disabled={!enCotizacion}
        onOpen={() => {
          setOpen(true);
        }}
        onClose={() => {
          setOpen(false);
        }}
        isOptionEqualToValue={(option, value) => option.clientId === value.clientId}
        // value={value}
        getOptionLabel={(option) => {
          return `${option.name ? option.name : "Sin nombre"} (${option.nickname ? option.nickname : "*"})`
        }}
        options={setData()}
        loading={loading}
        onChange={handleChangeValue}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Busca un Cliente"
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <React.Fragment>
                  {loading ? (
                    <CircularProgress color="inherit" size={20} />
                  ) : null}
                  {params.InputProps.endAdornment}
                </React.Fragment>
              ),
            }}
          />
        )}
      />
      <Button
        style={{ margin: ".5rem 1rem" }}
        size="small"
        variant="contained"
        color="buttonGreenPink"
        disabled= {!enCotizacion}
        endIcon={<AddCircleIcon sx={{ margin: "0", padding: "0" }} />}
        onClick={() => setOpenDialog(true)}
      >
        {/* Agregar */}
      </Button>

      < FormularioDatos setOpenDialog={setOpenDialog} openDialog={openDialog} />

    </Box>
  );
};

export default BuscarCliente;
