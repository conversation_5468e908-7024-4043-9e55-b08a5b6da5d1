import React from 'react'
import { BuscarProductoComponenteExterno } from './BuscarProductoComponente';
import { Cantidad } from './componentesProducts/Cantidad';
import {
  Box,
  Button,
  TableCell,
  TableRow,
  Grid
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import sales from "../css/ventas/sales.module.css";
import { PriceProvedor } from './componentesProducts/PriceProvedor';
import { Utility } from './componentesProducts/Utility';
import { PriceTotal } from './componentesProducts/PriceTotal';
import { ResumeSale } from './componentesProducts/ResumeSale';
import DiscountDialogPartida from './DiscountDialogPartida';
import {
  calculatePriceClient
} from '../utils/utils';

const PartidaVentaDirecta = ({
  setPartidas,
  partida,
  handleValueQuantity,
  handleChangePrice,
  handleUtilityChange,
  handleTypeMoney,
  handleAcceptDiscount,
  handleDelete,
  enCotizacion,
  isMediumScreen = false
}) => {
  const [open, setOpen] = React.useState(false)
  return (
    <>
      <TableRow
        sx={{
          padding: "0",
          borderBottom: "1px solid grey",
        }}
      >
        <TableCell
          component="th"
          scope="row"
          align="center"
          sx={{
            padding: "0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            borderBottom: "0",
          }}
        >
          <Box
            sx={{
              marginTop: "1.5em !important",
              padding: "0px",
              display: "flex",
              gap: "1rem",
              alignItems: "start",
              justifyContent: "center",
            }}
          >
            <Button
              onClick={() => handleDelete(partida.id)}
              style={{ marginTop: "18px", padding:"0", width: "auto", minWidth: "10px" }}
              disabled={!enCotizacion}
            >
              <DeleteIcon color="error" />
            </Button>
            <BuscarProductoComponenteExterno
              product={partida}
              setProducts={setPartidas}
              enCotizacion={enCotizacion}
            />
          </Box>
        </TableCell>

        <TableCell
          component="th"
          scope="row"
          sx={{ borderBottom: "0", verticalAlign: "top" }}
        >
          <Cantidad
            product={partida}
            handleValueQuantity={handleValueQuantity}
            enCotizacion={enCotizacion}
          />
          
          <Box className={sales.showInput}>
            <PriceProvedor
              product={partida}
              setProducts={setPartidas}
              handleChangePrice={handleChangePrice}
              enCotizacion={enCotizacion}
            />
          </Box>
        </TableCell>

        <TableCell
          component="th"
          scope="row"
          sx={{ borderBottom: "0", verticalAlign: "top" }}
          className={sales.hiddenCell}
        >
          <PriceProvedor
            product={partida}
            setProducts={setPartidas}
            handleChangePrice={handleChangePrice}
            enCotizacion={enCotizacion}
            isMediumScreen={isMediumScreen}
          />
        </TableCell>

        <TableCell
          component="th"
          scope="row"
          sx={{ borderBottom: "0", verticalAlign: "top" }}
        >
          <Utility
            product={partida}
            handleUtilityChange={handleUtilityChange}
            handleTypeMoney={handleTypeMoney}
            sales={sales}
            enCotizacion={enCotizacion}
          />
          <Box className={sales.showInput}>
            <PriceTotal
              product={partida}
              handleClickOpen={setOpen}
              sales={sales}
              enCotizacion={enCotizacion}
            />
          </Box>
        </TableCell>
        
        <TableCell
          component="th"
          scope="row"
          sx={{ borderBottom: "0", verticalAlign: "top" }}
          className={sales.hiddenCell}
        >
          <PriceTotal
            product={partida}
            handleClickOpen={setOpen}
            sales={sales}
            enCotizacion={enCotizacion}
          />
        </TableCell>

        <TableCell
          component="th"
          scope="row"
          sx={{ borderBottom: "0", verticalAlign: "top" }}
          align="right"
        >
          <ResumeSale
            product={partida}
          />
        </TableCell>
        
      </TableRow>
      <DiscountDialogPartida partida={partida} open={open} setOpen={setOpen} handleAcceptDiscount={handleAcceptDiscount} />
    </>

  )
}

export default PartidaVentaDirecta