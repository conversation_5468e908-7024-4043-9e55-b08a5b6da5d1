import React from 'react'
import {
  useMediaQuery,
  Box,
  Button,
  CircularProgress,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Paper,
  Divider,
  Grid
} from "@mui/material";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import TablaPartidas from './TablaPartidas';
import { calculatePriceClient, calculateImport, handleUtilidadTypeMoney } from '../utils/utils';
import sales from "../css/ventas/sales.module.css";
import { ComercialConditions } from "./componentesProducts/ComercialConditions";
import PersonIcon from "@mui/icons-material/Person";
import { useDispatch, useSelector } from "react-redux";
import BuscarCliente from './BuscarCliente';
import VentaDirectaModalConfirmacion from './VentaDirectaModalConfirmacion';
import CantidadesFinales from './CantidadesFinales';
import CommentsWrapper from '../componentesGenerales/CommentsWrapper';
import {
  setMsgVentas
} from "../../redux/pedidosDucks";
import { instanceAxios } from '../../redux/axiosInstance';
import { useCookies } from "react-cookie";
import { addNewCommentFunctionGeneral, deleteCommentGeneral, updateCommentGeneral, returnCommentTypeUrl, noComment } from "../utils/utils"
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@emotion/react';
import SearchIcon from '@mui/icons-material/Search';
import LocationCityIcon from '@mui/icons-material/LocationCity';
import EmailIcon from '@mui/icons-material/Email';
import LocationOnIcon from '@mui/icons-material/LocationOn';

const VentaDirecta = ({ directSale }) => {
  const dispatch = useDispatch()
  const navigate = useNavigate();
  const [cookies, setCookie] = useCookies();
  const csrf_access_token = cookies.csrf_access_token
  const user = useSelector((store) => {
    return store.usuario.user;
  });
  const isSmallScreen = useMediaQuery("(max-width:850px)");
  const enCotizacion = directSale ? directSale.directSaleInternalStatus.directSaleInternalStatus === "En cotización" : true
  const [partidas, setPartidas] = React.useState(directSale ? directSale.productsInDirectSale : []);
  const [selectedOptions, setSelectedOptions] = React.useState(directSale?.directSale_commercialTerms ? directSale.directSale_commercialTerms.map(item => item.commercialTerm) : []);
  const [selectedClient, setSelectedClient] = React.useState(directSale ? directSale.client : null);
  const [loading, setLoading] = React.useState(false)
  const [sendPrice, setSendPrice] = React.useState(directSale?.shipping ? parseInt(directSale.shipping) : 0);
  const commentType = 'direct_sale'
  const commentTypeUrlAdd = returnCommentTypeUrl(commentType, 'add')
  const commentTypeUrlDelete = returnCommentTypeUrl(commentType, 'delete')
  const commentTypeUrlUpdate = returnCommentTypeUrl(commentType, 'update')
  const [comments, setComments] = React.useState(directSale ? directSale.directSaleStackableComments : [])
  const [lastComment, setLastComment] = React.useState(comments.length > 0 ? comments[comments.length - 1] : noComment)
  const [isAddingComment, setIsAddingComment] = React.useState(false);
  const [openRedirect, setOpenRedirect] = React.useState(false);

  const addNewCommentFunction = (commentToAdd) => {
    return addNewCommentFunctionGeneral(directSale.directSaleId, commentToAdd, commentType, csrf_access_token, comments, setComments, setLastComment, commentTypeUrlAdd)
  }
  const deleteComment = (commentId) => {
    return deleteCommentGeneral(commentId, commentTypeUrlDelete, csrf_access_token, comments, setComments, setLastComment, lastComment)
  }
  const updateComment = (commentId, commentToUpdate) => {
    return updateCommentGeneral(commentId, commentToUpdate, csrf_access_token, commentTypeUrlUpdate, comments, setComments, setLastComment)
  }
  const getOrderCommentsWithDeletedFunction = async (idOrder, showDeletedComments = false) => {
    return getOrderCommentsWithDeleted(idOrder, showDeletedComments, setComments);
  };
  const handleValueQuantity = (value, idd) => {
    setPartidas((partidas) =>
      partidas.map((partida) => {
        if (partida.id === idd) {
          const updatedProduct = {
            ...partida,
            quantity: value,
            import: partida.priceClient * value,
          };
          return updatedProduct;
        }
        return partida;
      })
    );
  };

  const handleChangePrice = (e, product, productt, type) => {
    if (!e) return;
    const copyProducts = [...partidas];
    const index = copyProducts.findIndex((p) => p.id === productt.id);
    // copyProducts[index].selectProduct = e.target.value;
    // copyProducts[index].priceProv = e.target.value;
    copyProducts[index].selectProduct =
      type == "interno" ? product : product.supplierStore;
    copyProducts[index].priceProv = product.cost;
    setPartidas(copyProducts);
  };

  const handleUtilityChange = (value, id) => {
    // retorno el valor en pesos sin porcentajes
    setPartidas((partidas) =>
      partidas.map((partida) => {
        if (partida.id === id) {
          return {
            ...partida,
            utility: value,
            priceClient: calculatePriceClient(
              partida.priceProv,
              value,
              partida.typeMoney,
              partida
            ),
            import: calculateImport(
              partida.priceProv,
              value,
              partida.typeMoney,
              partida.quantity
            ),
          };
        }
        return partida;
      })
    );
  };


  const handleTypeMoney = (event, id) => {

    setPartidas(
      partidas.map((partida) => {
        let discountAmount = partida.discount.amount;
        let discountType = partida.discount.typeDiscount;

        let newPriceClient = calculatePriceClient(
          partida.priceProv,
          partida.utility,
          event.target.value,
          partida
        );

        if (partida.discount.amount > 0) {
          newPriceClient =
            newPriceClient -
            handleUtilidadTypeMoney(
              discountAmount,
              newPriceClient,
              discountType,
              partida.quantity
            );
        }

        // console.error(newPriceClient, "---NEWPRICECLIENT--");

        if (partida.id === id) {
          return {
            ...partida,
            discount: {
              ...partida.discount,
              refPrice: calculatePriceClient(
                partida.priceProv,
                partida.utility,
                event.target.value,
                partida
              ),
            },
            typeMoney: event.target.value,
            import: newPriceClient * partida.quantity,
            priceClient: newPriceClient,
          };
        }
        return partida;
      })
    );
  };

  const handleAcceptDiscount = (id, newPriceClient, discountAmount, discountType) => {
    setPartidas(
      partidas.map((partida) => {
        if (partida.id === id) {
          return {
            ...partida,
            priceClient: newPriceClient,
            import: newPriceClient * partida.quantity,
            discount: {
              ...partida.discount,
              refPrice: calculatePriceClient(
                partida.priceProv,
                partida.utility,
                partida.discount.typeDiscount,
                partida
              ),
              amount: discountAmount,
              typeDiscount: discountType
            },
          };
        }
        return partida;
      })
    );
  };
  const handleDelete = (id) => {
    setPartidas(partidas.filter(partida => partida.id !== id))
  }
  //******** */
  const addNewSellItem = (event) => {
    const idUnique = Date.now().toString() + Math.random().toString().substr(2, 5);
    const newItem = {
      id: idUnique,
      productSelected: {
        product: null,
        store: null
      },
      quantity: 1,
      utility: 0,
      priceClient: 0,
      discount: {
        typeDiscount: "$",
        amount: 0,
        totalBeforeDiscount: 0,
        refPrice: 0
      },
      import: 0,
      typeMoney: "$"
    }
    setPartidas([...partidas, newItem])
  }

  const updateDirectSale = async (data) => {
    const url = "/api/directSales/updateDirectSale";
    try {
      const data2 = await instanceAxios.put(url, data, {
        headers: {
          "x-csrf-token": csrf_access_token,
          "Content-Type": "application/json",
        },
      });
      setOpenRedirect(true)
    } catch (error) {
      const errorMessage = error.response?.data?.errores ? error.response?.data?.errores : "Error desconocido"
      dispatch(setMsgVentas(errorMessage))
    }
  }


  const newDirectSale = async (data) => {
    const url = "/api/directSales/recordDirectSale";
    try {
      const data2 = await instanceAxios.post(url, data, {
        headers: {
          "x-csrf-token": csrf_access_token,
          "Content-Type": "application/json",
        },
      });
      setOpenRedirect(true)
    } catch (error) {
      const errorMessage = error.response?.data?.errores ? error.response?.data?.errores : "Error desconocido"
      dispatch(setMsgVentas(errorMessage, "error"))
    }
  }


  const handleSubmit = async () => {
    if (!selectedClient) {
      dispatch(setMsgVentas("El cliente es obligatorio", "warning"))
      return;
    }
    if (selectedOptions.length === 0) {
      dispatch(setMsgVentas("Debes seleccionar al menos una condición comercial", "warning"))
      return;
    }
    if (!sendPrice) {
      dispatch(setMsgVentas("Costeo de envío obligatorio", "warning"))
      return;
    }
    if (partidas.length === 0) {
      dispatch(setMsgVentas("No hay ningún producto en la venta", "warning"))
      return;
    }
    if (partidas.find(partida => !partida.productSelected.cost)) {
      dispatch(setMsgVentas("Existe por lo menos una partida sin producto seleccionado", "warning"))
      return;
    }
    setLoading(true);
    const dataSend2 = {
      clientId: selectedClient.clientId,
      shipping: sendPrice,
      directSaleType: 1,
      commercialTerms: selectedOptions.map((option) => (
        option.id
      )),
      products: partidas.map((product) => {
        return {
          productAtStore: {
            internalSku: product.productSelected.product.internalSku,
            store: {
              type: product.productSelected.store.type,
              id: product.productSelected.store.id
            },
          },
          units: product.quantity,
          discount: {
            amount: product.discount.amount,
            type: product.discount.typeDiscount == "$" ? "nominal" : "percent",
          },
          utility: {
            amount: product.utility,
            type: product.typeMoney === "$" ? "nominal" : "percent",
          },

        };
      }),
      ...(directSale ? { direct_sale_id: directSale.directSaleId } : {}),
    };
    directSale ? await updateDirectSale(dataSend2) : await newDirectSale(dataSend2)
    setLoading(false);
  };


  const theme = useTheme();

  return (
    <>
      <div style={{ padding: " 0 1em", display: "flex", flexDirection: "column" }}>
       {/*  <div>
          <h5 className={sales.p}>Vendedor</h5>
          <div className={sales.divContainerUser}>
            <PersonIcon />
            <h4 className={sales.userP}>{user?.split(" ")[0]}</h4>
          </div>
        </div> */}

        <div className={sales.section} >
          
          <Card sx={{ width: '100%', boxShadow: 2, borderRadius: 2, overflow: 'visible' }}>

            <CardHeader 
              title="Datos del Comprador" 
              titleTypographyProps={{ variant: 'h6' }}
              avatar={<SearchIcon color="primary" />}
            />

            <Divider />
            
            <CardContent>
              {/* Buscador en la parte superior */}
              <Box sx={{ mb: 2 }}>
                <BuscarCliente 
                  selectedClient={selectedClient} 
                  setSelectedClient={setSelectedClient} 
                  enCotizacion={enCotizacion} 
                />
              </Box>
              
              {/* Información del cliente en la parte inferior */}
              {selectedClient && selectedClient !== "" && (
                <Box sx={{ mt: 3 }}>
                  <Divider sx={{ my: 2 }} />
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {selectedClient?.name?.toUpperCase() || "SIN NOMBRE"}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                        # {selectedClient?.clientId || ""}
                      </Typography>
                    </Box>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LocationCityIcon color="action" fontSize="small" />
                          <Box>
                            <Typography variant="caption" color="text.secondary">CIUDAD</Typography>
                            <Typography variant="body2">{selectedClient.city || "*"}</Typography>
                          </Box>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <EmailIcon color="action" fontSize="small" />
                          <Box>
                            <Typography variant="caption" color="text.secondary">CORREO</Typography>
                            <Typography variant="body2">{selectedClient.email || "*"}</Typography>
                          </Box>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LocationOnIcon color="action" fontSize="small" />
                          <Box>
                            <Typography variant="caption" color="text.secondary">DIRECCIÓN</Typography>
                            <Typography variant="body2">{selectedClient.address || "*"}</Typography>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </div>
        
        <div className={sales.section}>
          <div>
            <h5 className={sales.p}>Productos</h5>
            <br />
            <TablaPartidas
              partidas={partidas}
              setPartidas={setPartidas}
              handleValueQuantity={handleValueQuantity}
              handleChangePrice={handleChangePrice}
              handleUtilityChange={handleUtilityChange}
              handleTypeMoney={handleTypeMoney}
              handleAcceptDiscount={handleAcceptDiscount}
              handleDelete={handleDelete}
              enCotizacion={enCotizacion}
            />
            <Button
              style={{ padding: "0.2rem .4rem", margin: ".5rem 1rem" }}
              size="small"
              variant="contained"
              color='buttonGreenPink'
              disabled={!enCotizacion}
              endIcon={<AddCircleIcon />}
              onClick={addNewSellItem}
            >
              Agregar
            </Button>
          </div>
        </div>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-end",
            minWidth: "200px",
            maxWidth: isSmallScreen ? "100%" : "auto",
            margin: isSmallScreen ? "" : "",
          }}
        >
          <Box
            className={sales.resume}
            sx={{
              width: "100%",
              display: "flex",
              justifyContent: directSale ? "space-evenly" : "flex-end",
            }}
          >
            {directSale && (
              <CommentsWrapper
                comments={comments}
                deleteComment={deleteComment}
                updateComment={updateComment}
                addNewCommentFunction={addNewCommentFunction}
                isAddingComment={isAddingComment}
                setIsAddingComment={setIsAddingComment}
                commentType={commentType}
                getOrderCommentsWithDeletedFunction={getOrderCommentsWithDeletedFunction}
              />)}
            <Box sx={{ minWidth: "200px" , width: {
              xss: "100%",
              xs: "100%",
              md: "400px",
            }
            }}>
              <CantidadesFinales partidas={partidas} sendPrice={sendPrice} setSendPrice={setSendPrice} enCotizacion={enCotizacion} />

              {loading ?
                (
                  <Box
                    sx={{
                      padding: "1rem",
                      display: "flex",
                      justifyContent: "center",
                    }}
                  >
                    <CircularProgress />
                  </Box>
                ) :
                (enCotizacion ? (<Box className={sales.contentBtn}>
                  <Button
                    sx={{ width: "100%" }}
                    variant="outlined"
                    color="error"
                    onClick={() => navigate("/ventas/administrar")}
                  >
                    Cancelar
                  </Button>
                  <Button
                    sx={{ width: "100%" }}
                  color="buttonGreenPink"
                    variant="contained"
                    onClick={() => handleSubmit()}
                  >
                    Guardar
                  </Button>
                </Box>) :
                  <Button
                    sx={{ width: "100%" }}
                    variant="contained"
                    color="buttonGreenPink"
                    onClick={() => navigate("/ventas/administrar")}
                  >
                    Ir a ventas
                  </Button>
                )}
            </Box>
          </Box>
        </Box>
        <div className={sales.section}>
          <h5 className={sales.p}>Condiciones Comerciales</h5>
          <ComercialConditions setSelectedOptions={setSelectedOptions} selectedOptions={selectedOptions} enCotizacion={enCotizacion} />
        </div>
      </div>
      <VentaDirectaModalConfirmacion openRedirect={openRedirect} message={directSale ? "Revisar venta" : "Nueva venta"} />
    </>
  )
}

export default VentaDirecta
