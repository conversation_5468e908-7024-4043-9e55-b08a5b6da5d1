import React from 'react'
import {
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TextField
} from "@mui/material";
import sales from "../css/ventas/sales.module.css";
import { getPriceWithDiscountProduct } from '../utils/utils';
import { NumericFormat } from 'react-number-format';

const CantidadesFinales = ({ partidas, sendPrice, setSendPrice, enCotizacion }) => {

    const getTotals = () => {
        if (partidas.find(partida => !partida.productSelected.cost)) {
            return ['*', '*', '*']
        }
        const subtotalGeneral = partidas?.reduce(
            (total, partida) => total + getPriceWithDiscountProduct(partida),
            0
        );
        const ivaGeneral = partidas.map((partida) => {
            const value =
                getPriceWithDiscountProduct(partida) * 0.16;
            let iva = Math.floor(value * 100) / 100;
            return iva;
        })
            .reduce((acc, iva) => acc + iva, 0)
        const totalGeneral = subtotalGeneral +
            partidas.map((partida) => {
                const value =
                    getPriceWithDiscountProduct(partida) * 0.16;
                let iva = Math.floor(value * 100) / 100;
                return iva;
            }).reduce((acc, iva) => acc + iva, 0)
        return [subtotalGeneral, ivaGeneral, totalGeneral + sendPrice]
    }

    const [subtotalGeneral, ivaGeneral, totalGeneral] = getTotals()
    return (
        <TableContainer component={Paper}>
            <Table aria-label="simple table">
                <TableHead>
                    <TableRow>
                        <TableCell
                            // sx={{ width: "50px" }}
                            className={sales.widthCell}
                            align="center"
                        >
                            Resumen
                        </TableCell>
                        <TableCell className={sales.widthCell} align="center">
                            Cantidad
                        </TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    <TableRow>
                        <TableCell component="th" scope="row">
                            subtotal
                        </TableCell>
                        <TableCell component="th" scope="row">
                            {subtotalGeneral.toLocaleString("es-MX", {
                                style: "currency",
                                currency: "MXN",
                            })}
                        </TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell component="th" scope="row">
                            IVA
                        </TableCell>
                        <TableCell component="th" scope="row">
                            {ivaGeneral.toLocaleString("es-MX", {
                                style: "currency",
                                currency: "MXN",
                            })}
                        </TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell component="th" scope="row">
                            Envio
                        </TableCell>
                        <TableCell component="th" scope="row">
                            {/* <TextField
                                id="standard-basic"
                                type="number"
                                label="Envio"
                                variant="standard"
                                disabled={!enCotizacion}
                                value={sendPrice}
                                InputProps={{
                                    inputProps: {
                                        style: { textAlign: "left" },
                                        min: 0,
                                        step: 1,
                                    },
                                }}
                                onChange={(e) => setSendPrice(parseInt(e.target.value))}
                            /> */}
                            <NumericFormat
                                id="standard-basic"
                                type="text"
                                label="Envio"
                                variant="standard"
                                disabled={!enCotizacion}
                                value={sendPrice}
                                customInput={TextField}
                                InputProps={{
                                    inputProps: {
                                        style: { textAlign: "left" },
                                        min: 0,
                                        step: 1,
                                    },
                                }}
                                onValueChange={(values) => {
                                    const { formattedValue, value } = values;
                                    setSendPrice(parseInt(value))
                                }}
                                thousandSeparator={true}
                                decimalSeparator="."
                                allowNegative={false}
                                isNumericString={true}
                                prefix="$"
                                decimalScale={2}
                                fixedDecimalScale={true}
                                allowLeadingZeros={false}
                                allowEmptyFormatting={true}
                            />
                        </TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell component="th" scope="row">
                            Total
                        </TableCell>
                        <TableCell component="th" scope="row">
                            {totalGeneral.toLocaleString("es-MX", {
                                style: "currency",
                                currency: "MXN",
                            })}
                        </TableCell>
                    </TableRow>
                </TableBody>
            </Table>

        </TableContainer>
    )
}

export default CantidadesFinales