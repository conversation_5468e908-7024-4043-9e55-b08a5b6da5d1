import {
  Box,
  TextField,
  FormControlLabel,
  Paper,
  Radio,
  RadioGroup,
} from "@mui/material";
import { NumericFormat } from "react-number-format";

export const PriceCustomer = ({calculatePriceClient,handleClickOpen, product}) => {
  return (
    <Box
      sx={{
        margin: "1em auto 0 auto",
        width: "min-content",
      }}
    >
      {/* <TextField
        label="Precio"
        sx={{ width: "150px" }}
        disabled={true}
        type="number"
        variant="outlined"
        // var exp = Math.pow(10, dec || 2); // 2 decimales por defecto
        // return parseInt(num * exp, 10) / exp
        value={calculatePriceClient(
          product.priceProv,
          product.utility,
          product.typeMoney,
          product
        )}
        inputProps={{
          min: 0,
          max: 100,
          step: 1,
        }}
        InputProps={{
          startAdornment: <span>$</span>,
          inputProps: { style: { textAlign: "right" } },
        }}
      /> */}
      <NumericFormat
        label="Precio"
        sx={{ width: "150px" }}
        disabled={true}
        variant="outlined"
        value={calculatePriceClient(
          product.priceProv,
          product.utility,
          product.typeMoney,
          product
        )}
        customInput={TextField}
        thousandSeparator
        decimalScale={2}
        fixedDecimalScale={true}
        decimalSeparator="."
        allowNegative={false}
        prefix="$"
        allowLeadingZeros={false}
        allowEmptyFormatting={true}
        />
      <Box>
        <Button
        // disabled={
        //   Object.keys(product.productSelected)
        //     .length != 0
        //     ? false
        //     : true
        // }
        >
          <AddCircleOutlineIcon />
          <InputLabel
            id="demo-simple-select-label"
            onClick={() => handleClickOpen(product.id)}
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            Descuento
            {product?.discount?.amount > 0 ? (
              <p className={sales.pText}>
                ({product.discount.amount}
                {product.discount.typeDiscount})
              </p>
            ) : (
              <p className={sales.pText}>(0)</p>
            )}
          </InputLabel>
        </Button>
      </Box>
    </Box>
  );
};
