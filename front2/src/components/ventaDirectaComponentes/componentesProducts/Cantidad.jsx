import React from "react";
import { Box, TextField } from "@mui/material";
import { NumericFormat } from "react-number-format";

export const Cantidad = ({ product, handleValueQuantity, isResponsive, enCotizacion }) => {
  return (
    <Box>
      <NumericFormat
        label="Cantidad"
        type="text"
        disabled={!enCotizacion}
        customInput={TextField}
        sx={{
          position: "relative",
          top: isResponsive ? "auto" : "11px",
          width: "150px"
        }}
        value={product.quantity}
        onValueChange={(values) => {
          const { formattedValue, value } = values;
          handleValueQuantity(value, product.id);
        }}
        thousandSeparator
        decimalScale={0}
        allowNegative={false}
        decimalSeparator="."
        fixedDecimalScale={true}
        prefix=""
        isNumericString={true}
        allowLeadingZeros={false}
        allowEmptyFormatting={true}
        InputProps={{
          inputProps: {
            style: { textAlign: "left" },
            min: 1,
            max: 100,
            step: 1,
          },
        }}
        />
    </Box>
  );
};
