import React from 'react'
import { instanceAxios } from '../../../redux/axiosInstance'
import { Box, ListSubheader, MenuItem, Select, useMediaQuery } from "@mui/material";
import { CircularProgress } from "@mui/material";
import sales from "../../css/ventas/sales.module.css";
import { truncateToTwoDecimalsString, truncateToTwoDecimals, isEmpty,sleep } from "../../utils/utils";

export const PriceProvedor = ({ product, setProducts, enCotizacion, isAccordion = false  }) => {
  const [pricesList, setPricesList] = React.useState(null)
  const [loading, setLoading] = React.useState(false)
  const pricesListSelect = () => {
    const list = [
      <ListSubheader>Interno</ListSubheader>,
      ...pricesList.productInInternalStore.map(
        (priceProduct) => (
          <MenuItem key={`interno-${priceProduct.store.id}`} value={`interno-${priceProduct.store.id}`}>
            {truncateToTwoDecimalsString(priceProduct.cost)}
          </MenuItem>
          
        )
      ),
      <ListSubheader>Externo</ListSubheader>,
      ...pricesList.productInSupplierStore.map(
        (priceProduct) => {
          return (
            <MenuItem key={`externo-${priceProduct.supplierStore.id}`} value={`externo-${priceProduct.supplierStore.id}`}>
              {truncateToTwoDecimals(priceProduct.cost).toLocaleString(
                "es-MX",
                {
                  style: "currency",
                  currency: "MXN",
                }
              )}
            </MenuItem>
          );
        }
      )
    ]
    return list
  }


  const getStoreById = (selected, priceListArg) => {
    const selectedSplited= selected.split('-')
    const storeType = selectedSplited[0]
    const id = selectedSplited[1]
    if(storeType === 'interno'){
      const productInInternalStore = priceListArg.productInInternalStore.find((product) => product.store.id === parseInt(id))
      return [productInInternalStore.stock, productInInternalStore.cost, {...productInInternalStore.store, type: 'store'}]
    }else{
      const productInSupplierStore = priceListArg.productInSupplierStore.find((product) => product.supplierStore.id === parseInt(id))
      return [productInSupplierStore.stock, productInSupplierStore.cost, {...productInSupplierStore.supplierStore, type: 'supplier_store'}]
    }
  }

  const setCostStockStore = (id, priceListArg) => {
    setProducts((partidas) =>
      partidas.map((partida) => {
          const [stock, cost, store] = getStoreById(id, priceListArg)
          if (partida.id === product.id) {
              return {
                  ...partida,
                  productSelected: {
                      ...partida.productSelected,
                      store: store,
                      cost: cost,
                      stock: stock
                  }
              }
          }
          return partida
      })
  );
  }
  const check = (event, value) => {
    setCostStockStore(event.target.value, pricesList)
  }

  React.useEffect(() => {
    const getPricesProduct = async () => {
      setLoading(true)
      if (!isEmpty(product.productSelected) && product.productSelected.product !== null) {
        const url = `api/directSales/productToSell/${product.productSelected.product.internalSku}`;  
        const res = await instanceAxios.get(url);
        if(product.productSelected.store && !isEmpty(product.productSelected.store) && !product.productSelected.cost){
          const typeStore  = product.productSelected.store.type === 'store' ? 'interno' : 'externo'
          setCostStockStore(`${typeStore}-${product.productSelected.store.id}`, res.data)
        }
        setPricesList(res.data)
      }
    setLoading(false)
    };
    setPricesList(null)    
    getPricesProduct()
  }, [product.productSelected.product]);


  const isMediumScreen = useMediaQuery("(max-width:1413px)");

  
  return (
    <Box
      sx={{
        marginTop: isAccordion ? ".7em" : isMediumScreen ? "-.6rem" : "-1.6rem",
        minWidth: "150px",
        position: isAccordion ? "block" : "relative",
        top: "39px"
      }}
      className={sales?.space}
    >
      
      <Select
        value={loading ? 'circularProgress' : (pricesList && product.productSelected.store !=='') ? `${product.productSelected.store.type === 'store' ? 'interno' : 'externo'}-${product.productSelected.store.id}` : ''}
        id="grouped-select"
        label="Proveedor"
        disabled={!enCotizacion}
        sx={{ minWidth: "150px" }}
        onChange={check}
      >
        {loading ?
          (
            <MenuItem value='circularProgress'>
              <CircularProgress size={18} />
            </MenuItem>
          ) :
          (pricesList ?
            (
              pricesListSelect()
            ) :
            (null))}
      </Select>
    </Box>
  )
}