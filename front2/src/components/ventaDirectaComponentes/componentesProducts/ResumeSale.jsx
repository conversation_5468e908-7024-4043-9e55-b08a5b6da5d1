import { useMediaQ<PERSON>y, Box, Divider, Typography } from "@mui/material";
import { handleUtilidadTypeMoney } from "../../utils/utils";
import { truncateToTwoDecimalsString, getPriceWithDiscountProduct, getIvaProduct, getFinalPrice } from "../../utils/utils";
import { useTheme } from "@emotion/react";

export const ResumeSale = ({
  product,
  isResponsive
}) => {
  const styles = {
    marginTop: isResponsive ? "0" : "0  ",
    display: isResponsive ? "flex" : undefined,
    gap: isResponsive ? "1em" : undefined,
    flexDirection: isResponsive ? "column" : undefined,
  };
  const theme = useTheme();
  console.log('00000000000000000000000000000000088888888888888888888888888888888888888888888888888888888888888')
  console.log(product.productSelected.cost)
  console.log(isNaN(product.productSelected.cost))
  console.log('00000000000000000000000000000000088888888888888888888888888888888888888888888888888888888888888')
  if (isNaN(product.productSelected.cost)) {
    return (
      <div>
        *
      </div>
    )
  } else {
    return (
      <Box sx={{ ...styles, bgcolor: 'background.paper', borderRadius: 2, p: 2, boxShadow: 1,  color: theme.palette.text.primary, 
        minWidth: "210px",
      }}
      >
        <Typography
          variant="body2"
          sx={{ color: "text.primary", textAlign: "right", mb: 0.5 }}
        >
          {/* Precio unitario */}
          {Number(product.productSelected.cost).toLocaleString("es-MX", {
            style: "currency",
            currency: "MXN",
          })}{" "}
          x {product.quantity} ={" "}
          {/* Subtotal con utilidad */}
          <span style={{ fontWeight: 500 }}>
            {(
              Number(product.productSelected.cost) * product.quantity +
              handleUtilidadTypeMoney(
                product.utility,
                product.productSelected.cost,
                product.typeMoney,
                product.quantity
              )
            ).toLocaleString("es-MX", {
              style: "currency",
              currency: "MXN",
            })}
          </span>
        </Typography>

        <Typography
          variant="body2"
          sx={{ color: product?.discount?.amount > 0 ? "error.main" : "text.secondary", textAlign: "right", mb: 0.5 }}
        >
          {product?.discount?.amount > 0
            ? product.discount.typeDiscount == "$"
              ? "$" + product.discount.amount
              : product.discount.amount + "%"
            : 0}{" "}
          ={" "}
          <span style={{ fontWeight: 500 }}>
            {getPriceWithDiscountProduct(product).toLocaleString("es-MX", {
              style: "currency",
              currency: "MXN",
            })}
          </span>
        </Typography>

        <Typography
          variant="body2"
          sx={{  textAlign: "right", mb: 0.5 }}
        >

          +{" "}
          <span style={{ fontWeight: 500 }}>
            {getIvaProduct(product).toLocaleString("es-MX", {
              style: "currency",
              currency: "MXN",
            })}
          </span>
        </Typography>

        <Divider light sx={{ my: 1 }} />

        <Box
          sx={{
            bgcolor: "success.lighter",
            border: "1px solid",
            borderColor: "success.light",
            borderRadius: 1,
            p: 1,
            textAlign: "end"
          }}
        >
          <Typography variant="subtitle1" sx={{ color: "success.dark", fontWeight: 700 }}>
            Total:{" "}
            {getFinalPrice(product).toLocaleString("es-MX", {
              style: "currency",
              currency: "MXN",
            })}
          </Typography>
        </Box>
      </Box>
    )
  }

};
