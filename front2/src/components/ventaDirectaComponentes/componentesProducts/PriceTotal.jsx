import { Box, TextField, Input<PERSON>abel, Button, Tooltip } from "@mui/material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { calculatePriceClient } from "../../utils/utils";
import { NumericFormat } from "react-number-format";

export const PriceTotal = ({
  product,
  handleClickOpen,
  sales,
  isResponsive,
  enCotizacion,
  isAccordion = false,
}) => {
  return (
    <Box
      sx={{
        margin: isAccordion ? "" : "1em auto 0 auto",
        width: "min-content",
        minWidth: "100px",
      }}
    >
      <NumericFormat
        value={
          product.productSelected.cost
            ? calculatePriceClient(
              product.productSelected.cost,
              product.utility,
              product.typeMoney,
              product
            )
            : "*"
        }
        customInput={TextField}
        label="Precio"
        sx={{ width: "150px" }}
        disabled={true}
        thousandSeparator
        decimalScale={2}
        fixedDecimalScale
        allowNegative={false}
        prefix="$"
        InputProps={{
          inputProps: { style: { textAlign: "right" } },
        }}
      />
      <Box>
        <Tooltip title="Agregar descuento">
          <Button disabled={!enCotizacion} className="buttonGreenPink"
            onClick={() => handleClickOpen(true)}>
            <AddCircleOutlineIcon color="buttonGreenPink" />
            <InputLabel
              id="demo-simple-select-label"
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                fontSize: ".8rem",
              }}
            >
              Dsc
              {product?.discount?.amount > 0 ? (
                <p className={sales.pText}>
                  ({product.discount.amount}
                  {product.discount.typeDiscount})
                </p>
              ) : (
                <p className={sales.pText}>(0)</p>
              )}
            </InputLabel>
          </Button>
        </Tooltip>
      </Box>
    </Box>
  );
};
