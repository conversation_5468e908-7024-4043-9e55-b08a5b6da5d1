import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Autocomplete, Checkbox, TextField, Tooltip, Chip } from '@mui/material';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import { getTaxes } from '../../../redux/ventaDirecta';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

export const ComercialConditions = ({ setSelectedOptions, selectedOptions, enCotizacion }) => {
    const dispatch = useDispatch();

    const { commercialTerms = [] } = useSelector((state) => state.ventaDirecta.taxes || {});

    const handleChange = (event, value) => {
        setSelectedOptions(value);
    };

    React.useEffect(() => {
        dispatch(getTaxes());
    }, [dispatch]);


    return (
        <Autocomplete
        multiple
        id="checkboxes-tags-demo"
        options={commercialTerms}
        disableCloseOnSelect
        disabled={!enCotizacion}
        value={selectedOptions}
        getOptionLabel={(option) => option.commercialTerm}
        isOptionEqualToValue={(option, value) => option.id === value.id} // Verifica que los ids coincidan
        sx={{ marginLeft: '1rem', marginTop: '1rem', marginBottom:"2em", minWidth: '200px', width: 'inherit',
          maxWidth: '300px'
         }}
        renderOption={(props, option, { selected }) => {
          const { key, ...optionProps } = props;
          return (
            <Tooltip title={option.commercialTerm} arrow>
              <li key={key} {...optionProps} style={{ display: 'flex', alignItems: 'center' }}>
                <Checkbox
                  icon={icon}
                  checkedIcon={checkedIcon}
                  style={{ marginRight: 8 }}
                  checked={selected}
                />
                <span style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  maxWidth: '200px'
                }}>
                  {option.commercialTerm}
                </span>
              </li>
            </Tooltip>
          );
        }}
        renderTags={(tagValue, getTagProps) =>
          tagValue.map((option, index) => (
            <Tooltip key={index} title={option.commercialTerm} arrow>
              <Chip
                label={option.commercialTerm}
                {...getTagProps({ index })}
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  maxWidth: '200px'
                }}
              />
            </Tooltip>
          ))
        }
        renderInput={(params) => (
          <TextField {...params} label="Condiciones Comerciales" placeholder="Condicion" />
        )}
        onChange={handleChange}
      />
    );
};

export default ComercialConditions;



// import * as React from 'react';
// import Checkbox from '@mui/material/Checkbox';
// import TextField from '@mui/material/TextField';
// import Autocomplete from '@mui/material/Autocomplete';
// import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
// import CheckBoxIcon from '@mui/icons-material/CheckBox';
// import { useDispatch, useSelector } from 'react-redux';
// import { getTaxes } from '../../../redux/ventaDirecta';

// const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
// const checkedIcon = <CheckBoxIcon fontSize="small" />;

// export const ComercialConditions = ({ setSelectedOptions, selectedOptions }) => {
//     const dispatch = useDispatch();

//     const { commercialTerms = {} } = useSelector((state) => state.ventaDirecta.taxes || {});

//     const handleChange = (event, value) => {
//         setSelectedOptions(value);
//     };

//     React.useEffect(() => {
//         dispatch(getTaxes())
//     }
//         , []);

//     return (
//         <Autocomplete
//             multiple
//             id="checkboxes-tags-demo"
//             options={commercialTerms}
//             disableCloseOnSelect
//             value={selectedOptions}
//             getOptionLabel={(option) => option.commercialTerm}
//             sx={{ marginLeft: "1rem", marginTop: "1rem", minWidth: "300px", width: "min-content"}}
//             renderOption={(props, option, { selected }) => {
//                 const { key, ...optionProps } = props;
//                 return (
//                     <li key={key} {...optionProps}>
//                         <Checkbox
//                             icon={icon}
//                             checkedIcon={checkedIcon}
//                             style={{ marginRight: 8 }}
//                             checked={selected}
//                         />
//                         {option.commercialTerm}
//                     </li>
//                 );
//             }}
//             renderInput={(params) => (
//                 <TextField {...params} label="Condiciones Comerciales" placeholder="Condicion" />
//             )}
//             onChange={handleChange} // Agregamos el evento onChange
//         />
//     );
// };

