import React from "react";
import {
  Box,
  TextField,
  FormControlLabel,
  Paper,
  Radio,
  RadioGroup,
} from "@mui/material";
import { NumericFormat } from "react-number-format";

export const Utility = ({
  product,
  handleUtilityChange,
  handleTypeMoney,
  isResponsive,
  sales,
  enCotizacion,
  isAccordion = false,
}) => {
  return (
    <Box
      sx={{
        marginTop: isAccordion ? "0" : "1em",
        maxWidth: isAccordion ? "100px" : "150px",
        minWidth: "150px",
      }}
      className={sales?.margin}
    >
      {/* <TextField
        label="Utilidad"
        type="number"
        variant="outlined"
        value={product.utility}
        disabled={!enCotizacion}
        onChange={(e) => handleUtilityChange(e, product.id)}
        InputProps={{
          startAdornment: <span>{product.typeMoney == "$" ? "$" : "%"}</span>,
          inputProps: {
            style: { textAlign: "right" },
            min: 1,
            step: 1,
          },
        }}
      /> */}
      <NumericFormat
        value={product.utility}
        disabled={!enCotizacion}
        onValueChange={(values) => {
          const { value } = values; // `value` es el valor sin formato
          handleUtilityChange(value, product.id);
        }}
        customInput={TextField}
        label="Utilidad"
        variant="outlined"
        thousandSeparator
        decimalScale={2}
        allowNegative={false}
        prefix={product.typeMoney === "$" ? "$" : ""}
        suffix={product.typeMoney === "%" ? "%" : ""}
        InputProps={{
          inputProps: {
            style: { textAlign: "right" },
            min: 1,
            step: 1,
          },
        }}
        sx={{ width: "100%" }}
      />
      <RadioGroup
        aria-labelledby="demo-radio-buttons-group-label"
        value={product.typeMoney}
        onChange={(event) => handleTypeMoney(event, product.id)}
        name="radio-buttons-group"
        className="buttonGreenPink"
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-evenly",
          padding: "10px 4px",
        }}
      >
        <FormControlLabel
          value="$"
          disabled={!enCotizacion}
          control={
            <Radio 
              sx={{ 
                padding: "0",
                '&.Mui-checked': {
                  color: theme => theme.palette.primary.main,
                }
              }} 
            />
          }
          label="$"
          sx={{ margin: "0" }}
        />
        <FormControlLabel
          value="%"
          disabled={!enCotizacion}
          control={
            <Radio 
              sx={{ 
                padding: "0",
                '&.Mui-checked': {
                  color: theme => theme.palette.primary.main,
                }
              }} 
            />
          }
          label="%"
          sx={{ margin: "0" }}
        />
      </RadioGroup>
    </Box>
  );
};
