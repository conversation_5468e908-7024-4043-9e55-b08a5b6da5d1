import React from 'react'
import sales from "../css/ventas/sales.module.css";
import { Tooltip } from "@mui/material";
import { isEmpty } from '../utils/utils';
import Box from '@mui/material/Box';


const InfoSelectedProduct = ({ selectedProductProduct, isAccordion }) => {

    if (!selectedProductProduct || isEmpty(selectedProductProduct)) {
        return null
    }

    return (
        <div style={{
            display: "flex", padding: isAccordion ? "0px" : "1rem 0"
            , 
            margin:"auto", maxWidth:"200px",
            overflow:"auto",
            // , flexWrap: "wrap",
        }}>
            <div>
                <img
                    src={selectedProductProduct?.productImage ? selectedProductProduct?.productImage : "/notImage.png"}
                    width={selectedProductProduct?.productImage ? 50 : 50}
                    height={selectedProductProduct?.productImage ? 50 : 50}
                    style={{
                        margin:"auto"
                    }}
                    alt="Imagen del producto"
                />
            </div>
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    marginLeft: '1rem',
                    textAlign: 'left',
                }}
            >
                <Box sx={{ display: 'flex', gap: 1 }}>
                    <strong>Marca:</strong>
                    <span>{selectedProductProduct.productBase.brand}</span>
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                    <strong>Modelo:</strong>
                    <Tooltip title={selectedProductProduct.productBase.model}>
                        <span>
                            {selectedProductProduct.productBase.model.length > 10
                                ? selectedProductProduct.productBase.model.substring(0, 10) + '...'
                                : selectedProductProduct.productBase.model}
                        </span>
                    </Tooltip>
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                    <strong>Descripción:</strong>
                    <Tooltip title={selectedProductProduct.productDescription}>
                        <span>
                            {selectedProductProduct.productDescription.length > 10
                                ? selectedProductProduct.productDescription.substring(0, 10) + '...'
                                : selectedProductProduct.productDescription}
                        </span>
                    </Tooltip>
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                    <strong>internalSku:</strong>
                    <Tooltip title={selectedProductProduct.internalSku}>
                        <span>
                            {selectedProductProduct.internalSku.length > 10
                                ? selectedProductProduct.internalSku.substring(0, 10) + '...'
                                : selectedProductProduct.internalSku}
                        </span>
                    </Tooltip>
                </Box>
            </Box>

        </div>
    )
}

export default InfoSelectedProduct