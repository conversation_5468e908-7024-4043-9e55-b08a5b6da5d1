import React from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
  IconButton
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import DeleteIcon from '@mui/icons-material/Delete';
import { BuscarProductoComponenteExterno } from './BuscarProductoComponente';
import { Cantidad } from './componentesProducts/Cantidad';

import { PriceProvedor } from './componentesProducts/PriceProvedor';
import { Utility } from './componentesProducts/Utility';
import { PriceTotal } from './componentesProducts/PriceTotal';
import { ResumeSale } from './componentesProducts/ResumeSale';
import DiscountDialogPartida from './DiscountDialogPartida';

const AccordionViewPartida = ({
  partida,
  setPartidas,
  handleValueQuantity,
  handleChangePrice,
  handleUtilityChange,
  handleTypeMoney,
  handleAcceptDiscount,
  handleDelete,
  enCotizacion,
}) => {
  const [open, setOpen] = React.useState(false);

  console.log('AccordionViewPartida', partida, enCotizacion, setPartidas);

  return (
    <>
      <Accordion sx={{ marginBottom: 1 }}>

        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel-content"
          id={`accordion-${partida.id}`}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
            <IconButton
              onClick={() => handleDelete(partida.id)}
              disabled={!enCotizacion}
            >
              <DeleteIcon color="error" />
            </IconButton>
            <Typography sx={{ flexGrow: 1 }}>
              {partida?.productSelected?.product?.productDescription || 'Producto'}
            </Typography>
          </Box>
        </AccordionSummary>

        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 , margin:"autos"}}>

            <BuscarProductoComponenteExterno
              product={partida}
              setProducts={setPartidas}
              enCotizacion={enCotizacion}
              isAccordion={true}
            />

            {/* Cantidad + PriceProvedor */}
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', margin:"auto" }}>
              <Cantidad
                product={partida}
                handleValueQuantity={handleValueQuantity}
                enCotizacion={enCotizacion}
                isAccordion={true}
              />
              <PriceProvedor
                product={partida}
                setProducts={setPartidas}
                handleChangePrice={handleChangePrice}
                enCotizacion={enCotizacion}
                isAccordion={true}
              />
            </Box>

            {/* Utility + PriceTotal */}
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', margin:"auto" }}>
              <Utility
                product={partida}
                handleUtilityChange={handleUtilityChange}
                handleTypeMoney={handleTypeMoney}
                sales={{}}
                enCotizacion={enCotizacion}
                isAccordion={true}
              />
              <PriceTotal
                product={partida}
                handleClickOpen={setOpen}
                sales={{}}
                enCotizacion={enCotizacion}
                isAccordion={true}
              />
            </Box>

            <ResumeSale product={partida} />
          </Box>

        </AccordionDetails>
      </Accordion>

      <DiscountDialogPartida
        partida={partida}
        open={open}
        setOpen={setOpen}
        handleAcceptDiscount={handleAcceptDiscount}
      />
    </>
  );
};

export default AccordionViewPartida;
