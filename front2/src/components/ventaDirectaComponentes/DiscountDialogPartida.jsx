import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControlLabel,
  FormLabel,
  Radio,
  RadioGroup,
  TextField,
  Slide
} from "@mui/material";
import sales from "../css/ventas/sales.module.css";
import { calculatePriceClient, handleUtilidadTypeMoney } from '../utils/utils';
import { NumericFormat } from 'react-number-format';



const DiscountDialogPartida = ({ partida, open, setOpen, handleAcceptDiscount }) => {

  const [discountType, setDiscountType] = React.useState(partida.discount.typeDiscount)
  const [discountAmount, setDiscountAmount] = React.useState(partida.discount.amount)
  
  const handleClose = () => {
    setOpen(false)
  }

  const changeDiscountType = (e) => {
    console.log('-------------->>>')
    console.log(e.target.value)
    setDiscountType(e.target.value)
    console.log('-------------->>>')
  }

  const changeDiscountAmount = (e) => {
    console.log('-------------->>>')
    console.log(e.target.value)
    setDiscountAmount(e.target.value)
    console.log('-------------->>>')
  }

  const handleSubmitDiscount = () => {
    ////////////////////////////////////////////////////////////////////Estados
    // obtenemos el precio  con descuento
    const oldPriceClient = calculatePriceClient(
      partida.productSelected.cost,
      partida.utility,
      partida.typeMoney,
      partida
    )
    //console.log('------------------------')
    //console.log(oldPriceClient)
    //console.log('------------------------')
    const newPriceClient =
      oldPriceClient -
      handleUtilidadTypeMoney(
        discountAmount,
        oldPriceClient,
        discountType,
        partida.quantity
      );
      //console.log('------------------------')
      //console.log(oldPriceClient)
      //console.log('------------------------')
      //console.log('****************************')
      //console.log(partida.id)
      //console.log(newPriceClient)
      //console.log('****************************')
      handleAcceptDiscount(partida.id, newPriceClient, discountAmount, discountType)

    setOpen(false);
  }
  /*const Transition = React.forwardRef(function Transition(props, ref) {
    console.log('-----------------------------||||||||||||')
    console.log(props)
    console.log('-----------------------------||||||||||||||')
    return <Slide direction="up" ref={ref} {...props} />;
  });
        TransitionComponent={Transition}
  */


  return (
    <Dialog
      open={open}
      keepMounted
      onClose={handleClose}
      aria-describedby="alert-dialog-slide-description"
    >
      <DialogTitle>
        <p>
          Añade un Descuento{" "}
          <p style={{ fontSize: ".8rem" }} className={sales.pText}>
            {partida.productSelected.model}
          </p>
        </p>
      </DialogTitle>
      <DialogContent>
        {partida.discount.amount > 0 ? (
          <p>Ya hay un descuento aplicado</p>
        ) : null}
        <DialogContentText id="alert-dialog-slide-description">
          {/* <FormLabel id="demo-error-radios">Descuento</FormLabel> */}
          {/* <TextField
            label="Utilidad"
            type="number"
            variant="outlined"
            value={discountAmount}
            onChange={changeDiscountAmount}
            InputProps={{
              startAdornment: (
                <span>
                  {partida.discount.typeDiscount == "$" ? "$" : "%"}
                </span>
              ),
              inputProps: {
                style: { textAlign: "right" },
                min: 0,
                step: 1,
              },
            }}
          /> */}
          <NumericFormat
            label="Descuento"
            variant="outlined"
            value={discountAmount}
            onValueChange={(values) => {
              const { value } = values;
              setDiscountAmount(value);
            }}
            InputProps={{
              startAdornment: (
                <span>
                  {discountType === "$" ? "$" : "%"}
                </span>
              ),
              inputProps: {
                style: { textAlign: "right" },
                min: 0,
                step: 1,
              },
            }}
            customInput={TextField}
            thousandSeparator
            decimalScale={2}
            fixedDecimalScale={true}
            decimalSeparator="."
            allowNegative={false}
            prefix={discountType === "$" ? "$" : ""}
            suffix={discountType === "%" ? "%" : ""}
            allowLeadingZeros={false}
            allowEmptyFormatting={true}
            isNumericString={true}
            fullWidth
            sx={{ mb: 2 }}
          />
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            defaultValue="female"
            value={discountType} // Asigna el estado como valor del grupo de radio
            onChange={changeDiscountType}
            name="radio-buttons-group"
            sx={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "center",
            }}
          >
            <FormControlLabel
              value="$"
              control={<Radio />}
              label="$"
            // sx={{margin: "0"}}
            />
            <FormControlLabel
              value="%"
              control={<Radio />}
              label="%"
              sx={{ margin: "0" }}
            />
          </RadioGroup>
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button color="error" onClick={handleClose}>
          Cancelar
        </Button>
        {/* <Button color="primary"> */}
        <Button
        color='buttonGreenPink'
          onClick={() => handleSubmitDiscount()}
        >
          Aceptar
        </Button>
      </DialogActions>
    </Dialog>
  )
};

export default DiscountDialogPartida
