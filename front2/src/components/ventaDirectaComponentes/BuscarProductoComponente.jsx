import React from 'react'
import InfoSelectedProduct from './InfoSelectedProduct'
import BuscarProductoRe from '../productosComponentes/BuscarProductoRe'
export const BuscarProductoComponenteExterno = ({
    product,
    setProducts,
    enCotizacion,
    isAccordion = false
}) => {
    return (
        <div
            style={{
                display: "flex",
                flexDirection: isAccordion ? "row" : "column",
                padding: isAccordion ? "0px" : ".4rem 0",
                width: isAccordion ? "100%" : "200px",
                minWidth: isAccordion ? "100%" : "200px",
                flexWrap: "wrap",
            }}
        >
            <BuscarProductoRe product={product} setProducts={setProducts} disabled={!enCotizacion}/>
            {product.productSelected ? <InfoSelectedProduct selectedProductProduct={product.productSelected.product}  isAccordion={isAccordion}/> : null}
        </div>
    )
}
