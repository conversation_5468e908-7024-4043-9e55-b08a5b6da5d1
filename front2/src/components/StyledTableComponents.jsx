import React from "react";
import { styled } from "@mui/material/styles";
import { tableCellClasses } from "@mui/material/TableCell";
import { TableRow, TableCell } from "@mui/material";

export const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
      // backgroundColor: theme.palette.common.black,
      // color: theme.palette.common.
      // fontFamily: "Filson Pro",
      font: "500 16px 'Filson Pro'",
      textAling: "left !important",
    },
    [`&.${tableCellClasses.body}`]: {
      fontSize: 14,
      padding: "5px 16px 5px 16px",
      borderBottom: '0 !important',

    },
  }));
  
export const StyledTableRow = styled(TableRow)(({ theme }) => ({
 
    "&:nth-of-type(4n+2)": {
      backgroundColor: theme.palette.mode === "light" ? theme.palette.primary.backgroundPink : theme.palette.primary.greenMainTable,
    },
    "&:nth-of-type(4n)": {
      backgroundColor: theme.palette.mode === "light" ? theme.palette.primary.backgroundPink : theme.palette.primary.greenMainTable,
    },
    cursor: "pointer",
  
    // hide last border
    "&:last-child td, &:last-child th": {
      border: 0,
    },
  }));