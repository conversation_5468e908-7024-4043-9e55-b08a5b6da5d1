import {
  <PERSON><PERSON>ield,
  IconButton,
  InputAdornment,
  FormControl,
  InputLabel,
  createTheme,
  ThemeProvider,
  Box,
  Skeleton,
  Button,
} from "@mui/material";
import SendIcon from "@mui/icons-material/Send";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useCookies } from "react-cookie";
import { ContainerDefaultMessage } from "./ContainerDefaultMessage";
import { getDefaultMessages, newDefaultMessage, editDefaultMessage } from "../../redux/mensajesDucks";
import AddIcon from '@mui/icons-material/Add';



const DefaultMessages = ({ defaultMessage, notEdit, setShowDefaultMessages, setSelectedMessageFromMessages, filterText, setFilterText, forceShowCreationFields = false }) => {


  const dispatch = useDispatch();

  const [cookies, setCookie] = useCookies();


  const [message, setMessage] = React.useState({
    name: "",
    message: "",
  });

  const [messageEdit, setMessageEdit] = React.useState({
    id: "",
    name: "",
    message: "",
    edit: false,
  });

  const messagesDefault = useSelector(
    (state) => state.mensajes.messagesDefault
  );

  const loading = useSelector((state) => state.mensajes.loading);
  const orderMessages = useSelector((store) => store.mensajes.orderMessages);

  // Nuevo estado para controlar la visibilidad de los campos de nuevo mensaje
  const [showNewMessageFields, setShowNewMessageFields] = React.useState(false);

  useEffect(() => {
    if (messagesDefault.length === 0 && orderMessages.length > 0) {

      dispatch(getDefaultMessages());
    }
  }, [dispatch]);


  const handleSend = () => {

    if (messageEdit.edit) {
      handleSave();
      return;
    }

    if (message.name === "" || message.message === "") {
      return;
    }
    dispatch(newDefaultMessage(message, cookies.csrf_access_token));
    setMessage({ name: "", message: "" });
    setShowNewMessageFields(false); // Ocultar los campos después de enviar un nuevo mensaje
  };

  const handleSave = () => {
    // Lógica para guardar el mensaje editado

    dispatch(editDefaultMessage(messageEdit, cookies.csrf_access_token));

    setMessageEdit({ id: "", name: "", message: "", edit: false });
    setShowNewMessageFields(false); // Ocultar los campos después de guardar una edición
  };

  // Función para activar el modo de nuevo mensaje
  const handleActivateNewMessage = () => {
    setShowNewMessageFields(true);
    setMessage({ name: "", message: "" }); // Limpiar campos de nuevo mensaje
    setMessageEdit({ id: "", name: "", message: "", edit: false }); // Salir del modo de edición
  };

  return (
    <>
      {/*     <ThemeProvider theme={styles}>*/}
      <ContainerDefaultMessage messagesDefault={messagesDefault} loading={loading} setMessageEdit={setMessageEdit} messageEdit={messageEdit} notEdit={notEdit} setShowDefaultMessages={setShowDefaultMessages} setSelectedMessageFromMessages={setSelectedMessageFromMessages} filterText={filterText} setFilterText={setFilterText} />
      {/* Botón para activar los campos de nuevo mensaje si no estamos editando ni creando uno nuevo */}
      {!notEdit && !messageEdit.edit && !showNewMessageFields && !forceShowCreationFields && ( 
        <Button
          color="buttonGreenPink"
          size="small"
          onClick={handleActivateNewMessage}
          sx={{  width: '100%' }}
        >
          <AddIcon sx={{padding: 0}}/>
          Nuevo Mensaje Predeterminado
        </Button>
      )}

      {(messageEdit.edit || showNewMessageFields || forceShowCreationFields) && (
        <FormControl sx={{ width: "100%", borderRadius: "12px", display: "flex", flexDirection: "column", gap: "1rem" }} >
          {/*  <TextField
            id="outlined-adornment-message"
            type="text"
            multiline
            rows={4}
            label="Responder"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            sx={
              {
                marginY: "4px",
                // 👉 1.  Añadimos relleno superior al root
                "& .MuiOutlinedInput-root": {
                  borderRadius: "12px",
                  "& fieldset": { border: "none" },
                },
              }
            }
          /> */}

          <TextField
            id="outlined-name-message"
            type="text"
            label="Título del mensaje"
            value={messageEdit.edit ? messageEdit.name : message.name}
            onChange={(e) => {
              if (messageEdit.edit) {
                setMessageEdit({ ...messageEdit, name: e.target.value });
              } else {
                setMessage({ ...message, name: e.target.value });
              }
            }}
            fullWidth
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: "12px",
              },
              bgcolor: "background.default",
              borderRadius: "12px",
            }}
          />
          <TextField
            id="outlined-content-message"
            type="text"
            multiline
            rows={4}
            label="Contenido del mensaje"
            value={messageEdit.edit ? messageEdit.message : message.message}
            onChange={(e) => {
              if (messageEdit.edit) {
                setMessageEdit({ ...messageEdit, message: e.target.value });
              } else {
                setMessage({ ...message, message: e.target.value });
              }
            }}
            fullWidth
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: "12px",
              },
              bgcolor: "background.default",
              borderRadius: "12px",
            }}
          />

          {!notEdit && (
            <IconButton
              aria-label="Mensaje predeterminado"
              edge="start"
              color="buttonGreenPink"
              sx={{
                position: "absolute",
                bottom: 8,
                left: 20,
              }}
              onClick={() => {
                defaultMessage();
                  setShowDefaultMessages(false); // Cierra el popover
                  setShowNewMessageFields(false); // Oculta los campos de nuevo mensaje
                  setMessageEdit({ id: "", name: "", message: "", edit: false }); // Sale del modo de edición
                  setMessage({ name: "", message: "" }); // Limpia los campos de nuevo mensaje
              }}
            >
              <ArrowBackIcon />
            </IconButton>
          )}
          <IconButton
            aria-label="Enviar mensaje"
            edge="end"
            color="buttonGreenPink"
            sx={{
              position: "absolute",
              bottom: 8,
              right: 20,
            }}
            onClick={() => handleSend()}
          >
            <SendIcon />
          </IconButton>


        </FormControl>
      )}

    </>
  )
}

export default DefaultMessages
