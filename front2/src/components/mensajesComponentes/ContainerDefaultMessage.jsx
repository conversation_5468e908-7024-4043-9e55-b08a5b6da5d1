import { Autocomplete, Box, CircularProgress, Skeleton, TextField, Typography, Button } from "@mui/material";
import React, { useEffect, useRef } from "react";
import "./messagesList.css";
import { MessageDefault } from "./components/MessageDefault";
import { useDispatch } from "react-redux";
import { editDefaultMessage, getDefaultMessages } from "../../redux/mensajesDucks";



export const ContainerDefaultMessage = ({ messagesDefault, loading, setMessageEdit, messageEdit, setSelectedMessageFromMessages, notEdit = false, setShowDefaultMessages, filterText, setFilterText }) => {

    const [open, setOpen] = React.useState(false);
    const [selectedMessage, setSelectedMessage] = React.useState(null);
    const [orderedMessages, setOrderedMessages] = React.useState(messagesDefault || []);
    const [loadingNoMatch, setLoadingNoMatch] = React.useState(false);
    const debounceTimeout = useRef(null);



    React.useEffect(() => {
        setOrderedMessages(messagesDefault);
    }, [messagesDefault]);

    const handleSelectMessage = (event, newValue) => {
        setSelectedMessage(newValue);
        if (newValue) {
            // Mover el mensaje seleccionado al primer lugar
            const newOrder = [newValue, ...orderedMessages.filter(m => m.id !== newValue.id)];
            setOrderedMessages(newOrder);
        } else {
            // No hay coincidencias
            handleNoMatch();
        }
    };

    const handleNoMatch = () => {
    };

    const dispatch = useDispatch();

    const handleNoMatchRequest = async (search) => {
        setLoadingNoMatch(true);

        const offset = 0;
        const nc = 10;

        dispatch(getDefaultMessages({ search, offset, nc }));
    };

    useEffect(() => {
        if (!loading) {

            setLoadingNoMatch(false);

        }
    }, [loading]);

    // 2️⃣  Cada vez que cambia el texto del Autocomplete…
    const handleInputChange = (event, value, reason) => {
        if (reason === "input") {
            // 🔸  Debounce para la búsqueda en servidor (lo que ya tenías)
            if (debounceTimeout.current) clearTimeout(debounceTimeout.current);

            // 🔸  Normaliza para búsqueda insensible a mayúsculas/minúsculas
            const term = value.trim().toLowerCase();

            // ⏳  Si no hay coincidencias locales, dispara la petición remota
            if (term && !messagesDefault.some(m => m.name.toLowerCase().includes(term))) {
                debounceTimeout.current = setTimeout(() => handleNoMatchRequest(term), 500);
            }

            // 🔸  Re-ordena localmente
            if (term) {
                const matches = messagesDefault.filter(m =>
                    m.name.toLowerCase().includes(term)
                );
                const rest = messagesDefault.filter(m =>
                    !m.name.toLowerCase().includes(term)
                );
                setOrderedMessages([...matches, ...rest]);
            } else {
                // Si el input quedó vacío, vuelve al orden original
                setOrderedMessages(messagesDefault);
            }
        }
    };

    // Limpieza del timeout al desmontar el componente
    useEffect(() => {
        return () => {
            if (debounceTimeout.current) {
                clearTimeout(debounceTimeout.current);
            }
        };
    }, []);

    const handleEdit = (message) => {
        setMessageEdit({
            id: message.id,
            name: message.name,
            message: message.message,
            edit: true,
        });
    };

    const mensajesFiltrados = filterText
        ? orderedMessages.filter(m =>
            m.name.toLowerCase().includes(filterText.toLowerCase()) ||
            m.message.toLowerCase().includes(filterText.toLowerCase())
        )
        : orderedMessages;

    React.useEffect(() => {
        if (
            filterText &&
            mensajesFiltrados.length === 0 &&
            !loadingNoMatch // para no hacer múltiples llamadas
        ) {
            handleNoMatchRequest(filterText);
        }
        // eslint-disable-next-line
    }, [filterText, mensajesFiltrados.length]);

    React.useEffect(() => {
        // Solo cerrar cuando:
        // 1. Hay un término de búsqueda
        // 2. La carga ha terminado completamente
        // 3. Después de la carga, realmente no hay resultados
        if(!notEdit) return;
        if (filterText !== "" && 
            !loadingNoMatch && 
            !loading &&
            orderedMessages.length > 0 && // Aseguramos que ya se cargaron mensajes
            orderedMessages.filter(m =>
                m.name.toLowerCase().includes(filterText.toLowerCase()) ||
                m.message.toLowerCase().includes(filterText.toLowerCase())
            ).length === 0) {
            // Añadimos un delay mayor para asegurar que los resultados se procesaron
            const timer = setTimeout(() => {
                setShowDefaultMessages(false);
            }, 0);
            
            return () => clearTimeout(timer); // Limpiamos el timeout si cambian las dependencias
        }
    }, [loadingNoMatch, loading, filterText, orderedMessages]);


    if (loading && !loadingNoMatch) return (
        <Skeleton
            variant="rectangular"
            width="100%"
            height={notEdit ? "300px" : "450px"}
            sx={{ borderRadius: "12px", margin: "1em auto" }}
        />
    );

    return (
        <>
            {messagesDefault?.length === 0 ? (
                <div className="messages-list">
                    <div className="message-item">
                        <div className="message-content">
                            <Typography variant="body2" color="text.secondary">No hay mensajes</Typography>
                        </div>
                    </div>
                </div>
            ) : (
                <Box className="messages-list" sx={{
                    backgroundColor: "background.default", width: "100%",

                    height: notEdit ? "300px" : "425px",
                }}>

                    <Autocomplete
                        id="asynchronous-demo"
                        sx={{ width: "100%" }}
                        open={open}
                        value={selectedMessage}
                        onOpen={() => {
                            setOpen(true);
                        }}
                        onClose={() => {
                            setOpen(false);
                        }}
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        getOptionLabel={(option) => option.name}
                        onChange={handleSelectMessage}
                        onInputChange={handleInputChange}
                        options={messagesDefault}
                        noOptionsText={loadingNoMatch ? "Cargando..." : "No hay opciones"}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                label="Busca un mensaje"
                                InputProps={{
                                    ...params.InputProps,
                                    endAdornment: (
                                        <React.Fragment>

                                            {loading || loadingNoMatch ? (
                                                <CircularProgress color="inherit" size={20} />
                                            ) : null}
                                            {params.InputProps.endAdornment}

                                        </React.Fragment>
                                    ),
                                }}
                            />
                        )}
                    />

                    {/*  {loadingNoMatch && (
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 2 }}>
                            <CircularProgress size={24} sx={{ mr: 1 }} />
                            <Typography variant="body2">Buscando coincidencias...</Typography>
                        </Box>
                    )} */}

                    {mensajesFiltrados?.map((message) => (
                        <Box
                            key={message.id}
                            className="message-item"
                            style={{
                                width: "100%",
                                maxWidth: "100%",
                            }}
                        >
                            <MessageDefault message={message} handleEdit={handleEdit} setSelectedMessage={setSelectedMessageFromMessages} notEdit={notEdit} setShowDefaultMessages={setShowDefaultMessages} setFilterText={setFilterText} />
                        </Box>
                    ))}
                </Box>
            )}




        </>
    );
};
