import { Box, Typography, IconButton } from "@mui/material";
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import EditIcon from '@mui/icons-material/Edit';

export const MessageDefault = ({ message, handleEdit, setSelectedMessage, notEdit = false, setShowDefaultMessages, setFilterText }) => {
    return (
        <Box sx={{ borderRadius: "12px", padding: "1em", backgroundColor: "background", width: "100%", cursor: "pointer", "&:hover": { backgroundColor: "action.hover" } }}
            onClick={() => {
                setFilterText("");
                // Reemplazar el texto después del @ con el mensaje seleccionado
                setSelectedMessage(prev => {
                    // Buscar la última aparición de @ en el texto
                    const lastAtIndex = prev.lastIndexOf('@');
                    if (lastAtIndex !== -1) {
                        // Obtener el texto antes del último @
                        const textBeforeAt = prev.substring(0, lastAtIndex);
                        // Eliminar cualquier texto después del @ y reemplazarlo con el mensaje seleccionado
                        return textBeforeAt + message.message;
                    } else {
                        // Si no hay @, simplemente añadir el mensaje al final
                        return prev + " " + message.message;
                    }
                });
                setShowDefaultMessages(false);
            }}
        >
            <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: "0.5em" }}>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    {!notEdit && (
                        <ChatBubbleOutlineIcon sx={{ color: "text.secondary", mr: 2 }} />
                    )}
                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                        <Typography variant="body1" color="text.primary">
                            {message.name}
                        </Typography>
                        <Typography variant="caption" color="text.primary">
                            {message.message}
                        </Typography>
                    </Box>
                </Box>


                <IconButton size="small" aria-label="Editar mensaje"
                    onClick={(e) => {
                        e.stopPropagation();
                        handleEdit(message);
                    }}
                >
                    <EditIcon sx={{ color: "text.secondary" }} />
                </IconButton>

            </Box>
        </Box>
    );
};
