import React from 'react';
import { Popover, Typography } from '@mui/material';
import DefaultMessages from './DefaultMessages';

export const MessagePopover = ({
  open,
  anchorEl,
  onClose,
  defaultMessage,
  setShowDefaultMessages,
  setSelectedMessageFromMessages,
  filterText,
  setFilterText,
  showCreationOptions = true,
  forceShowCreationFields = false,
}) => {
  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      disableAutoFocus
      disableEnforceFocus
      disableRestoreFocus
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      PaperProps={{
        sx: {
          borderRadius: '16px',
          maxWidth: '500px',
          width: '90vw',
          maxHeight: 'auto',
          p: 2,
          marginTop: '-30px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
        }
      }}
    >
     {/*  <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
        Mensajes predeterminados
      </Typography> */}
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Selecciona, edita o crea un mensaje predeterminado
      </Typography>
      <DefaultMessages
        defaultMessage={defaultMessage}
        notEdit={!showCreationOptions}
        setShowDefaultMessages={setShowDefaultMessages}
        setSelectedMessageFromMessages={setSelectedMessageFromMessages}
        filterText={filterText}
        setFilterText={setFilterText}
        forceShowCreationFields={forceShowCreationFields}
      />
    </Popover>
  );
}; 