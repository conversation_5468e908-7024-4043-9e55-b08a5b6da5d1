.messages-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px;
  margin: 0 auto 20px;
  border-radius: 12px;
  height: 380px;
  max-height: 380px;
  overflow-y: auto; 
}

.message-item {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  position: relative;
  min-width: 0;
}

.message-left {
  align-self: flex-start;
  margin-left: 10px;
}

.message-right {
  align-self: end;
    margin-right: 10px;
}

.message-content {
  padding: 8px 12px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  position: relative;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100%;
  min-width: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Estilo para mensajes de la izquierda (recibidos) */
.message-left .message-content {
  background-color: #5e5e5e;
  color: #e9edef;
  border-bottom-left-radius: 0px;
}

.message-left .message-content::before {
  content: '';
  position: absolute;
  left: -8px;
  bottom: 0;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-right-color: #5e5e5e;
  border-left: 0;
  border-bottom: 0;
}

/* Estilo para mensajes de la derecha (enviados) */
.message-right .message-content {
  background-color: #41644a;
  color: #e9edef;
  border-bottom-right-radius: 0px;
}

.message-right .message-content::before {
  content: '';
  position: absolute;
  right: -8px;
  bottom: 0;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-left-color: #41644a;
  border-right: 0;
  border-bottom: 0;
}

/* Opcional: Añadir hora del mensaje */
.message-time {
  font-size: 11px;
  color: #8696a0;
  margin-top: 2px;
  text-align: right;
  opacity: 0.7;
}

.messages-list {
  overflow-y: auto;           /* se puede hacer scroll */
  scrollbar-width: none !important;   /* Firefox */
  -ms-overflow-style: none;   /* IE 10–11 y Edge Legacy */
}
/* Chrome, Safari, Edge Chromium, Opera… */
.messages-list::-webkit-scrollbar {
  display: none;   /* versión más contundente que width:0 */
}

/* Botonera oculta por defecto */
.comment-actions {
  display: flex;       /* mantiene el hueco cuando aparezca  */
  gap: 4px;
  opacity: 0;
  visibility: hidden;  /* impide que sea clicable/inlcuyable en el tab index */
  transition: opacity 0.2s ease;
}

/* Al posar el cursor sobre el mensaje se muestran */
.message-item .comment-actions {
  opacity: 1;
  visibility: visible;
}
