import {
  <PERSON><PERSON>ield,
  IconButton,
  FormControl,
  Box,
  InputAdornment,
  Typography,
} from "@mui/material";
import SendIcon from "@mui/icons-material/Send";
import QuickreplyIcon from "@mui/icons-material/Quickreply";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { enviarMensaje, getDefaultMessages } from "../../redux/mensajesDucks";
import { useCookies } from "react-cookie";
import { DefaultMessagesChip } from "./components/DefaultMessagesChip";
import { ContainerDefaultMessage } from "./ContainerDefaultMessage";
import { MessagePopover } from "./MessagePopover";
import MensajeInput from "../componentesGenerales/MensajeInput";
import { AlertComponent } from "../componentesGenerales/Alert";



const SendMessages = ({ pedido, defaultMessage, isEditing, setIsEditing, setCommentText, commentText }) => {
  const [message, setMessage] = React.useState("");
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const pedidoId = pedido.id;
  const severity = useSelector((state) => state.mensajes.severity);
  const loading = useSelector((state) => state.mensajes.loading);
  const [filterText, setFilterText] = React.useState("");

  const [showDefaultMessages, setShowDefaultMessages] = React.useState(false);
  const [anchorEl, setAnchorEl] = React.useState(null); // Para el popover

  const [selectedMessage, setSelectedMessage] = React.useState(null);

  const messagesDefault = useSelector(
    (state) => state.mensajes.messagesDefault
  );

  const orderMessages = useSelector((store) => store.mensajes.orderMessages);

  // Nuevo estado para recordar términos sin coincidencias
  const [noMatchTerms, setNoMatchTerms] = React.useState([]);

  const maxLength = useSelector((store) => store.pedidos.commentMaxLength);

  useEffect(() => {
    if (messagesDefault.length == 0) {
      dispatch(getDefaultMessages());
    }
  }, [dispatch, orderMessages]);


  /* useEffect(() => {
    if (filterText == "") {
      debugger
      setMessage(prev => prev.replace(/@([^\s]*)/g, ''));
    }
  }, [filterText]); */


  React.useEffect(() => {
    if (severity === "success") {
      setMessage("");
    }
  }, [severity]);

  // Función para abrir el popover
  const handleOpenPopover = (event) => {
    setAnchorEl(event.currentTarget);
    setShowDefaultMessages(true);
  };

  // Función para cerrar el popover
  const handleClosePopover = () => {
    setAnchorEl(null);
    setShowDefaultMessages(false);
  };

  // Escuchar cuando un término no tiene coincidencias
  useEffect(() => {
    if (!loading && filterText && messagesDefault.length > 0) {
      const hasMatches = messagesDefault.some(m =>
        m.name.toLowerCase().includes(filterText.toLowerCase()) ||
        m.message.toLowerCase().includes(filterText.toLowerCase())
      );

      if (!hasMatches && !noMatchTerms.includes(filterText)) {
        setNoMatchTerms(prev => [...prev, filterText]);
      }
    }
  }, [loading, filterText, messagesDefault]);

  const [showMaxLengthAlert, setShowMaxLengthAlert] = React.useState(false);

  const handleSendMessage = () => {
    if (message.length >= maxLength) {
      setShowMaxLengthAlert(true);
      return;
    }
    dispatch(enviarMensaje(pedidoId, message, cookies.csrf_access_token));
  }

  useEffect(() => {
    if (showMaxLengthAlert) {
      setTimeout(() => {
        setShowMaxLengthAlert(false);
      }, 3000);
    }
  }, [showMaxLengthAlert]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "1rem", width: "100%" }}>
      <FormControl sx={{ width: "100%", borderRadius: "12px" }}>
      

        <MensajeInput
          value={message}
          setValue={setMessage}
          onSend={handleSendMessage}
          loading={loading}
          maxLength={maxLength}
          onMaxLengthAlert={() => setShowMaxLengthAlert(true)}
        />

        {showMaxLengthAlert && (
          <AlertComponent
            color="warning"
            message={`Has llegado al límite máximo de ${maxLength} caracteres`}
            cleanMessage={() => setShowMaxLengthAlert(false)}
            time={3000}
          />
        )}

      </FormControl>

    </Box>
  );
};

export default SendMessages;
