import React from "react";
import "./messagesList.css";
import { Box, Skeleton, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { obtenerMensajesOrden } from "../../redux/mensajesDucks";
import ChatBubbleOutlineIcon from "@mui/icons-material/ChatBubbleOutline";
import { formatHeaderDate, formatTime } from "../../Utils/generalFunctions";


/**
 * Componente de chat estilo redes sociales
 * - Agrupa mensajes por día (UTC)
 * - Muestra encabezado de fecha (Hoy / Ayer / dd de mes de aaaa)
 * - Dentro de cada burbuja muestra solo la HORA (UTC)
 * - Mantiene adjuntos e indicador Cliente / Vendedor
 */
const MessagesList = ({ id }) => {
  const dispatch = useDispatch();

  /* ------------------------------------------------- */
  /*  Obtener mensajes                                */
  /* ------------------------------------------------- */
  React.useEffect(() => {
    dispatch(obtenerMensajesOrden(id));
  }, [id, dispatch]);

  const loading = useSelector((store) => store.mensajes.loading);
  const orderMessages = useSelector((store) => store.mensajes.orderMessages);
  const [messages, setMessages] = React.useState([]);

  React.useEffect(() => setMessages(orderMessages), [orderMessages]);

  /* ------------------------------------------------- */
  /*  Agrupar mensajes por día (UTC)                   */
  /* ------------------------------------------------- */
  const grouped = React.useMemo(() => {
    const byDate = {};
    messages.forEach((m) => {
      // Clave fecha UTC: yyyy-mm-dd (sin desfase local)
      const key = new Date(m.timeStamp).toISOString().substring(0, 10);
      if (!byDate[key]) byDate[key] = [];
      byDate[key].push(m);
    });
    return byDate;
  }, [messages]);


 
  /* ------------------------------------------------- */

  /* ------------------------------------------------- */
  /*  Render                                          */

  /* ------------------------------------------------- */

  if (loading  && orderMessages.length === 0) {
    return (
      <Skeleton variant="rectangular" width="100%" height={360} sx={{ borderRadius: 2, my: 2 }} />
    );
  }

  if (messages.length === 0) {
    return (
      <Box
        className="messages-list"
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          height: 360,
          my: 2,
        }}
      >
        <ChatBubbleOutlineIcon sx={{ fontSize: 48, color: "text.disabled", mb: 1 }} />
        <Typography variant="body2" color="text.secondary">
          No hay mensajes todavía
        </Typography>
      </Box>
    );
  }

  return (
    <Box className="messages-list" sx={{ bgcolor: "background.default", width: "100%", overflowY: "auto" }}>
      {Object.keys(grouped)
        .sort() // Orden ascendente (más antiguos arriba)
        .map((dateKey) => (
          <React.Fragment key={dateKey}>
            {/* Encabezado de fecha */}
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ display: "block", textAlign: "center", my: 1 }}
            >
              {formatHeaderDate(dateKey)}
            </Typography>

            {/* Mensajes de ese día */}
            {grouped[dateKey].map((msg) => (
              <Box
                key={msg.id}
                className={`message-item ${msg.fromClient ? "message-left" : "message-right"}`}
              >
                <div className="message-content">
                  {/* Texto del mensaje (HTML permitido) */}
                  <span dangerouslySetInnerHTML={{ __html: msg.message }} />

                  {/* Adjuntos */}
                  {msg.orderMessageAtachments?.length > 0 && (
                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                      {msg.orderMessageAtachments.map((att) => (
                        <img
                          key={att.id}
                          src={att.URLPhoto ? att.URLPhoto.replace(/\\/g, "+++") : undefined}
                          alt={att.marketplaceId}
                          style={{ maxWidth: 200, height: "auto", borderRadius: 12, padding: 5 }}
                        />
                      ))}
                    </Box>
                  )}

                  {/* Hora (UTC) */}
                  <Typography
                    variant="caption"
                    sx={{
                      display: "block",
                      mt: 0.2,
                      color: (theme) => theme.palette.text.pinkMain, // aquí sí puedes usar función
                    }}
                  >
                    {formatTime(msg.timeStamp)}
                  </Typography>
                </div>
              </Box>
            ))}
          </React.Fragment>
        ))}
    </Box>
  );
};

export default MessagesList;



/* import React from 'react'
import "./messagesList.css";
import { Box, Skeleton, Typography } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import CargandoLista from '../CargandoLista';
import { obtenerMensajesOrden } from '../../redux/mensajesDucks';

const MessagesList = ({ id }) => {
  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(obtenerMensajesOrden(id));
  }, [id]);
  

    const loading = useSelector((store) => store.mensajes.loading);
    const orderMessages = useSelector((store) => store.mensajes.orderMessages);
  
    const [messages, setMessages] = React.useState([]);
  
    React.useEffect(() => {
      setMessages(orderMessages);

    }, [orderMessages]);


  return (
    <>
    {loading ? <Skeleton variant="rectangular" width="100%" height={"450px"}  sx={{ borderRadius: "12px", margin: "1em auto" }} /> :
    <>
      {messages?.length === 0 ? (
        <div className="messages-list">
          <div className="message-item">
            <div className="message-content">
              <Typography variant="body2" color="text.secondary">No hay mensajes</Typography>
            </div>
          </div>
        </div>
      ) : (
        <Box className="messages-list" sx={{ backgroundColor: "background.default", width: "100%" }}>
          {messages?.map((message) => (
            <Box
              key={message.id}
              className={`message-item ${message.fromClient === false ? "message-right" : "message-left"
                }`}
            >
              <div className="message-content">{message.message}
                {message.orderMessageAtachments?.length > 0 && (
                  <Box sx={{ display: "flex", flexDirection: "column" }}>
                    {message.orderMessageAtachments.map((attachment) => (
                      <img
                        key={attachment.id}
                        src={attachment.URLPhoto ? attachment.URLPhoto.replace("\\", "+++") : null} 
                        alt={attachment.marketplaceId}
                        style={{ maxWidth: "200px", height: "auto", borderRadius: "12px", padding: "5px" }}
                      />
                    ))}
                  </Box>
                )}

              </div>
            </Box>
          ))}
        </Box>
      )}
    </>
    }
    </>
  );
};

export default MessagesList
 */