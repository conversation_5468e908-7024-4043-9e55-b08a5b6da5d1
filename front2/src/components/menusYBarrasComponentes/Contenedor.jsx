import * as React from 'react';
import { styled, useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import MuiDrawer from '@mui/material/Drawer';
import MuiAppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import CssBaseline from '@mui/material/CssBaseline';
import Typography from '@mui/material/Typography';
import logo2 from '/logoWithoutBgBn2.png';
// import logo2 from '/logotipo_principal.png';
import { ModeContext } from '../../context/ModeProvider';
import Listas from './Listas';
import { useDispatch, useSelector } from 'react-redux';
import { useMediaQuery } from '@mui/material';
import { useEffect, useState } from 'react';
import { Route, Routes, useLocation, useNavigate } from 'react-router-dom';
import Welcome from "../paginaInicioComponentes/Welcome";
import ProductosGeneral from "../productosComponentes/ProductosGeneral";
import AlmacenesGeneral from "../almacenesComponentes/AlmacenesGeneral";
import ProveedoresGeneral from "../proveedoresComponentes/ProveedoresGeneral";
import MetricasGeneral from "../metricasComponentes/MetricasGeneral";
import VentasGeneral from "../ventasComponentes/VentasGeneral";
import PerfilGeneral from "../personalizarPerfilComponentes/PerfilGeneral";
import CuentasGeneral from "../cuentasComponentes/CuentasGeneral";
import ConfiguracionSistema from "../configuracionComponentes/ConfiguracionSistema";
import ChageImageCover from "../configuracionComponentes/ChangeImageCover";
import ChageImageLogin from "../configuracionComponentes/ChangeImageLogin";
import { ManejarErrores } from "../ManejarErrores";
import SetMarketplace from "../configuracionComponentes/SetMarketplace";
import StatusInterno from "../configuracionComponentes/statusInterno/StatusInterno";
import { Alert, Stack } from "@mui/material";
import InfoCompanyGeneral from "../personalizarInfoCompanyComponentes/InfoCompanyGeneral";
import TablaPedidos from "../ordenesComponentes/TablaPedidos";
import FacturasMenu from "../facturasComponentes/FacturasMenu";
import Navbar from './Navbar';
import CambiarZonaHoraria from "../configuracionComponentes/CambiarZonaHoraria";
////
// import {
//   Box,
//   Button,
//   Hidden,
//   Modal,
//   Typography,
//   useMediaQuery,
// } from "@mui/material";
// const modalStyle = {
//   position: "absolute",
//   top: "50%",
//   left: "50%",
//   transform: "translate(-50%, -50%)",
//   width: "60%",
//   bgcolor: "background.paper",
//   border: "2px solid #000",
//   boxShadow: 24,
//   p: 4,
// };

import { Button, Modal } from "@mui/material";
import { AlertComponent } from '../componentesGenerales/Alert';
import { limpiarMensajeAlmacen } from '../../redux/almacenesDucks';
import CustomDialog from '../componentesGenerales/CustomDialog';
const modalStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "60%",
  bgcolor: "background.paper",
  border: "2px solid #000",
  boxShadow: 24,
  p: 4,
};
///

const drawerWidth = 240;

const openedMixin = (theme) => ({
  width: drawerWidth,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: 'hidden',
});

const closedMixin = (theme) => ({
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: 'hidden',
  width: `calc(${theme.spacing(7)} + 1px)`,
  [theme.breakpoints.up('sm')]: {
    width: `calc(${theme.spacing(8)} + 1px)`,
  },
});

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-end',
  padding: theme.spacing(0, 1),
  ...theme.mixins.toolbar,

}));

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(['width', 'margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(['width', 'margin'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

const Drawer = styled(MuiDrawer, { shouldForwardProp: (prop) => prop !== 'open' })(
  ({ theme, open }) => {
    // Declarar variables aquí
    const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
    const borderRadiusValue = `0px ${open ? '25px' : '0px'} ${isSmallScreen && !open ? '0px' : '25px'} 0px`;

    return {
      width: drawerWidth,
      flexShrink: 0,
      whiteSpace: 'nowrap',
      boxSizing: 'border-box',
      ...(open && {
        ...openedMixin(theme),
        '& .MuiDrawer-paper': openedMixin(theme),
      }),
      ...(!open && {
        ...closedMixin(theme),
        '& .MuiDrawer-paper': closedMixin(theme),
      }),
      '& .MuiPaper-root , .MuiDrawer-paper': {
        height: isSmallScreen ? open ? "auto" : "88.22px" : "auto",
        maxHeight: "100%",
        // overflowY: "scroll",
        borderBottom : isSmallScreen && !open ? "2px solid" : "none",
        borderRadius: borderRadiusValue,
      },
    };
  }
);


export default function MiniDrawer() {
  const theme = useTheme();
  const [open, setOpen] = React.useState(false);
  const [mobileOpen, setMobileOpen] = React.useState(false);

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = () => {
    setOpen(!open);
  };

  const handleMobileDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const { themee, colorMode } = React.useContext(ModeContext);

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const drawer = (
    <div>
      <DrawerHeader
        style={{
          backgroundColor: "primary",
        }}
      >
        {/* <IconButton onClick={handleDrawerClose}>
            {theme.direction === 'rtl' ? <ChevronRightIcon /> : <ChevronLeftIcon />}
          </IconButton> */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            position: 'absolute',
            left: isMobile && !open ? '43%' : '50%',
            transform: 'translateX(-50%)',
            cursor: 'pointer',
            // justifyContent: 'space-between',
            // margin: 'auto',
            width: '100%',
            paddingLeft: ".15em",
            // top: isMobile && !open ? '13px' : '0px',
            marginTop: isMobile  ? '20px' : '0px',

          }}
        // onClick={goHome}
        >
          <img
            src={themee.palette.mode === 'dark' ? logo2 : logo2}
            alt="logo"
            onClick={handleDrawerClose}
            style={{
              width: 40, height: 40,
              fontSize: '1.45rem',
              marginLeft: '10px',
            }}
          />
          {open &&
            <Typography variant="h6" component="div"
              style={{
                fontWeight: 'nomal',
                // fontSize: '1.45rem',
                marginLeft: '0.6em',
              }}
              color={themee.palette.mode === 'light' ? '#ffff !important' : theme.palette.text.primary}
            >
              mercaleader
            </Typography>
          }
        </Box>
      </DrawerHeader>

      {open && isMobile && 
        <Listas open={open} setOpen={setOpen} />}
      { !isMobile &&
        <Listas open={open} setOpen={setOpen} />}
        

    </div>
  );

  const dispatch = useDispatch();
  const user = useSelector((store) => store.usuario.user);
  const msgVentas = useSelector((store) => store.pedidos.msgVentas);
  const msgProductos = useSelector((store) => store.productos.mensajeAlert);
  const msgFacturas = useSelector((store) => store.facturas.mensajeFacturas);
  const msgMensajes = useSelector((store) => store.mensajes.mensaje);
  const severityMensajes = useSelector((store) => store.mensajes.severity);

  const [modalFactura, setModalFactura] = useState(false);
  const [abrir, setAbrir] = React.useState(false);
  const accionAbrir = () => {
    setAbrir(!abrir);
  };

  const setNullMsgWrapper = () => {
    setCurrentMsg(null);
  };

  const [currentMsg, setCurrentMsg] = useState(null);

  useEffect(() => {
    setCurrentMsg(msgProductos);
  }, [msgProductos]);


  
    
  useEffect(() => {
    if (msgFacturas?.mensaje === "Documento de ingreso registrado exitosamente") {
      setCurrentMsg(null);
      setModalFactura({open:true,
        title:"Ingreso exitoso",
        reloadButton:"Agregar otra"
        })
    }else if (msgFacturas?.mensaje === "Documento de ingreso actualizado exitosamente") {
      setCurrentMsg(null);
      setModalFactura({open:true,
        title:"Actualización exitosa",
        reloadButton:"Revisar"
        })
    } else {
      setCurrentMsg(msgFacturas);
    }
  }, [msgFacturas]);

  useEffect(() => {
    setCurrentMsg(msgVentas);
    
    // Si hay un mensaje, configurar un temporizador para quitarlo después de 4 segundos
    if (msgVentas) {
      const timer = setTimeout(() => {
        setCurrentMsg(null);
      }, 4000);
      
      // Limpiar el temporizador si el componente se desmonta o si cambia msgVentas
      return () => clearTimeout(timer);
    }
  }, [msgVentas]);

  useEffect(() => {
    const object = {
      mensaje: msgMensajes,
      severity: severityMensajes,
    }
    setCurrentMsg(object);
    
    // Si hay un mensaje, configurar un temporizador para quitarlo después de 4 segundos
    if (msgMensajes) {
      const timer = setTimeout(() => {
        setCurrentMsg(null);
      }, 4000);
      
      // Limpiar el temporizador si el componente se desmonta o si cambia msgVentas
      return () => clearTimeout(timer);
    }
  }, [msgMensajes, severityMensajes]);

  const themeBreak = useTheme();

  const isSmallScreen = useMediaQuery(themeBreak.breakpoints.down("sm"));


  const drawerRef = React.useRef(null); // Referencia para el Drawer

  // Detectar clics fuera del Drawer
  useEffect(() => {
    function handleClickOutside(event) {
      if (drawerRef.current && !drawerRef.current.contains(event.target)) {
        setOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Detectar scroll fuera del Drawer
  useEffect(() => {
    function handleScroll(event) {
      if (drawerRef.current && !drawerRef.current.contains(event.target)) {
        setOpen(false);
      }
    }

    document.addEventListener("scroll", handleScroll, true);
    return () => {
      document.removeEventListener("scroll", handleScroll, true);
    };
  }, []);

  const location = useLocation();
const hideAppBarRoutes = ["/landing"];
const shouldHideAppBar = hideAppBarRoutes.includes(location.pathname);


  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />

  {!shouldHideAppBar && (
    <>
      <AppBar position="fixed" open={open}
        style={{
          color: themee.palette.text.primary,
          boxShadow: "none",
          // backgroundColor: "red",
          width: `calc(100% - ${open ? "220px" : isMobile ? "57px" : "65px"})`,
          paddingLeft: open ? "20px" : "0px",
          zIndex: "1000 !important",
          /* '& .MuiPaper-root , .MuiDrawer-paper': {
            zIndex: "1000 !important",
          }, */
        }}
      >
        <Toolbar
          sx={{
            height: "88.22px",
            borderBottom: "2px solid",
          }}
        >
          <Navbar openNav={open} />
          {/* <Typography variant="h6" noWrap component="div">
            Mini variant drawer
          </Typography> */}
        </Toolbar>
      </AppBar>
  

      <Drawer
        variant="permanent"
        ref={drawerRef}
        open={open}
        style={{
          // backgroundColor: "primary !important",
          borderRadius: "30px",
          height: "auto !important",
          width: "auto !important",
        /*   '& .MuiPaper-root , .MuiDrawer-paper': {
            height: "auto !important",
            width: "auto !important",
          }, */
        }}
        sx={{
          display: { xs: 'none', sm: 'block', },
        }}
      >
        {drawer}
      </Drawer>
      </>
)}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { 
            xss: "100%",
             sm: `calc(100% - ${drawerWidth}px)`,
            },
        }}
      >
        {!shouldHideAppBar && (
        <DrawerHeader />
        )}

        <div style={{ display: "flex", width: "100%" }}>

          <div
            style={{
              padding: isSmallScreen ? " 4em 0 0 0" : shouldHideAppBar ? " 0 0 0 0" : "60px 2% 20px 2%",
              flexGrow: 1,
              width: "100%",
              // backgroundColor: "#ffffff" /*theme.palette.background.default*/,
            }}
          >
            <Routes>
              <Route index element={<Welcome />} />
              <Route
                path={`pedidosTabla`}
                element={<TablaPedidos abrir={abrir} />}
              />


              {/* {role === "Logistics_employee" ? (
                <Route path={`facturas/*`} element={<NotAutorized />} />
              ) : ( */}
              <Route path={`facturas/*`} element={<FacturasMenu />} />
              {/* // )} */}
              <Route path={`productos/*`} element={<ProductosGeneral />} />
              <Route path={`proveedores/*`} element={<ProveedoresGeneral />} />
              <Route path={`almacenes/*`} element={<AlmacenesGeneral />} />
              <Route path={`metricas/*`} element={<MetricasGeneral />} />
              <Route path={`ventas/*`} element={<VentasGeneral />} />
              <Route path={`perfil/*`} element={<PerfilGeneral />} />
              <Route path={`cuentas/*`} element={<CuentasGeneral />} />
              <Route path={`compania/*`} element={<InfoCompanyGeneral />} />
              <Route
                path="*"
                element={<ManejarErrores errorCode={"status 404"} />}
              />
              {/* <Route path={`configuracion/`} element={<ConfiguracionSistema />} /> */}
              <Route path={`/configuracion`} element={<ConfiguracionSistema />}>
                <Route path="marketplace" element={<SetMarketplace />} />
                <Route path="statusInterno" element={<StatusInterno />} />
                <Route
                  path="statusInterno/ventaDirecta"
                  element={<StatusInterno />}
                />
                <Route path="cambiarImagenPortada" element={<ChageImageCover />} />
                <Route path="cambiarImagenLogin" element={<ChageImageLogin />} />
                <Route path="cambiarZonaHoraria" element={<CambiarZonaHoraria />} />
              </Route>
              <Route
                path="*"
                element={<ManejarErrores errorCode={"status 404"} />}
              />
            </Routes>

            

             {currentMsg && currentMsg.mensaje &&  currentMsg.mensaje != "" &&  (
              <AlertComponent
                color={currentMsg.severity}
                message={
                  typeof currentMsg.mensaje === "object"
                    ? currentMsg.mensaje.mensaje || currentMsg.errores || JSON.stringify(currentMsg.mensaje)
                    : currentMsg.mensaje 
                }
                cleanMessage={ currentMsg.extraFunction || setCurrentMsg}
                time={currentMsg.time || 3000}
              />
            )}



            {msgFacturas && (
              <CustomDialog
                open={modalFactura.open}
                onClose={() => {}}
                title={modalFactura.title}
                maxWidth="60vh"
                maxHeight="50vh"
                width="100%"
                actions={
                  <>
                    <Button
                      color="error"
                      onClick={() => {
                        window.location.reload();
                      }}
                      variant="outlined"
                      sx={{ marginRight: "10px" }}
                    >
                      {modalFactura.reloadButton}
                    </Button>
                    <Button
                      color="primary"
                      variant="contained"
                      onClick={() => {
                        window.location.href = "facturas/consultar";
                      }}
                    >
                      Consultar Facturas
                    </Button>
                  </>
                }
              >
                  {msgFacturas?.mensaje}
              </CustomDialog>
            )}
          </div>
        </div>


      </Box>
    </Box>
  );
}
