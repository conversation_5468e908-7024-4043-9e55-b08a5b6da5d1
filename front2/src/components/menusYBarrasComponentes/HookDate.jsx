import { useEffect, useState } from 'react';

const formatDateTime = (isoString, timeZone) => {
  const date = new Date(isoString);

  const dateFormatter = new Intl.DateTimeFormat('es-MX', {
    year: 'numeric',
    month: 'long',
    day: '2-digit',
    timeZone,
  });

  const timeFormatter = new Intl.DateTimeFormat('es-MX', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone,
  });

  const rawDate = dateFormatter.format(date); // Ej: "13 de mayo de 2025"
  // Use a regex split to handle potential multiple spaces or varying formats robustly
  const parts = rawDate.split(/\s*de\s*/); // ["13", "mayo", "2025"] or ["13", "mayo de 2025"] - let's stick to simple split
  const rawParts = rawDate.split(' '); // ["13", "de", "mayo", "de", "2025"]

  // Reconstruct assuming the "day de month de year" structure
  let formattedDate = rawParts[0] + ' ' + rawParts[2] + ' ' + rawParts[4];


  const formattedTime = timeFormatter.format(date);
  // Get the last part after the final slash, or the whole string if no slash
  const shortZone = timeZone.split('/').pop() || timeZone;

  return {
    date: formattedDate,
    time: formattedTime,
    location: shortZone.replace(/_/g, ' '), // Replace underscores for readability
  };
};

// ✅ Ajustar una fecha a otra zona horaria y retornar el resultado formateado
const adjustDateToTimeZone = (originalDateString, targetTimeZone) => {
    const date = new Date(originalDateString);

    const dateFormatter = new Intl.DateTimeFormat('es-MX', {
      year: 'numeric',
      month: 'long',
      day: '2-digit',
      timeZone: targetTimeZone,
    });

    const timeFormatter = new Intl.DateTimeFormat('es-MX', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
      timeZone: targetTimeZone,
    });

    const rawDate = dateFormatter.format(date); // "13 de mayo de 2025"
    // Reconstruct assuming the "day de month de year" structure
    const rawParts = rawDate.split(' '); // ["13", "de", "mayo", "de", "2025"]
    const formattedDate = rawParts[0] + ' ' + rawParts[2] + ' ' + rawParts[4]; // "13 mayo 2025"

    const formattedTime = timeFormatter.format(date); // "18:25"

    // Get the last part after the final slash, or the whole string if no slash
    const shortZone = targetTimeZone.split('/').pop() || targetTimeZone;

    // Include the timezone in the adjusted string
    return `${formattedDate}, ${formattedTime} `;
  };


export const useCustomDateTime = (inputDateString = null) => {
  const getData = () => {
    // --- TIMEZONE DETERMINATION LOGIC ---
    let timeZoneToUse = localStorage.getItem('timeZone');

    if (!timeZoneToUse) {
        // If not in local storage, try browser timezone
        try {
            const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            if (browserTimeZone) {
                timeZoneToUse = browserTimeZone;
                localStorage.setItem('timeZone', timeZoneToUse);
            } else {
                // If browser timezone is somehow not available, default to Mexico City
                console.warn("Browser timezone not detected, falling back to America/Mexico_City.");
                localStorage.setItem('timeZone', 'America/Mexico_City');
                timeZoneToUse = 'America/Mexico_City';
            }
        } catch (e) {
            // Handle potential errors during timezone detection (unlikely but good practice)
            console.error("Error detecting browser timezone, falling back:", e);
            timeZoneToUse = 'America/Mexico_City';
        }
    }

    // Final fallback in case something unexpected happened
     if (!timeZoneToUse) {
         timeZoneToUse = 'America/Mexico_City';
     }
    // ------------------------------------

    const storedTimeZone = timeZoneToUse; // Use the determined timezone

    const iso = localStorage.getItem('customDateTime') ?? new Date().toISOString();

    const baseDate = inputDateString ?? iso;

    // Formatear la fecha ajustada a la zona horaria deseada (la determinada arriba)
    const adjusted = adjustDateToTimeZone(baseDate, storedTimeZone);

    // Formatear la fecha base a la zona horaria deseada (la determinada arriba)
    const formatted = formatDateTime(baseDate, storedTimeZone);


    return {
      ...formatted, // Contains date, time, location (based on storedTimeZone)
      adjustedDateTime: adjusted, // "13 mayo 2025, 18:25 Mexico_City"
    };
  };

  const [currentDateTime, setCurrentDateTime] = useState(getData());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentDateTime(getData());
    }, 1000);

    // Re-run effect if inputDateString changes to reset the base date
    return () => clearInterval(interval);
  }, [inputDateString]); // Dependency array includes inputDateString

  // Note: The hook returns the object { date, time, location, adjustedDateTime }
  // adjustedDateTime is the string like "13 mayo 2025, 18:25 Mexico_City"
  return currentDateTime;
};