import React from "react";
import Listas from "./Listas";
import { Drawer, Divider } from "@mui/material";

const drawerWidth = 200;

const customStyle = {
  drawer: {
    width: drawerWidth,
    flexShrink: 0,
  },

  drawerPaper: { width: 200 },
};

const Cajon = (props) => {
  return (
    <Drawer
      sx={customStyle.drawer}
      classes={{ paper: customStyle.drawerPaper }}
      anchor="left"
      variant={props.variant}
      open={props.open}
      onClose={props.onClose ? props.onClose : null}
    >
      <Divider />
      <Listas />
    </Drawer>
  );
};

export default Cajon;
