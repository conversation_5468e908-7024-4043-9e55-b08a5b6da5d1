//este compornte es la barra superior de la pagina, contiene el logo, el search y el switch para el modo de color
// tambien contiene un search que se muestra en las paginas de marketplace, productos, ventas y facturas
// en el search se puede buscar por proveedor, fecha, status, status interno, abiertos/cerrados, sellerMarketplace, search, dependiendo 
// de los filtros que se tengan en la pagina
// en el switch se puede cambiar entre modo claro y oscuro
// este codigo actualiza tambirn la fecha y hora cada minuto

import {
  Typography,
  Switch,
  useMediaQuery,
} from "@mui/material";
import { Avatar, Popover, List, ListItemButton, ListItemText } from '@mui/material';
import React, { useEffect, useState } from "react";
import {
  cerrarSesionAccion, leerUsuarioInfoAccion
} from '../../redux/usersDucks'
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Box from '@mui/material/Box';
import { useTheme, styled } from '@mui/material/styles';
import { ModeContext } from "../../context/ModeProvider";
import SearchField from "../SearchField";
import { useSearchParams } from "react-router-dom";
import { obtenerTotalPedidos } from "../../redux/pedidosDucks";
import { obtenerOrdenesConsolidadas } from "../../redux/ordenesConsolidadas";
import { obtenerTotalProductos } from "../../redux/productosDucks";
import { providerList } from "../../Utils/atributtesHandlerSupplier";
import { obtenerTotalFacturas } from "../../redux/facturasDucks";
import { useCustomDateTime } from "./HookDate";

//switch para ligt y dark mode
const MaterialUISwitch = styled(Switch)(({ theme }) => ({
  width: 90,
  height: 41,
  padding: 0,
  overflow: 'visible',
  '& .MuiSwitch-switchBase': {
    position: 'absolute',
    padding: 0,
    transition: 'transform 0.5s ease, background-color 0.5s ease', // Hacer más lento el cambio
    '&.Mui-checked': {
      transform: 'translateX(45px)', // Desplaza el botón completamente hacia la derecha
      '& .MuiSwitch-thumb': {
        backgroundColor: '#FFFFFF',
        transition: 'background-color 0.5s ease', // Animación de color de fondo más visible y lenta
        backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 20 20"><path fill="${encodeURIComponent('#41644A')}" d="M4.2 2.5l-.7 1.8-1.8.7 1.8.7.7 1.8.6-1.8L6.7 5l-1.9-.7-.6-1.8zm15 8.3a6.7 6.7 0 11-6.6-6.6 5.8 5.8 0 006.6 6.6z"/></svg>')`,
      },
    },
    '&:not(.Mui-checked)': {
      transform: 'translateX(8px)',
      '& .MuiSwitch-thumb': {
        backgroundColor: '#FFFFFF',
        transition: 'background-color 0.5s ease',
        backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 20 20"><path fill="${encodeURIComponent('#41644A')}" d="M9.305 1.667V3.75h1.389V1.667h-1.39zm-4.707 1.95l-.982.982L5.09 6.072l.982-.982-1.473-1.473zm10.802 0L13.927 5.09l.982.982 1.473-1.473-.982-.982zM10 5.139a4.872 4.872 0 00-4.862 4.86A4.872 4.872 0 0010 14.862 4.872 4.872 0 0014.86 10 4.872 4.872 0 0010 5.139zm0 1.389A3.462 3.462 0 0113.471 10a3.462 3.462 0 01-3.473 3.472A3.462 3.462 0 016.527 10 3.462 3.462 0 0110 6.528zM1.665 9.305v1.39h2.083v-1.39H1.666zm14.583 0v1.39h2.084v-1.39h-2.084zM5.09 13.928L3.616 15.4l.982.982 1.473-1.473-.982-.982zm9.82 0l-.982.982 1.473 1.473.982-.982-1.473-1.473zM9.305 16.25v2.083h1.389V16.25h-1.39z"/></svg>')`,
      },
    },
  },
  '& .MuiSwitch-thumb': {
    width: "41px",
    height: "41px",
    position: 'relative',
    // top: isMobile ? "6px" : "0px",
    backgroundColor: '#FFFFFF',
    transition: 'transform 0.5s ease, background-color 0.5s ease',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
  },
  '& .MuiSwitch-track': {
    width: '100%',
    height: '100%',
    borderRadius: 50,
    backgroundColor: '#D1D5DB',
    border: '1px solid #FFFFFF',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 5px',
    transition: 'background-color 0.5s ease',
    // backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" kheight="24" width="24" viewBox="0 0 24 24"><path fill="${encodeURIComponent('#41644A')}" d="M4.2 2.5l-.7 1.8-1.8.7 1.8.7.7 1.8.6-1.8L6.7 5l-1.9-.7-.6-1.8zm15 8.3a6.7 6.7 0 11-6.6-6.6 5.8 5.8 0 006.6 6.6z"/></svg>')`, // Sol visible
    backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 20 20"><path fill="${encodeURIComponent('#41644A')}" d="M9.305 1.667V3.75h1.389V1.667h-1.39zm-4.707 1.95l-.982.982L5.09 6.072l.982-.982-1.473-1.473zm10.802 0L13.927 5.09l.982.982 1.473-1.473-.982-.982zM10 5.139a4.872 4.872 0 00-4.862 4.86A4.872 4.872 0 0010 14.862 4.872 4.872 0 0014.86 10 4.872 4.872 0 0010 5.139zm0 1.389A3.462 3.462 0 0113.471 10a3.462 3.462 0 01-3.473 3.472A3.462 3.462 0 016.527 10 3.462 3.462 0 0110 6.528zM1.665 9.305v1.39h2.083v-1.39H1.666zm14.583 0v1.39h2.084v-1.39h-2.084zM5.09 13.928L3.616 15.4l.982.982 1.473-1.473-.982-.982zm9.82 0l-.982.982 1.473 1.473.982-.982-1.473-1.473zM9.305 16.25v2.083h1.389V16.25h-1.39z"/></svg>')`,
    backgroundRepeat: 'no-repeat',
    backgroundPosition: '.83em',
    '&.Mui-checked': {
      backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 20 20"><path fill="${encodeURIComponent('#41644A')}" d="M4.2 2.5l-.7 1.8-1.8.7 1.8.7.7 1.8.6-1.8L6.7 5l-1.9-.7-.6-1.8zm15 8.3a6.7 6.7 0 11-6.6-6.6 5.8 5.8 0 006.6 6.6z"/></svg>')`,
    },
    '&.css-tj8d99-MuiSwitch-track': {
      // backgroundColor: 'red',
      border: '1px solid #FFFFFF',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 5px',
      transition: 'background-color 0.5s ease',
      backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="20" width="20" viewBox="0 0 20 20"><path fill="${encodeURIComponent('#41644A')}" d="M4.2 2.5l-.7 1.8-1.8.7 1.8.7.7 1.8.6-1.8L6.7 5l-1.9-.7-.6-1.8zm15 8.3a6.7 6.7 0 11-6.6-6.6 5.8 5.8 0 006.6 6.6z"/></svg>')`,
      backgroundRepeat: 'no-repeat',
      backgroundPosition: '3.3em',
    },

  },
}));

const Navbar = ({ openNav }) => {
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const cerrarSesion = () => {
    dispatch(cerrarSesionAccion())
  }

  //info de usuario
  const info = useSelector((store) => store.usuario.info);
  const name = useSelector(store => store.usuario.name);
  const nickname = useSelector(store => store.usuario.alias);
  const URLphoto = useSelector(store => store.usuario.URLphoto);

  const { themee, colorMode } = React.useContext(ModeContext);


  //trair la info del usuario
  React.useEffect(() => {
    if (!info || Object.keys(info).length === 0) {
      dispatch(leerUsuarioInfoAccion());
    }
  }, []);

  const goHome = () => {
    navigate("/");
  }
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const profileClick = () => {
    navigate('/perfil/personalizar')
    handleClose()
  }

  const open = Boolean(anchorEl);
  const id = open ? 'avatar-popover' : undefined;

  let isMobile = useMediaQuery((theme) => theme.breakpoints.down("sm"));

  //get the marketplaces available from the redux store
  const origenes = useSelector(
    (store) => store.ordenesConsolidadas.origenDeOrden
  );

  // info de facturas
  //contiene informacion de los proveedores seleccionados de las facturas
  // estas facturas tienen id especificos y no se identifican por su nombre
  const [proveedoresFiltro, setProveedoresFiltro] = React.useState(
    searchParams.get("provider") === null
      ? providerList.map((proveedor) => proveedor.issuerRfc)
      : searchParams.get("provider").split("-")
  );

  const [checked, setChecked] = useState(false);

  const handleSwitchChange = (event) => {
    setChecked(event.target.checked); // Cambia el valor del switch

    colorMode.toggleColorMode();
  };

  //cambia el estado del switch si se cambia el modo de color
  useEffect(() => {
    setChecked(themee.palette.mode === 'dark');
  }
    , [themee.palette.mode])

  const [currentDateTime, setCurrentDateTime] = useState({
    date: "",
    time: "",
    location: "Cd. Mex",
  });

  // actualiza la fecha y hora cada minuto
  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date();
      const optionsDate = {
        day: "numeric",
        month: "long",
        year: "numeric",
      };
      const optionsTime = {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      };

      setCurrentDateTime({
        date: now.toLocaleDateString("es-MX", optionsDate),
        time: now.toLocaleTimeString("es-MX", optionsTime),
        location: "Cd. Mex",
      });
    };

    // Actualiza la fecha y hora cada minuto
    updateDateTime();
    const interval = setInterval(updateDateTime, 60000);

    return () => clearInterval(interval); // Limpia el intervalo al desmontar
  }, []);

  //se obtiene la url actual para saber si la pagina llevara un search
  let marketplaceSearch = window.location.href.includes("pedidosTabla");
  let ventasSearch = window.location.href.includes("ventas/administrar");
  let searchProducts = window.location.href.includes("productos/consultar");
  let searchInvoice = window.location.href.includes("facturas/consultar");

  const ismarketPlaceUrl = marketplaceSearch || ventasSearch || searchProducts || searchInvoice;

  // ocupado para todas barras
  const searchText =
    searchParams.get("search") === null ? "" : searchParams.get("search");

  const namesStatus = useSelector((store) => store.pedidos.orderStatus);
  const namesStatusInternoV2 = useSelector(
    (store) => store.pedidos.orderInternalStatus
  );

  const today = new Date();

  const oneMonthAgo = new Date(
    today.getFullYear(),
    today.getMonth() - 1,
    today.getDate()
  );
  /**Nombres globales para los parametros de los filtros */

  //solo para marketplace
  const proveedor =
    searchParams.get("proveedores") === null
      ? "1-2-3-4"
      : searchParams.get("proveedores");

  //  solo para marketplace
  const status =
    searchParams.get("orderStatusId") === null
      ? namesStatus?.map((status) => status.orderStatusId)?.join("-")
      : searchParams.get("orderStatusId");

  // solo para marketplace
  const statusInterno =
    searchParams.get("orderStatusInternoId") === null
      ? namesStatusInternoV2
        ?.map((statusInterno) => statusInterno.orderInternalStatusId)
        ?.join("-")
      : searchParams.get("orderStatusInternoId");

  // para todos
  const initialDate = () => {
    let param = searchParams.get("fechaInicial") || searchParams.get("initialDate");
    if (param === null) {
      return oneMonthAgo.toISOString().split("T")[0];
    } else {
      return param;
    }
  };

  const finalDate = () => {
    let param = searchParams.get("fechaFinal") || searchParams.get("finalDate");

    if (param === null) {
      return today.toISOString().split("T")[0];
    } else {
      return param;
    }
  }

  //solo para marketplace y consolidado
  const openCloseToggle =
    searchParams.get("abiertosCerrados") === null
      ? "Abiertos"
      : searchParams.get("abiertosCerrados");

  const sellerMarketplaceToggle =
    searchParams.get("sellerMarketplace") === null
      ? "2"
      : searchParams.get("sellerMarketplace");

  const marketplaces = useSelector((store) => store.pedidos.marketplaces);

  const filtro =
    searchParams.get("filtro") === null
      ? marketplaces ? `${origenes.map(marketplace => marketplace.id || marketplace.marketplaceId).join('-')}` : "ds-1-2-3-4-5-7"
      : searchParams.get("filtro");



  //funcion para enviar el search dependiendo de la pagina
  const enviarSearch = (event, searchTextField) => {
    console.log('00000000000000000000000000000000000000')
    console.log(event)
    console.log(searchTextField)

    console.log('00000000000000000000000000000000000000')
    var nuevoSearch = event?.target[1]?.value || searchTextField
    const dateInitial = initialDate();
    const finalDateLocal = finalDate();

    if (searchProducts) {
      dispatch(obtenerTotalProductos({ search: nuevoSearch }));
    } else if (marketplaceSearch) {

      dispatch(
        obtenerTotalPedidos({
          proveedor,
          filtrosStatus: status,
          filtrosStatusInterno: statusInterno,
          finalDate: finalDateLocal,
          initialDate: dateInitial,
          abiertosCerrados: openCloseToggle,
          sellerMarketplace: sellerMarketplaceToggle,
          search: nuevoSearch,
        })
      );
    }
    else if (ventasSearch) {
      dispatch(
        obtenerOrdenesConsolidadas(dateInitial, finalDateLocal, nuevoSearch, filtro)
      );
    } else if (searchInvoice) {

      let proveedorFiltro = proveedoresFiltro?.map((el) => el.toString()).join("-");

      dispatch(
        obtenerTotalFacturas({
          proveedor: proveedorFiltro,
          finalDate: finalDate(),
          initialDate: initialDate(),
          search: nuevoSearch,
        })
      );
    }

    if (marketplaceSearch) {
      navigate(
        `?&search=${nuevoSearch}&proveedores=${proveedor}&fechaInicial=${dateInitial}&fechaFinal=${finalDateLocal}&orderStatusId=${status}&orderStatusInternoId=${statusInterno}&abiertosCerrados=${openCloseToggle}&sellerMarketplace=${sellerMarketplaceToggle}&page=${1}`
      );
    } else if (ventasSearch) {
      navigate(
        `?&search=${nuevoSearch}&filtro=${filtro}&fechaInicial=${dateInitial}&fechaFinal=${finalDateLocal}&page=${1}`
      );
    } else if (searchProducts) {
      navigate(`?page=${1}&search=${nuevoSearch}`);
    } else if (searchInvoice) {
      let proveedorFiltro = proveedoresFiltro?.map((el) => el.toString()).join("-");
      navigate(
        `?provider=${proveedorFiltro}&page=${1}&initialDate=${initialDate()
        }&finalDate=${finalDate()}&search=${nuevoSearch}`
      );
    }
  };

  const theme = useTheme();
  const currentDateTimeHook = useCustomDateTime();


  //es para saber si la pantalla es pequeña
  const isXSMobile = useMediaQuery(theme.breakpoints.down("xs"));

  const returDate = () => {
    return  <Typography
    sx={{
      fontSize: '12.31px',
      fontFamily: 'Noto Sans',
      fontWeight: '400',
      [theme.breakpoints.down('lg')]: { fontSize: '12px' },
      [theme.breakpoints.down('md')]: { fontSize: '10px' },
      [theme.breakpoints.down('sm')]: { fontSize: '8px' },
    }}
  >
    {currentDateTimeHook.date}, {currentDateTimeHook.time}  {currentDateTimeHook.location}
  </Typography>
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
      }}>

      <Box
        style={{
          display: 'flex',
          justifyContent: isMobile ? 'space-around' : 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        {/* Contenedor para el logo e imagen centrados */}

        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          sx={{
            minWidth: isMobile && openNav ? '100%' : isMobile && !openNav ? 'auto' : '230px',
            cursor: "pointer",
          }}
        // onClick={goHome}
        >
          <Box
            sx={{
              display: 'flex',
              cursor: 'pointer',
              flexDirection: 'column',
              justifyContent: 'start',
              alignItems: 'start',
            }}
          >
            <Typography
              variant="body1"
              sx={{
                fontFamily: 'Filson Pro',
                fontSize: '22.57px', // Tamaño base
                fontWeight: 700,
                color: theme.palette.text.greenLight,
                [theme.breakpoints.down('lg')]: { fontSize: '18px' }, // Pantallas grandes
                [theme.breakpoints.down('md')]: { fontSize: '16px' }, // Tablets
                [theme.breakpoints.down('sm')]: { fontSize: '14px' }, // Móviles
              }}
            >
              !Hola, semidios {" "}
              {nickname
                ? (nickname?.length > 10 ? `${nickname?.slice(0, 10)}...` : nickname)
                : (name?.split(" ")[0])}
            </Typography>

            {/* //fecha       */}
            {!isXSMobile && (
              returDate()

            )}
            {/* </Typography> */}
          </Box>
        </Box>


        {/* // search */}
        {ismarketPlaceUrl && !isXSMobile && (
          <SearchField enviarSearch={enviarSearch} searchText={searchText} isMobile={isMobile} />
        )}

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '14px',
          }} >
          <MaterialUISwitch
            sx={{ m: 1 }}
            checked={checked} // Controla el valor del switch
            onChange={handleSwitchChange} // Cambia el estado al hacer clic
          />

          {/* notificaciones comentadas temporalmente */}
          {/* <Badge color="secondary" variant="dot"
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <NotificationsNoneIcon />
        </Badge> */}

          <Avatar
            sx={{ width: 28, height: 28, cursor: 'pointer' }}
            src={URLphoto}
            onClick={handleClick}

          />

          <Popover
            id={id}
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'center',
            }}
          >
            <List>
              <ListItemButton onClick={profileClick}>
                <ListItemText primary="Mi perfil" />
              </ListItemButton>
              <ListItemButton color="inherit" onClick={() => cerrarSesion()}>
                <ListItemText primary="Cerrar sesión" />
              </ListItemButton>
            </List>
          </Popover>
        </Box>

      </Box>

      {isXSMobile && (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          sx={{
            width: '100%',
          }}
        >
          {returDate()}
          <SearchField enviarSearch={enviarSearch} searchText={searchText} isMobile={isMobile} />
        </Box>
      )}
    </Box>
  );
}


export default Navbar;
