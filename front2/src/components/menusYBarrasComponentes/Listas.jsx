import * as React from "react";
import ListSubheader from "@mui/material/ListSubheader";
import List from "@mui/material/List";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import Collapse from "@mui/material/Collapse";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import DriveFolderUploadIcon from "@mui/icons-material/DriveFolderUpload";
import QueryStatsIcon from "@mui/icons-material/QueryStats";
import ListItemIcon from "@mui/material/ListItemIcon";
import BallotIcon from "@mui/icons-material/Ballot";
import InventoryIcon from "@mui/icons-material/Inventory";
import LocalMallIcon from "@mui/icons-material/LocalMall";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import ContentPasteSearchIcon from "@mui/icons-material/ContentPasteSearch";
import SellIcon from "@mui/icons-material/Sell";
import StoreMallDirectoryIcon from "@mui/icons-material/StoreMallDirectory";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import WarehouseIcon from "@mui/icons-material/Warehouse";
import StorefrontIcon from "@mui/icons-material/Storefront";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import CreateIcon from "@mui/icons-material/Create";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import UndoIcon from "@mui/icons-material/Undo";
import AltRouteIcon from "@mui/icons-material/AltRoute";
import DomainAddIcon from "@mui/icons-material/DomainAdd";
import SearchIcon from "@mui/icons-material/Search";
import BusinessIcon from "@mui/icons-material/Business";
import InsightsIcon from "@mui/icons-material/Insights";
import SettingsIcon from "@mui/icons-material/Settings";
import ContactsIcon from "@mui/icons-material/Contacts";
import RecentActorsIcon from "@mui/icons-material/RecentActors";
import CorporateFareIcon from "@mui/icons-material/CorporateFare";
import StarsIcon from "@mui/icons-material/Stars";
import StoreIcon from "@mui/icons-material/Store";
import MonitorHeartIcon from "@mui/icons-material/MonitorHeart";
import AdminPanelSettingsIcon from "@mui/icons-material/AdminPanelSettings";
import HomeWorkIcon from "@mui/icons-material/HomeWork";
import HouseSidingIcon from "@mui/icons-material/HouseSiding";
import AccountBoxIcon from "@mui/icons-material/AccountBox";
import ManageAccountsIcon from "@mui/icons-material/ManageAccounts";
import LayersIcon from "@mui/icons-material/Layers";
import GiteIcon from '@mui/icons-material/Gite';
import GroupIcon from '@mui/icons-material/Group';

import { BrowserRouter as Router, Link, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { AccountBalance, Business, DomainAdd } from "@mui/icons-material";
import theme from "../../temaConfig";
import { useTheme } from "@emotion/react";
import { Typography } from "@mui/material";

const Listas = ({ open, setOpen }) => {
  const [openProductos, setOpenProductos] = React.useState(false);
  const [openContact, setOpenContact] = React.useState(false);
  const [openVentas, setOpenVentas] = React.useState(false);
  const [openCuentas, setOpenCuentas] = React.useState(false);
  const role = useSelector((store) => store.usuario.role);
  const handleClickProductos = () => {
    setOpenProductos(!openProductos);
  };

  const handleClickContact = () => {
    setOpenContact(!openContact);
  };
  const handleClickVentas = () => {
    setOpenVentas(!openVentas);
  };

  const handleClickCuentas = () => {
    setOpenCuentas(!openCuentas);
  };

  const [openFacturas, setOpenFacturas] = React.useState(false);
  const handleClickFacturas = () => {
    setOpenFacturas(!openFacturas);
  };

  const [openXMLFacturas, setOpenXMLFacturas] = React.useState(false);
  const handleClickXMLFacturas = () => {
    setOpenXMLFacturas(!openXMLFacturas);
  };

  const [openAlmacenes, setOpenAlmacenes] = React.useState(false);
  const handleClickAlmacenes = () => {
    setOpenAlmacenes(!openAlmacenes);
  };

  const [openCompany, setOpenCompany] = React.useState(false);
  const handleClickCompany = () => {
    setOpenCompany(!openCompany);
  };

  const [openProveedores, setOpenProveedores] = React.useState(false);
  const handleClickProveedores = () => {
    setOpenProveedores(!openProveedores);
  };

  const [openAlmacenesInternos, setOpenAlmacenesInternos] =
    React.useState(false);
  const handleClickAlmacenesInternos = () => {
    setOpenAlmacenesInternos(!openAlmacenesInternos);
  };
  // administrar
  const [openAdministrarAlmacenes, setOpenAdministrarAlmacenes] =
    React.useState(false);
  const handleClickAdministrarAlmacenes = () => {
    setOpenAdministrarAlmacenes(!openAdministrarAlmacenes);
  };
  // ver almacenes dentro de administrar
  const [openVerAlmacenes, setOpenVerAlmacenes] = React.useState(false);
  const handleClickVerAlmacenes = () => {
    setOpenVerAlmacenes(!openVerAlmacenes);
  };
  const [openAlmacenesRemotos, setOpenAlmacenesRemotos] = React.useState(false);

  const handleClickAlmacenesRemotos = () => {
    setOpenAlmacenesRemotos(!openAlmacenesRemotos);
  };

  const [openAlmacenesInternosSalidas, setOpenAlmacenesInternosSalidas] =
    React.useState(false);
  const handleClickAlmacenesInternosSalidas = () => {
    setOpenAlmacenesInternosSalidas(!openAlmacenesInternosSalidas);
  };

  const [openAlmacenesInternosEntradas, setOpenAlmacenesInternosEntradas] =
    React.useState(false);
  const [openAlmacenesRemotosSalidas, setOpenAlmacenesRemotosSalidas] =
    React.useState(false);
  const [openDirectas, setOpenDirectas] = React.useState(false);

  const handleClickAlmacenesInternosEntradas = () => {
    setOpenAlmacenesInternosEntradas(!openAlmacenesInternosEntradas);
  };
  const handleClickAlmacenesRemotosSalidas = () => {
    setOpenAlmacenesRemotosSalidas(!openAlmacenesRemotosSalidas);
  };

  const handleClickDirectas = () => {
    setOpenDirectas(!openDirectas);
  };

  const [openAlmacenesExternos, setOpenAlmacenesExternos] =
    React.useState(false);
  const handleClickAlmacenesExternos = () => {
    setOpenAlmacenesExternos(!openAlmacenesExternos);
  };

  React.useEffect(() => {
    if (!open) {
      setOpenProductos(false);
      setOpenContact(false);
      setOpenVentas(false);
      setOpenCuentas(false);
      setOpenFacturas(false);
      setOpenXMLFacturas(false);
      setOpenAlmacenes(false);

      setOpenCompany(false);
      setOpenProveedores(false);
      setOpenAlmacenesInternos(false);
      setOpenAdministrarAlmacenes(false);
      setOpenVerAlmacenes(false);
      setOpenAlmacenesRemotos(false);
      setOpenAlmacenesInternosSalidas(false);
      setOpenAlmacenesInternosEntradas(false);
      setOpenAlmacenesRemotosSalidas(false);

      setOpenAlmacenesExternos(false);
    }
  }, [open]);


  const theme = useTheme();

  const stylesList = {
    paddingLeft: "5px",
    paddingRight: "5px",
    fontSize: "12px",
    margin: "auto",
    '& .MuiListItemText-root, .MuiTypography-root, .MuiSvgIcon-root': {
      color: theme.palette.mode === "light" ? "#ffffff" : theme.palette.text.primary,
    },
    '& .Mui-selected': {
      backgroundColor: "#d4d8d678 !important", // Color de fondo para el estado seleccionado
      borderRadius: "25px !important", // Asegura el borde redondeado
      color: "#1976d2 !important", // Cambia el color del texto del elemento seleccionado
    },
    '& .Mui-selected:hover': {
      backgroundColor: "#d4d8d678 !important", // Color de fondo para el estado seleccionado al pasar el mouse
      color: "#ffffff !important", // Color del texto en el hover
      borderRadius: "25px !important", // Asegura el borde redondeado
    },
    '& .MuiListItemButton-root': {
      borderRadius: "25px !important", // Establece el borde redondeado por defecto
      margin: "5px 0"
    },
    '& .MuiListItemButton-root:hover': {
      backgroundColor: "#d4d8d678", // Color de fondo al pasar el mouse sobre el botón
      borderRadius: "25px !important", // Asegura el borde redondeado
      color: "#000000", // Color del texto al pasar el mouse
    },
    '& .MuiListItemIcon-root': {
      minWidth: "45px"
    }

  };




  const location = useLocation();

  const closeNav = () => {
    setOpen(false);
  }


  return (
    <List
      sx={stylesList}
      component="nav"
      aria-labelledby="nested-list-subheader"
    // subheader={
    //   <ListSubheader >
    //     Menú principal
    //   </ListSubheader>
    // }

    >
      {!open ?
        <div style={{ height: "19.8px" }}></div>
        : <Typography variant="h6" style={{ fontSize: "12px", paddingLeft: "14px" }}>Menú</Typography>}
      {/* metricas */}

      <Link to={`metricas/visualizar`} style={{
        textDecoration: "none",
        display: "flex",
      }}
        onClick={() => closeNav()}
      >
        <ListItemButton selected={location.pathname === "/metricas/visualizar"} >
          <ListItemIcon >
            <InsightsIcon />
          </ListItemIcon>
          <ListItemText primary="Métricas" />
        </ListItemButton>
      </Link>


      <ListItemButton onClick={() => {
        if (!open) {
          setOpen(true);
        }
        handleClickVentas();
      }}
      // selected={location.pathname
      >
        <ListItemIcon>
          <StoreMallDirectoryIcon />
        </ListItemIcon>
        <ListItemText primary="Ventas" />
        {openVentas ? <ExpandLess /> : <ExpandMore />}
      </ListItemButton>

      <Collapse in={openVentas} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>

          <Link to={`ventas/administrar`} style={{ textDecoration: "none" }}>
            <ListItemButton
              sx={{
                pl: 3,
                // backgroundColor: openVentas ? "#E8E8E8" : " white",
              }}
              selected={location.pathname === "/ventas/administrar"}
              onClick={() => closeNav()}

            >
              <ListItemIcon>
                <SellIcon />
              </ListItemIcon>
              <ListItemText primary="Consolidado" />
            </ListItemButton>
          </Link>


          <Link to={`/pedidosTabla`} style={{ textDecoration: "none" }}>
            <ListItemButton
              sx={{
                pl: 3,
                // backgroundColor: openVentas ? "#E8E8E8" : " white",
              }} /*onClick={() => actualizarPedidos('todos')}*/
              selected={location.pathname === "/pedidosTabla"}
              onClick={() => closeNav()}
            >
              <ListItemIcon>
                <StoreIcon />
              </ListItemIcon>
              <ListItemText primary="Marketplaces" />
            </ListItemButton>
          </Link>

          <ListItemButton
            sx={{
              pl: 4,
              // backgroundColor: openAlmacenesInternosEntradas
              //   ? "#D1D1D1"
              //   : "#DCDCDC",
            }}
            onClick={() => {
              // debugger
              if (!open) {
                setOpen(true);
                setOpenVentas(true);
              }
              handleClickDirectas()
            }}
          >
            <ListItemIcon>
              <FileDownloadIcon />
            </ListItemIcon>
            <ListItemText primary="Directas" />
            {openDirectas ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={openDirectas} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>

              <Link
                to={`ventas/administrarDirecta`}
                style={{ textDecoration: "none" }}
                onClick={() => closeNav()}
              >
                <ListItemButton
                  sx={{
                    pl: 5,
                    // backgroundColor: "#D1D1D1",
                  }} /*onClick={() => handleClickAlmacenesInternosEntradas()}*/
                  selected={location.pathname === "/ventas/administrarDirecta"}
                >
                  <ListItemIcon>
                    <CreateIcon />
                  </ListItemIcon>

                  <ListItemText primary="Cotizaciones" />
                </ListItemButton>
              </Link>



              <Link to={`ventas/directa`} style={{ textDecoration: "none" }}>
                <ListItemButton
                  sx={{
                    pl: 5,
                    // backgroundColor: " #D1D1D1",
                    // backgroundColor: openVentas ? "#E8E8E8" : " white",
                  }} /*onClick={() => actualizarPedidos('todos')}*/
                  selected={location.pathname === "/ventas/directa"}
                  onClick={() => closeNav()}
                >
                  <ListItemIcon>
                    <AddCircleIcon />
                  </ListItemIcon>
                  <ListItemText primary="Nueva venta" />
                </ListItemButton>
              </Link>

            </List>
          </Collapse>
        </List>
      </Collapse>

      {/* productos */}

      <ListItemButton onClick={() => {
        if (!open) {
          setOpen(true);
        } handleClickProductos()
      }}
      >
        <ListItemIcon>
          <LocalMallIcon />
        </ListItemIcon>
        <ListItemText primary="Productos" />
        {openProductos ? <ExpandLess /> : <ExpandMore />}
      </ListItemButton>
      <Collapse in={openProductos} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>
          {role !== "Logistics_employee" ? (

            <Link to={`productos/nuevo`} style={{ textDecoration: "none" }}
              onClick={() => closeNav()}
            >
              <ListItemButton
                sx={{
                  pl: 3,
                  // backgroundColor: openProductos ? "#E8E8E8" : " white",
                }} /*onClick={() => actualizarPedidos('todos')}*/
                selected={location.pathname === "/productos/nuevo"}
              >
                <ListItemIcon>
                  <AddCircleIcon />
                </ListItemIcon>
                <ListItemText primary="Nuevo" />
              </ListItemButton>
            </Link>

          ) : null}

          <Link to={`productos/consultar`} style={{ textDecoration: "none" }}>
            <ListItemButton
              sx={{
                pl: 3,
                // backgroundColor: openProductos ? "#E8E8E8" : " white",
              }} /*onClick={() => actualizarPedidos('todos')}*/
              selected={location.pathname === "/productos/consultar"}
              onClick={() => closeNav()}
            >
              <ListItemIcon>
                <ContentPasteSearchIcon />
              </ListItemIcon>
              <ListItemText primary="Administrar" />
            </ListItemButton>
          </Link>

          {role !== "Logistics_employee" ? (
            <Link
              to={`productos/cargaMasiva`}
              style={{ textDecoration: "none" }}
              onClick={() => closeNav()}
            >
              <ListItemButton
                sx={{
                  pl: 3,
                  // backgroundColor: openProductos ? "#E8E8E8" : " white",
                }} /*onClick={() => actualizarPedidos('todos')}*/
                selected={location.pathname === "/productos/cargaMasiva"}
              >
                <ListItemIcon>
                  <CloudUploadIcon />
                </ListItemIcon>

                <ListItemText primary="Carga Masiva" />
              </ListItemButton>
            </Link>

          ) : null}

          <Link to={`productos/consultarKits`} style={{ textDecoration: "none" }}
            onClick={() => closeNav()}
          >

            <ListItemButton
              sx={{
                pl: 3,
                // backgroundColor: openProductos ? "#E8E8E8" : " white",
              }}
              selected={location.pathname === "/productos/consultarKits"}
            >
              <ListItemIcon>
                <LayersIcon />
              </ListItemIcon>
              <ListItemText primary="Kits" />
            </ListItemButton>
          </Link>

        </List>
      </Collapse>

      {/* promociones */}
      {/* comentar para la DEMO */}
      {/* <ListItemButton>
        <ListItemIcon>
          <StarsIcon />
        </ListItemIcon>
        <Link to={`promociones`} style={{ textDecoration: "none" }}>
          <ListItemText primary="Promociones" />
        </Link>
      </ListItemButton> */}

      {/* Contactos */}
      {/* comentar para la DEMO */}
      {/* <ListItemButton onClick={() => handleClickContact()}>
        <ListItemIcon>
          <ContactsIcon />
        </ListItemIcon>
        <ListItemText primary="Contacto" />
        {openContact ? <ExpandLess /> : <ExpandMore />}
      </ListItemButton>
      <Collapse in={openContact} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>
          <ListItemButton
            sx={{
              pl: 3,
            }} 
          >
            <ListItemIcon>
              <CorporateFareIcon />
            </ListItemIcon>
            <Link to={`cantacto/empresas`} style={{ textDecoration: "none" }}>
              <ListItemText primary="Empresas" />
            </Link>
          </ListItemButton>

          <ListItemButton
            sx={{
              pl: 3,
            }} 
          >
            <ListItemIcon>
              <RecentActorsIcon />
            </ListItemIcon>
            <Link to={`cantacto/clientes`} style={{ textDecoration: "none" }}>
              <ListItemText primary="Clientes" />
            </Link>
          </ListItemButton>
        </List>
      </Collapse> */}

      {/* ventas */}
      <Link to={`proveedores/registrar`} style={{
        textDecoration: "none",
        display: "flex",
      }}
        onClick={() => closeNav()}
      >
        <ListItemButton
          sx={{
            // backgroundColor: "white",
          }} /*onClick={() => handleClickAlmacenesInternosEntradas()}*/
          selected={location.pathname === "/proveedores/registrar"}
        >

          <ListItemIcon>
            <BusinessIcon />
          </ListItemIcon>
          <ListItemText primary="Proveedores"
            style={{
              color: theme.palette.text.primary,
            }} />
        </ListItemButton>
      </Link>


      {/* Almacenes */}

      <ListItemButton
        onClick={() => {
          if (!open) {
            setOpen(true);
          } handleClickAlmacenes()
        }}
      // sx={{ backgroundColor: " white" }}
      >
        <ListItemIcon>
          <WarehouseIcon />
        </ListItemIcon>
        <ListItemText primary="Almacenes" />
        {openAlmacenes ? <ExpandLess /> : <ExpandMore />}
      </ListItemButton>

      <Collapse in={openAlmacenes} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>
          {/* internos */}

          <ListItemButton
            sx={{
              pl: 3,
              // backgroundColor: openAlmacenesInternos ? "#DCDCDC" : "#E8E8E8",
            }}
            onClick={() => {
              if (!open) {
                setOpen(true);
                setOpenAlmacenes(true);
              } handleClickAlmacenesInternos()
            }}
            selected={location.pathname === "/almacenes/verInternos"}
          >
            <ListItemIcon>
              <HomeWorkIcon />
            </ListItemIcon>
            <ListItemText primary="Internos" />
            {openAlmacenesInternos ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>

          <Collapse in={openAlmacenesInternos} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>

              <Link
                to={`almacenes/verInternos`}
                style={{ textDecoration: "none", display: "flex" }}
                onClick={() => closeNav()}
              >
                <ListItemButton
                  sx={{
                    pl: 4,
                    // backgroundColor: openAlmacenesInternos
                    //   ? "#DCDCDC"
                    //   : "#E8E8E8",
                  }}
                  selected={location.pathname === "/almacenes/verInternos"}
                >

                  <ListItemIcon>
                    <InventoryIcon />
                  </ListItemIcon>

                  <ListItemText primary="Ver Internos" />
                </ListItemButton>
              </Link>


              <Link
                to={`almacenes/administrarUbicaciones`}
                style={{ textDecoration: "none", display: "flex" }}
                onClick={() => closeNav()}
              >
                <ListItemButton
                  sx={{
                    pl: 4,
                    // backgroundColor: openAlmacenesInternos ? "#DCDCDC" : "#E8E8E8",
                  }}
                  selected={location.pathname === "/almacenes/administrarUbicaciones"}
                >

                  <ListItemIcon>
                    <GiteIcon />
                  </ListItemIcon>

                  <ListItemText primary="Adm Ubicaciones" />
                </ListItemButton>
              </Link>


              {/* entradas */}

              <ListItemButton
                sx={{
                  pl: 4,
                  // backgroundColor: openAlmacenesInternosEntradas
                  //   ? "#D1D1D1"
                  //   : "#DCDCDC",
                }}
                onClick={() => {
                  if (!open) {
                    setOpen(true);
                    setOpenAlmacenes(true);
                    setOpenAlmacenesInternos(true);
                  } handleClickAlmacenesInternosEntradas()
                }}
              >
                <ListItemIcon>
                  <FileDownloadIcon />
                </ListItemIcon>
                <ListItemText primary="Entradas" />
                {openAlmacenesInternosEntradas ? (
                  <ExpandLess />
                ) : (
                  <ExpandMore />
                )}
              </ListItemButton>
              <Collapse
                in={openAlmacenesInternosEntradas}
                timeout="auto"
                unmountOnExit
              >
                <List component="div" disablePadding>

                  <Link
                    to={`facturas/manual`}
                    style={{ textDecoration: "none", display: "flex" }}
                    onClick={() => closeNav()}
                  >
                    <ListItemButton
                      sx={{
                        pl: 5,
                        // backgroundColor: "#D1D1D1",
                      }} /*onClick={() => handleClickAlmacenesInternosEntradas()}*/
                      selected={location.pathname === "/facturas/manual"}
                    >

                      <ListItemIcon>
                        <CreateIcon />
                      </ListItemIcon>

                      <ListItemText primary="Manual" />
                    </ListItemButton>
                  </Link>


                  <Link
                    to={`facturas/subir`}
                    style={{ textDecoration: "none", display: "flex" }}
                    onClick={() => closeNav()}

                  >
                    <ListItemButton
                      sx={{
                        pl: 6,
                        // backgroundColor: "#C6C6C6",
                      }} /*onClick={() => actualizarPedidos('todos')}*/
                      selected={location.pathname === "/facturas/subir"}
                    >

                      <ListItemIcon>
                        <DriveFolderUploadIcon />
                      </ListItemIcon>

                      <ListItemText primary="Subir" />
                    </ListItemButton>
                  </Link>


                  <Link
                    to={`facturas/consultar`}
                    style={{ textDecoration: "none", display: "flex" }}
                    onClick={() => closeNav()}

                  >
                    <ListItemButton
                      sx={{
                        pl: 6,
                        // backgroundColor: "#C6C6C6",
                      }} /*onClick={() => actualizarPedidos('todos')}*/
                      selected={location.pathname === "/facturas/consultar"}
                    >

                      <ListItemIcon>
                        <QueryStatsIcon />
                      </ListItemIcon>

                      <ListItemText primary="Consultar" />
                    </ListItemButton>
                  </Link>

                </List>
              </Collapse>

              {/* salidas */}
              {/* comentar para la DEMO */}
              {/* <ListItemButton
                sx={{
                  pl: 4,
                  // backgroundColor: openAlmacenesRemotosSalidas
                  //   ? "#D1D1D1"
                  //   : "#DCDCDC",
                }}
                onClick={() => handleClickAlmacenesRemotosSalidas()}
              >
                <ListItemIcon>
                  <FileUploadIcon />
                </ListItemIcon>
                <ListItemText primary="Salidas" />
                {openAlmacenesRemotosSalidas ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton> */}
              {/* <Collapse
                in={openAlmacenesRemotosSalidas}
                timeout="auto"
                unmountOnExit
              >
                <List component="div" disablePadding>
                  <ListItemButton
                    sx={{
                      pl: 5,
                    }}
                  >
                    <ListItemIcon>
                      <CreateIcon />
                    </ListItemIcon>
                    <Link
                      to={`facturas/manual`}
                      style={{ textDecoration: "none" }}
                    >
                      <ListItemText primary="Nueva Transferencia" />
                    </Link>
                  </ListItemButton>
                </List>
              </Collapse> */}

              {/* <List component="div" disablePadding>
                <ListItemButton sx={{ pl: 4, backgroundColor: openAlmacenesInternos ? "#DCDCDC" : "#E8E8E8"}} onClick={() => handleClickAlmacenesInternosSalidas()}>
                  <ListItemIcon>
                    <FileUploadIcon />
                  </ListItemIcon>
                  <ListItemText primary="Salidas" />
                  {openAlmacenesInternosSalidas ? <ExpandLess /> : <ExpandMore />}
                </ListItemButton>
              </List> */}
            </List>
          </Collapse>

          {/* remotos */}

          {/* <ListItemButton
            sx={{
              pl: 3,
              // backgroundColor: openAlmacenesInternos ? "#DCDCDC" : "#E8E8E8",
            }}
            onClick={() => handleClickAlmacenesRemotos()}
          >
            <ListItemIcon>
              <HouseSidingIcon />
            </ListItemIcon>
            <ListItemText primary="Remotos" />
            {openAlmacenesRemotos ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton> */}
          {/* 
comentar para la DEMO
          <Collapse in={openAlmacenesRemotos} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              <ListItemButton
                sx={{
                  pl: 4,
                  // backgroundColor: openAlmacenesRemotos ? "#DCDCDC" : "#E8E8E8",
                }}
              >
                <ListItemIcon>
                  <AddCircleIcon />
                </ListItemIcon>
                <Link
                  to={`almacenes/nuevaTransferencia`}
                  style={{ textDecoration: "none" }}
                >
                  <ListItemText primary="Transferencia" />
                </Link>
              </ListItemButton>
            </List>
          </Collapse> */}

          {/* Administrar */}

          {/* <ListItemButton
            sx={{
              pl: 3,
              // backgroundColor: openAdministrarAlmacenes ? "#DCDCDC" : "#E8E8E8",
            }}
            onClick={() => handleClickAdministrarAlmacenes()}
          >
            <ListItemIcon>
              <AdminPanelSettingsIcon />
            </ListItemIcon>
            <ListItemText primary="Administrar" />
            {openAdministrarAlmacenes ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton> */}

          <Collapse in={openAdministrarAlmacenes} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {/* comentar para la DEMO */}
              {/* <ListItemButton
                sx={{
                  pl: 4,
                  // backgroundColor: openVerAlmacenes ? "#DCDCDC" : "#E8E8E8",
                }}
                onClick={() => handleClickVerAlmacenes()}
              >
                <ListItemIcon>
                  <StorefrontIcon />
                </ListItemIcon>
                <ListItemText primary="Ver Almacenes" />
                {openVerAlmacenes ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton> */}

              {/* <Collapse in={openVerAlmacenes} timeout="auto" unmountOnExit>
              comentar para la DEMO
                <List component="div" disablePadding>
                  <ListItemButton
                    sx={{
                      pl: 5,
                      // backgroundColor: openVerAlmacenes ? "#DCDCDC" : "#E8E8E8",
                    }}
                  >
                    <ListItemIcon>
                      <AddCircleIcon />
                    </ListItemIcon>
                    <Link
                      to={`almacenes/ver`}
                      style={{ textDecoration: "none" }}
                    >
                      <ListItemText primary="Remoto" />
                    </Link>
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 5,
                      // backgroundColor: openVerAlmacenes ? "#DCDCDC" : "#E8E8E8",
                    }}
                  >
                    <ListItemIcon>
                      <AddCircleIcon />
                    </ListItemIcon>
                    <Link
                      to={`almacenes/ver`}
                      style={{ textDecoration: "none" }}
                    >
                      <ListItemText primary="Interno" />
                    </Link>
                  </ListItemButton>
                </List>
              </Collapse> */}

              {/* monitorear */}
              {/* comentar para la DEMO */}
              {/* <ListItemButton
                sx={{
                  pl: 4,
                  // backgroundColor: openAdministrarAlmacenes
                  //   ? "#DCDCDC"
                  //   : "#E8E8E8",
                }}
              >
                <ListItemIcon>
                  <MonitorHeartIcon />
                </ListItemIcon>
                <Link
                  to={`almacenes/monitorear`}
                  style={{ textDecoration: "none" }}
                >
                  <ListItemText primary="Monitorear" />
                </Link>
              </ListItemButton> */}
            </List>
          </Collapse>
        </List>
      </Collapse>

      {/* Administrar Cuentas */}
      {/* {role !== "Admin" ? ( */}
      <>
        <ListItemButton onClick={() => {
          if (!open) {
            setOpen(true);
          } handleClickCuentas()
        }}>
          <ListItemIcon>
            <AccountBoxIcon />
          </ListItemIcon>
          <ListItemText primary="Cuentas" />
          {openCuentas ? <ExpandLess /> : <ExpandMore />}
        </ListItemButton>
        <Collapse in={openCuentas} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            <List component="div" disablePadding>

              <Link
                to={`cuentas/administrar`}
                style={{ textDecoration: "none", display: "flex" }}
                onClick={() => closeNav()}

              >
                <ListItemButton
                  sx={{
                    pl: 3,
                    // backgroundColor: "#E8E8E8",
                  }}
                  selected={location.pathname === "/cuentas/administrar"}
                >

                  <ListItemIcon>
                    <ManageAccountsIcon />
                  </ListItemIcon>

                  <ListItemText primary="Administrar Cuentas" />
                </ListItemButton>
              </Link>


              <Link
                to={`cuentas/administrarClientes`}
                style={{ textDecoration: "none", display: "flex" }}
                onClick={() => closeNav()}

              >
                <ListItemButton
                  sx={{
                    pl: 3,
                    // backgroundColor: "#E8E8E8",
                  }}
                  selected={location.pathname === "/cuentas/administrarClientes"}
                >
                  <ListItemIcon>
                    <GroupIcon />
                  </ListItemIcon>
                  <ListItemText primary="Administrar Clientes" />
                </ListItemButton>
              </Link>

            </List>
          </List>
        </Collapse>
      </>
      {/* ) : null} */}

      {/* compañia */}
      <ListItemButton onClick={() => {
        if (!open) {
          setOpen(true);
        } handleClickCompany()
      }}>
        <ListItemIcon>
          <Business />
        </ListItemIcon>
        <ListItemText primary="Compañia" />
        {openCompany ? <ExpandLess /> : <ExpandMore />}
      </ListItemButton>
      <Collapse in={openCompany} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>

          <Link to={`compania/informacion`} style={{ textDecoration: "none" }}
            onClick={() => closeNav()}
          >
            <ListItemButton
              sx={{
                pl: 3,
                // backgroundColor: openCompany ? "#E8E8E8" : " white",
              }}
              selected={location.pathname === "/compania/informacion"}
            >
              <ListItemIcon>
                <DomainAdd />
              </ListItemIcon>
              <ListItemText primary="Información" />
            </ListItemButton>
          </Link>

          <Link to={`compania/informacion/bancaria`} style={{ textDecoration: "none" }}
            onClick={() => closeNav()}
          >
            <ListItemButton
              sx={{
                pl: 3,
                // backgroundColor: openCompany ? "#E8E8E8" : " white",
              }}
              selected={location.pathname === "/compania/informacion/bancaria"}
            >
              <ListItemIcon>
                <AccountBalance />
              </ListItemIcon>
              <ListItemText primary="Info Bancaria" />
            </ListItemButton>
          </Link>

        </List>
      </Collapse>

      {/* configuracion */}
      <Link to={`configuracion`} style={{ textDecoration: "none" }}
        onClick={() => closeNav()}
      >
        <ListItemButton selected={location.pathname === "/configuracion"}>
          <ListItemIcon>
            <SettingsIcon />
          </ListItemIcon>
          <ListItemText primary="Configuracion"
            style={{
              color: theme.palette.text.primary,
            }} />
        </ListItemButton>
      </Link>

    </List>
  );
};

export default Listas;
