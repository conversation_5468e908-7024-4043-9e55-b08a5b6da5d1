import { Icon<PERSON>utton, Table, TableContainer } from '@mui/material'
import { tableCellClasses } from '@mui/material/TableCell'
import { styled } from '@mui/material/styles'
import { TableBody, TableCell, TableHead, TableRow } from '@mui/material'
import { Paper, Typography, Box } from '@mui/material'
import { Visibility } from '@mui/icons-material'
import { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useDispatch } from 'react-redux'
import MapIcon from '@mui/icons-material/Map';
import React from 'react'
import StockVisual from './StockVisual'
import { colocarAlmacen, obtenerAlmacenesDesdeProveedores, obtenerStock } from '../../redux/almacenesDucks'
import { CircularProgress } from '@mui/material'
import { StyledTableCell, StyledTableRow } from '../StyledTableComponents'

const AlmacenesVisualizar = () => {
    const almacenes = useSelector((store) => store.almacenes.almacenes);
    const proveedor = useSelector((store) => store.almacenes.proveedor);  
    const RFC = useSelector((store) => store.almacenes.RFC);
    
    const dispatch = useDispatch();

    const GoToMaps = (url) => {
      if (url) {
        window.open(url, '_blank');
      }
    };
    const [loading, setLoading] = React.useState();
    const [selectedStock, setSelectedStock] = React.useState(null);
    const [selectedRow, setSelectedRow] = React.useState(null);
  
    const handleShowStock = async (almacen, index) => {
      if (isRowSelected(index)) {
        // Si la fila ya está seleccionada, deseleccionarla y ocultar la tabla de StockVisual
        setSelectedStock(null);
        setSelectedRow(null);
      } else {
        try {
          setLoading(true);
          // Obtener el stock mediante el dispatch
          const stockResponse = await new Promise((resolve, reject) => {
            dispatch(obtenerStock(almacen)).then(resolve).catch(reject);
          });
    
          setSelectedStock(stockResponse);
          setSelectedRow(index);
          setLoading(false);
        } catch (error) {
          console.log(error);
          setLoading(false);
          // Manejar el error aquí
        }
      }
    };
    
  
    const isRowSelected = (index) => {
      return index === selectedRow;
    };
  
    
    

    useEffect(() => {
      dispatch(obtenerAlmacenesDesdeProveedores(RFC));
    }, []);


    return (
      <>
        <Typography variant="h5" gutterBottom>Almacenes {proveedor}</Typography>
        <Box display="flex" justifyContent="center" sx={{marginBottom:'60px'}}>
          <TableContainer component={Paper} sx={{maxwidth:'85%'}}>
            <Table>
              <TableHead>
                <StyledTableRow>
                  <StyledTableCell align="center">Nombre</StyledTableCell>
                  <StyledTableCell align="center">Zona</StyledTableCell>
                  <StyledTableCell align="center">Dirección</StyledTableCell>
                  <StyledTableCell align="center">Teléfono</StyledTableCell>
                  <StyledTableCell align="center">Acciones</StyledTableCell>
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {almacenes.map((almacen, index) => (
                  <React.Fragment key={index}>
                    <StyledTableRow>
                      <StyledTableCell align="center">{almacen.storeName}</StyledTableCell>
                      <StyledTableCell align="center">{almacen.zone}</StyledTableCell>
                      <StyledTableCell align="center">{almacen.address}</StyledTableCell>
                      <StyledTableCell align="center">{almacen.phone}</StyledTableCell>
                      <StyledTableCell align="center">    
                        <IconButton
                          variant="outlined"
                          color="primary"
                          disabled={almacen.urlMaps === null}
                          onClick={() => GoToMaps(almacen.urlMaps)}
                        >
                          <MapIcon />
                        </IconButton>
                        {loading ? (
                          <CircularProgress size={20} color="primary" />
                        ) : (
                          <IconButton
                            variant="outlined"
                            color="primary"
                            disabled={loading}
                            onClick={() => handleShowStock(almacen.supplierStoreId, index)}
                          >
                            <Visibility />
                          </IconButton>
                        )}
                      </StyledTableCell>
                    </StyledTableRow>
                    {isRowSelected(index) && (
                      <StyledTableRow>
                        <TableCell colSpan={5}>
                          <StockVisual stock={selectedStock} />
                        </TableCell>
                      </StyledTableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </>
    );
    
}

export default AlmacenesVisualizar
