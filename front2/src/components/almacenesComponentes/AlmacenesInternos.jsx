import React from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useEffect } from "react";
import { useState } from "react";
import { Button, MenuItem, TableRow } from "@mui/material";
import { TableCell } from "@mui/material";
import {
  crearAlmacenInterno,
  deleteAlmacenInterno,
  limpiarMensajeAlmacen,
  modificarAlmacenInterno,
  obtenerAlmacenesInternos,
  obtenerStockInterno,
} from "../../redux/almacenesDucks";
import { Box } from "@mui/system";
import { TableContainer } from "@mui/material";
import { Paper } from "@mui/material";
import { Table } from "@mui/material";
import { TableHead } from "@mui/material";
import { TableBody } from "@mui/material";
import { IconButton } from "@mui/material";
import { Add, Delete, Edit, Map, Remove, Visibility } from "@mui/icons-material";
import StockVisual from "./StockVisual";
import { CircularProgress } from "@mui/material";
import { StyledTableCell, StyledTableRow } from "../StyledTableComponents";
import { Dialog, DialogTitle, DialogContent, DialogActions, TextField } from "@mui/material";
import ModalConfirmacionBorrar from "../componentesGenerales/ModalConfirmacionBorrar";
import { useCookies } from "react-cookie";
import InfoIcon from '@mui/icons-material/Info';
import { useTheme, useMediaQuery, Tooltip, Typography, Grid } from "@mui/material";
import CustomDialog from "../componentesGenerales/CustomDialog";
import AddAlmacen from "./ubicacionesInternasComponentes/AddAlmacen";
import { EditAlmacen } from "./ubicacionesInternasComponentes/EditAlmacen";
import ViewAlmacen from "./ubicacionesInternasComponentes/ViewAlamecen";
import { AlertComponent } from "../componentesGenerales/Alert";


export const AlmacenesInternos = () => {
  const [almacenes, setAlmacenes] = useState([]);
  const [createStoreOpen, setCreateStoreOpen] = useState(false);
  const [openEditStore, setOpenEditStore] = useState(false);
  const [openDeleteStore, setOpenDeleteStore] = useState(false);
  const [selectedStock, setSelectedStock] = React.useState(null);
  const [selectedRow, setSelectedRow] = React.useState(null);
  const [selectedId, setSelectedId] = React.useState(null);
  const [loading, setLoading] = React.useState();
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const almacen = useSelector((store) => store.almacenes.almacenes);
  const mensaje = useSelector((store) => store.almacenes.mensaje);
  const [messageLocal, setMessageLocal] = useState(null);
  const severity = useSelector((store) => store.almacenes.severity);
  const zonas = useSelector((store) => store.almacenes.zones);
  const [data, setData] = useState(
    {
      name: "",
      description: "",
      urlMaps: "",
      address: "",
      phone: "",
      zoneNumber: ""
    }
  );
  const [originalData, setOriginalData] = useState(null);

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md"));

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedAlmacen, setSelectedAlmacen] = useState(null);

  const isRowSelected = (index) => {
    return index === selectedRow;
  };

  useEffect(() => {
    dispatch(obtenerAlmacenesInternos());
  }, []);

  useEffect(() => {
    setAlmacenes(almacen);
  }, [almacen]);

  const handleShowStock = async (almacen, index) => {
    // if (isRowSelected(index)) {
    if (selectedId === almacen) {
      // Si la fila ya está seleccionada, deseleccionarla y ocultar la tabla de StockVisual
      setSelectedStock(null);
      setSelectedId(null);
    } else {
      try {
        setLoading(true);
        setSelectedId(almacen);

        // Obtener el stock mediante el dispatch
        const stockResponse = await new Promise((resolve, reject) => {
          dispatch(obtenerStockInterno(almacen)).then(resolve).catch(reject);
        });

        setSelectedStock(stockResponse);
        setSelectedRow(index);
        setLoading(false);
      } catch (error) {
        console.log(error);
        setLoading(false);
        // Manejar el error aquí
      }
    }
  };

  const handleResetData = () => {
    setData({
      name: "",
      description: "",
      urlMaps: "",
      address: "",
      phone: "",
      zoneNumber: ""
    });
    setSelectedId(null);
    setOpenDeleteStore(false);
    setOpenEditStore(false);
  };

  const handleCreateStore = () => {
    dispatch(crearAlmacenInterno(data, cookies.csrf_access_token));
  };

  const handleEditStore = (almaceen) => {
    if(!almaceen?.id) {
      setMessageLocal("Error en el almacen");
      return
    }
    const newData = {
      name: almaceen.storeName,
      description: almaceen.storeDescription,
      urlMaps: almaceen.urlMaps,
      address: almaceen.address,
      phone: almaceen.phone,
      zoneNumber: almaceen.zone.zoneNumber
    };
    setData(newData);
    setOriginalData(newData);
    setOpenEditStore(true);
    setSelectedId(almaceen.id);
  };

  const sendEditStore = () => {
    dispatch(modificarAlmacenInterno(selectedId, data, cookies.csrf_access_token));
  };

  const handleOpenDeleteStore = (almaceen) => {
    if(!almaceen?.id) {
      setMessageLocal("Error en el almacen");
      return
    }
    setOpenDeleteStore(true);
    setSelectedId(almaceen.id);
  };

  const sendDeleteStore = () => {
    if(!selectedId) {
      setMessageLocal("Error en el almacen");
      return
    }
    dispatch(deleteAlmacenInterno(selectedId, cookies.csrf_access_token));
    severity === "success" && mensaje ?
      handleResetData() : console.log("Error al eliminar");
  };

  const handleShowMap = (almacen, index) => {
    almacen.urlMaps ? window.open(almacen.urlMaps, "_blank") : console.log("No hay URL");
  };

  const handleOpenDetails = (almacen) => {
    setSelectedAlmacen(almacen);
    setOpenDialog(true);
  };

  const handleCloseDetails = () => {
    setOpenDialog(false);
  };


  useEffect(() => {
    if( severity === "success" && mensaje && mensaje != "" ) {
      handleResetData();
      setOpenEditStore(false);
      setCreateStoreOpen(false);
      
      dispatch(limpiarMensajeAlmacen());
    }else{
      if(mensaje && mensaje != ""){
        setMessageLocal(mensaje);
      }
      
    }

  }, [mensaje]);
  console.log(almacenes, almacen)

  return (
    <>
      {loading ? (
        <CargandoLista />
      ) : almacenes?.length > 0 ? (
        <>
          <Box display="flex" justifyContent="end" sx={{ marginBottom: "10px" }}>
            <Button variant="contained" color="buttonGreenPink" onClick={() => {
              handleResetData();

              setCreateStoreOpen(true);
            }}>
              <Add />
              Agregar Almacén
            </Button>
          </Box>
          <Box display="flex" justifyContent="center" sx={{ marginBottom: "60px" }}>
            <TableContainer component={Paper}
              sx={{ width: "95%", borderRadius: "25px" }}

            >
              <Table>
                <TableHead>
                  <StyledTableRow>
                    <StyledTableCell align="center">Nombre</StyledTableCell>
                    {!isSmallScreen && <StyledTableCell align="center">Zona</StyledTableCell>}
                    {!isMediumScreen && <StyledTableCell align="center">Dirección</StyledTableCell>}
                    {!isSmallScreen && <StyledTableCell align="center">Teléfono</StyledTableCell>}
                    <StyledTableCell align="center">Acciones</StyledTableCell>
                  </StyledTableRow>
                </TableHead>

                <TableBody>
                  {almacenes?.map((almacen, index) => (
                    <React.Fragment key={index}>
                      <StyledTableRow>
                        <StyledTableCell align="center">{almacen.storeName}</StyledTableCell>
                        {!isSmallScreen && (
                          <StyledTableCell align="center">
                            <p>{"Zona: "}{almacen.zone.zoneNumber}</p>
                            <p>{"Envío: "}{zonas?.find((zona) => zona.zoneNumber === almacen.zone.zoneNumber)?.deliveryTime}</p>
                          </StyledTableCell>
                        )}
                        {!isMediumScreen && (
                          <StyledTableCell align="center">{almacen.address}</StyledTableCell>
                        )}
                        {!isSmallScreen && (
                          <StyledTableCell align="center">{almacen.phone}</StyledTableCell>
                        )}
                        <StyledTableCell align="center">
                          <Box sx={{ display: 'flex', justifyContent: 'center', gap: '5px', flexWrap: 'wrap' }}>
                            {isSmallScreen && (
                              <Tooltip title="Ver detalles">
                                <IconButton
                                  variant="contained"
                                  color="colorGreenPink"
                                  size="small"
                                  onClick={() => handleOpenDetails(almacen)}
                                >
                                  <InfoIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            <IconButton
                              variant="outlined"
                              color="colorGreenPink"
                              disabled={loading}
                              onClick={() => handleShowMap(almacen, index)}
                            >
                              <Map />
                            </IconButton>
                            <IconButton
                              variant="outlined"
                              color="colorGreenPink"
                              disabled={loading}
                              onClick={() => {
                                handleEditStore(almacen);
                              }}
                            >
                              <Edit />
                            </IconButton>
                            <IconButton
                              variant="outlined"
                              color="colorGreenPink"
                              disabled={loading}
                              onClick={() => {
                                handleOpenDeleteStore(almacen);
                              }}
                            >
                              <Delete />
                            </IconButton>
                          </Box>
                        </StyledTableCell>
                      </StyledTableRow>
                      {selectedId === almacen.storeId && (
                        <StyledTableRow>
                          <TableCell colSpan={5}>
                            <StockVisual stock={selectedStock} />
                          </TableCell>
                        </StyledTableRow>
                      )}
                    </React.Fragment>
                  ))}
                </TableBody>

              </Table>
            </TableContainer>
          </Box>


          <AddAlmacen createStoreOpen={createStoreOpen} setCreateStoreOpen={setCreateStoreOpen} data={data} setData={setData} handleCreateStore={handleCreateStore} zonas={zonas} />


          <EditAlmacen
            openEditStore={openEditStore}
            setOpenEditStore={setOpenEditStore}
            data={data}
            setData={setData}
            sendEditStore={sendEditStore}
            zonas={zonas}
            originalData={originalData}
            setMessageLocal={setMessageLocal}
          />


          <ModalConfirmacionBorrar open={openDeleteStore} setOpen={setOpenDeleteStore} deleteFunction={sendDeleteStore} />

          <ViewAlmacen openDialog={openDialog} setOpenDialog={setOpenDialog} selectedAlmacen={selectedAlmacen} zonas={zonas} themeBreak={isSmallScreen} handleCloseDetails={handleCloseDetails} />

        </>
      ) : (
        <h1>No hay almacenes internos</h1>
      )}

      { messageLocal && messageLocal != "" &&
         <AlertComponent color={severity} message={messageLocal} cleanMessage={() => setMessageLocal(null)} time={3000}/>
      }

    </>
  );
};

export default AlmacenesInternos;
