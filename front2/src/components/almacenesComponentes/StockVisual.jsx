import React from "react";
import {
  Table,
  TableContainer,
  TableBody,
  TableCell,
  Button,
} from "@mui/material";
import { TableHead, TableRow } from "@mui/material";
import { Paper, Box } from "@mui/material";
import { styled } from "@mui/material/styles";
import { tableCellClasses } from "@mui/material/TableCell";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useState } from "react";
import { StyledTableCell,StyledTableRow } from "../StyledTableComponents";

const StockVisual = (stock) => {
  const [products, setProducts] = React.useState([]);
  const origen = useSelector((store) => store.almacenes.origen);

  useEffect(() => {
    if (stock) {
      if (origen === "proveedor") {
        setProducts(stock.stock.productsInSupplierStore);
      } else if (origen === "interno") {
        setProducts(stock?.stock?.productsInStore);
      }
    }
  }, [stock, origen]);

  return (
    <>
      {origen === "proveedor" ? (
        <Box
          display="flex"
          justifyContent="center"
          sx={{ marginBottom: "60px" }}
        >
          <TableContainer component={Paper} sx={{ maxwidth: "85%" }}>
            <Table>
              <TableHead>
                <StyledTableRow>
                  <StyledTableCell align="center">SKU</StyledTableCell>
                  <StyledTableCell align="center">Marca</StyledTableCell>
                  <StyledTableCell align="center">Modelo</StyledTableCell>
                  <StyledTableCell align="center">Condición</StyledTableCell>
                  <StyledTableCell align="center">Descripción</StyledTableCell>
                  <StyledTableCell align="center">Código SAT</StyledTableCell>
                  <StyledTableCell align="center">
                    Código de Unidad
                  </StyledTableCell>
                  <StyledTableCell align="center">UPC</StyledTableCell>
                  <StyledTableCell align="center">Stock</StyledTableCell>
                  <StyledTableCell align="center">Precio</StyledTableCell>
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {products.map((producto, index) => (
                  <StyledTableRow key={index}>
                    <StyledTableCell align="center">
                      {producto.product.internalSku}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.product.productBase.brand}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.product.productBase.model}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.product.productBase.condition}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.product.productBase.description}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.product.productBase.satCode}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.product.productBase.unitCode}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.product.productBase.upc}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.stock}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.cost}
                    </StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      ) : origen === "interno" ? (
        <Box
          display="flex"
          justifyContent="center"
          sx={{ marginBottom: "60px" }}
        >
          <TableContainer component={Paper} sx={{ maxwidth: "85%" }}>
            <Table>
              <TableHead>
                <StyledTableRow>
                  <StyledTableCell align="center">SKU</StyledTableCell>
                  <StyledTableCell align="center">
                    Ubicación Interna
                  </StyledTableCell>
                  <StyledTableCell align="center">Stock</StyledTableCell>
                  <StyledTableCell align="center">Precio</StyledTableCell>
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {products?.map((producto, index) => (
                  <StyledTableRow key={index}>
                    <StyledTableCell align="center">
                      {producto.product.internalSku}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.internalLocation}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.stock}
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      {producto.cost}
                    </StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      ) : null}
    </>
  );
};

export default StockVisual;
