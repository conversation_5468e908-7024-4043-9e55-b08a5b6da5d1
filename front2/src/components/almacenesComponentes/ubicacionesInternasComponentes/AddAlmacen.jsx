import CustomDialog from "../../componentesGenerales/CustomDialog";
import { Box, Button, MenuItem, TextField, Grid } from "@mui/material";


export const AddAlmacen = ({ createStoreOpen, setCreateStoreOpen, data, setData, handleCreateStore, zonas }) => {
  return (
    <CustomDialog
    open={createStoreOpen}
    onClose={() => setCreateStoreOpen(false)}
    title="Crear Almacén"
    maxWidth="sm"
    maxHeight="80vh"
    width="100%"
    actions={
      <>
        <Button
          color="secondary"
          onClick={() => setCreateStoreOpen(false)}
        >
          Cancelar
        </Button>
        <Button
          onClick={() => handleCreateStore()}
          color="colorGreenPink"
        >
          Crear
        </Button>
      </>
    }
  >
    <Box sx={{ p: 2 }}>
      <Grid container spacing={2} justifyContent="center">
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="Nombre"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.name}
            onChange={(e) => setData({ ...data, name: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="Descripción"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.description}
            onChange={(e) => setData({ ...data, description: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="URL Maps"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.urlMaps}
            onChange={(e) => setData({ ...data, urlMaps: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="Dirección"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.address}
            onChange={(e) => setData({ ...data, address: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="Teléfono"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.phone}
            onChange={
              (e) => {
                const isPhoneValid = /^(\+)?(\d+)?$/.test(e.target.value);
                if (isPhoneValid)
                  setData({ ...data, phone: e.target.value });
              }
            }
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            select
            label="Zona"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.zoneNumber}
            onChange={(e) => setData({ ...data, zoneNumber: e.target.value })}
          >
            {zonas?.map((zona, index) => (
              <MenuItem key={index} value={zona.zoneNumber}>
                {"Zona: "}{zona.zoneNumber}{" - Envío: "}{zona.deliveryTime}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      </Grid>
    </Box>
    
  </CustomDialog>
  );
};

export default AddAlmacen;

