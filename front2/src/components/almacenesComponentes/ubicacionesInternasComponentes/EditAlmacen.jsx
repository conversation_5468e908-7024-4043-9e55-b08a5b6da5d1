import CustomDialog from "../../componentesGenerales/CustomDialog";
import { Box, Button, MenuItem, TextField, Grid } from "@mui/material";

export const EditAlmacen = ({ openEditStore, setOpenEditStore, data, setData, sendEditStore, zonas,originalData, setMessageLocal }) => {

      // Función para comparar los datos
  const isDataChanged = () => {
    return JSON.stringify(data) !== JSON.stringify(originalData);
  };

  const handleSave = () => {
    if (isDataChanged()) {
      sendEditStore();
    } else {
      setMessageLocal("No hay cambios para guardar");
    }
  };


  return (
    <CustomDialog
    open={openEditStore}
    onClose={() => setOpenEditStore(false)}
    title="Editar Almacén"
    maxWidth="sm"
    width="100%"
    maxHeight="80vh"
    actions={
      <>
        <Button color="secondary" onClick={() => setOpenEditStore(false)}>
          Cancelar
        </Button>
        <Button onClick={() => handleSave()} color="colorGreenPink">
          Guardar
        </Button>
      </>
    }
  >
    <Box sx={{ p: 2 }}>
      <Grid container spacing={2} justifyContent="center">
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="Nombre"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.name}
            onChange={(e) => setData({ ...data, name: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="Descripción"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.description}
            onChange={(e) => setData({ ...data, description: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="URL Maps"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.urlMaps}
            onChange={(e) => setData({ ...data, urlMaps: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="Dirección"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.address}
            onChange={(e) => setData({ ...data, address: e.target.value })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            label="Teléfono"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.phone}
            onChange={
              (e) => {
                const isPhoneValid = /^(\+)?(\d+)?$/.test(e.target.value);
                if (isPhoneValid)
                  setData({ ...data, phone: e.target.value });
              }
            }
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <TextField
            select
            label="Zona"
            fullWidth
            margin="dense"
            variant="outlined"
            value={data.zoneNumber}
            onChange={(e) => setData({ ...data, zoneNumber: e.target.value })}
          >
            {zonas?.map((zona, index) => (
              <MenuItem key={index} value={zona.zoneNumber}>
                {"Zona: "}{zona.zoneNumber}{" - Envío: "}{zona.deliveryTime}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      </Grid>
    </Box>
  </CustomDialog>
  );
};

export default EditAlmacen;

