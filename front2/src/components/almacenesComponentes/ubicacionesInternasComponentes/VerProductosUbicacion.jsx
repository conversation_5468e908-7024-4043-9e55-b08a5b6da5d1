import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Box,
  Select,
  TextField,
  Alert,
  Tooltip,
  useMediaQuery,
  Typography,
} from "@mui/material";
import { StyledTableCell, StyledTableRow } from "../../StyledTableComponents";
import { MoveUp } from "@mui/icons-material";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  Input,
  InputLabel,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import MenuItem from "@mui/material/MenuItem";
import {
  limpiarMensajeStock,
  limpiarSelectedStock,
  moverStock,
} from "../../../redux/almacenesDucks";
import CloseIcon from "@mui/icons-material/Close";
import { useCookies } from "react-cookie";
import { AlertComponent } from "../../componentesGenerales/Alert";
import { NumericFormat } from "react-number-format";
import { useTheme } from "@emotion/react";

export const VerProductosUbicacion = (props) => {
  const { stock } = props;
  const [ubicacionNueva, setUbicacionNueva] = React.useState(null);
  const [open, setOpen] = React.useState(false);
  const locationsList = useSelector(
    (store) => store.almacenes.storeLocationsList
  );
  const [selectedProduct, setSelectedProduct] = React.useState(null);
  const [stockToMove, setStockToMove] = React.useState(null);
  const mensaje = useSelector((store) => store.almacenes.mensajeStock);
  const severity = useSelector((store) => store.almacenes.severityStock);
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();

  const handleSendInfo = () => {
    const dataToSend = {
      internalSku: selectedProduct.product_store.product.internalSku,
      originInternalLocation: selectedProduct.locationLevelItemId,
      destinationInternalLocation: ubicacionNueva,
      stockToMove: stockToMove,
    };
    dispatch(moverStock(dataToSend, cookies.csrf_access_token));
  };

  React.useEffect(() => {
    if (mensaje && severity === "success") {
      setTimeout(() => {
        dispatch(limpiarSelectedStock());
        dispatch(limpiarMensajeStock());
      }, 2000);
    }
  }, [mensaje]);

  const theme = useTheme();
const isMobile = useMediaQuery(theme.breakpoints.down("sm"));


  return (
    <>
      {stock.length < 1 ? (
        <h3>No hay productos en esta ubicación</h3>
      ) : (
        <>
        
        <Box sx={{ width: "100%", display: "flex", flexDirection: "column", gap: 2 }}>
  {isMobile ? (
    // Vista móvil tipo tarjeta vertical
    stock.map((product) => (
      <Paper
        key={product.product_store.product.internalSku}
        sx={{
          p: 2,
          borderRadius: 2,
        }}
      >
        <Typography variant="body1" fontWeight="bold">
          SKU Interno:
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          {product.product_store.product.internalSku}
        </Typography>

        <Typography variant="body1" fontWeight="bold">
          Cantidad:
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          {product.stock}
        </Typography>

        <Box>
          <Tooltip title="Mover stock">
            <IconButton
              onClick={() => {
                setOpen(true);
                setSelectedProduct(product);
              }}
            >
              <MoveUp color="buttonGreenPink" />
            </IconButton>
          </Tooltip>
        </Box>
      </Paper>
    ))
  ) : (
    // Vista de escritorio: tabla normal
    <TableContainer
      component={Paper}
      sx={{
        borderRadius: "20px",
        overflowX: "auto",
        boxShadow: 3,
      }}
    >
      <Table
        aria-label="tabla de stock"
        sx={{
          minWidth: 650,
          tableLayout: "auto",
        }}
      >
        <TableHead>
          <StyledTableRow>
            <StyledTableCell align="center">SKU interno</StyledTableCell>
            <StyledTableCell align="center">Cantidad</StyledTableCell>
            <StyledTableCell align="center">Opciones</StyledTableCell>
          </StyledTableRow>
        </TableHead>
        <TableBody>
          {stock.map((product) => (
            <StyledTableRow key={product.product_store.product.internalSku}>
              <StyledTableCell align="center">
                {product.product_store.product.internalSku}
              </StyledTableCell>
              <StyledTableCell align="center">{product.stock}</StyledTableCell>
              <StyledTableCell align="center">
                <Tooltip title="Mover stock">
                  <IconButton
                    onClick={() => {
                      setOpen(true);
                      setSelectedProduct(product);
                    }}
                  >
                    <MoveUp color="buttonGreenPink" />
                  </IconButton>
                </Tooltip>
              </StyledTableCell>
            </StyledTableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  )}
</Box>

          <Dialog
            open={open}
            onClose={() => {
              setOpen(false);
              setUbicacionNueva(null);
              dispatch(limpiarMensajeStock());
            }}
            PaperProps={{
              classes: {
                width: "200px",
                height: "200px",
              },
            }}
          >
            <DialogTitle>Elige la nueva ubicación del producto</DialogTitle>
            <DialogContent>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  width: "100%",
                }}
              >
                <TextField
                  select
                  sx={{ width: "100%", marginTop: "10px" }}
                  id="ubicacion-nueva"
                  title="Ubicación"
                  label="Ubicación"
                  value={ubicacionNueva ? ubicacionNueva : ""}
                  onChange={(e) => setUbicacionNueva(e.target.value)}
                >
                  {locationsList.map((location) => (
                    <MenuItem key={location.id} value={location.id}>
                      {location.entireName}
                    </MenuItem>
                  ))}
                </TextField>
                {/* <TextField
                  sx={{ width: "100%", marginTop: "5%" }}
                  label="Cantidad a mover"
                  type="number"
                  value={stockToMove}
                  onChange={(e) => {
                    e.target.value < 0
                      ? setStockToMove(0)
                      : e.target.value > selectedProduct.stock
                      ? null
                      : setStockToMove(e.target.value);
                  }}
                /> */}
                <NumericFormat
                  sx={{ width: "100%", marginTop: "5%" }}
                  label="Cantidad a mover"
                  value={stockToMove}
                  onValueChange={(values) => {
                    const { formattedValue, value } = values;
                    setStockToMove(value);
                  }}
                  customInput={TextField}
                  thousandSeparator
                  decimalScale={0}
                  allowNegative={false}
                  isNumericString
                  decimalSeparator="."
                  fixedDecimalScale={true}
                  prefix=""
                  variant="outlined"
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => {
                  setOpen(false);
                  setUbicacionNueva(null);
                }}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleSendInfo}
                disabled={ubicacionNueva === null}
              >
                Guardar
              </Button>
            </DialogActions>
          </Dialog>
          {mensaje && (
            <AlertComponent
              color={severity}
              message={mensaje}
              cleanMessage={limpiarMensajeStock}
            />
          )}
        </>
      )}
    </>
  );
};

export default VerProductosUbicacion;
