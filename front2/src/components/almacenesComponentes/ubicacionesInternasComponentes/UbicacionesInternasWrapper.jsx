import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  obtenerAlmacenesInternosStock,
  obtenerUbicacionesInternas,
} from "../../../redux/almacenesDucks";
import { useEffect } from "react";
import CargandoLista from "../../CargandoLista";
import { useCookies } from "react-cookie";
import { Box, Card, CardContent, Typography, Grid, Paper } from "@mui/material";
import ListaDeUbicaciones from "./ListaDeUbicaciones";
import ConfiguracionDeUbicacion from "./ConfiguracionDeUbicacion";
import VerProductosUbicacion from "./VerProductosUbicacion";

const UbicacionesInternasWrapper = () => {
  const [cookies, setCookie] = useCookies();
  const internalLocations = useSelector((store) => store.almacenes.internalLocations);
  const [data, setData] = React.useState([]);
  const dispatch = useDispatch();
  const loading = useSelector((state) => state.almacenes.loading);

  useEffect(() => {
    dispatch(obtenerUbicacionesInternas());
  }, []);

  useEffect(() => {
    if (internalLocations?.almacenes?.length > 0) {
      setData(internalLocations.almacenes);
    }
  }, [internalLocations]);

  return (
    <>
      {loading ? (
        <>
        <CargandoLista />
        </>
      ) : data?.length > 0 ? (
        <UbicacionesInternas />
      ) : (
        <Typography variant="h4" align="center" sx={{ mt: 4, color: 'text.secondary' }}>
          No hay almacenes internos
        </Typography>
      )}
    </>
  );
};

const UbicacionesInternas = () => {
  const selectedLocation = useSelector(
    (store) => store.almacenes.selectedLocation
  );
  const selectedStock = useSelector((store) => store.almacenes.selectedStock);
  
  return (
    <Box sx={{ p: 3, width: "100%" }}>
      <Grid container spacing={3}>
        <Grid item xss={12} md={4}>
          <Card 
            elevation={3}
            sx={{
              minHeight: {
                xss: "150px",
                md: "300px",
              },
              height: '100%',
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: (theme) => theme.palette.background.default,
                borderRadius: '10px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: (theme) => theme.palette.primary.main,
                borderRadius: '10px',
                '&:hover': {
                    background: (theme) => theme.palette.primary.dark,
                },
              },
              padding: '1em',
              borderRadius: '20px',
            }}
          >
            <CardContent>

              <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
                Ubicaciones
              </Typography>

              <ListaDeUbicaciones />
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xss={12} md={8}>
         
              {selectedLocation && <ConfiguracionDeUbicacion />}
              {selectedStock && <VerProductosUbicacion stock={selectedStock} />}

              {!selectedLocation && !selectedStock && (
                <Typography variant="h6" align="center" sx={{ mt: 4, color: 'text.secondary' }}>
                  
                </Typography>
              )}


        </Grid>
      </Grid>
    </Box>
  );
};

export default UbicacionesInternasWrapper;
