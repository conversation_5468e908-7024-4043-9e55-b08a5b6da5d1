 import CustomDialog from "../../componentesGenerales/CustomDialog";
import { Box, Typography, Grid } from "@mui/material";

export const ViewAlmacen = ({ openDialog, setOpenDialog, selectedAlmacen, zonas , themeBreak, handleCloseDetails}) => {
 

    return (
        <CustomDialog
        open={openDialog}
        onClose={handleCloseDetails}
        title="Detalles del Almacén"
        maxWidth="sm"
        width="100%"
      >
        {selectedAlmacen && (
          <Box sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {selectedAlmacen.storeName}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" color="text.secondary">
                  Zona: {selectedAlmacen.zone.zoneNumber}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" color="text.secondary">
                  Envío:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {zonas?.find((zona) => zona.zoneNumber === selectedAlmacen.zone.zoneNumber)?.deliveryTime}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" color="text.secondary">
                  Dirección:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {selectedAlmacen.address}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" color="text.secondary">
                  Teléfono:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {selectedAlmacen.phone}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        )}
      </CustomDialog>
      );
};

export default ViewAlmacen;