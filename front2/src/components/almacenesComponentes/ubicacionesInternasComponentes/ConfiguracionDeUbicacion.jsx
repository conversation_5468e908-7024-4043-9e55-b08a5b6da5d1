import { Description, Edit, Handyman, Info, Storage, Straighten } from '@mui/icons-material';
import { <PERSON><PERSON>, Box, Button, Card, CardContent, Divider, IconButton, TextField, Typography } from '@mui/material';
import React from 'react'
import { useDispatch, useSelector } from 'react-redux';
import CloseIcon from '@mui/icons-material/Close';
import { limpiarMensaje, modificarUbicacion } from '../../../redux/almacenesDucks'
import { useCookies } from 'react-cookie';
import { Chip } from "@mui/material";
import { Close } from "@mui/icons-material";


const ConfiguracionDeUbicacion = () => {
  const selectedLocation = useSelector((store) => store.almacenes.selectedLocation);
  const [editing, setEditing] = React.useState(false);
  const [locationName, setLocationName] = React.useState('');
  const mensaje = useSelector((store) => store.almacenes.mensaje);
  const severity = useSelector((store) => store.almacenes.severity);
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();

  React.useEffect(() => {
    dispatch(limpiarMensaje());
  }, [selectedLocation]);

  const handleModifyLocation = () => {
    const idUbicacion = selectedLocation.id;
    const data = {
      name: locationName,
      storeId: selectedLocation.storeId,
      locationLevelTypeId: selectedLocation.locationLevelTypeId,
      locationLevelItemParentId: selectedLocation.parentId,
    };
    dispatch(modificarUbicacion(idUbicacion, data, cookies.csrf_access_token));
    setEditing(false);
  };

  console.log(selectedLocation);

  return (
<Box sx={{ display: "flex", flexDirection: "column", gap: 3, width: "100%" }}>
  {/* Título */}
  {/* <Typography variant="h6" fontWeight="bold" color="primary" sx={{ mb: 1, fontSize:"1rem" }}>
    Configuración De Ubicación
  </Typography> */}

  {/* Contenedor principal: responsivo */}
  <Box
    sx={{
      display: "flex",
      flexWrap: "wrap",
      gap: 3,
      justifyContent: { xs: "center", md: "flex-start" },
    }}
  >
    {/* Información Principal */}
    <Card
      elevation={3}
      sx={{
        minWidth: {
          xss: "150px",
          md: "250px",
        },
        flex: "1 1 300px",
        borderRadius: 3,
        p: 2,
      }}
    >
      <CardContent>
        <Typography variant="h6" fontWeight="bold" >
          <Info fontSize="small" sx={{ mr: 1 }} />
          Información Principal
        </Typography>

        <Typography variant="subtitle1" sx={{ mt: 1 }}>
          {selectedLocation?.entireName}
        </Typography>
        <Typography variant="body2" color="gray">
          ID: {selectedLocation?.id}
        </Typography>

        <Divider sx={{ my: 2, borderColor: "#444" }} />

        {/* Nombre editable */}
        {editing ? (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <input
              type="text"
              value={locationName}
              onChange={(e) => setLocationName(e.target.value)}
              style={{
                padding: "8px",
                borderRadius: "5px",
                border: "1px solid #555",
                backgroundColor: "#2c2c2c",
                color: "#fff",
              }}
            />
            <Box sx={{ display: "flex", gap: 1 }}>
              <Button onClick={() => setEditing(false)} color="error" variant="outlined">
                Cancelar
              </Button>
              <Button onClick={handleModifyLocation} disabled={locationName.length < 3} variant="contained">
                Guardar
              </Button>
            </Box>
          </Box>
        ) : (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography fontWeight="bold">Nombre: {selectedLocation?.name}</Typography>
            <IconButton onClick={() => setEditing(true)} size="small" color="primary">
              <Edit fontSize="small" />
            </IconButton>
          </Box>
        )}


        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" >
            Tipo y Nivel
          </Typography>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Chip label={selectedLocation?.locationLevelType.name} sx={{ mt: 1}} />

        <Chip
          label={`Nivel ${selectedLocation?.locationLevelType.locationLevel.level}`}
          sx={{ mt: 1, fontWeight: "bold" }}
        />
        </Box>
        </Box>
      </CardContent>
    </Card>

    {/* Detalles Adicionales */}
    <Card
      elevation={3}
      sx={{
        minWidth: "250px",
        flex: "1 1 250px",
        borderRadius: 3,
        p: 2,
      }}
    >
      <CardContent>
        <Typography variant="h6" fontWeight="bold" color="secondary">
          <Info fontSize="small" sx={{ mr: 1 }} />
          Detalles Adicionales
        </Typography>

        <Typography variant="body2" sx={{ mt: 1 }}>
          Ubicación Seleccionada
        </Typography>
        <Typography variant="h6">
          {selectedLocation?.id}
        </Typography>

        <Typography variant="body2" sx={{ mt: 2 }}>
          Estado
        </Typography>
        <Chip label="Activo" sx={{ backgroundColor: "#00c853", color: "#fff", fontWeight: "bold", mt: 1 }} />
      </CardContent>
    </Card>
  </Box>

  {/* Descripción del Nivel */}
  <Card
    elevation={3}
    sx={{
      borderRadius: 3,
      p: 2,
    }}
  >
    <CardContent>
      <Typography variant="h6" fontWeight="bold" >
        <Description fontSize="small" sx={{ mr: 1 }} />
        Descripción del Nivel
      </Typography>

      <Typography variant="body1" sx={{ mt: 2 }}>
        {selectedLocation?.locationLevelType.description ? selectedLocation?.locationLevelType.description : "No hay descripción"}
      </Typography>
    </CardContent>
  </Card>

  {/* Alertas */}
  {mensaje && (
    <Alert
      severity={severity}
      sx={{ mt: 2 }}
      action={
        <IconButton aria-label="close" color="inherit" size="small" onClick={() => dispatch(limpiarMensaje())}>
          <Close fontSize="inherit" />
        </IconButton>
      }
    >
      {mensaje}
    </Alert>
  )}
</Box>



  )
}

export default ConfiguracionDeUbicacion
