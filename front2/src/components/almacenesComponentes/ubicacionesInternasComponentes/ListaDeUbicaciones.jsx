import React, { useState } from "react";
import {
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  IconButton,
  TextField,
  Skeleton,
  Alert,
} from "@mui/material";
import RenderizaUbicaciones from "./RenderizaUbicaciones";
import { useDispatch, useSelector } from "react-redux";
import {
  cambiarSelectedLocation,
  crearUbicacionInterna,
  limpiarMensajeUbicacionInterna,
  listaDeUbicacionesEnAlmacen,
} from "../../../redux/almacenesDucks";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Input,
} from "@mui/material";
import { Box } from "@mui/system";
import { Help } from "@mui/icons-material";
import CloseIcon from "@mui/icons-material/Close";
import { useCookies } from "react-cookie";
import { AlertComponent } from "../../componentesGenerales/Alert";

const ListaDeUbicaciones = () => {
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();

  const message = useSelector((store) => store.almacenes.locationMessage);
  const severity = useSelector((store) => store.almacenes.locationSeverity);
  const loading = useSelector((store) => store.almacenes.loading);
  const almacenes = useSelector((store) => store.almacenes.almacenes);

  const [selectedAlmacen, setSelectedAlmacen] = useState(null);
  const [selectedAlmacenId, setSelectedAlmacenId] = useState(null);
  const ubicacionesInternas = useSelector(
    (store) => store.almacenes.internalLocations
  );
  const [ubicaciones, setUbicaciones] = useState(null);
  const [ubicacionPadre, setUbicacionPadre] = useState(null);
  const [ubicacionesPadreList, setUbicacionesPadreList] = useState([]);
  const [tipoUbicacion, setTipoUbicacion] = useState(null);
  const [ubicacionLevel, setUbicacionLevel] = useState(null);
  const [crearUbicacion, setCrearUbicacion] = useState(false);
  const [ubicacionNueva, setUbicacionNueva] = useState(null);
  const [data, setData] = React.useState([]);

  React.useEffect(() => {
    if (almacenes?.length > 0) {
      setData(almacenes);
    }
  }, [almacenes]);

  const handleAlmacenChange = (event) => {
    const selectedAlmacenId = event.target.value;
    const almacen = data.find((a) => a.id === selectedAlmacenId);
    setSelectedAlmacen(almacen);
    setSelectedAlmacenId(selectedAlmacenId);
    dispatch(cambiarSelectedLocation(null));
  };

  const handleSendInfo = () => {
    const name = ubicacionNueva;
    const locationLevelItemParentId = ubicacionPadre;
    const locationLevelTypeId = tipoUbicacion;
    const storeId = selectedAlmacenId;

    const data = {
      name: name,
      storeId: storeId,
      locationLevelTypeId: locationLevelTypeId,
      locationLevelItemParentId: locationLevelItemParentId,
    };
    dispatch(crearUbicacionInterna(data, cookies.csrf_access_token));
  };

  const obtenerTodosLosElementos = (almacenes) => {
    let resultado = [];

    const recorrer = (objeto) => {
      resultado.push({
        id: objeto.id,
        name: objeto.name,
        entireName: objeto.entireName,
        type: objeto.locationLevelType,
      });
      if (objeto.children) {
        objeto.children.forEach((child) => recorrer(child));
      }
    };

    almacenes?.forEach((almacen) => recorrer(almacen));
    return resultado;
  };

  const obtenerElementosPadre = (almacenes, locationLevel) => {
    let resultado = [];

    const recorrer = (objeto) => {
      if (objeto.locationLevelType.locationLevel.level < locationLevel) {
        resultado.push({
          id: objeto.id,
          name: objeto.name,
          entireName: objeto.entireName,
          type: objeto.locationLevelType,
        });
      }
      if (objeto.children) {
        objeto.children.forEach((child) => recorrer(child));
      }
    };

    almacenes?.forEach((almacen) => recorrer(almacen));
    return resultado;
  };

  React.useEffect(() => {
    if (selectedAlmacen) {
      dispatch(
        listaDeUbicacionesEnAlmacen(
          obtenerTodosLosElementos(selectedAlmacen.locationLevelItems)
        )
      );
    }
  }, [selectedAlmacen]);

  const getAllLocationTypes = (ubicaciones) => {
    const allLocationTypes = ubicaciones.flatMap((ubicacion) =>
      ubicacion.locationLevelTypes.map((locationType) => ({
        ...locationType,
        level: ubicacion.level,
      }))
    );
    setUbicaciones(allLocationTypes);
  };

  React.useEffect(() => {
    if (ubicacionesInternas) {
      getAllLocationTypes(ubicacionesInternas.locationLevels);
    }
  }, [ubicacionesInternas, selectedAlmacen]);

  React.useEffect(() => {
    if (tipoUbicacion) {
      setUbicacionLevel(
        ubicaciones.find((ubicacion) => ubicacion.id === tipoUbicacion).level
      );
    }
  }, [tipoUbicacion]);

  React.useEffect(() => {
    if (ubicacionLevel) {
      setUbicacionesPadreList(
        obtenerElementosPadre(
          selectedAlmacen.locationLevelItems,
          ubicacionLevel
        )
      );
    }
  }, [ubicacionLevel]);

  return (
    <div style={{ marginTop: "20px" }}>
      {selectedAlmacen && (
        <Button
          onClick={() => setCrearUbicacion(true)}
          color="buttonGreenPink"
          variant="outlined"
          sx={{
            marginBottom: "20px",
            marginTop: "20px",
            width: "100%",
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          + Crear ubicación en este almacen
        </Button>
      )}
      <FormControl style={{ width: "100%" }}>
        <InputLabel id="select-almacen-label">Seleccionar Almacén</InputLabel>

        <Select
          labelId="select-almacen-label"
          id="select-almacen"
          label="Seleccionar Almacén"
          value={selectedAlmacen ? selectedAlmacen.id : ""}
          onChange={handleAlmacenChange}
        >
          {data?.map((almacen) => (
            <MenuItem key={almacen.id} value={almacen.id}>
              {almacen.storeName}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {selectedAlmacen && selectedAlmacen.locationLevelItems?.length > 0 && (
        <RenderizaUbicaciones
          ubicaciones={selectedAlmacen.locationLevelItems}
        />
      )}

      {selectedAlmacen && (
        <Dialog
          open={crearUbicacion}
          onClose={() => (
            setCrearUbicacion(false),
            setUbicacionNueva(null),
            setTipoUbicacion(null),
            setUbicacionPadre(null),
            setSelectedAlmacen(null),
            setSelectedAlmacenId(null)
          )}
          PaperProps={{
            classes: {
              width: "400px",
              height: "200px",
            },
          }}
        >
          <DialogTitle> Crear Ubicación </DialogTitle>
          <DialogContent>
            {loading ? (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: "100%",
                }}
              >
                <Skeleton variant="rectangular" />
              </Box>
            ) : (
              <Box
                sx={{
                  display: message ? "none" : "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  width: "100%",
                }}
              >
                <TextField
                  sx={{ width: "100%", marginTop: "10px" }}
                  label="Nombre"
                  title="Ubicación"
                  id="ubicacion-nueva"
                  value={ubicacionNueva}
                  onChange={(e) => setUbicacionNueva(e.target.value)}
                />
                <TextField
                  select
                  sx={{ width: "100%", marginTop: "10px" }}
                  label="Tipo"
                  title="Tipo"
                  id="tipo-ubicacion"
                  value={tipoUbicacion ? tipoUbicacion : ""}
                  onChange={(e) => setTipoUbicacion(e.target.value)}
                >
                  {ubicaciones.map((ubicacion) => (
                    <MenuItem key={ubicacion.id} value={ubicacion.id}>
                      {ubicacion.name}
                    </MenuItem>
                  ))}
                </TextField>
                <TextField
                  select
                  sx={{ width: "100%", marginTop: "10px" }}
                  id="ubicacion-padre"
                  title="Ubicación superior"
                  label="Ubicación superior"
                  value={ubicacionPadre || ""}
                  onChange={(e) => setUbicacionPadre(e.target.value)}
                >
                  {selectedAlmacen.locationLevelItems &&
                  selectedAlmacen.locationLevelItems.length > 0 ? (
                    ubicacionesPadreList.map((location) => (
                      <MenuItem key={location.id} value={location.id}>
                        {location.entireName}
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem disabled value="">
                      No hay ubicaciones disponibles
                    </MenuItem>
                  )}
                </TextField>
                <h6 style={{ marginTop: "10px", fontSize: "15px" }}>
                  <Help sx={{ fontSize: "15px" }} /> Si no selecciona una
                  ubicación superior, la ubicación será de nivel 1
                </h6>
              </Box>
            )}
          </DialogContent>
          <DialogActions sx={message || loading ? { display: "none" } : null}>
            <Button
              onClick={() => (
                setCrearUbicacion(false),
                setUbicacionNueva(null),
                setUbicacionPadre(null),
                setTipoUbicacion(null)
              )}
              color="error"
            >
              Cancelar
            </Button>
            <Button onClick={() => handleSendInfo()}>Guardar</Button>
          </DialogActions>
        </Dialog>
      )}
      {message && (
        <AlertComponent
          color={severity}
          message={message}
          cleanMessage={limpiarMensajeUbicacionInterna}
        />
      )}
    </div>
  );
};

export default ListaDeUbicaciones;
