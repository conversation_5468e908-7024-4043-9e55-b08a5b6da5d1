import React, { useState } from 'react';
import { Select, MenuItem, FormControl, InputLabel, Modal, Dialog, DialogTitle, DialogContent, DialogActions, Input, Button, Menu, Box } from '@mui/material';
import { IconButton } from '@mui/material';
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useDispatch } from 'react-redux';
import { cambiarSelectedLocation, cambiarSelectedStock } from '../../../redux/almacenesDucks';
import { MoreVert } from '@mui/icons-material';
import VerProductosUbicacion from './VerProductosUbicacion';


const RenderizaUbicaciones = ({ ubicaciones }) => {
  const dispatch = useDispatch();
  const [selectedUbicacion, setSelectedUbicacion] = useState(null);
  const [selectedStock, setSelectedStock] = useState(null);
    const [anchorEl, setAnchorEl] = useState(null);

  const handleUbicacionChange = (event) => {
    const selectedUbicacionId = event.target.value;
    const ubicacionSeleccionada = ubicaciones.find((u) => u.id === selectedUbicacionId);
    setSelectedUbicacion(ubicacionSeleccionada);
    setSelectedStock(null);
  };

    const handleClose = () => {
        setAnchorEl(null);
        };

    const handleConfigurar = () => {
        dispatch(cambiarSelectedLocation(selectedUbicacion))
        setAnchorEl(null);
    }

    const handleVerStock = () => {
        dispatch(cambiarSelectedStock(selectedUbicacion.locationLevelItem_Product_Store))
        setAnchorEl(null);
    }


  return (
    <Box style={{ marginTop: "20px" }}>
        <Box sx={{display:"flex", flexDirection:"row", justifyContent:"space-between", alignItems:"center"}}>

            <FormControl style={{width:"100%"}}>
                <InputLabel id={`select-label`}>Seleccionar Ubicación</InputLabel>
                <Select
                label="Seleccionar Ubicación"
                value={selectedUbicacion ? selectedUbicacion.id : ''}
                onChange={handleUbicacionChange}
                >
                    {ubicaciones.map((ubicacion) => (
                        <MenuItem key={ubicacion.id} value={ubicacion.id}>
                            {`${ubicacion.locationLevelType.name} ${ubicacion.name}`}
                        </MenuItem>
                        ))}
                </Select>
            </FormControl>

            { selectedUbicacion &&
                <>
                <IconButton
                    variant="contained" 
                    color="buttonGreenPink" 
                    style={{marginLeft:"20px"}}
                    onClick={(e) => (setAnchorEl(e.currentTarget))}
                >
                    <MoreVert color="buttonGreenPink" />
                </IconButton>
                <Menu
                id="simple-menu"
                anchorEl={anchorEl}
                keepMounted
                open={Boolean(anchorEl)}
                onClose={handleClose}
                >
                    <MenuItem onClick={handleConfigurar}>Configurar ubicación</MenuItem>
                    <MenuItem onClick={handleVerStock}>Ver productos</MenuItem>
                </Menu>
                </>
            }

        </Box>
        {selectedUbicacion && selectedUbicacion.children?.length > 0 && (
        <RenderizaUbicaciones ubicaciones={selectedUbicacion.children} />
      )}
    </Box>
    );
}

export default RenderizaUbicaciones;