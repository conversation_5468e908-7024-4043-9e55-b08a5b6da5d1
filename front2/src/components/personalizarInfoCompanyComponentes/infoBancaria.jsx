
import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import { Button, TextField, IconButton, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import DeleteIcon from '@mui/icons-material/Delete';
import { useDispatch, useSelector } from 'react-redux';
import { Edit, Send, Sync } from '@mui/icons-material';
import CancelIcon from "@mui/icons-material/Cancel";
import { AlertComponent } from '../componentesGenerales/Alert';
import { actualizarCuentaBancaria, agregarCuentaBancaria, eliminarCuentaBancaria, limpiarMessageBank } from '../../redux/companyDucks';
import { useCookies } from 'react-cookie';
import Fab from '@mui/material/Fab';

const InfoBancaria = () => {
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const companyInfo = useSelector(store => store.company.info);
  const bankAccounts = companyInfo?.bankAccounts;
  const loadingInfo = useSelector(store => store.company.loadingInfo);
  const changingInfo = useSelector(store => store.company.changingInfo);
  const messageBank = useSelector(store => store.company.messageBank);
  const severityBank = useSelector(store => store.company.severityBank);

  const [openModal, setOpenModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);


  React.useEffect(() => {
    if( severityBank === 'success' ){
        const timer = setTimeout(() => {
            window.location.reload();
        }, 5000);
        return () => clearTimeout(timer);
    }
    }, [messageBank]);


  React.useEffect(() => {
    companyInfo?.bankAccounts ?
    setBankAccountsModified(companyInfo.bankAccounts) :
    null
    }, [companyInfo]);

  console.log('bankAccounts', bankAccounts);
  console.log('info', companyInfo);

  const [bankAccountsModified, setBankAccountsModified] = useState(
    bankAccounts ? bankAccounts.map(f => ({ ...f, editing:false, new:false })) : null
    );

  const agregarFormulario = () => {
    const nuevoId = bankAccountsModified.length > 0 ? Math.max(...bankAccountsModified.map(f => f.id)) + 1 : 1;
    setBankAccountsModified([...bankAccountsModified, { id: nuevoId, bankName: '', accountNumber: '', clabe: '', editing: true, new:true}]);
  };

  const eliminarFormulario = (id) => {
    dispatch(eliminarCuentaBancaria(id, cookies.csrf_access_token));
    setBankAccountsModified(bankAccountsModified.filter(f => f.id !== id));
    setOpenModal(false);
    };

  const setEditing = (id) => {
    const newBankAccounts = [...bankAccountsModified];
    const index = newBankAccounts.findIndex(f => f.id === id);
    newBankAccounts[index] = { ...newBankAccounts[index], editing: true };
    setBankAccountsModified(newBankAccounts);
    }

   React.useEffect(() => {
    if (bankAccountsModified) {
        console.log('bankAccountsModified', bankAccountsModified);
    }
    }, [bankAccountsModified]);

  const agregarCuenta = (data) => {
    const newData = {
        bankName: data.bankName,
        accountNum: data.accountNumber,
        clabe: data.clabe
    }
    dispatch(agregarCuentaBancaria(newData,cookies.csrf_access_token));
  }

  const editarCuenta = (data) => {
    const newData = {
        bankName: data.bankName,
        accountNum: data.accountNumber,
        clabe: data.clabe
    }
    dispatch(actualizarCuentaBancaria( data.id, newData, cookies.csrf_access_token));
  }

  const handleCloseModal = () => {
    setOpenModal(false);
  }

  return (
    <>
            <Box
            sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'}}>
            <Card variant='outlined' sx={{width:'80%' ,borderRadius:'20px', borderColor:'#003876', borderWidth:'3'}}>
                <Box sx={{ height:'80vh'}}>
                    <CardContent sx={{overflowY:'auto'}}>
                        <Box display='flex' flexDirection='column' sx={{ justifyContent: 'center' }}>
                            <Box sx={{display:'flex', justifyContent:'center', marginBottom:'1%'}}>
                                <Typography sx={{fontStyle:'oblique',alignSelf:'center'}} variant="h6">Información Bancaria</Typography>
                            </Box>
                            <Box sx={{display:'flex', justifyContent:'center', flexDirection:'column'}}>
                                {bankAccountsModified?.map((formulario, index) => (
                                    <Box key={formulario.id} sx={{ display: 'flex', alignItems: 'center', alignSelf:'center', gap: 2, marginBottom: 2 }}>
                                    <TextField
                                        label="Banco"
                                        variant="outlined"
                                        value={bankAccountsModified[index]?.bankName}
                                        onChange={(e) => {
                                            const newBankAccounts = [...bankAccountsModified];
                                            newBankAccounts[index] = { ...newBankAccounts[index], bankName: e.target.value };
                                            setBankAccountsModified(newBankAccounts);
                                            }
                                        }
                                        disabled={!formulario.editing}
                                    />
                                    <TextField
                                        label="Cuenta"
                                        variant="outlined"
                                        value={bankAccountsModified[index]?.accountNumber}
                                        onChange={(e) => {
                                            const newBankAccounts = [...bankAccountsModified];
                                            newBankAccounts[index] = { ...newBankAccounts[index], accountNumber: e.target.value };
                                            setBankAccountsModified(newBankAccounts);
                                            }
                                        }
                                        disabled={!formulario.editing}
                                    />
                                    <TextField
                                        label="CLABE"
                                        variant="outlined"
                                        value={bankAccountsModified[index]?.clabe}
                                        onChange={(e) => {
                                            const newBankAccounts = [...bankAccountsModified];
                                            newBankAccounts[index] = { ...newBankAccounts[index], clabe: e.target.value };
                                            setBankAccountsModified(newBankAccounts);
                                            }
                                        }
                                        disabled={!formulario.editing}
                                    />
                                        {formulario.new ? (
                                            <>
                                                <IconButton 
                                                    onClick={() => 
                                                        {
                                                            setBankAccountsModified(bankAccountsModified.map(f => f.id === formulario.id ? { ...f, editing: false, new:false } : f))
                                                            agregarCuenta(formulario)
                                                        }
                                                    }
                                                    disabled={bankAccountsModified[index]?.bankName.length < 2 || bankAccountsModified[index]?.accountNumber.length !== 10 || bankAccountsModified[index]?.clabe.length !== 18}
                                                >
                                                    <Send />
                                                </IconButton>
                                                <IconButton
                                                    onClick={() => setBankAccountsModified(bankAccountsModified.filter(f => f.id !== formulario.id))}>
                                                    <DeleteIcon />
                                                </IconButton>
                                            </>
                                            
                                            
                                        ) : formulario.editing ?
                                        (
                                            <>
                                                <IconButton
                                                    onClick={() => 
                                                        {
                                                            setBankAccountsModified(bankAccountsModified.map(f => f.id === formulario.id ? { ...f, editing: false } : f))
                                                            editarCuenta(formulario)
                                                        }
                                                    }
                                                    disabled={bankAccountsModified[index]?.bankName.length < 2 || bankAccountsModified[index]?.accountNumber.length !== 10 || bankAccountsModified[index]?.clabe.length !== 18}
                                                >
                                                    <Sync />
                                                </IconButton>
                                                <IconButton
                                                    onClick={() => setBankAccountsModified(bankAccountsModified.map(f => f.id === formulario.id ? { ...f, editing: false } : f))}>
                                                    <CancelIcon />
                                                </IconButton>
                                            </>
                                        )
                                        :
                                        (
                                            <>
                                                <IconButton
                                                    onClick={() => setEditing(formulario.id)}>
                                                    <Edit />
                                                </IconButton>
                                                <IconButton
                                                    onClick={() => {
                                                        setSelectedRow(formulario);
                                                        setOpenModal(true);
                                                    }}>
                                                    <DeleteIcon />
                                                </IconButton>
                                            </>
                                        )}
                                    </Box>
                                ))}
                            </Box>
                        </Box>
                    </CardContent>
                </Box>
            </Card>
        </Box>
        <Fab
          variant="extended"
          color="buttonGreen"
          aria-label="add"
          style={{
            position: "fixed",
            bottom: 20,
            right: 20,
            marginBottom: "5px",
          }}
          onClick={agregarFormulario}
        >
          <AddCircleOutlineIcon sx={{ mr: 1 }} />
          Agregar
        </Fab>
        {messageBank && (
            <AlertComponent color={severityBank} message={messageBank} cleanMessage={limpiarMessageBank} />
        )
        }
        <Dialog open={openModal} onClose={handleCloseModal}>
          <DialogTitle>{selectedRow?.bankName}</DialogTitle>
          <DialogContent>
            ¿Estás seguro de que deseas eliminar esta cuenta? Esta acción no se puede deshacer
          </DialogContent>
          <DialogActions>
              <Button onClick={handleCloseModal} color='error'>Cancelar</Button>
              <Button onClick={()=>eliminarFormulario(selectedRow?.id)} variant="contained" color="primary">
                Confirmar
              </Button>
          </DialogActions>
      </Dialog>
    </>
  );
};

export default InfoBancaria;
