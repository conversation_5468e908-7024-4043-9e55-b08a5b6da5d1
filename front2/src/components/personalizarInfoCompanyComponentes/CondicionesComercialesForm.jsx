import { Delete, Send, Sync } from '@mui/icons-material';
import CloseIcon from '@mui/icons-material/Close'
import { Alert, Button, Grid, IconButton, TextField } from '@mui/material'
import { Box } from '@mui/system'
import React from 'react'
import { useCookies } from 'react-cookie';
import { useDispatch, useSelector } from 'react-redux';
import { actualizarCondicionComercial, agregarCondicionComercial, eliminarCondicionComercial, limpiarMessageCondicion } from '../../redux/companyDucks';

const CondicionesComercialesForm = () => {

  const mensaje = useSelector((store) => store.company.messageCondicion);
  const severity = useSelector((store) => store.company.severityCondicion);
  const loading = useSelector((store) => store.company.loadingCondicion);
  const info = useSelector((store) => store.company.info);

  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();

  const [deleting, setDeleting] = React.useState(null);

  const [terms, setTerms] = React.useState(
    info.commercialTerms ?
    info.commercialTerms.map((item) => ({
        id: item?.id,
        commercialTerm: item?.commercialTerm,
        new: false,
    }))
    : [
        {
            commercialTerm: "",
            new: true,
        },
    ]

    );

    const handleAdd = () => {
        terms ?
        terms.some((item) => item.new) ?
        null :
        setTerms([
            ...terms,
            {
                commercialTerm: "",
                new: true,
            },
        ])
        : setTerms([
            {
                commercialTerm: "",
                new: true,
            },
        ])
    }

    

    const handleSendCondition = (item) => {
        if (item.new) {
            const data = {
                "commercialTerm": item.commercialTerm
            }
            dispatch(agregarCondicionComercial(data, cookies.csrf_access_token));
        }
    }

    const handleDeleteCondition = () => {
        const item = terms.find((item) => item.id === deleting);
        if (item.new) {
            setTerms(terms.filter((item) => item.id !== deleting));
        }else{
        dispatch(eliminarCondicionComercial(deleting, cookies.csrf_access_token));
        }
    }

    const handleUpdateCondition = (item) => {
        const id = item.id;
        const data = {
            "commercialTerm": item.commercialTerm
        }
        dispatch(actualizarCondicionComercial(id, data, cookies.csrf_access_token));
    }

    React.useEffect(() => {
        console.log("info", info);
        setTerms(
            info.commercialTerms?.map((item) => ({
                id: item?.id,
                commercialTerm: item?.commercialTerm,
                new: false,
            }))
        );
    }, [info])

    React.useEffect(() => {
        if (deleting !== null) {
            if (severity === "success") {
                setDeleting(null);
            }
        }
    }, [severity])

  return (
    info.companyName && info.RFC && info.url_SAT ?
    (<>
        {terms?.map((item, index) => (
            <Box
                key={index}
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    width: "100%",
                    padding: 2,
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        width: "100%",
                        flexDirection: "row",
                    }}
                >
                    <TextField
                        key={index}
                        id={item.id}
                        fullWidth
                        label="Condiciones Comerciales"
                        variant="outlined"
                        value={item.commercialTerm}
                        onChange={(e) => {
                            const newTerms = [...terms];
                            newTerms[index].commercialTerm = e.target.value;
                            setTerms(newTerms);
                        }}
                        
                    />
                    {item.new ? (
                    <Button
                        variant="contained"
                        color="primary"
                        style={{ marginLeft: 10, borderRadius: 10 }}
                        onClick={() => handleSendCondition(item)}
                    >
                        <Send/>
                    </Button>
                    ) : (
                    <Button
                        variant="contained"
                        color="primary"
                        style={{ marginLeft: 10, borderRadius: 10 }}
                        onClick={() => handleUpdateCondition(item)}
                    >
                        <Sync/>
                    </Button>
                    )}
                    <Button
                        variant="contained"
                        color="error"
                        style={{ marginLeft: 10, borderRadius: 10 }}
                        onClick={() => setDeleting(item.id)}
                    >
                        <Delete/>
                    </Button>
                </Box>
                {deleting === item.id && (
                    <Grid container justifyContent="center" sx={{marginTop:"10px"}}>
                        <Grid item>
                            <Button
                                variant="contained"
                                color="primary"
                                style={{ marginRight: 10, borderRadius: 10 }}
                                onClick={() => {
                                    setDeleting(null);
                                }}
                            >
                                Cancelar
                            </Button>
                            <Button
                                variant="contained"
                                color="error"
                                style={{ borderRadius: 10 }}
                                onClick={handleDeleteCondition}
                            >
                                Eliminar Definitivamente
                            </Button>
                        </Grid>
                    </Grid>
                )

                }
            </Box>
        ))}
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                width: "100%",
                padding: 2,
            }}
        >
            <Button 
                onClick={handleAdd}
                variant="contained"
                color="primary"
                fullWidth
                style={{ borderRadius: 10 }}
            >
                Agregar Condicion Comercial
            </Button>
        </Box>

        { mensaje && <Alert 
                  severity={severity} 
                  action={
                    <IconButton
                      aria-label="close"
                      color="inherit"
                      size="small"
                      onClick={() => {
                        dispatch(limpiarMessageCondicion());
                      }
                    }
                    >
                      <CloseIcon fontSize="inherit" />
                    </IconButton>
                }>
                  {mensaje}
                </Alert>}
    </>)
    : 
    <Box
        sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            width: "100%",
            padding: 2,
        }}
    >
        <Alert severity="info">"Primero debes registrar la información de la compañía"</Alert>
    </Box>
)
}

export default CondicionesComercialesForm
