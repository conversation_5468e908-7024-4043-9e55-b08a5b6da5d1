import React from 'react'
import { Item } from '../personalizarPerfilComponentes/PerfilGeneral'
import {
    Routes,
    Route,
} from "react-router-dom";
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import PersonalizarInfo from './personalizarInfo';
import { useDispatch, useSelector } from 'react-redux';
import { getCompanyInfo } from '../../redux/companyDucks';
import InfoBancaria from './infoBancaria';
import { TitleModule } from '../componentesGenerales/TitleModule';

const InfoCompanyGeneral = () => {
    const dispatch = useDispatch();
    React.useEffect(() => {
        console.log('useEffect peticion');
        dispatch(getCompanyInfo());
    }, [])
    return (
        <>
            <Grid container spacing={2}>
               <TitleModule title="Información de la Compañia"/>
            </Grid>
            <Routes>
                <Route path="/informacion" element={<PersonalizarInfo />} />
                <Route path="/informacion/bancaria" element={<InfoBancaria />} />
            </Routes>
        </>
    )
}

export default InfoCompanyGeneral
