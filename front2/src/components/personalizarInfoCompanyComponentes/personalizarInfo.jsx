import React from 'react'
import { Box, Card, CardContent, Avatar, IconButton, Typography, Icon, Fab, Dialog, DialogTitle, DialogContent, DialogActions, <PERSON>ton, Tooltip, <PERSON>ert, <PERSON>ack, TextField } from '@mui/material'
import { customStyles } from '../personalizarPerfilComponentes/PersonalizarPerfil'
import { useDispatch, useSelector } from 'react-redux'
import Skeleton from '@mui/material/Skeleton'
import AddPhotoIcon from '@mui/icons-material/AddPhotoAlternate'
import { useCookies } from 'react-cookie'
import { actualizarCompanyInfo, cambiarStatusImage, cambiarStatusInfo, getCompanyInfo } from '../../redux/companyDucks'
import { EditableLabel } from '../personalizarPerfilComponentes/PersonalizarPerfil'
import { Info } from '@mui/icons-material'
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import CondicionesComercialesForm from './CondicionesComercialesForm'
import CameraAltRoundedIcon from "@mui/icons-material/CameraAltRounded";
import CloudUploadRoundedIcon from "@mui/icons-material/CloudUploadRounded";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import QrCode2RoundedIcon from "@mui/icons-material/QrCode2Rounded";
import CheckCircleOutlineRoundedIcon from "@mui/icons-material/CheckCircleOutlineRounded";
import BusinessIcon from "@mui/icons-material/Business";
import Divider from '@mui/material/Divider';
import CustomDialog from '../componentesGenerales/CustomDialog'


const CompanyInfo = ({ cookies, companyInfo, loadingInfo }) => {
    const dispatch = useDispatch();

    const handleNameSave = (newValue) => {
        const formData = new FormData();
        if (newValue.length > 0) {
            formData.append('company_name', newValue);
            dispatch(actualizarCompanyInfo(formData, cookies.csrf_access_token));
        } else {
            alert('El nombre de la empresa no puede estar vacío');
        }
    };

    const handleRFCSave = (newValue) => {
        const formData = new FormData();
        if (newValue.length < 1 || newValue.length > 13) {
            alert('El RFC debe tener una longitud de 12 caracteres');
        }
        else {
            if (newValue.match(/^[A-Z0-9]/)) {
                formData.append('RFC', newValue);
                dispatch(actualizarCompanyInfo(formData, cookies.csrf_access_token));
            } else {
                alert('El RFC no tiene un formato válido');
            }
        }
    }

    const handleQRSave = (newValue) => {
        const formData = new FormData();
        if (newValue.length < 1) {
            alert('El código QR no puede estar vacío');
        }
        else {
            //newValue debe ser una URL valida
            if (newValue.match(/^(http|https|www)/)) {
                formData.append('qr_code_SAT', newValue);
                dispatch(actualizarCompanyInfo(formData, cookies.csrf_access_token));
            }
            else {
                alert('El código QR no tiene un formato válido');
            }
        }
    }

    const Field = ({ loading, label, value, onSave }) => (
        <Box>
            {loading ? (
                <Skeleton variant="text" width="60%" height={32} />
            ) : (
                <EditableLabel label={label} value={value} onSave={onSave} />
            )}
        </Box>
    );

    return (
        <Stack spacing={2}>
            <Field
                loading={loadingInfo.name}
                label="Nombre"
                value={companyInfo?.companyName || ''}
                onSave={handleNameSave}
            />

            <Field
                loading={loadingInfo.rfc}
                label="RFC"
                value={companyInfo?.RFC || ''}
                onSave={handleRFCSave}
            />

            <Field
                loading={loadingInfo.qr_code}
                label="URL del Sitio"
                value={companyInfo?.url_SAT || ''}
                onSave={handleQRSave}
            />
        </Stack>
    )
}


const personalizarInfo = () => {
    const loadingInfo = useSelector((store) => store.company.loadingInfo);
    const companyInfo = useSelector((store) => store.company.info);
    const [URLphoto, setURLphoto] = React.useState(companyInfo ? companyInfo.imageCompany : null);
    const changingImage = useSelector((store) => store.company.changingImage);
    const changingInfo = useSelector((store) => store.company.changingInfo);
    const [isHovering, setIsHovering] = React.useState(false);
    const [openConditionTerms, setOpenConditionTerms] = React.useState(false);
    const [openEditDialog, setOpenEditDialog] = React.useState(false);
    const [editValues, setEditValues] = React.useState({
        companyName: companyInfo?.companyName || '',
        RFC: companyInfo?.RFC || '',
        url_SAT: companyInfo?.url_SAT || ''
    });

    const handleMouseOver = () => {
        setIsHovering(true);
    };

    const handleMouseOut = () => {
        setIsHovering(false);
    };

    const [cookies, setCookie] = useCookies();
    const dispatch = useDispatch();

    React.useEffect(() => {
        if (changingInfo) {
            console.log('changingInfo');
            dispatch(getCompanyInfo());
            dispatch(cambiarStatusInfo(false));
        }
    }, [changingInfo]);

    React.useEffect(() => {
        if (companyInfo) {
            setURLphoto(companyInfo.imageCompany);
        }
    }, [companyInfo]);

    const [imgSmall, setImgSmall] = React.useState(false);

    const handleImgLoad = (e) => {
        const { naturalWidth, naturalHeight } = e.target;
        // Si alguna dimensión es menor a 280 px la consideramos pequeña
        setImgSmall(naturalWidth < 280 || naturalHeight < 280);
    };



    const handleImageUpload = (event) => {
        const file = event.target.files[0];
        if (file) {
            // Si alguna dimensión es menor a 280 px la consideramos pequeña

            const formData = new FormData();
            formData.append('image_company', file);

            const reader = new FileReader();
            reader.onload = function (event) {
                const image = new Image();
                image.src = event.target.result;

                image.onload = function () {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    let width = image.width;
                    let height = image.height;
                    const maxWidth = 1920; // Ancho máximo para resolución FHD
                    const maxHeight = 1080; // Alto máximo para resolución FHD

                    if (width > height && width > maxWidth) {
                        height *= maxWidth / width;
                        width = maxWidth;
                    } else if (height > maxHeight) {
                        width *= maxHeight / height;
                        height = maxHeight;
                    }

                    // Rotación y dibujo de la imagen en el canvas
                    canvas.width = width;
                    canvas.height = height;
                    ctx.drawImage(image, 0, 0, width, height);

                    // Convierte el canvas a Blob y crea un nuevo archivo
                    canvas.toBlob((blob) => {
                        const rotatedFile = new File([blob], file.name, { type: file.type });
                        formData.set('image_company', rotatedFile);


                        dispatch(actualizarCompanyInfo(formData, cookies.csrf_access_token, 'image'));
                    }, file.type);
                };
            };

            reader.readAsDataURL(file);
        }
    };

    const handleEditClick = () => {
        setEditValues({
            companyName: companyInfo?.companyName || '',
            RFC: companyInfo?.RFC || '',
            url_SAT: companyInfo?.url_SAT || ''
        });
        setOpenEditDialog(true);
    };

    const handleSaveAll = () => {
        // Validar y guardar todos los campos
        if (editValues.companyName.length > 0) {
            const formData = new FormData();
            formData.append('company_name', editValues.companyName);
            dispatch(actualizarCompanyInfo(formData, cookies.csrf_access_token));
        }

        if (editValues.RFC.length > 0) {
            const formData = new FormData();
            formData.append('RFC', editValues.RFC);
            dispatch(actualizarCompanyInfo(formData, cookies.csrf_access_token));
        }

        if (editValues.url_SAT.length > 0) {
            const formData = new FormData();
            formData.append('qr_code_SAT', editValues.url_SAT);
            dispatch(actualizarCompanyInfo(formData, cookies.csrf_access_token));
        }

        setOpenEditDialog(false);
    };

    const handleCancelEdit = () => {
        setOpenEditDialog(false);
    };

    const handleInputChange = (field, value) => {
        setEditValues(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const Field = ({ loading, label, value, onSave }) => (
        <Box>
            {loading ? (
                <Skeleton variant="text" width="60%" height={32} />
            ) : (
                <EditableLabel label={label} value={value} onSave={onSave} />
            )}
        </Box>
    );


    return (
        <>
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>


                <Box
                    sx={{
                        py: 6,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    {/* Card principal */}
                    <Box sx={{ position: "relative", width: "100%", maxWidth: 1200 }}>
                        {/* Efecto glow */}
                        <Box
                            sx={{
                                position: "absolute",
                                inset: 0,
                                transform: "translate(-4px, -4px)",
                                borderRadius: 3,
                                filter: "blur(18px)",
                                opacity: 0.15,
                                zIndex: -1,
                            }}
                        />

                        <Card elevation={8} sx={{ position: "relative", borderRadius: "20px", p: 3 }}>

                            <CardContent sx={{ p: 0 }}>
                                {/* Contenedor responsive */}
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: { xss: "column", md: "row" },
                                        alignItems: "stretch",
                                        gap: 2,
                                    }}
                                >
                                    {/* Sección Imagen corporativa */}
                                    <Box sx={{ flex: 1, minWidth: 0, textAlign: "center" }}>

                                        <Box display="flex" justifyContent="center" alignItems="center" mb={2}>
                                            <CameraAltRoundedIcon sx={{ mr: 1 }} />
                                            <Typography variant="h6">Imagen Corporativa</Typography>
                                        </Box>

                                        {/* Contenedor de imagen */}
                                        {/* Contenedor de imagen (280 × 280) */}
                                        <Box
                                            sx={{
                                                position: "relative",
                                                width: 280,
                                                height: 280,
                                                mx: "auto",
                                                overflow: "hidden",
                                                borderRadius: 2,
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                                boxShadow: 3,
                                                bgcolor: "background.paper",
                                            }}
                                            onMouseOver={() => setIsHovering(true)}
                                            onMouseOut={() => setIsHovering(false)}
                                        >
                                            {loadingInfo.image ? (
                                                <Skeleton variant="rectangular" width={280} height={280} />
                                            ) : URLphoto ? (
                                                <Box
                                                    component="img"
                                                    src={URLphoto}
                                                    alt="Imagen corporativa"
                                                    onLoad={handleImgLoad}
                                                    sx={{
                                                        maxWidth: "100%",
                                                        maxHeight: "100%",
                                                        width: imgSmall ? "auto" : "100%",
                                                        height: imgSmall ? "auto" : "100%",
                                                        objectFit: imgSmall ? "contain" : "cover",
                                                        imageRendering: imgSmall ? "auto" : "auto", // evita pixelado deliberado
                                                    }}
                                                />
                                            ) : (
                                                <Box textAlign="center">
                                                    <CloudUploadRoundedIcon sx={{ fontSize: 48, mb: 1 }} />
                                                    <Typography variant="body2">Subir imagen</Typography>
                                                </Box>
                                            )}

                                            {/* Overlay hover */}
                                            <label htmlFor="avatar-upload">
                                                <Box
                                                    sx={{
                                                        position: "absolute",
                                                        inset: 0,
                                                        bgcolor: isHovering || !URLphoto ? "rgba(0,0,0,0.6)" : "transparent",
                                                        display: "flex",
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                        transition: "background 0.3s ease",
                                                        cursor: "pointer",
                                                    }}
                                                >
                                                    {(isHovering || !URLphoto) && (
                                                        <Tooltip title="Cambiar imagen">
                                                            <IconButton
                                                                component="span"
                                                                sx={{
                                                                    bgcolor: "#f97316",
                                                                    "&:hover": { bgcolor: "#f97316", transform: "scale(1.1)" },
                                                                    transition: "transform 0.3s ease",
                                                                }}
                                                            >
                                                                <CameraAltRoundedIcon sx={{ color: "#fff" }} />
                                                            </IconButton>
                                                        </Tooltip>
                                                    )}
                                                </Box>
                                                <input
                                                    accept="image/*"
                                                    id="avatar-upload"
                                                    type="file"
                                                    hidden
                                                    onChange={handleImageUpload}
                                                />
                                            </label>
                                        </Box>

                                        {/* Aviso si la imagen es pequeña */}
                                        {imgSmall && !loadingInfo.image && URLphoto && (
                                            <Alert severity="info" sx={{ mt: 1 }}>
                                                La imagen es de baja resolución. Se muestra en su tamaño real para evitar pixelación.
                                            </Alert>
                                        )}

                                        {/* Recomendación */}
                                        <Box
                                            mt={2}
                                            px={2}
                                            py={1}
                                            sx={{
                                                bgcolor: "background.default",
                                                borderRadius: 2,
                                                display: "inline-flex",
                                                alignItems: "center",
                                            }}
                                        >
                                            <InfoOutlinedIcon sx={{ fontSize: 18, mr: 0.5 }} />
                                            <Typography variant="caption">
                                                Se recomienda una imagen cuadrada 1:1 y de al menos 280 px de lado
                                            </Typography>
                                        </Box>
                                    </Box>

                                    {/* Divisor adaptable */}
                                    <Divider
                                        orientation="vertical"
                                        flexItem
                                        sx={{ display: { xs: "none", md: "block" } }}
                                    />
                                    <Divider sx={{ display: { xs: "block", md: "none" } }} />

                                    {/* Información de la Empresa */}
                                    <Box sx={{ flex: 1, minWidth: 0, textAlign: "center" }}>
                                        <Box display="flex" justifyContent="start" alignItems="center" mb={2}>
                                            <BusinessIcon sx={{ mr: 1 }} />
                                            <Typography variant="h6">Información Empresa</Typography>
                                            <IconButton
                                            color='buttonGreenPink'
                                                onClick={handleEditClick}
                                                sx={{ ml: 'auto' }}
                                                size="small"
                                            >
                                                <EditOutlinedIcon />
                                            </IconButton>
                                        </Box>

                                        <Box
                                            sx={{
                                                flex: 1,
                                                minWidth: 0,
                                                bgcolor: "background.default",
                                                borderRadius: "12px",
                                                p: 2,
                                            }}
                                        >


                                            {CompanyInfo && (
                                                <CompanyInfo
                                                    cookies={cookies}
                                                    companyInfo={companyInfo}
                                                    loadingInfo={loadingInfo}
                                                />
                                            )}
                                        </Box>
                                    </Box>

                                    {/* Divisor adaptable */}
                                    <Divider
                                        orientation="vertical"
                                        flexItem
                                        sx={{ display: { xs: "none", md: "block" } }}
                                    />
                                    <Divider sx={{ display: { xs: "block", md: "none" } }} />

                                    {/* Sección Código QR */}
                                    <Box sx={{ flex: 1, minWidth: 0, textAlign: "center" }}>
                                        <Box display="flex" justifyContent="center" alignItems="center" mb={2}>
                                            <QrCode2RoundedIcon sx={{ mr: 1 }} />
                                            <Typography variant="h6">Código QR</Typography>
                                        </Box>

                                        {/* Contenedor de QR */}
                                        <Box
                                            sx={{
                                                display: "inline-block",
                                                overflow: "hidden",
                                                borderRadius: "12px",
                                                width: 280,
                                                height: 280,
                                            }}
                                        >
                                            {loadingInfo.qr_code ? (
                                                <Skeleton variant="rectangular" width={280} height={280} />
                                            ) : companyInfo?.qr_code_SAT ? (
                                                <Box
                                                    component="img"
                                                    src={companyInfo.qr_code_SAT}
                                                    alt="Código QR SAT"
                                                    sx={{ width: "100%", height: "100%", objectFit: "contain" }}
                                                />
                                            ) : (
                                                <Box
                                                    textAlign="center"
                                                    display="flex"
                                                    flexDirection="column"
                                                    alignItems="center"
                                                    justifyContent="center"
                                                    height="100%"
                                                >
                                                    <QrCode2RoundedIcon sx={{ fontSize: 64, mb: 1 }} />
                                                </Box>
                                            )}
                                        </Box>

                                        {/* Estado */}
                                        <Box
                                            mt={2}
                                            display="flex"
                                            alignItems="center"
                                            justifyContent="center"
                                            bgcolor="background.default"
                                            borderRadius="12px"
                                            p={1}
                                        >
                                            <CheckCircleOutlineRoundedIcon sx={{ fontSize: 18, mr: 0.5 }} />
                                            <Typography variant="caption">
                                                Código QR generado automáticamente cuando ingresa la URL del sitio
                                            </Typography>
                                        </Box>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Box>
                </Box>

                {/* Diálogo de edición de información de la empresa */}

                <CustomDialog
                    open={openEditDialog}
                    onClose={handleCancelEdit}
                    title="Editar Información de la Empresa"
                    maxWidth="sm"
                    maxHeight="70vh"
                    width="100%"
                    actions={
                        <>
                            <Button variant="text" color="error" onClick={handleCancelEdit}>
                                Cancelar
                            </Button>
                            <Button variant="outlined" onClick={handleSaveAll} color="buttonGreenPink" autoFocus>
                                Guardar
                            </Button>
                        </>
                    }
                >
                    <DialogContent>
                        <Stack spacing={3}>
                            <Box>
                                <TextField
                                    fullWidth
                                    label="Nombre de la Empresa"
                                    value={editValues.companyName}
                                    onChange={(e) => handleInputChange('companyName', e.target.value)}

                                />
                            </Box>
                            <Box>
                                <TextField
                                    fullWidth
                                    label="RFC"
                                    value={editValues.RFC}
                                    onChange={(e) => handleInputChange('RFC', e.target.value)}
                                    helperText="Debe tener 12 caracteres alfanuméricos"

                                />
                            </Box>
                            <Box>
                                <TextField
                                    fullWidth
                                    label="URL del Sitio"
                                    value={editValues.url_SAT}
                                    onChange={(e) => handleInputChange('url_SAT', e.target.value)}
                                    helperText="Debe ser una URL válida (http://, https://, www)"

                                />
                            </Box>
                        </Stack>
                    </DialogContent>
                </CustomDialog>


                <Dialog open={openConditionTerms} onClose={() => setOpenConditionTerms(false)}>
                    <DialogTitle>Condiciones comerciales</DialogTitle>
                    <DialogContent>
                        <CondicionesComercialesForm />
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={() => setOpenConditionTerms(false)}>Cerrar</Button>
                    </DialogActions>
                </Dialog>
            </Box>
            <Fab
                variant="extended"
                color="buttonGreen"
                aria-label="add"
                style={{
                    position: "fixed",
                    bottom: 20,
                    right: 20,
                    marginBottom: "5px",
                }}
                onClick={() => { setOpenConditionTerms(true) }}
            >
                <AddCircleOutlineIcon sx={{ mr: 1 }} />
                Condiciones comerciales
            </Fab>

        </>
    )
}

export default personalizarInfo
