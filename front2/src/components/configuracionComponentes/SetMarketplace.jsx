import { Box, Typography } from "@mui/material";
import DinamicCardMarketplace from "./DinamicCardMarketplace.jsx";
import responsive from "../css/configuraciones/configResponsive.module.css";
import { MarketPlaceGroup } from "./marketplaces/MarketPlaceGroup.jsx";

const SetMarketplace = () => {
  return (
    <Box className={responsive.containerSetMarketplace}>
      <Box  className = {responsive.titleHeader} >
        <Typography variant="h6" component="h2" >
          Configuracion de Marketplaces
        </Typography>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ padding: "0rem 0rem 0 2rem" }}
        >
          podras configurar los marketplaces de tu preferencia y aprovechar al
          maximo la plataforma
        </Typography>
      </Box>
      {/* <DinamicCardMarketplace /> */}
      <MarketPlaceGroup />
    </Box>
  );
};

export default SetMarketplace;
