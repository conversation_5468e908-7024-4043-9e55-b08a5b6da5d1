import React, { useState, useCallback } from "react";

// import "./css/dragAndDropImageForm.css";
import "../css/dragAndDropImageForm.css";
import AddAPhotoIcon from "@mui/icons-material/AddAPhoto";
// import ImageUpdaterItem from "../ImageUpdaterList";
import ImageUpdaterPreview from "./ImageUpdaterPreview";
// import { ImagesFormContext } from "../context/ImagesFormProvider";
import MenuItem from "@mui/material/MenuItem";
import Menu from "@mui/material/Menu";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "@mui/material/Button";
import LoadingButton from "@mui/lab/LoadingButton";
import { isDOMComponent } from "react-dom/test-utils";

function checkUrl(string) {
  let givenURL;
  try {
    givenURL = new URL(string);
  } catch (error) {
    console.log("error is", error);
    return false;
  }
  return true;
}

// CHECK IF IMAGE EXISTS
const loadImg2 = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = url;
    img.onload = () => resolve(true);
    img.onerror = () => reject(false);
  });
};

const DragAndDrop = (props) => {
  const { imageList, setImageList } = props;
  // const [imageList, setImageList] = useState([]);
  const colorDragDrop = React.useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [open, setOpen] = React.useState(false);
  const [tituloMensajeError, setTitulomensajeError] = React.useState("Error");
  const [urlActualValidation, setUrlActualValidation] = React.useState({
    valor: "",
    error: false,
    color: null,
    textoAyuda: " ",
  });
  const [cargandoValidacion, setCargandoValidacion] = React.useState(false);
  const [openErrorImage, setOpenErrorImage] = React.useState(false);

  //variable auxiliar para guardar el objeto de la imagen
  let datoo = null;

  const handleOpenErrorImage = () => {
    setOpenErrorImage(true);
  };

  const handleCloseOpenErrorImage = () => {
    setOpenErrorImage(false);
  };

  const manejarCambioUrl = (event) => {
    const url = event.target.value;
    if (
      imageList.some((image) => {
        if (image.key === url) {
          return true;
        }
        return false;
      })
    ) {
      setUrlActualValidation({
        valor: url,
        error: true,
        color: null,
        textoAyuda: "La imagen ya esta agregada",
      });
    } else {
      // aqui modifico la url
      if (checkUrl(url)) {
        setUrlActualValidation({
          valor: url,
          error: false,
          color: "success",
          textoAyuda: "url correcta",
        });
      } else {
        setUrlActualValidation({
          valor: url,
          error: true,
          color: null,
          textoAyuda: "La url introducida no es válida",
        });
      }
    }
  };
  // ref
  const inputRef = React.useRef(null);
  const buttonRef = React.useRef(null);
  //Andle clicks------------------------------------------------------------
  // triggers when file is selected with click
  const handleChange = async (e) => {
    e.stopPropagation();
    e.preventDefault();
    const typeData = e.dataTransfer ? e.dataTransfer : e.target;
    if (typeData.files && typeData.files[0]) {
      if (typeData.files.length === 1) {
        //comprobar si la imagen ya se inserto
        if (
          imageList?.length > 0 &&
          imageList.some((image) => {
            if (image.key === typeData.files[0].name) {
              return true;
            }
            return false;
          })
        ) {
          console.log("La imagen ya esta seleccionada");
        } else {
          let validado;
          datoo = typeData.files[0];
          try {
            const res = await loadImg2(URL.createObjectURL(typeData.files[0]));
            validado = res;
          } catch (error) {
            validado = error;
          }
          if (validado === true && imageList.length < 1) {
            setImageList([
              ...imageList,
              <ImageUpdaterPreview
                url={URL.createObjectURL(datoo)}
                title={datoo.name}
                key={datoo.name}
                file={datoo}
                imageList={imageList}
                setImageList={setImageList}
              />,
            ]);
            datoo = null;
          } else {
            handleOpenErrorImage();
          }
        }
      } else {
        console.log("Solo se puede seleccionar una imagena a la vez");
      }
      setSelectedFile(typeData.files);
      setSelectedFile(null);

      /*Funcion de agregar recuadro */
      /**disabled={urlActualValidation.error} */
    }
  };

  // triggers the input when the button is clicked
  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  // muestra un modal donde se pida la URL
  const showURLField = () => {
    handleCloseMenu();
    setOpen(true);
  };

  // cerramos el menu y llamamos al input por medio de una referencia
  const onButtonClick = () => {
    handleCloseMenu();
    inputRef.current.click();
  };

  const addURLImage = async () => {
    setCargandoValidacion(true);
    let validado;
    try {
      const res = await loadImg2(urlActualValidation.valor);
      validado = res;
    } catch (error) {
      validado = error;
    }
    setCargandoValidacion(false);
    if (validado === true && imageList.length < 1) {
      setAnchorEl(null);

      setImageList(
        imageList.concat(
          <ImageUpdaterPreview
            url={urlActualValidation.valor}
            title={urlActualValidation.valor}
            key={urlActualValidation.valor}
            file={null}
            imageList={imageList}
            setImageList={setImageList}
          />
        )
      );
      setOpen(false);
      setUrlActualValidation({
        valor: "",
        error: false,
        color: null,
        textoAyuda: " ",
      });
    } else {
      setUrlActualValidation({
        valor: urlActualValidation.valor,
        error: true,
        color: null,
        textoAyuda: "Ya hay una imagen o la url es invalida",
      });
    }
  };

  const handleClose = () => {
    setUrlActualValidation({
      valor: "",
      error: false,
      color: null,
      textoAyuda: " ",
    });
    setOpen(false);
  };

  // drag and drop
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = useCallback((event) => {
    event.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDrop = (event) => {
    event.preventDefault();
    let file = event;
    handleChange(event);
    setIsDragging(false);
    file = null;
  };

  const handleDragLeave = (event) => {
    //Archivo no está siendo arrastrado
    setIsDragging(false);
  };

  const isOpen = () => {
    if (imageList.length < 1) {
      return Boolean(anchorEl);
    }
    // setAnchorEl(null);
    return false;
  };

  return (
    <>
      <form
        id="form-file-upload"
        style={{
          width: "36.625rem",
          height: " 27.5rem",
          flexShrink: "0",
          margin: "auto",
        }}
        onSubmit={(e) => e.preventDefault()}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onDragLeave={handleDragLeave}
      >
        <div
          id="form-image-upload"
          // className={ isDragging ?  "form-image-drag" : "form-image-upload"}
        >
          {isDragging ? (
            <div className="dragAndDrop">Suelte la imagen</div>
          ) : (
            <div
              // id="form-image-upload"
              style={{
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <input
                ref={inputRef}
                type="file"
                id="input-image-upload"
                multiple={false}
                onChange={handleChange}
                accept="image/*"
              />
              <label
                id="label-image-upload"
                htmlFor="input-image-upload"
                className={colorDragDrop}
              >
                <div
                  style={{
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <br />

                  <button
                    onClick={handleMenu}
                  >
                    <AddAPhotoIcon />
                    Agregar
                  </button>
                  <Menu
                    id="menu-appbar"
                    anchorEl={anchorEl}
                    anchorOrigin={{
                      vertical: "top",
                      horizontal: "right",
                    }}
                    keepMounted
                    transformOrigin={{
                      vertical: "top",
                      horizontal: "right",
                    }}
                    // al abrir comprueba si imageList tiene una imagen

                    open={isOpen()}
                    onClose={handleCloseMenu}
                  >
                    <MenuItem onClick={showURLField}>URL</MenuItem>
                    <MenuItem onClick={onButtonClick}>Desde PC</MenuItem>
                  </Menu>
                </div>
              </label>
            </div>
          )}
        </div>
      </form>
      <Dialog
        // al abrir comprueba si imageList tiene una imagen
        open={imageList.length < 1 ? open : false}
        onClose={handleClose}
        fullWidth={true}
      >
        <DialogTitle>URL de la imagen</DialogTitle>
        <DialogContent>
          <DialogContentText>Escriba la url de la imagen</DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="urlImagen"
            label="URL imagen"
            type="text"
            fullWidth
            variant="standard"
            onChange={manejarCambioUrl}
            value={urlActualValidation.valor}
            color={urlActualValidation.color}
            error={urlActualValidation.error}
            helperText={urlActualValidation.textoAyuda}
          />
        </DialogContent>
        <DialogActions>
          <Button
            color="error"
          onClick={handleClose}>Cancelar</Button>
          <LoadingButton
          color="buttonGreen"
            onClick={addURLImage}
            loading={cargandoValidacion}
            disabled={urlActualValidation.error}
          >
            Agregar
          </LoadingButton>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openErrorImage}
        onClose={handleCloseOpenErrorImage}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{tituloMensajeError}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            solo se puede ingresar una imagen
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseOpenErrorImage} autoFocus>
            Aceptar
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DragAndDrop;
