import React, { useEffect, useState } from "react";

import Drag<PERSON>ndDrop from "./DragAndDrop";
import {
  Alert,
  Box,
  Button,
  CircularProgress,
  Collapse,
  IconButton,
} from "@mui/material";

import { cambiarImagenAccion } from "../../redux/configuracionesDucks";
import { useDispatch, useSelector } from "react-redux";
import { useCookies } from "react-cookie";

const GetAlert = ({ open, setOpen, msjHelp, colorHelp }) => {
  return (
    <Box sx={{ width: "70%", margin: "auto" }}>
      <Collapse in={open}>
        <Alert
          color={colorHelp}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpen(false);
              }}
            >
              {/* <CloseIcon fontSize="inherit" /> */}
              close
            </IconButton>
          }
          sx={{ mb: 2 }}
        >
          {msjHelp}
        </Alert>
      </Collapse>
      {/* <Button
        disabled={open}
        variant="outlined"
        onClick={() => {
          setOpen(true);
        }}
      >
        Re-open
      </Button> */}
    </Box>
  );
};

const DragDropBox = ({ type }) => {
  //   const { imageList, setImageList } = React.useContext(ImagesFormContext);
  const [imageList, setImageList] = useState([]);
  const [imageListUrl, setImageListUrl] = useState([]);
  const formData = new FormData();
  formData.append("prueba", "prueba");
  const typeImage = type === "portada" ? "imagePortada" : "imageLogin";
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const isloading = useSelector((store) => store.ConfiguracionSistema.loading);
  const isMensaje = useSelector((store) => store.ConfiguracionSistema.mensaje);
  const [open, setOpen] = useState(true);
  const [msjHelp, setMsjHelp] = useState("");
  const [colorHelp, setColorHelp] = useState("");
  const [helpError, setHelpError] = useState(false);

  const hadleSubmitImage = (e) => {
    if (imageList.length === 0) {
      setMsjHelp("No hay ninguna imagen");
      setColorHelp("error");
      setHelpError(true);
      setOpen(true);
      return;
    }
    const input = imageList[0].props.file ? "image" : "urlimage";
    const image = imageList[0].props.file
      ? imageList[0].props.file
      : imageList[0].props.url;

    formData.append(input, image);

    dispatch(cambiarImagenAccion(formData, cookies.csrf_access_token));
  };
  useEffect(() => {
    if (isMensaje == "OK") {
      setMsjHelp("Imagen agregada con exito");
      setColorHelp("success");
      setOpen(true);
    } else {
      setMsjHelp("Hubo un problema");
      setColorHelp("error");
      setOpen(true);
    }
  }, [isMensaje]);

  return (
    <React.Fragment>
      <section
        style={{
          display: "flex",
          overflowX: "auto",
          width: "100%",
          position: "relative",
        }}
      >
        <DragAndDrop
          imageList={imageList}
          setImageList={setImageList}
          imageListUrl={imageListUrl}
          setImageListUrl={setImageListUrl}
        />
        <div
          style={{
            display: "flex",
            margin: "auto 5px",
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
        >
          {imageList.length > 0 ? imageList : imageListUrl}
        </div>
      </section>
      <div
        style={{
          display: "flex",
          justifyContent: "end",
          width: "100%",
          marginTop: "1rem",
        }}
      >
        <Button
          variant="outlined"
          color="error"
          size="small"
          sx={{ marginRight: "1rem" }}
          disabled={isloading ? true : false}
        >
          Cancelar
        </Button>
        <Button
          variant="contained"
          size="small"
          color="buttonGreenPink"
          onClick={isloading ? null : hadleSubmitImage}
        >
          {isloading ? <CircularProgress color="info" size={20} /> : "Cambiar"}
          {/* change */}
        </Button>
      </div>
      {isMensaje == "OK" || helpError ? (
        <GetAlert
          open={open}
          setOpen={setOpen}
          msjHelp={msjHelp}
          colorHelp={colorHelp}
        />
      ) : null}
    </React.Fragment>
  );
};

export default DragDropBox;
