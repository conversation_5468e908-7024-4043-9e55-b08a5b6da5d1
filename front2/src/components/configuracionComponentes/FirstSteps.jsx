import { Box, Grid, Typography } from "@mui/material";
import logoCP from "../img/LogoCP500x500.svg";
import responsive from "../css/configuraciones/configResponsive.module.css";

const FirstSteps = () => {
  return (
    <Box className={responsive.firstSteps}>
      <Box
        sx={{
          margin: "auto",
          //   backgroundImage: `url(${logoCP})`,
          //   backgroundSize: "cover",
          //   backgroundPosition: "center",
          //   //   mixBlendMode: "overlay",
          //   backgroundColor: "rgba(0, 0, 0, 0.5)",
          //   zIndex: 10,
        }}
      >
        <Typography variant="h6" component="h5">
          Primeros pasos:
        </Typography>
        <Box sx={{ margin: "3rem 0 0 2rem" }}>
          <Typography variant="body1" component="h5">
            Configuracion General
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ padding: "1rem 0rem 0 2rem", display: "flex" }}
          >
            Cambiar la{" "}
            <Typography
              variant="body2"
              color="primary"
              sx={{ marginLeft: "0.3rem" }}
            >
              imagen de Login
            </Typography>
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ padding: "1rem 0rem 0 2rem", display: "flex" }}
          >
            Cambiar la{" "}
            <Typography
              variant="body2"
              color="primary"
              sx={{ marginLeft: "0.3rem" }}
            >
              imagen de Portada
            </Typography>
          </Typography>
        </Box>
        <Box sx={{ margin: "3rem 0 0 2rem" }}>
          <Typography variant="body1" component="h5">
            Configuracion de Producto
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ padding: "1rem 0rem 0 2rem", display: "flex" }}
          >
            Puedes modificar los estatus de los pedidos cuando quieras {" => "}
            <Typography
              variant="body2"
              color="primary"
              sx={{ marginLeft: "0.3rem" }}
            >
              Modificar estatus
            </Typography>
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default FirstSteps;
