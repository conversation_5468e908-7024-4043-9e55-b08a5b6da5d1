import { Box, Button, Skeleton, Typography } from "@mui/material";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getMarketplaceWithCredentials,
  setMarketplaceSupported,
} from "../../redux/configuracionesDucks";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { useEffect } from "react";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import responsive from "../css/configuraciones/configResponsive.module.css";
import { useTheme } from "@emotion/react";

const DinamicInputsMarketplace = ({
  marketplace,
  handleSubmit,
  setMarketplaceId,
  setMarketplaceSecretKey,
  marketplaceId,
  marketplaceSecretKey,
  isEditing,
  cancelEdit,
  keyAws,
  setKeyAws,
  setCodeMl,
  codeMl,
  setUrlMl,
  urlMl,
  setRefreshToken,
  refreshToken,
}) => {
  const formData = new FormData();

  const isSecretKeyValid = marketplaceSecretKey?.length >= 6;
  const isMarketplaceIdEmpty = marketplaceId?.trim() === "";
  const isMarketplaceSecretKeyEmpty = marketplaceSecretKey?.trim() === "";

  const dispatch = useDispatch();

  const getMktCredentials = useSelector(
    (store) => store.ConfiguracionSistema.marketplaceWithCredentials
  );

  const isEditComplete = useSelector(
    (store) => store.ConfiguracionSistema.isEditComplete);

  const loading = useSelector((store) => store.ConfiguracionSistema.loading);

  const checkCredentials = useSelector(
    (store) => store.ConfiguracionSistema.checkCredentials
  );

  useEffect(() => {
    const handleMarketplaceSupported = async () => {
      if (isEditing) {
        // await dispatch(getMarketplaceWithCredentials());
        handleCredentials();
      }
    };
    handleMarketplaceSupported();
  }, [isEditing]);

  useEffect(() => {
    if (isEditing) {
      handleCredentials();
    }
  }, [getMktCredentials]);

  useEffect(() => {
    // setMarketplaceId("");
    // setMarketplaceSecretKey("");
  }, []);

  // recupero credenciales en caso de actualizar
  const handleCredentials = async () => {
    const result = getMktCredentials?.marketplaces_with_credentials?.find(
      (mkt) => mkt.marketplaceId == marketplace.supportedMarketplaceId
    );
    if (isEditComplete) {
      const newMktCredentials = getMktCredentials;
      newMktCredentials.marketplaces_with_credentials = newMktCredentials.marketplaces_with_credentials.map((mkt) => {
        if (mkt.marketplaceId === marketplace.supportedMarketplaceId) {
          return {
            ...mkt,
            marketplaceCredentials: {
              ...mkt.marketplaceCredentials,
              check: true,
            },
          };
        }
        return mkt;
      });
    }

    console.log('0000000000000000,.,,,,,,,,,,,,,,,,')
    console.log(result)
    console.log('0000000000000000,.,,,,,,,,,,,,,,,,')
    if (result) {
      setMarketplaceId(result.marketplaceCredentials?.clientId);
      setMarketplaceSecretKey(result.marketplaceCredentials?.clientSecret);
      if (result.marketplaceCredentials?.marketplaceCredentialsAwsConnection) {
        setKeyAws({
          awsAccessKey:
            result.marketplaceCredentials?.marketplaceCredentialsAwsConnection
              .awsAccessKey,
          awsSecretKey:
            result.marketplaceCredentials?.marketplaceCredentialsAwsConnection
              .awsSecretKey,
          roleArn:
            result.marketplaceCredentials?.marketplaceCredentialsAwsConnection.roleArn,
        });
      }
      if (result.marketplaceCredentials?.codeMl) {
        setCodeMl(result.marketplaceCredentials?.codeMl);
      }
      if (result.marketplaceCredentials?.urlMl) {
        setUrlMl(result.marketplaceCredentials?.urlMl);
      }
      if (result.marketplaceCredentials?.refreshToken) {
        setRefreshToken(result.marketplaceCredentials.refreshToken)
      }
    }
  };

  const handlePreview = (e) => {
    // e.preventDefault(); // Detener el comportamiento predeterminado del botón submit
    if (marketplaceId == "" || marketplaceSecretKey == "") {
      return;
    }
    handleSubmit(e);
  };
  const handleKeyAws = (value, key) => {
    setKeyAws((prevState) => ({
      ...prevState,
      [key]: value,
    }));
  };

  const helperUserInput = (value, key) => {
    return (
      <Typography
        variant="body2"
        sx={{
          color: value?.length >= 3 ? "success" : "red",
          position: "absolute",
          bottom: "-26px", // Ajusta la posición vertical del mensaje
          marginTop: "0.5rem",
        }}
        endIcon={value?.length >= 3 ? null : <ErrorOutlineIcon color="red" />}
      >
        {value && value?.length >= 3
          ? null
          : value
            ? "Debe tener al menos 3 caracteres"
            : null}
        {/* {value.length >= 3 ? null : "Debe tener al menos 3 caracteres"} */}
      </Typography>
    );
  };

  const [showPassword, setShowPassword] = useState(false);

  const handlePasswordToggle = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };
  const theme = useTheme();
  console.log('theme', theme);
  return (
    <Box>
      {/* <form onSubmit={handleSubmit}> */}
      <form className={responsive.formBox}>
        <Box
          sx={{
            width: "80%",
            display: "flex",
            justifyContent: "space-evenly",
            alignItems: "center",
            marginTop: "2rem",
          }}
          className={responsive.contentInputs}
        >
          <Typography style={{ fontSize: "1rem" }} color={"primary"} className={responsive.sizeText}>
            ID cliente:{" "}
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              position: "relative",
              width: "70%",
            }}
            className={responsive.inputBox}
          >
            {loading ? (
              <Skeleton
                variant="rectangular"
                width="100%"
                height={45}
                sx={{ borderRadius: ".5rem" }}
              />
            ) : (
              <input
                style={{
                  width: "90%",
                  borderRadius: ".5rem",
                  height: "2.875rem",
                  outline: "none",
                  padding: "0 2rem",
                  backgroundColor: "transparent",
                  color: theme.palette.text.secondary,
                  border:
                    marketplaceId ? "1px solid green" : "1px solid red",
                }}
                type="text"
                autocomplete="new-password"
                onChange={(e) => setMarketplaceId(e.target.value)}
                placeholder="ID cliente"
                value={marketplaceId}
              />
            )}
            <Typography
              variant="body2"
              sx={{
                color: marketplaceId.length >= 3 ? "success" : "red",
                position: "absolute",
                bottom: "-26px", // Ajusta la posición vertical del mensaje
                marginTop: "0.5rem",
              }}
              endIcon={
                marketplaceId.length >= 3 ? null : (
                  <ErrorOutlineIcon color="red" />
                )
              }
            >
              {marketplaceId && marketplaceId.length >= 3
                ? null
                : marketplaceId
                  ? "Debe tener al menos 3 caracteres"
                  : null}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            width: "80%",
            display: "flex",
            justifyContent: "space-evenly",
            alignItems: "center",
            marginTop: "2rem",
          }}
          className={responsive.contentInputs}
        >
          <Typography style={{ fontSize: "1rem" }} color={"primary"} className={responsive.sizeText}>
            Llave secreta:{" "}
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              position: "relative",
              width: "70%",
            }}
            className={responsive.inputBox}
          >
            {loading ? (
              <Skeleton
                variant="rectangular"

                width="100%"
                height={45}
                sx={{ borderRadius: ".5rem" }}
              />
            ) : (
              <Box className={responsive.secretKey}>
                <input
                  style={{
                    width: "90%",
                    borderRadius: ".5rem",
                    height: "2.875rem",
                    outline: "none",
                    padding: "0 4.2em 0 2rem",
                    backgroundColor: "transparent",
                    color: theme.palette.text.secondary,
                    border:
                      marketplaceSecretKey ? "1px solid green" : "1px solid red",
                    // marketplaceSecretKey && marketplaceSecretKey.length >= 6
                    //   ? "1px solid green"
                    //   : marketplaceSecretKey
                    //   ? "1px solid red"
                    //   : "none",
                  }}
                  type={showPassword ? "text" : "password"}
                  autocomplete="new-password"
                  placeholder="Llave secreta"
                  onChange={(e) => setMarketplaceSecretKey(e.target.value)}
                  value={marketplaceSecretKey}
                />
                <Button
                  sx={{
                    width: "fit-content",
                    position: "absolute",
                    right: "-.3em",
                    top: "0.8em",
                    padding: "0",
                    // backgroundColor: "white",
                  }}
                  color="buttonGreen"
                  type="button"
                  onClick={handlePasswordToggle}
                >
                  {showPassword ? <VisibilityOffIcon size={10} /> : <VisibilityIcon />}{" "}
                </Button>
              </Box>
            )}

            <Typography
              variant="body2"
              sx={{
                color: marketplaceSecretKey.length >= 6 ? "success" : "red",
                position: "absolute",
                bottom: "-26px", // Ajusta la posición vertical del mensaje
                marginTop: "0.5rem",
              }}
              endIcon={
                marketplaceSecretKey.length >= 6 ? null : (
                  <ErrorOutlineIcon color="red" />
                )
              }
            >
              {marketplaceSecretKey && marketplaceSecretKey.length >= 3
                ? null
                : marketplaceSecretKey
                  ? "Debe tener al menos 6 caracteres"
                  : null}
            </Typography>
          </Box>
        </Box>
        {/* {marketplace.supportedMarketplaceId == 1 ? (
          <>
            <Box
              sx={{
                width: "80%",
                display: "flex",
                justifyContent: "space-evenly",
                alignItems: "center",
                marginTop: "2rem",
              }}
              className={responsive.contentInputs}
            >
              <span style={{ fontSize: "1rem", color: "#003876" }} className={responsive.sizeText}>
                llave acceso aws:{" "}
              </span>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  position: "relative",
                  width: "70%",
                }}
                className={responsive.inputBox}
              >
                {loading ? (
                   <Skeleton
                   variant="rectangular"

                   width="100%"
                   height={45}
                   sx={{  borderRadius: "1.875rem"}}
                 />
                ) : (
                  <input
                    style={{
                      width: "90%",
                      borderRadius: "1.875rem",
                      height: "2.875rem",
                      outline: "none",
                  padding: "0 2rem",

                      border:
                        keyAws.awsAccessKey && keyAws.awsAccessKey.length >= 3
                          ? "1px solid green"
                          : keyAws.awsAccessKey
                          ? "1px solid red"
                          : "none",
                    }}
                    type="text"
                    placeholder="llave acceso aws"
                    onChange={(e) =>
                      handleKeyAws(e.target.value, "awsAccessKey")
                    }
                    value={keyAws?.awsAccessKey}
                  />
                )}
                {helperUserInput(keyAws?.awsAccessKey, "awsAccessKey")}
              </Box>
            </Box>
            <Box
              sx={{
                width: "80%",
                display: "flex",
                justifyContent: "space-evenly",
                alignItems: "center",
                marginTop: "2rem",
              }}
              className={responsive.contentInputs}
            >
              <span style={{ fontSize: "1rem", color: "#003876" }} className={responsive.sizeText}>
                llave secreta aws:{" "}
              </span>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  position: "relative",
                  width: "70%",
                }}
                className={responsive.inputBox}
              >
                {loading ? (
                   <Skeleton
                   variant="rectangular"

                   width="100%"
                   height={45}
                   sx={{  borderRadius: "1.875rem"}}
                 />
                ) : (
                  <input
                    style={{
                      width: "90%",
                      borderRadius: "1.875rem",
                      height: "2.875rem",
                      outline: "none",
                  padding: "0 2rem",

                      border:
                        keyAws.awsSecretKey && keyAws.awsSecretKey.length >= 3
                          ? "1px solid green"
                          : keyAws.awsSecretKey
                          ? "1px solid red"
                          : "none",
                    }}
                    type="text"
                    placeholder="llave secreta aws"
                    onChange={(e) =>
                      handleKeyAws(e.target.value, "awsSecretKey")
                    }
                    value={keyAws?.awsSecretKey}
                  />
                )}
                {helperUserInput(keyAws?.awsSecretKey, "awsSecretKey")}
              </Box>
            </Box>
            <Box
              sx={{
                width: "80%",
                display: "flex",
                justifyContent: "space-evenly",
                alignItems: "center",
                marginTop: "2rem",
              }}
              className={responsive.contentInputs}
            >
              <span style={{ fontSize: "1rem", color: "#003876" }} className={responsive.sizeText}>
                rol arn:{" "}
              </span>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  position: "relative",
                  width: "70%",
                }}
                className={responsive.inputBox}
              >
                {loading ? (
                   <Skeleton
                   variant="rectangular"

                   width="100%"
                   height={45}
                   sx={{  borderRadius: "1.875rem"}}
                 />
                ) : (
                  <input
                    style={{
                      width: "90%",
                      borderRadius: "1.875rem",
                      height: "2.875rem",
                      outline: "none",
                  padding: "0 2rem",

                      border:
                        keyAws.roleArn && keyAws.roleArn.length >= 3
                          ? "1px solid green"
                          : keyAws.roleArn
                          ? "1px solid red"
                          : "none",
                    }}
                    type="text"
                    placeholder=" rol arn"
                    onChange={(e) => handleKeyAws(e.target.value, "roleArn")}
                    value={keyAws?.roleArn}
                  />
                )}
                {helperUserInput(keyAws?.roleArn, "roleArn")}
              </Box>
            </Box>
          </>
        ) : null} */}
        {/* edit code ML */}
        {marketplace.supportedMarketplaceName == 'Amazon' ? (
          <Box
            sx={{
              width: "80%",
              display: "flex",
              justifyContent: "space-evenly",
              alignItems: "center",
              marginTop: "2rem",
            }}
            className={responsive.contentInputs}
          >
            <Typography style={{ fontSize: "1rem" }} color={"primary"} className={responsive.sizeText}>
              Refresh token
            </Typography>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                position: "relative",
                width: "70%",
              }}
              className={responsive.inputBox}
            >
              {loading ? (
                <Skeleton
                  variant="rectangular"

                  width="100%"
                  height={45}
                  sx={{ borderRadius: ".5rem" }}
                />
              ) : (
                <input
                  style={{
                    width: "90%",
                    borderRadius: ".5rem",
                    height: "2.875rem",
                    outline: "none",
                    backgroundColor: "transparent",
                    color: theme.palette.text.secondary,
                    border:
                      codeMl.length >= 3
                        ? "1px solid green"
                        : "1px solid red",
                  }}
                  type="text"
                  placeholder="refreshToken"
                  onChange={(e) => setRefreshToken(e.target.value)}
                  // onChange={(e) => setCodeMl(e.target.value)}
                  value={refreshToken}
                />
              )}
              {helperUserInput(codeMl, "code")}
            </Box>
          </Box>
        ) : null}
        {marketplace.supportedMarketplaceName == 'Mercado Libre' ? (
          <>
            <Box
              sx={{
                width: "80%",
                display: "flex",
                justifyContent: "space-evenly",
                alignItems: "center",
                marginTop: "2rem",
              }}
              className={responsive.contentInputs}
            >

              <Typography style={{ fontSize: "1rem" }} color={"primary"} className={responsive.sizeText}>
                Code ML4
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  position: "relative",
                  width: "70%",
                }}
                className={responsive.inputBox}
              >
                {loading ? (
                  <Skeleton
                    variant="rectangular"

                    width="100%"
                    height={45}
                    sx={{ borderRadius: ".5rem" }}
                  />
                ) : (
                  <input
                    style={{
                      width: "90%",
                      borderRadius: ".5rem",
                      height: "2.875rem",
                      outline: "none",
                      backgroundColor: "transparent",
                      color: theme.palette.text.secondary,
                      border:
                        codeMl.length >= 3
                          ? "1px solid green"
                          : "1px solid red",
                    }}
                    type="text"
                    placeholder="Code ML3"
                    onChange={(e) => setCodeMl(e.target.value)}
                    // onChange={(e) => setCodeMl(e.target.value)}
                    value={codeMl}
                  />
                )}
                {helperUserInput(codeMl, "code")}
              </Box>
            </Box>
            <Box
              sx={{
                width: "80%",
                display: "flex",
                justifyContent: "space-evenly",
                alignItems: "center",
                marginTop: "2rem",
              }}
              className={responsive.contentInputs}
            >
              <Typography style={{ fontSize: "1rem" }} color={"primary"} className={responsive.sizeText}>
                url ML
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  position: "relative",
                  width: "70%",
                }}
                className={responsive.inputBox}
              >
                {loading ? (
                  <Skeleton
                    variant="rectangular"

                    width="100%"
                    height={45}
                    sx={{ borderRadius: ".5rem" }}
                  />
                ) : (
                  <input
                    style={{
                      width: "90%",
                      borderRadius: ".5rem",
                      height: "2.875rem",
                      outline: "none",
                      backgroundColor: "transparent",
                      color: theme.palette.text.secondary,
                      border:

                        urlMl.length >= 3
                          ? "1px solid green"
                          : "1px solid red",
                      // urlMl && urlMl.length >= 3
                      //   ? "1px solid green"
                      //   : urlMl
                      //   ? "1px solid red"
                      //   : "none",
                    }}
                    type="text"
                    placeholder="url ML"
                    onChange={(e) => setUrlMl(e.target.value)}
                    // onChange={(e) => setCodeMl(e.target.value)}
                    value={urlMl}
                  />
                )}
                {helperUserInput(urlMl, "urlMl")}
              </Box>
            </Box>
          </>
        ) : null}
        <Box sx={{ width: "100%", display: "flex", justifyContent: "end" }} className={responsive.responsiveButton} >
          {isEditing && (
            <Button
              variant="outlined"
              type="submit"
              color="error"
              sx={{ marginTop: "2rem", marginLeft: "1rem" }}
              onClick={() => cancelEdit(marketplace.supportedMarketplaceId)}
            >
              Cancelar
            </Button>
          )}
          <Button
            variant="contained"
            // type="submit"
            sx={{ marginTop: "2rem", marginLeft: "1rem" }}
            disabled={
              !marketplaceId || !isSecretKeyValid || marketplaceId.length < 3
            }
            // onClick={handleSubmit}
            onClick={(e) => handlePreview(e)}
          >
            Guardar
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default DinamicInputsMarketplace;
