import { Box, Typography } from "@mui/material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { isValidIdentifier } from "@babel/types";

const FeaturesStatusInterno = ({
  setNewStatusInterno,
  newStatusInterno,
  setSelectedStatusInterno,
  setNewTypeStatusInterno,
  newTypeStatusInterno,
  isVentaDirecta,
  setOpen,
}) => {
  const handleNewStatusInterno = () => {
    setOpen(true);
    setNewStatusInterno(true);
    setSelectedStatusInterno(null);
  };
  const handleNewTypeStatusInterno = () => {
    setOpen(true);
    setNewTypeStatusInterno(true);
    setSelectedStatusInterno(null);
  };

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        // marginTop: "2em",
      }}
    >
      {/* <Box sx={{ width: "100%", display: "flex", justifyContent: "start" }}>
        <Typography variant="h6" component="h2">
          caracteristicas
        </Typography>
      </Box> */}
      <Box>
        <Box
          sx={{
            display: "flex",
            cursor: "pointer",
            padding: "1em",
            "&:hover": {
              background: "#0d6efd12",
              borderRadius: "10px",
            },
          }}
          onClick={() => handleNewStatusInterno()}
        >
          <AddCircleOutlineIcon
            sx={{ fontSize: "1.5rem", alignSelf: "center" }}
          />
          <Typography variant="body1" color={newStatusInterno ? "secondary" : ""}  marginLeft={1} paddingBottom={1} > 
            Nuevo Estatus 
          </Typography>
        </Box>
        {/* boton de un nuevo tipo de status */}
        {isVentaDirecta() ? null : (
          <Box
            sx={{
              display: "flex",
              cursor: "pointer",
              padding: "1em",
              "&:hover": {
                background: "#0d6efd12",
                borderRadius: "10px",
              },
            }}
            onClick={() => handleNewTypeStatusInterno()}
          >
            <AddCircleOutlineIcon
              sx={{ fontSize: "1.5rem", alignSelf: "center" }}
            />
            <Typography
              variant="body1"
              // color={newTypeStatusInterno ? "primary" : ""}
              color={newTypeStatusInterno ? "secondary" : ""}  marginLeft={1} paddingBottom={1} 
            >
              Nuevo Tipo de Estatus
            </Typography>
          </Box>
        )}
        {/* <Box sx={{ display: "flex", cursor: "pointer", padding: "0 .5em" }}>
          <AddCircleOutlineIcon
            color="primary"
            sx={{ fontSize: "1.5rem", alignSelf: "center" }}
          />
          <Typography variant="body1">separador</Typography>
        </Box> */}
      </Box>
    </Box>
  );
};

export default FeaturesStatusInterno;
