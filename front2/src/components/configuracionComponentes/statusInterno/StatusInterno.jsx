import * as React from "react";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import GetLisStatusInterno from "./GetListStatusInterno";

import CardStatusInterno from "./CardStatusInterno";
import FeaturesStatusInterno from "./FeaturesStatusInterno";
import { useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  getInternalStatus,
  getInternalStatusDirectSale,
  setNewInternalStatus,
  setNewInternalStatusOrders,
  setNewTypeInternalStatus,
  updateInternalStatus,
  updateInternalStatusDirectSale,
  updateInternalStatusType,
} from "../../../redux/configuracionesDucks";
import { useCookies } from "react-cookie";
import SortableList from "./DyD";
import GetLisStatusInternoOrders from "./GetListStatusInternoOrders";
import responsive from "../../css/configuraciones/configResponsive.module.css";
import { Hidden, useMediaQuery } from "@mui/material";
import { useTheme } from "@emotion/react";
import CustomizedDialogs from "./DialogStatus";
// import CustomizedDialogs from "./DialogStatus";

let namesStatusInterno = [
  {
    orderInternalStatus: "No revisado",
    orderInternalStatusId: 1,
    orderInternalStatusType: "Flujo general",
    orderInternalStatusTypeId: 1,
  },
  {
    orderInternalStatus: "Pedido al proveedor",
    orderInternalStatusId: 2,
    orderInternalStatusType: "Pedido t\u00edpico",
    orderInternalStatusTypeId: 2,
  },
  {
    orderInternalStatus: "En camino(proveedor)",
    orderInternalStatusId: 3,
    orderInternalStatusType: "Pedido t\u00edpico",
    orderInternalStatusTypeId: 2,
  },
  {
    orderInternalStatus: "Recibido parcialmente",
    orderInternalStatusId: 4,
    orderInternalStatusType: "Pedido t\u00edpico",
    orderInternalStatusTypeId: 2,
  },
  {
    orderInternalStatus: "Stock Atenea",
    orderInternalStatusId: 5,
    orderInternalStatusType: "Pedido t\u00edpico",
    orderInternalStatusTypeId: 2,
  },
  {
    orderInternalStatus: "Listo para empaquetar",
    orderInternalStatusId: 6,
    orderInternalStatusType: "Pedido t\u00edpico",
    orderInternalStatusTypeId: 2,
  },
  {
    orderInternalStatus: "Enviado al cliente",
    orderInternalStatusId: 7,
    orderInternalStatusType: "Pedido t\u00edpico",
    orderInternalStatusTypeId: 2,
  },
  {
    orderInternalStatus: "Entregado al cliente",
    orderInternalStatusId: 8,
    orderInternalStatusType: "Pedido t\u00edpico",
    orderInternalStatusTypeId: 2,
  },
  {
    orderInternalStatus: "En espera... No surtir",
    orderInternalStatusId: 9,
    orderInternalStatusType: "Pedido at\u00edpico",
    orderInternalStatusTypeId: 3,
  },
  {
    orderInternalStatus: "Finalizado: No concretado",
    orderInternalStatusId: 10,
    orderInternalStatusType: "Pedido at\u00edpico",
    orderInternalStatusTypeId: 3,
  },
];

const StatusInterno = () => {
  const [selectedStatusInterno, setSelectedStatusInterno] =
    React.useState(null);

  // manejo de dialog
  const [open, setOpen] = React.useState(false);

  // manejo del select
  const [selectedStatusInternoInt, setSelectedStatusInternoInt] =
    React.useState(0);

  const [updateStatusInterno, setUpdateStatusInterno] = React.useState("");
  // bandera para new status interno
  const [newStatusInterno, setNewStatusInterno] = React.useState(false);
  //para nuevo tipo
  const [newTypeStatusInterno, setNewTypeStatusInterno] = React.useState(false);
  // descripcion si es un nuevo status interno
  const [newDescription, setNewDescription] = React.useState("");

  const dispatch = useDispatch();
  let mensage = useSelector((store) => store.ConfiguracionSistema.mensaje);
  const [cookies, setCookie] = useCookies();

  const location = useLocation();

  React.useEffect(() => {
    dispatch(getInternalStatusDirectSale());
  }, []);

  const directSaleInternalStatus = useSelector(
    (store) => store.ConfiguracionSistema.directSaleInternalStatus
  );

  const [directSaleInternalStatuses, setDirectSaleInternalStatuses] =
    React.useState(directSaleInternalStatus?.directSaleInternalStatuses);

  React.useEffect(() => {
    // Esta función se ejecuta cada vez que directSaleInternalStatus cambia.
    if (Array.isArray(directSaleInternalStatus?.directSaleInternalStatuses)) {
      const sortedArray = [
        ...directSaleInternalStatus.directSaleInternalStatuses,
      ].sort((a, b) => a.orderNum - b.orderNum);
      setDirectSaleInternalStatuses(sortedArray);
    }
  }, [directSaleInternalStatus]);

  // actualiza status internos
  const handleUpdateStatus = () => {
    let updateStatusInternoConst = {};

    // para hacer un nuevo status interno
    if (newStatusInterno) {
      updateStatusInternoConst = {
        name: updateStatusInterno,
        description: newDescription,
      };
      if (!isVentaDirecta()) {
        updateStatusInternoConst = {
          ...updateStatusInternoConst,
          id_type: selectedStatusInternoInt,
        };
        dispatch(
          setNewInternalStatusOrders(
            updateStatusInternoConst,
            cookies.csrf_access_token
          )
        );
        setNewStatusInterno(false);
      } else {
        setSelectedStatusInternoInt(0);
        dispatch(
          setNewInternalStatus(
            updateStatusInternoConst,
            cookies.csrf_access_token
          )
        );
      }
    }
    // para hacer un nuevo status interno de tipo
    if (newTypeStatusInterno) {
      updateStatusInternoConst = {
        name: updateStatusInterno,
      };
      dispatch(
        setNewTypeInternalStatus(
          updateStatusInternoConst,
          cookies.csrf_access_token
        )
      );
      setNewTypeStatusInterno(false);
    }
    // para hacer un update de status interno
    else {
      updateStatusInternoConst = {
        id: isVentaDirecta()
          ? selectedStatusInterno?.directSaleInternalStatusId
          : selectedStatusInterno?.orderInternalStatusType
          ? selectedStatusInterno?.orderInternalStatusTypeId
          : selectedStatusInterno?.orderInternalStatusId,
        name: updateStatusInterno,
      };

      dispatch(
        isVentaDirecta()
          ? updateInternalStatusDirectSale(
              updateStatusInternoConst,
              cookies.csrf_access_token
            )
          : // comprobando si este es un status interno de tipo
          selectedStatusInterno?.orderInternalStatusType
          ? updateInternalStatusType(
              updateStatusInternoConst,
              cookies.csrf_access_token
            )
          : updateInternalStatus(
              updateStatusInternoConst,
              cookies.csrf_access_token
            )
      );
    }

    setSelectedStatusInternoInt(0);
  };

  React.useEffect(() => {
    if (mensage === 200 || mensage === 201) {
      dispatch(
        isVentaDirecta() ? getInternalStatusDirectSale() : getInternalStatus()
      );
      setSelectedStatusInterno(null);
      setUpdateStatusInterno("");
      setNewDescription("");
    }
  }, [mensage]);

  const isVentaDirecta = () => {
    const pattern = /\/configuracion\/statusInterno\/ventaDirecta/i;

    if (pattern.test(location.pathname)) {
      return true;
    }
    return false;
  };

  const themeBreak = useTheme();

  const isMediumScreen = useMediaQuery(themeBreak.breakpoints.down(1181));

  React.useEffect(() => {
    if (selectedStatusInterno != null) {
      setOpen(true);
    }
  }, [selectedStatusInterno]);

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        // sx={{ padding: "0rem 4rem 1rem 4rem" }}
        className={responsive.titleHeader}
      >
        <Typography variant="h6" component="h2">
          Configuracion de Status Internos {isVentaDirecta() && "Venta Directa"}
        </Typography>
        <Typography
          variant="body2"
          // color={theme => theme.palette.primary.main}
          sx={{ padding: "0rem 0rem 0 2rem" }}
        >
          podras configurar los status internos y modificarlos a tu conveniencia
        </Typography>
      </Box>
      <Box className={responsive.containerStatusInterno}>
        <Box sx={{ width: "25%", minWidth: "280px", marginBottom:"2em" }}>
          <Grid item xs={12} md={6}>
            <Typography
              sx={{ mt: 4, mb: 2 }}
              variant="h6"
              component="div"
            >
              Estatus internos
            </Typography>
            {isVentaDirecta() ? (
              <GetLisStatusInterno
                selectedStatusInterno={selectedStatusInterno}
                setSelectedStatusInterno={setSelectedStatusInterno}
                namesStatusInterno={namesStatusInterno}
                isVentaDirecta={isVentaDirecta}
                directSaleInternalStatus={directSaleInternalStatuses}
                newStatusInterno={newStatusInterno}
                setNewStatusInterno={setNewStatusInterno}
                setDirectSaleInternalStatuses={setDirectSaleInternalStatuses}
                setOpen={setOpen}
              />
            ) : (
              <GetLisStatusInternoOrders
                selectedStatusInterno={selectedStatusInterno}
                setSelectedStatusInterno={setSelectedStatusInterno}
                namesStatusInterno={namesStatusInterno}
                isVentaDirecta={isVentaDirecta}
                directSaleInternalStatus={directSaleInternalStatuses}
                newStatusInterno={newStatusInterno}
                setNewStatusInterno={setNewStatusInterno}
                setNewTypeStatusInterno={setNewTypeStatusInterno}
                newTypeStatusInterno={newTypeStatusInterno}
                setDirectSaleInternalStatuses={setDirectSaleInternalStatuses}
                setOpen={setOpen}
              />
            )}
          </Grid>
        </Box>

        <Box
          sx={{
            width: "60%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          {/* <FeaturesStatusInterno
            newStatusInterno={newStatusInterno}
            setNewStatusInterno={setNewStatusInterno}
            setSelectedStatusInterno={setSelectedStatusInterno}
          /> */}
          {/* para pantallas chicas s emostrara en un dialog */}
          {isMediumScreen ? (
            <CustomizedDialogs
              open={open}
              setOpen={setOpen}
              title="Estatus Interno"
            >
              <CardStatusInterno
                selectedStatusInterno={selectedStatusInterno}
                setSelectedStatusInterno={setSelectedStatusInterno}
                setUpdateStatusInterno={setUpdateStatusInterno}
                handleUpdateStatus={handleUpdateStatus}
                updateStatusInterno={updateStatusInterno}
                newStatusInterno={newStatusInterno}
                setNewStatusInterno={setNewStatusInterno}
                setNewTypeInternalStatus={setNewTypeStatusInterno}
                newTypeStatusInterno={newTypeStatusInterno}
                setNewDescription={setNewDescription}
                newDescription={newDescription}
                isVentaDirecta={isVentaDirecta}
                selectedStatusInternoInt={selectedStatusInternoInt}
                setSelectedStatusInternoInt={setSelectedStatusInternoInt}
                isResponsive={true}
              />
            </CustomizedDialogs>
          ) : (
            <CardStatusInterno
              selectedStatusInterno={selectedStatusInterno}
              setSelectedStatusInterno={setSelectedStatusInterno}
              setUpdateStatusInterno={setUpdateStatusInterno}
              handleUpdateStatus={handleUpdateStatus}
              updateStatusInterno={updateStatusInterno}
              newStatusInterno={newStatusInterno}
              setNewStatusInterno={setNewStatusInterno}
              setNewTypeInternalStatus={setNewTypeStatusInterno}
              newTypeStatusInterno={newTypeStatusInterno}
              setNewDescription={setNewDescription}
              newDescription={newDescription}
              isVentaDirecta={isVentaDirecta}
              selectedStatusInternoInt={selectedStatusInternoInt}
              setSelectedStatusInternoInt={setSelectedStatusInternoInt}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default StatusInterno;
