import * as React from "react";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import IconButton from "@mui/material/IconButton";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import {
  deleteInternalStatusDirectSale,
  deleteTypeInternalStatus,
  getInternalStatus,
  getInternalStatusDirectSale,
  reOrderTypeInternalStatus,
} from "../../../redux/configuracionesDucks";
import { useDispatch, useSelector } from "react-redux";
import {
  Skeleton,
  Stack,
  Typography,
  ListItemIcon,
  ListItemSecondaryAction,
  CircularProgress,
  Paper,
} from "@mui/material";
import { useCookies } from "react-cookie";
import FeaturesStatusInterno from "./FeaturesStatusInterno";
import SortableList from "./DyD";
import { Container, Draggable } from "react-smooth-dnd";
import DragHandleIcon from "@mui/icons-material/DragHandle";
import { arrayMoveImmutable } from "array-move";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";

const Demo = styled("div")(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
}));

const GetLisStatusInternoOrders = ({
  selectedStatusInterno,
  setSelectedStatusInterno,
  namesStatusInterno,
  //   comprobar que estatus es
  isVentaDirecta,
  directSaleInternalStatus,
  setDirectSaleInternalStatuses,
  newStatusInterno,
  setNewStatusInterno,
  setNewTypeStatusInterno,
  newTypeStatusInterno,
  setOpen,
}) => {
  const dispatch = useDispatch();
  const [cookies, setCookies] = useCookies();

  const [isLoading, setIsLoading] = React.useState(false);
  const [deleteId, setDeleteId] = React.useState();

  const getInternalStatusData = useSelector(
    (store) => store.ConfiguracionSistema.directSaleInternalStatusOrders
  );

  const [getDataInternalStatus, setGetDataInternalStatus] = React.useState(
    []
    // getInternalStatusData?.directSaleInternalStatuses
  );

  // borra solo el id internal status
  const handleDeleteStatus = async (id) => {
    setIsLoading(true);

    // boorar mi getDataInternalStatus tras borrar este pets

    setDeleteId(id);
    try {
      await dispatch(deleteTypeInternalStatus(id, cookies.csrf_access_token));

      const nuevoArreglo = getDataInternalStatus.filter(
        (item) => item.orderInternalStatusTypeId !== id
      );

      setGetDataInternalStatus(nuevoArreglo);
    } catch (e) { }
    setIsLoading(false);
  };

  React.useEffect(() => {
    // Esta función se ejecuta cada vez que getInternalStatus cambia.

    if (!getInternalStatusData?.directSaleInternalStatuses) return;
    const sortedArray = [
      ...getInternalStatusData?.directSaleInternalStatuses,
    ].sort((a, b) => a.orderNum - b.orderNum);
    setGetDataInternalStatus(sortedArray);
  }, [getInternalStatusData]);

  React.useEffect(() => {
    dispatch(getInternalStatus());
  }, []);

  // cambiando el nombre del tipo de status interno
  const handleChangeType = (item) => {
    setSelectedStatusInterno(item);
  };

  const [dense, setDense] = React.useState(false);
  // borrar status
  const [secureDelete, setSecureDelete] = React.useState(false);

  const secureDeleteFunction = (id) => {
    setSecureDelete(id);
  };

  const [isDragging, setIsDragging] = React.useState(false);

  const onDrop = ({ removedIndex, addedIndex }) => {
    const movedArray = arrayMoveImmutable(
      getDataInternalStatus,
      removedIndex,
      addedIndex
    );

    // setGetDataInternalStatus((prevItems) => {
    //   console.log(prevItems, "prevItems");

    //   const updatedItems = movedArray.map((item, index) => {
    //     console.log(item, "item");
    //     return {
    //       ...item,
    //       orderNum: index + 1,
    //     };
    //   });

    //   const sortedArray = [...updatedItems].sort(
    //     (a, b) => a.orderNum - b.orderNum
    //   );

    //   return sortedArray;
    // });

    // console.log(
    //   removedIndex,
    //   "removedIndex",
    //   addedIndex,
    //   "addedIndex",
    //   movedArray
    // );

    // if (isVentaDirecta()) {
    //   setDirectSaleInternalStatuses((prevItems) => {
    //     // console.log(prevItems, "prevItems");
    //     const movedArrayDirectSale = arrayMoveImmutable(
    //       prevItems,
    //       removedIndex,
    //       addedIndex
    //     );
    //     return movedArrayDirectSale.map((item, index) => {
    //       return { ...item, orderNum: index + 1 };
    //     });
    //   });
    // } else {
    //   setDirectSaleInternalStatuses((prevItems) => {
    //     return prevItems.map((item, index) => {
    //       if (item.orderInternalStatusTypeId === typeId) {
    //         const modifiedArray = movedArray.map((innerItem, innerIndex) => {
    //           return {
    //             ...innerItem,
    //             orderNum: innerIndex + 1,
    //           };
    //         });

    //         const sortedArray = [...modifiedArray].sort(
    //           (a, b) => a.orderNum - b.orderNum
    //         );

    //         // console.log(sortedArray, "sortedArray");

    //         return {
    //           ...item,
    //           OrderInternalStatus: sortedArray,
    //         };
    //       }
    //       return item;
    //     });
    //   });
    // }

    // setChange(true);
  };

  const drag = (isDrag) => {
    setIsDragging(isDrag);
  };

  // function ConditionalContent(item) {
  //   if (item.OrderInternalStatus) {
  //     return (
  //       <SortableList
  //         directSaleInternalStatus={item.OrderInternalStatus}
  //         setDirectSaleInternalStatuses={setGetDataInternalStatus}
  //         typeId={item.orderInternalStatusTypeId}
  //         dataAll={getDataInternalStatus}
  //         selectedStatusInterno={selectedStatusInterno}
  //         setSelectedStatusInterno={setSelectedStatusInterno}
  //         isVentaDirecta={isVentaDirecta}
  //       />
  //     );
  //   } else {
  //     return (
  //       <Stack spacing={2}>
  //         <Skeleton variant="rounded" width={210} height={40} />
  //         <Skeleton variant="rounded" width={210} height={40} />
  //         <Skeleton variant="rounded" width={210} height={40} />
  //         <Skeleton variant="rounded" width={210} height={40} />
  //         <Skeleton variant="rounded" width={210} height={40} />
  //         <Skeleton variant="rounded" width={210} height={40} />
  //         <Skeleton variant="rounded" width={210} height={40} />
  //       </Stack>
  //     );
  //   }
  // }

  const moveItemDown = (index) => {
    if (index === getDataInternalStatus.length - 1) return;
    const coopyArray = [...getDataInternalStatus];
    const item = coopyArray[index];

    coopyArray[index] = coopyArray[index + 1];
    coopyArray[index + 1] = item;

    coopyArray.map((item, index) => {
      return (item.orderNum = index + 1);
    });
    // let new_order_to_status = {};
    const new_order_to_status = coopyArray.reduce((result, item) => {
      result[item.orderInternalStatusTypeId] = item.orderNum;
      return result;
    }, {});

    const result = { new_order_to_status };

    dispatch(reOrderTypeInternalStatus(result, cookies.csrf_access_token));

    setGetDataInternalStatus(coopyArray);
  };

  const moveItemUp = (index) => {
    if (index === 0) return;
    const coopyArray = [...getDataInternalStatus];
    const item = coopyArray[index];
    coopyArray[index] = coopyArray[index - 1];
    coopyArray[index - 1] = item;

    coopyArray.map((item, index) => {
      return (item.orderNum = index + 1);
    });
    // let new_order_to_status = {};
    const new_order_to_status = coopyArray.reduce((result, item) => {
      result[item.orderInternalStatusTypeId] = item.orderNum;
      return result;
    }, {});

    const result = { new_order_to_status };

    dispatch(reOrderTypeInternalStatus(result, cookies.csrf_access_token));

    setGetDataInternalStatus(coopyArray);
  };

  return (
    <>
      <Demo>
        <List dense={dense} component={Paper}>
          <Container
            dragHandleSelector=".drag-handle"
            lockAxis="y"
            onDrop={onDrop}
            onDragStart={() => drag(true)}
            onDragEnd={() => setIsDragging(false)}
            
          >
            {getDataInternalStatus?.map((item, index) => (
              // <Draggable key={item.orderInternalStatusTypeId}>
              <>
                {/* {() => (
                  <div> */}
                <React.Fragment key={item.orderNum}>
                  <Draggable key={item.orderInternalStatusTypeId}>
                    <Box>
                      <Typography
                        variant="subtitle1"
                        gutterBottom
                        sx={{
                          // color: "red",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "start",
                          alignItems: "center",
                        }}
                      // onClick={() => handleChangeType(item)}
                      >
                        <Box sx={{ display: "flex", flexDirection: "column" }} >
                          <ListItemIcon
                            className="drag-handle"
                            sx={{
                              display: "flex",
                              justifyContent: "center",
                              cursor: "pointer",
                              "&:hover": {
                                color: "green",
                              },
                            }}
                            onClick={() => moveItemUp(index)}
                          >
                            {/* <DragHandleIcon /> */}
                            <KeyboardArrowUpIcon />
                          </ListItemIcon>
                          <ListItemIcon
                            className="drag-handle"
                            sx={{
                              display: "flex",
                              justifyContent: "center",
                              cursor: "pointer",
                              "&:hover": {
                                color: "red",
                              },
                            }}
                            onClick={() => moveItemDown(index)}
                          >
                            {/* <DragHandleIcon /> */}
                            <KeyboardArrowDownIcon />
                          </ListItemIcon>
                        </Box>
                        <Box sx={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "space-between", width: "100%", paddingRight:"1em" }}>
                          <Box onClick={() => handleChangeType(item)}>
                            {item.orderInternalStatusType}
                          </Box>
                          {/* todo lo que tiene que ver con eliminar un tipo */}
                          {isLoading &&
                            deleteId === item.orderInternalStatusTypeId ? (
                            <CircularProgress
                              size={20}
                              sx={{
                                marginLeft: "10px",
                                color: "red",
                              }}
                            />
                          ) : secureDelete === item.orderInternalStatusTypeId ? (
                            <>
                              <IconButton
                                edge="end"
                                aria-label="delete"
                                onClick={() =>
                                  handleDeleteStatus(
                                    item.orderInternalStatusTypeId
                                  )
                                }
                              >
                                <CheckCircleOutlineIcon
                                  sx={{
                                    color: "green",
                                    "&:hover": {
                                      color: "green",
                                    },
                                  }}
                                />
                              </IconButton>
                              <IconButton
                                edge="end"
                                aria-label="delete"
                                onClick={() => setSecureDelete(null)}
                              >
                                <HighlightOffIcon
                                  sx={{
                                    color: "red",
                                    "&:hover": {
                                      color: "red",
                                    },
                                  }}
                                />
                              </IconButton>
                            </>
                          ) : (
                            // Cuando isLoading es false
                            <IconButton
                              edge="end"
                              aria-label="delete"
                              onClick={() =>
                                secureDeleteFunction(
                                  item.orderInternalStatusTypeId
                                )
                              }
                              sx={{
                                "&:hover": {
                                  color: "red",
                                },
                                // position: "relative",
                                // right: "-66px"
                              }}
                            >
                              <DeleteIcon
                                sx={{
                                  "&:hover": {
                                    color: "red",
                                  },
                                  marginLeft: "1rem",
                                }}
                              />
                            </IconButton>
                          )}
                        </Box>
                      </Typography>
                    </Box>
                  </Draggable>
                  {/* {!isDragging && <ConditionalContent item={item} />} */}
                  {/* </Draggable> */}
                  {getDataInternalStatus.length > 0 ? (
                    <SortableList
                      directSaleInternalStatus={item.OrderInternalStatus}
                      setDirectSaleInternalStatuses={setGetDataInternalStatus}
                      typeId={item.orderInternalStatusTypeId}
                      dataAll={getDataInternalStatus}
                      selectedStatusInterno={selectedStatusInterno}
                      setSelectedStatusInterno={setSelectedStatusInterno}
                      isVentaDirecta={isVentaDirecta}
                    />
                  ) : (
                    <Stack spacing={2}>
                      <Skeleton variant="rounded" width={210} height={40} />
                      <Skeleton variant="rounded" width={210} height={40} />
                      <Skeleton variant="rounded" width={210} height={40} />
                    </Stack>
                  )}
                </React.Fragment>
                {/* </div>
                ) } */}
              </>
              // </Draggable>
            ))}
          </Container>
        </List>
        <FeaturesStatusInterno
          newStatusInterno={newStatusInterno}
          setNewStatusInterno={setNewStatusInterno}
          setSelectedStatusInterno={setSelectedStatusInterno}
          setNewTypeStatusInterno={setNewTypeStatusInterno}
          newTypeStatusInterno={newTypeStatusInterno}
          isVentaDirecta={isVentaDirecta}
          setOpen={setOpen}
        />
      </Demo>
    </>
  );
};

export default GetLisStatusInternoOrders;
