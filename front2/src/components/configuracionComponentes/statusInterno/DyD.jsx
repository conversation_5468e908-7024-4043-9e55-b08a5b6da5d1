import {
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  CircularProgress,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { Container, Draggable } from "react-smooth-dnd";
import { arrayMoveImmutable } from "array-move";
import DragHandleIcon from "@mui/icons-material/DragHandle";
import DeleteIcon from "@mui/icons-material/Delete";
import IconButton from "@mui/material/IconButton";
import {
  deleteInternalStatus,
  deleteInternalStatusDirectSale,
  getInternalStatus,
  getInternalStatusDirectSale,
  reOrderInternalStatus,
  reOrderInternalStatusDirectSale,
} from "../../../redux/configuracionesDucks";
import { useDispatch, useSelector } from "react-redux";
import { useCookies } from "react-cookie";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import { Box } from "@mui/system";
import LockIcon from "@mui/icons-material/Lock";
import CustomizedDialogs from "./DialogStatus";

const SortableList = ({
  directSaleInternalStatus,
  setDirectSaleInternalStatuses,
  typeId,
  dataAll,
  selectedStatusInterno,
  setSelectedStatusInterno,
  isVentaDirecta,
}) => {
  const dispatch = useDispatch();
  const [cookies, setCookies] = useCookies();

  const [openDialogCard, setOpenDialogCard] = useState(false);

  const [change, setChange] = React.useState(false);

  const onDrop = ({ removedIndex, addedIndex }) => {
    const movedArray = arrayMoveImmutable(
      directSaleInternalStatus,
      removedIndex,
      addedIndex
    );

    if (isVentaDirecta()) {
      if (addedIndex == 0 || addedIndex == 1) return;
      setDirectSaleInternalStatuses((prevItems) => {
        const movedArrayDirectSale = arrayMoveImmutable(
          prevItems,
          removedIndex,
          addedIndex
        );
        return movedArrayDirectSale.map((item, index) => {
          return { ...item, orderNum: index + 1 };
        });
      });
    } else {
      setDirectSaleInternalStatuses((prevItems) => {
        return prevItems.map((item, index) => {
          if (item.orderInternalStatusTypeId === typeId) {
            const modifiedArray = movedArray.map((innerItem, innerIndex) => {
              return {
                ...innerItem,
                orderNum: innerIndex + 1,
              };
            });

            const sortedArray = [...modifiedArray].sort(
              (a, b) => a.orderNum - b.orderNum
            );

            return {
              ...item,
              OrderInternalStatus: sortedArray,
            };
          }
          return item;
        });
      });
    }

    setChange(true);
  };

  React.useEffect(() => {
    if (change === false) return;

    handleReOrder();
  }, [change]);

  const handleReOrder = () => {
    setChange(false);

    let new_order_to_status = {};

    directSaleInternalStatus.forEach((item, index) => {
      new_order_to_status[
        isVentaDirecta()
          ? item.directSaleInternalStatusId
          : item.orderInternalStatusId
      ] = item.orderNum;
    });

    dispatch(
      isVentaDirecta()
        ? reOrderInternalStatusDirectSale(
            { new_order_to_status },
            cookies.csrf_access_token
          )
        : reOrderInternalStatus(
            {
              new_order_to_status: new_order_to_status,
              id_internal_status_type: typeId,
            },
            cookies.csrf_access_token
          )
    );
    // await dispatch(getInternalStatusDirectSale());
  };

  const [isDragging, setIsDragging] = useState(false);

  const [draggingItem, setDraggingItem] = useState(null);

  const [isLoading, setIsLoading] = React.useState(false);
  const [deleteId, setDeleteId] = React.useState();

  const handleDeleteStatus = async (id) => {
    setIsLoading(true);

    setDeleteId(id);
    await dispatch(
      isVentaDirecta()
        ? deleteInternalStatusDirectSale(id, cookies.csrf_access_token)
        : deleteInternalStatus(id, cookies.csrf_access_token)
    );
    await dispatch(
      isVentaDirecta() ? getInternalStatusDirectSale() : getInternalStatus()
    );
    setIsLoading(false);
  };

  const [secureDelete, setSecureDelete] = React.useState(false);

  const [arregloSorted, setArregloSorted] = React.useState([]);

  useEffect(() => {
    setArregloSorted(
      directSaleInternalStatus?.sort((a, b) => a.orderNum - b.orderNum)
    );
  }, [directSaleInternalStatus]);

  const secureDeleteFunction = (id) => {
    setSecureDelete(id);
  };

  const openDialog = (data) => {
    // setOpenDialogCard(true);
    setSelectedStatusInterno(data);
  };

  return (
    <>
      <List>
        <Container
          dragHandleSelector=".drag-handle"
          lockAxis="y"
          onDrop={onDrop}
          onDragStart={() => setIsDragging(true)}
          onDragEnd={() => setIsDragging(false)}
        >
          {arregloSorted?.map((data, index) => {
            // bloqueando los dos primeros status internos de venta directa
            return isVentaDirecta() && (index == 0 || index == 1) ? (
              <Box sx={{ display: "flex", marginLeft: "1rem" }}>
                <ListItemIcon
                  className="drag-handle"
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    cursor: "grab",
                    color: "#ff161670",
                  }}
                >
                  <LockIcon />
                </ListItemIcon>
                <ListItemText
                  sx={{ color: "#0050a7b0" }}
                  primary={
                    isVentaDirecta()
                      ? data.directSaleInternalStatus
                      : data.orderInternalStatus
                  }
                />
              </Box>
            ) : (
              <Draggable
                key={
                  data?.[
                    isVentaDirecta()
                      ? "directSaleInternalStatusId"
                      : "orderInternalStatus"
                  ]
                }
              >
                <ListItem
                  onMouseDown={() =>
                    setDraggingItem(
                      isVentaDirecta()
                        ? data.directSaleInternalStatusId
                        : data.orderInternalStatusId
                    )
                  }
                  onMouseUp={() => setDraggingItem(null)}
                  sx={{
                    background:
                      data?.[
                        isVentaDirecta()
                          ? "directSaleInternalStatusId"
                          : "orderInternalStatusId"
                      ] ===
                      selectedStatusInterno?.[
                        isVentaDirecta()
                          ? "directSaleInternalStatusId"
                          : "orderInternalStatusId"
                      ]
                        ? "#0d6efd12"
                        : "",
                    borderRadius: "10px",
                    backgroundColor:
                      data?.[
                        isVentaDirecta()
                          ? "directSaleInternalStatusId"
                          : "orderInternalStatus"
                      ] === draggingItem
                        ? "#0d6efd12"
                        : "",
                    "&:hover": {
                      background: draggingItem ? "" : "#0d6efd12",
                      borderRadius: "10px",
                    },
                    cursor: "pointer",
                  }}
                  key={
                    data?.[
                      isVentaDirecta()
                        ? "directSaleInternalStatusId"
                        : "orderInternalStatus"
                    ]
                  }
                >
                  <ListItemIcon
                    className="drag-handle"
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      cursor: "grab",
                    }}
                  >
                    <DragHandleIcon />
                  </ListItemIcon>
                  <ListItemText
                    // onClick={() => setSelectedStatusInterno(data)}
                    onClick={() => openDialog(data)}
                    primary={
                      isVentaDirecta()
                        ? data.directSaleInternalStatus
                        : data.orderInternalStatus
                    }
                  />

                  <ListItemSecondaryAction>
                    {isLoading &&
                    // borrar el ID
                    deleteId ==
                      data?.[
                        isVentaDirecta()
                          ? "directSaleInternalStatusId"
                          : "orderInternalStatusId"
                      ] ? (
                      <CircularProgress size={20} />
                    ) : (
                      <>
                        {secureDelete ==
                        data?.[
                          isVentaDirecta()
                            ? "directSaleInternalStatusId"
                            : "orderInternalStatusId"
                        ] ? (
                          <>
                            <IconButton
                              edge="end"
                              aria-label="delete"
                              onClick={() =>
                                handleDeleteStatus(
                                  data?.[
                                    isVentaDirecta()
                                      ? "directSaleInternalStatusId"
                                      : "orderInternalStatusId"
                                  ]
                                )
                              }
                            >
                              <CheckCircleOutlineIcon
                                sx={{
                                  color: "green",

                                  "&:hover": {
                                    color: "green",
                                  },
                                }}
                              />
                            </IconButton>
                            <IconButton
                              edge="end"
                              aria-label="delete"
                              onClick={() => setSecureDelete(null)}
                            >
                              <HighlightOffIcon
                                sx={{
                                  color: "red",
                                  "&:hover": {
                                    color: "red",
                                  },
                                }}
                              />
                            </IconButton>
                          </>
                        ) : (
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            onClick={() =>
                              secureDeleteFunction(
                                data?.[
                                  isVentaDirecta()
                                    ? "directSaleInternalStatusId"
                                    : "orderInternalStatusId"
                                ]
                              )
                            }
                          >
                            <DeleteIcon
                              sx={{
                                "&:hover": {
                                  color: "red",
                                },
                              }}
                            />
                          </IconButton>
                        )}
                      </>
                    )}
                  </ListItemSecondaryAction>
                </ListItem>
              </Draggable>
            );
          })}
        </Container>
      </List>
    </>
  );
};

export default SortableList;
