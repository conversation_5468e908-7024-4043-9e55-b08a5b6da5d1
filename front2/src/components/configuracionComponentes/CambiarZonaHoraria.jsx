import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Paper,
  Grid,
  Snackbar,
  Alert,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { useTheme } from '@mui/material/styles';

const timeZones = [
  { value: 'America/Mexico_City', label: 'Ciudad de México (UTC-6)' },
  { value: 'America/Tijuana', label: 'Tijuana (UTC-8/UTC-7)' },
  { value: 'America/Hermosillo', label: 'Hermosillo (UTC-7)' },
  { value: 'America/Cancun', label: 'Cancún (UTC-5)' },
  { value: 'America/New_York', label: 'Nueva York (UTC-5/UTC-4)' },
  { value: 'America/Los_Angeles', label: 'Los Ángeles (UTC-8/UTC-7)' },
  { value: 'America/Chicago', label: 'Chicago (UTC-6/UTC-5)' },
  { value: 'America/Denver', label: 'Denver (UTC-7/UTC-6)' },
  { value: 'America/Phoenix', label: 'Phoenix (UTC-7)' },
  { value: 'America/Toronto', label: 'Toronto (UTC-5/UTC-4)' },
  { value: 'America/Vancouver', label: 'Vancouver (UTC-8/UTC-7)' },
  { value: 'America/Bogota', label: 'Bogotá (UTC-5)' },
  { value: 'America/Lima', label: 'Lima (UTC-5)' },
  { value: 'America/Santiago', label: 'Santiago (UTC-4/UTC-3)' },
  { value: 'America/Buenos_Aires', label: 'Buenos Aires (UTC-3)' },
  { value: 'America/Sao_Paulo', label: 'São Paulo (UTC-3)' },
  { value: 'America/Caracas', label: 'Caracas (UTC-4)' },
  { value: 'America/La_Paz', label: 'La Paz (UTC-4)' },
  { value: 'America/Montevideo', label: 'Montevideo (UTC-3)' },
  { value: 'America/Havana', label: 'La Habana (UTC-5/UTC-4)' },
  { value: 'America/Santo_Domingo', label: 'Santo Domingo (UTC-4)' },
  { value: 'America/Guatemala', label: 'Guatemala (UTC-6)' },
  { value: 'America/Panama', label: 'Panamá (UTC-5)' },
  { value: 'Europe/Madrid', label: 'Madrid (UTC+1/UTC+2)' },
  { value: 'Asia/Tokyo', label: 'Tokio (UTC+9)' },
  { value: 'Australia/Sydney', label: 'Sídney (UTC+10/UTC+11)' }
];

export default function CambiarZonaHoraria() {
  const theme = useTheme();
  const [selectedTimeZone, setSelectedTimeZone] = useState('');
  const [currentTimeZone, setCurrentTimeZone] = useState('');
  const [currentTime, setCurrentTime] = useState('');
  const [previewTime, setPreviewTime] = useState('');
  const [open, setOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [severity, setSeverity] = useState('success');
  const [openDialog, setOpenDialog] = useState(false);
  const [detectedTimeZone, setDetectedTimeZone] = useState('');
  const [detectedTime, setDetectedTime] = useState('');

  useEffect(() => {
    const savedTimeZone = localStorage.getItem('timeZone') || 'America/Mexico_City';
    setCurrentTimeZone(savedTimeZone);
    setSelectedTimeZone(savedTimeZone);

    updateCurrentTime(savedTimeZone);
    const intervalId = setInterval(() => updateCurrentTime(currentTimeZone), 1000);

    return () => clearInterval(intervalId);
  }, [currentTimeZone]);

  useEffect(() => {
    if (selectedTimeZone && selectedTimeZone !== currentTimeZone) {
      updatePreviewTime(selectedTimeZone);
    } else {
      setPreviewTime('');
    }
  }, [selectedTimeZone, currentTimeZone]);

  const updateCurrentTime = (tz) => {
    const timeZone = Intl.supportedValuesOf('timeZone').includes(tz) ? tz : 'America/Mexico_City';
    const options = {
      timeZone,
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    };
    setCurrentTime(new Date().toLocaleString('es-MX', options));
  };

  const updatePreviewTime = (tz) => {
    const timeZone = Intl.supportedValuesOf('timeZone').includes(tz) ? tz : 'America/Mexico_City';
    const options = {
      timeZone,
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    };
    const formattedDate = new Date().toLocaleString('es-MX', options);
    setPreviewTime(formattedDate);
  };

  const handleTimeZoneChange = (event) => {
    setSelectedTimeZone(event.target.value);
  };

  const detectBrowserTimeZone = () => {
    try {
      const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const isInList = timeZones.some(tz => tz.value === browserTimeZone);
      const detected = isInList ? browserTimeZone : 'America/Mexico_City';
      setDetectedTimeZone(detected);

      const options = {
        timeZone: detected,
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      };
      setDetectedTime(new Date().toLocaleTimeString('es-MX', options));
      setOpenDialog(true);
    } catch (error) {
      console.error('Error al detectar zona horaria:', error);
      setSelectedTimeZone('America/Mexico_City');
      setAlertMessage('No se pudo detectar la zona horaria. Se usará Ciudad de México');
      setSeverity('warning');
      setOpen(true);
    }
  };

  const confirmDetectedTimeZone = () => {
    setSelectedTimeZone(detectedTimeZone);
    setOpenDialog(false);
    try {
      localStorage.setItem('timeZone', detectedTimeZone);
      setCurrentTimeZone(detectedTimeZone);
      setAlertMessage('Zona horaria del navegador aplicada y guardada');
      setSeverity('success');
      setOpen(true);
    } catch (error) {
      console.error('Error al guardar la configuración:', error);
      setAlertMessage('Error al actualizar la configuración');
      setSeverity('error');
      setOpen(true);
    }
  };

  const handleSaveSettings = () => {
    try {
      localStorage.setItem('timeZone', selectedTimeZone);
      setCurrentTimeZone(selectedTimeZone);
      setAlertMessage('Configuración actualizada correctamente');
      setSeverity('success');
      setOpen(true);
    } catch (error) {
      console.error('Error al guardar la configuración:', error);
      setAlertMessage('Error al actualizar la configuración');
      setSeverity('error');
      setOpen(true);
    }
  };

  const handleCancelChanges = () => {
    setSelectedTimeZone(currentTimeZone);
    setPreviewTime('');
  };

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') return;
    setOpen(false);
  };

  const isConfigChanged = () => selectedTimeZone !== currentTimeZone;

  return (
    <Box sx={{ p: 3 }}>
      <Paper elevation={3} sx={{ p: 3, mb: 3, borderRadius: 8 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <AccessTimeIcon fontSize="large" color="primary" />
          </Grid>
          <Grid item>
            <Typography variant="h5" component="h1" gutterBottom>
              Configuración de Zona Horaria
            </Typography>
          </Grid>
        </Grid>

        <Typography variant="body1" paragraph>
          La configuración de zona horaria afecta cómo se muestran las fechas y horas en todo el sistema.
        </Typography>

        <Box sx={{ mb: 3, p: 2, bgcolor: theme.palette.background.default, borderRadius: 4 }}>
          <Typography variant="subtitle1" fontWeight="bold">
            Fecha y hora actual del sistema:
          </Typography>
          <Typography variant="h6">
            {previewTime || currentTime}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Zona horaria: {previewTime ? selectedTimeZone : currentTimeZone}
          </Typography>
        </Box>


        <Stack spacing={3}>
          <FormControl fullWidth>
            <InputLabel id="timezone-select-label">Seleccionar zona horaria</InputLabel>
            <Select
              labelId="timezone-select-label"
              id="timezone-select"
              value={selectedTimeZone}
              label="Seleccionar zona horaria"
              onChange={handleTimeZoneChange}
            >
              {timeZones.map((tz) => (
                <MenuItem key={tz.value} value={tz.value}>
                  {tz.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Stack direction="column" spacing={2}>
            <Button variant="outlined" color="buttonGreenPink" onClick={detectBrowserTimeZone}>
              Detectar zona horaria del navegador
            </Button>
            <Button variant="contained" color="buttonGreenPink" onClick={handleSaveSettings} disabled={!isConfigChanged()}>
              Guardar cambios
            </Button>
            <Button onClick={handleCancelChanges} disabled={!isConfigChanged()} variant="outlined" color="error">
              Cancelar
            </Button>
          </Stack>
        </Stack>
      </Paper>

      <Snackbar open={open} autoHideDuration={6000} onClose={handleClose}>
        <Alert onClose={handleClose} severity={severity} sx={{ width: '100%' }}>
          {alertMessage}
        </Alert>
      </Snackbar>

      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        aria-labelledby="timezone-dialog-title"
        aria-describedby="timezone-dialog-description"
      >
        <DialogTitle id="timezone-dialog-title">Confirmar zona horaria</DialogTitle>
        <DialogContent>
          <DialogContentText id="timezone-dialog-description">
            Se ha detectado la zona horaria: {detectedTimeZone}
            <br />
            La hora actual en esta zona es: {detectedTime}
            <br /><br />
            ¿Es esta su zona horaria correcta?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} variant="outlined" color="error">
            Cancelar
          </Button>
          <Button onClick={confirmDetectedTimeZone} color="]buttonGreenPink" autoFocus>Confirmar</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
