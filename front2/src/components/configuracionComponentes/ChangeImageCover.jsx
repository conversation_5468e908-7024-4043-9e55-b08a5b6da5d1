import { Box, Typography } from "@mui/material";
import DragDropBox from "./DargDropBox";
import responsive from "../../components/css/configuraciones/configResponsive.module.css";

const ChageImageCover = () => {
  return (
    <Box sx={{ width: "80%", margin: "auto" }}>
      {/* <h2>Imagen de Portada </h2> */}
      <Box
        // sx={{ padding: "0rem 4rem 1rem 4rem" }}
        className={responsive.TitleChangeImage}
      >
        <Typography variant="h6" component="h2">
          Imagen de Portada
        </Typography>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ padding: "0rem 0rem 0 2rem" }}
        >
          Arrastra y suelta una imagen para subirla
          <br />
          La imagen de Portada es la imagen que te da la bienvenida al sistema
          despues de logearte
        </Typography>
      </Box>
      <DragDropBox type={"portada"} />
      {/* <GetAlert /> */}
      <Box
        // sx={{ padding: "1rem 4rem 0 4rem" }}
        className={responsive.TitleChangeImage}
      >
        <Typography variant="h6" component="h5">
          Sugerencia:
        </Typography>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ padding: "0rem 0rem 0 2rem", display: "flex" }}
        >
          Tambien podrias cambiar la{" "}
          <Typography
            variant="body2"
            sx={{ marginLeft: "0.3rem" }}

          >
            {"  "} imagen de Login
          </Typography>
        </Typography>
      </Box>
    </Box>
  );
};

export default ChageImageCover;
