import * as React from "react";
import ListSubheader from "@mui/material/ListSubheader";
import List from "@mui/material/List";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Collapse from "@mui/material/Collapse";
import InboxIcon from "@mui/icons-material/MoveToInbox";
import DraftsIcon from "@mui/icons-material/Drafts";
import SendIcon from "@mui/icons-material/Send";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import StarBorder from "@mui/icons-material/StarBorder";
import { Box, Typography } from "@mui/material";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useNavigate } from "react-router-dom";
import StoreMallDirectoryIcon from "@mui/icons-material/StoreMallDirectory";
import configStyle from "../../components/css/configuraciones/configResponsive.module.css";
import LibraryAddCheckIcon from '@mui/icons-material/LibraryAddCheck';
import PlaylistAddIcon from '@mui/icons-material/PlaylistAdd';
import AddPhotoAlternateIcon from '@mui/icons-material/AddPhotoAlternate';
import PanoramaWideAngleSelectIcon from '@mui/icons-material/PanoramaWideAngleSelect';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

export default function NavbarSettings() {
  const navigate = useNavigate();

  const getStructSettings = (message, path) => {
    return message;
    // const styleBoxSettings = {
    //   width: "80%",
    //   margin: "auto",
    //   padding: ".5em 1em",
    //   background: "#D9D9D9",
    //   borderRadius: "10px",
    //   display: "flex",
    //   justifyContent: "space-around",
    //   marginTop: "1em",
    //   cursor: "pointer",
    // };

    // const getUrl = () => {
    //   if (path) {
    //     navigate(path);
    //   }
    // };
    // return (
    //   <Box sx={styleBoxSettings} onClick={getUrl}>
    //     <Box>{message}</Box>
    //     <ArrowForwardIosIcon color="primary" />
    //   </Box>
    // );
  };

  const redirectPath = (path) => {
    if (path !== "") {
      navigate(path);
    }
    return;
  };

  return (
    <>
      <List
      
        className={configStyle.list}
        component="nav"
        aria-labelledby="nested-list-subheader"
      subheader={
        <ListSubheader component="div" id="nested-list-subheader">
          Conifguración
        </ListSubheader>
      }
      >
        <Box
          sx={{
            width: "100%",
            display: "flex",
            justifyContent: "space-around",
            flexDirection: "column",
          }}
        >

          <Box sx={{ marginTop: "10%" }}>
            <Typography gutterBottom component="div" variant="h6">
              Configuración Marketplace
            </Typography>
            <ListItemButton
              className={configStyle.listItemButton}
              onClick={() => redirectPath("/configuracion/marketplace")}
            >
              <ListItemIcon>
                <StoreMallDirectoryIcon />
              </ListItemIcon>
              <ListItemText
                primary={getStructSettings("Establece marketplaces")}
              />
            </ListItemButton>
            <ListItemButton
              className={configStyle.listItemButton}
              onClick={() => redirectPath("/configuracion/statusInterno")}
            >
              <ListItemIcon>
                <PlaylistAddIcon />
              </ListItemIcon>
              <ListItemText
                primary={getStructSettings("Configuracion status interno")}
              />
            </ListItemButton>
          </Box>

          <Box sx={{ marginTop: "10%" }}>
            <Typography gutterBottom component="div" variant="h6">
              Configuración Venta Directa
            </Typography>
            <ListItemButton
              className={configStyle.listItemButton}
              onClick={() =>
                redirectPath("/configuracion/statusInterno/ventaDirecta")
              }
            >
              <ListItemIcon>
                <PlaylistAddIcon />

              </ListItemIcon>
              <ListItemText
                primary={getStructSettings("Status Interno Venta Directa")}
              />
            </ListItemButton>
          </Box>

          <Box sx={{ marginTop: "10%" }}>
            <Typography gutterBottom component="div" variant="h6">
              General
            </Typography>
            <ListItemButton
              className={configStyle.listItemButton}
              onClick={() =>
                redirectPath("/configuracion/cambiarImagenPortada")
              }
            >
              <ListItemIcon>
                <PanoramaWideAngleSelectIcon />
              </ListItemIcon>
              <ListItemText
                primary={getStructSettings("Cambiar imagen portada")}
              />
            </ListItemButton>
            <ListItemButton
              className={configStyle.listItemButton}
              onClick={() => redirectPath("/configuracion/cambiarImagenLogin")}
            >
              <ListItemIcon>
                <AddPhotoAlternateIcon />
              </ListItemIcon>
              <ListItemText
                primary={getStructSettings("Cambiar imagen Login")}
              />
            </ListItemButton>

            <ListItemButton
              className={configStyle.listItemButton}
              onClick={() => redirectPath("/configuracion/cambiarZonaHoraria")}
            >
              <ListItemIcon>
                <AccessTimeIcon />
              </ListItemIcon>
              <ListItemText
                primary={getStructSettings("Cambiar zona horaria")}
              />
            </ListItemButton>

          </Box>
        </Box>
      </List>
    </>
  );
}
