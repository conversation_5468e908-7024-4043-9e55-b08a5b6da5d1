import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { styled, useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'; // Asegúrate de importar ExpandMoreIcon
import { Accordion, AccordionSummary, AccordionDetails, Box, Paper, Alert } from '@mui/material';
import { AccordionDetailsGeneric } from './AccordeonDetails';
import { cleanCheck, desynchronizeMarketplaceGroup } from '../../../redux/configuracionesDucks';
import { AlertComponent } from '../../componentesGenerales/Alert';
import { useCookies } from "react-cookie";

const Item = styled(Paper)(({ theme }) => ({
    backgroundColor: theme.palette.mode === 'dark'
        ? theme.palette.background.paper
        : '#fff',
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: 'center',
    color: theme.palette.text.secondary,
    maxWidth: 400,
}));

export const GroupMarketplace = () => {
    const [cookies, setCookie] = useCookies();
    const groupsMarketplace = useSelector(state => state.ConfiguracionSistema.groupsMarketplace);
    const loadingGroupsMarketplace = useSelector(state => state.ConfiguracionSistema.loadingGroupsMarketplace);

    const [selectedMarketplace, setSelectedMarketplace] = useState(null);
    const [expanded, setExpanded] = useState(null);

    const handleMarketplaceClick = (marketplace) => {
        setSelectedMarketplace(marketplace);
        setExpanded(expanded === marketplace ? null : marketplace); // Toggle the accordion
    };

    const theme = useTheme();

    const getMarketplaceStyle = (marketplace) => ({
        alignItems: 'center',
        ml: 4,
        gap: 2,
        cursor: 'pointer',
        bgcolor: selectedMarketplace === marketplace ? theme.palette.background.default : 'transparent',
        borderRadius: 1,
        p: 1,
        '&:hover': {
            bgcolor: theme.palette.background.default,
        }
    });

    const { mensaje, status, checkCredentials } = useSelector(state => state.ConfiguracionSistema);
    const dispatch = useDispatch();
    const handleDesync = async (marketplace) => {
            dispatch(desynchronizeMarketplaceGroup(marketplace.id, cookies.csrf_access_token));
        }

    return (
        <>
            {mensaje &&
                <AlertComponent color={status < 300 ? 'success' : 'error'} message={mensaje} cleanMessage={cleanCheck} />
            }
            {loadingGroupsMarketplace ? (
                <p>Cargando...</p>
            ) : (
                <>
                    {groupsMarketplace?.supportedMarketplaceGroups?.map((supportedMarketplaceGroup, index) => (
                        <Item key={index} sx={{ my: 1, mx: 'auto', p: 2 , maxWidth:"600px"}}>
                            <AccordionDetailsGeneric
                                supportedMarketplaceGroup={supportedMarketplaceGroup}
                                index={index}
                                expanded={expanded}
                                handleMarketplaceClick={handleMarketplaceClick}
                                selectedMarketplace={selectedMarketplace}
                                handleDesync={handleDesync}
                            />
                            {expanded?.id !== supportedMarketplaceGroup.id && (
                                <Box sx={{ display: 'flex', justifyContent: 'center', flexDirection: "column", mt: 2, gap: 1, ml: 4 }}>
                                    {supportedMarketplaceGroup.supportedMarketplaces.map((supportedMarketplace, index) => (
                                        // group.supportedMarketplaces.length > 1 &&
                                        <Stack key={index} spacing={4} direction="row" sx={{ alignItems: 'center', gap: 2 }}>
                                            <Stack>
                                                <Avatar src={supportedMarketplace.urlPhoto} sx={{ width: 36, height: 36 }} alt={supportedMarketplace.name} />
                                            </Stack>
                                            <Stack sx={{ minWidth: 0 }}>
                                                <Typography noWrap>{supportedMarketplace.name}</Typography>
                                            </Stack>
                                        </Stack>
                                    ))}
                                </Box>
                            )}
                        </Item>
                    ))}
                </>

            )}
        </>
    );
};