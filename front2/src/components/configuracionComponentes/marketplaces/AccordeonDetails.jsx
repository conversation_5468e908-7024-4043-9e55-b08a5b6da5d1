import React, { useState, useEffect } from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import { useDispatch, useSelector } from 'react-redux';
import { Alert, Button, TextField } from '@mui/material';
import theme from '../../../temaConfig';
import { setCredentialMarketplaceGroup, updateMarketplaceWithCredentialsGroup } from '../../../redux/configuracionesDucks';
import LoadingButton from '@mui/lab/LoadingButton';
import { useCookies } from "react-cookie";

export const AccordionDetailsGeneric = ({
    supportedMarketplaceGroup,
    index,
    expanded,
    handleMarketplaceClick,
    handleDesync,
}) => {
    const [supportedMarketplaceGroupState, setSupportedMarketplaceGroupState] = useState(supportedMarketplaceGroup);
    const [cookies, setCookie] = useCookies();
    const loadingSetMarketplace = useSelector(state => state.ConfiguracionSistema.loadingSetMarketplace);


    const handleChanges = (e, key) => {
        const { value } = e.target;
        setSupportedMarketplaceGroupState(prev => {
            if (!prev.marketplaceGroup) {
                return {
                    ...prev,
                    marketplaceGroup: {
                        id: prev.id,
                        marketplaceGroupCredentials: {
                            [key]: value
                        },
                        new: true
                    }
                }
            }
            return {
                ...prev,
                marketplaceGroup: {
                    ...prev.marketplaceGroup,
                    marketplaceGroupCredentials: {
                        ...prev.marketplaceGroup.marketplaceGroupCredentials,
                        [key]: value
                    }
                }
            };
        });
    };

    const dispatch = useDispatch();



    const handleSubmit = (e) => {
        e.preventDefault();
        const credentials = {
                clientId: supportedMarketplaceGroupState.marketplaceGroup.marketplaceGroupCredentials.clientId,
                clientSecret: supportedMarketplaceGroupState.marketplaceGroup.marketplaceGroupCredentials.clientSecret,
                refreshToken: supportedMarketplaceGroupState.marketplaceGroup.marketplaceGroupCredentials.refreshToken || '',
                code: supportedMarketplaceGroupState.marketplaceGroup.marketplaceGroupCredentials.codeMl || '',
                redirectUrl: supportedMarketplaceGroupState.marketplaceGroup.marketplaceGroupCredentials.urlMl || ''
            }
        console.log('0000000000000000000000000000000000000000000000000')
        console.log('credentials', supportedMarketplaceGroupState);
        console.log('0000000000000000000000000000000000000000000000000')

        dispatch(supportedMarketplaceGroupState.marketplaceGroup.marketplaceGroupCredentials.status === "No sincronizado" ? 
            setCredentialMarketplaceGroup({
                supportedMarketplaceGroupId: supportedMarketplaceGroupState.id,
                credentials: credentials
            }, cookies.csrf_access_token) : 
            updateMarketplaceWithCredentialsGroup( supportedMarketplaceGroupState.id, credentials, cookies.csrf_access_token)
        );
    }
    // debugger

    return (
        <>
            <Accordion
                key={index}
                expanded={expanded === supportedMarketplaceGroup}
                onChange={() => handleMarketplaceClick(supportedMarketplaceGroup)}
                style={{
                    borderRadius: '10px',
                }}
                sx={{
                    backgroundColor: 'transparent',
                    boxShadow: 'none',
                    maxWidth: 'fit-content',

                }} // Set background to transparent
            >
                <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`panel-${index}-content`}
                    id={`panel-${index}-header`}
                >
                    <Stack spacing={6} direction="row" sx={{ alignItems: 'center', gap: 2, maxWidth: "fit-content" }}>
                        <Stack>
                            <Avatar src={supportedMarketplaceGroup.urlPhoto} sx={{ width: 36, height: 36 }} alt={supportedMarketplaceGroup.name} />
                        </Stack>
                        <Stack sx={{ minWidth: 0 }}>
                            <Typography sx={{ textAlign: "start" }} noWrap>{supportedMarketplaceGroup.name}</Typography>
                            <Typography sx={{
                                color: supportedMarketplaceGroup?.marketplaceGroup?.marketplaceGroupCredentials && supportedMarketplaceGroup?.marketplaceGroup?.marketplaceGroupCredentials.check ? theme => theme.palette.success.main : theme => theme.palette.error.main,

                            }}> {
                                    supportedMarketplaceGroup?.marketplaceGroup?.marketplaceGroupCredentials && supportedMarketplaceGroup?.marketplaceGroup?.marketplaceGroupCredentials.status ? supportedMarketplaceGroup?.marketplaceGroup?.marketplaceGroupCredentials.status : 'Sin credenciales'
                                }
                            </Typography>
                        </Stack>
                    </Stack>
                </AccordionSummary>
                <AccordionDetails>
                    <form style={{ width: '100%' }} onSubmit={handleSubmit}>
                        <TextField
                            id={`clientId-${index}`}
                            label="clientId"
                            variant="outlined"
                            fullWidth
                            sx={{ mb: 2 }}
                            value={
                                supportedMarketplaceGroupState?.marketplaceGroup?.marketplaceGroupCredentials?.clientId || ''
                            }
                            onChange={(e) => handleChanges(e, 'clientId')}
                        />
                        <TextField
                            id={`clientSecret-${index}`}
                            label="clientSecret"
                            variant="outlined"
                            fullWidth
                            sx={{ mb: 2 }}
                            value={supportedMarketplaceGroupState?.marketplaceGroup?.marketplaceGroupCredentials?.clientSecret || ''} // Use an empty string for default value //revisar usar effect para bprrar
                            onChange={(e) => handleChanges(e, 'clientSecret')}
                        />
                        {/* solo en Mzon */}
                        {supportedMarketplaceGroup.name === "Amazon" &&
                            <TextField
                                id={`refreshToken-${index}`}
                                label="refreshToken"
                                variant="outlined"
                                fullWidth
                                sx={{ mb: 2 }}
                                value={supportedMarketplaceGroupState?.marketplaceGroup?.marketplaceGroupCredentials?.refreshToken || ''} // Use an empty string for default value
                                onChange={(e) => handleChanges(e, 'refreshToken')}
                            />
                        }
                        {/* solo en ML */}
                        {supportedMarketplaceGroup.name === "Mercado Libre" &&
                            <>
                                <TextField
                                    id={`accessToken-${index}`}
                                    label="Code Ml2"
                                    variant="outlined"
                                    fullWidth
                                    sx={{ mb: 2 }}
                                    value={supportedMarketplaceGroupState?.marketplaceGroup?.marketplaceGroupCredentials?.codeMl || ''} // Use an empty string for default value
                                    onChange={(e) => handleChanges(e, 'codeMl')}
                                />
                                <TextField
                                    id={`accessToken-${index}`}
                                    label="Url Ml"
                                    variant="outlined"
                                    fullWidth
                                    sx={{ mb: 2 }}
                                    value={supportedMarketplaceGroupState?.marketplaceGroup?.marketplaceGroupCredentials?.urlMl || ''} // Use an empty string for default value
                                    onChange={(e) => handleChanges(e, 'urlMl')}
                                />
                            </>
                        }
                        <LoadingButton
                            size="small"
                            type='submit'
                            //   endIcon={<SendIcon />}
                            loading={loadingSetMarketplace}
                            loadingPosition="end"
                            variant="contained"
                            color="buttonGreen"
                            fullWidth
                            sx={{ mt: 2 }}
                        >
                            Guardar
                        </LoadingButton>
                        <Button
                            variant="outlined"
                            color="buttonGreenPink"
                            fullWidth
                            sx={{ mt: 2 }}
                            onClick={() => {
                                handleDesync(supportedMarketplaceGroup);
                            }}
                        >
                            Desincronizar
                        </Button>
                    </form>
                </AccordionDetails>
            </Accordion >
        </>
    );
};