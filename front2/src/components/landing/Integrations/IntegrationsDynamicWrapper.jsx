// se recibe un id por medio de useParams, se renderiza un componente de IntegrationsDynamic que contiene la información de los planes
// se renderiza un componente de IntegrationsDynamic que contiene la información de los planes

import { useNavigate, useParams } from "react-router-dom";
import { IntegrationsDynamic } from "./IntegrationsDynamic";
import theme from "../../../temaConfig";
import { useMediaQuery } from "@mui/material";
import { useTheme } from "@emotion/react";

export const IntegrationsDynamicWrapper = () => {
    const { id } = useParams();

    // Datos dinámicos según la ruta y se mostraran en cada desglose de cada plan
    const content = {
        marketplaces: {
            title: "TIENDA VIRTUAL + MARKETPLACES",
            //   color: "#FF6347",
            features: [
                "Sincroniza tanto marketplaces como tiendas oficiales.",
                "Da seguimiento en tiempo real a las órdenes y comunícate con todo tu equipo sin salir de la aplicación.",
                "Aplica reglas del negocio y precios por cada punto de venta.",
                "Mide la rentabilidad de cada venta en un solo punto.",
                "Consulta, Exporta e Importa tus productos y órdenes.",
                "Ten un punto de venta físico para ventas por otros medios."
            ],
            image: "/integrations/tienda.svg" // Imagen específica
        },
        archivos: {
            title: "ARCHIVOS + TIENDA VIRTUAL + MARKETPLACES",
            //   color: "#FF5733",
            features: [
                "Conecta tu sistema de gestión empresarial para mantener órdenes y stock sincronizados.",
                "Sincroniza tanto marketplaces como tiendas oficiales.",
                "Da seguimiento en tiempo real a las órdenes.",
                "Aplica reglas del negocio y precios por cada punto de venta.",
                "Mide la rentabilidad de cada venta en un solo punto.",
                "Consulta, Exporta e Importa tus productos y órdenes."
            ],
            image: "/integrations/archivo.svg" // Imagen específica
        },
        erp: {  // Nueva sección ERP
            title: "ERP + TIENDA VIRTUAL + MARKETPLACES",
            color: "#FF8C00",
            features: [
                "Sube tus listas desde archivos de Excel.",
                "Sincroniza tanto marketplaces como tiendas oficiales.",
                "Da seguimiento en tiempo real a las órdenes y comunícate con todo tu equipo sin salir de la aplicación.",
                "Aplica reglas del negocio y precios por cada punto de venta.",
                "Mide la rentabilidad de cada venta en un solo punto.",
                "Consulta, exporta e importa tus productos y órdenes.",
                "Ten un punto de venta físico para ventas por otros medios, cotiza y sigue todo el proceso.",
                "* Con un costo extra integramos los inventarios de tus proveedores en tiempo real."
            ],
            image: "/integrations/erp.svg"  // Imagen específica
        }
    };

    // Fallback si el ID no existe
    const data = content[id] || {
        title: "No encontrado",
        color: "#555",
        features: ["La página no existe."],
        image: "/integrations/erp.svg"
    };

    const navigateTo = useNavigate();

    const theme = useTheme();

    const isMobile = useMediaQuery(theme.breakpoints.down("sm"))

    return (
        <div style={{ minHeight: "90vh", backgroundColor: "white" }}>
            <img
                alt="flecha regresar"
                src="/integrations/arrowBack.svg"
                style={{
                    position: isMobile ? "relative" : "absolute",
                    top: isMobile ? "0" : "10em",
                    margin: isMobile && "1em 0",
                    left: "3em",
                    width: "25px",
                    height: "25px",
                    cursor: "pointer",
                    transition: "transform 0.3s ease, box-shadow 0.3s ease",
                    backgroundColor: "transparent",
                    
                }}
                onClick={() => navigateTo("/landing/integraciones")}
                onMouseEnter={(e) => {
                    e.currentTarget.style.transform = "scale(.8) translateY(-5px)";
                }}
                onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "scale(1) translateY(0)";
                    e.currentTarget.style.boxShadow = "none";
                }}
            />

            {/* se renderizara el componente de IntegrationsDynamic que contiene la información de los planes */}
            <IntegrationsDynamic
                title={data.title}
                color={data.color}
                features={data.features}
                image={data.image}
            />
        </div>
    );
};
