// renderiza las cards donde van los planes

import { Box, Typography, useMediaQuery, useTheme } from "@mui/material";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";


export const ModalidadesIntegracion = () => {

    const navigate = useNavigate();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

    // Variante de animacion de las cards
    const cardVariants = {
        hidden: { opacity: 0, y: 50 },
        visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: "easeOut" } },
        hover: { scale: 1.05, transition: { duration: 0.4, ease: "easeInOut" } }
    };

    // regresa la flecha de cada card con su respectivo color y animacion
    const returArrow = (color = "#ff5733") => {
        return (
            <Box
                sx={{
                    position: "absolute",
                    bottom: "3em",
                    width: "40px",
                    height: "40px"
                }}
            >
                <motion.svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="40"
                    height="40"
                    viewBox="0 0 56.729 56.843"
                    initial={{ y: 0 }}
                    animate={{ y: [0, -5, 0] }}
                    transition={{ duration: 2, ease: "easeInOut", repeat: Infinity }}
                >
                    <g transform="translate(204.937 306.738) rotate(180)">
                        <path
                            d="M2093.629,219.552l-22.765,22.765,22.765,22.765"
                            transform="translate(-1917 36)"
                            fill="none"
                            stroke={color}
                            strokeLinecap="round"
                            strokeWidth="8"
                        />
                        <path
                            d="M2149.528,214.86h-43.276"
                            transform="translate(-1948.592 63.457)"
                            fill="none"
                            stroke={color}
                            strokeLinecap="round"
                            strokeWidth="8"
                        />
                    </g>
                </motion.svg>
            </Box>

        )
    }

    return (
        <Box
            sx={{
                backgroundColor: "#FFF",
                // padding: "60px 0 0 0 ",
                textAlign: "center",
                height: "100%",
                padding: "60px calc(50vw - 45%) 60px calc(50vw - 45%)",

            }}
        >
            {/* Título */}
            <Typography
                variant="h4"
                sx={{
                    color: "#355438", fontWeight: "bold", marginBottom: "50px",
                    fontSize: "calc(1.3rem + 1vw)",
                }}
            >
                Modalidades <br />
                de integración
            </Typography>


            {/* Contenedor de las cards */}
            <Box
                sx={{
                    display: "flex",
                    flexDirection: isMobile ? "column" : "row",
                    justifyContent: "space-around",
                    gap: "40px",
                }}
            >
                {/* CARD 1 */}
                <motion.div
                    variants={cardVariants}
                    initial="hidden"
                    animate="visible"
                    whileHover="hover"
                    style={{ margin: "0 auto" }}
                    onClick={() => navigate("/landing/tienda/erp")}
                >
                    <Box
                        sx={{
                            width: isMobile ? "250px" : "250px",
                            height: "500px",
                            borderRadius: "160px",
                            backgroundColor: theme.palette.text.pinkMain,
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            boxShadow: "0 8px 24px rgba(0,0,0,0.1)",
                            position: "relative",
                            backgroundImage: "url('/integrations/cuadro.png')",
                            backgroundSize: "90%",
                            backgroundRepeat: "no-repeat",
                            backgroundPosition: "center",
                            cursor: "pointer"
                        }}
                    >
                        <Typography
                            sx={{ color: "#263A2A", fontWeight: "bold", fontSize: "20px", marginTop: "4em" }}
                        >
                            ERP <br /> +   <br />TIENDA VIRTUAL <br /> +  <br /> MARKETPLACES
                        </Typography>
                        {/* <img src="/integrations/arrow.svg" alt="Arrow" style={{ width: "40px", height: "40px", position: "absolute", bottom: "3em" }} /> */}
                        {returArrow("#273a2a")}
                    </Box>

                </motion.div>

                {/* CARD 2 */}
                <motion.div
                    variants={cardVariants}
                    initial="hidden"
                    animate="visible"
                    whileHover="hover"
                    style={{ margin: "0 auto" }}
                    onClick={() => navigate("/landing/tienda/marketplaces")}
                >
                    <Box
                        sx={{
                            width: isMobile ? "250px" : "250px",
                            height: "500px",
                            borderRadius: "160px",
                            backgroundColor: "#E76A33",
                            display: "flex",
                            flexDirection: "column",
                            // justifyContent: "center",
                            alignItems: "center",
                            boxShadow: "0 8px 24px rgba(0,0,0,0.1)",
                            position: "relative",
                            backgroundImage: "url('/integrations/circulo.png')",
                            backgroundSize: "90%",
                            backgroundRepeat: "no-repeat",
                            backgroundPosition: "center",
                            cursor: "pointer"

                        }}
                    >
                        <Typography
                            sx={{ color: "#FFF", fontWeight: "bold", fontSize: "20px", marginTop: "4em" }}
                        >
                            TIENDA VIRTUAL <br /> +  <br /> MARKETPLACES
                        </Typography>
                        {returArrow("#FFFFFF")}

                    </Box>
                </motion.div>

                {/* CARD 3 */}
                <motion.div
                    variants={cardVariants}
                    initial="hidden"
                    animate="visible"
                    whileHover="hover"
                    style={{ margin: "0 auto" }}
                    onClick={() => navigate("/landing/tienda/archivos")}
                >
                    <Box
                        sx={{
                            width: isMobile ? "250px" : "250px",
                            height: "500px",
                            borderRadius: "160px",
                            backgroundColor: theme.palette.primary.backgroundPink,
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            boxShadow: "0 8px 24px rgba(0,0,0,0.1)",
                            position: "relative",
                            backgroundImage: "url('/integrations/triangulo.png')",
                            backgroundSize: "90%",
                            backgroundRepeat: "no-repeat",
                            backgroundPosition: "center",
                            cursor: "pointer"

                        }}
                    >
                        {/* <img src="/integrations/cuadro.png" alt="Cuadro" style={{ width: "80px", height: "80px", marginBottom: "20px" }} /> */}
                        <Typography
                            sx={{
                                color: theme.palette.colorGreen.main
                                , fontWeight: "bold", fontSize: "20px", marginTop: "4em"
                            }}
                        >
                            ARCHIVOS <br /> +  <br /> TIENDA VIRTUAL <br /> +  <br /> MARKETPLACES
                        </Typography>
                        {returArrow(theme.palette.colorGreen.main)}

                    </Box>
                </motion.div>
            </Box>
        </Box>
    );
};
