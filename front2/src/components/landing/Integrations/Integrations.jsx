// contenedor de las integraciones, titulo principal y cards de planes e imagenes 
import { Box, Typography, useMediaQuery, useTheme } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { ModalidadesIntegracion } from "./ModalidadesIntegracion";
import "./Integrations.css";  // Estilos CSS externos
import { motion } from "framer-motion";

export const Integrations = () => {

    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
    const isTablet = useMediaQuery(theme.breakpoints.down("md"));

    const navigateTo = useNavigate();

    return (
        <Box>

            <Box
                sx={{
                    backgroundColor: "#FFEFEA",
                    padding: "60px 0",
                    textAlign: "center",
                    minHeight: "100vh",
                }}
            >
                <Box
                    sx={{
                        textAlign: "center",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        flexDirection: "row",
                        gap: "2rem",
                    }}
                >
                    {/* Imagen izquierda animada */}
                    <motion.img
                        src="starQuestions.png"
                        alt="Estrella izquierda"
                        style={{ maxWidth: "100%", height: "auto", width: "35px" }}
                        initial={{ y: 0 }}
                        animate={{ y: [0, -5, 0] }}
                        transition={{ duration: 2, ease: "easeInOut", repeat: Infinity }}
                    />

                    <Typography
                        variant="h2"
                        align="center"
                        gutterBottom
                        sx={{
                            color: theme.palette.colorGreen.main,
                            padding: "1rem 0",
                            fontSize: "calc(1.3rem + 1vw)",
                        }}
                    >
                        Integraciones
                    </Typography>

                    {/* Imagen derecha animada */}
                    <motion.img
                        src="starQuestions.png"
                        alt="Estrella derecha"
                        style={{ maxWidth: "100%", height: "auto", width: "35px" }}
                        initial={{ y: 0 }}
                        animate={{ y: [0, -5, 0] }}
                        transition={{ duration: 2, ease: "easeInOut", repeat: Infinity }}
                    />
                </Box>

                {/* //imagenes principales */}
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        position: "relative",
                        marginTop: "50px",
                        gap: isMobile ? "20px" : "0",
                    }}
                >
                    {/* MARKETPLACES */}
                    <Box
                        className="image-container"
                        onClick={() => navigateTo("/landing/tienda/archivos")}
                        sx={{
                            backgroundImage: "url('/integrations/barMarketplace1.png')",
                            backgroundSize: "cover",
                            backgroundRepeat: "no-repeat",
                            backgroundPosition: isTablet && !isMobile ? "-17em" : isMobile ? "-7em" : "calc(.1vw - -22%)",
                            width: "100%",
                            height: isMobile ? "150px" : "274px",
                            cursor: "pointer",
                        }}
                    />

                    {/* SISTEMAS DE GESTIÓN */}
                    <Box
                        className="image-container"
                        onClick={() => navigateTo("/landing/tienda/archivos")}
                        sx={{
                            backgroundImage: "url('/integrations/barGestion1.png')",
                            backgroundSize: "cover",
                            backgroundRepeat: "no-repeat",
                            backgroundPosition: isTablet && !isMobile ? "-17em" : isMobile ? "-10em" : "calc(.1vw - -22%)",
                            width: "100%",
                            height: isMobile ? "150px" : "274px",
                            marginTop: isMobile ? "0" : "-6em",
                            position: "relative",
                            zIndex: "30",
                            cursor: "pointer",
                        }}
                    />

                    {/* TIENDAS ONLINE */}
                    <Box
                        className="image-container"
                        onClick={() => navigateTo("/landing/tienda/archivos")}
                        sx={{
                            backgroundImage: "url('/integrations/barTienda1.png')",
                            backgroundSize: "cover",
                            backgroundRepeat: "no-repeat",
                            backgroundPosition: isTablet && !isMobile ? "-17em" : isMobile ? "-10em" : "calc(.1vw - -20%)",
                            width: "100%",
                            height: isMobile ? "180px" : "320px",
                            marginTop: isMobile ? "0" : "-6em",
                            position: "relative",
                            zIndex: "50",
                            cursor: "pointer",
                        }}
                    />
                </Box>
            </Box>

            {/* cards de los planes */}
            <ModalidadesIntegracion />
        </Box>
    );
};
