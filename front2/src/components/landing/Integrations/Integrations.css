/* Animación fluida y ligera para hover */
.image-container {
    transition: transform 0.4s ease, opacity 0.4s ease;
}

.image-container:hover {
    transform: scale(1.05);  /* Efecto de zoom suave */
    opacity: 0.9;            /* Ligero oscurecimiento */
}

/* Animación de aparición */
.image-container {
    animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
