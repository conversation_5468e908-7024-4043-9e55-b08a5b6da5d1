
import React from "react";
import { Outlet } from "react-router-dom";
import { AppBar, Box, Toolbar, useMediaQuery } from "@mui/material";
import { useTheme } from '@emotion/react';
import NavbarLanding from './components/NavbarLanding';

export const LandingLayout = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <>
      <AppBar
        position="fixed"
        sx={{
          color: theme.palette.text.pinkMain,
          boxShadow: "none",
          paddingLeft: "0px",
          zIndex: "1000 !important",
          backgroundColor: theme.palette.colorGreen.main,
          backgroundImage: "none",
        }}
      >
        <Toolbar sx={{ height: "88.22px" }}>
          <NavbarLanding />
        </Toolbar>
      </AppBar>

      <Box sx={{ marginTop: "88.22px" }}> 
        <Outlet />
      </Box>
    </>
  );
};
