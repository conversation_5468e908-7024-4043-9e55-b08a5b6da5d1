import React from 'react';
import { motion } from "framer-motion";

export const ImgAnimation = ({ src = 'starQuestions.png', alt, style, animationType }) => {

    // Lista de animaciones disponibles
    const animations = [
        { initial: { y: 0 }, animate: { y: [0, -10, 0] }, transition: { duration: 2, repeat: Infinity, ease: "easeInOut" } },  // Rebote vertical
        { initial: { rotate: 0 }, animate: { rotate: [0, 15, -15, 0] }, transition: { duration: 2, repeat: Infinity, ease: "easeInOut" } },  // Oscilación
        { initial: { scale: 1 }, animate: { scale: [1, 1.1, 1] }, transition: { duration: 1.5, repeat: Infinity, ease: "easeInOut" } },  // Zoom suave
        { initial: { opacity: 1 }, animate: { opacity: [1, 0.5, 1] }, transition: { duration: 1.5, repeat: Infinity, ease: "easeInOut" } },  // Parpadeo
        { initial: { x: 0 }, animate: { x: [0, 10, -10, 0] }, transition: { duration: 2, repeat: Infinity, ease: "easeInOut" } }  // Oscilación horizontal
    ];

    // Si no se proporciona `animationType`, selecciona una aleatoria
    const animationIndex = animationType !== undefined 
        ? animationType % animations.length 
        : Math.floor(Math.random() * animations.length);

    const selectedAnimation = animations[animationIndex];

    return (
        <motion.img
            src={src}
            alt={alt}
            style={{ maxWidth: "100%", height: "auto", width: "35px", ...style }}
            {...selectedAnimation}
        />
    );
};
