import {
  Typography,
  Switch,
  useMediaQuery,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Box from '@mui/material/Box';
import { useTheme, ThemeProvider, createTheme, styled } from '@mui/material/styles';
import { useSearchParams } from "react-router-dom";
import logo from '../../../../src/components/img/mercaleader/Mercaleader_Logo_Principal.png';
import { ListItems } from "./ListItems";


const NavbarLanding = ({ openNav }) => {
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const cerrarSesion = () => {
    dispatch(cerrarSesionAccion())
  }
  const name = useSelector(store => store.usuario.name);
  const URLphoto = useSelector(store => store.usuario.URLphoto);

  let isMobile = useMediaQuery((theme) => theme.breakpoints.down("sm"));



  // const isMobile = useMediaQuery((theme) => theme.breakpoints.down("sm"));

  const theme = useTheme();




  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
      }}>
      <ListItems theme={theme} isMobile={isMobile} name={name} URLphoto={URLphoto} logo={logo} />


    </Box>
  );
}

export default NavbarLanding;
