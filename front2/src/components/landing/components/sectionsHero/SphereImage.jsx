import { useState, useEffect } from "react";
import { <PERSON>, Typography, Button, ListItemButton, ListItemText } from "@mui/material";
import { ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import { motion, AnimatePresence } from "framer-motion";
import { useTheme } from "@emotion/react";
import sphere from "../../../../components/img/mercaleader/sphere.png";


export const SphereImage = () => {
 

    const theme = useTheme();

    return (
        <Box sx={{
            textAlign: "center", p: 5, 
            bgcolor: theme.palette.colorGreen.main,

            height: '91vh',
            padding: "0 calc(50vw - 45%)",
            width: "100%",

        }}>

            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-evenly",
                    alignItems: "center",
                    gap: 3,
                    width: "100%",
                    backgroundColor: theme.palette.colorGreen.main,
                    margin: "auto",
                    height: "100%",
                }}
            >


                <Box sx={{ display: "flex", justifyContent: "center", gap: "1rem", pt: 2 }}>
                    <img src={sphere} alt="sphere" style={{ width: "100%", height: "auto",
                        maxWidth: "900px", 
                     }} />
                </Box>
               
                  
                        <Button
                            primary="+ Integraciones"
                            sx={{
                                backgroundColor: theme.palette.text.pinkMain,
                                border: `1px solid ${theme.palette.text.pinkMain}`,
                                padding: "8px 16px",
                                borderRadius: "20px",
                                transition: "background-color 0.3s ease, color 0.3s ease, border 0.3s ease",
                                fontSize: "14px",
                                fontFamily: "Noto Sans",
                                fontWeight: "500",
                                color: theme.palette.colorGreen.main,
                                fontWeight: "bold",
                                ' & .MuiButtonBase-root .MuiListItemButton-root': {
                                    flexGlow: "0 !important",
                                },
                                "&:hover": {
                                    color: theme.palette.text.pinkMain, 
                                    backgroundColor: "transparent",
                                    border: `1px solid ${theme.palette.text.pinkMain}`,
                                },
                            }}
                        >
                            Agenda una llamada al Olimpo
                        </Button>
            </Box>
        </Box>

    );
}
