import { useState, useEffect } from "react";
import { Box, Typography, Button, ListItemButton, ListItemText, TextField } from "@mui/material";
import { ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import { motion, AnimatePresence } from "framer-motion";
import { useTheme } from "@emotion/react";
import oraculo from "../../../../components/img/mercaleader/oraculo.png";
import { ImgAnimation } from "../utilities/StarAnimation";


export const Oraculum = () => {


    const theme = useTheme();

    return (
        <Box sx={{
            textAlign: "center", p: 5,
            bgcolor: theme.palette.primary.backgroundPink,

            height: '90vh',
            padding: "0 calc(50vw - 45%)",
            width: "100%",

        }}>

            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-evenly",
                    alignItems: "center",
                    gap: 3,
                    width: "100%",
                    backgroundColor: theme.palette.primary.backgroundPink,
                    margin: "auto",
                    height: "100%",
                }}
            >



                <Box sx={{
                    display: "flex", justifyContent: "center", gap: "1rem", pt: 2, color: "#263a2a",
                    flexDirection: "column",
                }}>
                    <ImgAnimation
                        src={oraculo}
                        alt="sphere"
                        style={{
                            width: "150px", height: "auto", margin: "auto",
                            paddingRight: "10px`",
                        }}
                    />

                    {/* <img src={oraculo} alt="sphere" style={{
                        width: "150px", height: "auto", margin: "auto",
                        paddingRight: "10px`",
                    }} /> */}

                    <Typography variant="h6" fontWeight="bold" color="#263a2a" sx={{
                        fontSize: "calc(1.8rem + 1vw)",
                        // fontWeight: "
                        lineHeight: "1.2",
                        letterSpacing: "0.04em",
                    }}>
                        El Oráculo
                    </Typography>

                    <Typography variant="body2"
                        sx={{
                            fontSize: "calc(.7rem + 1vw)",
                            paddingTop: ".5rem",
                            fontWeight: "200",
                        }}>
                        Suscríbete para recibir estrategias                         <br />
                        y sabiduría de los Dioses que <br />
                        transformarán tus marketplaces
                    </Typography>

                </Box>
                <Box sx={{
                    display: "flex", justifyContent: "center", gap: "1.5rem", pt: 2, color: "#263a2a",
                    flexDirection: "column",
                }}>
                    <TextField
                        placeholder="Tu correo"
                        autoComplete="email"
                        width="400px"
                        sx={{
                            height: "50px",
                            width: "400px",
                            maxWidth: "400px",
                            color: "orange",
                            borderColor: "orange",
                            "& .MuiOutlinedInput-root": {
                                borderRadius: "50px", // Bordes redondeados
                                border: `2px solid ${theme.palette.secondary.main}`, // Borde de 2px
                                color: theme.palette.secondary.main, // Color del texto
                                height: "50px", // Alto de 50px
                            },
                            "& .MuiOutlinedInput-input": {
                                textAlign: "center", // Centrar el texto
                                color: theme.palette.secondary.main, // Color del texto
                                border: "none", // Sin borde
                            },
                        }}
                    />
                    <Button
                        primary="+ Integraciones"
                        sx={{
                            backgroundColor: theme.palette.colorGreen.main,
                            border: `1px solid ${theme.palette.colorGreen.main}`,
                            padding: "8px 16px",
                            width: "400px",
                            maxWidth: "400px",
                            borderRadius: "20px",
                            transition: "background-color 0.3s ease, color 0.3s ease, border 0.3s ease",
                            fontSize: "14px",
                            fontFamily: "Noto Sans",
                            fontWeight: "500",
                            color: theme.palette.text.pinkMain,
                            fontWeight: "bold",
                            height: "50px",
                            ' & .MuiButtonBase-root .MuiListItemButton-root': {
                                flexGlow: "0 !important",
                            },
                            "&:hover": {
                                color: theme.palette.colorGreen.main,
                                backgroundColor: "transparent",
                                border: `1px solid ${theme.palette.colorGreen.main}`,
                            },
                        }}
                    >
                        Suscríbete
                    </Button>
                </Box>

            </Box>
        </Box>

    );
}
