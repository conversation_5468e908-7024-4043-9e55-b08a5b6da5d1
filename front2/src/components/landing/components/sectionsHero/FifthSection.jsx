
import { Box, Typography } from "@mui/material";
import ZeusMalabar from "../../../../components/img/mercaleader/ZeusMalabar.png";
import { useTheme } from "@emotion/react";


export const FifthSection = ({ imageSrc }) => {

  const theme = useTheme();
  return (
    <Box
      sx={{
        alignItems: "center",
        height: "90vh",
        width: "100vw",
        padding: "0 calc(50vw - 45%)",
        bgcolor: theme.palette.primary.backgroundPink,
        position: "relative",
      }}
    >
      {/* Imagen a la izquierda */}
      <Box
        sx={{
          flex: 1,
          display: "flex",
          justifyContent: "center",
        }}
      >
        <img
          // src={ZeusMalabar}
          src="zeusMalabares.png"
          alt="Ahorrar tiempo y esfuerzo"
          style={{
            width: "61%",
            height: "80vh",
            position: "absolute",
            bottom: "-10.5em",
            maxWidth: "1800px",
            zIndex: 1,
          }}
        />
      </Box>

      {/* Contenido de texto a la derecha */}
      <Box
        sx={{
          textAlign: "left",
          maxWidth: "800px",
          position: "absolute",
          left: "74%",
          transform: "translateX(-36%)",
          color: "#263a2a",
          top: "1em",
          width: { xs: "80%", sm: "400px", md: "450px" }, // Ajusta según el tamaño de la pantalla
          maxWidth: "500px", // Máximo permitido
          minWidth: "300px", // Mínimo para evitar que se encoja demasiado
          fontFamily: "Filson Pro !important",
        }}
      >
        <Typography variant="h4" fontWeight="bold" color="green" mb={2}>
          ¡Todo en<br /> tiempo real!
        </Typography>
        <Typography
          variant="body1"
          color="green"
          sx={{
            fontSize: "calc(.7rem + 1vw)",
            lineHeight: "1.3",
            letterSpacing: "0.04em",
            fontSize: "calc(.7rem + 1vw)",
            // width: "250px",
            paddingTop: "1rem",
            fontWeight: "200",
          }}
        >
          Integramos tu CRM / ERP a nuestra <br />
          plataforma, contamos con APIs <br />
          compartibles con el 99% de <br />
          plataformas de gestion
        </Typography>
      </Box>

    </Box>
  );
};
