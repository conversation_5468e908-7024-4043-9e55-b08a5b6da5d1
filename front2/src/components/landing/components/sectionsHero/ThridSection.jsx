
import { Box, Typography } from "@mui/material";
import ZeusCargandoCaja from "../../../../components/img/mercaleader/ZeusCargandoCaja.png";
import { useTheme } from "@emotion/react";


export const ThirdSection = () => {
    const theme = useTheme();
    return (
        <Box
            sx={{
                bgcolor: theme.palette.primary.backgroundPink,
                width: "100%",
                minHeight: "100vh",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                padding: "0 calc(50vw - 45%)",
                position: "relative",
            }}
        >
            {/* Texto superior */}
            <Typography variant="h3" fontWeight="bold" color="green" textAlign="center" mb={3}
                sx={{
                    color: theme.palette.colorGreen.main,
                    padding: "3rem 0 1rem 0",
                    fontSize: "calc(1rem + 2vw)",
                }}>
                A continuación te contaremos <br /> las hazañas de este santo Dios:
            </Typography>

            {/* Imagen central */}
            <Box sx={{ width: "84%", maxWidth: "900px", display: "flex", justifyContent: "center" }}>
                <img src={ZeusCargandoCaja} alt="Zeus Hazañas" style={{ width: "75%", height: "auto" }} />
            </Box>

            {/* Texto flotante */}
            <Box sx={{
                mt: "200px", textAlign: "left", maxWidth: "800px",
                position: "absolute",
                left: "calc(50vw - 45%)",
                color: "#263a2a"
            }}>
                <Typography variant="h6" fontWeight="bold"
                    sx={{
                        fontSize: "calc(1.8rem + 1vw)",
                        // fontWeight: "
                        lineHeight: "1.2",
                        letterSpacing: "0.04em",
                    }}>
                    ¡Olvídate de las <br />
                    malas calificaciones <br />
                    por cancelaciones!
                </Typography>
                <Typography variant="body2" sx={{
                    fontSize: "calc(.7rem + 1vw)",
                    // width: "250px",
                    paddingTop: "1rem",
                    fontWeight: "200",
                }}>
                    Gestiona los inventarios de todos tus <br />
                    marketplaces y
                    tienda oficial en un <br />solo punto,
                    sincronizados en tiempo<br /> real.
                </Typography>
            </Box>

             {/* Texto flotante */}
             <Box sx={{
                 textAlign: "left", maxWidth: "800px",
                position: "absolute",
                right: "calc(50vw - 45%)",
                color: "#263a2a",
                top: "63%",
                fontFamily: "Filson Pro !important",
            }}>
                <Typography variant="h6" fontWeight="bold"
                    sx={{
                        fontSize: "calc(1.8rem + 1vw)",
                        // fontWeight: "
                        lineHeight: "1.2",
                        letterSpacing: "0.04em",
                    }}>
                    ¡No pierdas ventas <br />
                    ni utilidad! <br />
                </Typography>
                <Typography variant="body2" sx={{
                    fontSize: "calc(.7rem + 1vw)",
                    // width: "250px",
                    paddingTop: "1rem",
                    fontWeight: "200",
                }}>
                    Gestiona los inventarios de todos tus <br />
                    marketplaces y
                    tienda oficial en un <br />solo punto,
                    sincronizados en tiempo<br /> real.
                </Typography>
            </Box>

        </Box>
    )
}