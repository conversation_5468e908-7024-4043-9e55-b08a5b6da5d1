import { useState, useEffect } from "react";
import { Box, Typography, Button, ListItemButton, ListItemText } from "@mui/material";
import { ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import { motion, AnimatePresence } from "framer-motion";
import { useTheme } from "@emotion/react";
import logoAmazonVerde from "../../../../components/img/mercaleader/LogosIntegraciones/logo-amazon-verde.png";
import logoClaro from "../../../../components/img/mercaleader/LogosIntegraciones/logo-claro-verde.png";
import logoMercado from "../../../../components/img/mercaleader/LogosIntegraciones/logo-mercadolibre-verde.png";
import logoWalmart from "../../../../components/img/mercaleader/LogosIntegraciones/logo-walmart-verde.png";

const integrations = [
    [
        { name: "<PERSON>lar<PERSON>", src: logoClaro },
        { name: "Amazon", src: logoAmazonVerde },
        { name: "Mercado Libre", src: logoMercado },
        { name: "Walmart", src: logoWalmart },
    ],
    // [
    //     { name: "eBay", src: logoClaro },
    //     { name: "Shopify", src: logoAmazonVerde },
    //     { name: "Facebook", src: logoMercado },
    //     { name: "Google", src: logoWalmart },
    // ],
    // [
    //     { name: "Etsy", src: "/images/etsy.png" },
    //     { name: "AliExpress", src: "/images/aliexpress.png" },
    //     { name: "TikTok", src: "/images/tiktok.png" },
    //     { name: "Instagram", src: "/images/instagram.png" },
    // ],
];

export const IntegrationsCarousel = () => {
    const [index, setIndex] = useState(0);
    const [timer, setTimer] = useState(null);

    // Cambia de slide automáticamente cada 5 segundos
    useEffect(() => {
        if (timer) clearInterval(timer);

        const newTimer = setInterval(() => {
            nextSlide();
        }, 5000); // Cambia de slide cada 5 segundos

        setTimer(newTimer);
        return () => clearInterval(newTimer);
    }, [index]);

    const nextSlide = () => {
        setIndex((prevIndex) => (prevIndex + 1) % integrations.length);
    };

    const prevSlide = () => {
        setIndex((prevIndex) => (prevIndex - 1 + integrations.length) % integrations.length);
    };

    const theme = useTheme();

    return (
        <section
            // id= "integraciones"
                >
                <Box sx={{
                    textAlign: "center", p: 5, bgcolor: "white",
                    height: "90vh",
                    padding: "0 calc(50vw - 45%)",
                    width: "100%",

                }}>

                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "space-evenly",
                            alignItems: "center",
                            gap: 3,
                            width: "80%",
                            backgroundColor: "white",
                            margin: "auto",
                            height: "100%",
                            margin: "auto",
                        }}
                    >

                        <Typography variant="h4" fontWeight="bold" color={theme.palette.colorGreen.main} sx={{ mb: 3 }}>
                            Integraciones
                        </Typography>

                        {/* Carrusel de logos */}
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                mt: 3,
                                gap: 2,
                                width: "100%",
                            }}
                        >
                            {/* Flecha izquierda */}
                            {/* <ArrowBackIos sx={{ cursor: "pointer" }} onClick={prevSlide} /> */}

                            {/* Contenedor del carrusel */}
                            <Box
                                sx={{
                                    width: "100%", // Ocupa el 80% de la pantalla
                                    overflow: "hidden",
                                    position: "relative",
                                    display: "flex",
                                    justifyContent: "center",
                                }}
                            >
                                <AnimatePresence mode="wait">
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, x: 100 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        exit={{ opacity: 0, x: -100 }}
                                        transition={{ duration: 0.5 }}
                                        style={{
                                            display: "flex",
                                            justifyContent: "space-between", // Espacia bien las imágenes
                                            width: "100%",
                                            flexWrap: "wrap", // Asegura que en pantallas pequeñas no se desborde
                                            alignItems: "center",
                                        }}
                                    >
                                        {integrations[index].map((item) => (
                                            <motion.img
                                                key={item.name}
                                                src={item.src}
                                                alt={item.name}
                                                style={{
                                                    width: "140px",
                                                    // height: "71px",
                                                    // maxWidth: "120px", // Evita que sean demasiado grandes
                                                    height: "auto",
                                                }}
                                                whileHover={{ scale: .7, rotate: 5 }}
                                            />
                                        ))}
                                    </motion.div>
                                </AnimatePresence>
                            </Box>

                            {/* Flecha derecha */}
                            {/* <ArrowForwardIos sx={{ cursor: "pointer" }} onClick={nextSlide} /> */}
                        </Box>


                        {/* Indicadores */}
                        <Box sx={{ display: "flex", justifyContent: "center", mt: 2, gap: 1 }}>
                            {integrations.map((_, i) => (
                                <Box
                                    key={i}
                                    sx={{
                                        width: 10,
                                        height: 10,
                                        borderRadius: "50%",
                                        backgroundColor: index === i ? "green" : "#ccc",
                                        cursor: "pointer",
                                    }}
                                    onClick={() => setIndex(i)}
                                />
                            ))}
                        </Box>

                        {/* Botón de más integraciones */}


                        {/* <Button
                            primary="+ Integraciones"
                            sx={{
                                backgroundColor: theme.palette.secondary.main,
                                border: `1px solid ${theme.palette.secondary.main}`,
                                padding: "8px 16px",
                                borderRadius: "20px",
                                transition: "background-color 0.3s ease, color 0.3s ease, border 0.3s ease",
                                fontSize: "14px",
                                fontFamily: "Noto Sans",
                                fontWeight: "500",
                                color: theme.palette.primary.backgroundPink,
                                ' & .MuiButtonBase-root .MuiListItemButton-root': {
                                    flexGlow: "0 !important",
                                },
                                "&:hover": {
                                    color: theme.palette.secondary.main, // Asegura que el texto sea visible sobre el nuevo fondo
                                    backgroundColor: "transparent",
                                    border: `1px solid ${theme.palette.secondary.main}`,
                                },
                            }}
                        >
                            + Integraciones
                        </Button> */}
                    </Box>
                </Box>

        </section >

    );
}
