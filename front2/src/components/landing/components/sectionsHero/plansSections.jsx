import { useState, useEffect } from "react";
import { Box, Typography } from "@mui/material";
import { motion, AnimatePresence } from "framer-motion";
import { useTheme } from "@emotion/react";
import { CardPlan } from "./CardPlan";

const testimonios = [
    {
        title: "INICIADO",
        ventas: "Hasta 10 ventas",
        canales: "4 canales de ventas",
        sku: "Hasta 100 SKU",
        usuarios: "1 usuario",
        color: '#41644a',
        bgColor: 'text.pinkMain',
        icon: "handOne.png",
    },
    {
        title: "ILUMINADO",
        ventas: "Hasta 50 ventas",
        canales: "4 canales de ventas",
        sku: "Hasta 150 SKU",
        usuarios: "3 usuarios",
        soporte: "Soporte básico",
        color: "#FFCDEA",
        bgColor: 'secondary.main',
        icon: "handTwo.png",
    },
    {
        title: "SEMI DIOS",
        ventas: "Hasta 1500 ventas",
        canales: "4 canales de ventas",
        sku: "Hasta 15000 SKU",
        usuarios: "5 usuarios",
        conexionERP: "Conexión con ERP (no incluye costo de implementación)",
        conexionAPI: "Conexión API",
        //   color: "#304E39",
        color: 'white',
        bgColor: 'colorGreen.main',
        icon: "handTrhee.png",
    },
    {
        title: "DIOS DEL OLIMPO",
        ventas: "Ventas ilimitadas",
        canales: "4 canales de ventas",
        sku: "SKU's ilimitados",
        usuarios: "Usuarios ilimitados",
        conexionERP: "Conexión con ERP",
        conexionAPI: "Conexión API",
        soporte: "Soporte especializado",
        color: "#263a2a",
        bgColor: 'colorGreen.main',
        icon: "handFour.png",
    },
];

export const PlansSections = () => {
    const theme = useTheme();
    const [index, setIndex] = useState(0);

    // Cambio automático de testimonios cada 5 segundos
    useEffect(() => {
        const interval = setInterval(() => {
            setIndex((prevIndex) => (prevIndex + 1) % testimonios.length);
        }, 5000);

        return () => clearInterval(interval);
    }, []);

    return (
        <Box
            sx={{
                bgcolor: theme.palette.primary.backgroundPink,
                width: "100%",
                height: "100%",
                minHeight: "100vh",
                padding: "2em calc(50vw - 45%)",
                position: "relative",
            }}
        >
            <Typography variant="h2" fontWeight="bold" color="secondary" textAlign="center" mb={3}
                sx={{
                    color: theme.palette.secondary.main,
                    padding: "3rem 0 1rem 0",
                    fontSize: "calc(1rem + 2vw)",
                }}>
                ¡Escoge tu divino plan!
            </Typography>
            <Box
                sx={{
                    display: "grid",
                    gap: "1rem",
                    margin: "auto",
                    justifyContent: "center",
                    pt: 2,
                   '@media (max-width: 600px)': {
                        gridTemplateColumns: "1fr", // 1 columna a partir de 800px
                    },
                    "@media (min-width: 600px)": {
                        gridTemplateColumns: "1fr 1fr"  // 2 columnas a partir de 800px
                    },
                    "@media (min-width: 1100px)": {
                        gridTemplateColumns: "1fr 1fr 1fr 1fr"  // 2 columnas a partir de 800px
                    }
                }}
            >
                {testimonios.map((plan, index) => (
                    <CardPlan key={index} plan={plan} />
                ))}
            </Box>



        </Box>
    );
};