import { Box, Typography, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import ZeusPreocupado from "../../../../components/img/mercaleader/ZeusPreocupado3.png";


export const FirstSection = () => {
    const theme = useTheme();

    const isMobile = useMediaQuery(`(max-width: 1100px)`);

    // Detecta diferentes tamaños de pantalla
    const isSmall = useMediaQuery(`(max-width: 600px)`);
    const isMedium = useMediaQuery(`(max-width: 1100px)`);
    const isLarge = useMediaQuery(`(max-width: 1400px)`);

    // Tamaño dinámico que crece a medida que la pantalla se hace más pequeña
    const backgroundSize = isSmall
        ? "170vw"
        : isMedium
            ? "140vw" :
            isLarge
                ? "120vw"
                : "100vw";

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "space-evenly",
                height: "100%",
                width: "100%",
                backgroundColor: theme.palette.colorGreen.main,
                position: "relative",
                overflow: "hidden",
            }}>
                
            {/* Imagen que debe estar por encima de la sección verde */}
            <Box
                sx={{
                    position: "absolute",
                    top: "0",
                    left: "0",
                    width: "100%",
                    zIndex: 2, // Asegúrate de que este valor sea mayor que el de la sección verde
                }}
            >

            </Box>

            {/* texto e imagen */}
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-evenly",
                    height: "91vh",
                    width: "100vw",
                    padding: "0 calc(50vw - 45%)",
                    backgroundColor: theme.palette.colorGreen.main,
                    position: "relative",
                    overflow: "hidden",
                    zIndex: 1, // Asegúrate de que este valor sea menor que el de la imagen
                }}
            >
                {/* Texto */}
                <Box sx={{ position: "relative", maxWidth: "40%" }}>
                    <Typography
                        variant="h3"
                        sx={{
                            fontWeight: "bold",
                            color: theme.palette.text.pinkMain,
                            fontSize: "4rem",
                            position: "relative",
                            zIndex: 2,
                        }}
                    >
                        Tienes a Zeus <span>preocupado</span>
                    </Typography>
                </Box>

                {/* Imagen de Zeus */}
                <Box
                    sx={{
                        width: "50%",
                        height: "auto",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <img
                        src={ZeusPreocupado}
                        alt="Zeus preocupado"
                        style={{ width: "100%", height: "auto", maxWidth: "600px" }}
                    />
                </Box>
            </Box>

            {/* Espiral de Zeus como background-image */}
            <Box
                sx={{
                    position: "relative",
                    height: 'calc(100vh + 1vh)',
                    width: "100%",
                    backgroundColor: "white",
                }}>
                <Box
                    sx={{
                        height: 'calc(100% + 30vh)',
                        width: "100%",
                        maxWidth: "1800px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        position: "absolute",
                        backgroundImage: `url("espiralZeus1.png")`,
                        // backgroundSize: "cover", // Cambia a "cover" para que cubra todo el espacio disponible
                        backgroundSize: backgroundSize,
                        backgroundPosition: "center",
                        backgroundRepeat: "no-repeat",
                        top: "-36%",
                        left: "0",
                        zIndex: 100,
                    }}
                >
                </Box>
            </Box>

        </Box>
    )
}