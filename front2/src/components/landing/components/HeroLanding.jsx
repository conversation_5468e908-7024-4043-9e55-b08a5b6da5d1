import { Box, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import ZeusPreocupado from "../../../components/img/mercaleader/ZeusPastel.png";
import { FirstSection } from "./sectionsHero/FirstSection";
import { SecondSection } from "./sectionsHero/SecondSection";
import { ThirdSection } from "./sectionsHero/ThridSection";
import { QuarterSection } from "./sectionsHero/QuarterSection";
import { FifthSection } from "./sectionsHero/FifthSection";
import { SixthSection } from "./sectionsHero/SixthSection";
import { PlansSections } from "./sectionsHero/plansSections";
import { IntegrationsCarousel } from "./sectionsHero/IntegrationsCarousel";
import { SphereImage } from "./sectionsHero/SphereImage";
import { Oraculum } from "./sectionsHero/Oraculum";
import { QuestionsAnswers } from "./sectionsHero/Questions&Answers";
import { Footer } from "./Footer";
import { CommentsSection } from "./sectionsHero/CommentsSection";
import { Banner } from "./sectionsHero/ImgBannerComments";

export const HeroLanding = () => {
    const theme = useTheme();

    return (
        <Box
          sx={{
            fontFamily: "Filson Pro !important",
            '& h1, h2, h3, h4, h5, h6': {
                fontFamily: 'Filson Pro !important',
                },
            // '& p': {
            //     fontFamily: 'Filson Pro !important',
            // },
          }}
        >
            <FirstSection />

            <SecondSection />

           <ThirdSection />

            <QuarterSection />

             <FifthSection />

            <SixthSection />
            {/* <Banner /> */}

            <CommentsSection />

            {/* <section
            id= "planes"
                > */}
             <PlansSections />
            {/* </section> */}

             {/* <section
            id= "integraciones"
                > */}
            <IntegrationsCarousel />
            {/* </section> */}

            <SphereImage />

            <Oraculum />

           <QuestionsAnswers />

            <Footer />
            
        </Box>
    );
};
