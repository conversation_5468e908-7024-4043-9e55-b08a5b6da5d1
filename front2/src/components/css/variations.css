.containerVariation {
  margin-top: 1em;
  display: flex;
  justify-content: space-between;
  /* box-shadow: 2px 2px 2px 0px gray; */

  align-items: center;
  padding: 0.5em 1em;
  border-radius: 10px 10px 0 0;
  /* background: #ebebeb; */
}
.containerVariationClose {
  margin-top: 1em;
  /* box-shadow: 2px 2px 2px 0px gray; */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5em 1em;
  border-radius: 10px 10px 10px 10px;
  /* background: #ebebeb; */
}

.barraVariacion {
  width: 100%;
  display: flex;
  justify-content: space-between;
  /* background-color: #ebebeb; */
  /* padding: 0.5em 1em; */
  /* margin: 1em auto 0 auto; */
  border-radius: 10px 10px 0 0;
  cursor: pointer;
}
.barraVariacionClose {
  width: 100%;
  display: flex;
  justify-content: space-between;
  /* background-color: #ebebeb; */

  /* padding: 0.5em 1em; */
  /* margin: 1em auto 0 auto; */
  border-radius: 10px 10px;
  cursor: pointer;
}

.barraVariacionInfoBasic {
  display: flex;
  justify-content: space-between;
  box-shadow: 2px 2px 2px 0px gray;
  /* box-shadow: 2px 2px 2px 0px gray; */
  /* background-color: #ebebeb; */
  padding: 0.5em 1em;
  margin: 1em auto 0 auto;
  border-radius: 10px 10px 0 0;
  cursor: pointer;
}

.barraVariacionCloseInfoBasic {
  display: flex;
  justify-content: space-between;
  box-shadow: 2px 2px 2px 0px gray;
  /* background-color: #ebebeb; */
  padding: 0.5em 1em;
  margin: 1em auto 0 auto;
  border-radius: 10px 10px;
  cursor: pointer;
}

.containerDeleteIconArrow {
  display: flex;
  justify-content: space-between;
}

.containerDeleteIconArrow > div {
  margin: auto 2em auto 0;
}

/* DELETEiCON */
.divIconDelete {
  /* background-color: #ebebeb; */
}

.containerPrincipal {
  /* box-shadow: 2px 2px 2px 0px gray; */
  width: 100%;
  margin: auto;
  /* background-color: #ebebeb; */
  border-radius: 0 0 10px 10px;
}

.containerInputs {
  width: 100%;
  margin: auto;
  padding-bottom: 1em;
}

/* select */
.selectVariations {
  display: flex;
  align-items: center;
}

.selectVariations p {
  font-weight: 500;
  margin: 0;
}

.iconDelete {
  font-size: small;
  color: red;
}
