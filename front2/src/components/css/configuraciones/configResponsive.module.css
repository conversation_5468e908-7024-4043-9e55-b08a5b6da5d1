.mobileNavbar {
  display: block;
  width: 30% !important;
  z-index: 100 !important;
  padding: 0 2em;
  min-width: 350px;
}

.mobileContainer {
  flex-direction: row;
  height: 90vh;
}

.listItemButton {
  width: 80%;
}

@media (max-width: 900px) {
  .hiddenBtn {
    display: block !important;
    padding-top: 1em;
    position: relative;
    button {
      position: fixed;
      background-color: white;
      left: 1em;
      z-index: 100;

    } 
  }

  .mobileNavbar {
    display: none !important;
  }

  .list {
    width: 100%;
    max-width: 300px !important;
    position: relative !important;
    padding: 0 1em !important;
  }

  .listItemButton {
    width: 100%;
  }

  .firstSteps {
    width: 90% !important;
    margin: auto !important;
    left: 0 !important;
  }

  .boxMarketplace {
    display: grid !important;
    /* grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); */
    grid-template-columns: repeat(2, minmax(100px, 1fr));

    grid-gap: 10px;
    justify-content: center;
    /* Centrar horizontalmente */
    align-content: center;
    /* Espacio entre las columnas */
  }

  .cardConteiner {
    width: fit-content !important;
  }

  .cardShadow {
    width: min-content;
    margin: auto;
    /* box-shadow: none !important; */
  }

  .cardContentCongratulations {
    width: 100% !important;
  }

  .imgMarketplace {
    display: none !important;
  }

  .titleMarketplace {
    /* font-size: 1.5rem !important; */
    width: 100% !important;
  }

  /* inputs */

  /* .formBox {
   .contentInputs {
      width: 100% !important;
      .sizeText{
        width: 20% !important;
      }
      .inputBox{
        width: 80% !important;
        input{
          width: 100% !important;
        }
      }
    }
  } */
  .formBox{
    .contentInputs {
       width: 100% !important;
       .sizeText{
         width: auto !important;
       }
       .inputBox{
         width: 100% !important;
         .secretKey{
           width: 100% !important;
         }
         input{
           width: 100% !important;
         }
       }
     }
     div{
       /* width: 95% !important; */
       justify-content: space-between !important;
       flex-direction: column !important;
       align-items: baseline !important;
     }
 
     .inputBox{
       width: 100% !important;
       input{
         width: 100% !important;
       }
     }
     .sizeText{
       width: auto !important;
     }
 
     .responsiveButton{
       align-items: normal !important;
       Button{
         margin-left: 0 !important;
       }
     }
 
   }
  .containerSetMarketplace {
    width: 95% !important;
  }
  .cardMarketplace{
    padding: 0 !important;
  }

  /* .mobileContainer {
    height: auto;
  }
   */
}

@media (min-width: 900px) {
  .list {
    position: fixed !important;
  }
  .formBox{
    width: 100% !important;
    padding: 0 !important;
   div{
      flex-direction: column !important;
      width: 100% !important;
      align-items: flex-start;
   }
  }
  .responsiveButton{
    display: block !important;
    text-align: end;
    align-items: normal !important;
  }
}



@media (min-width: 1020px) {
  .formBox{
   
    div{
      /* width: 95% !important; */
      justify-content: space-between !important;
      flex-direction: column !important;
      input{
        width: 100% !important;
      
      }
    }
  }
  

}
.boxCardContent{
  padding: 1rem 10% ;
}

.firstSteps {
  padding: 4rem;
  width: 60%;
  margin: auto;
  /* background: #d9d9d9; */
  border-radius: 10px;
  position: relative;
}

.hiddenBtn {
  display: none;
}

.list {
  max-width: 360px;
  min-width: 300px;
  /* border-right: 1px solid #d9d9d9; */
  height: calc(100vh - 150px);
  overflow-y: auto;
  position: fixed;
  overflow-y: auto;
  scrollbar-width: thin;
  /* Para navegadores compatibles con la especificación de barra de desplazamiento del sistema */
  scrollbar-color: #aaa #fff;

}

/* para status interno */

.containerStatusInterno {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
}

@media (max-width: 1180px) {
  .containerStatusInterno {
    flex-direction: column;
    align-items: center;
  }
  .responsiveButton{
    display: block;
    text-align: end;
  }
}

@media (max-width: 515px) {
  .cardStatusInterno {
    width: auto !important;
    min-width: auto !important;
    /* flex-wrap: wrap !important; */
  }

  .cardStatusInternoName {
    flex-wrap: wrap !important;
  }

  .cardMarketplace {
    padding: 2em .8rem !important;
  }
  .formBox{
   .contentInputs {
      width: 100% !important;
      .sizeText{
        width: auto !important;
      }
      .inputBox{
        width: 100% !important;
        .secretKey{
          width: 100% !important;
        }
        input{
          width: 100% !important;
        }
      }
    }
    div{
      /* width: 95% !important; */
      justify-content: space-between !important;
      flex-direction: column !important;
      align-items: baseline !important;
    }

    .inputBox{
      width: 100% !important;
      input{
        width: 100% !important;
      }
    }
    .sizeText{
      width: auto !important;
    }

    .responsiveButton{
      align-items: normal !important;
    }

  }
  
  .boxCardContent{
    padding: 1rem 4% !important ;
  }

}

.cardMarketplace {
  padding: 2rem;
  width: 100%;
  /* height: 27.5rem; */
  margin: 2em auto 0 auto;
  /* background: #D9D9D9; */
  border-radius: 10px;
  position: relative;
}

.cardStatusInternoName {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  padding: 1em;
}

.cardStatusInterno {
  /* background: #d9d9d9; */
  width: auto;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1em;
  border-radius: 10px;
  /* box-shadow: 1px 1px 4px 0px #8e8e8e; */
}

@media (max-width: 450px) {
  .titleHeader {
    padding: 1rem !important;
  }
}

.titleHeader {
  padding: 0rem 4rem 1rem 4rem;
}

.mobileContent {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: center;
  padding: 1em 0;
}

@media (max-width: 515px) {
  .mobileContent {
    padding: 1em 0 4em 0;
  }

  .TitleChangeImage {
    padding: 1em 0 3em 0 !important;
  }

  .containerSetMarketplace {
    width: 95% !important;
  }
}

.TitleChangeImage {
  padding: 0em 4rem 1em 4rem;
}

.containerSetMarketplace {
  width: 80%;
  margin: auto;
}


.inputBox{
  input{
    width: 100% !important;
  }
}

