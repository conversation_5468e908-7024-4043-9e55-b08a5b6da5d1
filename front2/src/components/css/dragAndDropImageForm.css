#form-image-upload {
  height: 100%;
  width: 100%;
  max-width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* al estar el drag and drop */
.dragAndDrop {
  width: 100%;
  height: 100%;
  background-color: #c1e5ec;
  /* color: rgb(124, 124, 118); */
  /* color: #003876; */
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  border-width: 2px;
  border-radius: 0.5rem;
  border-style: dashed;
  border-color: #044ea485;
  background-color: #00377622;
}

#input-image-upload {
  display: none;
}

#label-image-upload {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-width: 2px;
  border-radius: 0.5rem;
  border-style: dashed;
  /* border-color: #003876; */
  background-color: #3131311e;
}

#label-image-upload.drag-active {
  background-color: #e5f6fd;
}
#label-image-upload.file-charged {
  background-color: #fff4e5;
}

#label-image-upload.file-error {
  background-color: #fdeded;
}

#label-image-upload.file-success {
  background-color: #edf7ed;
}

.upload-button {
  cursor: pointer;
  padding: 0.25rem;
  font-size: 1rem;
  border: none;
  font-family: "Oswald", sans-serif;
  background-color: transparent;
  width: 100%;
  height: 100%;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  /* color: #003876; */
}

.upload-button:hover {
  text-decoration-line: underline;
}

#drag-image-element {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1rem;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}
