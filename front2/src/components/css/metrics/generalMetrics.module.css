.Line<PERSON>hart {
    background-color: brown;

    .css-1k2u9zb-Mui<PERSON>hartsAxis-root {
        display: none !important;
    }
}

.css-6wcx0a-MuiResponsiveChart-container {
    background-color: brown;

    .css-1k2u9zb-Mui<PERSON>hartsAxis-root {
        display: none !important;
    }
}

.css-6wcx0a-MuiResponsiveChart-container>svg {
    background-color: brown;

    .css-1k2u9zb-MuiChartsAxis-root {
        display: none !important;
    }
}

/* .ssshhsshhs{
    background-color: brown;
    
}

.CardMetric{
    background-color: red;
} */

#CardMetricwe {
    background-color: red;
}

.MuiBox-root {
    background-color: red;
}

.css-6wcx0a-MuiResponsiveChart-container>svg {
    background-color: brown;

}

.nuevooo {
    /* background-color: brown !important; */

    /* .css-1qz5qoy-MuiPaper-root-MuiCard-root{
    .css-6wcx0a-MuiResponsiveChart-container{
        background-color: brown !important;
        
    }
} */
    
    /* .bonoooo div{
        width: 120% !important;
        height: auto !important;
        position: relative;
        left: -35px;
        background-color: aqua !important;

        svg {
            background-color: brown !important;
            .css-1k2u9zb-MuiChartsAxis-root {
                display: none !important;
              }
        }
    } */
}

