@media (max-width: 805px) {
  .decriptionResponsive {
    display: block !important;
  }
  .hiddenCellDescription {
    display: none !important;
  }
}

.hiddenCellDescription {
  padding: 1em 0;
}

/* .ecomicDateTable{
  min-width: max-content;
} */

@media (max-width: 915px) {
  .economicDataTable {
    display: none !important;
  }
  .ecomicDateTable {
    display: none !important;
  }
  .EconomicDataResponsive {
    display: block !important;
  }

  .contentEconomicDescription {
    display: block !important;
    margin-bottom: 1em;
  }

  .decriptionResponsive {
    display: none;
  }

  /* .descriptionResponsive {
      display: none !important;
    } */
}

@media (max-width: 1020px) {
  .showActions {
    display: block !important;
  }
  .hiddenCell {
    display: none !important;
  }
  .accionesHidden {
    display: none !important;
  }
}

@media (max-width: 580px) {
  .status {
    display: none !important ;
  }
  .showStatus {
    display: block !important;
  }
}

@media (max-width: 420px) {
  /* .status {
    display: none !important ;
  }
  .showStatus {
    display: block !important;
  } */
  .hiddenEmptyCell {
    display: none !important;
  }
  .showEmptyCell {
    display: block !important;
    margin-top: 2em;
  }
}

@media (max-width: 500px) {
  .showEconomicSubTable {
    display: block !important;
  }
  .hiddenEconomicSubTable {
    display: none !important;
  }
}

.showEconomicSubTable {
  display: none;
}

.hiddenEconomicSubTable {
  display: none;
}

.showEmptyCell {
  display: none;
  margin-top: 2em;
}

.showStatus {
  display: none;
}

.showActions {
  display: none;
}

.contentEconomicDescription {
  display: none;
}
.css-rorn0c-MuiTableContainer-root {
  overflow: none;
}
