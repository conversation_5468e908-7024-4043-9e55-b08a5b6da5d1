@media (max-width: 1020px) {
  /* .economicDataTable {
    display: none !important;
  } */
  .responsiveGuia {
    display: none !important;
  }
  .accionesHidden {
    display: none !important;
  }
  .showActions {
    display: block !important;
  }
  .guiaResponsiva {
    display: block !important;
  }
}

.statusResponsiveNormal{
.guiaResponsiva {
  display: none;
}}

@media (max-width: 450px) {
  .openCloseResponsive {
    display: block !important;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .responsiveOpenClose {
    display: none !important;
  }
  .showAllDataDescription{
    display: block !important;
  
  }
}
.showAllDataDescription{
  display: none;

}

.openCloseResponsive {
  display: none ;
}
.responsiveOpenClose {
  display: block;
}

@media (max-width: 915px) {
  .OutlinedCard {
    display: none !important;
  }
  .hiddenTableCell {
    display: none !important;
  }
  .EconomicDataResponsive {
    display: block !important;
  }

  .decriptionResponsive {
    display: block;
  }
  .ecomicDateTable{
    display: none !important;
  }
  .showAmount {
    display: block !important;
  }
  .datata{
    .showAmountLast{
      display: block !important;
    }
  }
  
}
.datata{
  .showAmountLast{
    display: none ;
  }
}

.showAmount {
  display: none ;
}
.decriptionResponsive .showActions {
  display: none !important;
}

@media (max-width: 800px) {
  .descriptionResponsive {
    display: none !important;
  }
  
  .DescriptionData {
    display: block !important;
  }
  .descriptionDataTable {
    display: none !important;
  }
}

@media (max-width: 600px) {
  #statusResponsive {
    display: block !important;
    margin-top: 1em;
  }
  .EconomicDataConsolidate {
    display: block !important;
  }
  .statusResponsiveNormal {
    display: none !important;
  }
  .showAmount {
    display: none !important;
  }
}

.EconomicDataResponsive {
  display: none;
}
.EconomicDataConsolidate {
  display: none;
}

/* .ecomicDateTable {
  display: none;
} */

/* est descripcion esta entre economic data al hacerce responsivo
pero quiero que se active a los 800px
*/
.DescriptionData {
  display: none;
}

#statusResponsive {
  display: none;
}
