.divContainerUser {
  display: flex;
  align-items: center;
  width: fit-content;
  padding: 0.5rem 1rem;
  border-radius: 40px;
}

.userP {
  margin: 0;
  padding: 0 0.5rem 0 1.5rem;
}

.p {
  margin-bottom: 4px;
}

.pText {
  margin: 0;
  padding: 0;
}

.pTextFlex {
  margin: 0;
  padding: 0;
  display: flex;
}

.pDescriptionProduct {
  margin-bottom: 4px;
  margin-left: 5px;
  color: rgb(58, 51, 94);
  display: flex;
  /* align-items: center; */
}

.pDescriptionProductText {
  margin-left: 5px;
  margin-bottom: 0;
}

.section {
  margin: 1rem 0;
}

/* estylos */
.cliente {
  display: flex;
  justify-items: center;
}

.widthCell {
  min-width: auto;
}

.contentBtn {
  padding: 1rem .2em;
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: .4em;
  /* flex-wrap: wrap; */
}

@media screen and (max-width: 768px) {
  .contentBtn {
      flex-wrap: wrap;

  }
  
}

.tableProducts {
  display: flex;
  align-items: flex-end;
 
}

.AccordionProducts {
  display: none;
}

@media (max-width: 850px) {
  

  .AccordionProducts {
    display: block;
  }

  .AccordionContainInfo {
    display: flex;
    flex-direction: row;
    gap: 1em;
    width: 100%;
  }

  .AccordionContainInfoSale {
    display: flex;
    flex-direction: column;
    gap: 1em;
    width: 45%;
    margin: auto;
  }

 /*  .resume {
    max-width: 350px;
  }  */

  .AccordionContainInfoResume {
    width: 35%;
    margin: auto;
  }

  .css-o4b71y-MuiAccordionSummary-content {
    display: flex;
    align-items: center;
  }

  .header {
    display: flex;
    align-items: center;
  }
}

.resume {
  min-width: 200px;
  display: flex;
}

@media (max-width: 1413px) {
  .hiddenCell {
    display: none !important;
  }

  .showInput {
    display: block !important;
  }

  .space {
    top: 81px !important;
  }

  .margin {
    margin: auto;
  }

  /* TableCell,
  th {
    padding: 0%;
  } */
  /* .css-17fajfj-MuiTableCell-root {
    padding: 0;
  } */
}

.showInput {
  display: none;
}

.space {
  top: 39px;
}
