import React from "react";
import TableContainer from "@mui/material/TableContainer";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableHead from "@mui/material/TableHead";
import TableBody from "@mui/material/TableBody";
import { useDispatch, useSelector } from "react-redux";
import { useCookies } from "react-cookie";
import IconButton from "@mui/material/IconButton";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import EditIcon from "@mui/icons-material/Edit";
import Collapse from "@mui/material/Collapse";
import DetalleDesplegableFactura from "./DetalleDesplegableFactura";
import Avatar from "@mui/material/Avatar";
import { getProviderListPropertyUsingIssuerRFC } from "../../Utils/atributtesHandlerSupplier";
import { crearFechaFormato } from "../../Utils/atributtesHandler";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import DisabledByDefaultIcon from "@mui/icons-material/DisabledByDefault";
import ReportIcon from "@mui/icons-material/Report";
import {
  addNewCommentFunctionGeneral,
  deleteCommentGeneral,
  updateCommentGeneral,
  noComment,
  returnCommentTypeUrl,
} from "../utils/utils";
import { StyledTableCell, StyledTableRow } from "../StyledTableComponents";
import { rateToPercent } from "../../Utils/generalFunctions";
import { Delete, Remove } from "@mui/icons-material";
import { deleteFactura } from "../../redux/facturasDucks";
import ModalConfirmacionBorrar from "../componentesGenerales/ModalConfirmacionBorrar";
import { Tooltip, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import CustomDialog from "../componentesGenerales/CustomDialog";
import { BreakDownInvoices } from "./tablaFacturasComponentes/BreakDownInvoices";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

const Row = ({ entryDocument, index }) => {
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();

  const [open, setOpen] = React.useState(false);
  const [openDeleteDocument, setOpenDeleteDocument] = React.useState(false);

  const id = entryDocument.id;
  const csrf_access_token = cookies.csrf_access_token;
  const commentType = "invoice";
  const commentTypeUrlAdd = returnCommentTypeUrl(commentType, "add");
  const commentTypeUrlDelete = returnCommentTypeUrl(commentType, "delete");
  const commentTypeUrlUpdate = returnCommentTypeUrl(commentType, "update");

  const [comments, setComments] = React.useState(
    entryDocument.entryDocumentStackableComments
  );
  const [lastComment, setLastComment] = React.useState(
    comments.length > 0 ? comments[comments.length - 1] : noComment
  );

  const proveedorStyle = getProviderListPropertyUsingIssuerRFC(
    entryDocument.issuerRfc,
    "providerStyle"
  );
  const user = useSelector((store) => store.usuario.info);
  const [productsState, setProductsState] = React.useState(null);

  const onClickExpand = () => {
    setOpen(!open);
  };

  const addNewCommentFunction = (commentToAdd) => {
    return addNewCommentFunctionGeneral(
      id,
      commentToAdd,
      commentType,
      csrf_access_token,
      comments,
      setComments,
      setLastComment,
      commentTypeUrlAdd
    );
  };
  const deleteComment = (id) => {
    return deleteCommentGeneral(
      id,
      commentTypeUrlDelete,
      csrf_access_token,
      comments,
      setComments,
      setLastComment,
      lastComment
    );
  };

  const updateComment = async (id, commentToUpdate) => {
    
    return updateCommentGeneral(
      id,
      commentToUpdate,
      csrf_access_token,
      commentTypeUrlUpdate,
      comments,
      setComments,
      setLastComment
    );
  };

  const armarProductosCadena = (productosJSON) => {
    const maxProductos = 1; // Número máximo de productos a mostrar
    const lenMax = 140;
    const lenProductos = productosJSON?.length;
    const separator = " || ";
    const lenSeparator = separator.length;
    const sizeToProduct = (lenMax - lenSeparator * (maxProductos - 1)) / maxProductos;

    if (!productosJSON || productosJSON.length === 0) return "";

    const productosMostrados = productosJSON.slice(0, maxProductos);
    const productosRestantes = productosJSON.length - maxProductos;

    const productosFormateados = productosMostrados
      .map((producto) => producto.description.slice(0, 15))
      .join(separator);

    return productosRestantes > 0
      ? `${productosFormateados} ... +${productosRestantes}`
      : productosFormateados;
  };

  React.useEffect(() => {
    const allProductsNotNull = entryDocument.productsInEntryDocument.every(
      (item) => item.productBase !== null
    );
    const allProductsCorrect = entryDocument.productsInEntryDocument.every(
      (item) => item.totalEntered === item.units
    );
    const atLeastOneProductNotNull = entryDocument.productsInEntryDocument.some(
      (item) => item.productBase !== null
    );

    if (allProductsNotNull && allProductsCorrect) {
      setProductsState("correct");
    } else if (atLeastOneProductNotNull) {
      setProductsState(2);
    } else {
      setProductsState(null);
    }
  }, [entryDocument]);

  const retornarTotalFacturaEIvas = () => {
    var totalFactura = 0;
    var totalIvas = 0;
    var subtotal = 0;
    entryDocument?.productsInEntryDocument?.forEach((producto) => {
      subtotal = subtotal + parseFloat(producto?.conceptAmount);
      totalFactura =
        totalFactura +
        parseFloat(producto?.conceptAmount) *
        (1 +
          rateToPercent(
            parseFloat(
              producto?.productInEntryDocumentTaxes[0]?.taxRate || 16
            )
          ));
      totalIvas =
        totalIvas +
        parseFloat(producto?.conceptAmount) *
        rateToPercent(
          parseFloat(producto?.productInEntryDocumentTaxes[0]?.taxRate || 16)
        );
    });
    return { totalFactura, totalIvas, subtotal };
  };
  let totalFactura = 0;
  let totalIvas = 0;
  let subtotal = 0;
  if (user.role.roleName != "Logistics_employee") {
    totalFactura = retornarTotalFacturaEIvas().totalFactura;
    totalIvas = retornarTotalFacturaEIvas().totalIvas;
    subtotal = retornarTotalFacturaEIvas().subtotal;
  }
  console.log("entry", entryDocument);

  const handleDeleteDocument = (id) => {
    dispatch(deleteFactura(id, csrf_access_token));
  }

  const theme = useTheme();

  const resum = ({ totalFactura, entryDocument, user }) => {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', gap: 1, flexDirection: "column" }}>

        ${totalFactura.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}  {" "} {entryDocument.currency}
        {entryDocument.paymentConditions && (
          <Typography variant="caption" display="block" sx={{
            mt: 0.5,
            display: {
              xss: "block",
              lg: "none",
            }
          }}>{entryDocument.paymentConditions}</Typography>)}

        <Typography variant="caption" display="block" sx={{
          mt: 0.5,
          display: {
            xss: "block",
            md: "none",
          }
        }}>
          {crearFechaFormato(entryDocument.issueDate)}
         
        </Typography>

      </Box>
    )
  }

  return (
    <React.Fragment>
      <ModalConfirmacionBorrar deleteFunction={() => handleDeleteDocument(id)} open={openDeleteDocument} setOpen={setOpenDeleteDocument} />

      <StyledTableRow key={entryDocument.id}
        onClick={onClickExpand}
      >
        <StyledTableCell>
          <Avatar
            style={{ height: "80px", width: "80px" }}
            src={proveedorStyle.logo}
          />
          <Typography variant="body2" sx={{ whiteSpace: 'nowrap' }}>
            ID: <strong>{entryDocument.invoice
              ? `${entryDocument.invoice.serie}/${entryDocument.internalId}`
              : ` ISF/${entryDocument.internalId}`}</strong>
          </Typography>
        </StyledTableCell>
        <StyledTableCell align="center" sx={{
          minWidth: {
            xss: "100px",
            sm: "190px",
          },
          padding: {
            xss: "10px 0 10px 0 !important",
            sm: "auto !important",
          }
        }}>
          {armarProductosCadena(entryDocument.productsInEntryDocument)}
          <Typography variant="caption" display="block" sx={{
            mt: 0.5,
            display: {
              xss: "block",
              sm: "none",
            }
          }}>
            {resum({ totalFactura, entryDocument, user })}
          </Typography>
        </StyledTableCell>

        <StyledTableCell align="center" sx={{
          display: {
            xss: "none",
            md: "table-cell",
          }
        }}>
          {crearFechaFormato(entryDocument.issueDate)}
        

        </StyledTableCell>

        {user.role.roleName != "Logistics_employee" ? (
          <>
            <StyledTableCell align="center" sx={{
              display: {
                xss: "none",
                sm: "table-cell",
              }
            }}>
              {resum({ totalFactura, entryDocument, user })}
            </StyledTableCell>
            <StyledTableCell align="center" sx={{
              display: {
                xss: "none",
                lg: "table-cell",
              }
            }}>
              {entryDocument.paymentConditions}
            </StyledTableCell>
          </>
        ) : null}

        {user.role.roleName === "Admin" ? (
          <StyledTableCell align="center">
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, alignItems: "center", flexWrap: "wrap" }}>
              <IconButton
                variant="outlined"
                color="primary"
                onClick={() => (window.location = `/facturas/manual/${id}`)}
              >
                <EditIcon />
              </IconButton>

              <IconButton
                variant="outlined"
                color="primary"
                onClick={() => setOpenDeleteDocument(true)}
                disabled={productsState === "correct" || productsState === 2}
              >
                <Delete />
              </IconButton>

              {productsState === "correct" ? (
                <Tooltip title="Productos correctos">
                  <CheckCircleOutlineIcon 
                    fontSize="medium" 
                    sx={{ color: "#4caf50" }} 
                  />
                </Tooltip>
              ) : productsState === 2 ? (
                <Tooltip title="Productos faltantes">
                  <ErrorOutlineIcon 
                    fontSize="medium" 
                    sx={{ color: "#fbc02d" }} 
                  />
                </Tooltip>
              ) : (
                <Tooltip title="Productos incorrectos">
                  <CancelOutlinedIcon
                    fontSize="medium"
                    sx={{ color: "#d32f2f" }}
                  />
                </Tooltip>
              )}
              
            </Box>
          </StyledTableCell>

        ) : user.role.roleName != "Logistics_employee" ? (
          <StyledTableCell align="center">
            {productsState === "correct" ? (
              <Tooltip title="Productos correctos">
                <CheckCircleOutlineIcon 
                  fontSize="medium" 
                  sx={{ color: "#4caf50" }} 
                />
              </Tooltip>
            ) : productsState === 2 ? (
              <Tooltip title="Productos faltantes">
                <ErrorOutlineIcon 
                  fontSize="medium" 
                  sx={{ color: "#fbc02d" }} 
                />
              </Tooltip>
            ) : (
              <Tooltip title="Productos incorrectos">
                <CancelOutlinedIcon
                  fontSize="medium"
                  sx={{ color: "#d32f2f" }}
                />
              </Tooltip>
            )}
          </StyledTableCell>
        ) : null}

      </StyledTableRow>


      <CustomDialog
        open={open}
        onClose={ onClickExpand}
        title="Desglose de Factura"
        maxHeight="80vh"
        maxWidth="90%"
        width="100%"
      >
        <>
         <BreakDownInvoices entryDocument={entryDocument} totalFactura={totalFactura.toFixed(2)} subtotal={subtotal.toFixed(2)} totalIvas={totalIvas.toFixed(2)} addNewCommentFunction={addNewCommentFunction} comments={comments} deleteComment={deleteComment} updateComment={updateComment} user={user} />
        </>
      </CustomDialog>

    </React.Fragment >
  );
};



const TablaDesplegableFacturas = ({ entryDocuments }) => {

  const user = useSelector((store) => store.usuario.info);

  // son mis constantes de media query ->->-<-<-<
  const theme = useTheme()
  // console.log("themeBreak", props);

  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md"));

  // abajo de 700
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  // abajo de 1280
  const isDownLargeScreen = useMediaQuery(theme.breakpoints.down("lg"));
  // entre 960 y 1060


  return (
    <TableContainer sx={{ borderRadius: "25px", }}>
      <Table aria-label="collapsible table">
        <TableHead>
          <StyledTableRow>

            <StyledTableCell align="center">Proveedor</StyledTableCell>
            <StyledTableCell align="center">Productos</StyledTableCell>
            <StyledTableCell align="center" sx={{
              display: {
                xss: "none",
                md: "table-cell",
              }
            }}>Fechas</StyledTableCell>

            {user.role.roleName != "Logistics_employee" && (
              <>
                <StyledTableCell align="center" sx={{
                  display: {
                    xss: "none",
                    sm: "table-cell",
                  }
                }}>Resumen</StyledTableCell>

                <StyledTableCell align="center" sx={{
                  display: {
                    xss: "none",
                    lg: "table-cell",
                  }

                }} > Términos</StyledTableCell>

              </>
            )}

            {/* Campo exclusivo para Admin */}
            {user.role.roleName === "Admin" ? (
              <StyledTableCell align="center">
                {isSmallScreen ? "Acc." : "Acciones"}
              </StyledTableCell>
            ) : user.role.roleName != "Logistics_employee" ? (
              <StyledTableCell align="center">
                {isSmallScreen ? "Acc." : "Acciones"}
              </StyledTableCell>
            ) : null}

          </StyledTableRow>
        </TableHead>
        <TableBody>
          {entryDocuments.map((entryDocument, index) => (
            <Row key={entryDocument.id} entryDocument={entryDocument} index={index} />
          ))}
        </TableBody>
      </Table>
    </TableContainer >
  );
};

export default TablaDesplegableFacturas;
