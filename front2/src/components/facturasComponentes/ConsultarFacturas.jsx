import React from "react";
import { useSearchParams } from "react-router-dom";
import Collapse from "@mui/material/Collapse";
import { obtenerTotalFacturas } from "../../redux/facturasDucks";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import { useDispatch, useSelector } from "react-redux";
import Pagination from "@mui/material/Pagination";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import CargandoLista from "../CargandoLista";
import Fab from "@mui/material/Fab";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import Alert from "@mui/material/Alert";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import { useTheme, styled, alpha } from "@mui/material/styles";
import OutlinedInput from "@mui/material/OutlinedInput";
import TextField from "@mui/material/TextField";
import Chip from "@mui/material/Chip";
import { DesktopDatePicker } from "@mui/x-date-pickers/DesktopDatePicker";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import {
  providerList,
  getProviderListPropertyUsingIssuerRFC,
} from "../../Utils/atributtesHandlerSupplier";
import Grid from "@mui/material/Grid";
import { useNavigate } from "react-router-dom";
import SearchField from "../SearchField";
import TablaDesplegableFacturasWrapper from "./TablaDesplegableFacturasWrapper";
import ModalGeneral from "../ModalGeneral";
import { ManejarErrores } from "../ManejarErrores";
import { Menu } from "@mui/material";
import { SkeletonTables } from "../componentesGenerales/SkeletonTables";
import { AlertComponent } from "../componentesGenerales/Alert";

//const
const ITEM_HEIGHT = 30;
const ITEM_PADDING_TOP = 4;

const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

//functions

const StyledMenu = styled((props) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'right',
      left: "3em"
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'right',
    }}
    {...props}
  />
))(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    left: "85% !important",
    top: "20% !important",
    color:
      theme.palette.mode === 'light' ? 'rgb(55, 65, 81)' : theme.palette.grey[300],
    boxShadow:
      'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
    '& .MuiMenu-list': {
      padding: '4px 0',
    },
    '& .MuiMenuItem-root': {
      '& .MuiSvgIcon-root': {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      '&:active': {
        backgroundColor: alpha(
          theme.palette.primary.main,
          theme.palette.action.selectedOpacity,
        ),
      },
    },
  },
}));

const getStyles = (status, listaStatus, theme) => {
  return {
    fontWeight:
      listaStatus.indexOf(status) === -1
        ? theme.typography.fontWeightRegular
        : theme.typography.fontWeightMedium,
  };
};

const ConsultarFacturas = () => {
  /*Hooks*/
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const theme = useTheme();
  const [searchParams] = useSearchParams();
  /*Obtener la fecha actual */
  const today = new Date();
  /*url params */
  /**Nombres globales para los parametros de los filtros */
  const initialDate =
    searchParams.get("initialDate") === null
      ? ""
      : searchParams.get("initialDate");
  const finalDate =
    searchParams.get("finalDate") === null
      ? today.toISOString().split("T")[0]
      : searchParams.get("finalDate");
  const page = searchParams.get("page") === null ? 1 : searchParams.get("page");
  const searchText =
    searchParams.get("search") === null ? "" : searchParams.get("search");

  /*  Use selector -> variables desde redux */
  const esperaTortuga = useSelector((store) => store.facturas.esperaTortuga);
  const cantidadDeFacturassPorPagina = useSelector(
    (store) => store.facturas.nextC
  );
  const cantidadDePaginas = useSelector(
    (store) => store.facturas.cantidadDePaginas
  );
  const [open, setOpen] = React.useState(false);
  const offset = cantidadDeFacturassPorPagina * (page - 1);
  const handleOpen = () => setOpen(true);
  const [showingFilters, setShowingFilters] = React.useState(false);
  const [mensajeModal, setMensajeModal] = React.useState("");

  const [initialDatePicker, setInitialDatePicker] = React.useState(
    searchParams.get("initialDate") === null
      ? new Date(today.getFullYear(), today.getMonth(), 1)
      : new Date(
        searchParams.get("initialDate").split("-")[0],
        (
          parseInt(searchParams.get("initialDate").split("-")[1]) - 1
        ).toString(),
        searchParams.get("initialDate").split("-")[2]
      )
  );
  const [finalDatePicker, setFinalDatePicker] = React.useState(
    searchParams.get("finalDate") === null
      ? today
      : new Date(
        searchParams.get("finalDate").split("-")[0],
        (
          parseInt(searchParams.get("initialDate").split("-")[1]) - 1
        ).toString(),
        searchParams.get("finalDate").split("-")[2]
      )
  );

  // Estados para manejo de errores centralizados
  const [errorMessage, setErrorMessage] = React.useState("");
  const [errorType, setErrorType] = React.useState("error");

  const proveedores =
    searchParams.get("provider") === null ? "" : searchParams.get("provider");

  const [proveedoresFiltro, setProveedoresFiltro] = React.useState(
    searchParams.get("provider") === null
      ? providerList.map((proveedor) => proveedor.issuerRfc)
      : searchParams.get("provider").split("-")
  );

  const mensajeAlertaProveedores =
    "Selecciona al menos un proveedor";

  const handleClose = () => setOpen(false);

  // Función para limpiar mensajes de error
  const cleanErrorMessage = () => {
    setErrorMessage("");
  };

  const manejarCambioDeProveedoresFiltros = (event) => {
    var nombre = event.target.name;
    var valor = event.target.value;
    setProveedoresFiltro(
      // On autofill we get a stringified value.
      typeof valor === "string" ? valor.split(",") : valor
    );
  };

  const allProveedoresFiltros = providerList.map(
    (proveedor) => proveedor.issuerRfc
  );
  const handleAllProveedoresFiltros = (event) => {
    setProveedoresFiltro(
      // On autofill we get a stringified value.
      typeof allProveedoresFiltros === "string"
        ? allProveedoresFiltros.split(",")
        : allProveedoresFiltros
    );
  };

  const noneProveedoresFiltros = [];
  const handleNoneProveedoresFiltros = (event) => {
    setProveedoresFiltro(
      // On autofill we get a stringified value.
      typeof noneProveedoresFiltros === "string"
        ? noneProveedoresFiltros.split(",")
        : noneProveedoresFiltros
    );
  };

  const aplicarFiltros = () => {
    // Limpiar errores anteriores
    setErrorMessage("");

    // Validación de proveedores
    if (proveedoresFiltro.length === 0) {
      setErrorMessage(mensajeAlertaProveedores);
      setErrorType("error");
      return;
    }

    // Validación de fecha inicial
    if (initialDatePicker > today) {
      setErrorMessage("Fecha de inicio incorrecta!");
      setErrorType("error");
      return;
    }

    // Validación de fecha final
    if (finalDatePicker > today) {
      setErrorMessage("Fecha de fin incorrecta!");
      setErrorType("error");
      return;
    }

    // Validación de rango de fechas
    if (finalDatePicker - initialDatePicker < 0) {
      setErrorMessage("Rango incorrecto, la fecha de inicio no puede ser después de la fecha de fin!");
      setErrorType("error");
      return;
    }

    let proveedorFiltro = "";
    proveedoresFiltro.forEach((element) => {
      if (proveedorFiltro !== "") {
        proveedorFiltro = proveedorFiltro + "-";
      }
      proveedorFiltro = proveedorFiltro + element.toString();
    });
    dispatch(
      obtenerTotalFacturas({
        proveedor: proveedorFiltro,
        finalDate: finalDatePicker.toISOString().split("T")[0],
        initialDate: initialDatePicker.toISOString().split("T")[0],
        search: searchText,
      })
    );
    navigate(
      `?provider=${proveedorFiltro}&page=${1}&initialDate=${initialDatePicker.toISOString().split("T")[0]
      }&finalDate=${finalDatePicker.toISOString().split("T")[0]
      }&search=${searchText}`
    );
  };

  const handleShowingFilters = () => {
    setShowingFilters(!showingFilters);
  };

  const enviarSearch = (event) => {
    event.preventDefault();
    var nuevoSearch = event.target[1].value;
    dispatch(
      obtenerTotalFacturas({
        proveedor: proveedores,
        finalDate: finalDate,
        initialDate: initialDate,
        search: nuevoSearch,
      })
    );
    navigate(
      `?provider=${proveedores}&page=${1}&initialDate=${initialDate}&finalDate=${finalDate}&search=${nuevoSearch}`
    );
  };

  const cambioDePagina = (evento, valor) => {
    dispatch(
      obtenerTotalFacturas({
        proveedor: proveedores,
        finalDate: finalDate,
        initialDate: initialDate,
        search: searchText,
      })
    );
    navigate(
      `?proveedor=${proveedores}&page=${valor}&initialDate=${initialDate}&finalDate=${finalDate}&search=${searchText}`
    );
  };

  const [anchorEl, setAnchorEl] = React.useState(null);
  const openMenu = Boolean(anchorEl);

  const handleClickMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleRedirect = (tipo) => {
    if (tipo === 2) {
      navigate("/facturas/subir");
    } else if (tipo === 1) {
      navigate("/facturas/manual");
    }
  };

  /*Componente grid donde se encuentran los filtros */
  const FiltersGrid = () => {
    return (
      <Box sx={{ flexGrow: 1, mb: 1, display: "flex", alignItems: "center" }}>
        <Grid container spacing={2}>
          <Grid item xs={1}>
            <Fab
              size="medium"
              color="buttonGreen"
              aria-label="add"
              style={{ marginBottom: "5px" }}
              onClick={() => handleShowingFilters()}
            >
              <FilterAltIcon />
            </Fab>
          </Grid>
          {/* <Grid item xs={9}>
            <SearchField enviarSearch={enviarSearch} searchText={searchText} />
          </Grid> */}

        </Grid>
        <Box item xs={3} sx={{ display: "flex", justifyContent: "end", paddingX: "1em", paddingTop: "1em", marginBottom: "1em" }}>
          <Button
            id="demo-customized-button"
            aria-controls={openMenu ? 'demo-customized-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={openMenu ? 'true' : undefined}
            variant="outlined"
            disableElevation
            onClick={handleClickMenu}
            color="buttonGreenPink"
          // endIcon={<KeyboardArrowDownIcon />}
          // startIcon={< />}
          >
            Añadir
          </Button>
          <StyledMenu
            id="demo-customized-menu"
            MenuListProps={{
              'aria-labelledby': 'demo-customized-button',
            }}
            anchorEl={anchorEl}
            open={openMenu}
            onClose={handleCloseMenu}
          >
            <MenuItem onClick={() => handleRedirect(1)} disableRipple>
              Manual
            </MenuItem>
            <MenuItem onClick={() => handleRedirect(2)} disableRipple>
              Masivo
            </MenuItem>

          </StyledMenu>

        </Box>
      </Box>
    );
  };
  /*UseEffect */
  React.useEffect(() => {
    const fetchData = () => {
      dispatch(
        obtenerTotalFacturas({
          proveedor: proveedores,
          finalDate: finalDate,
          initialDate: initialDate,
          search: searchText,
        })
      );
    };
    fetchData();
  }, []);


  /*Manejar el return */
  if (esperaTortuga === false && cantidadDePaginas !== null) {
    if (typeof cantidadDePaginas === "string") {
      return <ManejarErrores errorCode={cantidadDePaginas} />;
    } else {
      return (
        <React.Fragment>
          <ModalGeneral
            open={open}
            handleClose={handleClose}
            mensajeModal={mensajeModal}
          />

          <FiltersGrid />

          <Collapse in={showingFilters} timeout="auto" sx={{ mb: 2 }}>
            <Box
              display="flex"
              flexDirection={{ xss: "column", md: "row" }}
              gap={2}
              sx={{
                maxWidth: "100%",
                marginBottom: "5px",
                padding: { xss: 1, md: 0 }
              }}
            >
              {/* Sección de Proveedores */}
              <Box sx={{
                width: { xss: "100%", md: "auto" },
                minWidth: { md: "350px" },
                flex: { md: "0 0 auto" }
              }}>
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  sx={{ width: "100%" }}
                >
                  <FormControl sx={{
                    marginTop: "5px",
                    width: { xss: "100%", sm: "100%", md: "400px" },
                    maxWidth: "600px"
                  }}>
                    <InputLabel id="demo-multiple-chip-label">
                      Proveedores
                    </InputLabel>
                    <Select
                      labelId="demo-multiple-chip-label"
                      id="demo-multiple-chip"
                      multiple
                      value={proveedoresFiltro}
                      onChange={manejarCambioDeProveedoresFiltros}
                      input={
                        <OutlinedInput
                          id="select-multiple-chip"
                          label="Status interno"
                        />
                      }
                      renderValue={(selected) =>
                        selected.length === providerList.length ? (
                          <Chip key="all-selected" label="Todos seleccionados" />
                        ) : (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.map((value) => (
                              <Chip
                                key={value}
                                label={getProviderListPropertyUsingIssuerRFC(value, "providerName")}
                              />
                            ))}
                          </Box>
                        )
                      }
                      MenuProps={MenuProps}
                    >
                      {providerList.map((provider) => (
                        <MenuItem
                          key={provider.issuerRfc}
                          value={provider.issuerRfc}
                          style={getStyles(
                            provider.providerName,
                            proveedoresFiltro,
                            theme
                          )}
                        >
                          {provider.providerName}
                        </MenuItem>
                      ))}
                    </Select>

                  </FormControl>
                </Box>

                {/* Botones de selección */}
                <Box
                  display="flex"
                  flexDirection={{ xss: "column", sm: "row" }}
                  gap={1}
                  sx={{
                    justifyContent: "center",
                    marginTop: 1
                  }}
                >
                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      width: { xss: "100%", sm: "195px" },
                      minWidth: "150px"
                    }}
                    color="buttonGreenPink"
                    onClick={handleAllProveedoresFiltros}
                  >
                    Seleccionar Todos
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{
                      width: { xss: "100%", sm: "195px" },
                      minWidth: "150px"
                    }}
                    onClick={handleNoneProveedoresFiltros}
                  >
                    Seleccionar Ninguno
                  </Button>
                </Box>
              </Box>

              {/* Sección de Fechas y Botón */}
              <Box
                display="flex"
                flexDirection={{ xss: "column", lg: "row" }}
                gap={2}
                sx={{
                  flex: 1,
                  minWidth: { xss: "100%", md: "400px" }
                }}
              >
                {/* Subsección de Fechas */}
                <Box sx={{ flex: 1 }}>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <Box
                      display="flex"
                      flexDirection={{ xss: "column", sm: "row" }}
                      gap={2}
                      sx={{
                        paddingTop: "10px",
                        minHeight: { xss: "auto", md: "130px" }
                      }}
                    >
                      <Box sx={{
                        width: { xss: "100%", sm: "50%" },
                        minWidth: "200px"
                      }}>
                        <DesktopDatePicker
                          label="Desde:"
                          inputFormat="MM/dd/yyyy"
                          value={initialDatePicker}
                          onChange={(date) => setInitialDatePicker(date)}
                          maxDate={new Date()} // <-- Solo permite fechas hasta hoy
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              fullWidth
                              sx={{ width: "100%" }}
                            />
                          )}
                        />
                      </Box>

                      <Box sx={{
                        width: { xss: "100%", sm: "50%" },
                        minWidth: "200px"
                      }}>
                        <DesktopDatePicker
                          label="Hasta:"
                          inputFormat="MM/dd/yyyy"
                          value={finalDatePicker}
                          onChange={(date) => setFinalDatePicker(date)}
                          maxDate={new Date()} // <-- Solo permite fechas hasta hoy
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              fullWidth
                              sx={{ width: "100%" }}
                            />
                          )}
                        />
                      </Box>
                    </Box>
                  </LocalizationProvider>
                </Box>

                {/* Botón de Filtrar - Siempre del lado derecho */}
                <Box
                  display="flex"
                  alignItems={{ xss: "flex-start", lg: "flex-end" }}
                  sx={{
                    width: { xss: "100%", lg: "auto" },
                    paddingTop: { xss: "10px", lg: "10px" },
                    minWidth: { lg: "120px" },
                    justifyContent: { xss: "flex-end", lg: "center" }
                  }}
                >
                  <Button
                    variant="contained"
                    color="buttonGreenPink"
                    onClick={() => aplicarFiltros()}
                    sx={{
                      width: { xss: "auto", lg: "auto" },
                      minWidth: { xss: "100%", md :"100px",lg: "120px" },
                      height: "fit-content",
                      alignSelf: { xss: "flex-end", lg: "auto" }
                    }}
                  >
                    Filtrar
                  </Button>
                </Box>
              </Box>
            </Box>
          </Collapse>

          {cantidadDePaginas === 0 ? (
            <Box>No hay facturas que cumplan con estas especificiaciones</Box>
          ) : (
            <React.Fragment>
              <TablaDesplegableFacturasWrapper
                proveedor={proveedores}
                offset={offset}
                initialDate={initialDate}
                finalDate={finalDate}
                search={searchText}
              />
              <Box sx={{
                justifyContent: "center", display: "flex",
                marginTop: "1em", marginBottom: "1em"
              }}>
                <Stack spacing={2}>
                  <Pagination
                    count={cantidadDePaginas}
                    page={parseInt(page)}
                    onChange={cambioDePagina}
                  />
                </Stack>
              </Box>
            </React.Fragment>
          )}

          {/* AlertComponent centralizado para todos los errores */}
          {errorMessage && (
            <AlertComponent
              color={errorType}
              message={errorMessage}
              cleanMessage={cleanErrorMessage}
              time={4000}
            />
          )}
        </React.Fragment>
      );
    }
  } else {
    return (
      <>
        <SkeletonTables />
      </>
    );
  }
};

export default ConsultarFacturas;
