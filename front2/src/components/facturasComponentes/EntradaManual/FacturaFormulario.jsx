import React, { useEffect } from 'react'
import { Grid, TextField } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import { Select } from "@mui/material";
import Alert from "@mui/material/Alert";
import Box from '@mui/material/Box';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import CamposOpcionales from './CamposOpcionales';

const checkedToDisplay = (showOptionalFields, key) => {
  if (!(key in showOptionalFields)) {
    return 'inline'
  } else {
    return showOptionalFields[key].checked ? 'inline' : 'none'
  }
}
const FacturaFormulario = (props) => {


  const { invoice, isInvoice, showOptionalFields, setShowOPtionalFields, DateFieldCustom, SelectCustom, TextFieldCustom } = props;
  const today = new Date();



  return (
    <Grid container
      sx={{ justifyContent: "center", flexWrap: { xs: "wrap", md: "nowrap" } }}
    //sx={{ justifyContent: "center", alignItems: "center", border:1}}

    >
      <Grid item xss={10} sm={10} md={12} container sx={{ alignItems: "flex-start", }}>
        {isInvoice && Object.keys(invoice.invoice).map((key) => (
          <Grid item xss={12} sm={12} md={3} sx={{ padding: "16px", margin: 0, display: checkedToDisplay(showOptionalFields, key) }}>
            {invoice.invoice[key].type === "TextField" ? TextFieldCustom(["invoice"], key) : invoice.invoice[key].type === "Select" ? SelectCustom(["invoice"], key) : DateFieldCustom(["invoice"], key)}
          </Grid>
        ))}

        {Object.keys(invoice.general).map((key) => (
          <Grid item xss={12} sm={12} md={3} sx={{ padding: "16px", margin: 0, display: checkedToDisplay(showOptionalFields, key) }}>
            {invoice.general[key].type === "TextField" ? TextFieldCustom(["general"], key) : invoice.general[key].type === "Select" ? SelectCustom(["general"], key) : DateFieldCustom(["general"], key)}
          </Grid>
        ))}

      </Grid>

      <Grid item xss={12} sm={12} md={2} sx={{ maxWidth: "83.333333% !important", width: "100% !important" }} >
        <CamposOpcionales showOptionalFields={showOptionalFields} setShowOPtionalFields={setShowOPtionalFields} />
      </Grid>

    </Grid>
  )
}

export default FacturaFormulario
