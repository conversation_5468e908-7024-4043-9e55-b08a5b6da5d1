import React from "react";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { obtenerFacturaUsandoId } from "../../../redux/facturasDucks";
import FormularioEntradaManual from './FormularioEntradaManual'
import CargandoLista from "../../CargandoLista";
import { Button } from "@mui/material";
import styles from "../../css/findBySku.module.css";
import MenuItem from "@mui/material/MenuItem";
import { CODIGO_POSIBLE_UNIDAD } from "../../../Utils/config";
const today = new Date();
const itemsCfdiUsage =
    [
        <MenuItem value="G01">G01: Adquisición de mercancías</MenuItem>,
        <MenuItem value="G02">G02: Devoluciones, descuentos o bonificaciones</MenuItem>,
        <MenuItem value="G03">G03: Gastos en general</MenuItem>,
        <MenuItem value="I01">I01: Construcciones</MenuItem>,
        <MenuItem value="I02">I02: Mobiliario y equipo de oficina por inversiones</MenuItem>,
        <MenuItem value="I03">I03: Equipo de transporte</MenuItem>,
        <MenuItem value="I04">I04: Equipo de cómputo y accesorios</MenuItem>,
        <MenuItem value="I05">I05: Dados, troqueles, moldes, matrices y herramental</MenuItem>,
        <MenuItem value="I06">I06: Comunicaciones telefónicas</MenuItem>,
        <MenuItem value="I07">I07: Comunicaciones satelitales</MenuItem>,
        <MenuItem value="I08">I08: Otra maquinaria y equipo</MenuItem>,
        <MenuItem value="D01">D01: Honorarios médicos, dentales y gastos hospitalarios.</MenuItem>,
        <MenuItem value="D02">D02: Gastos médicos por incapacidad o discapacidad</MenuItem>,
        <MenuItem value="D03">D03: Gastos funerales.</MenuItem>,
        <MenuItem value="D04">D04: Donativos</MenuItem>,
        <MenuItem value="D05">D05: Intereses reales efectivamente pagados por créditos hipotecarios (casa habitación).</MenuItem>,
        <MenuItem value="D06">D06: Aportaciones voluntarias al SAR.</MenuItem>,
        <MenuItem value="D07">D07: Primas por seguros de gastos médicos.</MenuItem>,
        <MenuItem value="D08">D08: Gastos de transportación escolar obligatoria.</MenuItem>,
        <MenuItem value="D09">D09: Depósitos en cuentas para el ahorro, primas que tengan como base planes de pensiones.</MenuItem>,
        <MenuItem value="D10">D10: Pagos por servicios educativos (colegiaturas)</MenuItem>,
        <MenuItem value="P01">P01: Por definir.</MenuItem>
    ]
const itemsCurrency = [
    <MenuItem value="MXN">MXN - Pesos Mexicanos</MenuItem>,
    <MenuItem value="USD">USD - US Dollar</MenuItem>
]

const itemsPaymentMethod = [
    <MenuItem value="PUE">PUE - Payment in one exhibition</MenuItem>,
    <MenuItem value="PPD">PPD - Payment in partialities or deferred</MenuItem>
]

const codigoDeUnidadPosible = () => CODIGO_POSIBLE_UNIDAD.map(codigo => (
    <MenuItem value={codigo.split(' - ')[0]}>{codigo}</MenuItem>
))

const itemsPaymentForm = [
    <MenuItem value={1}>01: Efectivo</MenuItem>,
    <MenuItem value={2}>02: Cheque nominativo</MenuItem>,
    <MenuItem value={3}>03: Transferencia electrónica de fondos</MenuItem>,
    <MenuItem value={4}>04: Tarjeta de crédito</MenuItem>,
    <MenuItem value={5}>05: Monedero electrónico</MenuItem>,
    <MenuItem value={6}>06: Dinero electrónico</MenuItem>,
    <MenuItem value={8}>08: Vales de despensa</MenuItem>,
    <MenuItem value={12}>12: Dación en pago</MenuItem>,
    <MenuItem value={13}>13: Pago por subrogación</MenuItem>,
    <MenuItem value={14}>14: Pago por consignación</MenuItem>,
    <MenuItem value={15}>15: Condonación</MenuItem>,
    <MenuItem value={17}>17: Compensación</MenuItem>,
    <MenuItem value={23}>23: Novación</MenuItem>,
    <MenuItem value={24}>24: Confusión</MenuItem>,
    <MenuItem value={25}>25: Remisión de deuda</MenuItem>,
    <MenuItem value={26}>26: Prescripción o caducidad</MenuItem>,
    <MenuItem value={27}>27: A satisfacción del acreedor</MenuItem>,
    <MenuItem value={28}>28: Tarjeta de débito</MenuItem>,
    <MenuItem value={29}>29: Tarjeta de servicios</MenuItem>,
    <MenuItem value={30}>30: Aplicación de anticipos</MenuItem>,
    <MenuItem value={99}>99: Por definir</MenuItem>
]

const itemsFactorTipo = [
    <MenuItem value="tasa">Tasa</MenuItem>,
    <MenuItem value="cuota">Cuota</MenuItem>,
    <MenuItem value="exento">Exento</MenuItem>
]

const itemsClaveImpuesto = [
    <MenuItem value="001">001: ISR</MenuItem>,
    <MenuItem value="002">002: IVA</MenuItem>,
    <MenuItem value="003">003: IEPS</MenuItem>
]

const validateLen = (str, required, extras) => {
    const objectAssociated = extras.objectAssociated
    console.log(str)
    const strLen = str.length
    console.log('str')
    console.log(str)
    console.log(strLen)
    console.log(objectAssociated.minLen)
    console.log(objectAssociated.maxLen)
    console.log('str')
    console.log('000000000000000000000')
    console.log()
    console.log('000000000000000000000')
    if (str === '') {
        if (required) {
            return "Campo requerido"
        } else {
            return null
        }
    }
    return ((strLen >= objectAssociated.minLen) && (strLen <= objectAssociated.maxLen)) ? '' : 'Longitud incorrecta'
}

const ValidateRfc = (rfc, required, extras) => {
    if (rfc === '') {
        if (required) {
            return "Campo requerido"
        } else {
            return null
        }
    }
    const re = /^([A-ZÑ&]{3,4}) ?(?:- ?)?(\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])) ?(?:- ?)?([A-Z\d]{2})([A\d])$/
    return rfc.match(re) ? '' : 'RFC con formato inválido'
}
const validateUUID = (uuid, required, extras) => {
    if (uuid === '') {
        if (required) {
            return "Campo requerido"
        } else {
            return null
        }
    }
    try {
        uuidParse(uuid)
        return ''
    } catch (err) {
        //return "UUID inválido"
        return ''
    }
}

const isInThePast = (date, required, extras) => {
    if (date === null) {
        if (required) {
            return "Campo requerido"
        } else {
            return null
        }
    }
    if (date > today) {
        return "Fecha incorrecta, fecha posterior al día de hoy"
    } else {
        return ''
    }
}

const isUptoExpDate = (date, required, extras) => {

    const expDate = extras.invoice.general.issueDate.value

    if (date === null) {
        if (required) {
            return "Campo requerido"
        } else {
            return null
        }
    }
    if (date < expDate) {
        return "Fecha incorrecta, fecha anterior a la fecha de expedición"
    } else {
        return ''
    }
}


const skeletons = {
    invoice: {
        uuid: {
            value: '',
            name: "UUID",
            error: null,
            type: "TextField",
            minLen: 32,
            maxLen: 40,
            validatorFunction: validateUUID,
        },
        serie: {
            value: '',
            name: "Serie",
            error: null,
            type: "TextField",
            minLen: 7,
            maxLen: 15,
            validatorFunction: validateLen,
        },
        csdSerie: {
            value: '',
            name: "CSD serie",
            error: null,
            type: "TextField",
            minLen: 10,
            maxLen: 21,
            validatorFunction: validateLen,
        },
        cfdiUsage: {
            value: '',
            name: "Uso de CFDI",
            error: null,
            type: "Select",
            items: itemsCfdiUsage
        },
        branchOffice: {
            value: '',
            name: "Sucursal",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 50,
            validatorFunction: validateLen
        },
    },
    general: {
        internalId: {
            value: '',
            name: "Folio",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 20,
            validatorFunction: validateLen
        },
        issuePlace: {
            value: '',
            name: "Lugar de expedición",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 200,
            validatorFunction: validateLen
        },
        issueDate: {
            value: null,
            name: "Fecha de expedición",
            error: null,
            type: "DateField",
            disableFuture: false,
            validatorFunction: isInThePast
        },
        certificationDate: {
            value: null,
            name: "Fecha de certificación",
            error: null,
            type: "DateField",
            disableFuture: true,
            validatorFunction: isInThePast
        },
        expirationDate: {
            value: null,
            name: "Fecha de vencimiento",
            error: null,
            type: "DateField",
            disableFuture: false,
            validatorFunction: isUptoExpDate
        },
        currency: {
            value: '',
            name: "Moneda",
            error: null,
            type: "Select",
            items: itemsCurrency
        },
        salesRep: {
            value: '',
            name: "Vendedor",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 200,
            validatorFunction: validateLen
        },
        paymentMethod: {
            value: '',
            name: "Metodo de pago",
            error: null,
            type: "Select",
            items: itemsPaymentMethod
        },
        paymentForm: {
            value: '',
            name: "Forma de pago",
            error: null,
            type: "Select",
            items: itemsPaymentForm
        },
        paymentConditions: {
            value: '',
            name: "Condiciones de pago",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 200,
            validatorFunction: validateLen
        },
        issuerRfc: {
            value: '',
            name: "RFC del proveedor",
            error: null,
            type: "TextField",
            minLen: 13,
            maxLen: 13,
            validatorFunction: ValidateRfc
        },
        saleOrder: {
            value: '',
            name: "Orden de venta",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 100,
            validatorFunction: validateLen
        },
        purchaseOrder: {
            value: '',
            name: "Orden de compra",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 100,
            validatorFunction: validateLen
        },
    },
    productGeneral: {
        units: {
            value: '',
            name: "Unidades",
            error: null,
            type: "Numeric",
            validatorFunction: validateLen
        },
        model: {
            value: '',
            name: "Modelo",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 80,
            validatorFunction: validateLen
        },
        supplierSku: {
            value: '',
            name: "SKU del proveedor",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 30,
            optional: true,
            validatorFunction: validateLen
        },
        unitKey: {
            value: '',
            name: "Código de unidad",
            error: null,
            type: "Select",//autocomplete
            items: codigoDeUnidadPosible(),
            validatorFunction: validateLen
        },
        satKey: {
            value: '',
            name: "Clave SAT",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 15,
            validatorFunction: validateLen
        },
        description: {
            value: '',
            name: "Descripción",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 500,
            sx: 12,
            sm: 12,
            md: 12,
            validatorFunction: validateLen
        },
        //predialNum: "",
        discountRate: {
            value: 0,
            name: "Descuento",
            error: null,
            type: "Numeric",
            validatorFunction: validateLen
        },
        unitPrice: {
            value: '',
            name: "Precio unitario",
            error: null,
            type: "Numeric",
            validatorFunction: validateLen
        },
        conceptAmount: {
            value: '',
            name: "Importe",
            error: null,
            type: "Numeric",
            disabled: true,
            validatorFunction: validateLen
        },
        upc: {
            value: '',
            name: "UPC",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 20,
            optional: true,
            validatorFunction: validateLen
        },
    },
    tax: {
        factorType: {
            value: '',
            name: "Factor de tipo",
            error: null,
            type: "Select",
            items: itemsFactorTipo
        },
        taxRate: {
            value: '',
            name: "Impuesto",
            error: null,
            type: "Numeric",
            minLen: 1,
            maxLen: 100,
            optional: true,
            validatorFunction: validateLen
        },
        taxKey: {
            value: '',
            name: "Clave del impuesto",
            error: null,
            type: "Select",
            optional: true,
            items: itemsClaveImpuesto
        },
    },
    motion: {
        motion: {
            value: '',
            name: "Pedimento",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 100,
            validatorFunction: validateLen
        },
        motionDate: {
            value: null,
            name: "Fecha de pedimento",
            error: null,
            type: "DateField",
            optional: true,
            validatorFunction: isInThePast
        },
        customs: {
            value: '',
            name: "Anotaciones",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 100,
            optional: true,
            validatorFunction: validateLen
        },
    },
    serie: {
        serie: {
            value: '',
            name: "Serie",
            error: null,
            type: "TextField",
            minLen: 1,
            maxLen: 100,
            validatorFunction: validateLen
        }
    }
}

const eng_spa = (cadena) => {
let new_cadena = cadena
Object.values(skeletons).forEach((skeleton_part) => {
    Object.keys(skeleton_part).forEach((key) =>{
        new_cadena = new_cadena.replace(key, skeleton_part[key].name)
    })
})
return new_cadena
}



const createProductComponent = (componentName) => {
    const component = {}
    Object.keys(skeletons[componentName]).forEach((key) => {
        component[key] = { ...skeletons[componentName][key]}
    })
    return component
}


const createNewProduct = () => {
    const product = {
        general: {},
        taxes: [],
        motions: [],
        series: []
    }
    Object.keys(skeletons.productGeneral).forEach((key) => {
        product.general = { ...product.general, [key]: { ...skeletons.productGeneral[key] } }
    })
    product.taxes = [createProductComponent('tax'),]
    return product
}

const emptyEntryDocument = () => {
    const formatted = {
        invoice: {},
        general: {},
        productsInEntryDocument: []
    }
    Object.keys(skeletons.invoice).forEach((key) => {
        formatted.invoice = { ...formatted.invoice, [key]: { ...skeletons.invoice[key] } }
    })
    Object.keys(skeletons.general).forEach((key) => {
        formatted.general = { ...formatted.general, [key]: { ...skeletons.general[key] } }
    })
    formatted.productsInEntryDocument = [createNewProduct(),]
    return [formatted, true]
}


const formatEntryDocument = (entryDocument) => {
    console.log('entryDocument-...............................................')
    console.log(entryDocument)
    console.log('entryDocument-...............................................')
    const formatted = {
        invoice: {},
        general: {},
        productsInEntryDocument: []
    }
    Object.keys(skeletons.invoice).forEach((key) => {
        formatted.invoice = { ...formatted.invoice, [key]: entryDocument.invoice ? { ...skeletons.invoice[key], value: entryDocument.invoice[key] } : { ...skeletons.invoice[key] } }
    })
    Object.keys(skeletons.general).forEach((key) => {
        formatted.general = { ...formatted.general, [key]: { ...skeletons.general[key], value: entryDocument[key] } }
    })

    entryDocument.productsInEntryDocument.forEach((productAux) => {
        const product = {
            general: {},
            taxes: [],
            motions: [],
            series: []
        }
        Object.keys(skeletons.productGeneral).forEach((key) => {
            product.general = { ...product.general, [key]: { ...skeletons.productGeneral[key], value: productAux[key] } }
        })
        productAux.productInEntryDocumentTaxes.forEach((taxAux) => {
            const tax = {}
            Object.keys(skeletons.tax).forEach((key) => {
                tax[key] = { ...skeletons.tax[key], value: taxAux[key] }
            })
            product.taxes = [...product.taxes, tax]
        })
        productAux.productInEntryDocumentMotions.forEach((motionAux) => {
            const motion = {}
            Object.keys(skeletons.motion).forEach((key) => {
                motion[key] = { ...skeletons.motion[key], value: motionAux[key] }
            })
            product.motions = [...product.motions, motion]
        })
        productAux.productInEntryDocumentSeries.forEach((serieAux) => {
            const serie = {}
            Object.keys(skeletons.serie).forEach((key) => {
                serie[key] = { ...skeletons.serie[key], value: serieAux[key] }
            })
            product.series = [...product.series, serie]
        })
        formatted.productsInEntryDocument = [...formatted.productsInEntryDocument, product]
    })
    console.log('00000i9999999999999999999999999999999999999999999999999999')
    console.log(formatted)
    console.log('00000i9999999999999999999999999999999999999999999999999999')
    return [formatted, Boolean(entryDocument.invoice)]
}


const FacturaWrapper = () => {
    const entryDocument = useSelector((store) => store.facturas.entryDocumentIndividual);
    const { idFactura } = useParams();

    const dispatch = useDispatch();
    React.useEffect(() => {
        if (idFactura) {
            const fetchData = () => {
                dispatch(obtenerFacturaUsandoId(idFactura));
            };
            fetchData();
        }
    }, []);

    const IdNotFound = () => {
        return (
            <div className={styles.divContainer}>
                <h3 className={styles.h3Style}>
                    Id de documento de ingreso no encontrado
                </h3>
                <div className={styles.divContainerBtnP}>
                    <p style={{ margin: "0" }}>Consultar las facturas 👉 </p>
                    <Button
                        size="small"
                        className={styles.btn}
                        variant="contained"
                        color="buttonGreenPink"
                        sx={{ ml: 1 }}
                        onClick={() => {
                            window.location.href = "facturas/consultar";
                        }}
                    >
                        Consultar
                    </Button>
                </div>
            </div>
        );
    };
    console.log('93243')
    if (idFactura) {
        if (entryDocument) {
            if (entryDocument.status === 200) {
                const [entryDocumentFormatted, isInvoice] = formatEntryDocument(entryDocument.info)
                console.log('9999999999999mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmccccccccccccccccc')
                console.log(entryDocument.info)
                console.log('9999999999999mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmccccccccccccccccc')
                return <FormularioEntradaManual
                        entryDocumentProp={entryDocumentFormatted}
                        isInvoiceProp={isInvoice}
                        createNewProduct={createNewProduct}
                        createProductComponent={createProductComponent}
                        idEntryDocument={entryDocument.info.id}
                        eng_spa={eng_spa}
                        />
            } else {
                return <IdNotFound />
            }
        } else {
            return <CargandoLista />
        }
    }else{
        const [entryDocumentFormatted, isInvoice] = emptyEntryDocument()
        
        return <FormularioEntradaManual 
                entryDocumentProp={entryDocumentFormatted} 
                isInvoiceProp={isInvoice} 
                createNewProduct={createNewProduct} 
                createProductComponent={createProductComponent}
                idEntryDocument={null}
                eng_spa={eng_spa}
                />
    }
    
};

export default FacturaWrapper;
