import React from 'react'

import { Grid, Button, Typography } from '@mui/material';
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";

const HeaderForProductInfo = ({
    infoName,
    handleFunction,
    index,
    mensajeAgregar
}) => {
    return (
        <Grid container spacing={2}
            display="flex"
            flexDirection="row"
            sx={{ justifyContent: "center", alignItems: "center", my: 2, width:"100% !important" }}
        >

            <Grid item xss={5} sm={6} md={6} sx={{ padding: "16px", margin: 0 }} >
                <Typography variant="h5" sx={{ marginBottom: "1%" }}>{infoName}</Typography>
            </Grid>

            <Grid item xss={5} sm={6} md={3} sx={{ padding: "16px", margin: 0 }} >
                <Button
                    title={mensajeAgregar}
                    sx={{ padding: "16px", margin: 0, height: "10px" }}
                    variant="contained"
                    size="small"
                    color="buttonGreen"
                    onClick={() => handleFunction(index)}
                >
                    <AddCircleOutlineIcon />
                </Button>
            </Grid>
           
        </Grid>
    )
}

export default HeaderForProductInfo