import React, { useEffect } from 'react'
import { Grid, TextField, Typography, Alert, useMediaQuery, Box } from '@mui/material';
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import Button from "@mui/material/Button";
import HeaderForProductInfo from './HeaderForProductInfo';
import PartOfProductOneRequired from './PartOfProductOneRequired';
import { createObjectFromInside, numberOrCero, traversePath } from '../../../Utils/generalFunctions';



const ProductInEntryDocument = ({ product, index, TextFieldCustom, SelectCustom, NumericCustom, DateFieldCustom, invoice, setInvoice, createProductComponent }) => {
    const path = ["productsInEntryDocument", index, 'general']

    const isSmallScreen = useMediaQuery('(max-width:700px)');

    const PartOfProduct = ({ product, index, name, spanishName }) => {
        //return(<div>dsfsee</div>)
        const listPart = product[name]

        return (

            <Grid item container xss={12} sm={12} md={12} spacing={2} sx={{
                justifyContent: "center", width: "90% !important", margin: "auto", gap: {
                    xss: 2,
                    md: 0
                }
            }}>
                {listPart.map((part, index2) => {
                    const path = ["productsInEntryDocument", index, name, index2];
                    const fieldPart = traversePath(["productsInEntryDocument", index, name, index2], invoice);

                    return (
                        <Grid
                            container
                            spacing={2}
                            justifyContent="center"
                            alignItems="center"
                            sx={(theme) => ({
                                marginTop: index2 === 0 ? 0 : 2,
                                backgroundColor: 'transparent',
                                [theme.breakpoints.down('sm')]: {
                                    backgroundColor: theme.palette.background.paper,
                                },
                                padding: "0px 15px 10px 2px",
                                borderRadius: "16px",
                                margin: "auto",
                                flexWrap: {
                                    xss: "wrap",
                                    sm: "nowrap",
                                    md: "nowrap"
                                }
                            })}
                        >

                            {/* Título e ícono en pantallas pequeñas */}
                            {isSmallScreen && (
                                <Grid item xss={12} container alignItems="center" justifyContent="space-between">
                                    <Typography variant="h6">Elemento {index2 + 1}</Typography>

                                    <Button
                                        title={`Eliminar ${spanishName}`}
                                        sx={{ padding: '4px' }}
                                        variant="contained"
                                        size="small"
                                        color="error"
                                        onClick={() => handleDeleteElementProduct(index, name, index2)}
                                    >
                                        <RemoveCircleOutlineIcon />
                                    </Button>

                                </Grid>
                            )}

                            {/* Campos del objeto */}
                            <Grid item xss={12} sm={12} md={12} container spacing={2}>
                                {Object.keys(part).map((key) => (
                                    <Grid item xss={12} sm={name === "series" ? 12 : 4} md={name === "series" ? 12 : 4} key={key}>
                                        {fieldPart[key].type === "TextField"
                                            ? TextFieldCustom(path, key)
                                            : fieldPart[key].type === "Select"
                                                ? SelectCustom(path, key)
                                                : fieldPart[key].type === "Numeric"
                                                    ? NumericCustom(path, key)
                                                    : DateFieldCustom(path, key)}
                                    </Grid>
                                ))}
                            </Grid>

                            {/* Botón eliminar para pantallas grandes */}
                            {!isSmallScreen && (
                                <Grid item xss={0} sm={1} md={1}>

                                    <Button
                                        title={`Eliminar ${spanishName}`}
                                        sx={{ padding: "16px", margin: 0, height: "10px" }}
                                        variant="contained"
                                        size="small"
                                        color="error"
                                        onClick={() => handleDeleteElementProduct(index, name, index2)}
                                    >
                                        <RemoveCircleOutlineIcon />
                                    </Button>

                                </Grid>
                            )}
                        </Grid>
                    );
                })}
            </Grid>

        )
    }

    const handleAddMotion = (index) => {
        const newForms = invoice.productsInEntryDocument.map((form, i) => {
            if (index === i) {
                return {
                    ...form,
                    motions: [...form.motions, createProductComponent('motion')],
                };
            }
            return form;
        });
        setInvoice({ ...invoice, productsInEntryDocument: newForms });
    }


    const handleAddTax = (index) => {
        const newForms = invoice.productsInEntryDocument.map((form, i) => {
            if (index === i) {
                return {
                    ...form,
                    taxes: [...form.taxes, createProductComponent('tax')],
                };
            }
            return form;
        });
        setInvoice({ ...invoice, productsInEntryDocument: newForms });


    }



    const handleAddSerie = (index) => {
        const newForms = invoice.productsInEntryDocument.map((form, i) => {
            if (index === i) {
                return {
                    ...form,
                    series: [...form.series, createProductComponent('serie')],
                };
            }
            return form;
        }
        );
        setInvoice({ ...invoice, productsInEntryDocument: newForms });
    }

    const handleDeleteForm = (index) => {
        const newForms = invoice.productsInEntryDocument.filter((_, i) => i !== index);
        setInvoice({ ...invoice, productsInEntryDocument: newForms });
    }

    const handleDeleteElementProduct = (indexProduct, element, indexElement) => {
        const newForms = invoice.productsInEntryDocument.map((form, i) => {
            if (indexProduct === i) {
                return {
                    ...form,
                    [element]: form[element].filter((_, j) => j !== indexElement),
                };
            }
            return form;
        }
        );
        setInvoice({ ...invoice, productsInEntryDocument: newForms });
    }

    useEffect(() => {
        const subtotal = numberOrCero(product.general.units.value) * numberOrCero(product.general.unitPrice.value)
        const totalDiscount = subtotal * numberOrCero(product.general.discountRate.value / 100)

        console.log('eeeeeeeeeeeee')
        const fieldAux = {
            value: (subtotal - totalDiscount),
            name: "Importe",
            error: null,
            type: "Numeric",
            disabled: true,

        }
        setInvoice(invoice => createObjectFromInside(path, "conceptAmount", invoice, fieldAux))
    }, [product.general.units, product.general.discountRate, product.general.unitPrice]);//

    return (
        <Grid container spacing={2}
            sx={{
                justifyContent: "center", alignItems: "center", borderBottom: 1, marginBottom: 2,
                width: { xss: "83.33%", sm: "83.33%", md: "100%" }
            }}
        >

            <Grid item container xs={12} sm={12} md={12} spacing={2} sx={{ justifyContent: "center" }}>
                {Object.keys(product.general).map((key) => {

                    const fxs = product.general[key].sx ? product.general[key].sx : 2
                    const fsm = product.general[key].sm ? product.general[key].sm : 2
                    const fmd = product.general[key].md ? product.general[key].md : 2

                    return (
                        <Grid item xss={12} sm={6} md={3} sx={{ justifyContent: "center" }}>
                            {product.general[key].type === "TextField" ?
                                TextFieldCustom(path, key) :
                                product.general[key].type === "Select" ? SelectCustom(path, key) :
                                    product.general[key].type === "Numeric" ? NumericCustom(path, key) :
                                        DateFieldCustom(path, key)}
                        </Grid>
                    )
                })}
            </Grid>

            <Box sx={{ display: "flex", flexDirection: "row", width: "100%", justifyContent: "center", alignItems: "center", flexDirection:"column" }}>

                <Grid xss={12} sm={12} md={8} spacing={2} sx={{ justifyContent: "center", mt: 4, width: "100%", alignItems: "center" }}>
                    <HeaderForProductInfo infoName='Impuestos' handleFunction={handleAddTax} index={index} mensajeAgregar='Agregar impuesto' />
                    {/*{PartOfProductOneRequired({ product: product, index: index, name: 'taxes' })}*/}
                    <PartOfProductOneRequired
                        product={product}
                        index={index}
                        name='taxes'
                        invoice={invoice}
                        setInvoice={setInvoice}
                        handleDeleteElementProduct={handleDeleteElementProduct}
                    />

                </Grid>

                <Grid item xss={12} sm={12} md={8} spacing={2} sx={{ justifyContent: "center", width: {
                    xss: "100%",
                    sm: "100%",
                    md: "80%"
                } }}>
                    <HeaderForProductInfo infoName='Pedimentos' handleFunction={handleAddMotion} index={index} mensajeAgregar='Agregar pedimento' />
                    {PartOfProduct({ product: product, index: index, name: 'motions' })}
                </Grid>

                <Grid xss={12} sm={12} md={8} spacing={2} sx={{ justifyContent: "center", width: "100%", alignItems: "center" }}>

                    <HeaderForProductInfo infoName='Series' handleFunction={handleAddSerie} index={index} mensajeAgregar='Agregar serie' />
                    {PartOfProduct({ product: product, index: index, name: 'series' })}

                </Grid>
            </Box>
            <Grid item xs={12} sm={12} md={12} >
                {invoice.productsInEntryDocument?.length > 1 && (
                    <Button
                        sx={{ padding: "16px", margin: "10px" }}
                        fullWidth
                        variant="contained"
                        color="error"
                        startIcon={<RemoveCircleOutlineIcon />}
                        onClick={() => handleDeleteForm(index)}
                    >
                        Delete
                    </Button>
                )}
            </Grid>

        </Grid>
    )
}

export default ProductInEntryDocument