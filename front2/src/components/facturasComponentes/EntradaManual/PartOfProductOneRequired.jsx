import React from 'react'
import { Grid, Typography, useMediaQuery } from '@mui/material'
import { createObjectFromInside, traversePath } from "../../../Utils/generalFunctions"
import { NumericFormat } from 'react-number-format';
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { Select } from "@mui/material";
import FormHelperText from '@mui/material/FormHelperText';
import TextField from "@mui/material/TextField";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import Box from "@mui/material/Box";

//const PartOfProductOneRequired = ({ product, index, name, spanishName, invoice, TextFieldCustom, SelectCustom, NumericCustom, DateFieldCustom }) => {

const itemsClaveImpuesto = [
    <MenuItem value="001">001: ISR</MenuItem>,
    <MenuItem value="002">002: IVA</MenuItem>,
    <MenuItem value="003">003: IEPS</MenuItem>
]
const PartOfProductOneRequired = ({ product, index, name, spanishName, invoice, setInvoice, handleDeleteElementProduct }) => {
    const changeFactorType = (factorTypeValue, pathP, fieldAuxP, index2) => {
        console.log('00000000000---------------')
        console.log(factorTypeValue)
        console.log(pathP)
        console.log(fieldAuxP)
        console.log('0000000000---------')

        if (factorTypeValue === "exento") {
            //const path = pathP.slice(0, pathP.length - 1)

            const fieldAux = {
                factorType: fieldAuxP,
                taxRate: {
                    value: 0,
                    name: "Impuesto",
                    error: null,
                    type: "Numeric",
                    optional: true,
                },
                taxKey: {
                    value: '',
                    name: "Clave del impuesto",
                    error: null,
                    type: "Select",
                    optional: true,
                    items: itemsClaveImpuesto
                }
            }
            setInvoice(invoice => createObjectFromInside(pathP, index2, invoice, fieldAux))

        } else {
            //const path = pathP.slice(0, pathP.length - 1)

            const fieldAux = {
                factorType: fieldAuxP,
                taxRate: {
                    value: 0,
                    name: "Impuesto",
                    error: null,
                    type: "Numeric",
                    optional: false,
                },
                taxKey: {
                    value: '',
                    name: "Clave del impuesto",
                    error: null,
                    type: "Select",
                    optional: false,
                    items: itemsClaveImpuesto
                }
            }

            setInvoice(invoice => createObjectFromInside(pathP, index2, invoice, fieldAux))
        }

    }
    //return(<div>dsfsee</div>)
    const listPart = product[name]
    //console.log('99999999999999999999999999999999999999999999999999999||||||||||||||||||||||||||||||||||||||||||')
    //console.log(listPart)
    //console.log('99999999999999999999999999999999999999999999999999999|||||||||||||||||||||||||||||||||||||||')

    const isSmallScreen = useMediaQuery('(max-width:700px)');

    console.log('isSmallScreen')

    return (
        <Grid item container xss={12} sm={12} md={12} spacing={2} sx={{
            justifyContent: "center", width: "90% !important", margin: "auto", gap: {
                xss: 2,
                md: 0
            }
        }}>
            {listPart.map((part, index2) => {
                const path = ["productsInEntryDocument", index, name]

                return (
                    <Grid
                        container
                        spacing={2}
                        justifyContent="center"
                        alignItems="center"
                        sx={(theme) => ({
                            marginTop: index === 0 ? 0 : 2,
                            backgroundColor: 'transparent',
                            [theme.breakpoints.down('sm')]: {
                                backgroundColor: theme.palette.background.paper,
                            },
                            padding: "0px 15px 10px 2px",
                            borderRadius: "16px",
                            margin: "auto"
                        })}
                    >
                        {/* Título e ícono en pantallas pequeñas */}
                        {isSmallScreen && (
                            <Grid item xss={12} container alignItems="center" justifyContent="space-between">
                                {isSmallScreen && (
                                    <Typography variant="h6">Impuesto {index2 + 1}</Typography>
                                )}
                                {isSmallScreen && index2 !== 0 && (
                                    <Button
                                        title={`Eliminar ${spanishName}`}
                                        sx={{ padding: '4px' }}
                                        variant="contained"
                                        size="small"
                                        color="error"
                                        onClick={() => handleDeleteElementProduct(index, name, index2)}
                                    >
                                        <RemoveCircleOutlineIcon />
                                    </Button>
                                )}
                            </Grid>
                        )}


                        {/* Contenido del impuesto */}
                        <Grid item xss={12} sm={11} md={11} container spacing={2}>
                            {/* Factor de tipo */}
                            <Grid item xss={12} sm={4} md={4}>
                                <FormControl fullWidth error={!!part.factorType.error}>
                                    <InputLabel id={`factor-type-label-${index2}`}>{part.factorType.name}</InputLabel>
                                    <Select
                                        labelId={`factor-type-label-${index2}`}
                                        id={`factor-type-select-${index2}`}
                                        label={part.factorType.name}
                                        required
                                        value={part.factorType.value}
                                        color={
                                            part.factorType.error === null
                                                ? undefined
                                                : part.factorType.error === ''
                                                    ? 'success'
                                                    : 'error'
                                        }
                                        onChange={(e) => {
                                            const value = e.target.value;
                                            const fieldAux = {
                                                ...part.factorType,
                                                value,
                                                error: '',
                                            };
                                            changeFactorType(value, path, fieldAux, index2);
                                        }}
                                    >
                                        {part.factorType.items}
                                    </Select>
                                    <FormHelperText>{part.factorType.error}</FormHelperText>
                                </FormControl>
                            </Grid>

                            {/* Impuesto */}
                            <Grid item xss={12} sm={4} md={4}>
                                <NumericFormat
                                    allowLeadingZeros={false}
                                    thousandSeparator=","
                                    customInput={TextField}
                                    disabled={part.taxRate.optional}
                                    label={part.taxRate.name}
                                    fullWidth
                                    required
                                    value={part.taxRate.value}
                                    onValueChange={(values) => {
                                        const fieldAux = {
                                            ...part.taxRate,
                                            value: values.value,
                                            error: '',
                                        };
                                        setInvoice((invoice) =>
                                            createObjectFromInside([...path, index2], 'taxRate', invoice, fieldAux)
                                        );
                                    }}
                                    error={!!part.taxRate.error}
                                    helperText={part.taxRate.error}
                                />
                            </Grid>

                            {/* Clave del impuesto */}
                            <Grid item xss={12} sm={4} md={4}>
                                <FormControl fullWidth error={!!part.taxKey.error}>
                                    <InputLabel id={`tax-key-label-${index2}`}>{part.taxKey.name}</InputLabel>
                                    <Select
                                        labelId={`tax-key-label-${index2}`}
                                        id={`tax-key-select-${index2}`}
                                        label={part.taxKey.name}
                                        required
                                        disabled={part.taxKey.optional}
                                        value={part.taxKey.value}
                                        color={
                                            part.taxKey.error === null
                                                ? undefined
                                                : part.taxKey.error === ''
                                                    ? 'success'
                                                    : 'error'
                                        }
                                        onChange={(e) => {
                                            const value = e.target.value;
                                            const fieldAux = {
                                                ...part.taxKey,
                                                value,
                                                error: '',
                                            };
                                            setInvoice((invoice) =>
                                                createObjectFromInside([...path, index2], 'taxKey', invoice, fieldAux)
                                            );
                                        }}
                                    >
                                        {part.taxKey.items}
                                    </Select>
                                    <FormHelperText>{part.taxKey.error}</FormHelperText>
                                </FormControl>
                            </Grid>
                        </Grid>

                        {/* Botón eliminar solo en pantallas medianas o grandes */}
                        {!isSmallScreen && (
                            <Grid xss={0} sm={1} md={1} >
                                {index2 !== 0 ? (
                                    <Button
                                        title={`Eliminar ${spanishName}`}
                                        sx={{ padding: '16px', margin: 0, height: '10px' }}
                                        variant="contained"
                                        size="small"
                                        color="error"
                                        onClick={() => handleDeleteElementProduct(index, name, index2)}
                                    >
                                        <RemoveCircleOutlineIcon />
                                    </Button>
                                ) : (
                                    <Box sx={{ width: 40, height: 40 }} />
                                )}
                            </Grid>
                        )}
                    </Grid>


                )
            }
            )}

        </Grid>
    )
}

export default PartOfProductOneRequired