import React from 'react'
import ProductInEntryDocument from './ProductInEntryDocument';
import { Box } from '@mui/material';


const ProductsInDocumentFormulario = (props) => {

    const { invoice, setInvoice, DateFieldCustom, SelectCustom, TextFieldCustom, NumericCustom, createProductComponent } = props;    
    return (
        <Box sx={{ width: "100%", display: "flex", flexDirection: "column", alignItems: "center" }}>
            {invoice.productsInEntryDocument?.map((product, index) => (
                <ProductInEntryDocument
                    product={product}
                    index={index}
                    TextFieldCustom={TextFieldCustom}
                    SelectCustom={SelectCustom}
                    NumericCustom={NumericCustom}
                    DateFieldCustom={DateFieldCustom}
                    invoice={invoice} 
                    setInvoice={setInvoice}
                    createProductComponent={createProductComponent}
                />
                
            ))}
        </Box>

    )

}

export default ProductsInDocumentFormulario
