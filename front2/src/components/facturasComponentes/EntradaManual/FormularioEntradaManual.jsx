import React, { useContext } from "react";
import TextField from "@mui/material/TextField";
import Alert from "@mui/material/Alert";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Select,
  useMediaQuery,
} from "@mui/material";
import { useCookies } from "react-cookie";
import { useState } from "react";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import Fab from "@mui/material/Fab";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import Button from "@mui/material/Button";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import { useDispatch, useSelector } from "react-redux";
import {
  darAltaFactura,
  actualizarFactura,
  setMensajeAlert,
} from "../../../redux/facturasDucks";
import { useEffect } from "react";
import { AccesDenied } from "../Accesdenied";
import FacturaFormulario from "./FacturaFormulario";
import ProductsInDocumentFormulario from "./ProductsInDocumentFormulario";
import "./FormularioEntradaManual.css";
import FormHelperText from "@mui/material/FormHelperText";
import { NumericFormat } from "react-number-format";
import {
  createObjectFromInside,
  numberOrCero,
  traversePath,
} from "../../../Utils/generalFunctions";

import {
  useNavigate,
  useLocation,
  UNSAFE_NavigationContext as NavigationContext,
} from "react-router-dom";
import GoBackButton from "../../componentesGenerales/GoBackButton";
import { Divider } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import MaterialUISwitch from "./SwitchComponent";
import { FormGroup, FormControlLabel, Stack } from "@mui/material";
import CustomDialog from '../../componentesGenerales/CustomDialog';

function useBlocker(blocker, when = true) {
  const { navigator } = useContext(NavigationContext);

  useEffect(() => {
    if (!when) return;

    const push = navigator.push;

    navigator.push = (...args) => {
      blocker({ action: "PUSH", location: args[0] });
    };

    return () => {
      navigator.push = push;
    };
  }, [blocker, when, navigator]);
}

function App({
  entryDocumentProp,
  isInvoiceProp,
  createNewProduct,
  createProductComponent,
  idEntryDocument,
  eng_spa,
}) {
  const [showOptionalFieldsInvoice, setShowOPtionalFieldsInvoice] =
    React.useState({
      branchOffice: { name: "Sucursal", checked: false, scope: "invoice" },

      saleOrder: { name: "Orden de venta", checked: false, scope: "general" },
      purchaseOrder: {
        name: "Orden de compra",
        checked: false,
        scope: "general",
      },
      salesRep: { name: "Vendedor", checked: false, scope: "general" },
      paymentConditions: {
        name: "Condiciones de pago",
        checked: false,
        scope: "general",
      },
    });

  const [showOptionalFieldsNote, setShowOPtionalFieldsNote] = React.useState({
    saleOrder: { name: "Orden de venta", checked: false, scope: "general" },
    purchaseOrder: {
      name: "Orden de compra",
      checked: false,
      scope: "general",
    },
    salesRep: { name: "Vendedor", checked: false, scope: "general" },
    paymentConditions: {
      name: "Condiciones de pago",
      checked: false,
      scope: "general",
    },

    certificationDate: {
      name: "Fecha de Certificación",
      checked: false,
      scope: "general",
    },
    expirationDate: {
      name: "Fecha de Vencimiento",
      checked: false,
      scope: "general",
    },
    issuerRfc: { name: "RFC Proveedor", checked: false, scope: "general" },
  });
  const [cookies, setCookie] = useCookies();
  const dispatch = useDispatch();
  const [subTotal, setSubTotal] = useState(0);
  const [discount, setDiscount] = useState(0);
  const [tax, setTax] = useState(0);
  const [total, setTotal] = useState(0);
  const [invoice, setInvoice] = useState(entryDocumentProp);
  const [isInvoice, setIsInvoice] = useState(isInvoiceProp);
  const showOptionalFields = isInvoice
    ? showOptionalFieldsInvoice
    : showOptionalFieldsNote;

  const DateFieldCustom = (path, key) => {
    const objectAssociated = traversePath(path, invoice);
    const isRequired =
      objectAssociated[key].optional !== true && !(key in showOptionalFields);
    return (
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DesktopDatePicker
          label={objectAssociated[key].name}
          format="MM/dd/yyyy"
          value={objectAssociated[key].value}
          disableFuture={objectAssociated[key].disableFuture}
          //value={objectAssociated[key].value}

          //color={"error"}
          onChange={(e) => {
            const fieldAux = {
              ...objectAssociated[key],
              value: e,
              error: objectAssociated[key].validatorFunction(e, isRequired, {
                invoice: invoice,
              }),
            };
            setInvoice((invoice) =>
              createObjectFromInside(path, key, invoice, fieldAux)
            );
          }}
          renderInput={(params) => (
            <TextField
              error
              required={!(key in showOptionalFields)}
              {...params}
              color={
                objectAssociated[key].error === null
                  ? null
                  : objectAssociated[key].error === ""
                    ? "success"
                    : "error"
              }
              fullWidth
              helperText={objectAssociated[key].error}
            />
          )}
        />
      </LocalizationProvider>
    );
  };

  const NumericCustom = (path, key) => {
    const objectAssociated = traversePath(path, invoice);
    const isRequired =
      objectAssociated[key].optional !== true && !(key in showOptionalFields);
    return (
      <NumericFormat
        allowLeadingZeros={false}
        thousandSeparator=","
        customInput={TextField}
        disabled={objectAssociated[key].disabled}
        label={objectAssociated[key].name}
        fullWidth
        required={isRequired}
        value={objectAssociated[key].value}
        onValueChange={(values, sourceInfo) => {
          const fieldAux = {
            ...objectAssociated[key],
            value: values.value,
            error: "",
          };
          setInvoice((invoice) =>
            createObjectFromInside(path, key, invoice, fieldAux)
          );
        }}
        error={objectAssociated[key].error}
      />
    );
  };

  const SelectCustom = (path, key) => {
    const objectAssociated = traversePath(path, invoice);
    const isRequired =
      objectAssociated[key].optional !== true && !(key in showOptionalFields);
    return (
      <FormControl
        fullWidth
        required={isRequired}
        error={objectAssociated[key].error}
      >
        <InputLabel id="cfdi-usage-label">
          {objectAssociated[key].name}
        </InputLabel>
        <Select
          labelId="cfdi-usage-label"
          id="cfdi-usage-select"
          label={objectAssociated[key].name}
          value={objectAssociated[key].value}
          color={
            objectAssociated[key].error === null
              ? null
              : objectAssociated[key].error === ""
                ? "success"
                : "error"
          }
          onChange={(e) => {
            const fieldAux = {
              ...objectAssociated[key],
              value: e.target.value,
              error: "",
            };
            setInvoice((invoice) =>
              createObjectFromInside(path, key, invoice, fieldAux)
            );
          }}
          error={objectAssociated[key].error}
        >
          {objectAssociated[key].items}
        </Select>
        <FormHelperText error>{objectAssociated[key].error}</FormHelperText>
      </FormControl>
    );
  };

  const TextFieldCustom = (path, key) => {
    const objectAssociated = traversePath(path, invoice);
    const isRequired =
      objectAssociated[key].optional !== true && !(key in showOptionalFields);
    return (
      <TextField
        label={objectAssociated[key].name}
        fullWidth
        required={isRequired}
        value={objectAssociated[key].value}
        slotProps={{ htmlInput: { maxLength: objectAssociated[key].maxLen } }}
        onChange={(e) => {
          const fieldAux = {
            ...objectAssociated[key],
            value: e.target.value,
            error: objectAssociated[key].validatorFunction(
              e.target.value,
              isRequired,
              { objectAssociated: objectAssociated[key] }
            ),
          };
          //console.log('mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm')
          //console.log('----')
          //console.log(objectAssociated[key])
          //console.log('----')
          //console.log(invoice)
          //objectAssociated[key] = fieldAux
          //console.log('----')
          //console.log(objectAssociated[key])
          //console.log('----')
          //console.log(createObjectFromInside())
          //console.log('wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww')

          setInvoice((invoice) =>
            createObjectFromInside(path, key, invoice, fieldAux)
          );
        }}
        color={
          objectAssociated[key].error === null
            ? null
            : objectAssociated[key].error === ""
              ? "success"
              : "error"
        }
        error={objectAssociated[key].error}
        helperText={objectAssociated[key].error}
      />
    );
  };

  const user = useSelector((state) => state.usuario.info);

  useEffect(() => {
    let subTotal = 0;
    let discount = 0;
    let tax = 0;
    let taxes = 0;
    let total = 0;

    const newProductsInEntryDocument = [];
    invoice.productsInEntryDocument.forEach((product) => {
      const subTotalProducto =
        numberOrCero(product.general.units.value) *
        numberOrCero(product.general.unitPrice.value);
      const discountProducto =
        subTotalProducto *
        (numberOrCero(product.general.discountRate.value) / 100);
      const conceptAmountProducto = subTotalProducto - discountProducto;
      subTotal += subTotalProducto;
      discount += discountProducto;
      product.taxes.forEach((tax) => {
        let calculatedTax;
        if (tax.factorType.value === "tasa") {
          calculatedTax =
            conceptAmountProducto * (numberOrCero(tax.taxRate.value) / 100);
        } else if (tax.factorType.value === "cuota") {
          calculatedTax = numberOrCero(tax.taxRate.value);
        } else if (tax.factorType.value === "exento") {
          calculatedTax = 0;
        } else {
          calculatedTax = 0;
        }
        taxes += calculatedTax;
      });
      newProductsInEntryDocument.push(product);
      tax = taxes;
      total = subTotal - discount + tax;
    });
    //

    //
    // Limitar a 2 decimales
    subTotal = parseFloat(subTotal.toFixed(2));
    discount = parseFloat(discount.toFixed(2));
    tax = parseFloat(tax.toFixed(2));
    taxes = parseFloat(taxes.toFixed(2));
    total = parseFloat(total.toFixed(2));

    setSubTotal(subTotal);
    setDiscount(discount);
    setTax(tax);
    setTotal(total);
  }, [invoice.productsInEntryDocument]); //invoice.productsInEntryDocument

  const handleAddProduct = () => {
    setInvoice({
      ...invoice,
      productsInEntryDocument: [
        ...invoice.productsInEntryDocument,
        { ...createNewProduct() },
      ],
    });
  };
  //state
  const validateFieldsInLevel = (
    old_invoice,
    new_invoice,
    data_to_send,
    readyForSend
  ) => {
    Object.keys(old_invoice).forEach((key) => {
      validatedField(old_invoice, new_invoice, data_to_send, key, readyForSend);
    });
  };

  const validatedField = (
    old_field,
    new_invoice,
    data_to_send,
    key,
    readyForSend
  ) => {
    const isRequired =
      old_field[key].optional !== true && !(key in showOptionalFields);
    if (old_field[key].value === "" || old_field[key].value === null) {
      if (isRequired) {
        new_invoice[key] = {
          ...old_field[key],
          error: "Campo requerido",
          value: "",
        };
        readyForSend.r = false;
      } else {
        data_to_send[key] = null;
        new_invoice[key] = { ...old_field[key] };
        //new_invoice = {new_invoice, [key]: {...old_field[key] }}
      }
    } else {
      new_invoice[key] = { ...old_field[key] };
      if (old_field[key].error) {
        readyForSend.r = false;
      } else {
        data_to_send[key] = old_field[key].value;
      }
    }
  };

  const handleSendInvoice = () => {
    const readyForSend = { r: true };
    const data_to_send = {};
    setInvoice((invoice) => {
      const newInvoice = {};
      newInvoice["invoice"] = {};
      //invoice
      if (isInvoice) {
        data_to_send["invoice"] = {};
        validateFieldsInLevel(
          invoice["invoice"],
          newInvoice["invoice"],
          data_to_send["invoice"],
          readyForSend
        );
      } else {
        data_to_send["invoice"] = null;
        Object.keys(invoice["invoice"]).forEach((key) => {
          newInvoice["invoice"][key] = { ...invoice["invoice"][key] };
          //newInvoice['invoice'] = { ...newInvoice['invoice'], [key]: {...invoice['invoice'][key]} }
        });
      }

      //general
      data_to_send["general"] = {};
      newInvoice["general"] = {};
      validateFieldsInLevel(
        invoice["general"],
        newInvoice["general"],
        data_to_send["general"],
        readyForSend
      );
      data_to_send["productsInEntryDocument"] = [];
      newInvoice["productsInEntryDocument"] = [];
      invoice.productsInEntryDocument.forEach((product) => {
        const newInvoice_product_aux = {
          general: {},
          taxes: [],
          motions: [],
          series: [],
        };
        const data_to_send_product_aux = {
          general: {},
          taxes: [],
          motions: [],
          series: [],
        };

        validateFieldsInLevel(
          product["general"],
          newInvoice_product_aux["general"],
          data_to_send_product_aux["general"],
          readyForSend
        );

        product.taxes.forEach((tax) => {
          const newInvoice_tax_aux = {};
          const data_to_send_tax_aux = {};
          validateFieldsInLevel(
            tax,
            newInvoice_tax_aux,
            data_to_send_tax_aux,
            readyForSend
          );
          data_to_send_product_aux["taxes"] = [
            ...data_to_send_product_aux["taxes"],
            data_to_send_tax_aux,
          ];
          newInvoice_product_aux["taxes"] = [
            ...newInvoice_product_aux["taxes"],
            newInvoice_tax_aux,
          ];
        });

        product.motions.forEach((motion) => {
          const newInvoice_motion_aux = {};
          const data_to_send_motion_aux = {};
          validateFieldsInLevel(
            motion,
            newInvoice_motion_aux,
            data_to_send_motion_aux,
            readyForSend
          );
          data_to_send_product_aux["motions"] = [
            ...data_to_send_product_aux["motions"],
            data_to_send_motion_aux,
          ];
          newInvoice_product_aux["motions"] = [
            ...newInvoice_product_aux["motions"],
            newInvoice_motion_aux,
          ];
        });

        product.series.forEach((serie) => {
          const newInvoice_serie_aux = {};
          const data_to_send_serie_aux = {};
          validateFieldsInLevel(
            serie,
            newInvoice_serie_aux,
            data_to_send_serie_aux,
            readyForSend
          );
          data_to_send_product_aux["series"] = [
            ...data_to_send_product_aux["series"],
            data_to_send_serie_aux,
          ];
          newInvoice_product_aux["series"] = [
            ...newInvoice_product_aux["series"],
            newInvoice_serie_aux,
          ];
        });

        data_to_send["productsInEntryDocument"] = [
          ...data_to_send["productsInEntryDocument"],
          {
            taxes: data_to_send_product_aux.taxes,
            motions: data_to_send_product_aux.motions,
            series: data_to_send_product_aux.series,
            ...data_to_send_product_aux.general,
          },
        ];
        newInvoice["productsInEntryDocument"] = [
          ...newInvoice["productsInEntryDocument"],
          newInvoice_product_aux,
        ];
      });
      return newInvoice;
    });
    const invoicePart = data_to_send.invoice ? data_to_send.invoice : null;
    const data_to_send_f = {
      invoice: invoicePart,
      productsInEntryDocument: data_to_send.productsInEntryDocument,
      ...data_to_send.general,
    };
    console.log(
      "0000000000000000000000000000000000000000888888888888888888888888888888888888888888888888888888888"
    );
    console.log(data_to_send);
    console.log(
      "0000000000000000000000000000000000000000888888888888888888888888888888888888888888888888888888888"
    );
    console.log("9999999999999999999999999999999>>>>>>>>>>>>>>>>>>>>>>>><");
    console.log(eng_spa);
    console.log("9999999999999999999999999999999>>>>>>>>>>>>>>>>>>>>>>>><");
    if (readyForSend.r === true) {
      idEntryDocument
        ? dispatch(
          actualizarFactura(
            idEntryDocument,
            data_to_send_f,
            cookies.csrf_access_token,
            eng_spa
          )
        )
        : dispatch(
          darAltaFactura(data_to_send_f, cookies.csrf_access_token, eng_spa)
        );
    } else {
      dispatch(
        setMensajeAlert("Corregir los errores del formulario", "warning")
      );
    }
  };

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [nextLocation, setNextLocation] = useState(null);
  const [isNavigating, setIsNavigating] = useState(false);
  const navigate = useNavigate();

  const handleBlockNavigation = ({ location }) => {
    if (!isNavigating && nextLocation === null) {
      setNextLocation(location);
      setIsModalOpen(true);
    }
  };

  useBlocker(handleBlockNavigation, true);

  const handleConfirmNavigation = () => {
    setIsModalOpen(false);
    setIsNavigating(true);
    console.log("nextLocation", nextLocation);
    if (nextLocation) {
      setNextLocation(null); // Limpia la ubicación pendiente
      setIsNavigating(false); // Reinicia el estado
      window.location.href = nextLocation.pathname; // Redirige a la nueva ubicación
    }
  };

  const handleCancelNavigation = () => {
    setIsModalOpen(false);
    setNextLocation(null); // Limpia la ubicación pendiente
    setIsNavigating(false); // Asegura que el estado se reinicie
  };

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (!isNavigating) {
        event.preventDefault();
        event.returnValue = ""; // Requerido para mostrar el cuadro de diálogo del navegador
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [isNavigating]);

  const theme = useTheme();
  const isSmallScreen = useMediaQuery('(max-width:700px)');

  // logistico no puede
  return user?.roleId != 3 ? (
    <>
      <CustomDialog
        open={isModalOpen}
        onClose={handleCancelNavigation}
        title="Confirmar navegación"
        maxWidth="sm"
        width="100%"
        actions={
          <>
            <Button onClick={handleCancelNavigation} color="primary">
              No
            </Button>
            <Button onClick={handleConfirmNavigation} color="primary" autoFocus>
              Sí
            </Button>
          </>
        }
      >
        <DialogContentText>
          ¿Estás seguro de que quieres salir de esta página? Los cambios no
          guardados se perderán.
        </DialogContentText>
      </CustomDialog>
      
      <GoBackButton
        URI={"/facturas/consultar"}
        title={"Consultar Facturas"}
      />

      <Box
        display="flex"
        flexDirection="row"
        sx={{
          justifyContent: "center",
          alignItems: "center",
          position: "relative",
          marginBottom: "1%",
          flexWrap: "wrap-reverse",
          gap: "2rem",
          width: "80%",
          margin: "auto",
        }}
      >

        <Typography variant="h4" sx={{ flex: 1, textAlign: "center" }}>
          {isInvoice ? "Factura" : "Nota"}
        </Typography>

        <Box
          sx={{
            right: 0,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 2
          }}
        >
          <FormGroup>
            <Stack direction="row" spacing={1} alignItems="center">
              <FormControlLabel
                control={
                  <MaterialUISwitch
                    checked={isInvoice}
                    onChange={(e) => setIsInvoice(e.target.checked)}
                    name="isInvoice"
                    inputProps={{ 'aria-label': 'isInvoice' }}
                  />
                }
                label=""
              />
            </Stack>
          </FormGroup>
        </Box>

      </Box>
      <form sx={{ width: "100%" }}>


        <FacturaFormulario
          invoice={invoice}
          isInvoice={isInvoice}
          showOptionalFields={showOptionalFields}
          setShowOPtionalFields={
            isInvoice ? setShowOPtionalFieldsInvoice : setShowOPtionalFieldsNote
          }
          DateFieldCustom={DateFieldCustom}
          SelectCustom={SelectCustom}
          TextFieldCustom={TextFieldCustom}
        />


        <Box
          display="flex"
          flexDirection="row"
          sx={{ justifyContent: "center" }}
        >
          <Typography variant="h4" sx={{ marginBottom: "1%" }}>
            Partidas
          </Typography>
        </Box>

        {
          <ProductsInDocumentFormulario
            invoice={invoice}
            setInvoice={setInvoice}
            DateFieldCustom={DateFieldCustom}
            SelectCustom={SelectCustom}
            TextFieldCustom={TextFieldCustom}
            NumericCustom={NumericCustom}
            createProductComponent={createProductComponent}
          />
        }

        <Fab
          variant="extended"
          className="buttonGreenPink"
          aria-label="add"
          style={{
            position: "fixed",
            bottom: 100,
            right: 20,
            marginBottom: "5px",
          }}
          onClick={handleAddProduct}

        >
          <AddCircleOutlineIcon />
          {!isSmallScreen ? "Agregar Partida" : ""}
        </Fab>

        <Box sx={{ padding: 2, width: "90%", margin: "auto" }}>
          <Box
            sx={{
              bgcolor: 'background.paper',
              borderRadius: 2,
              p: 2,
              boxShadow: 1,
              color: theme.palette.text.primary,
              minWidth: "210px",
              textAlign: "right"

            }}
          >
            <Typography
              variant="body2"
              sx={{ color: "text.primary", textAlign: "right", mb: 0.5 }}
            >
              Subtotal: {subTotal.toLocaleString("es-MX", {
                style: "currency",
                currency: "MXN",
              })}
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: "text.secondary", textAlign: "right", mb: 0.5 }}
            >
              Descuento: {discount.toLocaleString("es-MX", {
                style: "currency",
                currency: "MXN",
              })}
            </Typography>
            <Typography
              variant="body2"
              sx={{ textAlign: "right", mb: 0.5 }}
            >
              IVA: {tax.toLocaleString("es-MX", {
                style: "currency",
                currency: "MXN",
              })}
            </Typography>

            <Divider light sx={{ my: 1 }} />

            <Box
              sx={{
                bgcolor: "primary.lighter",
                border: "1px solid",
                borderColor: "primary.lighter",
                borderRadius: 1,
                p: 1,
                textAlign: "end"
              }}
            >
              <Typography variant="subtitle1" sx={{ color: "primary.lighter", fontWeight: 700 }}>
                Total: {total.toLocaleString("es-MX", {
                  style: "currency",
                  currency: "MXN",
                })}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box
          display="flex"
          flexDirection="column"
          sx={{ justifyContent: "center", width: "90%", margin: "auto", mb: 5 }}
        >
          <Button
            variant="contained"
            onClick={() => {
              handleSendInvoice();
            }}
            fullWidth
            color="buttonGreenPink"
            sx={{ marginTop: "15px" }}
          >
            {idEntryDocument ? "Actualizar" : "Ingresar"}
          </Button>
        </Box>
      </form>
    </>
  ) : (
    <AccesDenied />
  );
}

export default App;
