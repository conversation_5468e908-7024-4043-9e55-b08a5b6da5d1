
.toggle-button-cover {
    position: relative;
    width: 100px;
    height: 50px;
    box-sizing: border-box;
  }
  
  .button-cover,
  .knobs,
  .layer {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  
  .button {
    position: relative;
    width: 100px;
    height: 46px;
    margin: 0 auto 0 auto;
    overflow: hidden;
  }
  
  .button.r,
  .button.r .layer {
    border-radius: 100px;
  }
  
  .button.b2 {
    border-radius: 2px;
  }
  
  .checkbox {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 3;
  }
  
  .knobs {
    z-index: 2;
  }
  
  .layer {
    width: 100%;
    background-color: #ebf7fc;
    transition: 0.5s ease all;
    z-index: 1;
  }
  
  
  #button-2 .knobs:before,
  #button-2 .knobs:after {
    content: "Nota";
    position: absolute;
    top: 6px;
    left: 5px;
    width: 90px;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    line-height: 1;
    padding: 9px 4px;
    background-color: #7ddfee;
    border-radius: 20px;
    transition: 0.5s ease all;
  }
  
  #button-2 .knobs:before {
    content: "Nota";
  }
  
  #button-2 .knobs:after {
    content: "Factura";
  }
  
  #button-2 .knobs:after {
    right: -94px;
    left: auto;
    background-color: #184a7e;
  }
  
  #button-2 .checkbox:checked + .knobs:before {
    left: -94px;
  }
  
  #button-2 .checkbox:checked + .knobs:after {
    right: 5px;
  }
  
  #button-2 .checkbox:checked ~ .layer {
    background-color: #fcebeb;
  }
  