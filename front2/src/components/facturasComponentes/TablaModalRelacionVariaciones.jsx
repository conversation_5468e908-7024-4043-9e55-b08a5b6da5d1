import React, { useState, useEffect } from "react";
import Paper from "@mui/material/Paper";
import TableRow from "@mui/material/TableRow";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import Table from "@mui/material/Table";
import TableHead from "@mui/material/TableHead";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import { styled } from "@mui/material/styles";
import { Box, Select, MenuItem, Grid, TextField, Chip, Fab, ButtonGroup, Button } from "@mui/material";
import { IconButton, Tooltip } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import AddCircleIcon from '@mui/icons-material/AddCircle';
import RemoveIcon from "@mui/icons-material/Remove";
import RemoveCircleIcon from '@mui/icons-material/RemoveCircle';
import Skeleton from "@mui/material/Skeleton";
import { useDispatch, useSelector } from "react-redux";
import { AddCircle, RemoveCircle, Add, Edit, MoreVert } from "@mui/icons-material";
import {
  setAmountGreaterThanZero,
  setJsonToSend,
  setVariationMultipleEnters,
  obtenerHistorialMovimientos,
} from "../../redux/facturasDucks";
import { StyledTableCell, StyledTableRow } from "../StyledTableComponents";
import { HistorialMovimientosPopover } from "./HistorialMovimientosPopover";
import QuantityComponent from "../componentesGenerales/QuantityComponent";
import { Typography } from "@mui/material";
import { Card, CardContent } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { Fade } from "@mui/material";
import { Stack } from "@mui/material";
import { CardHeader } from "@mui/material";
import { Menu, ListItemIcon, ListItemText } from "@mui/material";
import { AlertComponent } from "../componentesGenerales/Alert";

export const TablaModalRelacionVariaciones = ({
  almacenes,
  showSelectAlmacen,
  almacenSelected,
  selectedProduct,
  productFromEntryDocument,
  productEnteredData,
  setProductEnteredData,
  setCurrentlyEntering, // <-- Agregar esta prop
  currentlyEntering,
}) => {
  const dispatch = useDispatch();
  const totalUnits = productFromEntryDocument.units;
  const totalEntered = productFromEntryDocument.totalEntered;
  const products = useSelector((store) => store.facturas.productVariations);



  const [productos, setProductos] = useState([]);
  const [initialized, setInitialized] = useState(false);
  const [hoveredProductIndex, setHoveredProductIndex] = useState(null);
  const [tieneMasDeUnEnter, setTieneMasDeUnEnter] = useState(false);
  const [tieneAmountMayorAZero, setTieneAmountMayorAZero] = useState(false);
  const [productsClean, setProductsClean] = useState([]);

  const historialMovimientos = useSelector(
    (store) => store.facturas.historialMovimientos
  );
  const loadingHistorialMovimientos = useSelector(
    (store) => store.facturas.loadingHistorialMovimientos
  );


  const [locations, setLocations] = useState([]);

  const [totalAmount, setTotalAmount] = useState(0);
  // Estado para el pendiente
  const [totalPending, setTotalPending] = useState(totalUnits - (productFromEntryDocument?.totalEntered || 0));

  // Estado para mantener el orden estable de las filas
  const [ordenEstableFilas, setOrdenEstableFilas] = useState([]);
  const [ordenInicializado, setOrdenInicializado] = useState(false);

  // Estado para mantener el orden estable de los productos en vista móvil
  const [ordenEstableProductos, setOrdenEstableProductos] = useState([]);
  const [ordenProductosInicializado, setOrdenProductosInicializado] = useState(false);




  // Función utilitaria para actualizar la estructura de envío al backend
  function updateProductEnteredData(internalSku, locationLevelItemId, delta, storeId = null, locationId = null) {
    setProductEnteredData(prev => {
      const newData = { ...prev };
      newData.productFromEntryDocumentId = productFromEntryDocument.id;


      // Buscar si ya existe el internalSku
      let productIndex = newData.productsEnteredIntoStore.findIndex(p => p.internalSku === internalSku);

      if (productIndex === -1) {
        // Si no existe el producto, crearlo
        newData.productsEnteredIntoStore.push({
          internalSku,
          enters: []
        });
        productIndex = newData.productsEnteredIntoStore.length - 1;
      }

      // Buscar si ya existe el enter con ese locationLevelItemId
      const product = newData.productsEnteredIntoStore[productIndex];
      const enterIndex = product.enters.findIndex(e => e.locationLevelItemId === locationLevelItemId);

      if (enterIndex !== -1) {
        // Actualizar existente: sumar el delta al amount actual
        product.enters[enterIndex].amount += delta;
      } else {
        // Agregar nuevo con el delta como amount inicial
        const enterData = {
          amount: delta,
          locationLevelItemId,
        };
        product.enters.push(enterData);
      }

      return newData;
    });
  }

  React.useEffect(() => {
    if (almacenes && almacenes.length > 0) {
      setLocations(
        almacenes.find((almacen) => almacen.id === almacenes[0].id)
          .locationLevelItems
      );
    }
  }, [almacenes]);
  React.useEffect(() => {
    if (initialized) {
      if (productos) {
        // Calcular la suma de amount en productos
        const sumAmount = productos.reduce(
          (total, product) =>
            total +
            product.enters
              .map((enter) => enter.amount)
              .reduce((total, amount) => total + amount, 0),
          0
        );
        setTotalAmount(sumAmount);
      }
    }
  }, [initialized, productos]);

  useEffect(() => {
    if (showSelectAlmacen) {
      const nuevosProductos = productos?.map((producto) => {
        const nuevoEnters = producto?.enters.map((enter) => ({
          ...enter,
          storeId: almacenSelected,
        }));
        return {
          ...producto,
          enters: nuevoEnters,
        };
      });
      setProductos(nuevosProductos);
    }
  }, [showSelectAlmacen, almacenSelected]);

  React.useEffect(() => {
    if (products) {
      // Convertir el objeto entries_by_variation a un array, excluyendo internalBaseSku
      const entriesArray = Object.entries(products.entries_by_variation || {})
        .filter(([key]) => key !== 'internalBaseSku')
        .map(([key, value]) => ({
          ...value,
          internalSku: key, // Guardamos la clave como internalSku para referencia
          enters: value.enters && value.enters.length > 0
            ? value.enters.map(enter => ({
              amount: 0,
              storeId: almacenes[0]?.id,
              locationId: 1,
            }))
            : [
              {
                amount: 0,
                storeId: almacenes[0]?.id,
                locationId: 1,
              },
            ]
        }));

      setProductos(entriesArray);
      setInitialized(true);

      // Reinicializar el orden estable cuando cambian los productos base
      setOrdenInicializado(false);
      setOrdenProductosInicializado(false);
      setOrdenEstableFilas([]);
      setOrdenEstableProductos([]);
    }
  }, [products]);

  React.useEffect(() => {
    if (productos) {
      const productosLimpios = productos
        .map((producto) => ({
          ...producto,
          enters: producto.enters.filter((enter) => enter.amount > 0),
        }))
        .filter((producto) => producto.enters.length > 0);
      setProductsClean(productosLimpios);
    }
  }, [productos]);

  React.useEffect(() => {
    if (productsClean) {
      const resultadoFinal = {
        productFromEntryDocumentId: productFromEntryDocument.id,
        productsEnteredIntoStore: productsClean.map((producto) => ({
          internalSku: producto.internalSku,
          enters: producto.enters.map((enter) => ({
            locationLevelItemId: enter.locationId,
            amount: enter.amount,
          })),
        })),
      };
      dispatch(setJsonToSend(resultadoFinal));
    }
  }, [productsClean]);

  const handleDecrease = (internalSku, storeId, locationId, locationName) => {

    setProductos(prev =>
      prev.map(producto => {
        if (producto.internalSku !== internalSku) return producto;
        // Actualiza stores/internalLocations
        const newStores = updateInternalLocationAmountDelta(producto.stores, storeId, locationId, -1);
        // Actualiza enters
        const currentEnter = producto.enters.find(
          e => e.storeId === storeId && e.locationId === locationId
        );

        const newAmount = Math.max(0, (currentEnter?.amount || 0) - 1);
        // Actualizar estructura para backend con delta -1
        updateProductEnteredData(internalSku, locationId, -1, storeId, locationId);

        return {
          ...producto,
          stores: newStores,
          enters: updateEntersArray(producto.enters, storeId, locationId, newAmount)
        };
      })
    );
  };

  const handleIncrease = (internalSku, storeId, locationId, locationName) => {
    if (totalPending === 0) return;
    setProductos(prev =>
      prev.map(producto => {
        if (producto.internalSku !== internalSku) return producto;
        // Actualiza stores/internalLocations
        const newStores = updateInternalLocationAmountDelta(producto.stores, storeId, locationId, 1);
        // Actualiza enters
        const currentEnter = producto.enters.find(
          e => e.storeId === storeId && e.locationId === locationId
        );
        const newAmount = (currentEnter?.amount || 0) + 1;
        // Actualizar estructura para backend con delta +1
        updateProductEnteredData(internalSku, locationId, +1, storeId, locationId);

        return {
          ...producto,
          stores: newStores,
          enters: updateEntersArray(producto.enters, storeId, locationId, newAmount)
        };
      })
    );
  };

  const handleChangeAmount = (value, internalSku, storeId, locationId) => {
    const amount = Math.max(0, Number(value));
    setProductos(prev =>
      prev.map(producto => {
        if (producto.internalSku !== internalSku) return producto;
        // Calcula la diferencia para stores/internalLocations
        const currentEnter = producto.enters.find(
          e => e.storeId === storeId && e.locationId === locationId
        );
        const currentAmount = currentEnter?.amount || 0;
        const delta = amount - currentAmount;
        const newStores = updateInternalLocationAmountDelta(producto.stores, storeId, locationId, delta);
        // Actualizar estructura para backend con el delta calculado
        if (delta !== 0) {
          updateProductEnteredData(internalSku, locationId, delta, storeId, locationId);
        }

        return {
          ...producto,
          stores: newStores,
          enters: updateEntersArray(producto.enters, storeId, locationId, amount)
        };
      })
    );
  };

  const handleAlmacenChange = (event, productIndex, enterIndex) => {
    const newProductos = [...productos];
    setLocations(
      almacenes.find((almacen) => almacen.id === event.target.value)
        .locationLevelItems
    );
    newProductos[productIndex].enters[enterIndex].storeId = event.target.value;
    newProductos[productIndex].enters[enterIndex].locationId = locations.length > 0 ? locations[0].id : null;
    setProductos(newProductos);
  };

  const obtenerTodosLosElementos = (almacenes) => {
    let resultado = [];

    const recorrer = (objeto) => {
      resultado.push({
        id: objeto.id,
        name: objeto.name,
        entireName: objeto.entireName,
      });
      if (objeto.children) {
        objeto.children.forEach((child) => recorrer(child));
      }
    };

    almacenes?.forEach((almacen) => recorrer(almacen));
    return resultado;
  };

  useEffect(() => {
    if (productos) {
      // Verifica si al menos un producto tiene algún elemento enters con amount mayor a 0
      const tieneAmountMayorAZero = productos.some((producto) =>
        producto.enters.some((enter) => enter.amount > 0)
      );
      // Actualiza el estado con el resultado de la condición
      setTieneAmountMayorAZero(tieneAmountMayorAZero);
    }
  }, [productos]);

  useEffect(() => {
    if (totalPending > 0) {
      dispatch(setAmountGreaterThanZero(true));
    } else if (totalPending === 0) {
      dispatch(setAmountGreaterThanZero(false));
    }
  }, [tieneAmountMayorAZero]);

  const handleKeyPress = (event) => {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  };


  // Función para transformar los productos en filas agrupadas por variación
  const obtenerFilasTabla = (productos) => {
    const filasAgrupadas = [];

    productos.forEach((producto) => {
      const storesArray = producto.stores
        ? Object.values(producto.stores)
        : [];

      // Agrupar ubicaciones por almacén
      const ubicacionesPorAlmacen = {};
      let primerAlmacen = null;

      storesArray.forEach((storeObj) => {
        const store = storeObj.store;
        const internalLocations = storeObj.internalLocations || {};

        if (!primerAlmacen) {
          primerAlmacen = store;
        }

        // Ordenar las ubicaciones por ID para mantener consistencia
        const locationIds = Object.keys(internalLocations).sort((a, b) => Number(a) - Number(b));

        locationIds.forEach((locId) => {
          const loc = internalLocations[locId];
          // Solo incluir si existe y tiene locationLevelItem
          if (loc && loc.locationLevelItem) {
            if (!ubicacionesPorAlmacen[store.id]) {
              ubicacionesPorAlmacen[store.id] = {
                store,
                ubicaciones: []
              };
            }

            ubicacionesPorAlmacen[store.id].ubicaciones.push({
              location: loc.locationLevelItem,
              amount: loc.amount,
              locationId: loc.id,
              key: `${producto.internalSku}-${store.id}-${loc.locationLevelItem.id}`
            });
          }
        });
      });

      // Solo agregar si tiene ubicaciones
      if (Object.keys(ubicacionesPorAlmacen).length > 0) {
        filasAgrupadas.push({
          producto,
          primerAlmacen,
          ubicacionesPorAlmacen,
          key: producto.internalSku
        });
      }
    });

    return filasAgrupadas;
  };

  const filasTabla = obtenerFilasTabla(productos);

  // Función para obtener productos ordenados de manera estable para vista móvil
  const obtenerProductosOrdenados = (productos) => {
    if (!productos || productos.length === 0) return [];

    // Si no se ha inicializado el orden, ordenar por total de amount y guardar el orden
    if (!ordenProductosInicializado) {
      const productosConTotal = productos.map(producto => ({
        ...producto,
        totalAmount: producto.enters.reduce((sum, enter) => sum + enter.amount, 0)
      }));

      const productosOrdenados = productosConTotal.sort((a, b) => b.totalAmount - a.totalAmount);
      const ordenKeys = productosOrdenados.map(p => p.internalSku);
      setOrdenEstableProductos(ordenKeys);
      setOrdenProductosInicializado(true);
      return productosOrdenados;
    }

    // Si ya está inicializado, mantener el orden estable
    if (ordenEstableProductos.length > 0) {
      return productos.sort((a, b) => {
        const indexA = ordenEstableProductos.indexOf(a.internalSku);
        const indexB = ordenEstableProductos.indexOf(b.internalSku);
        // Si algún producto no está en el orden original, ponerlo al final
        if (indexA === -1) return 1;
        if (indexB === -1) return -1;
        return indexA - indexB;
      });
    }

    return productos;
  };


  // 1. Estado para la fila editable
  const [nuevaFila, setNuevaFila] = useState(null); // { sku, storeId, locationId, amount, error }
  const [errorNuevaFila, setErrorNuevaFila] = useState("");

  // 2. Función para iniciar la edición de nueva ubicación para una variación
  const handleAgregarUbicacion = (sku) => {
    setNuevaFila({
      sku,
      storeId: almacenes[0]?.id || "",
      locationId: "",
      amount: 1,
      error: "",
    });
    setErrorNuevaFila("");
  };

  // 3. Función para cancelar la edición
  const handleCancelarNuevaFila = () => {
    setNuevaFila(null);
    setErrorNuevaFila("");
  };

  // 4. Función para guardar la nueva ubicación
  const handleGuardarNuevaFila = () => {
    if (!nuevaFila.storeId || !nuevaFila.locationId) {
      setErrorNuevaFila("Debes seleccionar almacén y ubicación interna.");
      return;
    }
    if (nuevaFila.amount <= 0) {
      setErrorNuevaFila("La cantidad debe ser mayor a cero.");
      return;
    }
    // Validar que no se repita la ubicación interna para la variación
    const variacion = productos.find(p => p.internalSku === nuevaFila.sku);
    const ubicacionesUsadas = variacion.enters.map(e => `${e.storeId}-${e.locationId}`);
    if (ubicacionesUsadas.includes(`${nuevaFila.storeId}-${nuevaFila.locationId}`)) {
      setErrorNuevaFila("Esta ubicación interna ya está asignada para esta variación.");
      return;
    }
    // Validar que la suma de cantidades no supere el máximo
    const sumaActual = variacion.enters.reduce((acc, e) => acc + e.amount, 0);
    const faltantes = totalUnits - sumaActual;
    if (nuevaFila.amount > faltantes) {
      setErrorNuevaFila(`No puedes asignar más de ${faltantes} productos a esta variación.`);
      return;
    }
    // Agregar la nueva ubicación
    const nuevosProductos = productos.map(p => {
      if (p.internalSku !== nuevaFila.sku) return p;
      return {
        ...p,
        enters: [
          ...p.enters,
          {
            amount: nuevaFila.amount,
            storeId: nuevaFila.storeId,
            locationId: nuevaFila.locationId,
          },
        ],
      };
    });
    setProductos(nuevosProductos);
    setNuevaFila(null);
    setErrorNuevaFila("");
  };

  // 5. Opciones de almacén y ubicaciones disponibles para la variación
  const getUbicacionesDisponibles = (sku, storeId) => {
    const variacion = productos.find(p => p.internalSku === sku);
    const ubicacionesUsadas = variacion.enters.map(e => `${e.storeId}-${e.locationId}`);
    const almac = almacenes.find(a => a.id === storeId);
    if (!almac) return [];
    const todas = obtenerTodosLosElementos(almac.locationLevelItems);
    return todas.filter(u => !ubicacionesUsadas.includes(`${storeId}-${u.id}`));
  };

  // Función helper para crear/actualizar el objeto stores correctamente
  const createStoresStructure = (currentStores, storeId, locationId, amount) => {
    // Buscar la información completa del store en almacenes
    const storeInfo = almacenes.find(almacen => almacen.id === storeId);
    if (!storeInfo) {
      return currentStores;
    }

    // Buscar la información completa de la ubicación en las locationLevelItems del almacén específico
    const locationInfo = obtenerTodosLosElementos(storeInfo.locationLevelItems).find(loc => loc.id === locationId);
    if (!locationInfo) {
      return currentStores;
    }

    // Crear la estructura completa
    const newStores = {
      ...currentStores,
      [storeId]: {
        store: {
          address: storeInfo.address || "",
          id: storeInfo.id,
          phone: storeInfo.phone || "",
          storeDescription: storeInfo.storeDescription || "",
          storeName: storeInfo.storeName || "",
          urlMaps: storeInfo.urlMaps || "",
        },
        internalLocations: {
          ...(currentStores[storeId]?.internalLocations || {}),
          [locationId]: {
            amount: amount,
            id: locationId,
            locationLevelItem: {
              entireName: locationInfo.entireName,
              id: locationInfo.id,
              name: locationInfo.name,
              parentId: locationInfo.parentId || null,
            },
          },
        },
      },
    };

    return newStores;
  };

  function updateInternalLocationAmountDelta(stores, storeId, locationId, delta) {


    const locationsArray = Object.values(stores[storeId].internalLocations);
    const index = locationsArray.findIndex(l => l.locationLevelItem.id === locationId);
    if (index === -1) {
      return stores;
    }
    const currentAmount = locationsArray[index].amount;
    const newAmount = Math.max(0, currentAmount + delta);

    const newLocationsArray = [
      ...locationsArray.slice(0, index),
      { ...locationsArray[index], amount: newAmount },
      ...locationsArray.slice(index + 1)
    ];

    // Reconstruir el objeto
    const newInternalLocations = {};
    newLocationsArray.forEach(loc => {
      newInternalLocations[loc.locationLevelItem.id] = loc;
    });

    return {
      ...stores,
      [storeId]: {
        ...stores[storeId],
        internalLocations: newInternalLocations
      }
    };
  }

  function updateEntersArray(enters, storeId, locationId, amount) {
    if (amount === 0) {
      // Elimina el objeto si amount es 0
      return enters.filter(
        e => !(e.storeId === storeId && e.locationId === locationId)
      );
    }
    // Si ya existe, actualiza; si no, agrega
    const exists = enters.some(
      e => e.storeId === storeId && e.locationId === locationId
    );
    if (exists) {
      return enters.map(e =>
        e.storeId === storeId && e.locationId === locationId
          ? { ...e, amount, locationLevelItemId: locationId }
          : e
      );
    } else {
      return [
        ...enters,
        { storeId, locationId, amount, locationLevelItemId: locationId }
      ];
    }
  }


  // Recalcula el pendiente cada vez que cambian los productos
  useEffect(() => {
    const totalAssigned = productos.reduce((acc, producto) => {
      // Suma todos los amount de todas las ubicaciones de todos los productos
      return acc + Object.values(producto.stores || {}).reduce((storeAcc, storeObj) => {
        return storeAcc + Object.values(storeObj.internalLocations || {}).reduce((locAcc, loc) => locAcc + (loc.amount || 0), 0);
      }, 0);
    }, 0);
    setTotalPending(totalUnits - totalAssigned);
  }, [productos, totalUnits]);

  // Calcular el total actual ingresado y comunicarlo al componente padre
  useEffect(() => {
    if (productEnteredData && productEnteredData.productsEnteredIntoStore) {

      const totalCurrentlyEntering = productEnteredData.productsEnteredIntoStore.reduce((total, product) => {
        return total + product.enters.reduce((productTotal, enter) => {
          return productTotal + (enter.amount || 0);
        }, 0);
      }, 0);
      // Comunicar el total actual al componente padre
      if (setCurrentlyEntering) {
        setCurrentlyEntering(totalCurrentlyEntering);
      }
    }
  }, [productEnteredData, setCurrentlyEntering]);

  return (
    <Box overflow="auto" mt={2}>
      {productos ? (

        <>
          {/* Tabla para pantallas grandes */}
          <TableContainer component={Paper} sx={{ borderRadius: "18px" }}>
            <Table aria-label="tabla-variaciones">
              <TableHead>
                <TableRow>
                  <StyledTableCell>Marca / Modelo</StyledTableCell>
                  <StyledTableCell>Variación</StyledTableCell>
                  <StyledTableCell>Almacén</StyledTableCell>
                  <StyledTableCell>Ubicación interna</StyledTableCell>
                  <StyledTableCell>Cantidad</StyledTableCell>
                  <StyledTableCell>Acciones</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filasTabla.length > 0 ? (
                  // Mostrar filas con datos existentes
                  filasTabla.map((filaAgrupada, idxFila) => {
                    const { producto, primerAlmacen, ubicacionesPorAlmacen } = filaAgrupada;

                    // Determinar si la fila editable está activa para esta variación
                    const filaEditableActiva = nuevaFila && nuevaFila.sku === producto.internalSku;

                    return (
                      <React.Fragment key={producto.internalSku + idxFila}>
                        <StyledTableRow>
                          {/* Marca / Modelo */}
                          <TableCell sx={{ verticalAlign: 'top', paddingTop: '8px' }}>
                            <Box>
                              {(() => {
                                const primerAlmacen = Object.values(ubicacionesPorAlmacen)[0];
                                const hayMultiplesAlmacenes = Object.keys(ubicacionesPorAlmacen).length > 1;

                                // Calcular el margen superior para alinear con el nombre del almacén
                                // El nombre del almacén tiene un mt: 2, así que necesitamos compensar
                                const margenSuperior = hayMultiplesAlmacenes ? 2 : 0;

                                return (
                                  <Typography variant="body2" sx={{
                                    mt: margenSuperior,
                                    pt: hayMultiplesAlmacenes ? 1 : 0
                                  }}>
                                    {producto.brand} / {producto.model}
                                  </Typography>
                                );
                              })()}
                            </Box>
                          </TableCell>

                          {/* Variación */}
                          <TableCell sx={{ verticalAlign: 'top', paddingTop: '8px' }}>
                            <Box>
                              {(() => {
                                const hayMultiplesAlmacenes = Object.keys(ubicacionesPorAlmacen).length > 1;

                                // Calcular el margen superior para alinear con el nombre del almacén
                                const margenSuperior = hayMultiplesAlmacenes ? 2 : 0;

                                return (
                                  <Typography variant="body2" sx={{
                                    mt: margenSuperior,
                                    pt: hayMultiplesAlmacenes ? 1 : 0
                                  }}>
                                    {producto.variationDescription} <br /> ({producto.internalSku})
                                  </Typography>
                                );
                              })()}
                            </Box>
                          </TableCell>

                          {/* Almacén - Solo mostrar el primer almacén */}
                          <TableCell sx={{ verticalAlign: 'top', paddingTop: '8px' }}>
                            <Box>
                              {Object.entries(ubicacionesPorAlmacen).map(([storeId, storeData], storeIdx) => {
                                return (
                                  <Box key={storeId} sx={{ mb: storeIdx < Object.keys(ubicacionesPorAlmacen).length - 1 ? 2 : 0 }}>
                                    {storeData.ubicaciones.map((ubicacion, ubIdx) => (
                                      <Box key={ubicacion.key} sx={{ mb: ubIdx < storeData.ubicaciones.length - 1 ? 1 : 0 }}>
                                        {/* Solo mostrar el nombre del almacén en la primera ubicación de cada almacén */}
                                        
                                        {ubIdx === 0 ? (
                                          <Typography variant="body2" sx={{
                                            mt: storeIdx === 0 ? 0 : storeData.ubicaciones.length * 5,
                                            mb: 1
                                          }}>
                                            {storeData.store.storeName}
                                          </Typography>
                                        ) : (
                                          <Box sx={{ height: '20px' }}>
                                            
                                          </Box>
                                        )}
                                      </Box>
                                    ))}
                                  </Box>
                                );
                              })}

                              {/* Fila editable: mostrar select de almacén */}
                              {filaEditableActiva && (() => {
                                const almacenesKeys =Object.entries(ubicacionesPorAlmacen);
                               let index = almacenesKeys[almacenesKeys.length -1][1].ubicaciones.length
                               if(index === 1) {
                                index = 1
                               }
                               const mtDinamico = index === 1 ? 9.6 : index * 3.8;
                                return (<Box sx={{ mt: mtDinamico }}>
                                  <Select
                                    value={nuevaFila.storeId}
                                    onChange={e => setNuevaFila({ ...nuevaFila, storeId: e.target.value, locationId: "" })}
                                    size="small"
                                  >
                                    {almacenes.map(a => (
                                      <MenuItem key={a.id} value={a.id}>{a.storeName}</MenuItem>
                                    ))} 
                                  </Select>
                                </Box>);
                              })()}
                            </Box>
                          </TableCell>

                          {/* Ubicaciones Internas - Apiladas verticalmente */}
                          <TableCell sx={{ verticalAlign: 'top', paddingTop: '8px' }}>
                            <Box>
                              {Object.entries(ubicacionesPorAlmacen).map(([storeId, storeData], storeIdx) => (
                                <Box key={storeId} sx={{ mb: storeIdx < Object.keys(ubicacionesPorAlmacen).length - 1 ? 2 : 0 }}>
                                  {/* Mostrar nombre del almacén solo si hay más de uno */}
                                  {Object.keys(ubicacionesPorAlmacen).length > 1 && (
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        color: 'text.secondary',
                                        fontWeight: 'bold',
                                        display: 'block',
                                        mb: 1,
                                        mt: storeIdx === 0 ? 0 : 1
                                      }}
                                    >
                                      {storeData.store.storeName}
                                    </Typography>
                                  )}

                                  {storeData.ubicaciones.map((ubicacion, ubIdx) => (
                                    <Box key={ubicacion.key} sx={{ mb: ubIdx < storeData.ubicaciones.length - 1 ? 1 : 0, mt: ubIdx !== 0 ? 2.6 : 2, whiteSpace: 'nowrap', textOverflow: 'ellipsis', overflow: 'hidden', minWidth: '100px' }}>
                                      <Tooltip title={ubicacion.location.entireName}>
                                        <Typography variant="body2">
                                          {ubicacion.location.entireName}
                                        </Typography>
                                      </Tooltip>
                                    </Box>
                                  ))}


                                </Box>
                              ))}

                            </Box>
                            {filaEditableActiva && (
                              <>
                                <Select
                                  value={nuevaFila.locationId}
                                  onChange={e => setNuevaFila({ ...nuevaFila, locationId: e.target.value, amount: 1 })}
                                  size="small"
                                  sx={{ width: '150px', padding: '0px', marginTop: 3 }}
                                >
                                  {getUbicacionesDisponibles(nuevaFila.sku, nuevaFila.storeId).map(u => (
                                    <MenuItem key={u.id} value={u.id}>{u.entireName}</MenuItem>
                                  ))}
                                </Select>
                                
                              </>
                            )}
                          </TableCell>

                          {/* Cantidades - Apiladas verticalmente alineadas con ubicaciones */}
                          <TableCell sx={{ verticalAlign: 'top', paddingTop: '8px' }}>
                            <Box>
                              {Object.entries(ubicacionesPorAlmacen).map(([storeId, storeData], storeIdx) => (
                                <Box key={storeId} sx={{ mb: storeIdx < Object.keys(ubicacionesPorAlmacen).length - 1 ? 2 : 0 }}>
                                  {/* Mostrar nombre del almacén solo si hay más de uno */}
                                  {Object.keys(ubicacionesPorAlmacen).length > 1 && (
                                    <Box sx={{
                                      height: '20px',
                                      mb: 1,
                                      mt: storeIdx === 0 ? 0 : 1
                                    }}>
                                      {/* Espacio vacío para alinear con el nombre del almacén en la columna de ubicaciones */}
                                    </Box>
                                  )}

                                  {storeData.ubicaciones.map((ubicacion, ubIdx) => (
                                    <Box key={ubicacion.key} sx={{
                                      mb: ubIdx < storeData.ubicaciones.length - 1 ? 0 : -1,
                                      mt: ubIdx !== 0 ? 0 : 0
                                    }}>
                                      <QuantityComponent
                                        amount={Number(ubicacion.amount)}
                                        totalUnits={totalUnits}
                                        handleDecrease={() =>
                                          handleDecrease(
                                            producto.internalSku,
                                            storeData.store.id,
                                            ubicacion.location.id,
                                            ubicacion.location.entireName
                                          )
                                        }
                                        handleIncrease={() => {
                                          handleIncrease(
                                            producto.internalSku,
                                            storeData.store.id,
                                            ubicacion.location.id,
                                            ubicacion.location.entireName
                                          )
                                        }}
                                        handleChangeAmount={(value) =>
                                          handleChangeAmount(
                                            value,
                                            producto.internalSku,
                                            storeData.store.id,
                                            ubicacion.location.id,
                                            ubicacion.location.entireName
                                          )
                                        }
                                        totalEntered={productFromEntryDocument?.totalEntered + currentlyEntering}
                                        totalAmount={totalAmount}
                                      />
                                    </Box>
                                  ))}
                                </Box>
                              ))}

                              {/* Fila editable: mostrar componente de cantidad */}
                              {filaEditableActiva && (
                                <Box sx={{ mt: 2 }}>
                                  <QuantityComponent
                                    amount={nuevaFila.amount}
                                    totalUnits={totalUnits}
                                    index={0}
                                    enterIndex={0}
                                    handleDecrease={() => {
                                      const newAmount = Math.max(0, nuevaFila.amount - 1);
                                      setNuevaFila({ ...nuevaFila, amount: newAmount });
                                    }}
                                    handleIncrease={() => {
                                      if (totalPending === 0) return;
                                      const newAmount = nuevaFila.amount + 1;
                                      setNuevaFila({ ...nuevaFila, amount: newAmount });
                                    }}
                                    handleChangeAmount={(value) => {
                                      const newAmount = Math.max(0, Number(value));
                                      setNuevaFila({ ...nuevaFila, amount: newAmount });
                                    }}
                                    totalEntered={productFromEntryDocument?.totalEntered + currentlyEntering}
                                    totalAmount={nuevaFila.amount}
                                  />
                                </Box>
                              )}
                            </Box>
                          </TableCell>

                          {/* Acciones - Apiladas verticalmente alineadas con cantidades */}
                          <TableCell sx={{ verticalAlign: 'top', paddingTop: '8px' }}>
                            <Box>
                              {Object.entries(ubicacionesPorAlmacen).map(([storeId, storeData], storeIdx) => (
                                <Box key={storeId} sx={{ mb: storeIdx < Object.keys(ubicacionesPorAlmacen).length - 1 ? 2 : 0 }}>
                                  {/* Mostrar nombre del almacén solo si hay más de uno */}
                                  {Object.keys(ubicacionesPorAlmacen).length > 1 && (
                                    <Box sx={{
                                      height: '20px',
                                      mb: 1,
                                      mt: storeIdx === 0 ? 0 : 1
                                    }}>
                                      {/* Espacio vacío para alinear con el nombre del almacén en la columna de ubicaciones */}
                                    </Box>
                                  )}

                                  {storeData.ubicaciones.map((ubicacion, ubIdx) => (
                                    <Box key={ubicacion.key} sx={{
                                      mb: ubIdx < storeData.ubicaciones.length - 1 ? 0 : 0,
                                      mt: ubIdx !== 0 ? 0 : 0,
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: 1,
                                      minHeight: '40px' // Altura mínima para alinear con QuantityComponent
                                    }}>
                                      {/* Botón de agregar ubicación solo en la primera ubicación */}
                                      <HistorialMovimientosPopover
                                        historialMovimientos={historialMovimientos}
                                        loadingHistorialMovimientos={loadingHistorialMovimientos}
                                        onClick={() => {
                                          dispatch(obtenerHistorialMovimientos(ubicacion.locationId));
                                        }}
                                        nameLocation={ubicacion.location.entireName}
                                      />
                                      {storeIdx === 0 && ubIdx === 0 && (
                                        <Tooltip title={`Agregar ubicación para ${producto.internalSku}`}>
                                          <IconButton
                                            color="buttonGreenPink"
                                            size="small"
                                            onClick={() => {
                                              // Solo permitir una fila editable vacía a la vez
                                              if (filaEditableActiva && (!nuevaFila.storeId || !nuevaFila.locationId || nuevaFila.amount <= 0)) {
                                                setErrorNuevaFila("Completa o cancela la fila actual antes de agregar otra.");
                                                return;
                                              }
                                              setNuevaFila({
                                                sku: producto.internalSku,
                                                storeId: almacenes[0]?.id || "",
                                                locationId: "",
                                                amount: 1,
                                                error: "",
                                              });
                                              setErrorNuevaFila("");
                                            }}
                                          >
                                            <AddCircle color="buttonGreenPink" />
                                          </IconButton>
                                        </Tooltip>
                                      )}


                                    </Box>
                                  ))}
                                </Box>
                              ))}

                              {/* Fila editable: mostrar botones de acciones */}
                              {filaEditableActiva && (
                                <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <IconButton color="success" size="small" sx={{ fontSize: "14px" }} onClick={() => {
                                    // Validación
                                    if (!nuevaFila.storeId || !nuevaFila.locationId) {
                                      setErrorNuevaFila("Debes seleccionar almacén y ubicación interna.");
                                      return;
                                    }
                                    if (nuevaFila.amount <= 0) {
                                      setErrorNuevaFila("La cantidad debe ser mayor a cero.");
                                      return;
                                    }
                                    if (nuevaFila.amount > totalPending) {
                                      setErrorNuevaFila(`No puedes asignar más de lo pendiente (${totalPending}).`);
                                      return;
                                    }
                                    // Validar que no se repita la ubicación interna para la variación
                                    const variacion = productos.find(p => p.internalSku === nuevaFila.sku);
                                    const ubicacionesUsadas = variacion.enters.map(e => `${e.storeId}-${e.locationId}`);
                                    if (ubicacionesUsadas.includes(`${nuevaFila.storeId}-${nuevaFila.locationId}`)) {
                                      setErrorNuevaFila("Esta ubicación interna ya está asignada para esta variación.");
                                      return;
                                    }
                                    // Validar que la suma de cantidades no supere el máximo por variación
                                    const sumaActual = variacion.enters.reduce((acc, e) => acc + e.amount, 0);
                                    const faltantes = totalUnits - sumaActual;
                                    if (nuevaFila.amount > faltantes) {
                                      setErrorNuevaFila(`No puedes asignar más de ${faltantes} productos a esta variación.`);
                                      return;
                                    }
                                    // Agregar la nueva ubicación
                                    const nuevosProductos = productos.map(p => {
                                      if (p.internalSku !== nuevaFila.sku) return p;
                                      // Usar las funciones helper existentes para mantener consistencia
                                      const newStores = updateInternalLocationAmountDelta(
                                        createStoresStructure(
                                          p.stores || {},
                                          nuevaFila.storeId,
                                          nuevaFila.locationId,
                                          0 // Inicializar en 0 para luego sumar el delta
                                        ),
                                        nuevaFila.storeId,
                                        nuevaFila.locationId,
                                        nuevaFila.amount // Delta a agregar
                                      );

                                      const newEnters = updateEntersArray(
                                        p.enters,
                                        nuevaFila.storeId,
                                        nuevaFila.locationId,
                                        nuevaFila.amount
                                      );

                                      return {
                                        ...p,
                                        enters: newEnters,
                                        stores: newStores,
                                      };
                                    });

                                    // Actualizar la data del backend con el delta
                                    updateProductEnteredData(
                                      nuevaFila.sku,
                                      nuevaFila.locationId,
                                      nuevaFila.amount, // Delta completo
                                      nuevaFila.storeId,
                                      nuevaFila.locationId
                                    );
                                    setProductos(nuevosProductos);
                                    // Cancelar la fila editable después de agregar
                                    setNuevaFila(null);
                                    setErrorNuevaFila("");
                                  }}>
                                    ok
                                  </IconButton>
                                  <IconButton color="error" size="small" sx={{ fontSize: "14px" }} onClick={handleCancelarNuevaFila}>
                                    cancelar
                                  </IconButton>
                                </Box>
                              )}

                              {/* Mostrar errores en modo edición */}
                              {filaEditableActiva && errorNuevaFila && (
                                <Box sx={{ mt: 1 }}>
                                  <AlertComponent
                                    color="error"
                                    message={errorNuevaFila}
                                    cleanMessage={() => setErrorNuevaFila("")}
                                    time={3000}
                                  />
                                </Box>
                              )}
                            </Box>
                          </TableCell>


                        </StyledTableRow>

                        {/* Fila editable para nueva ubicación de esta variación */}


                      </React.Fragment>
                    );
                  })
                ) : null}

                {/* Mostrar botones para agregar primera ubicación para variaciones sin ubicaciones */}
                {productos.filter(producto => {
                  // Verificar si esta variación tiene ubicaciones asignadas
                  const tieneUbicaciones = filasTabla.some(fila => fila.producto.internalSku === producto.internalSku);
                  return !tieneUbicaciones;
                }).map((producto, index) => {
                  const filaEditableActiva = nuevaFila && nuevaFila.sku === producto.internalSku;

                  return (
                    <React.Fragment key={producto.internalSku + index}>
                      <StyledTableRow>
                        {/* Marca / Modelo */}
                        <TableCell>{producto.brand} / {producto.model}</TableCell>

                        {/* Variación */}
                        <TableCell>{producto.variationDescription} <br /> ({producto.internalSku})</TableCell>

                        {/* Columnas vacías */}
                        {!filaEditableActiva && (
                          <TableCell colSpan={3}></TableCell>
                        )}

                        {/* Botón para agregar primera ubicación */}
                        {!filaEditableActiva && (
                          <TableCell>
                            <Button
                              variant="outlined"
                              color="buttonGreenPink"
                              size="small"
                              startIcon={<AddCircle />}
                              onClick={() => {
                                // Solo permitir una fila editable a la vez
                                if (nuevaFila && nuevaFila.sku !== producto.internalSku) {
                                  setErrorNuevaFila("Completa o cancela la fila actual antes de agregar otra.");
                                  return;
                                }
                                setNuevaFila({
                                  sku: producto.internalSku,
                                  storeId: almacenes[0]?.id || "",
                                  locationId: "",
                                  amount: 1,
                                  error: "",
                                });
                                setErrorNuevaFila("");
                              }}
                              sx={{ whiteSpace: 'nowrap' }}
                            >
                              {producto.internalSku}
                            </Button>
                          </TableCell>
                        )}

                        {filaEditableActiva && (
                          <>
                            <TableCell>
                              <Select
                                value={nuevaFila.storeId}
                                onChange={e => setNuevaFila({ ...nuevaFila, storeId: e.target.value, locationId: "" })}
                                size="small"
                              >
                                {almacenes.map(a => (
                                  <MenuItem key={a.id} value={a.id}>{a.storeName}</MenuItem>
                                ))}
                              </Select>
                            </TableCell>
                            <TableCell>
                              <Select
                                value={nuevaFila.locationId}
                                onChange={e => setNuevaFila({ ...nuevaFila, locationId: e.target.value, amount: 1 })}
                                size="small"
                                sx={{ width: '150px' }}
                              >
                                {getUbicacionesDisponibles(nuevaFila.sku, nuevaFila.storeId).map(u => (
                                  <MenuItem key={u.id} value={u.id}>{u.entireName}</MenuItem>
                                ))}
                              </Select>
                            </TableCell>
                            <TableCell>
                              <QuantityComponent
                                amount={nuevaFila.amount}
                                totalUnits={totalUnits}
                                index={0}
                                enterIndex={0}
                                handleDecrease={() => {
                                  const newAmount = Math.max(0, nuevaFila.amount - 1);
                                  setNuevaFila({ ...nuevaFila, amount: newAmount });
                                }}
                                handleIncrease={() => {
                                  if (totalPending === 0) return;
                                  const newAmount = nuevaFila.amount + 1;
                                  setNuevaFila({ ...nuevaFila, amount: newAmount });
                                }}
                                handleChangeAmount={(value) => {
                                  const newAmount = Math.max(0, Number(value));
                                  setNuevaFila({ ...nuevaFila, amount: newAmount });
                                }}
                                totalEntered={productFromEntryDocument?.totalEntered + currentlyEntering}
                                totalAmount={nuevaFila.amount}
                              />
                            </TableCell>
                            <TableCell>
                              <Box display="flex" alignItems="center" gap={1}>
                                <IconButton color="success" size="small" onClick={() => {
                                  // Validación
                                  if (!nuevaFila.storeId || !nuevaFila.locationId) {
                                    setErrorNuevaFila("Debes seleccionar almacén y ubicación interna.");
                                    return;
                                  }
                                  if (nuevaFila.amount <= 0) {
                                    setErrorNuevaFila("La cantidad debe ser mayor a cero.");
                                    return;
                                  }
                                  if (nuevaFila.amount > totalPending) {
                                    setErrorNuevaFila(`No puedes asignar más de lo pendiente (${totalPending}).`);
                                    return;
                                  }
                                  // Validar que no se repita la ubicación interna para la variación
                                  const variacion = productos.find(p => p.internalSku === nuevaFila.sku);
                                  const ubicacionesUsadas = variacion.enters.map(e => `${e.storeId}-${e.locationId}`);
                                  if (ubicacionesUsadas.includes(`${nuevaFila.storeId}-${nuevaFila.locationId}`)) {
                                    setErrorNuevaFila("Esta ubicación interna ya está asignada para esta variación.");
                                    return;
                                  }
                                  // Validar que la suma de cantidades no supere el máximo por variación
                                  const sumaActual = variacion.enters.reduce((acc, e) => acc + e.amount, 0);
                                  const faltantes = totalUnits - sumaActual;
                                  if (nuevaFila.amount > faltantes) {
                                    setErrorNuevaFila(`No puedes asignar más de ${faltantes} productos a esta variación.`);
                                    return;
                                  }
                                  // Agregar la nueva ubicación
                                  const nuevosProductos = productos.map(p => {
                                    if (p.internalSku !== nuevaFila.sku) return p;
                                    // Usar las funciones helper existentes para mantener consistencia
                                    const newStores = updateInternalLocationAmountDelta(
                                      createStoresStructure(
                                        p.stores || {},
                                        nuevaFila.storeId,
                                        nuevaFila.locationId,
                                        0 // Inicializar en 0 para luego sumar el delta
                                      ),
                                      nuevaFila.storeId,
                                      nuevaFila.locationId,
                                      nuevaFila.amount // Delta a agregar
                                    );

                                    const newEnters = updateEntersArray(
                                      p.enters,
                                      nuevaFila.storeId,
                                      nuevaFila.locationId,
                                      nuevaFila.amount
                                    );

                                    return {
                                      ...p,
                                      enters: newEnters,
                                      stores: newStores,
                                    };
                                  });

                                  // Actualizar la data del backend con el delta
                                  updateProductEnteredData(
                                    nuevaFila.sku,
                                    nuevaFila.locationId,
                                    nuevaFila.amount, // Delta completo
                                    nuevaFila.storeId,
                                    nuevaFila.locationId
                                  );
                                  setProductos(nuevosProductos);
                                  // Cancelar la fila editable después de agregar
                                  setNuevaFila(null);
                                  setErrorNuevaFila("");
                                }}
                                  sx={{ fontSize: "14px" }}
                                >
                                  ok
                                </IconButton>
                                <IconButton color="error" size="small" sx={{ fontSize: "14px" }} onClick={handleCancelarNuevaFila}>
                                  cancelar
                                </IconButton>
                              </Box>
                              {errorNuevaFila && (
                                <AlertComponent
                                  color="error"
                                  message={errorNuevaFila}
                                  cleanMessage={() => setErrorNuevaFila("")}
                                  time={3000}
                                />
                              )}
                            </TableCell>
                          </>
                        )}

                      </StyledTableRow>



                    </React.Fragment>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Cards para pantallas pequeñas */}
          <Box sx={{
            p: 2,
            display: {
              xs: "block",
              md: "none"
            }
          }}>
            {obtenerProductosOrdenados(productos)?.map((product, index) => {
              // Filtrar solo los enters con amount > 0 y mantener orden estable por enter
              const entersWithAmount = product.enters
                .filter(enter => enter.amount > 0);

              return entersWithAmount.map((enter, enterIndex) => {
                const showProductInfo = enterIndex === 0;

                return (
                  <Card
                    key={`${index}-${enterIndex}`}
                    sx={{
                      mb: 2,
                      borderRadius: '12px',
                      '&:hover': {
                        boxShadow: 3
                      }
                    }}
                    onMouseEnter={() => setHoveredProductIndex(index)}
                    onMouseLeave={() => setHoveredProductIndex(null)}
                  >
                    <CardContent>
                      {/* Información del producto - solo en el primer enter */}
                      {showProductInfo && (
                        <Box sx={{ mb: 2, pb: 2 }}>
                          <Typography variant="h6" component="div" sx={{ mb: 1, fontWeight: 'bold' }}>
                            {product.brand} - {product.model}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {product.internalSku + " " + product.variationDescription}
                          </Typography>
                        </Box>
                      )}

                      {/* Indicador de entrada adicional */}
                      {!showProductInfo && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                            Entrada adicional #{enterIndex + 1}
                          </Typography>
                        </Box>
                      )}

                      {/* Cantidad */}
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" sx={{ mb: 1, fontWeight: 'medium' }}>
                          Cantidad
                        </Typography>
                        <QuantityComponent
                          amount={Number(enter.amount)}
                          totalUnits={totalUnits}
                          index={index}
                          enterIndex={enterIndex}
                          handleDecrease={handleDecrease}
                          handleIncrease={handleIncrease}
                          handleChangeAmount={handleChangeAmount}
                          totalEntered={productFromEntryDocument?.totalEntered + currentlyEntering}
                          totalAmount={totalAmount}
                        />
                      </Box>

                      {/* Almacén */}
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" sx={{ mb: 1, fontWeight: 'medium' }}>
                          Almacén
                        </Typography>
                        <TextField
                          select
                          fullWidth
                          value={showSelectAlmacen ? almacenSelected : enter.storeId}
                          onChange={(event) => handleAlmacenChange(event, index, enterIndex)}
                          disabled={showSelectAlmacen}
                          size="small"
                        >
                          {almacenes.map((almacen) => (
                            <MenuItem key={almacen.id} value={almacen.id}>
                              {almacen.storeName}
                            </MenuItem>
                          ))}
                        </TextField>
                      </Box>

                      {/* Ubicación y botones */}
                      <Box>
                        <Typography variant="body2" sx={{ mb: 1, fontWeight: 'medium' }}>
                          Ubicación
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Select
                            labelId="internal-location-label"
                            required
                            id="internal-location"
                            value={enter.locationId}
                            onChange={(event) => {
                              const newProductos = [...productos];
                              newProductos[index].enters[enterIndex].locationId = event.target.value;
                              setProductos(newProductos);
                            }}
                            sx={{ flexGrow: 1 }}
                            size="small"
                          >
                            {locations?.length > 0 ? (
                              obtenerTodosLosElementos(locations).map((ubicacion) => (
                                <MenuItem key={ubicacion.id} value={ubicacion.id}>
                                  {ubicacion.entireName}
                                </MenuItem>
                              ))
                            ) : (
                              <MenuItem key={0} value={0} disabled>
                                Sin ubicaciones
                              </MenuItem>
                            )}
                          </Select>

                          {/* Botones de acción */}
                          {showProductInfo ? (
                            enterIndex === 0 && (
                              <IconButton
                                variant="contained"
                                color="buttonGreenPink"
                                onClick={() => {
                                  const newProductos = [...productos];
                                  const enters = [...newProductos[index].enters];
                                  enters.push({
                                    amount: 0,
                                    storeId: showSelectAlmacen ? almacenSelected : almacenes[0].id,
                                    locationId: 1,
                                  });
                                  newProductos[index].enters = enters;
                                  setProductos(newProductos);
                                }}
                                disabled={
                                  obtenerTodosLosElementos(
                                    almacenes.find((almacen) =>
                                      almacen.id === (showSelectAlmacen ? almacenSelected : enter.storeId)
                                    )?.locationLevelItems
                                  ).length === productos[index].enters.length
                                }
                                sx={{
                                  minWidth: '40px',
                                  '&:hover': {
                                    bgcolor: 'primary.dark'
                                  }
                                }}
                              >
                                <AddCircle color="buttonGreenPink" />
                              </IconButton>
                            )
                          ) : (
                            enterIndex > 0 && (
                              <IconButton
                                variant="contained"
                                color="buttonGreenPink"
                                onClick={() => {
                                  const newProductos = [...productos];
                                  const enters = [...newProductos[index].enters];
                                  const last = enters[enters.length - 1];
                                  if (enters.length > 1 && last.amount === 0) {
                                    enters.pop();
                                  }
                                  newProductos[index].enters = enters;
                                  setProductos(newProductos);
                                }}
                                sx={{
                                  minWidth: '40px',

                                }}
                              >
                                <RemoveCircle color="buttonGreenPink" />
                              </IconButton>
                            )
                          )}
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                );
              });
            })}
          </Box>
        </>
      ) : (
        <Box sx={{ width: "100%" }}>
          <Skeleton />
          <Skeleton animation="wave" />
          <Skeleton />
        </Box>
      )}
    </Box>
  );
};
