import React from "react";
import CargandoLista from "../CargandoLista";
import { useDispatch, useSelector } from "react-redux";
import TablaDesplegableFacturas from "./TablaDesplegableFacturas";
import { obtenerFacturasFiltradasAccion } from "../../redux/facturasDucks";
import { ManejarErrores } from "../ManejarErrores";
import { SkeletonTables } from "../componentesGenerales/SkeletonTables";

const TablaDesplegableFacturasWrapper = (props) => {
  const entryDocuments = useSelector((store) => store.facturas.facturas);
  const dispatch = useDispatch();
  const proveedor = props.proveedor;
  const offset = props.offset;
  const initialDate = props.initialDate;
  const finalDate = props.finalDate;
  const search = props.search;


  React.useEffect(() => {
    const fetchData = () => {
      dispatch(
        obtenerFacturasFiltradasAccion({
          proveedor: proveedor,
          offsetActual: offset,
          initialDate: initialDate,
          finalDate: finalDate,
          search: search,
        })
      );
    };
    fetchData();
  }, []);

  if (entryDocuments) {
    if (typeof entryDocuments === "string" && entryDocuments.startsWith("status")) {
      return <ManejarErrores errorCode={entryDocuments} />;
    } else {
      return (
        <TablaDesplegableFacturas entryDocuments={entryDocuments} />
        // <h1>JALO</h1>
      );
    }
  }
   else {
    return (
      <>
        <SkeletonTables />
        
      </>
    );
  }
};

export default TablaDesplegableFacturasWrapper;
