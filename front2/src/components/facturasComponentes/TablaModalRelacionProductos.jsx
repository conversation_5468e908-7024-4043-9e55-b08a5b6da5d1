import React from "react";
import Paper from "@mui/material/Paper";
import TableRow from "@mui/material/TableRow";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import Table from "@mui/material/Table";
import TableHead from "@mui/material/TableHead";
import { styled } from "@mui/material/styles";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import {
  Alert,
  Box,
  Chip,
  FormControlLabel,
  IconButton,
  MenuItem,
  Select,
  Skeleton,
  Switch,
} from "@mui/material";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import { Link } from "react-router-dom";
import BuscarProducto from "../productosComponentes/BuscarProducto";
import { useDispatch, useSelector } from "react-redux";
import {
  obtenerProductosFiltradosAccion,
  persistirDatosNuevoProducto,
  resetPersistirDatosNuevoProducto,
  setNewProductOrigin,
} from "../../redux/productosDucks";
import { useEffect } from "react";
import { TablaModalRelacionVariaciones } from "./TablaModalRelacionVariaciones";
import { CheckBox } from "@mui/icons-material";
import { set } from "date-fns";
import { obtenerAlmacenesInternos } from "../../redux/almacenesDucks";
import {
  enlazarProductosConFacturas,
  ingresarProductos,
  limpiarVariaciones,
  obtenerVariaciones,
  setMensaje,
} from "../../redux/facturasDucks";
import { useCookies } from "react-cookie";
import CloseIcon from "@mui/icons-material/Close";
import AssignmentTurnedInIcon from "@mui/icons-material/AssignmentTurnedIn";
import AssignmentLateIcon from "@mui/icons-material/AssignmentLate";
import AssignmentIcon from "@mui/icons-material/Assignment";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardHeader from "@mui/material/CardHeader";
import SearchIcon from "@mui/icons-material/Search";
import WarehouseIcon from '@mui/icons-material/Warehouse';
import Typography from "@mui/material/Typography";
import Stack from "@mui/material/Stack";
import Fade from "@mui/material/Fade";
import { AlertComponent } from "../componentesGenerales/Alert";


const TablaModalRelacionProductos = ({ buscarEnabled, setBuscarEnabled, productEnteredData, setProductEnteredData, currentlyEntering, setCurrentlyEntering, selectedProduct, setSelectedProduct }) => {
  const dispatch = useDispatch();
  const almacen = useSelector((store) => store.almacenes.almacenes);
  const loadingVariations = useSelector(
    (store) => store.facturas.loadingVariations
  );
  const disableSwitch = useSelector((store) => store.facturas.multipleEnters);

  const disableIngresar = useSelector(
    (store) => store.facturas.amountGreaterThanZero
  );

  const jsonToSend = useSelector((store) => store.facturas.jsonToSend);
  const ingresoRecargando = useSelector(
    (store) => store.facturas.ingresoRecargando
  );
  const mensaje = useSelector((store) => store.facturas.mensajeFacturas);
  const [almacenes, setAlmacenes] = React.useState([]);
  const user = useSelector((store) => {
    return store.usuario.user;
  });
  const productos = useSelector((store) => {
    return store.productos.productos;
  });
  const [cookies, setCookie] = useCookies();

  const productFromEntryDocument = useSelector(
    (store) => store.facturas.selectedProduct
  );
  const facturas = useSelector((store) => store.facturas.facturas);
  const productVariations = useSelector(
    (store) => store.facturas.productVariations
  );
  const enlaceRecargando = useSelector(
    (store) => store.facturas.enlaceRecargando
  );
  const facturaSeleccionada = useSelector(
    (store) => store.facturas.facturaSeleccionada
  );


  useEffect(() => {
    setAlmacenes(almacen);
  }, [almacen]);

  const [open, setOpen] = React.useState(false);
  const [options, setOptions] = React.useState([]);
  const [query, setQuery] = React.useState("");
  const [results, setResults] = React.useState([]);
  const [timer, setTimer] = React.useState(null);
  const [value, setValue] = React.useState("");

  const [almacenSelected, setAlmacenSelected] = React.useState(1);
  const [showSelectAlmacen, setShowSelectAlmacen] = React.useState(false);
  const [confirmDisabled, setConfirmDisabled] = React.useState(true);
  const [productsHaveVariations, setProductsHaveVariations] =
    React.useState(false);
  const [brand, setBrand] = React.useState("");
  const [model, setModel] = React.useState("");



  useEffect(() => {
    if (productVariations && productVariations.entries_by_variation?.length > 0) {
      setProductsHaveVariations(true);
    }
    if (productVariations && productVariations.entries_by_variation?.length === 0) {
      setProductsHaveVariations(false);
    }
  }, [productVariations]);

  useEffect(() => {
    if (
      productFromEntryDocument?.productBase === null &&
      productFromEntryDocument?.internalBaseSku === null
    ) {
      setBuscarEnabled(true);
    } else {
      setSelectedProduct(productFromEntryDocument.internalBaseSku);
      setBuscarEnabled(false);
      dispatch(obtenerVariaciones(productFromEntryDocument.id));
    }



  }, [productFromEntryDocument]);

  const getSearch = useSelector((store) => {
    return store.productos.productos;
  });

  let loading = open && getSearch?.length === 0;

  const handleShowSelectAlmacen = (event) => {
    setShowSelectAlmacen(event.target.checked);
  };

  React.useEffect(() => {
    if (productos && productsHaveVariations) {
      productos.map((producto) =>
        producto.internalBaseSku === selectedProduct
          ? setBrand(producto.brand)
          : null
      );
      productos.map((producto) =>
        producto.internalBaseSku === selectedProduct
          ? setModel(producto.model)
          : null
      );
    } else {
      setBrand("");
      setModel("");
    }
  }, [productos, productsHaveVariations]);

  const clickInAlert = () => {
    dispatch(setMensaje(null));
  };

  useEffect(() => {
    loading = true;
    if (getSearch) {
      setResults(getSearch);
      loading = false;
    }
  }, [getSearch, dispatch, loading]);

  const setData = () => {
    let productosBase = [];

    if (results.length > 0) {
      productosBase = [...results];
    }
    return productosBase;
  };

  const handleAlmacenChange = (event) => {
    setAlmacenSelected(event.target.value);
  };

  React.useEffect(() => {
    if (selectedProduct) {
      setConfirmDisabled(false);
    }
    if (selectedProduct === null || undefined) {
      setConfirmDisabled(true);
      setShowSelectAlmacen(false);
      dispatch(limpiarVariaciones());
    }
  }, [selectedProduct]);



  const handleSetInfo = () => {
    dispatch(setNewProductOrigin(true));
    dispatch(resetPersistirDatosNuevoProducto());
    persistirDatosNuevoProducto({
      key: "model",
      value: productFromEntryDocument.model,
    });
    dispatch(
      persistirDatosNuevoProducto({
        key: "description",
        value: productFromEntryDocument.description,
      })
    );
    dispatch(
      persistirDatosNuevoProducto({
        key: "unitCode",
        value: productFromEntryDocument.unitKey,
      })
    );
    dispatch(
      persistirDatosNuevoProducto({
        key: "satCode",
        value: productFromEntryDocument.satKey,
      })
    );
    dispatch(
      persistirDatosNuevoProducto({
        key: "upc",
        value: productFromEntryDocument.upc,
      })
    );
  };


  const handleConfirmar = () => {
    dispatch(
      enlazarProductosConFacturas(
        productFromEntryDocument.id,
        selectedProduct.internalBaseSku,
        facturaSeleccionada,
        cookies.csrf_access_token
      )
    );
  };

  const handleIngresar = () => {
    if (jsonToSend) {

      /*       aqui en ves de json poner el productEnteredData*/
      dispatch(ingresarProductos(productEnteredData, cookies.csrf_access_token, productFromEntryDocument.units - (productFromEntryDocument.totalEntered + currentlyEntering), setCurrentlyEntering));
    } else {
      alert("No hay productos para ingresar");
    }
  };

  const handleChange = (event) => {
    const inputValue = event.target.value;
    setValue(inputValue);
    loading = true;
  };

  useEffect(() => {
    if (!open) {
      setResults([]);
    }
    if (timer) {
      clearTimeout(timer);
    }
    setTimer(
      setTimeout(() => {
        dispatch(
          obtenerProductosFiltradosAccion({
            search: value,
            scope: "products-stores",
          })
        );
      }, 1000)
    );
  }, [value]);


  // Resetear estado local después de ingreso exitoso
  useEffect(() => {
    // Detectar cuando totalEntered cambia (indica éxito en ingreso)
    if (productFromEntryDocument?.totalEntered !== undefined) {
      // Resetear currentlyEntering a 0
      
      // Resetear productEnteredData a su estado inicial
      setProductEnteredData({
        productFromEntryDocumentId: productFromEntryDocument.id || null,
        productsEnteredIntoStore: []
      });
    }
  }, [productFromEntryDocument?.totalEntered]); // Escucha cambios en totalEntered


  return ingresoRecargando ? (
    <Box fullWidth>
      <Skeleton />
      <Skeleton animation="wave" />
      <Skeleton animation={false} />
      <Skeleton animation={false} />
      <Skeleton animation="wave" />
      <Skeleton />
    </Box>
  ) : (
    <Box sx={{ overflow: "auto", width: "100%", padding: "0", maxWidth: "100%" }}>

      {/* si ya hay un producto seleccionado, se muestra el buscador */}
      {buscarEnabled != false && (
        <Box
          display="flex"
          flexDirection="row"
          sx={{ justifyContent: "center", width: "100%", padding: "0" }}
        >

          <Card sx={{ width: '100%', boxShadow: 2, overflow: 'visible', borderRadius: "18px", bgcolor: "background.default" }}>
            <CardHeader
              title="Producto a Relacionar"
              titleTypographyProps={{ variant: 'h6' }}
              avatar={<SearchIcon color="primary" />}
            />

            <CardContent>
              {/* Sección principal */}
              <Box sx={{ mb: 2 }} style={{
                opacity: buscarEnabled ? 1 : 0.5,
                pointerEvents: buscarEnabled ? "auto" : "none",
              }}>
                <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '100%' }}>
                  {/* Buscador */}
                  <Box sx={{ flex: 1 }}>
                    <BuscarProducto setSelectedProduct={setSelectedProduct} />
                  </Box>

                  {/* Switch mejorado */}
                  {selectedProduct && (
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      minWidth: 'fit-content'
                    }}>
                      <Typography variant="caption" sx={{
                        fontSize: '0.75rem',
                        color: 'text.secondary',
                        mb: 0.5,
                        textAlign: 'center'
                      }}>
                        Destino único
                      </Typography>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={showSelectAlmacen}
                            onChange={handleShowSelectAlmacen}
                            disabled={disableSwitch}
                            size="small"
                          />
                        }
                        label=""
                        sx={{ m: 0 }}
                      />
                    </Box>
                  )}
                </Stack>

                {/* Selector de almacén integrado en la misma línea */}
                {showSelectAlmacen && selectedProduct && (
                  <Fade in={true} timeout={300}>
                    <Box sx={{ mt: 2 }}>
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Typography variant="caption" sx={{
                          fontSize: '0.75rem',
                          color: 'text.secondary',
                          minWidth: 'fit-content',
                          fontWeight: 500
                        }}>
                          Almacén:
                        </Typography>
                        <Box sx={{ flex: 1, maxWidth: '300px' }}>
                          <Select
                            id="Almacenes"
                            value={almacenSelected}
                            onChange={handleAlmacenChange}
                            size="small"
                            sx={{
                              width: "100%",
                              '& .MuiOutlinedInput-root': {
                                borderRadius: 1.5,
                              }
                            }}
                            displayEmpty
                          >
                            <MenuItem value="" disabled>
                              <Typography variant="body2" color="text.secondary">
                                Selecciona un almacén
                              </Typography>
                            </MenuItem>
                            {almacenes?.map((almacen) => (
                              <MenuItem key={almacen.id} value={almacen.id}>
                                {almacen.storeName}
                              </MenuItem>
                            ))}
                          </Select>
                        </Box>
                      </Stack>
                    </Box>
                  </Fade>
                )}
              </Box>
            </CardContent>

          </Card>

        </Box>
      )}

      {buscarEnabled === false && (
        <Box sx={{ width: "100%", mt: 2 }}>
          {/* Información del producto */}
          <Card sx={{
            borderRadius: "18px", overflow: 'visible',
            '& .MuiCardContent-root': {
              padding: '16px !important'
            }
          }}>
            <CardContent sx={{ py: 1, px: 2, mb: 0 }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 3,
                flexWrap: 'wrap',
                justifyContent: 'space-between'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <WarehouseIcon color="primary" fontSize="small" />
                  <Typography variant="body2">
                    {productFromEntryDocument.productBase?.brand || 'Sin marca'} / {productFromEntryDocument.productBase?.model || productFromEntryDocument.model}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                    - {productFromEntryDocument.description}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                  <Box>
                    <Typography variant="caption" color="text.secondary">SKU: </Typography>
                    <Typography variant="body2" component="span" sx={{ fontFamily: 'monospace' }}>
                      {productFromEntryDocument.internalBaseSku}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="caption" color="text.secondary">SAT: </Typography>
                    <Typography variant="body2" component="span">
                      {productFromEntryDocument.satKey}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="caption" color="text.secondary">Precio: </Typography>
                    <Typography variant="body2" component="span" sx={{ color: 'primary.main' }}>
                      ${productFromEntryDocument.unitPrice}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>

          {/* Chips de resumen */}
          <Grid container spacing={2} sx={{ display: "flex", justifyContent: "space-evenly", gap: 2, my: 2 }}>
            <Chip
              size="big"
              sx={{ fontSize: "16px" }}
              color="primary"
              icon={<AssignmentTurnedInIcon />}
              label={`Ingresados: ${productFromEntryDocument.totalEntered + currentlyEntering}`}
            />
            <Chip
              size="big"
              sx={{ fontSize: "16px" }}
              color="primary"
              icon={<AssignmentLateIcon />}
              label={`Faltantes: ${productFromEntryDocument.units - (productFromEntryDocument.totalEntered + currentlyEntering)}`}
            />

            <Chip
              size="big"
              sx={{ fontSize: "16px" }}
              color="primary"
              icon={<AssignmentIcon />}
              label={`Total: ${productFromEntryDocument.units}`}
            />
          </Grid>
        </Box>
      )}

      {buscarEnabled === false ? (
        loadingVariations ? (
          <Skeleton sx={{ width: '100%', height: '400px', borderRadius: '18px' }}  component={Paper} animation="wave" />
        ) : (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              
          
              <TablaModalRelacionVariaciones
                almacenes={almacenes}
                showSelectAlmacen={showSelectAlmacen}
                almacenSelected={almacenSelected}
                selectedProduct={selectedProduct}
                productFromEntryDocument={productFromEntryDocument}
                productVariations={productVariations}
                brand={brand}
                model={model}
                productEnteredData={productEnteredData}
                setProductEnteredData={setProductEnteredData}
                setCurrentlyEntering={setCurrentlyEntering}
                currentlyEntering={currentlyEntering}
              />
            
          </Grid>
        </Grid>
        )
      ) : null}
      {/*  <Box display="flex" flexDirection="row" sx={{ justifyContent: "center" }}>
        <Grid item width={210.4} sx={{ margin: "20px 10px" }}>
          <Button
            component={Link}
            to="/productos/nuevo"
            variant="outlined"
            color="buttonGreenPink"
            fullWidth
            onClick={handleSetInfo}
          >
            Agregar Producto
          </Button>
        </Grid>
        <Grid item width={210.4} sx={{ margin: "20px 10px" }}>
          {buscarEnabled === false ? (
            <Button
              variant="contained"
              color="buttonGreenPink"
              fullWidth
              disabled={!disableIngresar}
              onClick={handleIngresar}
            >
              Ingresar
            </Button>
          ) : (
            <Button
              variant="contained"
              color="buttonGreenPink"
              disabled={confirmDisabled}
              fullWidth
              onClick={handleConfirmar}
            >
              Confirmar
            </Button>
          )}
        </Grid>
      </Box> */}
      {/* Mostrar alerta siempre al final */}
      {mensaje && (
          <AlertComponent
            color={mensaje.severity}
            message={mensaje.mensaje}
            cleanMessage={clickInAlert}
            time={5000}
          />
        )}
    </Box>
  );
};

export default TablaModalRelacionProductos;

/*  */
