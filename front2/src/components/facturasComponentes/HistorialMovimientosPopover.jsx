import React, { useState } from "react";
import {
  <PERSON>over,
  IconButton,
  Typography,
  Box,
  Divider,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import { History } from "@mui/icons-material";
import { useCustomDateTime } from "../menusYBarrasComponentes/HookDate";
import { useTheme } from "@emotion/react";

const MovimientoItem = ({ movimiento }) => {
  const date = useCustomDateTime(movimiento.timeStamp);
  const fecha = date.adjustedDateTime;
  const theme = useTheme();
  
  const isPositive = movimiento.amount > 0;
  const amountColor = isPositive ? "success.main" : "error.main";
  const amountPrefix = isPositive ? "+" : "";

  return (
    <Box sx={{ mb: 2, bgcolor: theme.palette.background.default, borderRadius: 4, padding: 1, minWidth: '150px' }}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="subtitle1" fontWeight="bold" color="colorGreenPink">
          {movimiento.user.name}
        </Typography>
        <Typography 
          variant="subtitle1" 
          fontWeight="bold" 
          color={amountColor}
          sx={{ 
            fontSize: '1.1rem',
            bgcolor: isPositive ? 'success.light' : 'error.light',
            color: isPositive ? 'success.contrastText' : 'error.contrastText',
            px: 1,
            py: .3,
            borderRadius: 100,
            opacity: 0.9
          }}
        >
          {amountPrefix}{movimiento.amount}
        </Typography>
      </Box>
      <Typography variant="body2" color="colorGreenPink" sx={{ mt: 0.5 }}>
        {fecha}
      </Typography>
    </Box>
  );
};

export const HistorialMovimientosPopover = ({ historialMovimientos, onClick, nameLocation, loadingHistorialMovimientos }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleHistorialClick = (event) => {
    setAnchorEl(event.currentTarget);
    if (onClick) {
      onClick();
    }
  };

  const handleClosePopover = () => {
    setAnchorEl(null);
  };

  const showHistorial = Boolean(anchorEl);

  return (
    <div>
      <Tooltip title="Ver historial de movimientos">
        <IconButton 
          color="buttonGreenPink" 
          size="small" 
          onClick={handleHistorialClick}
        >
          <History />
        </IconButton>
      </Tooltip>

      <Popover
        open={showHistorial}
        anchorEl={anchorEl}
        onClose={handleClosePopover}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
        transformOrigin={{ vertical: "top", horizontal: "left" }}
        sx={{
          "& .MuiPopover-paper": {
            borderRadius: "20px",
            boxShadow: 3,
          },
        }}
      >
        <Box sx={{ padding: "10px", maxWidth: "400px", maxHeight: "500px", minWidth: "250px", overflowY: "auto" }}>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, textAlign: "center", fontSize: "1.2rem" }}>
            Movimientos <br /> {nameLocation}
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          {loadingHistorialMovimientos ? (
            <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", py: 4 }}>
              <CircularProgress size={40} />
            </Box>
          ) : historialMovimientos && historialMovimientos.direct_inventory_movements ? (
            historialMovimientos.direct_inventory_movements.length > 0 ? (
              historialMovimientos.direct_inventory_movements.map((movimiento) => (
                <MovimientoItem
                  key={movimiento.id}
                  movimiento={movimiento}
                />
              ))
            ) : (
              <Typography variant="body2" sx={{ textAlign: "center", py: 2 }}>
                No hay movimientos registrados
              </Typography>
            )
          ) : (
            <Typography variant="body2" sx={{ textAlign: "center", py: 2 }}>
              Haz clic en el ícono para cargar el historial
            </Typography>
          )}
        </Box>
      </Popover>
    </div>
  );
}; 