import React, { useEffect, useRef } from "react";

import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import { crearFechaFormato } from "../../Utils/atributtesHandler";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import { useCookies } from "react-cookie";
import { useDispatch, useSelector } from "react-redux";
import { alpha, styled } from "@mui/material/styles";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import TableRow from "@mui/material/TableRow";
import TableContainer from "@mui/material/TableContainer";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableHead from "@mui/material/TableHead";
import { getProviderListPropertyUsingIssuerRFC } from "../../Utils/atributtesHandlerSupplier";
import {
  // cambiarCommentsDeUnaFacturaTabla,
  facturaSeleccionada,
  limpiarFacturaSeleccionada,
  limpiarVariaciones,
  setMensaje,
} from "../../redux/facturasDucks";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import ReportIcon from "@mui/icons-material/Report";
import DisabledByDefaultIcon from "@mui/icons-material/DisabledByDefault";
import InfoIcon from "@mui/icons-material/Info";
import Chip from "@mui/material/Chip";
import Divider from "@mui/material/Divider";


import ModalBuscarProducto from "../productosComponentes/ModalBuscarProducto";
import { IconButton, Stack } from "@mui/material";
import Dialog from "@mui/material/Dialog";
import { setSelectedProduct } from "../../redux/facturasDucks";
import { ListComents } from "../ventasComponentes/ListComents";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { NewComment } from "../ventaDirectaComponentes/NewComment";
import { StyledTableCell, StyledTableRow } from "../StyledTableComponents";
import { rateToPercent } from "../../Utils/generalFunctions";
import { obtenerAlmacenesInternos } from "../../redux/almacenesDucks";
import { useTheme } from "@emotion/react";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import { CardProduct } from "./tablaFacturasComponentes/CardProduct";
import StatusIcon from './tablaFacturasComponentes/StatusIcon';

const Row = ({ entryDocument, productInEntryDocument, setOpenP, setOpen }) => {

  const [cookies, setCookie] = useCookies();

  const dispatch = useDispatch();
  let role = useSelector((store) => store.usuario.role);
  let user = useSelector((store) => store.usuario.info);
  const selectedProduct = useSelector(
    (store) => store.facturas.selectedProduct
  );

  let ivaRate,
    precioUnitarioSinIva,
    ivaPorProducto,
    suma,
    total = 0;

  const handleIconStatusPClick = () => {
    dispatch(setSelectedProduct(productInEntryDocument));
    setOpenP(true);
    dispatch(facturaSeleccionada(entryDocument.id));
  };

  if (user.role.roleName != "Logistics_employee") {
    ivaRate = parseFloat(
      productInEntryDocument.productInEntryDocumentTaxes[0]?.taxRate || 16
    );
    precioUnitarioSinIva = parseFloat(productInEntryDocument.unitPrice);
    ivaPorProducto = parseFloat(productInEntryDocument.unitPrice) * rateToPercent(ivaRate);
    suma = parseFloat(productInEntryDocument.unitPrice) + ivaPorProducto;
    total = suma * parseFloat(productInEntryDocument.units);
  }


  return (
    <React.Fragment>

      <StyledTableRow key={entryDocument.id}>
        <StyledTableCell align="center" sx={{ display: { xss: 'table-cell', sm: 'none' } }}>
          {`${productInEntryDocument.units ? productInEntryDocument.units : ""} / ${productInEntryDocument.unitKey ? productInEntryDocument.unitKey : ""}`}
          {productInEntryDocument.satKey}
          {`${productInEntryDocument.supplierSku ? productInEntryDocument.supplierSku : ""} / ${productInEntryDocument.model ? productInEntryDocument.model : ""}`}
        </StyledTableCell>

        <StyledTableCell align="center" sx={{ display: { xss: 'none', sm: 'table-cell' } }}>
          {`${productInEntryDocument.units ? productInEntryDocument.units : ""} / ${productInEntryDocument.unitKey ? productInEntryDocument.unitKey : ""}`}
        </StyledTableCell>
        <StyledTableCell align="center" sx={{ display: { xss: 'none', sm: 'table-cell' } }}>{productInEntryDocument.satKey}</StyledTableCell>
        <StyledTableCell align="center" sx={{ display: { xss: 'none', sm: 'table-cell' } }}>
          {`${productInEntryDocument.supplierSku ? productInEntryDocument.supplierSku : ""} / ${productInEntryDocument.model ? productInEntryDocument.model : ""}`}
        </StyledTableCell>
        <StyledTableCell align="center">
          {productInEntryDocument.description}
        </StyledTableCell>
        {/* datos monetarios */}
        {user.role.roleName != "Logistics_employee" ? (
          <>
            <StyledTableCell align="center" sx={{ display: { xss: 'none', md: 'table-cell' } }}>
              ${precioUnitarioSinIva?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </StyledTableCell>
            <StyledTableCell align="center" sx={{ display: { xss: 'none', md: 'table-cell' } }}>${ivaPorProducto?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</StyledTableCell>
            <StyledTableCell align="center" sx={{ display: { xss: 'none', md: 'table-cell' } }}>${suma?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</StyledTableCell>
            <StyledTableCell align="center" sx={{ display: { xss: 'none', md: 'table-cell' } }}>${total?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</StyledTableCell>

            {/* para pantallas chicas */}
            <StyledTableCell align="center" sx={{ display: { xss: 'table-cell', md: 'none' } }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Precios:</Typography>
                <Typography variant="caption">Unit: ${precioUnitarioSinIva?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</Typography>
                <Typography variant="caption">IVA: ${ivaPorProducto?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</Typography>
                <Typography variant="caption">Suma: ${suma?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</Typography>
                <Typography variant="caption">Total: ${total?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</Typography>
              </Box>
            </StyledTableCell>

            <StyledTableCell align="center">
              <StatusIcon
                productInEntryDocument={productInEntryDocument}
                user={user}
                onClick={() => handleIconStatusPClick({ productInEntryDocument })}
              />
            </StyledTableCell>
          </>
        ) : null}
      </StyledTableRow>
    </React.Fragment>
  );
};


const DetalleDesplegableFactura = ({ entryDocument, totalFactura, subtotal, totalIvas, addNewCommentFunction, comments, deleteComment, updateComment }) => {
  const dispatch = useDispatch();
  const [openP, setOpenP] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const user = useSelector((store) => store.usuario.info);
  const [isAddingComment, setIsAddingComment] = React.useState(false);

  const calculatePrices = (productInEntryDocument) => {
    if (user.role.roleName === "Logistics_employee") return null;

    const ivaRate = parseFloat(
      productInEntryDocument.productInEntryDocumentTaxes[0]?.taxRate || 16
    );
    const precioUnitarioSinIva = parseFloat(productInEntryDocument.unitPrice);
    const ivaPorProducto = parseFloat(productInEntryDocument.unitPrice) * rateToPercent(ivaRate);
    const suma = parseFloat(productInEntryDocument.unitPrice) + ivaPorProducto;
    const total = suma * parseFloat(productInEntryDocument.units);

    return {
      precioUnitarioSinIva,
      ivaPorProducto,
      suma,
      total
    };
  };

  const handleCloseP = () => {
    setOpenP(false);
    dispatch(setMensaje(null));
    dispatch(limpiarVariaciones());
    dispatch(limpiarFacturaSeleccionada());
  };

  const role = useSelector((store) => store.usuario.role);

  useEffect(() => {
    dispatch(obtenerAlmacenesInternos("internalLocations"));
  }, []);

  const proveedorName = getProviderListPropertyUsingIssuerRFC(
    entryDocument.issuerRfc,
    "providerName"
  );

  const theme = useTheme();

  const handleIconStatusPClick = ({ productInEntryDocument }) => {
    dispatch(setSelectedProduct(productInEntryDocument));
    setOpenP(true);
    dispatch(facturaSeleccionada(entryDocument.id));
  };

  const hasMultipleProducts = entryDocument.productsInEntryDocument.length > 1;



  return (
    <React.Fragment>

      {/* Tabla de productos de facturas */}

      <TableContainer style={{
        borderRadius: "20px",
        /* '& .MuiTableCell-root': {
          padding: '0px !important',
        }, */

      }} sx={{
        display: {
          xss: "none",
          md: "block",
        }
      }}>
        <Table aria-label="sticky table">
          <TableHead>
            <StyledTableRow>
              <StyledTableCell align="center" sx={{ padding: '8px', display: { xss: 'none', sm: 'table-cell' } }}>Cantidad / Unidad</StyledTableCell>
              <StyledTableCell align="center" sx={{ padding: '8px', display: { xss: 'none', sm: 'table-cell' } }}>Clave SAT</StyledTableCell>
              <StyledTableCell align="center" sx={{ padding: '8px', display: { xss: 'none', sm: 'table-cell' } }}>SKU / Modelo</StyledTableCell>
              <StyledTableCell align="center" sx={{ padding: '8px', display: { xss: 'table-cell', sm: 'none' } }}>Información</StyledTableCell>
              <StyledTableCell align="center" sx={{ padding: '8px' }}>Descripción</StyledTableCell>

              {user.role.roleName != "Logistics_employee" ? (
                <>
                  <StyledTableCell align="center" sx={{ padding: '8px', display: { xss: 'none', md: 'table-cell' } }}>P. Unit.</StyledTableCell>
                  <StyledTableCell align="center" sx={{ padding: '8px', display: { xss: 'none', md: 'table-cell' } }}>IVA</StyledTableCell>
                  <StyledTableCell align="center" sx={{ padding: '8px', display: { xss: 'none', md: 'table-cell' } }}>Suma</StyledTableCell>
                  <StyledTableCell align="center" sx={{ padding: '8px', display: { xss: 'none', md: 'table-cell' } }}>Total</StyledTableCell>
                  {/* para pantallas chicas */}
                  <StyledTableCell align="center" sx={{ padding: '8px', display: { xss: 'table-cell', md: 'none' } }}>Operaciones</StyledTableCell>

                  <StyledTableCell align="center" sx={{ padding: '8px' }}>Status</StyledTableCell>
                </>
              ) : null}
            </StyledTableRow>
          </TableHead>
          <TableBody>
            {entryDocument.productsInEntryDocument.map((productInEntryDocument) => {
              return (
                <Row
                  entryDocument={entryDocument}
                  productInEntryDocument={productInEntryDocument}
                  setOpenP={setOpenP}
                  setOpen={setOpen}
                />
              );
            })}

            <StyledTableRow>
              <StyledTableCell colSpan="100%" sx={{ border: 'none', py: 1 }}>
                {user.role.roleName !== 'Logistics_employee' && (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      pr: 2,
                    }}
                  >
                    <Stack
                      direction="row"
                      alignItems="center"
                      spacing={3}
                      sx={{
                        backgroundColor: 'rgba(0,0,0,0.02)',
                        borderRadius: 1,
                        p: 1,
                      }}
                    >
                      {/* Subtotal */}
                      <Typography variant="body2" color="text.secondary">
                        Subtotal:&nbsp;
                        <Typography component="span" variant="body2" fontWeight="medium" color="text.primary">
                          ${subtotal.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </Typography>
                      </Typography>

                      {/* IVA */}
                      <Typography variant="body2" color="text.secondary">
                        IVA:&nbsp;
                        <Typography component="span" variant="body2" fontWeight="medium" color="text.primary">
                          ${totalIvas.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </Typography>
                      </Typography>

                      {/* Divider vertical */}
                      <Divider orientation="vertical" flexItem />

                      {/* Total */}
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          borderRadius: 1,
                        }}
                      >
                        <Typography variant="body2" fontWeight="bold" >
                          Total:&nbsp;${totalFactura.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </Typography>
                      </Box>
                    </Stack>
                  </Box>
                )}
              </StyledTableCell>
            </StyledTableRow>


          </TableBody>
        </Table>
      </TableContainer>


      <Box
        sx={{
          display: { xss: 'grid', md: 'none' },
          width: "100%",
          gridTemplateColumns: {
            xss: '100%',
            sm: hasMultipleProducts
              ? 'repeat(auto-fit, minmax(250px, 1fr))'
              : '1fr'
          },
          gap: 2,
          py: 1,
          maxWidth: '100%',
          margin: "auto",
          justifyItems: hasMultipleProducts ? 'stretch' : 'center',

          // La card total siempre ocupa el ancho completo solo si hay múltiples productos
          ...(hasMultipleProducts && {
            '& > *:last-child': {
              gridColumn: '1 / -1',
              mt: 1,
              maxWidth: '400px',
              justifySelf: 'center',
            }
          }),

          // Para un solo producto, aplicar separación entre cards
          ...(!hasMultipleProducts && {
            '& > *:last-child': {
              mt: 2, // Mayor separación visual
            }
          }),

          // Animaciones
          '& > *': {
            animation: 'slideInScale 0.4s ease-out',
            animationFillMode: 'both',
          },
          '@keyframes slideInScale': {
            '0%': {
              opacity: 0,
              transform: 'scale(0.9) translateY(10px)',
            },
            '100%': {
              opacity: 1,
              transform: 'scale(1) translateY(0)',
            },
          },
        }}
      >
        {entryDocument.productsInEntryDocument.map((productInEntryDocument, index) => {
          const prices = calculatePrices(productInEntryDocument);
          return (
            <CardProduct
              key={productInEntryDocument.id}
              productInEntryDocument={productInEntryDocument}
              user={user}
              prices={prices}
              handleIconStatusPClick={handleIconStatusPClick}
            />
          );
        })}

        {/* Card de total con separación visual */}
        <CardProduct
          key={`total-${entryDocument.id}`}
          isFinal={true}
          totalFactura={totalFactura}
          subtotal={subtotal}
          totalIvas={totalIvas}
        />
      </Box>

      {/* Tabla de productos de facturas */}
      <ListComents
        comments={comments}
        deleteComment={deleteComment}
        updateComment={updateComment}
      />
      {isAddingComment ? (
        <NewComment
          comments={comments}
          commentType={'invoice'}
          addNewCommentFunction={addNewCommentFunction}
          setIsAddingComment={setIsAddingComment}
        />
      ) : (
        <Grid container spacing={2}>
          <Grid item sx={{ textAlign: "right" }}>
            <Button
              // fullWidth
              variant="contained"
              size="small"
              onClick={() => setIsAddingComment(true)}
            // disabled={role !== "Admin"} // Desactivar botón de agregar comentario si el rol no es 'Admin'
            >
              <AddCircleOutlineIcon />
              Comentario
            </Button>
          </Grid>
        </Grid>
      )}
      <ModalBuscarProducto
        openP={openP}
        handleCloseP={handleCloseP}
        setOpenP={setOpenP}
      />
    </React.Fragment>
  );
};

export default DetalleDesplegableFactura;