import React from 'react';
import { IconButton, Tooltip } from '@mui/material';
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { alpha } from '@mui/material/styles';

const StatusIcon = ({ productInEntryDocument, user, onClick }) => {
  const isDisabled = user.role.roleName === "Logistics_employee" || user.role.roleName === "Administrative_accountant";

  if (productInEntryDocument.units > productInEntryDocument.totalEntered) {
    return (
      <Tooltip title="Producto incompleto">
        <IconButton
          onClick={onClick}
          disabled={isDisabled}
          size="small"
          sx={{
            '&:hover': {
              backgroundColor: alpha(isDisabled ? '#9e9e9e' : '#fbc02d', 0.1)
            }
          }}
        >
          <ErrorOutlineIcon
            fontSize="small"
            sx={{
              color: isDisabled ? "#9e9e9e" : "#fbc02d",
            }}
          />
        </IconButton>
      </Tooltip>
    );
  } else if (productInEntryDocument.units === productInEntryDocument.totalEntered) {
    return (
      <Tooltip title="Producto completo">
        <IconButton
          disabled={isDisabled}
          size="small"
          sx={{
            '&:hover': {
              backgroundColor: alpha('#4caf50', 0.1)
            }
          }}
        >
          <CheckCircleOutlineIcon
            fontSize="small"
            sx={{
              color: "#4caf50",
            }}
          />
        </IconButton>
      </Tooltip>
    );
  } else  {
    return (
      <Tooltip title="Producto no encontrado">
        <IconButton
          disabled={isDisabled}
          onClick={onClick}
          size="small"
          sx={{
            '&:hover': {
              backgroundColor: alpha(isDisabled ? '#9e9e9e' : '#d32f2f', 0.1)
            }
          }}
        >
          <CancelOutlinedIcon
            fontSize="small"
            sx={{
              color: isDisabled ? "#9e9e9e" : "#d32f2f",
            }}
          />
        </IconButton>
      </Tooltip>
    );
  }

  return null;
};

export default StatusIcon; 