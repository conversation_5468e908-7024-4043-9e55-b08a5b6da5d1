import { useTheme } from "@emotion/react";
import DetalleDesplegableFactura from "../DetalleDesplegableFactura"
import { Typography, Box, Grid2 } from "@mui/material";
import { crearFechaFormato } from "../../../Utils/atributtesHandler";

export const BreakDownInvoices = ({ entryDocument, type, pedido, user, totalFactura, subtotal, totalIvas, addNewCommentFunction, comments, deleteComment, updateComment }) => {

    const theme = useTheme();

    const styleProp = {
        fontSize: 14,
        color: theme => theme.palette.text.greenGrey,
        marginBottom: 1.5,
        fontWeight: "400",
    };

    return (
        <Box sx ={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            
        <Box
            sx={{
                width: "100%",
                backgroundColor: (theme) => theme.palette.background.default,
                padding: "1em",
                borderRadius: "20px",
                overflow: "hidden",
                "& .MuiGrid2-item": {
                    margin: "auto",
                },
            }}
        >
            <Grid2
                container
                spacing={2}
                sx={{
                    justifyContent: { xs: 'flex-start', sm: 'space-around' },
                    flexDirection: { xs: 'column', sm: 'row' },
                    alignItems: { xs: 'flex-start', sm: 'center' }
                }}
            >
                {/* Primera columna */}
                <Grid2 item xs={12} sm={6} md={3}>
                    <Typography sx={{ fontSize: 14, marginBottom: 1 }}>
                        <span style={styleProp}>Pedido con ID: </span>
                        32
                    </Typography>
                    <Typography sx={{ fontSize: 14, marginBottom: 1 }}>
                        <span style={styleProp}>Fecha de Factura: </span>
                        {crearFechaFormato(entryDocument.issueDate)}
                    </Typography>
                    <Typography sx={{ fontSize: 14, marginBottom: 1 }}>
                        <span style={styleProp}>Proveedor: </span>
                        {entryDocument.issuerRfc}
                    </Typography>
                </Grid2>

                {/* Segunda columna */}
                <Grid2 item xs={12} sm={6} md={3}>
                    <Typography sx={{ fontSize: 14, marginBottom: 1 }}>
                        <span style={styleProp}>Metodo de Pago: </span>
                        {entryDocument.paymentMethod}
                    </Typography>
                    <Typography sx={{ fontSize: 14, marginBottom: 1 }} component="div">
                        <span style={styleProp}>Ejecutivo: </span>
                        {entryDocument.salesRep}
                    </Typography>
                </Grid2>
            </Grid2>
            
        </Box>

        {user.role.roleName != "Logistics_employee" ? (
            // entramos a despliege de facturciones
            <DetalleDesplegableFactura
              entryDocument={entryDocument}
              totalFactura={totalFactura}
              subtotal={subtotal}
              totalIvas={totalIvas}
              addNewCommentFunction={addNewCommentFunction}
              comments={comments}
              deleteComment={deleteComment}
              updateComment={updateComment}
            />
          ) : (
            <DetalleDesplegableFactura
              entryDocument={entryDocument}
              addNewCommentFunction={addNewCommentFunction}
              comments={comments}
              deleteComment={deleteComment}
              updateComment={updateComment}
            />
          )}
    </Box>


    )
}