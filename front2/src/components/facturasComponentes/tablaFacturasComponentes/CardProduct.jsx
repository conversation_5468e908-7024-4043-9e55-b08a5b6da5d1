import React from 'react'
import { Card, CardContent, Typography, Box } from '@mui/material'
import { useTheme } from '@mui/material/styles';
import StatusIcon from './StatusIcon';
import { Divider } from '@mui/material';


export const CardProduct = ({
    productInEntryDocument,
    user,
    prices,
    handleIconStatusPClick,
    isFinal = false,
    totalFactura,
    subtotal,
    totalIvas
}) => {
    const theme = useTheme();

    // Caso: TARJETA DE PRODUCTO
    if (!isFinal) {
        return (
            <Card
                sx={{
                    minWidth: 200,
                    width: '100%',
                    m: 'auto',
                    p: 0,
                    mb: 1.5,
                    borderRadius: '20px',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.25)',
                }}
            >
                <CardContent
                    sx={{
                        p: 1.5,
                        '&:last-child': { pb: 1.5 },
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 1.5,
                    }}
                >
                    {/* Descripción */}
                    <Box sx={{ minHeight: '48px', display: 'flex', flexDirection: 'column' }}>
                        <Typography
                            variant="body2"
                            sx={{
                                fontSize: '0.75rem',
                                color: theme.palette.text.secondary,
                                lineHeight: 1.4,
                                minHeight: '32px',
                                overflow: 'hidden',
                            }}
                        >
                            {productInEntryDocument.description || ''}
                        </Typography>
                    </Box>

                    {/* Detalles */}
                    <Box
                        sx={{
                            p: 1.5,
                            bgcolor: theme.palette.background.default,
                            borderRadius: '20px',
                        }}
                    >
                        <Typography
                            variant="caption"
                            sx={{
                                fontWeight: 700,
                                mb: 1,
                                fontSize: '0.75rem',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                gap: 1,
                            }}
                        >
                            Detalles:
                            <StatusIcon
                                productInEntryDocument={productInEntryDocument}
                                user={user}
                                onClick={() => handleIconStatusPClick({ productInEntryDocument })}
                            />
                        </Typography>

                        <Box
                            sx={{
                                display: 'grid',
                                gridTemplateColumns: '1fr 1fr',
                                gap: 0.5,
                                fontSize: '0.6875rem',
                            }}
                        >
                            <Typography variant="caption" sx={{ fontWeight: 400 }}>
                                Cant/Unidad:
                            </Typography>
                            <Typography
                                variant="caption"
                                sx={{ fontWeight: 500, textAlign: 'right' }}
                            >
                                {`${productInEntryDocument.units || ''} / ${productInEntryDocument.unitKey || ''}`}
                            </Typography>

                            <Typography variant="caption" sx={{ fontWeight: 400 }}>
                                SAT:
                            </Typography>
                            <Typography
                                variant="caption"
                                sx={{ fontWeight: 500, textAlign: 'right' }}
                            >
                                {productInEntryDocument.satKey}
                            </Typography>

                            <Typography variant="caption" sx={{ fontWeight: 400 }}>
                                SKU/Modelo:
                            </Typography>
                            <Typography
                                variant="caption"
                                sx={{ fontWeight: 500, textAlign: 'right', wordBreak: 'break-all' }}
                            >
                                {`${productInEntryDocument.supplierSku || ''} / ${productInEntryDocument.model || ''}`}
                            </Typography>
                        </Box>
                    </Box>

                    {/* Precios (solo si tiene permiso) */}
                    {user.role.roleName !== 'Logistics_employee' && prices && (
                        <Box
                            sx={{
                                p: 1.5,
                                bgcolor: theme.palette.background.default,
                                borderRadius: '20px',
                            }}
                        >
                            <Typography
                                variant="caption"
                                sx={{ fontWeight: 700, mb: 1, fontSize: '0.75rem' }}
                            >
                                Precios:
                            </Typography>

                            <Box
                                sx={{
                                    display: 'grid',
                                    gridTemplateColumns: '1fr 1fr',
                                    gap: 0.5,
                                    mb: 1,
                                }}
                            >
                                <Typography variant="caption" sx={{ fontWeight: 400 }}>
                                    Unit: ${prices.precioUnitarioSinIva?.toFixed(2)}
                                </Typography>
                                <Typography
                                    variant="caption"
                                    sx={{ fontWeight: 400, textAlign: 'right' }}
                                >
                                    IVA: ${prices.ivaPorProducto?.toFixed(2)}
                                </Typography>

                                <Typography variant="caption" sx={{ fontWeight: 400 }}>
                                    Suma: ${prices.suma?.toFixed(2)}
                                </Typography>
                                <Typography
                                    variant="caption"
                                    sx={{
                                        fontWeight: 700,
                                        fontSize: '0.75rem',
                                        textAlign: 'right',
                                        color: theme.palette.primary.main,
                                    }}
                                >
                                    Total: ${prices.total?.toFixed(2)}
                                </Typography>
                            </Box>
                        </Box>
                    )}
                </CardContent>
            </Card>
        );
    }

    // Caso: TARJETA “TOTAL GLOBAL”
    return (
        <Card
            sx={{
                minWidth: 200,
                width: '100%',
                m: 'auto',
                p: 0,
                mb: 1.5,
                borderRadius: '20px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.25)',
            }}
        >
            <CardContent
                sx={{
                    p: 1.5,
                    '&:last-child': { pb: 1.5 },
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1.5,
                }}
            >
                <Typography
                    variant="body2"
                    sx={{
                        fontWeight: 700,
                        fontSize: '0.875rem',
                        color: 'text.primary',
                    }}
                >
                    Total Global
                </Typography>

                <Box
                    sx={{
                        display: 'grid',
                        gridTemplateColumns: '1fr auto',
                        gap: 0.5,
                        fontSize: '0.75rem',
                        p: 1.5,
                        bgcolor: theme.palette.background.default,
                        borderRadius: '20px',
                    }}
                >
                    {/* Subtotal */}
                    <Typography variant="caption" color="text.secondary">
                        Subtotal:
                    </Typography>
                    <Typography variant="caption" fontWeight={500} textAlign="right">
                        ${subtotal.toLocaleString('es-MX', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                        })}
                    </Typography>

                    {/* IVA */}
                    <Typography variant="caption" color="text.secondary">
                        IVA:
                    </Typography>
                    <Typography variant="caption" fontWeight={500} textAlign="right">
                        ${totalIvas.toLocaleString('es-MX', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                        })}
                    </Typography>

                    {/* Separador */}
                    <Divider sx={{ gridColumn: '1 / -1', my: 1 }} />

                    {/* Total */}
                    <Typography
                        variant="subtitle2"
                        fontWeight="bold"
                        sx={{ gridColumn: '1', mt: 1, mb: 1 }}
                    >
                        Total:
                    </Typography>
                    <Typography
                        variant="subtitle2"
                        fontWeight="bold"
                        color="primary.main"
                        sx={{ gridColumn: '2', textAlign: 'right', mt: 1, mb: 1 }}
                    >
                        ${totalFactura.toLocaleString('es-MX', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                        })}
                    </Typography>

                    {/* Separador final (opcional) */}
                    <Divider sx={{ gridColumn: '1 / -1', my: 1 }} />
                </Box>
            </CardContent>
        </Card>
    );
};
