import React, { Fragment } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import DragAndDropFileUpdater from "./DragAndDropFileUpdater";

import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import ConsultarFacturas from "./ConsultarFacturas";
import { TitleModule } from "../componentesGenerales/TitleModule";
import FormularioEntradaManual from '../facturasComponentes/EntradaManual/FormularioEntradaManual';
import FacturaWrapper from "./EntradaManual/FacturaWrapper";

const Item = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  textAlign: "center",
}));
const FacturasMenu = () => {
  return (
    <Fragment>
     <TitleModule title="Módulo de Facturas"/>
      <Routes>
        <Route path="/manual" element={<FacturaWrapper/>}/>
        <Route path="/manual/:idFactura" element={<FacturaWrapper/>}/>
        <Route path="/subir" element={<DragAndDropFileUpdater />} />
        <Route path="/consultar" element={<ConsultarFacturas />} />
      </Routes>
    </Fragment>
  );
};

export default FacturasMenu;
