import React from 'react'
import tortuga from "./img/tortuga-animada.gif"
import { Box, CircularProgress } from '@mui/material'

const CargandoLista = () => {
  return (
    <Box display="flex" justifyContent="center" alignItems="center">
      <CircularProgress size={100}  color="buttonGreenPink"/>
    </Box>
    // <div className="container">
    //     <div className="row">
    //       <div style={{ height: '100%', width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
    //         <img src={tortuga} alt="" className="img-fluid" />
    //       </div>
    //     </div>
    //     <div className="row">
    //       <div style={{ height: '100%', width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
    //         <h2> Espera por favor ...</h2>
    //       </div>
    //     </div>
    //   </div>
  )
}

export default CargandoLista