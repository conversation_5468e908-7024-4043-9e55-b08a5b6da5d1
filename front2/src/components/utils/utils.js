import { instanceAxios } from "../../redux/axiosInstance";

export const noComment = { comment: 'Sin comentarios', id: null, timeStamp: null, userName: null }

export const returnCommentTypeUrl = (commentType, operationType) => {
  const order = 'orders/order'
  const direct_sale = 'directSales/directSale'
  const invoice = 'invoices/invoice'
  const updateC = 'comment'
  const deleteC = 'comment/orderStackableCommentDeleted'
  const addC = 'newComment'
  let url = '/api'

  if (commentType === 'order') {
    url = `${url}/${order}`
  } else if (commentType === 'direct_sale') {
    url = `${url}/${direct_sale}`
  } else if (commentType === 'invoice') {
    url = `${url}/${invoice}`
  }

  if (operationType === 'add') {
    url = `${url}/${addC}`
  } else if (operationType === 'update') {
    url = `${url}/${updateC}`
  } else if (operationType === 'delete') {
    url = `${url}/${deleteC}`
  }
  return url
}

export const isEmpty = (obj) => {
  return Object.entries(obj).length === 0;
}

const addPrimaryKeyNameCommentParent = (id, body, commentType) => {
  if (commentType === 'order') {
    body.orderId = id
  } else if (commentType === 'direct_sale') {
    body.directSaleId = id
  } else if (commentType === 'invoice') {
    body.entryDocumentId = id
  }
}

export function formatTimestamp(date = new Date()) {
  const pad = (n, size = 2) => String(n).padStart(size, "0");

  const yyyy = date.getFullYear();
  const MM = pad(date.getMonth() + 1);
  const dd = pad(date.getDate());
  const hh = pad(date.getHours());
  const mm = pad(date.getMinutes());
  const ss = pad(date.getSeconds());

  // JS solo da milisegundos (3 dígitos); añadimos ‘000’ ⇒ 6 dígitos
  const micro = pad(date.getMilliseconds(), 3) + "000";

  // offset local respecto a UTC, en minutos
  const offsetMin = -date.getTimezoneOffset();
  const sign = offsetMin >= 0 ? "+" : "-";
  const abs = Math.abs(offsetMin);
  const offH = pad(Math.floor(abs / 60));
  const offM = pad(abs % 60);

  return `${yyyy}-${MM}-${dd} ${hh}:${mm}:${ss}.${micro}${sign}${offH}:${offM}`;
}

export const getRecordComments = async (idOriginal ) => {
  try {
    let url = `/api/orders/order/comment/${idOriginal}/records?scope=orderStackableCommentRecords`
    const res = await instanceAxios.get(url);
    if (res.status == 200) {
      return res.data.commentsRecords
    } else {
      return []
    }
  } catch (error) {
    return []
  }
}

/**
 * Obtiene los comentarios de una orden, incluyendo los eliminados si showDeletedComments es true.
 * @param {string|number} idOrder - ID de la orden
 * @param {boolean} showDeletedComments - Si es true, incluye los comentarios eliminados
 * @returns {Promise<Array>} - Lista de comentarios
 *
 * Ejemplo de uso:
 *   const comments = await getOrderCommentsWithDeleted(123, true);
 */
export const getOrderCommentsWithDeleted = async (idOrder, showDeletedComments = false, setComments) => {
  try {
    const url = `/api/orders/order/${idOrder}/comment?showDeletedComments=${showDeletedComments}`;
    const res = await instanceAxios.get(url);
    if (res.status === 200) {
      const comments = res.data.comments || [];
      if (setComments) setComments(comments);
      return comments;
    } else {
      if (setComments) setComments([]);
      return [];
    }
  } catch (error) {
    if (setComments) setComments([]);
    return [];
  }
};



export const addNewCommentFunctionGeneral = async (saleId, commentToAdd, commentType, csrf_access_token, comments, setComments, setLastComment, commentTypeUrlAdd, type, email, name) => {
  console.log('99999999999999999.................-----------')
  console.log(csrf_access_token)
  console.log('99999999999999999.................-----------')
  try {
    const body = {
      newComment: commentToAdd
    }
    addPrimaryKeyNameCommentParent(saleId, body, commentType)
    const res = await instanceAxios.post(
      commentTypeUrlAdd,
      body,
      {
        headers: {
          "x-csrf-token": csrf_access_token,
        },
      }
    );
    if (res.status == 200 || res.status == 201) {
      const newComment = res.data.commentInfo

      const bulidStructureComment = {
        id: newComment.id,
        "orderStackableCommentRelevantRecords": {
          firstCommentRecord: {
            comment: commentToAdd,
            timeStamp: formatTimestamp(),
            user: {
              name: name,
              email: email,
            }
          }
          , lastCommentRecord: {
            comment: commentToAdd,
            timeStamp: formatTimestamp(),
            user: {
              name: name,
              email: email,
            }
          }
        },
      }




      // Añadir el nuevo comentario a la lista
      const updatedComments = [...comments, bulidStructureComment];

      // Ordenar los comentarios por timestamp (de más antiguo a más reciente)
      const sortedComments = updatedComments.sort((a, b) =>
        new Date(a.timeStamp) - new Date(b.timeStamp)
      );

      setComments(sortedComments)
      setLastComment(bulidStructureComment.orderStackableCommentRelevantRecords.lastCommentRecord)
      return ['comentario agregado correctamente', 'success']
    } else {
      return ['error', 'error']
    }
  } catch (error) {
    return ['error', 'error']
  }
}

export const deleteCommentGeneral = async (commentId, commentTypeUrlDelete, csrf_access_token, comments, setComments, setLastComment, lastComment) => {

  try {
    const data = {
      orderStackableCommentId: commentId
    }
    const res = await instanceAxios.post(commentTypeUrlDelete, data, {
      headers: {
        "x-csrf-token": csrf_access_token,
      },  
    });

    if (res.status == 200) {
      const newComments = comments.filter(comment => comment.id !== commentId)

      //setComments([...newComments])
      setComments(newComments)
      if (lastComment.id === commentId) {
        if (newComments.length > 0) {
          setLastComment(newComments[newComments.length - 1])
        } else {
          setLastComment(noComment)
        }

      }
      return ['comentario borrado correctamente', 'success']
    } else {
      return ['error', 'error']
    }
  } catch (error) {
    let message = error.response.data.errores || 'Ocurrio un error'
    return [message, 'error']

  }
}

export const updateCommentGeneral = async (commentId, commentToUpdate, csrf_access_token, commentTypeUrlUpdate, comments, setComments, setLastComment, type, email, name) => {
  try {
    const data = {
      commentId: commentId,
      commentInfo: commentToUpdate,
    };
    let idd = commentId
    const commentTypeUrlUpdateNew = type === 'update' ? `${commentTypeUrlUpdate}/${idd}` : commentTypeUrlUpdate;

    const res = await instanceAxios.put(
      commentTypeUrlUpdateNew,
      data,
      {
        headers: {
          "x-csrf-token": csrf_access_token,
        },
      }
    );

    if (res.status == 200) {
      const infoComment = res.data.commentInfo

      const findComment = comments.find(comment => comment.id === commentId);
      const commentToUpdateStructure = {
        comment: commentToUpdate,
        timeStamp: formatTimestamp(),
        id: infoComment.id + Math.random().toString(36).substring(2, 15), // Generar un ID único
        user: {
          name: name,
          email: email,
        }
      };
      // Actualizar el comentario en la estructura del comentario encontrado
      if (!findComment) {
        return ['error', 'error']
      }
      // Actualizar el comentario en la estructura del comentario encontrado
      findComment.orderStackableCommentRelevantRecords.lastCommentRecord = commentToUpdateStructure



      // Actualizar el comentario en la lista
      const updatedComments = comments.map(comment =>
        comment.id === commentId ? findComment : comment
      );
      // Actualizar el comentario en la lista


      // Ordenar los comentarios por timestamp (de más antiguo a más reciente)
      const sortedComments = updatedComments.sort((a, b) =>
        new Date(a.timeStamp) - new Date(b.timeStamp)
      );

      setComments(sortedComments)
      setLastComment(findComment.orderStackableCommentRelevantRecords.lastCommentRecord)
      return ['comentario actualizado correctamente', 'success']
    } else {
      return ['error', 'error']
    }

  } catch (error) {
    return ['error', 'error']
  }
}

export const changeOrderInternalStatus = async (id, newInternalStatus, csrf_access_token) => {
  //orderId
  try {
    let url = "/api/orders/internalStatusChange";
    const res = await instanceAxios.post(
      url,
      {
        orderId: id,
        'orderInternalStatusId': newInternalStatus,
      },
      {
        headers: {
          "x-csrf-token": csrf_access_token,
        },
      }
    );
    if (res.status == 201) {
      return ['Status interno actualizado correctamente', 'success']
    } else {
      return ['error', 'error']
    }
  } catch (error) {
    return ['error', 'error']
  }
}

export const updateStatusDirectSale = async (id, newInternalStatus, csrf_access_token) => {
  const api = "api/directSales/directSale/updateInternalStatus";
  try {

    const res = await instanceAxios.put(api, {
      directSaleId: id,
      newInternalStatusId: newInternalStatus
    }, {
      headers: {
        "x-csrf-token": csrf_access_token,
        "Content-Type": "application/json",
      },
    });
    if (res.status == 200) {
      const msj200 = res?.data.mensaje || ' Actualizado con exito'
      return [msj200, 'success']
    } else {
      const msjNo200 = res?.data.mensaje || ' Actualizado con exito'
      return [msjNo200, 'warning']
    }
  } catch (error) {
    const errorToShow = error?.response.data.errores || "Error desconocido, reportar soporte tecnico"
    return [errorToShow, 'error']
  }
};
///////////////////////////////
export const handleUtilidadTypeMoney = (value, priceProv, typeMoney, quantity) => {
  // si es porcentaje retornamos, en otro caso retornamos el valor x defaukt

  if (typeMoney === "%") {
    const percentage = (value * (priceProv * parseInt(quantity))) / 100; // Calcula el porcentaje

    // Formatea el número para limitar a dos decimales sin redondear
    const formattedPercentage = Math.floor(percentage * 100) / 100;
    return formattedPercentage;
  }
  // console.log(value, "VALUEEEEEEACA");
  // const s = value.toString();
  // const l = s.length;
  // const decimalLength = s.indexOf(".") + 1;
  // const numStr = s.substr(0, decimalLength + 2);
  // return Number(numStr);

  const s = value.toString();
  const decimalIndex = s.indexOf(".");
  let numStr;

  if (decimalIndex === -1) {
    // No hay punto decimal, es un número entero
    numStr = s + ".00"; // Agrega ".00" para convertirlo en un formato decimal
  } else {
    // Hay un punto decimal, extrae hasta dos decimales
    numStr = s.substr(0, decimalIndex + 3);
  }

  return Number(numStr);

  // return parseInt(value);
};

export const truncateToTwoDecimals = (num) => Math.floor(num * 100) / 100;

export const calculatePriceClient = (priceProv, utility, typeMoney, product) => {
  let priceCliente =
    priceProv * product.quantity +
    handleUtilidadTypeMoney(utility, priceProv, typeMoney, product.quantity);

  var s = priceCliente.toString();
  var l = s.length;
  var decimalLength = s.indexOf(".") + 1;
  var numStr = decimalLength == 0 ? s : s.substr(0, decimalLength + 2);
  return Number(numStr);
};

export const truncateToTwoDecimalsString = (num) => {
  // if(!num) return

  let s = num.toString();
  let decimalIndex = s.indexOf(".");

  // Verificar si el número tiene decimales
  if (decimalIndex === -1) {
    // No tiene decimales, retornar el número tal cual
    return num;
  } else {
    // Tiene decimales, proceder a truncar a dos decimales
    let numStr = s.substr(0, decimalIndex + 3); // Incluye el punto decimal + 2 decimales
    return Number(numStr);
  }
};

export const getDiscount = (product) => {
  let result =
    product?.discount?.amount > 0
      ? product.discount.typeDiscount == "$"
        ? product.discount.amount
        : (product.discount.amount / 100) *
        calculatePriceClient(
          product.productSelected.cost,
          product.utility,
          product.typeMoney,
          product
        )
      : 0;
  //
  var s = result.toString();
  var l = s.length;
  var decimalLength = s.indexOf(".") + 1;
  var discount = s.substr(0, decimalLength + 2);
  return Number(discount);
};

export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
export const getPriceWithDiscountProduct = (product) => {
  let newPrice = product.productSelected.cost
  let s = newPrice.toString();
  let l = s.length;
  let decimalLength = s.indexOf(".") + 1;
  // if (decimalLength == 0) return newPrice;
  let numStr = s.substr(0, decimalLength + 2);
  let priceDiscount =
    Number(decimalLength == 0 ? newPrice : numStr) * product.quantity +
    handleUtilidadTypeMoney(
      product.utility,
      product.productSelected.cost,
      product.typeMoney,
      product.quantity
    ) -
    getDiscount(product);

  // setSumSubTotal(sumSubTotal + priceDiscount);
  return priceDiscount;
};

export const getIvaProduct = (product) => {
  const value = getPriceWithDiscountProduct(product) * 0.16;
  var s = value.toString();
  var l = s.length;
  var decimalLength = s.indexOf(".") + 1;
  var numStr = s.substr(0, decimalLength + 2);

  // sumRef.current += iva;
  return Number(numStr);
};

export const getFinalPrice = (product) => {
  let finalPrice =
    getPriceWithDiscountProduct(product) + getIvaProduct(product);
  // return Math.floor(finalPrice * 100) / 100;
  return finalPrice;
};

export const calculateImport = (priceProv, utility, typeMoney, quantity) => {
  const priceClient =
    priceProv +
    handleUtilidadTypeMoney(utility, priceProv, typeMoney, quantity);
  const importValue = priceClient * quantity;
  return importValue;
};



// exportar orderStatuses para los status de ordenes
export const orderStatuses = [
  { orderStatus: "Pendiente de pago", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 1 },
  { orderStatus: "Cancelado no procesado", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 2 },
  { orderStatus: "Error en el pedido", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 3 },
  { orderStatus: "Inconsistencia de datos", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 4 },
  { orderStatus: "Reembolsado", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 5 },
  { orderStatus: "Devuelto", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 6 },
  { orderStatus: "Rechazado", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 7 },
  { orderStatus: "Contracargo", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 8 },
  { orderStatus: "Enviado", orderStatusFlag: "Yellow", orderStatusFlagId: 2, orderStatusId: 9 },
  { orderStatus: "Entregado", orderStatusFlag: "Green", orderStatusFlagId: 3, orderStatusId: 10 },
  { orderStatus: "Cancelado ya procesado", orderStatusFlag: "Green", orderStatusFlagId: 3, orderStatusId: 11 },
  { orderStatus: "Entregado sin posibilidad de cambios", orderStatusFlag: "Green", orderStatusFlagId: 3, orderStatusId: 12 },
  { orderStatus: "Esperando stock", orderStatusFlag: "Orange", orderStatusFlagId: 4, orderStatusId: 13 },
  { orderStatus: "Pendiente de envío", orderStatusFlag: "Orange", orderStatusFlagId: 4, orderStatusId: 14 },
  { orderStatus: "En Devolución", orderStatusFlag: "Orange", orderStatusFlagId: 4, orderStatusId: 15 },
  { orderStatus: "Acordar con el comprador", orderStatusFlag: "Orange", orderStatusFlagId: 4, orderStatusId: 16 }
];
