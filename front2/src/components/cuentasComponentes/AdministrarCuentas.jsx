import { TableContainer, Dialog, DialogTitle, DialogContent, DialogActions, Button, Snackbar, Stack, useTheme, useMediaQuery, Tooltip, Typography, Grid } from '@mui/material';
import React from 'react'
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableHead from '@mui/material/TableHead';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import { tableCellClasses } from '@mui/material/TableCell';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SendIcon from '@mui/icons-material/Send';
import IconButton from '@mui/material/IconButton';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import InfoIcon from '@mui/icons-material/Info';
import Fab from '@mui/material/Fab';
import { useSelector } from 'react-redux';
import CloseIcon from '@mui/icons-material/Close';
import { useDispatch } from 'react-redux';
import { editarUsuarioAccion, eliminarUsuarioAccion, limpiarMensajeUsers, obtenerUsuarios, registrarUsuarioAccion } from '../../redux/usersDucks';
import CargandoLista from '../CargandoLista';
import CancelIcon from '@mui/icons-material/Cancel';
import { ManejarErrores } from '../ManejarErrores';
import { useCookies } from 'react-cookie';
import Alert from '@mui/material/Alert';
import { StyledTableCell, StyledTableRow } from '../StyledTableComponents';
import CustomDialog from '../componentesGenerales/CustomDialog';
import { AlertComponent } from '../componentesGenerales/Alert';

const AddDialog = ({ open, onSave, onClose, roles, usersList, cookies }) => {
    const dispatch = useDispatch();
    const [name, setName] = React.useState('');
    const [nickname, setNickname] = React.useState('');
    const [phone, setPhone] = React.useState('');
    const [email, setEmail] = React.useState('');
    const [role, setRole] = React.useState('');
    const [isButtonDisabled, setIsButtonDisabled] = React.useState(true);
  
    const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    const isPhoneValid = /^(\+)?(\d+)?$/.test(phone);
    
    const handleNameChange = (event) => {
      const newName = event.target.value;
      setName(newName);
      validateForm(newName, email, role, phone);
    };
  
    const handleEmailChange = (event) => {
      const newEmail = event.target.value;
      setEmail(newEmail);
      validateForm(name, newEmail, role, phone);
    };
  
    const handlePhoneChange = (event) => {
      const newPhone = event.target.value;
      setPhone(newPhone);
      validateForm(name, email, role, newPhone);
    };
  
    const handleRoleChange = (event) => {
      const newRole = event.target.value;
      setRole(newRole);
      validateForm(name, email, newRole, phone);
    };

    const validateForm = (name, email, role, phone) => {
      if (name.trim() !== '' && email.trim() !== '' && role !== '' && isPhoneValid) {
        setIsButtonDisabled(false);
      } else {
        setIsButtonDisabled(true);
      }
    };

    const handleSave = () => {
      const newUser = {
        alias: nickname,
        email: email,
        name: name,
        phonenumber: phone,
        roleId: role,
      };
      try {
        dispatch(registrarUsuarioAccion(newUser, roles, cookies.csrf_access_token));
        onSave();
      } catch (error) {
        console.log("Error completo:", error);
        console.log("Mensaje de error:", error.message);
        console.log("Status:", error.status);
        console.log("Datos adicionales:", error.data);
      }
    };

  
  
    return (
      <CustomDialog
        open={open}
        onClose={onClose}
        title="Agregar nueva información"
        maxWidth="sm"
        maxHeight="60vh"
        width="100%"
        actions={
          <>
            <Button variant="text" color="error" onClick={onClose}>
              Cancelar
            </Button>
            <Button variant="outlined" onClick={handleSave} color="buttonGreenPink" disabled={isButtonDisabled}>
              Guardar
            </Button>
          </>
        }
      >
        <Box sx={{ p: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                size='small'
                label="Nombre"
                name='name'
                onChange={handleNameChange}
                error={name.trim() === ''}
                helperText={name.trim() === '' ? 'Este campo es obligatorio' : ''}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                size='small'
                label="Nickname"
                name='nickname'
                value={nickname}
                onChange={(e) => setNickname(e.target.value)}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                size='small'
                label="Teléfono"
                name='phone'
                onChange={handlePhoneChange}
                error={phone.trim() !== '' && !isPhoneValid}
                helperText={
                  phone.trim() === '' ? '' : !isPhoneValid ? 'Formato de teléfono no válido' : ''
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                size='small'
                label="Email"
                name='email'
                value={email}
                onChange={handleEmailChange}
                error={email.trim() === '' || !isEmailValid}
                helperText={
                  email.trim() === '' ? 'Este campo es obligatorio' : !isEmailValid ? 'Email no válido' : ''
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                size='small'
                select
                label="Rol"
                name='role'
                value={role}
                onChange={handleRoleChange}
                error={role === ''}
                helperText={role === '' ? 'Este campo es obligatorio' : ''}
                fullWidth
                margin="normal"
              >
                {roles?.map((role) => (
                  <MenuItem key={role.roleId} value={role.roleId}>
                    {role.roleName}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
          </Grid>
        </Box>
      </CustomDialog>
    );
  };
  

const EditDialog = ({ open, onClose, onSave, user, roles }) => {
  const [name, setName] = React.useState(user?.name || '');
  const [email, setEmail] = React.useState(user?.email || '');
  const [role, setRole] = React.useState(user?.role?.roleId || '');
  const [isButtonDisabled, setIsButtonDisabled] = React.useState(true);

  const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  React.useEffect(() => {
    if (user) {
      setName(user.name);
      setEmail(user.email);
      setRole(user.role?.roleId);
    }
  }, [user]);

  const validateForm = () => {
    
    if (name.trim() !== '' && email.trim() !== '' && role !== '' && isEmailValid) {
      setIsButtonDisabled(false);
    } else {
      setIsButtonDisabled(true);
    }
  };

  React.useEffect(() => {
    validateForm();
  }, [name, email, role]);

  const handleSave = () => {
    onSave(name, email, role);
  };


  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title="Editar Usuario"
      maxWidth="sm"
      width="100%"
      actions={
        <>
          <Button variant="text" color="error" onClick={onClose}>
            Cancelar
          </Button>
          <Button variant="outlined" onClick={handleSave} color="buttonGreenPink" disabled={isButtonDisabled}>
            Guardar
          </Button>
        </>
      }
    >
      <Box sx={{ p: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              size='small'
              label="Nombre"
              name='name'
              value={name}
              onChange={(e) => setName(e.target.value)}
              error={name.trim() === ''}
              helperText={name.trim() === '' ? 'Este campo es obligatorio' : ''}
              fullWidth
              margin="normal"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              size='small'
              label="Email"
              name='email'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              error={email.trim() === '' || !isEmailValid}
              helperText={
                email.trim() === '' ? 'Este campo es obligatorio' : !isEmailValid ? 'Email no válido' : ''
              }
              fullWidth
              margin="normal"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              size='small'
              select
              label="Rol"
              value={role}
              onChange={(e) => setRole(e.target.value)}
              error={role === ''}
              helperText={role === '' ? 'Este campo es obligatorio' : ''}
              fullWidth
              margin="normal"
            >
              {roles?.map((role) => (
                <MenuItem key={role.roleId} value={role.roleId}>
                  {role.roleName}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
        </Grid>
      </Box>
    </CustomDialog>
  );
};

const AdministrarCuentas = () => {
  const activeRole = useSelector((store) => store.usuario.role );
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const theme = useTheme();
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md")); // < 900px
  const [selectedUser, setSelectedUser] = React.useState(null);
  
  React.useEffect(() => {
    dispatch(obtenerUsuarios());
  }, []);
  const response = useSelector((store) => store.usuario.usersList );
  const rows = response.usersInfo;
  const roles = response.roles;
  const message = useSelector((store) => store.usuario.usersMensaje);
  const severity = useSelector((store) => store.usuario.usersSeverity);

  console.log("response", message);

  const loadingUsers = useSelector((store) => store.usuario.loadingUsers );
  const [editingIndex, setEditingIndex] = React.useState(-1);

  const [deleteIndex, setDeleteIndex] = React.useState(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = React.useState(false);
  const [addDialogOpen, setAddDialogOpen] = React.useState(false);
  const [name , setName] = React.useState('');
  const [email , setEmail] = React.useState('');
  const [role , setRole] = React.useState('');
  const [openEditDialog, setOpenEditDialog] = React.useState(false);

  const handleAddClick = () => {
    setAddDialogOpen(true);
  };

  const handleAddSave = (newRow) => {
    setAddDialogOpen(false);
  };
  

  const handleEditClick = (index, userId, name, email, roleId) => {

    setSelectedUser(rows[index]);
    setOpenEditDialog(true);
  };
  
  const handleSendClick = (userId) => {
    dispatch(editarUsuarioAccion(userId, name, email, role, response, cookies.csrf_access_token));
    setEditingIndex(-1);
  };
  
  const handleNameChange = (event) => {
    setName(event.target.value);
  };

  const handleEmailChange = (event) => {
    setEmail(event.target.value);
  };
  

  const handleRoleChange = (event) => {
    setRole(event.target.value);
  };
  

  const handleDeleteClick = (userId, index) => {
    setDeleteIndex(userId);
    setConfirmDeleteOpen(true);
  };

  const handleConfirmDelete = () => {
    dispatch(eliminarUsuarioAccion(deleteIndex, response, cookies.csrf_access_token))
    setDeleteIndex(null);
    setConfirmDeleteOpen(false);
  };

  const handleCancelDelete = () => {
    setDeleteIndex(null);
    setConfirmDeleteOpen(false);
  };

  const handleOpenDetails = (user) => {
    setSelectedUser(user);
    setAddDialogOpen(true);
  };

  const handleCloseDetails = () => {
    setAddDialogOpen(false);
    setSelectedUser(null);
  };

  const handleEditSave = (name, email, role) => {
    dispatch(editarUsuarioAccion(selectedUser.userId, name, email, role, response, cookies.csrf_access_token));
    setOpenEditDialog(false);
    setSelectedUser(null);
  };

  if (loadingUsers) {
    return <CargandoLista />;
  }
  else if (response) {
    if (typeof response === "string" && response.startsWith("status")){
      return <ManejarErrores errorCode={response} />;
    }
    else {
      return (
        <Box>
          <Box display="flex" justifyContent="flex-end" sx={{ maxWidth: '92.5%', marginBottom: '20px' }}>
            <Fab
              variant='extended'
              color="buttonGreen"
              aria-label="add"
              className='buttonGreenPink'
              style={{ position: 'fixed', bottom: 20, right: 20, marginBottom: "5px" }}
              onClick={handleAddClick}
            >   <AddCircleOutlineIcon sx={{ mr: 1 }} />
              Agregar Cuenta
            </Fab>
          </Box>

          <Box display="flex" justifyContent="center" sx={{ marginBottom: '60px', flexDirection: "column", marginX: "auto", width: "100%", alignItems: "center" }}>
            <TableContainer component={Paper} sx={{ maxWidth: '95%', margin: "auto", borderRadius: "25px" }}>
              <Table size={isMediumScreen ? "small" : "medium"}>
                <TableHead>
                  <StyledTableRow>
                    <StyledTableCell align="center">NOMBRE</StyledTableCell>
                    {!isMediumScreen && <StyledTableCell align="center">ALIAS</StyledTableCell>}
                    {!isMediumScreen && <StyledTableCell align="center">TELÉFONO</StyledTableCell>}
                    {!isMediumScreen && <StyledTableCell align="center">E-MAIL</StyledTableCell>}
                    {!isMediumScreen && <StyledTableCell align="center">ROL</StyledTableCell>}
                    <StyledTableCell align="center">ACCIONES</StyledTableCell>
                  </StyledTableRow>
                </TableHead>
                <TableBody>
                  {rows?.map((row, index) => (
                    <StyledTableRow key={index}>
                      <StyledTableCell align="center">
                        {editingIndex === index ? (
                          <TextField
                            size='small'
                            value={name}
                            onChange={handleNameChange}
                            disabled={editingIndex !== index}
                          />
                        ) : (
                          row.name
                        )}
                      </StyledTableCell>
                      {!isMediumScreen && <StyledTableCell align="center">{row.alias}</StyledTableCell>}
                      {!isMediumScreen && <StyledTableCell align="center">{row.phoneNumber}</StyledTableCell>}
                      {!isMediumScreen && <StyledTableCell align="center">
                        {editingIndex === index ? (
                          <TextField
                            size='small'
                            value={email}
                            onChange={handleEmailChange}
                            disabled={editingIndex !== index}
                          />
                        ) : (
                          row.email
                        )}
                      </StyledTableCell>}
                      {!isMediumScreen && <StyledTableCell align="center">
                        {editingIndex === index && row.roleName !== "Admin" ? (
                          <Select
                            size="small"
                            value={role}
                            onChange={handleRoleChange}
                            disabled={editingIndex !== index}
                          >
                            {roles?.map((role) => (
                              <MenuItem key={role.roleId} value={role.roleId}>
                                {role.roleName}
                              </MenuItem>
                            ))}
                          </Select>
                        ) : (
                          row.role?.roleName
                        )}
                      </StyledTableCell>}
                      <StyledTableCell align="center">
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: '5px', flexWrap: 'wrap' }}>
                          {isMediumScreen && (
                            <Tooltip title="Ver detalles">
                              <IconButton
                                variant="contained"
                                color="buttonGreenPink"
                                size="small"
                                onClick={() => handleOpenDetails(row)}
                              >
                                <InfoIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                          {editingIndex === index ? (
                            <>
                              <Tooltip title="Guardar">
                                <IconButton variant="contained" color="buttonGreenPink" size={isMediumScreen ? "small" : "medium"} onClick={() => handleSendClick(row.userId)}>
                                  <SendIcon fontSize={isMediumScreen ? "small" : "medium"} />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Cancelar">
                                <IconButton variant="outlined" color="buttonGreenPink" size={isMediumScreen ? "small" : "medium"} onClick={() => setEditingIndex(-1)}>
                                  <CancelIcon fontSize={isMediumScreen ? "small" : "medium"} />
                                </IconButton>
                              </Tooltip>
                            </>
                          ) : (
                            <>
                              <Tooltip title="Editar">
                                <IconButton variant="contained" color="buttonGreenPink" size={isMediumScreen ? "small" : "medium"} onClick={() => handleEditClick(index, row.userId, row.name, row.email, row.role.roleId)}>
                                  <EditIcon fontSize={isMediumScreen ? "small" : "medium"} />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Eliminar">
                                <IconButton variant="outlined" disabled={row.roleName === "Admin"} color="buttonGreenPink" size={isMediumScreen ? "small" : "medium"} onClick={() => handleDeleteClick(row.userId, index)}>
                                  <DeleteIcon fontSize={isMediumScreen ? "small" : "medium"} />
                                </IconButton>
                              </Tooltip>
                            </>
                          )}
                        </Box>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <CustomDialog
              open={confirmDeleteOpen}
              onClose={handleCancelDelete}
              title="Confirmar eliminación"
              maxWidth="sm"
              width="100%"
              actions={
                <>
                  <Button variant="text" color="error" onClick={handleCancelDelete}>
                    Cancelar
                  </Button>
                  <Button variant="outlined" onClick={handleConfirmDelete} color="buttonGreenPink" autoFocus>
                    Eliminar
                  </Button>
                </>
              }
            >
              <Box sx={{ p: 2 }}>
                <Typography variant="body1">
                  ¿Estás seguro de que deseas eliminar este usuario?
                </Typography>
              </Box>
            </CustomDialog>

            {/* Dialog para mostrar detalles en pantallas pequeñas */}
            <CustomDialog
              open={addDialogOpen && selectedUser}
              onClose={handleCloseDetails}
              title="Detalles del Usuario"
              maxWidth="sm"
              width="100%"
            >
              {selectedUser && (
                <Box sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    {selectedUser.name}
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1" color="text.secondary">
                        Alias:
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedUser.alias}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1" color="text.secondary">
                        Teléfono:
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedUser.phoneNumber || "----"}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" color="text.secondary">
                        Email:
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedUser.email || "----"}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" color="text.secondary">
                        Rol:
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedUser.role?.roleName}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}
            </CustomDialog>

            <AddDialog
              roles={roles}
              open={addDialogOpen && !selectedUser}
              onSave={handleAddSave}
              onClose={() => setAddDialogOpen(false)}
              usersList={response}
              cookies={cookies}
            />

            <EditDialog
              open={openEditDialog}
              onClose={() => {
                setOpenEditDialog(false);
                setSelectedUser(null);
              }}
              onSave={handleEditSave}
              user={selectedUser}
              roles={roles}
            />
          </Box>

          

          {message && (

            <AlertComponent
              color={severity}
              message={message}
              cleanMessage={limpiarMensajeUsers}
              time={3000}
            />

           
          )}
        </Box>
      );
    }
  }
}

export default AdministrarCuentas;
