import { TableContainer, Dialog, DialogTitle, DialogContent, DialogActions, Button, <PERSON>nack<PERSON>, <PERSON>ing, Stack, useTheme, useMediaQuery, Tooltip, Typography, Grid } from '@mui/material';
import React, { useEffect } from 'react'
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableHead from '@mui/material/TableHead';
import Box from '@mui/material/Box';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import IconButton from '@mui/material/IconButton';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import InfoIcon from '@mui/icons-material/Info';
import Fab from '@mui/material/Fab';
import { useSelector } from 'react-redux';
import CloseIcon from '@mui/icons-material/Close';
import { useDispatch } from 'react-redux';
import { editarUsuarioAccion, eliminarClienteAccion, eliminarUsuarioAccion, getClients, getNumClients, limpiarMensajeClients, limpiarMensajeUsers, obtenerUsuarios, registrarUsuarioAccion } from '../../redux/usersDucks';
import CargandoLista from '../CargandoLista';
import { ManejarErrores } from '../ManejarErrores';
import { useCookies } from 'react-cookie';
import Alert from '@mui/material/Alert';
import { StyledTableCell, StyledTableRow } from '../StyledTableComponents';
import Pagination from "@mui/material/Pagination";
import { FormularioDatos } from '../componentesGenerales/clientes/Register';
import CustomDialog from '../componentesGenerales/CustomDialog';

const AdministrarClientes = () => {
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const theme = useTheme();
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md")); // < 900px
  const [openDialog, setOpenDialog] = React.useState(false);
  const [selectedCliente, setSelectedCliente] = React.useState(null);

  React.useEffect(() => {
    dispatch(getNumClients());
    dispatch(getClients());
  }, []);
  const response = useSelector((store) => store.usuario.clientesList);

  const message = useSelector((store) => store.usuario.clientMensaje);
  const severity = useSelector((store) => store.usuario.clientSeverity);
  const [clientesListState, setClientesListState] = React.useState([]);

  const loadingClientes = useSelector((store) => store.usuario.loadingClientes);
  const [editingIndex, setEditingIndex] = React.useState(-1);
  const clientesList = useSelector((store) => store.usuario.clientesList);
  const cantidadDeClientes = useSelector((store) => store.usuario.cantidadDeClientes);

  const [deleteIndex, setDeleteIndex] = React.useState(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = React.useState(false);
  const [page, setPage] = React.useState(1);

  useEffect(() => {
    if (clientesList) {
      setClientesListState(clientesList);
    }
  }, [clientesList]);

  const handleAddClick = () => {
    setEditingIndex(-1);
    setOpenDialog(true);
  };

  const handleEditClick = (index) => {
    setEditingIndex(index);
    setOpenDialog(true);
  };

  const handleDeleteClick = (userId, index) => {
    setDeleteIndex(userId);
    setConfirmDeleteOpen(true);
  };

  const handleConfirmDelete = () => {
    dispatch(eliminarClienteAccion(deleteIndex, cookies.csrf_access_token))
    setDeleteIndex(null);
    setConfirmDeleteOpen(false);
  };

  const handleCancelDelete = () => {
    setDeleteIndex(null);
    setConfirmDeleteOpen(false);
  };

  const handleOpenDetails = (cliente) => {
    setSelectedCliente(cliente);
    setOpenDialog(true);
  };

  const handleCloseDetails = () => {
    setOpenDialog(false);
    setSelectedCliente(null);
  };

  const transformValue = (value, max = 100, scale = 5) => {
    return (value / max) * scale;
  };

  const cambioDePagina = (event, value) => {
    setPage(value);
    dispatch(getClients(value));
  };

  if (loadingClientes) {
    return <CargandoLista />;
  }
  else if (response) {
    if (typeof response === "string" && response.startsWith("status")) {
      return <ManejarErrores errorCode={response} />;
    }
    else {
      return (
        <Box>
          <Box display="flex" justifyContent="flex-end" sx={{ maxWidth: '92.5%', marginBottom: '20px' }}>
            <Fab
              variant='extended'
              color="buttonGreen"
              aria-label="add"
              className='buttonGreenPink'
              style={{ position: 'fixed', bottom: 20, right: 20, marginBottom: "5px" }}
              onClick={handleAddClick}
            >   <AddCircleOutlineIcon sx={{ mr: 1 }} />
              Agregar Cliente
            </Fab>
          </Box>

          <FormularioDatos setOpenDialog={setOpenDialog} openDialog={openDialog} edit={clientesList[editingIndex]} handleCloseDetails={handleCloseDetails} selectedCliente={selectedCliente} />

          <Box display="flex" justifyContent="center" sx={{ marginBottom: '60px', flexDirection: "column", marginX: "auto", width: "100%", alignItems: "center" }}>

            
            <TableContainer component={Paper} sx={{ maxWidth: '95%', margin: "auto", borderRadius: "25px" }}>
              <Table size={isMediumScreen ? "small" : "medium"}>
                <TableHead>
                  <StyledTableRow>
                    <StyledTableCell align="center">NOMBRE</StyledTableCell>
                    {!isMediumScreen && <StyledTableCell align="center">ALIAS</StyledTableCell>}
                    {!isMediumScreen && <StyledTableCell align="center">TELÉFONO</StyledTableCell>}
                    {!isMediumScreen && <StyledTableCell align="center">E-MAIL</StyledTableCell>}
                    {!isMediumScreen && <StyledTableCell align="center">DIRECCION</StyledTableCell>}
                    <StyledTableCell align="center">ACCIONES</StyledTableCell>
                  </StyledTableRow>
                </TableHead>
                <TableBody>
                  {clientesListState?.map((row, index) => (
                    <StyledTableRow key={index}>
                      <StyledTableCell align="center">
                        <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
                          {row.name}
                          {row.score != null ? <Rating name="half-rating-read" value={transformValue(row.score)} precision={0.1} readOnly /> : "Sin score"}
                        </Box>
                      </StyledTableCell>
                      {!isMediumScreen && <StyledTableCell align="center" sx={{ maxWidth: '150px', wordWrap: 'break-word', whiteSpace: 'normal' }}>{row.nickname}</StyledTableCell>}
                      {!isMediumScreen && <StyledTableCell align="center">{row.phoneNumber ? row.phoneNumber : "----"}</StyledTableCell>}
                      {!isMediumScreen && <StyledTableCell align="center">{row.email ? row.email : "----"}</StyledTableCell>}
                      {!isMediumScreen && <StyledTableCell align="center">{row.state} {' - '} {row.zipCode} {' - '} {row.city}  {" - "} {row.address}</StyledTableCell>}
                      <StyledTableCell align="center">
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: '5px', flexWrap: 'wrap' }}>
                          {isMediumScreen && (
                            <Tooltip title="Ver detalles">
                              <IconButton
                                variant="contained"
                                color="buttonGreenPink"
                                size="small"
                                onClick={() => handleOpenDetails(row)}
                              >
                                <InfoIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                          <Tooltip title="Editar">
                            <IconButton variant="contained" color="buttonGreenPink" size={isMediumScreen ? "small" : "medium"} onClick={() => handleEditClick(index)}>
                              <EditIcon fontSize={isMediumScreen ? "small" : "medium"}  />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Eliminar">
                            <IconButton variant="outlined" color="buttonGreenPink" size={isMediumScreen ? "small" : "medium"} onClick={() => handleDeleteClick(row.clientId, index)}>
                              <DeleteIcon fontSize={isMediumScreen ? "small" : "medium"} />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </StyledTableCell>
                    </StyledTableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>


            <Stack spacing={3} sx={{ marginTop: "1rem" }}>
              <Pagination
                count={cantidadDeClientes}
                page={page}
                onChange={cambioDePagina}
              />
            </Stack>

            <CustomDialog
              open={confirmDeleteOpen}
              onClose={handleCancelDelete}
              title="Confirmar eliminación"
              maxWidth="sm"
              width="100%"
              actions={
                <>
                  <Button variant="text" color="error" onClick={handleCancelDelete}>
                    Cancelar
                  </Button>
                  <Button variant="outlined" onClick={handleConfirmDelete} color="buttonGreenPink" autoFocus>
                    Eliminar
                  </Button>
                </>
              }
            >
              <Box sx={{ p: 2 }}>
                <Typography variant="body1">
                  ¿Estás seguro de que deseas eliminar este usuario?
                </Typography>
              </Box>
            </CustomDialog>

            {/* Dialog para mostrar detalles en pantallas pequeñas */}
            <CustomDialog
              open={openDialog && selectedCliente}
              onClose={handleCloseDetails}
              title="Detalles del Cliente"
              maxWidth="sm"
              width="100%"
            >
              {selectedCliente && (
                <Box sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    {selectedCliente.name}
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1" color="text.secondary">
                        Alias:
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedCliente.nickname}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1" color="text.secondary">
                        Teléfono:
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedCliente.phoneNumber || "----"}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" color="text.secondary">
                        Email:
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedCliente.email || "----"}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" color="text.secondary">
                        Dirección:
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedCliente.state} {' - '} {selectedCliente.zipCode} {' - '} {selectedCliente.city}  {" - "} {selectedCliente.address}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}
            </CustomDialog>
          </Box>

          {message && (
            <Alert
              severity={severity}
              sx={{ margin: "1rem", position: 'fixed', bottom: "1em", left: "1em", width: "30%" }}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => {
                    dispatch(limpiarMensajeClients());
                  }
                  }
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }>
              {message}
            </Alert>
          )}
        </Box>
      );
    }
  }
}

export default AdministrarClientes;
