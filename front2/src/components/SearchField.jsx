import * as React from "react";
import { styled, alpha } from "@mui/material/styles";
import Box from "@mui/material/Box";
import InputBase from "@mui/material/InputBase";
import SearchIcon from "@mui/icons-material/Search";
import Button from "@mui/material/Button";
import CustomDialog from "./componentesGenerales/CustomDialog";
import { useMediaQuery } from "@mui/material";

const SearchIconWrapper = styled("div")(({ theme }) => ({
  padding: theme.spacing(0, 0),
  bottom: 0,
  left: 0,
  height: "100%",
  width: "60px",
  paddingLeft: "15px",
  marginRight: 0,
  position: "absolute",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  zIndex: 1,
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: "inherit",
  width: "100%",
  paddingLeft: "60px",
  "& .MuiInputBase-input": {
    padding: theme.spacing(1, 1, 1, 0),
    // vertical padding + font size from searchIcon
    paddingLeft: "15px",
    transition: theme.transitions.create("width"),
  },
}));

const Search = styled("div")(({ theme }) => {
  const searchBorderRadius = "47px";

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return {
    position: "relative",
    backgroundColor: theme.palette.background.paper,
    borderRadius: searchBorderRadius,
    marginRight: isMobile ? 0 : theme.spacing(2),
    marginLeft: isMobile ? 0 : "15px",
    width: "100%",
    [theme.breakpoints.up("sm")]: {
      marginLeft: theme.spacing(3),
      width: "auto",
    },
  };
});

const SearchField = (props) => {

  const [searchTextField, setSearchTextField] = React.useState(props?.searchText || "");



  React.useEffect(() => {
    setSearchTextField(props?.searchText || "");
  }, [props?.searchText]);
  

  const isMobile = props.isMobile ? true : false;

  const actualizarSearchText = (event) => {
    setSearchTextField(event.target.value);
    // if (props.setSearchText) {
    //   props.setSearchText(event.target.value);
    // }
  };

  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      // debugger
      event.preventDefault(); // Evita que el formulario se envíe automáticamente
      // debugger

      if (isMobile) {
        setOpenDialog(false);
      }

      props.enviarSearch(event, searchTextField); // Llama a la función de búsqueda
    }
  };

  const [openDialog, setOpenDialog] = React.useState(false);


  return (
    <Box
      component="form"
      noValidate
      onSubmit={(e) => {

        e.preventDefault(); // Evita el comportamiento por defecto

        if (isMobile) {
          setOpenDialog(true);
          return;
        }

        props.enviarSearch(); // Llama a la función de búsqueda
      }}
      sx={{
        width: isMobile ? "auto" : "460px",
        maxWidth: isMobile ? "auto" : "560px",
        minWidth: isMobile ? "auto" : "160px",
      }}
    >
      {!isMobile &&
        <Search>
          <SearchIconWrapper>
            <Button type="submit" fullWidth color="buttonGreen"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                props.enviarSearch(e, searchTextField);
                // setSearchTextField("");
                setOpenDialog(false);
              }}
            >
              <SearchIcon
                sx={{
                  color: "pinkMain",
                }}
              />
            </Button>
          </SearchIconWrapper>
          <StyledInputBase
            placeholder="Buscar…"
            inputProps={{ "aria-label": "search" }}
            value={searchTextField}
            onChange={actualizarSearchText}
            onKeyDown={handleKeyDown} // Agrega el manejador de teclas
          />
        </Search>
      }

      {isMobile &&
        <Button type="submit" fullWidth color="buttonGreen">
          <SearchIcon
            // sx={{
            //   color: "pinkMain",
            // }}
            color="buttonGreenPink"
          />
        </Button>
      }

      <CustomDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        title={"Buscar"}
        maxHeight="50vh"
        actions={

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            sx={{ mt: 3, mb: 2 }}
            onClick={(e) => {
              props.enviarSearch(e, searchTextField);
              setSearchTextField("");
              setOpenDialog(false);
              
            }}
          >
            Buscar
          </Button>

        } >
        <Search>
          <SearchIconWrapper>
            <Button type="submit" fullWidth color="buttonGreen">
              <SearchIcon
                sx={{
                  color: "pinkMain",
                }}
              />
            </Button>
          </SearchIconWrapper>
          <StyledInputBase
            placeholder="Buscar…"
            inputProps={{ "aria-label": "search" }}
            value={searchTextField == null ? "" : searchTextField ? searchTextField : ""}
            onChange={actualizarSearchText}
            onKeyDown={(event) => {
              handleKeyDown(event); // Asegúrate de pasar el evento si `onKeyDown` lo necesita
            }}
          />
        </Search>
      </CustomDialog>



    </Box>
  );
};

export default SearchField;
