import React from 'react'
import Fade from '@mui/material/Fade';
import Modal from '@mui/material/Modal';
import Backdrop from '@mui/material/Backdrop';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { Warning } from '@mui/icons-material';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 730,
    bgcolor: 'background.paper',
    border: '2px solid #000',
    boxShadow: 24,
    p: 4,
  };
  
const ModalGeneral = (props) => {
  return (
                            
            <Modal
                aria-labelledby="transition-modal-title"
                aria-describedby="transition-modal-description"
                open={props.open}
                onClose={props.handleClose}
                closeAfterTransition
                BackdropComponent={Backdrop}
                BackdropProps={{
                timeout: 5,
                }}
            >
                <Fade in={props.open}>
                    <Box sx={style}>
                        <Typography id="transition-modal-title" variant="h5" component="h2">
                           <Warning fontSize="large" sx={{color:"red"}}/> Necesitas corregir los siguientes errores en los filtros <Warning fontSize="large" sx={{color:"red"}}/>
                        </Typography>
                        <Typography id="transition-modal-description" sx={{ mt: 2 }}>
                            <p style={{whiteSpace: 'pre-line'}}> {props.mensajeModal}</p>
                        </Typography>
                    </Box>
                </Fade>
            </Modal>
  )
}

export default ModalGeneral