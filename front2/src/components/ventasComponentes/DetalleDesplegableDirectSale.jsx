import React from 'react'
import { Box, CardContent, Chip, Typography } from '@mui/material'
import DesplegableTabla from './DesplegableTabla'
import { useCookies } from 'react-cookie'
import { FullWidthTextField } from '../ordenesComponentes/components/CommentsComponent'


const DetalleDesplegableDirectSale = (props) => {
    const { row, isMediumScreen, isSxScreen, type, addNewCommentFunction,  deleteComment, updateComment, comments} = props

  return (
    <>
      <CardContent
        style={{
          width: "100%",
          boxShadow: "1px 1px 3px #47474766",
          marginBottom: "10px",
          marginTop: "10px",
        }}
      >
        <Box sx={{ width: "95%" }}>
          {/* <CardContent style={{ border: "1px solid " }}> */}
          <Typography sx={{ fontSize: 14 }} color="text.secondary" gutterBottom>
            {isSxScreen || isMediumScreen ? (
              <span>ID: {type === "consolidate" ? row.id : row?.directSaleId} | </span>
            ) : (
              <span>Pedido con ID: {type === "consolidate" ? row.id : row?.directSaleId} | CreationDate: {type === "consolidate" ? row?.date : row?.saleDate} </span>
            )}

            {/* Pedido con ID: {pedido.orderId} | CreationDate:{" "} */}
            {row?.saleDate}
          </Typography>

          <Typography sx={{ fontSize: 14 }} component="div">
            Tipo de venta: {row?.directSaleType.directSaleType}
          </Typography>
        </Box>
        <DesplegableTabla row={row} />
        <FullWidthTextField
          addNewCommentFunction = {addNewCommentFunction}
          updateComment = {updateComment}
          deleteComment = {deleteComment}
          comments = {comments}
        />
      </CardContent>
    </>
  )
}

export default DetalleDesplegableDirectSale
