//Boton de Opciones en las ordenes que tiene dentro Surtir e imprimir guia

import * as React from 'react';
import { styled, alpha } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import Box from '@mui/material/Box';
import ListIcon from '@mui/icons-material/List';

const ButtonAbsolute = styled(Button)(({ theme }) => ({
    "& .MuiButton-endIcon": {
        position: "absolute",
        right: 16
    }
}
));

const StyledMenu = styled((props) => (
    <Menu
        elevation={0}
        anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
        }}
        transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
        }}
        {...props}
    />
))(({ theme }) => ({
    '& .MuiPaper-root': {
        borderRadius: 6,
        marginTop: theme.spacing(1),
        minWidth: 180,
        color: 'rgb(55, 65, 81)',
        boxShadow:
            'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
        '& .MuiMenu-list': {
            padding: '4px 0',
        },
        '& .MuiMenuItem-root': {
            '& .MuiSvgIcon-root': {
                fontSize: 18,
                color: theme.palette.text.secondary,
                marginRight: theme.spacing(1.5),
            },
            '&:active': {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    theme.palette.action.selectedOpacity,
                ),
            },
        },
        ...theme.applyStyles('dark', {
            color: theme.palette.grey[300],
        }),
    },
}));

const RowMenu = ({ listbotons, anchorEl, handleClick, handleClose, open, disabled=false }) => {
    return (
        <Box
            onClick={e => {
                e.stopPropagation();
            }}
        >

            <ButtonAbsolute
                id="demo-customized-button"
                aria-controls={open ? 'demo-customized-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                variant="outlined"
                color="buttonGreenPink"
                disableElevation
                size="small"
                onClick={e => {
                    e.stopPropagation();
                    handleClick(e);
                }}
                //se aplico el disabled para que no se pueda abrir el menu si esta en rojo
                disabled={disabled}
                // endIcon={<ListIcon />}
                sx={{
                    minWidth: "100%", // Ajusta según sea necesario
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "35.82px",
                    gap: "4px",
                }}
            >
                Mas <ListIcon />
            </ButtonAbsolute>


            <StyledMenu
                id="demo-customized-menu"
                MenuListProps={{
                    'aria-labelledby': 'demo-customized-button',
                }}
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
            >
                {listbotons}
            </StyledMenu>
        </Box>
    );
}

export default RowMenu