import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { StyledTableCell, StyledTableRow } from "../StyledTableComponents";
import {
  Avatar,
  AvatarGroup,
  Box,
  Button,
  Checkbox,
  Collapse,
  FormControl,
  IconButton,
  InputLabel,
  ListSubheader,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import { useCallback } from "react";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import logoCP from "../../components/img/LogoCP500x500.svg";
import CardContent from "@mui/material/CardContent";
import PrintIcon from "@mui/icons-material/Print";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import { Delete, Edit, Visibility, Money } from "@mui/icons-material";
import DetalleDesplegableDirectSale from "./DetalleDesplegableDirectSale";
import { useNavigate } from "react-router-dom";
import {
  EconomicData,
  EconomicDataChips,
  EconomicDataIcons,
} from "./EconomicData";
import { updateStatusDirectSale } from '../utils/utils'
import { useCookies } from "react-cookie";
import {
  setMsgVentas
} from "../../redux/pedidosDucks";
import RowMenu from "./SurtidoVentaDirecta/RowMenu";
import { addNewCommentFunctionGeneral, deleteCommentGeneral, noComment, returnCommentTypeUrl, updateCommentGeneral } from '../utils/utils'
import { ClickAwayListener } from "@mui/material";
import { Tooltip } from "@mui/material";
import Divider from '@mui/material/Divider';
import FormDialog from "./DialogShowPayment/DialogPayment";

const DirectSaleRow = (props) => {
  const {
    row,
    id,
    directSaleInternalStatuses,
    handleOpenActions,
    handleOpenActionsPrint,
    setIdDeleteSale,
    ordenesSeleccionadas,
    setOrdenesSeleccionadas,
    type,
    setIdDialog,
    setOpenDialogDelete,
    colocarEstadoEliminandoVentaDirecta,
    from, index
  } = props;
  const [cookies, setCookie] = useCookies();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const theme = useTheme();
  // son mis constantes de media query ->->-<-<-<

  const themeBreak = useTheme();

  const isMediumScreen = useMediaQuery(themeBreak.breakpoints.down("md"));

  // abajo de 700
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  // abajo de 1280
  const isDownLargeScreen = useMediaQuery(theme.breakpoints.down("lg"));
  // entre 960 y 1060
  const detailOperationScreen = useMediaQuery(
    theme.breakpoints.between("md", "detailOperationScreenNumber")
  );

  const isSxScreen = useMediaQuery(theme.breakpoints.down("xs"));

  const role = useSelector((store) => store.usuario.role);

  const isAdminn = useCallback(() => {
    if (role === "Admin" || role === "Administrative_accountant") return true;
    return false;
  }, [role]);
  const [comments, setComments] = React.useState(row.comments);
  const internalStatus = row.directSaleInternalStatus.directSaleInternalStatus
  const [lastComment, setLastComment] = React.useState(
    comments.length > 0 ? comments[comments.length - 1] : noComment
  );
  const commentType = "direct_sale"
  const commentTypeUrlAdd = returnCommentTypeUrl(commentType, "add");
  const commentTypeUrlDelete = returnCommentTypeUrl(commentType, "delete");
  const commentTypeUrlUpdate = returnCommentTypeUrl(commentType, "update");
  const csrf_access_token = cookies.csrf_access_token;
  const [checked, setChecked] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const [statusQuote, setStatusQuote] = React.useState(
    row?.directSaleInternalStatus?.directSaleInternalStatusId ?? 0
  );
  const [anchorEl, setAnchorEl] = React.useState(null);
  const openAnchor = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const [openDialog, setOpenDialog] = React.useState(false);
  const [typeOperation, setTypeOperation] = React.useState("");

  const registrarPago = () => {
    setOpenDialog(true)
    setTypeOperation("Registrar pago")
  }

  const verPago = () => {
    setOpenDialog(true)
    setTypeOperation("Ver pago")
  }

  const closeDialog = () => {
    setOpenDialog(false)
  }

  const openDeleteDialog = (id) => {
    dispatch(colocarEstadoEliminandoVentaDirecta(false));
    setOpenDialogDelete(true)
    setIdDialog(id)
  }
  const listbotons = () => {
    return ([
      <MenuItem
        onClick={() => { handleOpenActionsPrint(id) }}
        disableRipple
      >
        <PrintIcon />
        Ver pdf
      </MenuItem>,
      <MenuItem
        disableRipple
        onClick={() => openDeleteDialog(id)}
        disabled={internalStatus !== "En cotización"}
      >
        <Delete />
        Borrar
      </MenuItem>,
      <Divider sx={{ my: 0.5 }} />,
      <MenuItem
        onClick={internalStatus === "En cotización" ? registrarPago : verPago}
        disableRipple
      >
        <Money />
        {internalStatus === "En cotización" ? "Registrar pago" : "Ver pago"}
      </MenuItem>,
      <MenuItem
        onClick={() => { navigate(`../surtir/${id}`) }}
        disableRipple
      >
        <LocalShippingIcon />
        Surtir
      </MenuItem>
    ])

  }
  const addNewCommentFunction = (commentToAdd) => {
    return addNewCommentFunctionGeneral(
      id,
      commentToAdd,
      commentType,
      csrf_access_token,
      comments,
      setComments,
      setLastComment,
      commentTypeUrlAdd
    );
  };

  const getOrderCommentsWithDeleted = async (idOrder, showDeletedComments = false) => {
    return getOrderCommentsWithDeleted(idOrder, showDeletedComments, setComments);
  };

  const deleteComment = (id) => {
    return deleteCommentGeneral(
      id,
      commentTypeUrlDelete,
      csrf_access_token,
      comments,
      setComments,
      setLastComment,
      lastComment,
      noComment
    );
  };

  const updateComment = async (id, commentToUpdate) => {
    return updateCommentGeneral(
      id,
      commentToUpdate,
      csrf_access_token,
      commentTypeUrlUpdate,
      comments,
      setComments,
      setLastComment
    );
  };
  const handleChangeStatus = async (event) => {
    const newStatus = event.target.value
    const [message, severity] = await updateStatusDirectSale(
      id,
      newStatus,
      csrf_access_token
    )
    if (severity === "success") {
      setStatusQuote(event.target.value);
    }
    dispatch(setMsgVentas(message, severity))
  };

  React.useEffect(() => {
    if (ordenesSeleccionadas.includes(id)) {
      setChecked(true);
    } else {
      setChecked(false);
    }
  }, [ordenesSeleccionadas]);

  const handleChangeChecked = (event) => {
    if (!ordenesSeleccionadas.includes(id)) {
      const newOrdenesSeleccionadas = [...ordenesSeleccionadas, id];
      setOrdenesSeleccionadas(newOrdenesSeleccionadas);
      setChecked(true);
    } else if (ordenesSeleccionadas.includes(id)) {
      const newOrdenesSeleccionadas = ordenesSeleccionadas.filter(
        (orden) => orden !== id
      );
      setOrdenesSeleccionadas(newOrdenesSeleccionadas);
      setChecked(false);
    }
  };

  const [productImages, setProductImages] = React.useState([]);

  useEffect(() => {
    row.products
      ? setProductImages(
        row.products?.map(
          (product) => product.productAtStore.product.productImage
        )
      )
      : setProductImages([]);
    setStatusQuote(row.directSaleInternalStatus.directSaleInternalStatusId)
    setAnchorEl(false)
  }, [row]);

  useEffect(() => {
    // productImages ? console.log("h") : console.log("No hay imagenes");
  }, [productImages]);

  const [tooltipOpen, setTooltipOpen] = React.useState(false);
  const handleTooltipToggle = () => {
    setTooltipOpen(!tooltipOpen);
  };


  const handleTooltipClose = () => {
    setTooltipOpen(false);
  };

  const handleTooltipOpen = () => {
    setTooltipOpen(true);
  };
  // const theme = useTheme();
  /**
 * Genera los estilos para las filas que tienen estado de error.
 * Incluye efectos de hover y animaciones para mejor feedback visual.
 * 
  * @param {object} theme - El tema actual de Material-UI.
 * @returns {object} - Un objeto con los estilos para las filas que tienen estado de error.
 */
  const getErrorStyles = (theme) => ({
    backgroundColor: theme.palette.mode === 'light' ? '#FFA1A1' : '#2D1717',
    "&::after": {
      content: '"Esta orden presenta un problema, haz clic para saber más."',
      position: "absolute",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      backgroundColor: theme.palette.mode === "light" 
        ? "rgba(179, 91, 91, 0.21)" 
        : "rgba(89, 42, 42, 0.21)",
      backdropFilter: "blur(2px)",
      zIndex: 10,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      color: theme.palette.mode === "light" ? "black" : "white",
      fontWeight: "bold",
      opacity: 0,
      transition: "opacity 0.3s ease-in-out",
    },
    "&:hover::after": {
      opacity: 1,
      animation: "fadeIn 0.3s ease-in-out",
    },
    "@keyframes fadeIn": {
      "0%": { opacity: 0, transform: "scale(0.95)" },
      "100%": { opacity: 1, transform: "scale(1)" },
    },
    "@keyframes fadeOut": {
      "0%": { opacity: 1, transform: "scale(1)" },
      "100%": { opacity: 0, transform: "scale(0.95)" },
    },
  });

 /**
 * Genera los estilos para las filas alternadas de la tabla.
 * 
 * @param {Object} theme - Tema actual de MUI
 * @param {number} index - Índice de la fila
 * @returns {string} Color de fondo para la fila
 */
const getAlternateRowStyle = (theme, index) => (
 
  index % 2 === 1 
    ? theme.palette.mode === "light"
      ? theme.palette.primary.backgroundPink
      : theme.palette.primary.greenMainTable
    : ""
);

  return (
    <>
      <FormDialog closeDialog={closeDialog} openDialog={openDialog} typeOperation={typeOperation} id={id} from={from} />
      <StyledTableRow key={id}
        style={{
          backgroundColor: index % 2 === 1 ? theme.palette.mode === "light" ? theme.palette.primary.backgroundPink : theme.palette.primary.greenMainTable : ""
        }}

        // style={{
        //   backgroundColor: getAlternateRowStyle(theme, index),
        //   position: "relative",
        // }}
      >
        <StyledTableCell
          sx={isDownLargeScreen ? { padding: "0", width: "24px" } : {}}
        >
          <div
            style={{
              padding: "0",
              margin: "0",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Checkbox
              checked={checked}
              onChange={handleChangeChecked}
              inputProps={{ "aria-label": "primary checkbox" }}
            />
            <IconButton
              aria-label="expand row"
              size="small"
              onClick={() => setOpen(!open)}
            >
              {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            </IconButton>
          </div>
        </StyledTableCell>
        {!isDownLargeScreen ? (
          <StyledTableCell
            component="th"
            scope="row"
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              border: "none",
            }}
          >
            <Avatar
              style={{ height: "100px", width: "100px" }}
              src={logoCP}
              alt="Logo"
            />
          </StyledTableCell>
        ) : null}
        {isMediumScreen ? null : (
          <StyledTableCell align="center">
            <Box
            >
              <Typography variant="body2">{row.saleDate}</Typography>
              <Typography variant="body2">{row.seller.name}</Typography>
              <React.Fragment>
                {/* <CardContent
                  sx={{
                    border: "1px solid rgba(0, 0, 0, 0.12)",
                    borderRadius: "4px",
                    padding: "16px",
                    width: "100%",
                    display: "flex",
                    alignItems: "end",
                    flexDirection: "column",
                  }}
                > */}
                <Typography
                  sx={{ fontSize: 11 }}
                  color="text.secondary"
                  gutterBottom
                >
                  {(row.client.nickname === null ||
                    row.client.nickname === "" ||
                    row.client.nickname === undefined
                    ? "Sin nickName"
                    : row.client.nickname) +
                    "(" +
                    (row.client.score !== 0 ? row.client.score : "Sin") +
                    " puntos)"}
                </Typography>
                <Typography variant="body2" component="div">
                  {row.client.name === null ||
                    row.client.name === "" ||
                    row.client.name === undefined
                    ? "Sin nombre"
                    : row.client.name}
                </Typography>
                {/* </CardContent> */}
              </React.Fragment>
            </Box>
            {isAdminn() ? (
              detailOperationScreen || isMediumScreen ? (
                <EconomicDataChips row={row} />
              ) : null
            ) : null}
          </StyledTableCell>
        )}
        {isAdminn() ? (
          detailOperationScreen || isMediumScreen ? null : (
            <StyledTableCell align="right">
              <EconomicData row={row} />
            </StyledTableCell>
          )
        ) : null}
        {isSxScreen ? null : (
          <StyledTableCell align="center">
            <>
              {/* mi avaatr es mi market pero esta se pondra cuando haya vista responsiva */}
              {isDownLargeScreen ? (
                <Avatar
                  style={{
                    height: "50px",
                    width: "50px",
                    top: "0",
                    left: "0",
                  }}
                  variant="circular"
                  src={logoCP}
                  alt="Logo"
                />
              ) : null}
              <AvatarGroup
                total={productImages.length}
                sx={{ display: "flex", justifyContent: "center" }}
              >
                {productImages.map((image, index) => (
                  <Avatar
                    key={index}
                    alt="Imagen de producto"
                    src={image}
                    variant="square"
                    style={
                      isSmallScreen
                        ? { height: "50px", width: "50px" }
                        : { height: "100px", width: "100px" }
                    }
                  />
                ))}
              </AvatarGroup>
            </>
          </StyledTableCell>
        )}
        <StyledTableCell align="left">
          {isSxScreen ? (
            <>
              <>
                <Avatar
                  style={{
                    height: "50px",
                    width: "50px",
                    top: "0",
                    left: "0",
                  }}
                  variant="circular"
                  src={logoCP}
                  alt="Logo"
                />
                <AvatarGroup
                  total={productImages.length}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  {productImages.map((image, index) => (
                    <Avatar
                      key={index}
                      alt="Imagen de producto"
                      src={image}
                      variant="square"
                      style={
                        isSmallScreen
                          ? { height: "50px", width: "50px" }
                          : { height: "100px", width: "100px" }
                      }
                    />
                  ))}
                </AvatarGroup>
              </>
              <EconomicDataIcons row={row} />
            </>
          ) : null}
          <Typography variant="body2">
            <span
              style={{
                marginTop: "-5px",
                marginBottom: "-5px",
                display: "inline-block",
                maxWidth: "100%",
                fontSize: "16px",
              }}
            >
              {/*#{type === "consolidate" ? row.id : row.directSaleId}*/}
              #{id}
            </span>
            <div>
              {row.products[0].productAtStore.product.productDescription}
            </div>
            <div>
              Marca: {row.products[0].productAtStore.product.productBase.brand}
            </div>
            <div>
              Modelo: {row.products[0].productAtStore.product.productBase.model}
            </div>
          </Typography>
          <Typography component={"div"} fontWeight="fontWeightBold">
            Cantidad: {row.products[0].units}
          </Typography>
          {isMediumScreen && !isSxScreen ? <EconomicDataIcons row={row} /> : null}
          {isSmallScreen ? (
            <>
              <FormControl
                size="small"
                sx={{ width: "100%", marginTop: "10px" }}
              >
                <InputLabel id="demo-select-small-label">Status1</InputLabel>
                <Select
                  sx={{ width: "100%" }}
                  labelId="demo-select-small-label"
                  id="demo-select-small"
                  value={statusQuote}
                  label="Status"
                  onChange={(e) => {
                    handleChangeStatus(e);
                  }}
                >
                  <ListSubheader>
                    <i style={{ color: "red" }}>status</i>
                  </ListSubheader>
                  {directSaleInternalStatuses.map((option) => (
                    <MenuItem
                      key={option.directSaleInternalStatusId}
                      value={option.directSaleInternalStatusId}
                      disabled={option.directSaleInternalStatus === "En cotización" || option.directSaleInternalStatus === "Cerrada(Pago verificado)"}
                    >
                      {option.directSaleInternalStatus}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Box
                sx={{
                  display: "flex",
                  marginTop: "2%",
                  flexDirection: "column",
                }}
              >
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                  <Typography noWrap>
                    {
                      <ClickAwayListener onClickAway={handleTooltipClose}>
                        <Tooltip
                          title={
                            <Typography
                              variant="body2"
                              component="div"
                              dangerouslySetInnerHTML={{
                                __html: comments
                                  .map((comment) => `- ${comment.comment}`)
                                  .join("<br/>"),
                              }}
                            ></Typography>
                          }
                          placement="top"
                          open={tooltipOpen}
                          onClose={handleTooltipClose}
                          disableTouchListener
                        >
                          <span
                            onClick={handleTooltipToggle}
                            onMouseEnter={handleTooltipOpen}
                          >
                            * {lastComment.comment}
                          </span>
                        </Tooltip>
                      </ClickAwayListener>
                    }
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", justifyContent: "space-between", gap: "10px" }}>
                  <Button
                    size="small"
                    onClick={() => (window.location = `/ventas/editar/${id}`)}
                    variant="contained"
                    color="primary"
                    sx={{ flexBasis: "30%", marginBottom: "5px" }}
                  >
                    {internalStatus === "En cotización" ? <Edit /> : <Visibility />}
                  </Button>
                  <RowMenu listbotons={listbotons()} anchorEl={anchorEl} handleClick={handleClick} handleClose={handleClose} open={openAnchor} />
                </Box>
              </Box>
            </>
          ) : null}
        </StyledTableCell>
        {isSmallScreen ? null : (
          <StyledTableCell align="center" sx={{ padding: "16px" }}>
            <FormControl
              size="small"
              sx={{ width: "100%" }}
              disabled={internalStatus === "En cotización"}>
              <InputLabel id="demo-select-small-label">Status</InputLabel>
              <Select
                sx={{ width: "100%" }}
                labelId="demo-select-small-label"
                id="demo-select-small"
                value={statusQuote}
                label="Status"
                onChange={(e) => {
                  handleChangeStatus(e);
                }}
              >
                <ListSubheader>
                  <i style={{ color: "red" }}>status</i>
                </ListSubheader>
                {directSaleInternalStatuses?.map((option) => (
                  <MenuItem
                    key={option.directSaleInternalStatusId}
                    value={option.directSaleInternalStatusId}
                    disabled={option.directSaleInternalStatus === "En cotización" || option.directSaleInternalStatus === "Cerrada(Pago verificado)"}
                  >
                    {option.directSaleInternalStatus}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Box
              sx={{ display: "flex", marginTop: "2%", flexDirection: "column" }}
            >
              <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                <Typography noWrap>
                  {
                    <ClickAwayListener onClickAway={handleTooltipClose}>
                      <Tooltip
                        title={
                          <Typography
                            variant="body2"
                            component="div"
                            dangerouslySetInnerHTML={{
                              __html: comments
                                .map((comment) => `- ${comment.comment}`)
                                .join("<br/>"),
                            }}
                          ></Typography>
                        }
                        placement="top"
                        open={tooltipOpen}
                        onClose={handleTooltipClose}
                        disableTouchListener
                      >
                        <span
                          onClick={handleTooltipToggle}
                          onMouseEnter={handleTooltipOpen}
                        >
                          * {lastComment.comment}
                        </span>
                      </Tooltip>
                    </ClickAwayListener>
                  }
                </Typography>
              </Box>
              <Box sx={{ display: "flex", justifyContent: "space-between", gap: "10px" }}>
                <Button
                  size="small"
                  onClick={() => (window.location = `/ventas/editar/${id}`)}
                  variant="contained"
                  color="primary"
                  sx={{ flexBasis: "30%", marginBottom: "5px" }}
                >
                  {internalStatus === "En cotización" ? <Edit /> : <Visibility />}
                </Button>
                <RowMenu listbotons={listbotons()} anchorEl={anchorEl} handleClick={handleClick} handleClose={handleClose} open={openAnchor} />
              </Box>
            </Box>
          </StyledTableCell>
        )}
      </StyledTableRow>
      <StyledTableRow>
        <StyledTableCell
          style={{ paddingBottom: 0, paddingTop: 0 }}
          colSpan={8}
        >
          <Collapse in={open} timeout="auto" unmountOnExit>
            <DetalleDesplegableDirectSale
              row={row}
              isSxScreen={isSxScreen}
              isMediumScreen={isMediumScreen}
              type={type}
              addNewCommentFunction={addNewCommentFunction}
              deleteComment={deleteComment}
              updateComment={updateComment}
              comments={comments}
            />
          </Collapse>
        </StyledTableCell>
      </StyledTableRow>
    </>
  );
};

export default DirectSaleRow;
