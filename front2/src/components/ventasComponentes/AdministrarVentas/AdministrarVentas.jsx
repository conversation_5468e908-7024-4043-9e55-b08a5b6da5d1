import * as React from "react";
import Box from "@mui/material/Box";
import TableContainer from "@mui/material/TableContainer";
import Paper from "@mui/material/Paper";
import {
  Button,
  CircularProgress,
  Collapse,
  Grid,
  Pagination,
  Skeleton,
  Stack,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import SearchField from "../../SearchField";
import { TablaAdministrarVentas } from "./TablaAdministrarVentas";
import { useNavigate, useSearchParams } from "react-router-dom";
import BotonesFiltroOrigenAdministrarVentas from "./BotonesFiltroOrigenAdministrarVentas";
import { obtenerTotalOrdenesGeneral } from "../../../redux/ordenesConsolidadas";
import Fab from "@mui/material/Fab";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import { styled } from "@mui/material/styles";
import { obtenerOrdenesConsolidadas, deleteDirectSaleC, colocarEstadoEliminandoVentaDirectaC } from "../../../redux/ordenesConsolidadas";
import { ContadorTooltip } from "../../componentesGenerales/Contador";
import { obtenerImagenesMarketplaces, obtenerTotalPedidos } from "../../../redux/pedidosDucks";
import ConfirmarEliminarProducto from "../../productosComponentes/ConfirmarEliminarProducto"
import { SkeletonTables } from "../../componentesGenerales/SkeletonTables";
import { getTaxes } from "../../../redux/ventaDirecta";
import ClearAllIcon from "@mui/icons-material/ClearAll";
import RefreshIcon from "@mui/icons-material/Refresh";

export default function AdministrarVentas() {
  // state variables for the component
  const [searchParams] = useSearchParams();
  const theme = useTheme();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [buttons, setButtons] = React.useState([]);
  const today = new Date();
  const [selectedButtons, setSelectedButtons] = React.useState([]);
  //get the search text from the url
  const searchText =
    searchParams.get("search") === null ? "" : searchParams.get("search");
  //get the number of pages from the redux store
  const cantidadDePaginas = useSelector(
    (store) => store.ordenesConsolidadas.cantidadDePaginas
  );

  const numConsolidate = useSelector(
    (store) => store.ordenesConsolidadas.numConsolidate
  );

  //get the marketplaces available from the redux store
  const origenes = useSelector(
    (store) => store.ordenesConsolidadas.origenDeOrden
  );

  //get the number of orders per page from the redux store
  const cantidadDePedidosPorPagina = useSelector(
    (store) => store.ordenesConsolidadas.nextC
  );
  const ordenesConsolidadas = useSelector(
    (store) => store.ordenesConsolidadas.ordenesConsolidadas
  );

  const isEmpty = useSelector((store) => store.ordenesConsolidadas.isEmpty);

  const numOrders = useSelector((store) => store.ordenesConsolidadas.numOrders);
  //get the loading state from the redux store
  const loadingConso = useSelector((store) => store.ordenesConsolidadas.loadingConso);
  const estadoEliminandoVentaDirectaC = useSelector(
    (store) => store.ordenesConsolidadas.estadoEliminandoVentaDirectaC
  );
  const messageDeletingElementDirectSaleC = useSelector(
    (store) => store.ordenesConsolidadas.messageDeletingElementDirectSaleC
  );

  const marketplaces = useSelector((store) => store.pedidos.marketplaces);

  // get the marketplace filter from the url
  const filtro =
    searchParams.get("filtro") === null
      ? marketplaces ? `${origenes.map(marketplace => marketplace.id || marketplace.marketplaceId).join('-')}` : "ds-1-2-3-4-5-7"
      : searchParams.get("filtro");

  // get the initial date from the url
  const initialDate =
    searchParams.get("initialDate") === null
      ? new Date(today.getFullYear(), today.getMonth() - 1, 1)
        .toISOString()
        .split("T")[0]
      : searchParams.get("initialDate");
  // get the final date from the url
  const finalDate =
    searchParams.get("finalDate") === null
      ? new Date(today.getFullYear(), today.getMonth(), today.getDate())
        .toISOString()
        .split("T")[0]
      : searchParams.get("finalDate");
  // get the page from the url
  // const page = searchParams.get("page") === null ? 1 : searchParams.get("page");
  const page = React.useRef(searchParams.get("page") === null ? 1 : searchParams.get("page"));
  //set the offset value for the query
  // state variable to show the filters
  const [showingFilters, setShowingFilters] = React.useState(false);
  // make the number of orders and the orders petition to the server when the component mounts

  useEffect(() => {
    dispatch(obtenerTotalOrdenesGeneral(initialDate, finalDate, searchText, filtro));
    dispatch(obtenerOrdenesConsolidadas(initialDate, finalDate, searchText, filtro, page.current));
    // page.current = 1;
  }, []);
  //create the buttons for the filters using the origenes array with the available marketplaces and direct sell


  useEffect(() => {
    if (origenes?.length > 0) {
      const nuevoArreglo = origenes.map((origen) => ({
        supportedMarketplaceId:
          origen.supportedMarketplace.id || origen.supportedMarketplace.supportedMarketplaceId,
        supportedMarketplaceName:
          origen.supportedMarketplace.name || origen.supportedMarketplace.supportedMarketplaceName,
        selected: true,
      }));
      setButtons(nuevoArreglo);
    }

  }, [origenes]);
  //get the selected buttons from the buttons array
  useEffect(() => {
    const nuevoArreglo = buttons.filter((button) => button.selected === true);
    setSelectedButtons(nuevoArreglo);
  }, [buttons]);
  // Function to send the search text to the server
  const enviarSearch = (event) => {
    event.preventDefault();
    var nuevoSearch = event.target[1].value;
    dispatch(obtenerTotalOrdenesGeneral(initialDate, finalDate, nuevoSearch, filtro));
    dispatch(obtenerOrdenesConsolidadas(initialDate, finalDate, nuevoSearch, filtro, page.current));
    page.current = 1;
    navigate(
      `?&search=${nuevoSearch}&initialDate=${initialDate}&finalDate=${finalDate}&filtro=${filtro}&page=${1}&next=${cantidadDePedidosPorPagina}&offset=${0}`
    );
  };

  useEffect(() => {
    // dispatch(getTaxes({
    //   orderInternalStatusId: "1-2-3-4",
    //   initialDate: initialDate,
    //   finalDate: finalDate,
    //   search : searchText,
    //   page: page.current,
    // }));
    dispatch(getTaxes(
      "1-2-3-4",
      initialDate,
      finalDate,
      page.current,
      searchText,
    ));

    dispatch(obtenerImagenesMarketplaces(origenes));
  }, [origenes]);
  // Function to change the page
  const cambioDePagina = (evento, valor) => {
    dispatch(
      obtenerOrdenesConsolidadas(initialDate, finalDate, searchText, filtro, valor)
    );
    page.current = valor;
    navigate(
      `?&search=${searchText}&initialDate=${initialDate}&finalDate=${finalDate}&filtro=${filtro}&page=${valor}`
    );
  };
  // Styled components for the filters
  const ItemLeft = styled(Paper)(() => ({
    textAlign: "left",
    boxShadow: "none",
    minWidth: "0px",
    height: "0px",
    // marginTop: "1em",
  }));
  // Filters component
  const FiltersGrid = () => {
    return (
      <Box sx={{ flexGrow: 1, mb: 1 }}>
        <Grid
          container
          spacing={2}
          sx={{
            justifyContent: "space-evenly",
            gridTemplateColumns: "auto 12fr auto", // Define las columnas según tus necesidades
          }}
        >
          <Grid item>
            <ItemLeft
              sx={{
                zIndex: 1,
              }}
            >
              <Fab
                size="medium"
                color="buttonGreen"
                aria-label="add"
                style={
                  showingFilters
                    ? { marginBottom: "5px" }
                    : {
                      marginBottom: "5px",
                      // position: isSmallScreen ? "fixed" : "inherit",
                      zIndex: 1,
                    }
                }
                onClick={() => setShowingFilters(!showingFilters)}
              >
                <FilterAltIcon />
              </Fab>
            </ItemLeft>
          </Grid>
          <Grid item xs={10}>
            {/* Este item ocupa 5 columnas */}
            {/* <SearchField enviarSearch={enviarSearch} searchText={searchText} /> */}
          </Grid>
          <Grid item>{/* Este item ocupa 1 columna */}</Grid>
        </Grid>
      </Box>
    );
  };

  const themeBreak = useTheme();
  const isMediumScreen = useMediaQuery(themeBreak.breakpoints.down("md"));
  // abajo de 700
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  // abajo de 1280
  const isDownLargeScreen = useMediaQuery(theme.breakpoints.down("lg"));
  // entre 960 y 1060
  const detailOperationScreen = useMediaQuery(
    theme.breakpoints.between("md", "detailOperationScreenNumber")
  );
  const isSxScreen = useMediaQuery(theme.breakpoints.down("xs"));

  const [ordenesConsolidadasState, setOrdenesConsolidadasState] = React.useState([]);

  useEffect(() => {
    if (ordenesConsolidadas) {
      setOrdenesConsolidadasState(ordenesConsolidadas);
      return
    }
    setOrdenesConsolidadasState(null);
  }
    , [ordenesConsolidadas]);

  if ((ordenesConsolidadasState === null) && !loadingConso) {
    if (loadingConso) return
    return <Typography variant="h6" align="center" color="textSecondary">No hay ventas</Typography>;
  }


  const [openDialogDelete, setOpenDialogDelete] = React.useState(false)
  const [idDialog, setIdDialog] = React.useState(null);


  // Add this function to handle page refresh
  const handleRefreshPage = () => {
    window.location.reload();
  };

  // Add this function to clear all filters
  const handleClearFilters = () => {
    const filtro = marketplaces ? `${origenes.map(marketplace => marketplace.id || marketplace.marketplaceId).join('-')}` : "ds-1-2-3-4-5-7"
    dispatch(obtenerTotalOrdenesGeneral(initialDate, finalDate, "", filtro));
    dispatch(obtenerOrdenesConsolidadas(initialDate, finalDate, "", filtro, 1));
    navigate(`?page=1`);
  };

  return (
    <>
      <ConfirmarEliminarProducto
        open={openDialogDelete}
        setOpen={setOpenDialogDelete}
        idElement={idDialog}
        idName={"id"}
        urlReturn={"/ventas/administrar"}
        nameElement={"venta"}
        deleteElementFunction={deleteDirectSaleC}
        deleteElementState={estadoEliminandoVentaDirectaC}
        messageDeletingElement={messageDeletingElementDirectSaleC}
      />
      {!(ordenesConsolidadas?.length > 0) && !isEmpty ? (
        <SkeletonTables />
      ) : (
        <>
          <Box

            sx={{
              display: "flex", width: "100%",
              justifyContent: "center",
              alignItems: "center",
              padding: "0 1em",
              marginBottom: "2em",
            }}
          >
            
            <FiltersGrid loadingConso={loadingConso} />

            {/* aqui se llama el componente que refleja el numero de ordenes por partida */}
            <Box sx={{ display: "flex", gap: 2, margin: "auto 0", mr: "1em", flexWrap: "wrap" }}>

              <Tooltip title="Actualizar página" placement="top" arrow>
                <Button
                  variant="outlined"
                  color="buttonGreenPink"
                  onClick={handleRefreshPage}
                  startIcon={<RefreshIcon />}
                  size="small"
                >
                  {!isSmallScreen && "Actualizar"}
                </Button>
              </Tooltip>

              <Tooltip title="Limpiar filtros" placement="top" arrow>
                <Button
                  variant="outlined"
                  color="buttonGreenPink"
                  onClick={handleClearFilters}
                  startIcon={<ClearAllIcon />}
                  size="small"
                >
                  {!isSmallScreen && "Limpiar filtros"}
                </Button>
              </Tooltip>
            </Box>

            <ContadorTooltip
              totalProductosPedidos={numOrders}
              totalOrders={numConsolidate}
              isOrders={true}
              page={parseInt(page.current)}
            />
            {/* <ContadorTooltip
              totalProductosPedidos={totalProductosPedidos}
              totalOrders={totalOrders}
              isOrders={true}
              page={page}
            /> */}
          </Box>
          <Collapse in={showingFilters} timeout="auto">

            <BotonesFiltroOrigenAdministrarVentas
              filtro={filtro}
              selectedButtons={selectedButtons}
              buttons={buttons}
              setButtons={setButtons}
              setShowingFilters={setShowingFilters}
              showingFilters={showingFilters}
            />
          </Collapse>
          {loadingConso ? <SkeletonTables /> : isEmpty ?
            <Typography variant="h6" align="center" color="textSecondary">No hay ventas</Typography>
            : (
              <TableContainer component={Paper}
                sx={{
                  borderRadius: "25px",
                }}
              >
                <TablaAdministrarVentas
                  consolidate={ordenesConsolidadas}
                  marketplaces={origenes}
                  setOpenDialogDelete={setOpenDialogDelete}
                  setIdDialog={setIdDialog}
                  colocarEstadoEliminandoVentaDirecta={colocarEstadoEliminandoVentaDirectaC}
                />
              </TableContainer>
            )}
          <Box
            sx={{ marginTop: "1%", justifyContent: "center", display: "flex" }}
          >
            <Stack spacing={2}>
              <Pagination
                count={cantidadDePaginas}
                page={parseInt(page.current)}
                onChange={cambioDePagina}
                color="primary"
              />
            </Stack>
          </Box>
        </>
      )}
    </>
  );
}
