import React, { useEffect } from "react";
import { Checkbox, Table, TableBody, TableHead, TableRow } from "@mui/material";
import { ConsolidateRow } from "./FuncionRowAdminVentas";
import { StyledTableCell } from "../../StyledTableComponents";
import consolidateCss from "../../css/ventas/consolidate.module.css";
import { useDispatch } from "react-redux";
import { getTaxes } from "../../../redux/ventaDirecta";
import { useSelector } from "react-redux";
import {
  deleteDirectSale,
  imprimirGuiaDeUnaVenta,
} from "../../../redux/ventaDirecta";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  Typography,
  Box,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useMediaQuery } from "@mui/material";
import { SurtidoVenta } from "../SurtidoVentaDirecta/SurtidoVentaDirectaWrapper";

export const TablaAdministrarVentas = (props) => {
  const { consolidate, marketplaces, setOpenDialogDelete, setIdDialog, colocarEstadoEliminandoVentaDirecta } = props;
  const [checked, setChecked] = React.useState([]);
  const dispatch = useDispatch();
  const [openDeleteSale, setOpenDeleteSale] = React.useState(false);
  const [idDeleteSale, setIdDeleteSale] = React.useState("");
  const [openPrint, setOpenPrint] = React.useState(false);

  const [openSurtir, setOpenSurtir] = React.useState(false);

  const marketplaceLogos = useSelector(
    (store) => store.pedidos.marketplaceLogos
  );
  const messageSurtido = useSelector((store) => store.pedidos.messageSurtido);
  const severitySurtido = useSelector((store) => store.pedidos.severitySurtido);
  const surtido = useSelector((store) => store.pedidos.pedidoSurtir);

  const guiaEspera = useSelector((store) => store.ventaDirecta.guiaEspera);
  const guiaContenido = useSelector(
    (store) => store.ventaDirecta.guiaContenido
  );

  const role = useSelector((store) => store.usuario.role);
  const namesStatus = useSelector((store) => store.ordenesConsolidadas.status);
  const namesStatusInternoV2 = useSelector((store) => store.ordenesConsolidadas.orderInternalStatus);

  const isAdminn = () => {
    if (role === "Admin" || role === "Administrative_accountant") return true;
    return false;
  };

  // son mis constantes de media query ->->-<-<-<

  const themeBreak = useTheme();
  const theme = useTheme();

  const isMediumScreen = useMediaQuery(themeBreak.breakpoints.down("md"));

  // abajo de 700
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  // abajo de 1280
  const isDownLargeScreen = useMediaQuery(theme.breakpoints.down("lg"));
  // entre 960 y 1060
  const detailOperationScreen = useMediaQuery(
    theme.breakpoints.between("md", "detailOperationScreenNumber")
  );

  const isSxScreen = useMediaQuery(theme.breakpoints.down("xs"));

  const handleCheckAll = (event) => {
    if (event.target.checked) {
      const newSelecteds = consolidate.map((n) => n.id);
      setChecked(newSelecteds);
      return;
    }
    setChecked([]);
  };

  const esURLPDF = (url) => {
    // Verifica si la URL es not found
    return url.endsWith("NotFoundPage");
  };

  const handleOpenActions = () => {
    setOpenDeleteSale(true);
  };

  const handleCloseActions = () => {
    setOpenDeleteSale(false);
  };

  const handleCloseSurtir = () => {
    setOpenSurtir(false);
  };

  const handleOpenActionsPrint = (data) => {
    dispatch(imprimirGuiaDeUnaVenta(data));
    setOpenPrint(true);
  };

  const handleCloseActionsPrint = () => {
    setOpenPrint(false);
  };

  const handleDeleteSale = async () => {
    dispatch(deleteDirectSale(idDeleteSale));
    handleCloseActions();
  };


  // <TableContainer sx={{
  //   borderRadius: "25px",
  // }}>

  return (
    <>
      <Table
        aria-label="collapsible table"
        sx={{ width: "100%", margin: "auto", padding: "1rem", borderRadius: "25px", }}
        className={consolidateCss.ecomicTable}
      >
        <TableHead
          sx={{
            borderBottom: "2px solid",
          }}>
          <TableRow>
            {isSmallScreen ? null : (
              <StyledTableCell
                sx={isDownLargeScreen ? { padding: "0", width: "24px" } : {}}
              >
                <Checkbox
                  inputProps={{ "aria-label": "primary checkbox" }}
                  checked={checked.length === consolidate?.length}
                  onChange={handleCheckAll}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "#fff",
                    "& .MuiSvgIcon-root": { color: "#fff" },
                  }}
                />
              </StyledTableCell>
            )}
            {!isDownLargeScreen ? (
              <StyledTableCell align="center">Vendedor</StyledTableCell>
            ) : null}
            {isMediumScreen ? null : (
              <StyledTableCell align="center">
                Datos de la venta
              </StyledTableCell>
            )}
            {isAdminn() ? (
              detailOperationScreen || isMediumScreen ? null : (
                <StyledTableCell align="center">Detalles</StyledTableCell>
              )
            ) : null}
            {isSxScreen ? null : (
              <StyledTableCell align="center">Imagen</StyledTableCell>
            )}
            <StyledTableCell align="center">Descripción</StyledTableCell>
            {isSmallScreen ? null : (
              <StyledTableCell align="center">Status</StyledTableCell>
            )}
          </TableRow>
        </TableHead>
        <TableBody>
          {consolidate?.map((sale, i) => (
            <ConsolidateRow
              key={sale.id}
              row={sale}
              handleOpenActions={handleOpenActions}
              handleOpenActionsPrint={handleOpenActionsPrint}
              setIdDeleteSale={setIdDeleteSale}
              checked={checked}
              setChecked={setChecked}
              marketplaces={marketplaces}
              marketplaceLogos={marketplaceLogos}
              isSmallScreen={isSmallScreen}
              isMediumScreen={isMediumScreen}
              isDownLargeScreen={isDownLargeScreen}
              detailOperationScreen={detailOperationScreen}
              isSxScreen={isSxScreen}
              setOpenSurtir={setOpenSurtir}
              setOpenDialogDelete={setOpenDialogDelete}
              setIdDialog={setIdDialog}
              colocarEstadoEliminandoVentaDirecta={colocarEstadoEliminandoVentaDirecta}
              index={i}
              namesStatus={namesStatus}
              namesStatusInternoV2={namesStatusInternoV2}
            />
          ))}
        </TableBody>
      </Table>
      <Dialog
        open={openDeleteSale}
        onClose={handleCloseActions}
        disableBackdropClick
      >
        <DialogTitle>Eliminar venta</DialogTitle>
        <DialogContent>
          <DialogContentText>
            ¿Estás seguro que quieres eliminar esta venta?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseActions} color="error">
            Cerrar
          </Button>
          <Button onClick={handleDeleteSale} color="success">
            Aceptar
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openPrint}
        onClose={handleCloseActionsPrint}
        disableBackdropClick
        maxWidth="xl"
        fullWidth
      >
        <DialogTitle>Pdf Producto</DialogTitle>
        <DialogContent sx={{ width: "100%", height: "90vh" }}>
          <Box sx={{ width: "100%", height: "100%", display: "flex" }}>
            {guiaEspera ? (
              "Cargando..."
            ) : guiaContenido ? (
              esURLPDF(guiaContenido) ? (
                <Typography>No se obtuvo una url</Typography>
              ) : (
                <iframe
                  src={guiaContenido}
                  width="100%"
                  height="95%"
                  title="PDF Viewer"
                />
              )
            ) : (
              <Typography>No se pudo cargar la guía</Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseActionsPrint} color="error">
            Cerrar
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openSurtir}
        onClose={handleCloseSurtir}
        maxWidth="xl" // Ajusta el ancho máximo al valor que necesites
        fullWidth={true} // Hace que el diálogo ocupe todo el ancho
      >
        <DialogTitle>Surtido</DialogTitle>
        <DialogContent>
          <SurtidoVenta
            message={messageSurtido}
            severity={severitySurtido}
            origin={"marketplace"}
            surtido={surtido}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};
