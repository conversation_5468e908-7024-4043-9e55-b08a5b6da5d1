import { Alert } from '@mui/material'
import React from 'react'
import { useDispatch } from 'react-redux';

export const MessageAlert = ( props ) => {
    const { message, cleanMessage } = props;
    const dispatch = useDispatch();
  return (
    <Alert
        sx={{
        position: "sticky",
        bottom: "3rem",
        left: "2rem",
        maxWidth: "40%",
        }}
        onClose={() => dispatch(cleanMessage())}
        severity="warning"
    >
        {message}
    </Alert>
  )
}