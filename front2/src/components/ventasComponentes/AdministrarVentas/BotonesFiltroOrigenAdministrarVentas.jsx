import React from 'react'
import { Button, Checkbox, FormControl, InputLabel, ListItemText, MenuItem, OutlinedInput, Select } from '@mui/material'
import { styled } from '@mui/material/styles';
import { Box, Stack, TextField } from "@mui/material";
import { DesktopDatePicker } from "@mui/x-date-pickers/DesktopDatePicker";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { useState } from 'react';
import { Alert } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';
import { ButtonGroup } from '@mui/material';
import { Navigate, useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { obtenerOrdenesConsolidadas, obtenerTotalOrdenesGeneral } from '../../../redux/ordenesConsolidadas';
import ModalGeneral from '../../ModalGeneral';
import { getDirectSale, getTaxes } from '../../../redux/ventaDirecta';

const formatFecha = (fecha) => {
    if (fecha instanceof Date) {
        // Si es una fecha tipo Date (como Mon Sep 30 2024 ...)
        return fecha.toISOString().split("T")[0];
    } else if (typeof fecha === "string") {
        // Si es una cadena (como "2024-10-08")
        return fecha;
    } else {
        throw new Error("Formato de fecha no soportado");
    }
};

const BotonesFiltroOrigenAdministrarVentas = (props) => {
    //state variables for the component
    const theme = useTheme();
    const filtro = props.filtro;
    const buttons = props.buttons;
    const setButtons = props.setButtons;
    const showingFilters = props.showingFilters;
    const selectedButtons = props.selectedButtons;
    const setShowingFilters = props.setShowingFilters;
    const isDirectSale = props.isDirectSale;
    const taxes = props.taxes;
    const selectedInternalStatus = props.selectedInternalStatus;
    const handleChangeInternalStatus = props.handleChangeInternalStatus;
    const MenuProps = props.MenuProps;
    const pageProps = props.page;
    const handleSelectAll = props.handleSelectAll;
    const [mensajeModal, setMensajeModal] = React.useState("");
    const [open, setOpen] = React.useState(false);

    //get the params from the url
    const [searchParams, setSearchParams] = useSearchParams();
    const navigate = useNavigate();

    const today = new Date();
    const dispatch = useDispatch();

    const dateeee = new Date(today.getFullYear(), today.getMonth())
    const noseee = today.getMonth();


    const oneMonthAgo = new Date(
        today.getFullYear(),
        today.getMonth() - 1,
        today.getDate()
    );

    const fecha = new Date();

    const añoActual = fecha.getFullYear();
    const hoy = fecha.getDate();
    const mesActual = fecha.getMonth();
    const mesFormateado = mesActual < 10 ? `0${mesActual}` : mesActual;
    const diaFormateado = hoy < 10 ? `0${hoy}` : hoy;

    const initialDate =
        searchParams.get(isDirectSale ? "startDate" : "initialDate") === null
            ? oneMonthAgo.toISOString().split("T")[0]
            : searchParams.get(isDirectSale ? "startDate" : "initialDate");

    const finalDate =
        searchParams.get(isDirectSale ? "endDate" : "finalDate") === null
            ? today.toISOString().split("T")[0]
            : searchParams.get(isDirectSale ? "endDate" : "finalDate");

    const fechaFormateada = `${añoActual}-${mesFormateado}-${diaFormateado}`;

    // Inicialización de los estados con React.useState
    const [initialDatePicker, setInitialDatePicker] = React.useState(
        searchParams.get(isDirectSale ? "startDate" : "initialDate") === null
            ? oneMonthAgo // Fecha de hace un mes
            : searchParams.get(isDirectSale ? "startDate" : "initialDate")
    );

    const [finalDatePicker, setFinalDatePicker] = React.useState(
        searchParams.get(isDirectSale ? "endDate" : "finalDate") === null
            ? today
            : searchParams.get(isDirectSale ? "endDate" : "finalDate")
    );

    const themeBreak = useTheme();
    const isMediumScreen = useMediaQuery(themeBreak.breakpoints.down("md"));
    // abajo de 700
    const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
    // abajo de 1280
    const isDownLargeScreen = useMediaQuery(theme.breakpoints.down("lg"));
    // entre 960 y 1060
    const detailOperationScreen = useMediaQuery(
        theme.breakpoints.between("md", "detailOperationScreenNumber")
    );

    const getSxDate = (isSmallScreen, isMediumScreen) => {
        if (isMediumScreen) {
            return {
                width: "60%",
                margin: "5px",
                marginTop: "7px",
                minWidth: "150px",
                maxWidth: "60%",
                display: "flex",
                flexDirection: "column",
                columnGap: "10px",
            };
        }

        if (isSmallScreen) {
            return {
                margin: "1em auto auto auto",
                width: "100%",
                columnGap: "10px",
                bacground: "green",
                minWidth: "150px",
                maxWidth: "200px",
                display: "flex",
                flexDirection: "row",
            };
        }

        return {
            width: "100%",
            minWidth: "150px",
            maxWidth: "200px",
            display: "flex",
            flexDirection: "row",
            margin: "auto",
        };
    };

    const isSxScreen = useMediaQuery(theme.breakpoints.down("xs"));
    // autocomplete style
    const getSxAutoYDate = (isSmallScreen, isMediumScreen) => {
        if (isSmallScreen) {
            return {
                paddingBottom: "2em",
                display: "flex",
                flexDirection: "column",
                width: "90%",
                margin: "auto",
            };
        }

        if (isMediumScreen) {
            return {
                display: "flex",
                flexDirection: "column",
                width: "90%",
            };
        }
        return {
            display: "flex",
            width: "90%",
            margin: "auto",
        };
    };


    const getSxContentFiltroDepoyable = (isSmallScreen) => {
        if (isSmallScreen) {
            return {
                display: "flex",
                flexDirection: "column",
                width: "90%",
                // backgroundColor: "green",
                margin: "auto",
                alignItems: "center",
                justifyContent: "center",
                paddingBottom: "2em",
            };
        }

        return {
            margin: "5px",
            width: "97%",
            position: "relative",
            paddingBottom: "2em",
        };
    };

    const getSxFiltroDepoyable = getSxContentFiltroDepoyable(isSmallScreen);

    // dentro de tu componente
    const sxPropsAutoYDate = getSxAutoYDate(isSmallScreen, isMediumScreen);

    //Small view buttons style
    const getStylesMarketPlaceButton = () => {
        if (isSmallScreen) {
            return {
                marginBottom: "2px",
                width: "auto",
                borderRadius: "50px",
                // background: "green",
            };
        }
        return { marginBottom: "2px", width: "160px" };
    };

    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    //function to handle the marketplaces buttons click
    const handleButtonClick = (button) => {
        const id = button.supportedMarketplaceId;
        const selected = button.selected;
        const newButtons = buttons.map((button) => {
            if (button.supportedMarketplaceId === id) {
                return { ...button, selected: !selected };
            }
            return button;
        });
        setButtons(newButtons);
    };

    // mostrar los selecionados cada vez que se actualice el componente
    useEffect(() => {
        if (!isDirectSale && showingFilters) {
            // const filtro = searchParams.get("filtro");
            if (filtro !== null) {
                const origenesFiltrados = filtro.split("-");
                const newButtons = buttons.map((button) => {
                    // Ensure both are compared as strings
                    if (origenesFiltrados.includes(String(button.supportedMarketplaceId))) {
                        return { ...button, selected: true };
                    }
                    return { ...button, selected: false };
                });
                setButtons(newButtons);
            }
        }
    }, [showingFilters]);




    //function to handle the filter button click
    const handleSendFilter = () => {
        let mensaje = "";
        const origenesFiltrados = selectedButtons.map((button) => button.supportedMarketplaceId);
        const nuevoFiltro = origenesFiltrados.join("-");
        const fei = initialDate
        const fef = finalDate
        const fechaInicial = formatFecha(initialDatePicker);
        const fechaFinal = formatFecha(finalDatePicker);
        const searchtext = searchParams.get("search") === null ? "" : searchParams.get("search");
        const page = searchParams.get("page") === null ? 1 : searchParams.get("page");
        const offset = 30 * (page - 1);
        if (initialDatePicker > today) {
            mensaje = mensaje + "La fecha de inicio no es valida\n";
        }
        if (finalDatePicker > today) {
            mensaje = mensaje + "La fecha de final no es valida\n";
        }
        if (finalDatePicker - initialDatePicker < 0) {
            mensaje = mensaje + "El rango de fechas seleccionados no es válido \n";
        }


        if (mensaje !== "") {
            setMensajeModal(mensaje);
            handleOpen();
            return;
        }
        if (!isDirectSale) {
            dispatch(obtenerTotalOrdenesGeneral(fechaInicial, fechaFinal, searchtext, nuevoFiltro));
            dispatch(obtenerOrdenesConsolidadas(fechaInicial, fechaFinal, searchtext, nuevoFiltro, page));
            setShowingFilters(false);
            navigate(
                `?&search=${searchtext}&initialDate=${fechaInicial}&finalDate=${fechaFinal}&filtro=${nuevoFiltro}&page=${1}`
            )
            setSearchParams({
                search: searchtext,
                initialDate: fechaInicial,
                finalDate: fechaFinal,
                filtro: nuevoFiltro,
                page: 1,
            });
        } else {
            const newIds = taxes.directSaleInternalStatuses
                .filter((item) => selectedInternalStatus.includes(item.directSaleInternalStatus))
                .map((item) => item.directSaleInternalStatusId)
                .join('-');

            // dispatch(getTaxes({
            //     orderInternalStatusId: newIds,
            //     startDate: fechaInicial,
            //     endDate: fechaFinal,
            //     search: searchtext,
            // }));

            dispatch(getDirectSale({
                orderInternalStatusId: newIds,
                startDate: fechaInicial,
                endDate: fechaFinal,
                search: searchtext,
            }));

            // Establecemos la página a 1 y navegamos después del tiempo de espera
            pageProps.current = 1;
            navigate(`?orderInternalStatusId=${newIds}&startDate=${fechaInicial}&endDate=${fechaFinal}&search=${searchtext}&page=${pageProps.current}`);
            setSearchParams({
                orderInternalStatusId: newIds,
                startDate: fechaInicial,
                endDate: fechaFinal,
                search: searchtext,
                page: pageProps.current,
            });
        }
    }

    //create the buttons style
    const ComponentBtn = isSmallScreen ? Box : ButtonGroup;

    return (
        <>
            <Box display="flex" 
                style={{
                    display: "flex", width: "80%", justifyContent: "center", alignItems: "center", margin: "auto"

                }}
                sx={{
                    ...getSxFiltroDepoyable,
                    flexDirection: {
                        xss: "column",
                        xs: "column",
                        sm: "row",
                    }
                }}
            >
                <ModalGeneral
                    open={open}
                    handleClose={handleClose}
                    mensajeModal={mensajeModal}
                />
                {!isDirectSale && (
                    <Box sx={{ width: "minWidth", margin: "5px" }}>
                        {/* <Box> */}
                        <Box
                            display="flex"
                            flexDirection="column"
                            alignItems="center"
                            justifyContent="center"
                        >
                            <b style={{ marginBottom: "4px" }}>Marketplace</b>
                            <ComponentBtn
                                sx={
                                    isSmallScreen
                                        ? {
                                            display: "flex",
                                            flexWrap: "wrap",
                                            gap: "15px",
                                            justifyContent: "space-around",
                                        }
                                        : {}
                                }
                                orientation="vertical"
                                aria-label="vertical contained button group"
                                variant="contained"
                            >
                                {buttons.map((button) => (
                                    <Button
                                        key={button.supportedMarketplaceId}
                                        variant={button.selected ? "contained" : "outlined"}
                                        onClick={() => handleButtonClick(button)}
                                        color="buttonGreenPink"
                                        sx={{
                                            ...getStylesMarketPlaceButton(),
                                            flex: isSmallScreen ? "1 1 calc(50% - 15px)" : undefined, // dos botones por fila
                                            minWidth: "140px", // opcional, para mantener proporción visual
                                        }}
                                    >
                                        {button.supportedMarketplaceName}
                                    </Button>
                                ))}
                            </ComponentBtn>

                            {1 === 0 ? (
                                <Alert
                                    variant="filled"
                                    severity="error"
                                    justifyContent="center"
                                    style={{ marginTop: "6px", width: "160px" }}
                                >
                                    Seleccionar Origen
                                </Alert>
                            ) : null}
                        </Box>
                    </Box>
                )}

                {isDirectSale && (
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: "start" }}>
                        <FormControl sx={{ m: 1, width: 350 }}>
                            <InputLabel id="demo-multiple-checkbox-label">Tag</InputLabel>
                            <Select
                                labelId="demo-multiple-checkbox-label"
                                id="demo-multiple-checkbox"
                                multiple
                                value={selectedInternalStatus} // El valor debe ser un array
                                onChange={handleChangeInternalStatus}
                                input={<OutlinedInput label="Estatus" />}
                                renderValue={(selected) => selected.join(', ')} // Mostrar los seleccionados
                                MenuProps={MenuProps}
                            >
                                {/* Opción "Seleccionar todos" */}
                                <MenuItem value="all" onClick={handleSelectAll}>
                                    <Checkbox
                                        checked={selectedInternalStatus.length === taxes?.directSaleInternalStatuses.length}
                                        indeterminate={selectedInternalStatus.length > 0 && selectedInternalStatus.length < taxes.directSaleInternalStatuses.length}
                                        color='buttonGreenPink'
                                        sx={{
                                            padding: "0",
                                            '&.Mui-checked': {
                                                color: theme => theme.palette.primary.main,
                                            }
                                        }}
                                    />
                                    <ListItemText primary={selectedInternalStatus.length === taxes?.directSaleInternalStatuses.length ? 'Deseleccionar todo' : 'Seleccionar todo'} />
                                </MenuItem>

                                {/* Renderizar los ítems normales */}
                                {taxes?.directSaleInternalStatuses.map((item) => (
                                    <MenuItem key={item.directSaleInternalStatusId} value={item.directSaleInternalStatus}>
                                        <Checkbox checked={selectedInternalStatus.includes(item.directSaleInternalStatus)}
                                            sx={{
                                                padding: "0",
                                                '&.Mui-checked': {
                                                    color: theme => theme.palette.primary.main,
                                                }
                                            }}
                                        />
                                        <ListItemText primary={item.directSaleInternalStatus} />
                                    </MenuItem>
                                ))}
                            </Select>

                        </FormControl>
                    </div>
                )}

                {/* fechas */}
                <Box
                    sx={{ display: "flex", alignItems: "center", justifyContent: "center", width: "100%", flexDirection: "column" }}
                >
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                        <Box
                            sx={{
                                display: "flex",
                                width: "100%",
                                // margin: "1em auto auto auto",
                                margin: "1em",
                                marginX: "auto",
                                columnGap: "10px",
                                justifyContent: "space-evenly",
                            }}
                        >
                            <Box
                                sx={
                                    {
                                        display: "flex",
                                        width: "30%",
                                        flexDirection: "column",
                                    }
                                }
                            >
                                <DesktopDatePicker
                                    sx={{ display: "flex", flexDirection: "row" }}
                                    label="Desde:"
                                    inputFormat="MM/dd/yyyy"
                                    value={initialDatePicker}
                                    onChange={(date) => {
                                        return setInitialDatePicker(date)
                                    }}
                                    renderInput={(params) => (
                                        <TextField {...params} />
                                    )}
                                />
                                {initialDatePicker > today && (
                                    <Alert
                                        variant="filled"
                                        severity="error"
                                        style={{ marginTop: "6px" }}
                                    >
                                        Fecha incorrecta!
                                    </Alert>
                                )}
                            </Box>
                            <Box
                                sx={{
                                    display: "flex",
                                    width: "30%",
                                    flexDirection: "column",
                                }}
                            >
                                <DesktopDatePicker
                                    sx={{ display: "flex", flexDirection: "row" }}
                                    label="Hasta:"
                                    inputFormat="MM/dd/yyyy"
                                    value={finalDatePicker}
                                    onChange={(date) => setFinalDatePicker(date)}
                                    renderInput={(params) => (
                                        <TextField {...params} />
                                    )}
                                />
                                {finalDatePicker > today && (
                                    <Alert
                                        variant="filled"
                                        severity="error"
                                        style={{ marginTop: "6px" }}
                                    >
                                        Fecha incorrecta!
                                    </Alert>
                                )}
                            </Box>
                        </Box>
                    </LocalizationProvider>
                    {finalDatePicker - initialDatePicker < 0 && (
                        <Alert
                            variant="filled"
                            severity="error"
                            style={{ marginTop: "6px", width: "80.5%" }}
                        >
                            Rango incorrecto!
                        </Alert>
                    )}
                </Box>

                <Box sx={{ width: "10%", margin: "5px", display: "flex", alignItems: "flex-center" }}>
                    <Button
                        variant="contained"
                        color="buttonGreenPink"
                        sx={{ width: "100%", alignSelf: "end" }}
                        onClick={() => { handleSendFilter() }
                        }
                    >
                        Filtrar
                    </Button>
                </Box>
            </Box>

        </>
    )
}

export default BotonesFiltroOrigenAdministrarVentas;
