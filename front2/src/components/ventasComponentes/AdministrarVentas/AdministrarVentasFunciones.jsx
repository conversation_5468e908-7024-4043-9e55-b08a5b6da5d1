import * as React from "react";
import Box from "@mui/material/Box";
import { Card, Tooltip } from "@mui/material";
import { tooltipClasses } from "@mui/material/Tooltip";
import { styled } from "@mui/material/styles";

export function createData(name, calories, fat, carbs, protein, price) {
  return {
    name,
    calories,
    fat,
    carbs,
    protein,
    price,
    history: [
      {
        date: "2020-01-05",
        customerId: "11091700",
        amount: 3,
      },
      {
        date: "2020-01-02",
        customerId: "Anonymous",
        amount: 1,
      },
    ],
  };
}

export const OutlinedCard = ({fecha, origin, cliente}) => {
  return (
    <Box sx={{ minWidth: 170 }}>
      {fecha}
      <br />
      {origin}
      <Card variant="outlined" sx={{ padding: "1em" }}>
        {cliente}
      </Card>
    </Box>
  );
};

export const DescriptionDataProduct = ({ product }) => {
  return (
    <>
      <Box sx={{ minWidth: 170 }}>
        <h5 style={{ color: "#003876" }}>
          {product.brand} - {product.model} -{" "}
        </h5>{" "}
        - {product.title}
        <br />
        cantidad: {product.units}
        <br />
      </Box>
    </>
  );
};

export const DescriptionData = ({ brand, model, title, units }) => {
  const LightTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: theme.palette.common.white,
      color: "rgba(0, 0, 0, 0.87)",
      boxShadow: theme.shadows[1],
      fontSize: 11,
    },
  }));

  return (
    <>
      <Box sx={{ minWidth: 170 }}>
        <Box>
          <p style={{ color: "#003876", margin: "0" }}>
            {/* {product.internalSku} */}
          </p>{" "}
          {title}
          <br />
          {brand} - {model} <br />
          cantidad: {units}
        </Box>
      </Box>
    </>
  );
};
