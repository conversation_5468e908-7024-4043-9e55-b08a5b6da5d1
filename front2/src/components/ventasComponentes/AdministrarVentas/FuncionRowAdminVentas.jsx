import * as React from "react";
import { useDispatch, useSelector } from "react-redux";
import DirectSaleRow from "../DirectSaleRow";
import { Row } from "../../ordenesComponentes/TablaDesplegable";

export const ConsolidateRow = (props) => {
  const {
    row,
    checked,
    setChecked,
    handleOpenActionsPrint,
    handleOpenActions,
    setIdDeleteSale,
    setOpenDialogDelete,
    setIdDialog,
    colocarEstadoEliminandoVentaDirecta,
  } = props;
  
  const directSaleInternalStatuses = useSelector((store) => store.ordenesConsolidadas.directSaleInternalStatus);
  
  const saleType = row.directSaleType ? "directSale" : "order";
  
  const {
    marketplaces,
    marketplaceLogos,
    isSmallScreen,
    isMediumScreen,
    isDownLargeScreen,
    detailOperationScreen,
    isSxScreen,
    setOpenSurtir,
    namesStatus,
    namesStatusInternoV2,
  } = props;

  return (
    <>
      {saleType === "directSale" ? (
        <DirectSaleRow
          key={row.id}
          id={row.id}
          row={row}
          directSaleInternalStatuses={directSaleInternalStatuses}
          handleOpenActionsPrint={handleOpenActionsPrint}
          handleOpenActions={handleOpenActions}
          setIdDeleteSale={setIdDeleteSale}
          ordenesSeleccionadas={checked}
          setOrdenesSeleccionadas={setChecked}
          type={"consolidate"}
          setIdDialog={setIdDialog}
          setOpenDialogDelete={setOpenDialogDelete}
          colocarEstadoEliminandoVentaDirecta={
            colocarEstadoEliminandoVentaDirecta
          }
          index={props.index}
        />
      ) : (
        <Row
          key={row.id}
          id={row.id}
          pedido={row}
          marketplaces={marketplaces}
          marketplaceLogos={marketplaceLogos}
          isSmallScreen={isSmallScreen}
          isMediumScreen={isMediumScreen}
          isDownLargeScreen={isDownLargeScreen}
          detailOperationScreen={detailOperationScreen}
          isSxScreen={isSxScreen}
          ordenesSeleccionadas={checked}
          setOrdenesSeleccionadas={setChecked}
          setOpenSurtir={setOpenSurtir}
          type="consolidate"
          index={props.index}
          namesStatus={namesStatus}
          namesStatusInternoV2={namesStatusInternoV2}
        />
      )}
    </>
  );
};
