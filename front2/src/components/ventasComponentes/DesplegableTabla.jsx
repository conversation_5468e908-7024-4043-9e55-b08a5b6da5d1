import { Box, Chip, TableBody, TableCell } from "@mui/material";
import React from "react";
import { Table, TableContainer, TableHead, TableRow } from "@mui/material";
import { getDiscount, getTaxes, getTotal, getUtility } from "./EconomicData";

import { useTheme } from "@mui/material/styles";
import { useMediaQuery } from "@mui/material";
import { useSelector } from "react-redux";

const DesplegableTabla = (props) => {
  const { row } = props;
  const role = useSelector((store) => store.usuario.role);

  const isAdminn = () => {
    if (role === "Admin" || role === "Administrative_accountant") return true;
    return false;
  };

  // son mis constantes de media query ->->-<-<-<

  const themeBreak = useTheme();
  const theme = useTheme();

  const isMediumScreen = useMediaQuery(themeBreak.breakpoints.down("md"));

  // abajo de 700
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  // abajo de 1280
  const isDownLargeScreen = useMediaQuery(theme.breakpoints.down("lg"));
  // entre 960 y 1060
  const detailOperationScreen = useMediaQuery(
    theme.breakpoints.between("md", "detailOperationScreenNumber")
  );

  const isSxScreen = useMediaQuery(theme.breakpoints.down("xs"));
  return (
    <>
      <div
        style={{
          boxShadow: "1px 1px 3px #********",
          marginBottom: "10px",
          marginTop: "10px",
        }}
      >
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Imagen</TableCell>
                {detailOperationScreen || isMediumScreen ? null : (
                  <>
                    <TableCell>SKU</TableCell>
                    <TableCell>Modelo</TableCell>
                    <TableCell>Marca</TableCell>
                  </>
                )}
                <TableCell>Variación</TableCell>
                <TableCell>Cantidad</TableCell>
                {isAdminn() ? (
                  !isDownLargeScreen ? (
                    <>
                      <TableCell>Total</TableCell>
                      <TableCell>Tarifa</TableCell>
                      <TableCell>Descuento</TableCell>
                      <TableCell>Recibidosasasssssssssssssssssssssssssss</TableCell>
                      <TableCell>Utilidad</TableCell>
                    </>
                  ) : (
                    isSmallScreen ? null : (<TableCell>Operaciones</TableCell>
                    )
                  )
                ) : null}
              </TableRow>
            </TableHead>
            <TableBody>
              {row.products?.map((product, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <img
                      src={product.productAtStore.product?.productImage}
                      alt="Imagen del producto"
                      style={{ width: "50px", height: "50px" }}
                    />
                  </TableCell>
                  {detailOperationScreen || isMediumScreen ? null : (
                    <>
                      <TableCell>
                        {product.productAtStore.product?.internalSku}
                      </TableCell>
                      <TableCell>
                        {product.productAtStore.product.productBase?.model}
                      </TableCell>
                      <TableCell>
                        {product.productAtStore.product.productBase?.brand}
                      </TableCell>
                    </>
                  )}
                  <TableCell>
                    {product.productAtStore.product?.productDescription}
                    <br />
                    {detailOperationScreen || isMediumScreen ? (
                      <>
                        <Chip
                          color="primary"
                          label={`SKU: ${product.productAtStore.product?.internalSku}`}
                          size="small"
                          variant="outlined"
                        />
                        <Chip
                          color="primary"
                          label={`Modelo: ${product.productAtStore.product.productBase?.model}`}
                          size="small"
                          variant="outlined"
                        />
                        <Chip
                          color="primary"
                          label={`Marca: ${product.productAtStore.product.productBase?.brand}`}
                          size="small"
                          variant="outlined"
                        />
                        { isAdminn() ?  isSmallScreen ? (
                          <Box>
                          <Chip
                            color="primary"
                            label={`Total: $${
                              product.units * product.costAtTimeOfSale
                            }`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            color="primary"
                            label={`Tarifa: $${
                              getTaxes(product).type === "nominal"
                                ? getTaxes(product).taxes
                                : getTaxes(product).type === "percent"
                                ? (
                                    (getTaxes(product).taxes / 100) *
                                    (product.units * product.costAtTimeOfSale)
                                  ).toFixed(2)
                                : 0
                            }`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            color="primary"
                            label={`Descuento: ${
                              getDiscount(product).type === "nominal"
                                ? getDiscount(product).discount
                                : getDiscount(product).type === "percent"
                                ? (
                                    (getDiscount(product).discount / 100) *
                                    (product.units * product.costAtTimeOfSale)
                                  ).toFixed(2)
                                : 0
                            }`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            color="primary"
                            label={`Recibido: $${getTotal(product).toFixed(2)}`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            color="primary"
                            label={`Utilidad: ${
                              getUtility(product).type === "nominal"
                                ? getUtility(product).utility.toFixed(2)
                                : getUtility(product).type === "percent"
                                ? (
                                    (getUtility(product).utility / 100) *
                                    (product.units * product.costAtTimeOfSale)
                                  ).toFixed(2)
                                : 0
                            }`}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                        ) : null : null}
                      </>
                    ) : null}
                  </TableCell>
                  <TableCell>{product.units}</TableCell>
                  {isAdminn() ? (
                    !isDownLargeScreen ? (
                      <>
                        <TableCell>
                          $
                          {(product.units * product.costAtTimeOfSale).toFixed(
                            2
                          )}
                        </TableCell>
                        <TableCell>
                          {getTaxes(product).type === "nominal" ? (
                            <span>${getTaxes(product).taxes}</span>
                          ) : getTaxes(product).type === "percent" ? (
                            <span>
                              $
                              {(
                                (getTaxes(product).taxes / 100) *
                                (product.units * product.costAtTimeOfSale)
                              ).toFixed(2)}
                            </span>
                          ) : (
                            <span>0</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {getDiscount(product).type === "nominal" ? (
                            <span>${getDiscount(product).discount}</span>
                          ) : getDiscount(product).type === "percent" ? (
                            <span>
                              $
                              {(
                                (getDiscount(product).discount / 100) *
                                (product.units * product.costAtTimeOfSale)
                              ).toFixed(2)}
                            </span>
                          ) : (
                            <span>0</span>
                          )}
                        </TableCell>
                        <TableCell>${getTotal(product).toFixed(2)}</TableCell>
                        <TableCell>
                          {getUtility(product).type === "nominal" ? (
                            <span>
                              ${getUtility(product).utility.toFixed(2)}
                            </span>
                          ) : getUtility(product).type === "percent" ? (
                            <span>
                              $
                              {(
                                (getUtility(product).utility / 100) *
                                (product.units * product.costAtTimeOfSale)
                              ).toFixed(2)}
                            </span>
                          ) : (
                            <span>0</span>
                          )}
                        </TableCell>
                      </>
                    ) : (
                      isSmallScreen ? null : (
                      <TableCell>
                        <Box sx={{ display: "flex", flexDirection: "column", flexWrap:"wrap" }}>
                          <Chip
                            color="primary"
                            label={`Total: $${
                              product.units * product.costAtTimeOfSale
                            }`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            color="primary"
                            label={`Tarifa: $${
                              getTaxes(product).type === "nominal"
                                ? getTaxes(product).taxes
                                : getTaxes(product).type === "percent"
                                ? (
                                    (getTaxes(product).taxes / 100) *
                                    (product.units * product.costAtTimeOfSale)
                                  ).toFixed(2)
                                : 0
                            }`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            color="primary"
                            label={`Descuento: ${
                              getDiscount(product).type === "nominal"
                                ? getDiscount(product).discount
                                : getDiscount(product).type === "percent"
                                ? (
                                    (getDiscount(product).discount / 100) *
                                    (product.units * product.costAtTimeOfSale)
                                  ).toFixed(2)
                                : 0
                            }`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            color="primary"
                            label={`Recibido: $${getTotal(product).toFixed(2)}`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            color="primary"
                            label={`Utilidad: ${
                              getUtility(product).type === "nominal"
                                ? getUtility(product).utility.toFixed(2)
                                : getUtility(product).type === "percent"
                                ? (
                                    (getUtility(product).utility / 100) *
                                    (product.units * product.costAtTimeOfSale)
                                  ).toFixed(2)
                                : 0
                            }`}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </TableCell>
                      )
                    )
                  ) : null}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </>
  );
};

export default DesplegableTabla;
