// const AdministrarVentas = () => {
//   return <h1>aDMIN vENTAS</h1>;
// };

import * as React from "react";
import PropTypes from "prop-types";
import Box from "@mui/material/Box";
import Collapse from "@mui/material/Collapse";
import IconButton from "@mui/material/IconButton";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Typography from "@mui/material/Typography";
import Paper from "@mui/material/Paper";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import imgCard from "../../components/img/imgCard.jpg";
import {
  Alert,
  Autocomplete,
  Button,
  Card,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  ListSubheader,
  MenuItem,
  Select,
  Skeleton,
  Tooltip,
} from "@mui/material";
import ReceiptIcon from "@mui/icons-material/Receipt";
import RemoveIcon from "@mui/icons-material/Remove";
import PaidIcon from "@mui/icons-material/Paid";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import DiscountIcon from "@mui/icons-material/Discount";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import { tooltipClasses } from "@mui/material/Tooltip";
import { styled } from "@mui/material/styles";
import {
  cleanMessage,
  cleanMessageDelete,
  cleanMessageUpdate,
  deleteDirectSale,
  getDirectSale,
  imprimirGuiaDeUnaVenta,
  modificarDirectSale,
  updateStatusDirectSale,
} from "../../redux/ventaDirecta";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import notImage from "../../components/img/notImage.png";
import { useCookies } from "react-cookie";
import PlusOneIcon from "@mui/icons-material/PlusOne";
import DeleteIcon from "@mui/icons-material/Delete";
import { Edit } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import LocalPrintshopIcon from "@mui/icons-material/LocalPrintshop";
import CropOriginalIcon from "@mui/icons-material/CropOriginal";

// export default AdministrarVentas;

function Row(props) {
  const { row } = props;
  console.log(row, "ROW");
  const directSaleInternalStatuses = useSelector(
    (store) => store.ordenesConsolidadas.directSaleInternalStatus
  );
  const { handleOpenActions, handleOpenActionsPrint } = props;
  const { setIdDeleteSale } = props;
  const [open, setOpen] = React.useState(false);
  const [amountTypes, setAmountTypes] = React.useState([]);
  const [totalProdutcs, setTotalProducts] = React.useState(0);
  const message = useSelector((store) => store.ventaDirecta.message);
  const [total, setTotal] = React.useState(0);
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const navigate = useNavigate();

  const OutlinedCard = (props) => {
    const fecha = props.fecha;
    const fulfillmentChannel = props.fulfillmentChannel;
    return (
      <Box sx={{ minWidth: 170 }}>
        {fecha}
        <br />
        {fulfillmentChannel}
        <Card variant="outlined" sx={{ padding: "1em" }}>
          {props.cliente}
        </Card>
      </Box>
    );
  };

  const EconomicDataProduct = ({ product }) => {
    const priceProv = product.costAtTimeOfSale;
    var s = priceProv.toString();
    var l = s.length;
    var decimalLength = s.indexOf(".") + 1;
    var numStr = s.substr(0, decimalLength + 2);
    product.costAtTimeOfSale = Number(numStr);
    // const product = { ...product };

    // me creo un objeto donde tiene todos mis taxes
    // y sus valores
    const amountsTypes = product.productIndirectSale_amountChangers.map(
      (amountChanger, index) => {
        return {
          amountChangerType: amountChanger.amountChanger.amountChangerType,
          amount: amountChanger.amount,
          name: amountChanger.amountChanger.name,
        };
      }
    );

    // en esta funcion hacemos uso del objeto de amounts
    // que declaramos arriba, y nos devuelve la utilidad de cada
    // producto
    const getUtilityProduct = () => {
      let utility = 0;
      const amountUtility = amountsTypes.find(
        (amountType) => amountType.name === "utilidad"
      );

      const typeUtility = "";

      if (amountUtility) {
        utility =
          amountUtility.amountChangerType == "percent"
            ? (amountUtility.amount *
                (product.costAtTimeOfSale * parseInt(product.units))) /
              100
            : amountUtility.amount;
      }

      // var s = utility.toString();
      // var l = s.length;
      // var decimalLength = s.indexOf(".") + 1;
      // var numStr = s.substr(0, decimalLength + 2);
      // console.log(Number(numStr), "NUMSTRkhj", parseInt(product.units));

      const s = utility.toString();
      const decimalIndex = s.indexOf(".");
      let numStr;

      if (decimalIndex === -1) {
        // No hay punto decimal, es un número entero
        numStr = s + ".00"; // Agrega ".00" para convertirlo en un formato decimal
      } else {
        // Hay un punto decimal, extrae hasta dos decimales
        numStr = s.substr(0, decimalIndex + 3);
      }

      return Number(numStr);
      // console.log(amountUtility, "amountUtility", product, utility);
    };

    const getDiscountProduct = () => {
      let discount = 0;
      const amountUtility = amountsTypes.find(
        (amountType) => amountType.name === "descuento"
      );

      if (amountUtility) {
        discount =
          amountUtility.amountChangerType == "percent"
            ? (amountUtility.amount / 100) *
              (product.costAtTimeOfSale * parseInt(product.units) +
                getUtilityProduct())
            : amountUtility.amount;
      }

      // console.log(
      //   amountUtility.amount / 100,
      //   "UTILIDADA",
      //   product.costAtTimeOfSale,
      //   product,
      //   "BKJJHKN",
      //   getUtilityProduct(),
      //   discount
      // );
      // return parseFloat(discount.toFixed(2));
      return Math.floor(discount * 100) / 100;
      // console.log(amountUtility, "amountUtility", product, utility);
    };

    const getTotalProduct = () => {
      let total = 0;
      let priceIva = 0;
      let valueIva;
      let total2 = 0;
      let iva = amountsTypes.find((amountType) => amountType.name === "IVA");
      if (iva) {
        valueIva =
          iva.amountChangerType == "percent" ? iva.amount / 100 : iva.amount;
        // let priceIva = product.costAtTimeOfSale * valueIva;
        let proceBeforeIva =
          product.costAtTimeOfSale * parseInt(product.units) +
          getUtilityProduct() -
          getDiscountProduct();
        priceIva = proceBeforeIva * valueIva;

        var s = priceIva.toString();
        var l = s.length;
        var decimalLength = s.indexOf(".") + 1;
        var ivaStr = s.substr(0, decimalLength + 2);

        total = priceIva * 1 + proceBeforeIva;

        total2 =
          product.costAtTimeOfSale * parseInt(product.units) +
          getUtilityProduct() -
          getDiscountProduct();
      }

      var s = total.toString();
      var l = s.length;
      var decimalLength = s.indexOf(".") + 1;
      var numStr = s.substr(0, decimalLength + 2);

      // setTotalProducts(
      //   (prevState) => prevState + total * parseInt(product.units)
      // );

      // return parseFloat(total.toFixed(2));

      return Number(numStr);
    };

    return (
      <>
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            margin: "auto",
            maxWidth: "150px",
          }}
        >
          {/* <Card variant="outlined" sx={{ padding: "1em" }}> */}
          <Tooltip
            title={`Proveedor: ${product.costAtTimeOfSale.toLocaleString(
              "es-MX",
              {
                style: "currency",
                currency: "MXN",
              }
            )}`}
            placement="top"
          >
            <Chip
              variant="outlined"
              size="small"
              icon={<PaidIcon color="primary" />}
              color="primary"
              label={` ${product.costAtTimeOfSale.toLocaleString("es-MX", {
                style: "currency",
                currency: "MXN",
              })}`}
            />
          </Tooltip>
          <br />

          <Tooltip
            title={`Utilidad: ${getUtilityProduct().toLocaleString("es-MX", {
              style: "currency",
              currency: "MXN",
            })}`}
            placement="top"
          >
            <Chip
              variant="outlined"
              size="small"
              icon={<TrendingUpIcon color="primary" />}
              color="primary"
              label={` ${getUtilityProduct()}`}
            />
          </Tooltip>
          <br />
          <Tooltip
            title={`Cliente:  ${(
              product.costAtTimeOfSale * parseInt(product.units) +
              getUtilityProduct()
            ).toLocaleString("es-MX", {
              style: "currency",
              currency: "MXN",
            })}`}
            placement="top"
          >
            <Chip
              variant="outlined"
              size="small"
              icon={<ShoppingCartIcon color="primary" />}
              color="primary"
              label={`${(
                product.costAtTimeOfSale * parseInt(product.units) +
                getUtilityProduct()
              ).toLocaleString("es-MX", {
                style: "currency",
                currency: "MXN",
              })}`}
            />
          </Tooltip>
          <br />
          <Tooltip title={`Descuento: ${getDiscountProduct()}`} placement="top">
            <Chip
              variant="outlined"
              size="small"
              icon={<DiscountIcon color="primary" />}
              color="primary"
              label={`${getDiscountProduct()}`}
            />
          </Tooltip>
          <br />
          <Tooltip
            title={`Total:  ${getTotalProduct().toLocaleString("es-MX", {
              style: "currency",
              currency: "MXN",
            })}`}
            placement="top"
          >
            <Chip
              variant="outlined"
              size="small"
              icon={<ReceiptIcon color="primary" />}
              color="primary"
              label={`${getTotalProduct().toLocaleString("es-MX", {
                style: "currency",
                currency: "MXN",
              })}`}
            />
          </Tooltip>
          {/* </Card> */}
        </Box>
      </>
    );
  };

  const EconomicData = () => {
    const getUtilityProduct = (product) => {
      const amountsTypes = product?.productIndirectSale_amountChangers?.map(
        (amountChanger, index) => {
          return {
            amountChangerType: amountChanger.amountChanger.amountChangerType,
            amount: amountChanger.amount,
            name: amountChanger.amountChanger.name,
          };
        }
      );
      let utility = 0;
      const amountUtility = amountsTypes?.find(
        (amountType) => amountType.name === "utilidad"
      );

      var s = product.costAtTimeOfSale.toString();
      var l = s.length;
      var decimalLength = s.indexOf(".") + 1;
      var numStr = s.substr(0, decimalLength + 2);

      if (amountUtility) {
        utility =
          amountUtility.amountChangerType == "percent"
            ? (amountUtility.amount / 100) *
              (Number(numStr) * parseInt(product.units))
            : amountUtility.amount;
      }
      // return parseFloat(utility.toFixed(2))
      return Math.floor(utility * 100) / 100;
      // console.log(amountUtility, "amountUtility", product, utility);
    };

    const getDiscountProduct = (product) => {
      const amountsTypes = product.productIndirectSale_amountChangers.map(
        (amountChanger, index) => {
          return {
            amountChangerType: amountChanger.amountChanger.amountChangerType,
            amount: amountChanger.amount,
            name: amountChanger.amountChanger.name,
          };
        }
      );
      let discount = 0;
      const amountUtility = amountsTypes.find(
        (amountType) => amountType.name === "descuento"
      );

      if (amountUtility) {
        discount =
          amountUtility.amountChangerType == "percent"
            ? (amountUtility.amount *
                (product.costAtTimeOfSale * parseInt(product.units) +
                  getUtilityProduct(product))) /
              100
            : amountUtility.amount;
      }

      // return parseFloat(discount.toFixed(2));
      return Math.floor(discount * 100) / 100;
      // console.log(amountUtility, "amountUtility", product, utility);
    };

    const getIvaProduct = (product) => {
      const amountsTypes = product.productIndirectSale_amountChangers.map(
        (amountChanger, index) => {
          return {
            amountChangerType: amountChanger.amountChanger.amountChangerType,
            amount: amountChanger.amount,
            name: amountChanger.amountChanger.name,
          };
        }
      );
      let priceIva = 0;

      var s = product.costAtTimeOfSale.toString();
      var l = s.length;
      var decimalLength = s.indexOf(".") + 1;
      var numStr = s.substr(0, decimalLength + 2);

      let iva = amountsTypes.find((amountType) => amountType.name === "IVA");
      if (iva) {
        let valueIva =
          iva.amountChangerType == "percent" ? iva.amount / 100 : iva.amount;
        priceIva =
          (Number(numStr) * parseInt(product.units) +
            getUtilityProduct(product) -
            getDiscountProduct(product)) *
          valueIva;
      }

      return Math.floor(priceIva * 100) / 100;
    };

    const totalDirectSale = () => {
      const total = row.products.reduce((acc, product) => {
        var s = product.costAtTimeOfSale.toString();
        var l = s.length;
        var decimalLength = s.indexOf(".") + 1;
        var numStr = s.substr(0, decimalLength + 2);
        const proceProvShort = Number(numStr);

        return (
          acc +
          proceProvShort * parseInt(product.units) +
          getUtilityProduct(product) -
          getDiscountProduct(product) +
          getIvaProduct(product)
        );
      }, 0); // Nota: Añadí un valor inicial de 0 para el acumulador en el método reduce.

      const totalShort = Math.floor(total * 100) / 100;
      var s = total.toString();
      var l = s.length;
      var decimalLength = s.indexOf(".") + 1;
      var numStr = s.substr(0, decimalLength + 2);
      setTotal(Number(numStr));

      return Number(numStr);
      // return total.toLocaleString("es-MX", {
      //   style: "currency",
      //   currency: "MXN",
      // });
    };

    const amountsTypesGeneral = row.directSale_amountChangers.map(
      (amountChanger, index) => {
        return {
          amountChangerType: amountChanger.amountChanger.amountChangerType,
          amount: amountChanger.amount,
          name: amountChanger.amountChanger.name,
        };
      }
    );

    // COMENTADO HASTA NUEVO AVISOOO ---------->
    // const getDiscountGeneral = () => {
    //   let discount = 0;
    //   const amountUtility = amountsTypesGeneral.find(
    //     (amountType) => amountType.name === "descuento"
    //   );
    //   if (amountUtility) {
    //     discount =
    //       amountUtility.amountChangerType == "percent"
    //         ? (amountUtility.amount * totalDirectSale()) / 100
    //         : amountUtility.amount;
    //   }
    //   return parseFloat(discount.toFixed(2));
    // };

    return (
      <>
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            margin: "auto",
            maxWidth: "120px",
          }}
        >
          {/* <Card variant="outlined" sx={{ padding: "1em" }}> */}
          dvs
          <Tooltip
            title={`SubTotal: ${totalDirectSale().toLocaleString("es-MX", {
              style: "currency",
              currency: "MXN",
            })}`}
            placement="top"
          >
            <Chip
              variant="outlined"
              size="small"
              icon={<RequestQuoteIcon color="primary" />}
              color="primary"
              label={`${totalDirectSale().toLocaleString("es-MX", {
                style: "currency",
                currency: "MXN",
              })}`}
            />
          </Tooltip>
          {/* // COMENTADO HASTA NUEVO AVISOOO ----------> */}
          {/* <br />
          <Tooltip title={`Descuento: ${getDiscountGeneral()}`} placement="top">
            <Chip
              variant="outlined"
              size="small"
              icon={<RemoveIcon color="primary" />}
              color="primary"
              label={`${getDiscountGeneral()}`}
            />
          </Tooltip> */}
          <br />
          <Tooltip title={`Envio: ${row.shipping}`} placement="top">
            <Chip
              variant="outlined"
              size="small"
              icon={<LocalShippingIcon color="primary" />}
              color="primary"
              label={`${row.shipping}`}
            />
          </Tooltip>
          <br />
          <Tooltip
            title={`Total ${(
              totalDirectSale() + parseFloat(row.shipping)
            ).toLocaleString("es-MX", {
              style: "currency",
              currency: "MXN",
            })}`}
            placement="top"
          >
            <Chip
              variant="outlined"
              size="small"
              icon={<ReceiptIcon color="primary" />}
              color="primary"
              label={` ${(
                totalDirectSale() + parseFloat(row.shipping)
              ).toLocaleString("es-MX", {
                style: "currency",
                currency: "MXN",
              })}`}
            />
          </Tooltip>
          {/* </Card> */}
        </Box>
      </>
    );
  };

  // la info de cada producto que vienen en la venta

  const DescriptionDataProduct = ({ product }) => {
    return (
      <>
        <Box sx={{ minWidth: 170 }}>
          <h5 style={{ color: "#003876" }}>
            {product.internalSku} - {product.productBrand} -{" "}
            {product.productModel}
          </h5>{" "}
          - {product.produtDescription}
          <br />
          cantidad: {product.units}
          <br />
        </Box>
      </>
    );
  };
  const DescriptionData = ({ product }) => {
    const LightTooltip = styled(({ className, ...props }) => (
      <Tooltip {...props} classes={{ popper: className }} />
    ))(({ theme }) => ({
      [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.common.white,
        color: "rgba(0, 0, 0, 0.87)",
        boxShadow: theme.shadows[1],
        fontSize: 11,
      },
    }));

    return (
      <>
        <Box sx={{ minWidth: 170 }}>
          <Box>
            <p style={{ color: "#003876", margin: "0" }}>
              {product.internalSku}
            </p>{" "}
            {product.produtDescription}
            <br />
            {product.productBrand} - {product.productModel} <br />
            cantidad: {product.units}
          </Box>
        </Box>
      </>
    );
  };

  const [statusQuote, setStatusQuote] = React.useState(
    row?.directSaleInternalStatus?.directSaleInternalStatusId ?? 0
  );

  const handleChange = async (event) => {
    let status = {
      directSaleId: row.directSaleId,
      newInternalStatusId: event.target.value,
    };

    if (row.directSaleInternalStatus.directSaleInternalStatusId === 1) {
      dispatch(cleanMessage());
      props.setErrorMessage(
        "No es posible cambiar el status interno de una venta directa en cotización"
      );
      return;
    }
    await dispatch(updateStatusDirectSale(status, cookies.csrf_access_token));
    setStatusQuote(event.target.value);
  };

  const infoProducts = row.products.map((product) => ({
    priceProv: product.costAtTimeOfSale,
    quantity: product.units,
    sku: product.internalSku,
    description: product.produtDescription,
    brand: product.productBrand,
    model: product.productModel,
  }));

  const descriptions = infoProducts.map((product) => ({
    truncatedDescription:
      product?.description?.length > 10
        ? product?.description?.slice(0, 10) + "..."
        : product?.description,
    fullDescription: product,
  }));

  //en este array se ponen las descripciones con el separador y tooltip
  const descriptionsWithSeparator = descriptions.map((description, index) =>
    index === descriptions.length - 1 ? (
      <Tooltip
        title={
          <Box>
            <h6 style={{ color: "#003876" }}>
              {description["fullDescription"].sku} -
              {description["fullDescription"].brand} -
              {description["fullDescription"].model}
            </h6>
            {description["fullDescription"].productName}
            <br />
            {description["fullDescription"].description}
          </Box>
        }
        placement="top"
        key={index}
      >
        <span style={{ color: "#565656" }}>
          {description["truncatedDescription"]}
        </span>
      </Tooltip>
    ) : (
      <Tooltip
        title={
          <Box>
            <h6 style={{ color: "#003876" }}>
              {" "}
              {description["fullDescription"].sku} -{" "}
              {description["fullDescription"].brand} -{" "}
              {description["fullDescription"].model}
            </h6>
            {description["fullDescription"].productName}
            <br />
            {description["fullDescription"].description}
          </Box>
        }
        placement="top"
        key={index}
      >
        <span>
          <span style={{ color: "#565656" }}>
            {description["truncatedDescription"]}
          </span>
          <span style={{ color: "blue" }}> | </span>
        </span>
      </Tooltip>
    )
  );

  return (
    <React.Fragment>
      {/* <TableRow sx={{ "& > *": { borderBottom: "unset" } }}> */}

      <TableRow sx={{ borderRadius: "10px", marginTop: "20px" }}>
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell
          component="th"
          scope="row"
          sx={{ padding: "0 16px" }}
          align="center"
        >
          {row.products[0].productImage ? (
            <img
              src={row.products[0].productImage}
              width="90px"
              height="90px"
              style={{ borderRadius: "20px", margin: "5px 0" }}
            />
          ) : row.products.length > 1 ? (
            <PlusOneIcon />
          ) : (
            <img
              src={notImage}
              width="90px"
              height="90px"
              style={{ borderRadius: "20px", margin: "5px 0" }}
            />
          )}
        </TableCell>
        <TableCell align="left">
          <OutlinedCard
            fecha={row.saleDate ?? "Sin fecha"}
            // fulfillmentChannel="venta directa"
            fulfillmentChannel={
              row?.directSaleType.directSaleType ?? "Sin tipo"
            }
            cliente={row.client?.name ? row.client.name : "Sin nombre"}
          />
        </TableCell>
        <TableCell align="left" sx={{ maxWidth: "120px" }}>
          <EconomicData />
        </TableCell>
        <TableCell align="left">
          <Box
            sx={{
              display: "flex",
              flexWrap: "wrap",
              margin: "auto",
              justifyContent: "space-evenly",
              maxWidth: "80%",
              gap: ".2em 0",
            }}
          >
            {row.products.length <= 2 ? (
              row.products.map((product) => (
                <Box sx={{ width: "100%", padding: ".5em 0" }}>
                  <DescriptionData
                      brand={product.productAtStore.product.productBase.brand} 
                      model={product.productAtStore.product.productBase.model}
                      title={product.productAtStore.product.productDescription}
                      units={product.units}
                    />
                </Box>
              ))
            ) : (
              <p>{descriptionsWithSeparator}</p>
            )}
          </Box>
        </TableCell>
        <TableCell align="right" sx={{ padding: "0" }}>
          {" "}
          <FormControl size="small" sx={{ width: "100%", maxWidth: "200px" }}>
            <InputLabel id="demo-select-small-label">Status</InputLabel>
            <Select
              sx={{ width: "100%", maxWidth: "250px" }}
              labelId="demo-select-small-label"
              id="demo-select-small"
              value={statusQuote}
              label="Status"
              disabled={
                row.directSaleInternalStatus.directSaleInternalStatusId !== 1
                  ? false
                  : true
              }
              onChange={async (e) => {
                await dispatch(cleanMessage());
                handleChange(e);
              }}
            >
              <ListSubheader>
                <i style={{ color: "red" }}>status</i>
              </ListSubheader>
              {directSaleInternalStatuses.map((option) => (
                <MenuItem
                  key={option.directSaleInternalStatusId}
                  value={option.directSaleInternalStatusId}
                >
                  {option.directSaleInternalStatus}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </TableCell>

        {/* <TableCell align="right" sx={{ padding: "0" }}>
        
        </TableCell> */}
        <TableCell align="right" sx={{ padding: "0" }}>
          <IconButton
            onClick={() => {
              // setIdDeleteSale(row.directSaleId);
              console.log('00000000000000000000Cuak')
              console.log(row)
              console.log('00000000000000000000Cuak')
              handleOpenActionsPrint(row.directSaleId);
            }}
          >
            <CropOriginalIcon sx={{ color: "black" }} />
          </IconButton>
        </TableCell>
        <TableCell align="right" sx={{ padding: "0" }}>
          <IconButton
            onClick={() => {
              setIdDeleteSale(row.directSaleId);
              handleOpenActions(row);
            }}
          >
            <DeleteIcon sx={{ color: "red" }} />
          </IconButton>
        </TableCell>
        <TableCell align="right" sx={{ padding: "0" }}>
          <IconButton
            aria-label="edit"
            disabled={
              row.directSaleInternalStatus.directSaleInternalStatusId !== 1
                ? true
                : false
            }
            // onClick={() => navigate(`/ventas/editar/${row.directSaleId}`)}
            onClick={() =>
              (window.location = `/ventas/editar/${row.directSaleId}`)
            }
          >
            <Edit
              sx={{
                color:
                  row.directSaleInternalStatus.directSaleInternalStatusId !== 1
                    ? "grey"
                    : "blue",
                margin: "0",
                padding: "0",
              }}
            />
          </IconButton>
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                {/* Histor */}
              </Typography>
              <Table size="small" aria-label="purchases">
                <TableHead>
                  <TableRow>
                    <TableCell>Imagen</TableCell>
                    <TableCell>Datos Producto</TableCell>
                    <TableCell align="center">cantidad</TableCell>
                    <TableCell align="center">Montos</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {row.products.map((product) => (
                    <TableRow key={product.sku}>
                      <TableCell component="th" scope="row">
                        {/* {product.productImage ? ( */}
                        <img
                          src={
                            product.productImage
                              ? product.productImage
                              : notImage
                          }
                          width="90px"
                          height="90px"
                          style={{ borderRadius: "20px", margin: "5px 0" }}
                        />
                      </TableCell>
                      <TableCell>
                        <DescriptionDataProduct product={product} />
                      </TableCell>
                      <TableCell align="center">{product.units} </TableCell>
                      <TableCell align="center" sx={{ maxWidth: "100%" }}>
                        {/* economic data principal */}
                        <EconomicDataProduct product={product} />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>

      {/* </TableRow> */}
    </React.Fragment>
  );
}


export default function AdministrarVentas() {
  const dispatch = useDispatch();
  const directSales = useSelector((store) => store.ventaDirecta.directSales);
  const isLoading = useSelector((store) => store.ventaDirecta.isLoadinSale);
  const message = useSelector((store) => store.ventaDirecta.message);
  const [errorMessage, setErrorMessage] = React.useState(null);

  useEffect(() => {
    dispatch(getDirectSale());
    dispatch(cleanMessageUpdate());
  }, []);


  const [openDeleteSale, setOpenDeleteSale] = React.useState(false);
  const [openPrint, setOpenPrint] = React.useState(false);
  const [idDeleteSale, setIdDeleteSale] = React.useState(null);
  const guiaContenido = useSelector(
    (store) => store.ventaDirecta.guiaContenido
  );
  const guiaEspera = useSelector((store) => store.ventaDirecta.guiaEspera);

  const navigate = useNavigate();

  // const url = guiaContenido ? URL.createObjectURL(guiaContenido) : null;

  const esURLPDF = (url) => {
    // Verifica si la URL es not found
    return url.endsWith("NotFoundPage");
  };

  const handleOpenActions = () => {
    setOpenDeleteSale(true);
  };

  const handleCloseActions = () => {
    setOpenDeleteSale(false);
  };

  const handleOpenActionsPrint = (data) => {
    dispatch(imprimirGuiaDeUnaVenta(data));
    setOpenPrint(true);
  };

  const handleCloseActionsPrint = () => {
    setOpenPrint(false);
  };

  const messageDelete = useSelector((store) => {
    return store.ventaDirecta.messageDelete;
  });

  const handleDeleteSale = async () => {
    const result = await dispatch(deleteDirectSale(idDeleteSale));
    handleCloseActions();
  };

  useEffect(() => {
    if (messageDelete == "cotización eliminada exitosamente") {
      const copyDirectSales = [...directSalesState];
      const index = copyDirectSales.findIndex(
        (sale) => sale.directSaleId === idDeleteSale
      );
      copyDirectSales.splice(index, 1);
      setDirectSalesState(copyDirectSales);
      dispatch(modificarDirectSale(copyDirectSales));
      dispatch(cleanMessageDelete());
      setIdDeleteSale(null);
    }
  }, [messageDelete]);

  const [directSalesState, setDirectSalesState] = React.useState([]);
  useEffect(() => {
    setDirectSalesState(directSales);
  }, [directSales]);

  return directSalesState?.length > 0 ? (
    <>
      <TableContainer component={Paper}>
        <Table
          aria-label="collapsible table"
          sx={{ width: "85%", margin: "auto" }}
        >
          <TableHead>
            <TableRow>
              <TableCell />
              <TableCell align="center">Imagen</TableCell>
              <TableCell align="center">Comprador</TableCell>
              <TableCell align="center">Montos</TableCell>
              <TableCell align="center">Descripción</TableCell>
              <TableCell align="center">Estado</TableCell>
              {/* <TableCell align="center">PDF</TableCell> */}
              <TableCell align="center">Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {directSalesState?.map((sale) => (
              <Row
                key={sale.directSaleId}
                row={sale}
                setErrorMessage={setErrorMessage}
                handleOpenActions={handleOpenActions}
                handleOpenActionsPrint={handleOpenActionsPrint}
                setIdDeleteSale={setIdDeleteSale}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {message && (
        <Alert
          sx={{
            position: "sticky",
            bottom: "3rem",
            left: "2rem",
            maxWidth: "40%",
          }}
          onClose={() => dispatch(cleanMessage())}
          severity="warning"
        >
          {message}
        </Alert>
      )}
      {errorMessage && (
        <Alert
          sx={{
            position: "sticky",
            bottom: "3rem",
            left: "2rem",
            maxWidth: "40%",
          }}
          onClose={() => {
            setErrorMessage(null);
            dispatch(cleanMessage());
          }}
          severity="warning"
        >
          {errorMessage}
        </Alert>
      )}
      <div>
        <Dialog
          open={openDeleteSale}
          onClose={handleCloseActions}
          disablebackdropclick={true}
        >
          <DialogTitle>{"Eliminar venta"}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Estas seguro que quieres eliminar esta venta?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              // onClick={async () => {
              //   await dispatch(deleteDirectSale(idDeleteSale));
              //   handleCloseActions();
              // }}
              onClick={handleCloseActions}
              color="error"
            >
              Cerrar
            </Button>
            <Button
              // onClick={async () => {
              //   await dispatch(deleteDirectSale(idDeleteSale));
              //   handleCloseActions();
              // }}
              onClick={handleDeleteSale}
              color="success"
            >
              Aceptar
            </Button>
          </DialogActions>
        </Dialog>
      </div>
      <div sx={{ width: "100%", height: "100%" }}>
        <Dialog
          open={openPrint}
          onClose={handleCloseActions}
          disablebackdropclick={true}
          maxWidth="xl" // Cambia aquí para un ancho máximo más grande
          fullWidth={true} // Asegúrate de que ocupe todo el ancho disponible
          sx={{ width: "100%", height: "100vh" }}
        >
          <DialogTitle>{"Pdf Producto"}</DialogTitle>
          <DialogContent
            sx={{ width: "100%", height: "90vh", maxHeigth: "800px" }}
          >
            {/* <DialogContentText> */}
            <Box
              sx={{
                width: "100%",
                height: "100%",
                display: "flex",
              }}
            >
              {guiaEspera ? (
                "Cargando..."
              ) : guiaContenido ? (
                esURLPDF(guiaContenido) ? (
                  <Typography>No se obtuvo una url</Typography>
                ) : (
                  <iframe
                    src={guiaContenido}
                    width="100%"
                    height="95%"
                    title="PDF Viewer"
                  />
                )
              ) : (
                <Typography>No se pudo cargar la guía</Typography>
              )}
            </Box>
            {/* </DialogContentText> */}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseActionsPrint} color="error">
              Cerrar
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    </>
  ) : isLoading ? (
    <>
      <Skeleton
        variant="rectangular"
        width="80%"
        height={120}
        sx={{ borderRadius: "10px", margin: "1em auto" }}
        animation="wave"
      />
      <Skeleton
        variant="rectangular"
        width="80%"
        height={120}
        sx={{ borderRadius: "10px", margin: "1em auto" }}
        animation="wave"
      />
      <Skeleton
        variant="rectangular"
        width="80%"
        height={120}
        sx={{ borderRadius: "10px", margin: "1em auto" }}
        animation="wave"
      />
      <Skeleton
        variant="rectangular"
        width="80%"
        height={120}
        sx={{ borderRadius: "10px", margin: "1em auto" }}
        animation="wave"
      />
    </>
  ) : (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: "70vh", // Asegura que el Box ocupe toda la altura de la ventana
        backgroundColor: "#f5f5f5", // Color de fondo suave
        padding: "2em", // Espaciado interno
      }}
    >
      <Typography
        sx={{
          color: "#333", // Color del texto
          fontSize: "1.5em", // Tamaño del texto
          fontWeight: "bold", // Negrita
          marginBottom: "1em", // Espacio debajo del texto
        }}
      >
        Vaya, parece que aún no tienes ventas
      </Typography>
      <Button
        variant="contained"
        color="primary"
        sx={{
          margin: "1em auto",
          width: "200px", // Ancho fijo para el botón
          padding: "10px 0", // Espaciado vertical en el botón
          fontSize: "1em", // Tamaño del texto en el botón
          boxShadow: "0px 3px 5px rgba(0,0,0,0.2)", // Sombra para el botón
        }}
        onClick={() => navigate("/ventas/directa")}
      >
        Nueva venta
      </Button>
    </Box>
  );
}
