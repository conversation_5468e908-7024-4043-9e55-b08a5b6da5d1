import React, { useEffect } from 'react'
import {
    Avatar,
    Box,
    CardHeader,
    CircularProgress,
    Divider,
    Grid,
    IconButton,
    ListItem,
    ListItemAvatar,
    TextField,
    Typography,
    Tooltip,
    Button,
    Popover,
    Stack,
    Alert,
    Snackbar,
} from "@mui/material";
import { orange, purple } from "@mui/material/colors";
import { formatDate, formatTime } from "../../Utils/generalFunctions";
import SyncIcon from "@mui/icons-material/Sync";
import DeleteIcon from "@mui/icons-material/Delete";
import { useDispatch, useSelector } from "react-redux";
import {
    setMsgVentas
} from "../../redux/pedidosDucks";
import EditIcon from "@mui/icons-material/Edit";
import CancelIcon from "@mui/icons-material/Cancel";
import ModalConfirmacionBorrar from "../componentesGenerales/ModalConfirmacionBorrar";

import Card from '@mui/material/Card';
import CardMedia from '@mui/material/CardMedia';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import '../mensajesComponentes/messagesList.css';
import CustomDialog from '../componentesGenerales/CustomDialog';
import { getRecordComments } from '../utils/utils';
import { useTheme } from '@emotion/react';
import { Chip } from "@mui/material";
import { AlertComponent } from '../componentesGenerales/Alert';


const Comment = ({ comment, isSmallScreen, deleteComment, updateComment, idOriginal, originalUser, commentRaw }) => {
    const [commentText, setCommentText] = React.useState(comment.comment)
    const [isErrorComment, setIsErrorComment] = React.useState(false);
    const [isEditing, setIsEditing] = React.useState(false);
    const dispatch = useDispatch();
    const timeStamp = originalUser.timeStamp
    const fechaformtaeada = formatDate(timeStamp).split(" ")[1]

    const userName = originalUser.user.name
    const [loading, setLoading] = React.useState(false)
    const user = useSelector((store) => store.usuario);
    const role = useSelector((store) => store.usuario.role);
    const msgVentas = useSelector((store) => store.pedidos.msgVentas);
    const textFieldRef = React.useRef(null);
    const [openDeleteDialog, setOpenDeleteDialog] = React.useState(false);
    const [showMaxLengthAlert, setShowMaxLengthAlert] = React.useState(false);
    const maxLength = useSelector((store) => store.pedidos.commentMaxLength);
    const characterLimit = maxLength;
    const remainingChars = characterLimit - commentText.length;


    // Efecto para mover el cursor al final del texto cuando se activa el modo de edición
    React.useEffect(() => {
        if (isEditing && textFieldRef.current) {
            const input = textFieldRef.current.querySelector('textarea');
            if (input) {
                setTimeout(() => {
                    input.selectionStart = input.selectionEnd = input.value.length;
                    input.focus();
                }, 100);
            }
        }
    }, [isEditing]);


    const handleDeleteComment = async () => {
        setLoading(true)
        const [message, severity] = await deleteComment(idOriginal)
        dispatch(setMsgVentas(message, severity))
        setLoading(false)
        setOpenDeleteDialog(false)
    }

    const handleOpenDeleteDialog = () => {
        
        setOpenDeleteDialog(true);
    }

    const handleEditClick = () => {
        setIsEditing(true);
    }

    const handleUpdateComment = async () => {
        // Verifica si el nuevo comentario está vacío

        if (commentText === "") {
            setIsErrorComment(true);
            return;
        }
        // Si el comentario está al límite o lo supera, muestra la alerta y no permite guardar
        if (commentText.length >= characterLimit) {
            if (!showMaxLengthAlert) {
                setShowMaxLengthAlert(true);
                setTimeout(() => setShowMaxLengthAlert(false), 10000);
            }
            return;
        }
        setLoading(true)
        const [message, severity] = await updateComment(idOriginal, commentText)
        dispatch(setMsgVentas(message, severity))
        setLoading(false)
        setIsEditing(false)
    }

    const handleCancelEdit = () => {
        setCommentText(comment.comment);
        setIsEditing(false);
        setIsErrorComment(false);
    }

    React.useEffect(() => { setCommentText(comment.comment) }, [comment])


    const cardStyles = () => ({
        maxWidth: "345px",
        backgroundColor: "transparent !important",
        boxShadow: "none",
        width: "100%",
        margin: "auto",
        minWidth: "100%",
        '& .MuiPaper-root': {
            backgroundColor: "transparent !important",
            backgroundImg: "transparent !important",
        },
        "& .MuiCardHeader-content .MuiCardHeader-title": {
            fontSize: "16px",
            fontWeight: "400",
        },
        '& .MuiCardHeader-content .MuiCardHeader-subheader': {
            fontSize: "12px",
            color: '#717D74',
            fontWeight: "400",
        },
        "& .MuiCardHeader-action": {
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            '& .MuiCardHeader-avatar .MuiAvatar-root': {
                padding: "0",
                borderRadius: "100%",
            },
        },
    });

    function stringToColor(string) {
        let hash = 0;
        let i;

        /* eslint-disable no-bitwise */
        for (i = 0; i < string.length; i += 1) {
            hash = string.charCodeAt(i) + ((hash << 5) - hash);
        }

        let color = '#';

        for (i = 0; i < 3; i += 1) {
            const value = (hash >> (i * 8)) & 0xff;
            color += `00${value.toString(16)}`.slice(-2);
        }
        /* eslint-enable no-bitwise */

        return color;
    }





    const [commentsRecords, setCommentsRecords] = React.useState([])
    const [openCommentsRecords, setOpenCommentsRecords] = React.useState(false)
    const [anchorEl, setAnchorEl] = React.useState(null);

    const handleUpdateClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    /* 1.  Estado para la posición */
    const [anchorPos, setAnchorPos] = React.useState(null);

    /* 2.  Al hacer clic: consulta los comentarios y guarda la posición */
    const handleGetRecordComments = async (event) => {
        const commentsObj = await getRecordComments(idOriginal);
        const records = commentsObj.orderStackableCommentRecords ?? [];

        if (records.length) {
            setCommentsRecords(records);

            // Posición absoluta en la página (ten en cuenta el scroll vertical)
            setAnchorPos({
                top: event.clientY + window.scrollY,
                left: event.clientX,
            });
            setOpenCommentsRecords(true);
        }
    };


    const theme = useTheme();

    /** Devuelve el primer nombre o la cadena completa si no hay espacio */
    const first = (fullName = "") => fullName.split(" ")[0];

    /**
     *  Dentro de tu componente…
     */
    const showAuthor = userName !== user.name;           // ¿Escribió otro usuario?
    const isEdited = originalUser.id !== comment.id;   // ¿Hubo edición?
    const authorName = first(userName);
    const editorName = first(comment.user.name);


    return (
        <>
            <Box
                key={comment.id}
                style={{
                    position: "relative",
                }}
                className={`message-item ${userName === user.name ? "message-right" : "message-left"
                    }`}
            >

                {/* Nombre del autor (si es otro) */}

                <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ display: "flex", alignItems: "center", gap: .5, mt: 1, ml: 1.5, cursor: "default" }}
                >
                    {/* Autor original */}
                    {showAuthor && authorName}

                    {/* Separador si hay autor y está editado */}
                    {showAuthor && isEdited && <span style={{ fontSize: "1.5rem" }}>·</span>}

                    {/* Indicador de edición */}
                    {isEdited && (
                        <>
                            <span
                                onClick={(event) => role == "Admin" ? handleGetRecordComments(event) : null}
                                style={{
                                    cursor: role == "Admin" ? "pointer" : "default",
                                    textDecoration: role == "Admin" ? "underline" : "none"
                                }}
                            >
                                Editado
                            </span>
                 
                            <span style={{ textDecoration: "none !important" }}> {role == "Admin" && `(${editorName})`}</span>

                        </>
                    )}
                    {commentRaw.orderStackableCommentDeleted && <span style={{ color: "secondary" }}> <span style={{ fontSize: ".7rem" }}>·</span> Eliminado ( {commentRaw.orderStackableCommentDeleted.user.name.split(" ")[0]})</span>}
                </Typography>



                {/* Contenido del mensaje */}
                <div className="message-content">



                    <Box sx={{ display: "flex", flexDirection: "column-reverse", width: "100%", justifyContent: "space-between", alignItems: "flex-start" }}>

                        <span dangerouslySetInnerHTML={{ __html: comment.comment }} />

                        {/* ▶ BOTONERA – se ocultará hasta el hover gracias a CSS */}
                        <Box className="comment-actions" style={{
                            /*  position: "absolute", */
                            top: "1.2em",
                            right: 0,
                            zIndex: 10,
                            display: "flex",
                            flexDirection: "row",
                            gap: "8px",
                            alignItems: "center",
                            justifyContent: "flex-end",
                            width: "100%",
                            paddingRight: "10px",
                        }}>
                            <Tooltip
                                title={
                                    userName !== user.name && role !== "Admin" 
                                        ? "No eres el autor de este comentario"
                                        : commentRaw.orderStackableCommentDeleted ? "Comentario eliminado" : "Editar comentario"
                                }
                                arrow
                            >
                                <IconButton
                                    aria-label="edit"
                                    onClick={handleEditClick}
                                    disabled={userName !== user.name && role !== "Admin" || commentRaw.orderStackableCommentDeleted}
                                    color="primary"
                                >
                                    <EditIcon
                                        sx={{ fontSize: ".8rem" }}
                                    />
                                </IconButton>
                            </Tooltip>

                            <Tooltip
                                title={
                                    role !== "Admin" && userName !== user.name
                                        ? "No eres propietario"
                                        : commentRaw.orderStackableCommentDeleted ? "Comentario eliminado" : "Eliminar comentario"
                                }
                                arrow
                            >
                                 <span>
                                    <IconButton
                                        aria-label="delete"
                                        onClick={handleOpenDeleteDialog}
                                        disabled={userName !== user.name && role !== "Admin" || commentRaw.orderStackableCommentDeleted}
                                        color="secondary"
                                    >
                                        <DeleteIcon
                                            sx={{ fontSize: ".8rem" }}
                                        />
                                    </IconButton>
                                </span> 
                            </Tooltip>
                        </Box>

                    </Box>

                    <Typography
                        variant="caption"
                        sx={{
                            display: "block",
                            mt: 0.2,
                            color: (theme) => theme.palette.text.pinkMain,
                        }}
                    >
                        {fechaformtaeada} 
                       {/*  agrega la fecha y hora de eliminacion si es que hay */}
                       {commentRaw.orderStackableCommentDeleted && <span style={{ color: "secondary" }}> <span style={{ fontSize: ".7rem" }}>·</span>Eliminado ( {formatDate(commentRaw.orderStackableCommentDeleted.timeStamp).split(" ")[0]})</span>}
                    </Typography>
                </div>
            </Box>

            <CustomDialog
                open={isEditing}
                onClose={handleCancelEdit}
                title="Editar comentario"
                maxWidth="590px"
                width="100%"
                maxHeight="85%"
                dialogStyles={{ borderRadius: '24px' }}
                actions={
                    <Box mt={2} sx={{ display: "flex" }} gap={4} width={"40%"}>
                        <Button variant="text" color="error" onClick={handleCancelEdit}>
                            Cancelar
                        </Button>
                        <Button
                            endIcon={loading ? <CircularProgress size={24} /> : null}
                            disabled={loading || isErrorComment}
                            variant="outlined" loadingPosition="end" onClick={handleUpdateComment} color="buttonGreenPink" autoFocus>
                            Guardar
                        </Button>
                    </Box>
                }
            >
                {/* Content */}
                <Stack spacing={2} mx={1}>
                    <Typography variant="body2" color="text.secondary">
                        Escribe tu comentario aquí...
                    </Typography>

                    <TextField
                        fullWidth
                        multiline
                        rows={4}
                        autoFocus
                        variant="outlined"
                        placeholder="Escribe tu comentario aquí..."
                        value={commentText}
                        onChange={(e) => setCommentText(e.target.value)}
                        error={isErrorComment}

                        inputProps={{ maxLength: characterLimit }}
                        ref={textFieldRef}
                        onKeyDown={(event) => {
                            if (event.key === "Enter" && !event.shiftKey) {
                                event.preventDefault();
                                handleUpdateComment();
                            }
                        }}
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                borderRadius: 2,
                                '&:hover fieldset': {
                                    borderColor: 'primary.main',
                                },
                                '&.Mui-focused fieldset': {
                                    borderWidth: 2,
                                }
                            }
                        }}
                    />

                    {/* Character counter visual */}
                    <Box display="flex" justifyContent="flex-end">
                        <Typography 
                            variant="caption" 
                            color={
                                commentText.length >= characterLimit ? "error" :
                                commentText.length >= characterLimit * 0.8 ? "warning" :
                                "text.secondary"
                            }
                        >
                            {commentText.length} / {characterLimit}
                        </Typography>
                    </Box>
                </Stack>

            </CustomDialog>

            {/* Modal de confirmación para borrar comentario */}
            <ModalConfirmacionBorrar
                open={openDeleteDialog}
                setOpen={setOpenDeleteDialog}
                deleteFunction={handleDeleteComment}
                msg={`¿Estás seguro de que deseas eliminar este comentario?`}
            />


            <Popover
                open={openCommentsRecords}
                anchorReference="anchorPosition"     // ← clave
                anchorPosition={anchorPos ?? { top: 0, left: 0 }}
                onClose={() => setOpenCommentsRecords(false)}
                disableRestoreFocus
                transformOrigin={{ vertical: "top", horizontal: "left" }}  // alinea la esquina
                sx={{
                    "& .MuiPopover-paper": {
                        borderRadius: "20px",
                        boxShadow: 3,
                        maxWidth: 280,              /* limita el ancho si lo deseas */
                    },
                }}
            >
                <Box
                    sx={{
                        mb: 2,
                        /*  bgcolor: theme.palette.background.default, */
                        borderRadius: 4,
                        p: 1,
                    }}
                >
                    <Typography variant="h6" fontWeight="regular" color="colorGreenPink" sx={{ width: "100%", textAlign: "center", py: 2, fontSize: "1.2rem" }}>
                        Historial de ediciones
                    </Typography>
                    {commentsRecords
                        .sort((a, b) => new Date(b.timeStamp) - new Date(a.timeStamp)) // Ordenar del más reciente al más antiguo
                        .map((comment, index) => (
                            <Box key={comment.id || index} sx={{ mb: 2, bgcolor: theme.palette.background.default, borderRadius: "12px", padding: 1 }}>
                                <Typography variant="body2" fontWeight="bold" sx={{ mt: 0.5 }}>
                                    {comment.comment}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                    {`${comment.user.name} `}
                                </Typography>
                                <Typography variant="caption" fontWeight="bold" color="text.secondary">
                                    {` - ${formatDate(comment.timeStamp)}`}
                                </Typography>
                            </Box>
                        ))}
                </Box>
            </Popover>

            {/* Alerta de límite de caracteres */}
            {showMaxLengthAlert && (
           <AlertComponent  
            color="warning"
            message={`Has llegado al límite máximo de ${maxLength} caracteres`} 
            cleanMessage={() => setShowMaxLengthAlert(false)}
            time={3000}
            />
            )}
        </>

    )

}

export default Comment



