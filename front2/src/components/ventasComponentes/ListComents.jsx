// ListComents.jsx
import React, { useEffect, useMemo, useRef } from "react";
import {
  List,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import Comment from "./Comment";
import { formatHeaderDate } from "../../Utils/generalFunctions";
import "../mensajesComponentes/messagesList.css";

export const ListComents = ({ comments, deleteComment, updateComment }) => {
  const theme          = useTheme();
  const isSmallScreen  = useMediaQuery(theme.breakpoints.down("sm"));

  /* ------ AGRUPAR POR DÍA (igual que antes) ------------------------- */
  const grouped = useMemo(() => {
    const byDate = {};
    comments.forEach((m) => {
      const key = new Date(
        m.orderStackableCommentRelevantRecords.firstCommentRecord.timeStamp
      ).toISOString().substring(0, 10);
      (byDate[key] ??= []).push(m);
    });
    return byDate;
  }, [comments]);

  /* ------ AUTO-SCROLL AL FINAL -------------------------------------- */
  const bottomRef = useRef(null);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth", block: "end" });
  }, [comments.length]);               // se dispara cuando llega uno nuevo

  /* ------ RENDER ----------------------------------------------------- */
  return comments?.length ? (
    <List
      className="messages-list"
      sx={{
        pb: 2,
        bgcolor: "background.default",
        overflowY: "auto",
        height: "88%",
        width: "100%",           // <-- Asegura que no crece más que el contenedor
        overflowX: "hidden",     // <-- Previene scroll horizontal
        boxSizing: "border-box"  // <-- Incluye padding en el ancho
      }}
    >
      {Object.keys(grouped)
        .sort()                          // ascendente (días antiguos arriba)
        .map((dateKey) => (
          <React.Fragment key={dateKey}>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ display: "block", textAlign: "center", my: 1 }}
            >
              {formatHeaderDate(dateKey)}
            </Typography>

            {grouped[dateKey].map((msg) => (
              <Comment
                key={msg.id}
                comment={msg.orderStackableCommentRelevantRecords.lastCommentRecord}
                isSmallScreen={isSmallScreen}
                deleteComment={deleteComment}
                updateComment={updateComment}
                idOriginal={msg.id}
                originalUser={msg.orderStackableCommentRelevantRecords.firstCommentRecord}
                commentRaw={msg}
              />
            ))}
          </React.Fragment>
        ))}

      {/* ancla invisible para el scroll */}
      <div ref={bottomRef} />
    </List>
  ) : null;
};
