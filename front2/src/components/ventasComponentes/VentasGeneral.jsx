import React, { Fragment } from "react";
import { Routes, Route } from "react-router-dom";

import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import VisualMetricas from "../metricasComponentes/VisualMetricas";
// import VentaDirecta from "./ventasComponentes/VentaDirecta";
import AdministrarVentas from "./AdministrarVentas/AdministrarVentas";
// import VentaDirecta from "../ventaDirectaComponentes/VentaDirecta";
import EditOrder from "./EditOrder";
import VentaDirecta from "../ventaDirectaComponentes/VentaDirecta";
import AdministrarVentasDirectas from "./AdministrarVentasDirectas";
import  SurtidoVentaDirectaWrapper  from "./SurtidoVentaDirecta/SurtidoVentaDirectaWrapper";
import { TitleModule } from "../componentesGenerales/TitleModule";
import TablaPedidos from "../ordenesComponentes/TablaPedidos";

const Item = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  textAlign: "center",
  // color: "#000000",
}));

const VentasGeneral = () => {
  return (
    <div>
      <Fragment>
        <Grid container spacing={1}>
         <TitleModule title="Módulo de Ventas"/>
        </Grid>
        <Routes>
          <Route
                path={`pedidosTabla`}
                element={<TablaPedidos  />}
              />
          <Route path="/directa" element={<VentaDirecta />} />
          <Route path="/administrarDirecta" element={<AdministrarVentasDirectas />} />
          <Route path="/administrar" element={<AdministrarVentas />} />
          <Route path="/editar/:idVenta" element={<EditOrder />} />
          <Route path="/surtir/:idVenta" element={<SurtidoVentaDirectaWrapper />} />
        </Routes>
      </Fragment>
    </div>
  );
};

export default VentasGeneral;
