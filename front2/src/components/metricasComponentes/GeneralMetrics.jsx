import React from 'react'
import { Box, Grid, Skeleton } from "@mui/material"
import { CardMetric } from "./componentes/CardMetric"
import cardsCss from '../css/metrics/generalMetrics.module.css'
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import SalesOverview from "./componentes/Chart";
import { useEffect, useState } from "react";
import { getMetrics, getMetricsDinamyc, getMetricsOneWeek } from "../../redux/metricasDucks";
import { useDispatch, useSelector } from "react-redux";
import NumbersIcon from '@mui/icons-material/Numbers';
import { format, subDays, set } from 'date-fns';



const isEmpty = (obj) => {
  return obj.length === 0;
};

const getDateRange = (days) => {
  const startDate = new Date();
  const endDate = subDays(startDate, days);
  if (days === 0) return format(startDate, 'dd MMM yy');

  return `  ${format(endDate, 'dd MMM yy')} - ${format(startDate, 'dd MMM yy')}`;
};

const getPreviousDateRange = (days) => {
  // days = days == 0 ? days+1: days - 1;
  if (days == 0) return format(subDays(new Date(), 1), 'dd MMM yy');
  const endDate = subDays(new Date(), days + 1);
  const startDate = subDays(endDate, days - 1);
  return `${format(startDate, 'dd MMM yy')} - ${format(endDate, 'dd MMM yy')}`;
};

export const GeneralMetrics = () => {

  const metrics = useSelector(store => { return store.metricas.metric })
  const metricOneWeek = useSelector(store => { return store.metricas.metricOneWeek })
  const metricOneMonth = useSelector(store => { return store.metricas.metricOneMonth })
  const metricHour = useSelector(store => { return store.metricas.metricHour })
  // const AUXILIAR para ver si se hizo una peticion a la api
  let typeRef = React.useRef(null);
  let typeRefHour = React.useRef(false);

  const dispatch = useDispatch()
  const [loading, setLoading] = useState(false)

  const [data, setData] = useState(null)
  const [dataOneWeek, setDataOneWeek] = useState([])

  // console



  useEffect(() => {
    setLoading(true)
    dispatch(getMetrics())
    dispatch(getMetricsOneWeek())
    dispatch(getMetricsDinamyc(30))
  }, [])


  useEffect(() => {
    if (metrics === null) return
    setData(metrics.metricas);
    // const data = Object.keys(metrics.metrics?.sales.Months)
    setLoading(false);
  }, [metrics])



  useEffect(() => {
    if (metricOneWeek.length === 0) return;
    const serialSales = Object.entries(metricOneWeek.metricasIntervalo).map((info) => {
      return {
        day: info[0],
        value: info[1].ventas.ventas
      };
    });
    const serialReceipts = Object.entries(metricOneWeek.metricasIntervalo).map((info) => {
      return {
        day: info[0],
        value: info[1].ingresos.ingresos
      };
    });

    const serialClients = Object.entries(metricOneWeek.metricasIntervalo).map((info) => {
      return {
        day: info[0],
        value: info[1].clientes.ticketPromedio
      };
    });

    const serialTotalSales = Object.entries(metricOneWeek.metricasIntervalo).map((info) => {
      return {
        day: info[0],
        value: info[1].ventas.numVentas
      };
    });


    setDataOneWeek([serialSales, serialReceipts, serialClients, serialTotalSales]);

    // setLoading(false);
  }, [metricOneWeek]);



  const [resultClients, setResultClients] = useState([]);
  const [resultVentas, setResultVentas] = useState([]);
  const [resultIngresos, setResultIngresos] = useState([]);
  const [loadingMetrics, setLoadingMetrics] = useState({ Ventas: false, Ingresos: false, Clientes: false });


  useEffect(() => {

    if (typeRefHour.current) return

    if (typeRef.current && metricOneMonth.metricasIntervalo) {
      const currentType = typeRef.current; // Guarda el valor actual de typeRef.current

      if (currentType === "Ventas") {
        let ventas = {};
        let ventasPrev = {};
        Object.keys(metricOneMonth.metricasIntervalo).forEach(date => {
          ventas[`${date}`] = metricOneMonth.metricasIntervalo[date].ventas;
        });
        Object.keys(metricOneMonth.metricasIntervaloAnterior).forEach(date => {
          ventasPrev[`${date}`] = metricOneMonth.metricasIntervaloAnterior[date].ventas;
        });
        setResultVentas([ventas, ventasPrev]);
      } else if (currentType === "Ingresos") {
        let ingresos = {};
        let ingresosPrev = {};
        Object.keys(metricOneMonth.metricasIntervalo).forEach(date => {
          ingresos[`${date}`] = metricOneMonth.metricasIntervalo[date].ingresos;
        });
        Object.keys(metricOneMonth.metricasIntervaloAnterior).forEach(date => {
          ingresosPrev[`${date}`] = metricOneMonth.metricasIntervaloAnterior[date].ingresos;
        });
        setResultIngresos([ingresos, ingresosPrev]);
      } else if (currentType === "Clientes") {
        let clients = {};
        let clientsPrev = {};
        Object.keys(metricOneMonth.metricasIntervalo).forEach(date => {
          clients[`${date}`] = metricOneMonth.metricasIntervalo[date].clientes;
        });
        Object.keys(metricOneMonth.metricasIntervaloAnterior).forEach(date => {
          clientsPrev[`${date}`] = metricOneMonth.metricasIntervaloAnterior[date].clientes;
        });
        setResultClients([clients, clientsPrev]);
      }
      // apartado necesario para cambiar el loading de mi grafica
      setLoadingMetrics(prev => ({ ...prev, [currentType]: false })); // Utiliza el valor guardado en currentType
      typeRef.current = null; // Reinicia typeRef.current después de actualizar loadingMetrics

      return;
    }


    if (!(metricOneMonth?.metricasIntervalo)) return;

    let clients = {};
    let ventas = {};
    let ingresos = {};
    let clientsPrev = {};
    let ventasPrev = {};
    let ingresosPrev = {};
    Object.keys(metricOneMonth.metricasIntervalo).forEach(date => {
      clients[`${date}`] = metricOneMonth.metricasIntervalo[date].clientes;
      ventas[`${date}`] = metricOneMonth.metricasIntervalo[date].ventas;
      ingresos[`${date}`] = metricOneMonth.metricasIntervalo[date].ingresos;
    });

    Object.keys(metricOneMonth.metricasIntervaloAnterior).forEach(date => {
      clientsPrev[`${date}`] = metricOneMonth.metricasIntervaloAnterior[date].clientes;
      ventasPrev[`${date}`] = metricOneMonth.metricasIntervaloAnterior[date].ventas;
      ingresosPrev[`${date}`] = metricOneMonth.metricasIntervaloAnterior[date].ingresos;
    });

    setResultClients([clients, clientsPrev]);
    setResultVentas([ventas, ventasPrev]);
    setResultIngresos([ingresos, ingresosPrev]);
  }, [metricOneMonth]);



  useEffect(() => {
    if (typeRef.current && metricHour.metricasPorHora) {
      const currentType = typeRef.current; // Guarda el valor actual de typeRef.current

      if (currentType === "Ventas") {
        let ventas = {};
        let ventasPrev = {};
        Object.keys(metricHour.metricasPorHora).forEach(date => {
          ventas[`${date}`] = metricHour.metricasPorHora[date].ventas;
        });
        Object.keys(metricHour.metricasPorHoraAnterior).forEach(date => {
          ventasPrev[`${date}`] = metricHour.metricasPorHoraAnterior[date].ventas;
        });
        setResultVentas([ventas, ventasPrev]);
      } else if (currentType === "Ingresos") {
        let ingresos = {};
        let ingresosPrev = {};
        Object.keys(metricHour.metricasPorHora).forEach(date => {
          ingresos[`${date}`] = metricHour.metricasPorHora[date].ingresos;
        });
        Object.keys(metricHour.metricasPorHoraAnterior).forEach(date => {
          ingresosPrev[`${date}`] = metricHour.metricasPorHoraAnterior[date].ingresos;
        });
        setResultIngresos([ingresos, ingresosPrev]);
      } else if (currentType === "Clientes") {
        let clients = {};
        let clientsPrev = {};
        Object.keys(metricHour.metricasPorHora).forEach(date => {
          clients[`${date}`] = metricHour.metricasPorHora[date].clientes;
        });
        Object.keys(metricHour.metricasPorHoraAnterior).forEach(date => {
          clientsPrev[`${date}`] = metricHour.metricasPorHoraAnterior[date].clientes;
        });
        setResultClients([clients, clientsPrev]);
      }
      // apartado necesario para cambiar el loading de mi grafica
      setLoadingMetrics(prev => ({ ...prev, [currentType]: false })); // Utiliza el valor guardado en currentType
      typeRef.current = null; // Reinicia typeRef.current después de actualizar loadingMetrics
      typeRefHour.current = false

      return;
    }
  }, [metricHour]);

  const dataNotEmpty = () => {
    return (
      !isEmpty(resultClients) && !isEmpty(resultVentas) && !isEmpty(resultIngresos)
    );
  };



  const icon = <AttachMoneyIcon fontSize="large" />;
  const iconNumber = <NumbersIcon fontSize="large" />;

  // codigo para traer info de la api a las graficas

  // select
  const [month, setMonth] = React.useState({ Ventas: 4, Ingresos: 4, Clientes: 4 });
  const [monthPrevPeriod, setMonthPrevPeriod] = React.useState({ Ventas: 4, Ingresos: 4, Clientes: 4 });

  const [labelRangeDate, setLabelRangeDate] = useState({ Ventas: getDateRange(30), Ingresos: getDateRange(30), Clientes: getDateRange(30) });
  const [labelRangeDatePrev, setLabelRangeDatePrev] = useState({ Ventas: getPreviousDateRange(30), Ingresos: getPreviousDateRange(30), Clientes: getPreviousDateRange(30) });

  const handleChange = (event, type) => {
    setLoadingMetrics(prev => ({ ...prev, [type]: true }));
    const value = event.target.value;
    setMonth(prev => ({ ...prev, [type]: value }));
    setMonthPrevPeriod(prev => ({ ...prev, [type]: value }));

    typeRef.current = type;
    let range, prevRange;

    switch (value) {
      case 1:
        range = getDateRange(0);
        prevRange = getPreviousDateRange(0);
        typeRefHour.current = true;
        dispatch(getMetricsDinamyc(0));
        // dispatch(getMEtricToday())
        break;
      case 2:
        range = getDateRange(7);
        prevRange = getPreviousDateRange(7);
        dispatch(getMetricsDinamyc(7));
        break;
      case 3:
        range = getDateRange(15);
        prevRange = getPreviousDateRange(15);
        dispatch(getMetricsDinamyc(15));
        break;
      case 4:
        range = getDateRange(30);
        prevRange = getPreviousDateRange(30);
        dispatch(getMetricsDinamyc(30));
        break;
      default:
        return;
    }

    setLabelRangeDate(prev => ({ ...prev, [type]: range }));
    setLabelRangeDatePrev(prev => ({ ...prev, [type]: prevRange }));

  };

  const mobileWidth = {
    xs: "100vw",
    sm: "100vw",
    md: "100%",
    lg: "100%",
  }

  return (
    <>
      {/* {(!data) ? <h1>Loading...</h1> : */}

      {(data) && dataOneWeek ? (
        <Grid container spacing={4} sx={{ paddingLeft: 2, paddingRight: 2 }}>
          <Grid item xs={12} sm={6} md={6} lg={3}
            sx={{
              width: "100vw",
              flexBasis: { ...mobileWidth },
            }}
          >
            <CardMetric
              title="Ventas hoy"
              value={data?.ventas?.hoy.ventas.toLocaleString('es-MX', { style: 'currency', currency: 'MXN' })}
              icon={icon}
              dataOneWeek={dataOneWeek[0]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={3} 
            sx={{
              width: "100vw",
              flexBasis: { ...mobileWidth },

            }}
          > 

            <CardMetric
              title="Número de ventas"
              value={data?.ventas?.hoy.numVentas}
              icon={iconNumber}
              dataOneWeek={dataOneWeek[3]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={3}
            sx={{
              width: "100vw",
              flexBasis: { ...mobileWidth },

            }}>
            <CardMetric
              title="Ingresos hoy"
              value={data?.ingresos?.ingresosHoy.toLocaleString('es-MX', { style: 'currency', currency: 'MXN' })}
              icon={icon}
              dataOneWeek={dataOneWeek[1]}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={3} 
            sx={{
              width: "100vw",
              flexBasis: { ...mobileWidth },

            }}
          >
            <CardMetric
              title="Compras promedio"
              value={data?.clientes?.ticketPromedio.toLocaleString('es-MX', { style: 'currency', currency: 'MXN' })}
              icon={icon}
              dataOneWeek={dataOneWeek[2]}
            />
          </Grid>
        </Grid>


      ) : (
        <Grid container spacing={4} sx={{ paddingLeft: 2, paddingRight: 2 }}>
          <Grid item xss={12} xs={12} sm={6} md={4} lg={3}>
            <Box className={cardsCss.nuevooo}>
              <Skeleton variant="rectangular" width="100%" height="200px" sx={{ borderRadius: '10px' }} />
            </Box>
          </Grid>
          <Grid item xss={12} xs={12} sm={6} md={4} lg={3}>
            <Box className={cardsCss.nuevooo}>
              <Skeleton variant="rectangular" width="100%" height="200px" sx={{ borderRadius: '10px' }} />
            </Box>
          </Grid>
          <Grid item xss={12} xs={12} sm={6} md={4} lg={3}>
            <Box className={cardsCss.nuevooo}>
              <Skeleton variant="rectangular" width="100%" height="200px" sx={{ borderRadius: '10px' }} />
            </Box>
          </Grid>
          <Grid item xss={12} xs={12} sm={6} md={4} lg={3}>
            <Box className={cardsCss.nuevooo}>
              <Skeleton variant="rectangular" width="100%" height="200px" sx={{ borderRadius: '10px' }} />
            </Box>
          </Grid>
        </Grid>
      )}

      {dataNotEmpty() ?
        <Box mt={4} mb={4}>
          <Grid container spacing={4} sx={{ paddingLeft: 2, paddingRight: 2 }}>

            <Grid item xss={12} xs={12} sm={12} md={6} lg={6} sx={{ margin: "auto", width: "100vw" }}>
              <SalesOverview sales={resultVentas} type="ventas" handleChange={handleChange} month={month} monthPrevPeriod={monthPrevPeriod} labelRangeDate={labelRangeDate} labelRangeDatePrev={labelRangeDatePrev} loadingMetrics={loadingMetrics} />
            </Grid>
            <Grid item xss={12} xs={12} sm={12} md={6} lg={6} sx={{ margin: "auto" , width: "100vw"}}>
              <SalesOverview sales={resultIngresos} type="ingresos" handleChange={handleChange} month={month} monthPrevPeriod={monthPrevPeriod} labelRangeDate={labelRangeDate} labelRangeDatePrev={labelRangeDatePrev} loadingMetrics={loadingMetrics} />
            </Grid>
            <Grid item xss={12} xs={12} sm={12} md={6} lg={6} sx={{ margin: "auto", width: "100vw" }}>
              <SalesOverview sales={resultClients} type="numClientes" handleChange={handleChange} month={month} monthPrevPeriod={monthPrevPeriod} labelRangeDate={labelRangeDate} labelRangeDatePrev={labelRangeDatePrev} loadingMetrics={loadingMetrics} />
            </Grid>

          </Grid>

        </Box> : <Box mt={4} mb={4}>
          <Grid container spacing={4} sx={{ paddingLeft: 2, paddingRight: 2 }}>
            <Grid item xss={12} xs={12} sm={12} md={6} lg={6} sx={{ margin: "auto" }} >
              <Skeleton variant="rectangular" width="100%" height="620px" sx={{ borderRadius: '30px' }} />
            </Grid>
            <Grid item xss={12} xs={12} sm={12} md={6} lg={6} sx={{ margin: "auto" }}>
              <Skeleton variant="rectangular" width="100%" height="620px" sx={{ borderRadius: '30px' }} />
            </Grid>
            <Grid item xss={12} xs={12} sm={12} md={6} lg={6} sx={{ margin: "auto" }}>
              <Skeleton variant="rectangular" width="100%" height="620px" sx={{ borderRadius: '30px' }} />
            </Grid>
          </Grid>
        </Box>}



    </>
  )
}
