

import React, { useState } from 'react';
import { Tabs, Tab, Button } from '@mui/material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { GeneralMetrics } from './GeneralMetrics';

const TabPanel = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && (
        <div>
          {children}
        </div>
      )}
    </div>
  );
};



const VisualMetricas = () => {
  const [activeTab, setActiveTab] = useState(0);
 

  const onDragEnd = (result) => {
    // Handle card reorder logic here
  };

  
  return (
    <DragDropContext onDragEnd={onDragEnd}>
      {/* <Tabs value={activeTab} onChange={handleChange} sx={{marginBottom:'20px'}}>
        <Tab label="TODOS" />
        <Tab label="AMAZON" />
        <Tab label="CLARO SHOP" />
        <Tab label="MERCADO LIBRE" />
        <Tab label="WALMART" />
      </Tabs> */}
      <TabPanel value={activeTab} index={0}>
        <GeneralMetrics />
      </TabPanel>
      <TabPanel value={activeTab} index={1}>
      </TabPanel>
      <TabPanel value={activeTab} index={2}>

      </TabPanel>
      <TabPanel value={activeTab} index={3}>

      </TabPanel>
      <TabPanel value={activeTab} index={4}>
      </TabPanel>
    </DragDropContext>
  );
}

export default VisualMetricas




