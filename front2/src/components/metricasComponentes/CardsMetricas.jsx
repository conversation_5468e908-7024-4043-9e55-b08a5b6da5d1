


import React, { useEffect, useState } from 'react';
import { DragDrop<PERSON>ontext, Droppable, Draggable } from 'react-beautiful-dnd';
import { Card, CardContent, Typography, CardHeader, Box, Container } from '@mui/material';
import '../css/CardsMetricas.css'

import AmazonBG from '../img/AmazonCard.svg';
import ClaroBG from '../img/ClaroCard.svg';
import WalmartBG from '../img/WalmartCard.svg';
import MercadoLibreBG from '../img/MLCard.svg';
import TodosBG from '../img/CardWaves.svg';


export const Cards = ({ data, cardOrder, setCardOrder,activeTab }) => {
      
  const [cards, setCards] = useState(() => {
    // Declare a state variable called "cards" using useState() hook and provide an initial value
    // If cardOrder and cardOrder.length exist, map the indices in cardOrder array to corresponding data items and return as "cards"
    // Otherwise, return the original "data" as "cards"
    if (cardOrder && cardOrder.length) {

      return cardOrder.map((index) => data[index]);
    } else {

      return data;
    }
  });

  useEffect(() => {
    // useEffect() hook to update "cards" whenever "cardOrder" changes
    // If cardOrder and cardOrder.length exist, map the indices in cardOrder array to corresponding data items and update "cards"
    if (cardOrder && cardOrder.length) {
      setCards(cardOrder.map((index) => data[index]));
    }
    else if (data) {
      setCards(data);
    }

  }, [cardOrder || data]);
  


  const handleDragEnd = (result) => {
    // Event handler for drag end event
    // If no destination is provided in the result, return
    if (!result.destination) return;

    const items = Array.from(cards);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setCards(items);
  };

  const backgrounds = [TodosBG,AmazonBG,ClaroBG,MercadoLibreBG,WalmartBG];
  return (
    <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="cards"  direction="horizontal">
          {(provided) => (
            <ul {...provided.droppableProps} 
                  ref={provided.innerRef} className="cardList">
              {cards.map((card, index) => (
                <Draggable key={card.id} draggableId={card.id} index={index}>
                  {(provided) => (
                    <li
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}>
                      <Box sx={{width:"310px"}}>
                          <Card sx={{height:'164.05px' ,backgroundImage: `url(${backgrounds[activeTab]})`, flex: '0 0 0', marginRight: '10px'}}>
                            <CardContent>
                              <CardHeader title={ <Typography variant='h5' >{card.title}</Typography>}  />
                                <Typography variant="body2" >
                                  {card.content1}
                                </Typography>
                                <Typography variant="body2" >
                                  {card.content2}
                                </Typography>
                                <Typography variant="body2" >
                                  {card.content3}
                              </Typography>
                            </CardContent>
                          </Card>
                      </Box>
                    </li>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </ul>
          )}
        </Droppable>
    </DragDropContext>
  );
};

