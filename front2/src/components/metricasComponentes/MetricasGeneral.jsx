import React, { Fragment } from 'react'
import {
    Routes,
    Route,
} from "react-router-dom";

import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import VisualMetricas from './VisualMetricas';
import { TitleModule } from '../componentesGenerales/TitleModule';


const MetricasGeneral = () => {
  return (
    <div>
        <Fragment>

           <TitleModule title="Métricas"/>
            <Routes>
                <Route path="/visualizar" element={<VisualMetricas/>}/>
            </Routes>
        </Fragment> 
      
    </div>
  )
}

export default MetricasGeneral
