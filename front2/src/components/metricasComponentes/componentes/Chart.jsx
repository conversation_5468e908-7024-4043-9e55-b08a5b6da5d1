import React from 'react';
import { Select, MenuI<PERSON>, <PERSON>lide<PERSON>, FormHelperText, Skeleton, useMediaQuery } from '@mui/material';
import Chart from 'react-apexcharts';
import { Card, CardContent, Typography, Stack, Box } from "@mui/material";
import { useDispatch, useSelector } from 'react-redux';
import { useTheme } from '@emotion/react';

const skeletonHeights = {
    xl: 465, // Altura para pantallas extra grandes
    lg: 415, // Altura para pantallas grandes
    md: 365, // Altura para pantallas medianas
    sm: 315, // Altura para pantallas pequeñas
    xs: 265, // Altura para pantallas extra pequeñas
};

const DashboardCard = ({
    title,
    subtitle,
    children,
    action,
    footer,
    cardheading,
    headtitle,
    headsubtitle,
    middlecontent,
}) => {


    return (
        <Card sx={{ padding: 0 }} elevation={9} variant={undefined}>
            {cardheading ? (
                <CardContent>
                    <Typography variant="h5">{headtitle}</Typography>
                    <Typography variant="subtitle2" color="textSecondary">
                        {headsubtitle}
                    </Typography>
                </CardContent>
            ) : (
                <CardContent sx={{ p: "30px" }}>
                    {title ? (
                        <Stack
                            direction="row"
                            spacing={2}
                            justifyContent="space-between"
                            alignItems={"center"}
                            mb={3}
                        >
                            <Box>
                                {title ? <Typography variant="h5">{title}</Typography> : ""}

                                {subtitle ? (
                                    <Typography variant="subtitle2" color="textSecondary">
                                        {subtitle}
                                    </Typography>
                                ) : (
                                    ""
                                )}
                            </Box>
                            {action}
                        </Stack>
                    ) : null}

                    {children}
                </CardContent>
            )}

            {middlecontent}
            <Box>
                {footer}
            </Box>
        </Card>
    );
};


const SalesOverview = ({ sales, type, title, handleChange, month, monthPrevPeriod, labelRangeDate, labelRangeDatePrev, loadingMetrics }) => {
    const dynamic = useSelector(store => {  return store.metricas.metricsDynamic })

    const orderedDates = (id) => {
        return Object.keys(sales[id])
            .map(dateStr => {
                // Convertir a objeto de fecha para ordenar
                const [day, month, year] = dateStr.split('-').map(Number);
                return new Date(year, month - 1, day);
            })
            .sort((a, b) => a - b) // Orden ascendente
            .map(date => {
                // Convertir de vuelta a formato 'DD-MM-YYYY'
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                return `${day}-${month}-${year}`;
            })
    };

    const isHour = (id) => {
        
        const key = Object.keys(sales[id])[0];
        const keyTrue = key.includes(':');
        return keyTrue;
    };



    const info = isHour(0)
        ? null
        : orderedDates(0).map(date => {
            const [day, month] = date.split('-');
            return { sale: sales[0][date][type], date: `${day}-${month}` };
        });

    const info2 = isHour(1)
        ? null
        : orderedDates(1).map(date => {
            const [day, month] = date.split('-');
            return { sale: sales[1][date][type], date: `${day}-${month}` };
        });


    const handleChangePrevPeriod = (event) => {
        setMonthPrevPeriod(event.target.value);
    };
    // chart colors (replaced with fixed values)
    const primary = '#1c7fd4'; // Replace with the desired primary color
    const secondary = '#621cd4'; // Replace with the desired secondary color


    const categories = Object.keys(sales[0])
        .map(dateStr => {
            if (dateStr.includes(':')) { // Si dateStr es una hora
                return dateStr;
            } else { // Si dateStr es una fecha
                const [day, month, year] = dateStr.split('-').map(Number);
                return new Date(year, month - 1, day);
            }
        })
        .sort((a, b) => {
            if (typeof a === 'string' && typeof b === 'string') {
                // Comparar horas como cadenas
                return a.localeCompare(b);
            } else {
                // Comparar fechas como objetos Date
                return a - b;
            }
        });


    const filteredCategories = categories.map((date, index) => {
        if (typeof date === 'string') {
            // Es una hora
            return index % 3 === 0 ? date : '';
        } else {
            // Es una fecha
            const day = date.getDate().toString().padStart(2, '0');
            return index % 3 === 0 ? `${day}` : '';
        }
    });

    const theme = useTheme();


    // variables para mi grafica
    const optionscolumnchart = {
        chart: {
            type: 'bar',
            fontFamily: "'Plus Jakarta Sans', sans-serif;",
            foreColor: '#adb0bb',
            toolbar: {
                show: false,
            },
            height: 370,
        },
        colors: [primary, secondary, '#32GBA7'],
        plotOptions: {
            bar: {
                horizontal: false,
                barHeight: '60%',
                columnWidth: '42%',
                borderRadius: [6],
                borderRadiusApplication: 'end',
                borderRadiusWhenStacked: 'all',
            },
        },
        stroke: {
            show: true,
            width: 1,
            lineCap: 'round',
            colors: ['#fff'],
        },
        dataLabels: {
            enabled: false,
        },
        legend: {
            show: true,
        },
        grid: {
            borderColor: 'rgba(0,0,0,0.1)',
            strokeDashArray: 3,
            xaxis: {
                lines: {
                    show: true,
                },
            },
        },
        yaxis: {
            tickAmount: 4,
        },
        xaxis: {
            categories: filteredCategories,
            axisBorder: {
                show: false,
                color: secondary,
            },
            labels: {
                show: true,
            },
        },
        tooltip: {
            theme: theme.palette.mode,
            fillSeriesColor: false,
            x: {
                formatter: function (value, { series, seriesIndex, dataPointIndex, w }) {
                    const date = categories[dataPointIndex];
                    if (typeof date === 'string') {
                        // Es una hora
                        return date ;
                    } else {
                        // Es una fecha
                        return `${date.getDate().toString().padStart(2, '0')} ${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
                    }
                },
            },
            y: {
                formatter: function (value) {
                    return value.toLocaleString('es-MX', {
                        style: 'currency',
                        currency: 'MXN'
                    });
                }
            }
        },
        responsive: [
            {
                breakpoint: 1024,
                options: {
                    chart: {
                        height: 300,
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: '50%',
                        },
                    },
                },
            },
            {
                breakpoint: 768,
                options: {
                    chart: {
                        height: 250,
                    },
                    legend: {
                        position: 'bottom',
                        foreColor: '#adb0bb',
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: '60%',
                        },
                    },
                },
            },
            {
                breakpoint: 450,
                options: {
                    chart: {
                        height: 250,
                        width: 300,
                    },
                    xaxis: {
                        labels: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: '90%',
                        },
                    },
                },
            },
            {
                breakpoint: 200,
                options: {
                    chart: {
                        height: 250,
                        width: 3100,
                    },
                    xaxis: {
                        labels: {
                            show: true,
                        },
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: '100%',
                        },
                    },
                },
            },
        ],
    };
    const getChartData = (sales, type, isHour, info, period) => {
        let result = [];
        if (period === 'current') {
            result = isHour(0)
                ? (sales[0] && Object.keys(sales[0]).map(dateStr => {
                    return sales[0][dateStr] ? sales[0][dateStr][type] : null;
                }))
                : info.map(infoItem => infoItem.sale);
        } else if (period === 'previous') {
            result = isHour(1)
                ? (sales[1] && Object.keys(sales[1]).map(dateStr => {
                    return sales[1][dateStr] ? sales[1][dateStr][type] + 100 : null;
                }))
                : info.map(infoItem => infoItem.sale);
        }
        return result;
    };

    // variables para mi grafica
    const seriescolumnchart = [
        {
            name: `Periodo Actual`,
            data: getChartData(sales, type, isHour, info, 'current')
        },
        {
            name: `Periodo Anterior`,
            data: getChartData(sales, type, isHour, info2, 'previous')
        }
    ];



    const typeTitle = type === "ventas" ? "Ventas" : type == "numClientes" ? "Clientes" : "Ingresos"

    const breakpoint = useMediaQuery(theme.breakpoints.down('xl')) ? 'xl' :
        useMediaQuery(theme.breakpoints.down('lg')) ? 'lg' :
            useMediaQuery(theme.breakpoints.down('md')) ? 'md' :
                useMediaQuery(theme.breakpoints.down('sm')) ? 'sm' :
                    'xs';

    return (
        <DashboardCard
            sx={{ display: "flex", flexWrap: "wrap" , justifyContent: "end"}}
            title={typeTitle}
            action={
                <Box sx={{ display: "flex", flexDirection: "row", flexWrap: "wrap", gap: 1, justifyContent:"end" }}>
                    <Box>
                        <Select
                            // labelId="month-dd"
                            // id="month-dd"
                            value={month[typeTitle]}
                            size="small"
                            onChange={(e) => handleChange(e, typeTitle)}
                            sx={{ minWidth: "100px" }}
                            inputProps={{ 'aria-label': 'Without label' }}
                            // disabled={!loadingMetrics[typeTitle]}
                            disabled={loadingMetrics['Ventas'] || loadingMetrics['Ingresos'] || loadingMetrics['Clientes']}
                        >
                            <MenuItem value={1} name="hoy">Hoy </MenuItem>
                            <MenuItem value={2}>7 Dias </MenuItem>
                            <MenuItem value={3}>15 Dias </MenuItem>
                            <MenuItem value={4}>30 Dias</MenuItem>

                        </Select>
                        <FormHelperText>{labelRangeDate[typeTitle]}</FormHelperText>
                    </Box>
                    <Box>

                        <Select
                            labelId="month-dd"
                            id="month-dd"
                            value={monthPrevPeriod[typeTitle]}
                            size="small"
                            onChange={handleChangePrevPeriod}
                            sx={{ minWidth: "100px" }}
                            disabled
                        >
                            <MenuItem value={1}>Hoy</MenuItem>
                            <MenuItem value={2}>7 Dias</MenuItem>
                            <MenuItem value={3}>15 Dias</MenuItem>
                            <MenuItem value={4}>30 Dias</MenuItem>
                        </Select>
                        <FormHelperText >{labelRangeDatePrev[typeTitle]}</FormHelperText>
                    </Box>
                </Box>
            }
        >
            {loadingMetrics[typeTitle] ?
                <Skeleton
                    variant="rectangular"
                    width="100%"
                    height={skeletonHeights[breakpoint]} // Ajustar según el breakpoint actual
                    sx={{ borderRadius: '30px' }}
                />
                :
                <Chart
                    options={optionscolumnchart} series={seriescolumnchart} type="area" height={450}
                />
            }

        </DashboardCard>
    );
};

export default SalesOverview;

