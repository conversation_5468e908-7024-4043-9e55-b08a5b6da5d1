// import dynamic from "next/dynamic";
// const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });
import { useTheme } from '@mui/material/styles';
import { Stack, Typography, Avatar, Fab, Box, Card, CardContent, Tooltip, } from '@mui/material';
import cardsCss from '../../css/metrics/generalMetrics.module.css'
import Chart from "react-apexcharts";

// import { IconArrowDownRight, IconCurrencyDollar } from '@tabler/icons-react';
// import DashboardCard from '@/app/(DashboardLayout)/components/shared/DashboardCard';

const DashboardCard = ({
    title,
    subtitle,
    children,
    action,
    footer,
    cardheading,
    headtitle,
    headsubtitle,
    middlecontent,
}) => {
    return (
        <Card sx={{ padding: 0 }} elevation={9} variant={undefined}>
            {cardheading ? (
                <CardContent>
                    <Typography variant="h5">{headtitle}</Typography>
                    <Typography variant="subtitle2" color="textSecondary">
                        {headsubtitle}
                    </Typography>
                </CardContent>
            ) : (
                <CardContent sx={{ p: '16px 16px 0 16px' }}>
                    {title ? (
                        <Stack
                            direction="row"
                            spacing={2}
                            justifyContent="space-between"
                            alignItems={"center"}
                            mb={3}
                        >
                            <Box>
                                {title ? <Typography variant="h5">{title}</Typography> : ''}

                                {subtitle ? (
                                    <Typography variant="subtitle2" color="textSecondary">
                                        {subtitle}
                                    </Typography>
                                ) : (
                                    ''
                                )}
                            </Box>
                            {action}
                        </Stack>
                    ) : null}

                    {children}
                </CardContent>
            )}

            {middlecontent}
            <Box className={cardsCss.bonoooo}>
                {footer}
            </Box>
        </Card>
    );
};


export const CardMetric = ({ title, value, icon,dataOneWeek }) => {
    // chart color
    const theme = useTheme();
    const secondary = theme.palette.secondary.main;
    const secondarylight = '#f5fcff';
    const errorlight = '#fdede8';


    // chart
    const optionscolumnchart = {
        chart: {
            type: 'area',
            fontFamily: "'Plus Jakarta Sans', sans-serif;",
            foreColor: '#adb0bb',
            toolbar: {
                show: false,
            },
            height: 60,
            sparkline: {
                enabled: true,
            },
            group: 'sparklines',
        },
        stroke: {
            curve: 'smooth',
            width: 2,
        },
        fill: {
            colors: [secondarylight],
            type: 'solid',
            opacity: 0.05,
        },
        markers: {
            size: 0,
        },
        tooltip: {
            theme: theme.palette.mode === 'dark' ? 'dark' : 'light',
            fixed: {
                enabled: false,
                position: 'top'
              },
              shared: true
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            offsetX: -10
          }
    };

    const seriescolumnchart = [
        {
            name: '',
            color: secondary,
            data: dataOneWeek?.map((info) => info.value)
        },
    ];

    return (
        <DashboardCard
        className={cardsCss.CardMetric}
        title={
          <Tooltip title={title}>
            <Typography
              noWrap
              sx={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                width: '100%',
                fontWeight: 600,
                fontSize: '1rem',
              }}
            >
              {title}
            </Typography>
          </Tooltip>
        }
            action={
                <Fab color="secondary" size="small" sx={{ color: '#ffffff',
                    zIndex: 1,
                 }}>
                    {icon && <Box 
                        sx ={{
                            zIndex: 1,
                            position: "absolute",}}
                    >{icon}</Box>}
                </Fab>
            }
            footer={
                // <Box className={cardsCss.LineChartBox}>
                <Chart options={optionscolumnchart} series={seriescolumnchart} type="area" height={100} width={"100%"} />

               
            }
        >

            <>

                <Typography variant="h4" fontWeight="400" mt="-20px" className={cardsCss.ssshhsshhs}>
                    {value}
                </Typography>
                <Stack direction="row" spacing={1} my={1} alignItems="center">
                </Stack>
            </>
        </DashboardCard>
    );
};
// export default MonthlyEarnings;