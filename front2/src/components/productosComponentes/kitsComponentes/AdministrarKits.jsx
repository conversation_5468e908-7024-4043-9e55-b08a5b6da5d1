import {
  <PERSON><PERSON>,
  <PERSON>,
  But<PERSON>,
  DialogActions,
  DialogTitle,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  tableCellClasses,
} from "@mui/material";
import React, { useEffect } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { LONGDESCRIPTION } from "../../../Utils/config";
import { checkLength } from "../../../Utils/generalFunctions";
import BuscarProducto from "../BuscarProducto";
import Delete from "@mui/icons-material/Delete";
import styled from "@mui/material/styles/styled";
import { Add, MoreVert, Remove } from "@mui/icons-material";
import notImage from "../../img/notImage.png";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import { useDispatch, useSelector } from "react-redux";
import { createKit, resetMessage, updateKit } from "../../../redux/kitsDucks";
import { StyledTableCell, StyledTableRow } from "../../StyledTableComponents";
import { Dialog, DialogContent } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { NumericFormat } from "react-number-format";

const AdministrarKits = (kitInfo) => {
  // State variables initialization
  const [nombreDelKit, setNombreDelKit] = React.useState("");
  const [sku, setSku] = React.useState("");
  const [selectedProduct, setSelectedProduct] = React.useState(null);
  const [enabled, setEnabled] = React.useState(false);
  const mensaje = useSelector((store) => store.kits.mensaje);
  const severity = useSelector((store) => store.kits.severity);
  const navigate = useNavigate();
  const [products, setProducts] = React.useState([]);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [windowWidth, setWindowWidth] = React.useState(window.innerWidth);
  const [jsonToSend, setJsonToSend] = React.useState({});
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  const dispatch = useDispatch();

  // Function to handle drag end event to reorder the kit products
  const onDragEnd = (result) => {
    if (!result.destination) return; // Si no hay destino, no hacemos nada

    const updatedRows = [...products];
    const [reorderedItem] = updatedRows.splice(result.source.index, 1);
    updatedRows.splice(result.destination.index, 0, reorderedItem);

    setProducts(updatedRows);
  };

  // Function to handle window resize event and make the kits responsive
  const handleResize = () => {
    setWindowWidth(window.innerWidth);
  };

  // Function to handle the close of the select menu of every product in the kit
  const handleClose = () => {
    setAnchorEl(null);
  };

  React.useEffect(() => {
    // Add an event listener to handle the window resize event
    window.addEventListener("resize", handleResize);

    // Clear the event listener when the component is unmounted
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // State to handle the validation of the long description of the kit and set a new description
  const [longDescriptionValidation, setLongDescriptionValidation] =
    React.useState({
      valor: "",
      error: false,
      color: null,
      textoAyuda: "",
    });

  // Function to handle the change of the long description of the kit
  const manejarCambioDeDescripcionLarga = (value) => {
    const descripcionLarga = value;

    if (descripcionLarga.length === 0) {
      setLongDescriptionValidation({
        valor: descripcionLarga,
        error: false,
        color: "warning",
        textoAyuda: "Vacio",
      });
    } else {
      if (descripcionLarga.length >= LONGDESCRIPTION.longitud) {
        setLongDescriptionValidation({
          valor: descripcionLarga,
          error: false,
          color: "warning",
          textoAyuda: "Límite de caracteres alcanzado",
        });
      } else {
        let longitudValidation = checkLength(
          LONGDESCRIPTION.label,
          descripcionLarga,
          LONGDESCRIPTION.longitud
        );
        if (longitudValidation === true) {
          setLongDescriptionValidation({
            valor: descripcionLarga,
            error: false,
            color: "success",
            textoAyuda: "Descripción larga correcta",
          });
        } else {
          setLongDescriptionValidation({
            valor: descripcionLarga,
            error: true,
            color: null,
            textoAyuda: longitudValidation,
          });
          return longitudValidation;
        }
      }
    }
    return "";
  };

  // Function to handle the validation of the form to enable the save button
  React.useEffect(() => {
    if (
      nombreDelKit !== "" &&
      sku !== "" &&
      longDescriptionValidation.valor !== "" &&
      longDescriptionValidation.valor !== null &&
      longDescriptionValidation.valor !== undefined &&
      longDescriptionValidation.valor !== "<p><br></p>"
    ) {
      if (products.length > 0) {
        if (products.some((product) => product.cantidad > 1)) {
          setEnabled(true);
        } else if (products.length > 1) {
          setEnabled(true);
        }
      }
    } else {
      setEnabled(false);
    }
  }, [products, nombreDelKit, sku, longDescriptionValidation]);

  // Function to reset the modal and the message after the kit is created or updated
  React.useEffect(() => {
    mensaje && severity === "success" && setProducts([]);
  }, [mensaje, severity]);

  // Function to reset the message when the component is mounted
  React.useEffect(() => {
    dispatch(resetMessage());
  }, []);

  // Function to redirect to the kits list after the kit is created or updated
  const handleModal = () => {
    navigate("../consultarKits");
  };

  // Function to create the json to send to the backend
  useEffect(() => {
    const newProductsArray = products?.map((product) => ({
      internalSKu: product.internalBaseSku,
      amount: product.cantidad,
    }));

    setJsonToSend({
      title: nombreDelKit,
      description: longDescriptionValidation.valor,
      kit_products: newProductsArray,
    });
  }, [products, nombreDelKit, longDescriptionValidation]);

  // Function to add the description of the selected product to the long description of the kit
  const handleAgregarInfo = () => {
    const product = products[selectedIndex];
    const text = product.description;
    const descripcionLarga = longDescriptionValidation.valor;
    const texto = text;
    const nuevaDescripcionLarga = descripcionLarga + texto;
    manejarCambioDeDescripcionLarga(nuevaDescripcionLarga);
    handleClose();
  };

  // Function to replace the description of the kit with the long description of the selected product
  const handleReemplazarInfo = () => {
    const product = products[selectedIndex];
    const descripcion = product.description;
    const nombre = "Kit " + product.brand + " " + product.model;
    const sku = product.internalBaseSku;
    setSku(sku);
    setNombreDelKit(nombre);
    manejarCambioDeDescripcionLarga(descripcion);
    handleClose();
  };

  // Function to send the kit information to the backend
  const handleSendInfo = () => {
    //console.log(jsonToSend, "jsonToSend")
    if (kitInfo && kitInfo.kitInfo && kitInfo.kitInfo.products?.length > 0) {
      const newJsonToSend = {
        ...jsonToSend,
        kit_id: kitInfo.kitInfo.id,
      };
      dispatch(updateKit(newJsonToSend));
    } else {
      dispatch(createKit(jsonToSend));
    }
  };
  const [selectedProductVariations, setSelectedProductVariations] =
    React.useState(null);

  // Function to set the selected product variations and set the kit name, sku and long description when theres no info in the form
  React.useEffect(() => {
    if (selectedProduct) {
      setSelectedProductVariations(selectedProduct.products);
      if (products?.length === 0) {
        if (nombreDelKit === "") {
          setNombreDelKit(
            "Kit " + selectedProduct.brand + " " + selectedProduct.model
          );
        }
        if (sku === "") {
          setSku(selectedProduct.internalBaseSku);
        }
        if (
          longDescriptionValidation.valor === "" ||
          longDescriptionValidation.valor === null ||
          longDescriptionValidation.valor === undefined ||
          longDescriptionValidation.valor === "<p><br></p>"
        ) {
          manejarCambioDeDescripcionLarga(selectedProduct.description);
        }
      }
    }
  }, [selectedProduct]);

  // Function to add a product to the kit
  const agregarProducto = (product) => {
    const productWithAmount = {
      photo: product.productPhotos[0],
      internalBaseSku: product.internalSku,
      brand: selectedProduct.brand,
      model: selectedProduct.model,
      description:
        product?.ProductVariationCustom[0]?.customVariation
          ?.customVariationName +
        " " +
        product?.ProductVariationCustom[0]?.customVariationValue
          ?.customVariationValue +
        " " +
        (product.variationDescription === null
          ? ""
          : product.variationDescription) +
        " " +
        (selectedProduct.description === null
          ? ""
          : selectedProduct.description),
      cantidad: 1,
    };
    //console.log(products, "products")
    products !== undefined && products !== null && products.length > 0
      ? setProducts((prev) => [...prev, productWithAmount])
      : setProducts([productWithAmount]);
  };

  //Function to check if we are editing a kit and set the form with the kit info
  React.useEffect(() => {
    if (kitInfo !== null && kitInfo !== undefined) {
      //console.log(kitInfo, "kitInfo")
      setNombreDelKit(kitInfo?.kitInfo?.title);
      manejarCambioDeDescripcionLarga(
        kitInfo?.kitInfo?.description ? kitInfo?.kitInfo?.description : ""
      );
      const productsToStart = kitInfo?.kitInfo?.products?.map((product) => ({
        photo: product.photo ? product.photo : null,
        internalBaseSku: product.internalSku,
        brand: product.brand ? product.brand : "",
        model: product.model ? product.model : "",
        description: product.description ? product.description : "",
        cantidad: product.amount,
      }));
      setProducts(productsToStart);
    }
  }, [kitInfo]);

  return (
    <>
      <Grid container spacing={2}>
        {/* Kit's name textfield */}
        <Grid item xs={12} sm={12} md={6}>
          <TextField
            sx={{ width: "100%" }}
            label="Nombre del Kit"
            name="nombreDelKit"
            value={nombreDelKit}
            onChange={(e) => setNombreDelKit(e.target.value)}
          />
        </Grid>
        {/* Kit's sku textfield */}
        <Grid item xs={12} sm={12} md={6}>
          <TextField
            sx={{ width: "100%" }}
            label="SKU"
            name="sku"
            value={sku}
            onChange={(e) => setSku(e.target.value)}
          />
        </Grid>
        {/* Kit's long description */}
        <Grid
          item
          xs={12}
          sm={12}
          sx={{ height: "200px", overflow: "auto" }}
          md={12}
        >
          <ReactQuill
            required
            id={LONGDESCRIPTION.nombre}
            name={LONGDESCRIPTION.nombre}
            onChange={manejarCambioDeDescripcionLarga}
            value={longDescriptionValidation.valor}
            className={longDescriptionValidation.color}
            error={longDescriptionValidation.error}
            modules={{
              toolbar: [
                [{ header: [1, 2, 3, 4, 5, 6, false] }],
                ["bold", "italic", "underline", "strike", "blockquote"],
                [{ list: "ordered" }, { list: "bullet" }],
                ["link", "image"],
                ["clean"],
              ],
            }}
            style={{ height: "70%" }}
          />
        </Grid>
        {/* Search base product */}
        <Grid item xs={12} sm={12} md={12}>
          <BuscarProducto setSelectedProduct={setSelectedProduct} kits={true} />
        </Grid>
        {/* Base product variations table */}
        <Grid
          item
          xs={12}
          sm={12}
          md={12}
          sx={{ maxHeight: "400px", overflow: "auto" }}
        >
          {selectedProduct && (
            <Table>
              {selectedProductVariations && (
                <TableHead>
                  <StyledTableRow>
                    {windowWidth > 660 && (
                      <StyledTableCell
                        sx={{ width: "10%", textAlign: "center" }}
                      >
                        Imagen
                      </StyledTableCell>
                    )}

                    <StyledTableCell sx={{ width: "10%", textAlign: "center" }}>
                      SKU Interno
                    </StyledTableCell>

                    <StyledTableCell sx={{ width: "10%", textAlign: "center" }}>
                      Tipo
                    </StyledTableCell>

                    {windowWidth > 800 && (
                      <StyledTableCell sx={{ textAlign: "center" }}>
                        Descripción
                      </StyledTableCell>
                    )}
                    <StyledTableCell sx={{ width: "10%", textAlign: "center" }}>
                      Acciones
                    </StyledTableCell>
                  </StyledTableRow>
                </TableHead>
              )}
              <TableBody>
                {selectedProductVariations?.map((product, index) => (
                  <StyledTableRow key={index}>
                    {windowWidth > 660 && (
                      <StyledTableCell sx={{ textAlign: "center" }}>
                        <img
                          src={
                            product?.productPhotos[0]
                              ? product?.productPhotos[0].URLPhoto
                              : notImage
                          }
                          width="80px"
                          height="80px"
                          style={{ borderRadius: "20px", margin: "5px 0" }}
                        />
                      </StyledTableCell>
                    )}

                    <StyledTableCell sx={{ textAlign: "center" }}>
                      {product?.internalSku}
                    </StyledTableCell>

                    <StyledTableCell sx={{ textAlign: "center" }}>
                      {product?.ProductVariationCustom[0]?.customVariation
                        ?.customVariationName +
                        " " +
                        product?.ProductVariationCustom[0]?.customVariationValue
                          ?.customVariationValue}
                    </StyledTableCell>

                    {windowWidth > 800 && (
                      <StyledTableCell>
                        <Box
                          sx={{ width: "100%" }}
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <div
                            style={{
                              overflow: "hidden",
                              display: "-webkit-box",
                              WebkitLineClamp: 2, // Número de líneas a mostrar
                              WebkitBoxOrient: "vertical",
                              textAlign: "center", // Añadir centrado
                            }}
                          >
                            {product?.variationDescription}
                          </div>
                        </Box>
                      </StyledTableCell>
                    )}
                    <StyledTableCell sx={{ textAlign: "center" }}>
                      <IconButton
                        variant="outlined"
                        color="primary"
                        onClick={() => {
                          agregarProducto(product);
                        }}
                      >
                        <Add />
                      </IconButton>
                    </StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </Grid>
        {/* Kit's products table */}
        <Grid
          item
          xs={12}
          sm={12}
          md={12}
          sx={{ maxHeight: "400px", overflow: "auto" }}
        >
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="droppable">
              {(provided) => (
                <Table {...provided.droppableProps} ref={provided.innerRef}>
                  {products?.length > 0 && (
                    <TableHead>
                      <StyledTableRow>
                        {windowWidth > 660 && (
                          <StyledTableCell
                            sx={{ width: "10%", textAlign: "center" }}
                          >
                            Imagen
                          </StyledTableCell>
                        )}
                        {windowWidth > 473 && (
                          <StyledTableCell
                            sx={{ width: "10%", textAlign: "center" }}
                          >
                            SKU Interno
                          </StyledTableCell>
                        )}
                        {windowWidth > 660 && (
                          <StyledTableCell
                            sx={{ width: "10%", textAlign: "center" }}
                          >
                            Marca
                          </StyledTableCell>
                        )}
                        <StyledTableCell
                          sx={{ width: "10%", textAlign: "center" }}
                        >
                          Modelo
                        </StyledTableCell>
                        {windowWidth > 800 && (
                          <StyledTableCell sx={{ textAlign: "center" }}>
                            Descripción
                          </StyledTableCell>
                        )}
                        <StyledTableCell
                          sx={{ width: "10%", textAlign: "center" }}
                        >
                          Cantidad
                        </StyledTableCell>
                        <StyledTableCell
                          sx={{ width: "10%", textAlign: "center" }}
                        >
                          Acciones
                        </StyledTableCell>
                      </StyledTableRow>
                    </TableHead>
                  )}
                  <TableBody>
                    {products?.map((product, index) => (
                      <Draggable
                        key={index}
                        draggableId={index.toString()}
                        index={index}
                      >
                        {(provided) => (
                          <StyledTableRow
                            key={index}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            ref={provided.innerRef}
                            sx={{
                              border:
                                index === 0 ? "2px solid #003876" : "none",
                            }}
                          >
                            {windowWidth > 660 && (
                              <StyledTableCell sx={{ textAlign: "center" }}>
                                <img
                                  src={
                                    product.photo
                                      ? product.photo.URLPhoto
                                      : notImage
                                  }
                                  width="80px"
                                  height="80px"
                                  style={{
                                    borderRadius: "20px",
                                    margin: "5px 0",
                                  }}
                                />
                              </StyledTableCell>
                            )}

                            {windowWidth > 473 && (
                              <StyledTableCell sx={{ textAlign: "center" }}>
                                {product.internalBaseSku}
                              </StyledTableCell>
                            )}
                            {windowWidth > 660 && (
                              <StyledTableCell sx={{ textAlign: "center" }}>
                                {product.brand}
                              </StyledTableCell>
                            )}
                            <StyledTableCell sx={{ textAlign: "center" }}>
                              {product.model}
                            </StyledTableCell>
                            {windowWidth > 800 && (
                              <StyledTableCell>
                                <Box
                                  sx={{ width: "100%" }}
                                  display="flex"
                                  alignItems="center"
                                  justifyContent="center"
                                >
                                  <div
                                    style={{
                                      overflow: "hidden",
                                      display: "-webkit-box",
                                      WebkitLineClamp: 2, // Número de líneas a mostrar
                                      WebkitBoxOrient: "vertical",
                                      textAlign: "center", // Añadir centrado
                                    }}
                                  >
                                    {product.description}
                                  </div>
                                </Box>
                              </StyledTableCell>
                            )}
                            <StyledTableCell sx={{ textAlign: "center" }}>
                              <Box
                                sx={{
                                  width: "100%", // O el ancho deseado
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                              >
                                <IconButton
                                  variant="outlined"
                                  color="primary"
                                  onClick={() => {
                                    setProducts((prev) =>
                                      prev.map((item, i) => {
                                        if (i === index) {
                                          if (item.cantidad > 1) {
                                            return {
                                              ...item,
                                              cantidad: item.cantidad - 1,
                                            };
                                          }
                                        }
                                        return item; // Importante devolver el item original si no se cumple la condición
                                      })
                                    );
                                  }}
                                  disabled={product.cantidad === 1}
                                >
                                  <Remove />
                                </IconButton>
                                {/* <TextField
                                                    sx={{ width: "80px", textAlign: "center" }}
                                                    label="Cantidad"
                                                    name="cantidad"
                                                    value={product.cantidad}
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        setProducts((prev) =>
                                                            prev.map((item, i) => {
                                                                if (i === index) {
                                                                    return { ...item, cantidad: value };
                                                                } else {
                                                                    return item;
                                                                }
                                                            })
                                                        );
                                                    }}
                                                /> */}
                                <NumericFormat
                                  sx={{ width: "80px", textAlign: "center" }}
                                  label="Cantidad"
                                  name="cantidad"
                                  value={product.cantidad}
                                  onValueChange={(values) => {
                                    const { formattedValue, value } = values;
                                    setProducts((prev) =>
                                      prev.map((item, i) => {
                                        if (i === index) {
                                          return { ...item, cantidad: value };
                                        } else {
                                          return item;
                                        }
                                      })
                                    );
                                  }}
                                  customInput={TextField}
                                  thousandSeparator={true}
                                  decimalScale={0}
                                  allowNegative={false}
                                  isNumericString={true}
                                />
                                <IconButton
                                  variant="outlined"
                                  color="primary"
                                  onClick={() => {
                                    setProducts((prev) =>
                                      prev.map((item, i) => {
                                        if (i === index) {
                                          return {
                                            ...item,
                                            cantidad: item.cantidad + 1,
                                          };
                                        } else {
                                          return item;
                                        }
                                      })
                                    );
                                  }}
                                >
                                  <Add />
                                </IconButton>
                              </Box>
                            </StyledTableCell>

                            <StyledTableCell
                              sx={{
                                alignItems: "center",
                                alignContent: "center",
                                textAlign: "center",
                              }}
                            >
                              <IconButton
                                variant="outlined"
                                color="primary"
                                onClick={() => {
                                  setProducts((prev) =>
                                    prev.filter((item, i) => i !== index)
                                  );
                                }}
                              >
                                <Delete />
                              </IconButton>
                              <IconButton
                                variant="outlined"
                                color="primary"
                                onClick={(e) => (
                                  setAnchorEl(e.currentTarget),
                                  setSelectedIndex(index)
                                )}
                              >
                                <MoreVert />
                              </IconButton>
                              <Menu
                                id="simple-menu"
                                anchorEl={anchorEl}
                                keepMounted
                                open={Boolean(anchorEl)}
                                onClose={handleClose}
                              >
                                <MenuItem onClick={handleAgregarInfo}>
                                  Agregar Descripción
                                </MenuItem>
                                <MenuItem onClick={handleReemplazarInfo}>
                                  Reemplazar Info
                                </MenuItem>
                              </Menu>
                            </StyledTableCell>
                          </StyledTableRow>
                        )}
                      </Draggable>
                    ))}

                    {provided.placeholder}
                  </TableBody>
                </Table>
              )}
            </Droppable>
          </DragDropContext>
        </Grid>
        {/* Save button */}
        <Grid item xs={12} sm={12} md={12}>
          <Button
            variant="contained"
            color="primary"
            sx={{ width: "100%" }}
            onClick={handleSendInfo}
            disabled={!enabled}
          >
            Guardar Kit
          </Button>
        </Grid>
      </Grid>
      {/* Modal to show the message after the kit is created or updated */}
      {mensaje &&
        (severity == "success" ? (
          <Dialog
            open={Boolean(mensaje)}
            onClose={handleModal}
            disableBackdropClick
          >
            <DialogTitle>
              {severity == "success" ? "Éxito" : "Error"}
            </DialogTitle>
            <DialogContent>{mensaje}</DialogContent>
            <DialogActions>
              <Button onClick={handleModal}>Ver Kits</Button>
            </DialogActions>
          </Dialog>
        ) : (
          <Alert severity={severity} sx={{ margin: "1rem" }}>
            {mensaje}
          </Alert>
        ))}
    </>
  );
};
export default AdministrarKits;
