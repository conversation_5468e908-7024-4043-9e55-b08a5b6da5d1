import React from 'react'
import { useParams } from 'react-router-dom'
import AdministrarKits from './AdministrarKits';
import { useDispatch, useSelector } from 'react-redux';
import CargandoLista from '../../CargandoLista';
import { ManejarErrores } from '../../ManejarErrores';
import { Button } from '@mui/material';
import styles from "../../css/findBySku.module.css";
import { obtenerUnKit } from '../../../redux/kitsDucks';


const EditarKit = () => {
    const Kit = useParams();
    const dispatch = useDispatch();
    //console.log(Kit.id);
    const kitInfo = useSelector((store) => store.kits.kitIndividual);
    const loading = useSelector((store) => store.kits.loading);
    React.useEffect(() => {
        dispatch(obtenerUnKit(Kit.id));
    }, []);

    const IdNotFound = () => {
        return (
        <div className={styles.divContainer}>
            <h3 className={styles.h3Style}>
            Kit ID no encontrado, verifica la información
            </h3>
            <div className={styles.divContainerBtnP}>
            <p style={{ margin: "0" }}>Consultar los kits 👉 </p>
            <Button
                size="small"
                className={styles.btn}
                variant="contained"
                color="buttonGreen"
                onClick={() => {
                window.location.href = "productos/consultarKits";
                }}
            >
                Consultar
            </Button>
            </div>
        </div>
        );
    };

    if (loading) {
        return <CargandoLista />;
    }else{
        if (Kit.id) {
            if (
            typeof kitInfo === "string" &&
            kitInfo.startsWith("status")
            ) {
            return <ManejarErrores errorCode={kitInfo} />;
            } else {
            switch (kitInfo) {
                case "notFound":
                return IdNotFound();
                default:
                return (
                    <AdministrarKits kitInfo={kitInfo} />
                );
            }
            }
        } else {
            return <CargandoLista />;
        }
    }
}


export default EditarKit
