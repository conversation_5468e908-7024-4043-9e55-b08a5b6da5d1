import React from 'react'
import Typography from '@mui/material/Typography';
import { Link, useSearchParams } from "react-router-dom";
import TablaModalRelacionProductos from '../facturasComponentes/TablaModalRelacionProductos';
import { useDispatch, useSelector } from 'react-redux';
import CustomDialog from '../componentesGenerales/CustomDialog';
import { persistirDatosNuevoProducto, resetPersistirDatosNuevoProducto, setNewProductOrigin } from '../../redux/productosDucks';
import { enlazarProductosConFacturas, ingresarProductos, setJsonToSend } from '../../redux/facturasDucks';
import { useCookies } from 'react-cookie';
import { Box, Button, Grid } from '@mui/material';


const ModalBuscarProducto = (props) => {
    const [searchParams] = useSearchParams();
    const openP = props.openP;
    const setOpenP = props.setOpenP;
    const handleCloseP = props.handleCloseP;
    const searchText = searchParams.get('search') === null ? '' : searchParams.get('search');
    const [cookies, setCookie] = useCookies();
    const [buscarEnabled, setBuscarEnabled] = React.useState(true);
    const dispatch = useDispatch();
    const [selectedProduct, setSelectedProduct] = React.useState(null);


    const productFromEntryDocument = useSelector(
        (store) => store.facturas.selectedProduct
    );

    // Estado para la estructura de envío al backend
    const [productEnteredData, setProductEnteredData] = React.useState({
        productFromEntryDocumentId: productFromEntryDocument?.id || null,
        productsEnteredIntoStore: []
    });
    const [currentlyEntering, setCurrentlyEntering] = React.useState(0);

    const facturaSeleccionada = useSelector(
        (store) => store.facturas.facturaSeleccionada
    );



    const enviarSearch = (event) => {
        event.preventDefault();
    }

    const handleSetInfo = () => {
        dispatch(setNewProductOrigin(true));
        dispatch(resetPersistirDatosNuevoProducto());
        persistirDatosNuevoProducto({
            key: "model",
            value: productFromEntryDocument.model,
        });
        dispatch(
            persistirDatosNuevoProducto({
                key: "description",
                value: productFromEntryDocument.description,
            })
        );
        dispatch(
            persistirDatosNuevoProducto({
                key: "unitCode",
                value: productFromEntryDocument.unitKey,
            })
        );
        dispatch(
            persistirDatosNuevoProducto({
                key: "satCode",
                value: productFromEntryDocument.satKey,
            })
        );
        dispatch(
            persistirDatosNuevoProducto({
                key: "upc",
                value: productFromEntryDocument.upc,
            })
        );
    };

    const handleIngresar = () => {
        if (setJsonToSend) {
            /*       aqui en ves de json poner el productEnteredData*/
            dispatch(ingresarProductos(productEnteredData, cookies.csrf_access_token, productFromEntryDocument.totalEntered + currentlyEntering, setCurrentlyEntering));
        } else {
            alert("No hay productos para ingresar");
        }
    };

    const handleConfirmar = () => {
        dispatch(
            enlazarProductosConFacturas(
                productFromEntryDocument.id,
                selectedProduct.internalBaseSku,
                facturaSeleccionada,
                cookies.csrf_access_token
            )
        );
    };


    return (
        <CustomDialog
            open={openP}
            onClose={handleCloseP}
            title="Busca el producto que deseas relacionar"
            maxWidth="lg"
            width="85%"
            maxHeight="80vh"
            actions={
                <Box display="flex" flexDirection="row" sx={{ justifyContent: "center" }}>
                    {buscarEnabled && (
                    <Grid item width={210.4} sx={{ margin: "20px 10px" }}>
                        <Button
                            component={Link}
                            to="/productos/nuevo"
                            variant="outlined"
                            color="buttonGreenPink"
                            fullWidth
                            onClick={handleSetInfo}
                        >
                            Agregar Producto
                        </Button>
                    </Grid>
                    )}
                    <Grid item width={210.4} sx={{ margin: "20px 10px" }}>
                        {buscarEnabled === false ? (
                            <Button
                                variant="contained"
                                color="buttonGreenPink"
                                fullWidth
                                onClick={handleIngresar}
                                disabled={productEnteredData.productsEnteredIntoStore.length === 0}
                            >
                                Ingresar
                            </Button>
                        ) : (
                            <Button
                                variant="contained"
                                color="buttonGreenPink"
                                fullWidth
                                onClick={handleConfirmar}
                            >
                                Confirmar
                            </Button>
                        )}
                    </Grid>
                </Box>
            }
        >

            <TablaModalRelacionProductos
                openP={openP}
                handleCloseP={handleCloseP}
                setOpenP={setOpenP}
                buscarEnabled={buscarEnabled}
                setBuscarEnabled={setBuscarEnabled}
                productEnteredData={productEnteredData}
                setProductEnteredData={setProductEnteredData}
                setCurrentlyEntering={setCurrentlyEntering}
                currentlyEntering={currentlyEntering}
                selectedProduct={selectedProduct}
                setSelectedProduct={setSelectedProduct}
            />
        </CustomDialog>
    )
}

export default ModalBuscarProducto