import React, { useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, Button, TextField, IconButton, Chip } from '@mui/material';
import { useNavigate } from 'react-router-dom';



const DialogWarning = ({ setDialogWarning, dialogWarning }) => {
    const navigate = useNavigate();


    const handleClose = () => {
        setDialogWarning(false);
    };



    return (
        <div sx={{ maxWidth: "675px" }}>

            <Dialog open={dialogWarning} onClose={handleClose} keepMounted>
                <DialogTitle>Advertencia</DialogTitle>
                <DialogContent>
                    <DialogContentText>
                        Si cambias de vista, todos tus cambios se perderán. ¿Estás seguro de que deseas continuar?
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>Cancelar</Button>
                    <Button onClick={() => {
                        navigate('/productos/consultar');
                    }} autoFocus>
                        Continuar
                    </Button>
                </DialogActions>
            </Dialog>

        </div>
    );
};

export default DialogWarning;