import React, { useEffect, useState } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogContentText,
    DialogActions,
    Button,
    TextField,
    IconButton,
    Chip,
    Slide,
    Typography,
    Card,
    CardContent,
    InputAdornment,
    CircularProgress,
    Alert
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import { boxSizing, styled } from '@mui/system';
import { set } from 'date-fns';
import { AccountCircle } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { obtenerAllVariacionesFiltradas, obtenerVariacionProductos } from '../../../redux/productosDucks';
import SendIcon from "@mui/icons-material/Send";
import CustomDialog from '../../componentesGenerales/CustomDialog';

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
});

const AnimatedCard = styled(Card)(({ theme, selected }) => ({
    backgroundColor: selected ? theme.palette.background.paper : 'transparent',
    boxShadow: selected ? theme.shadows[4] : 'none',
    cursor: 'pointer',
    marginBottom: '1em',
    transition: 'background-color 0.3s ease, box-shadow 0.3s ease',
    boxSizing: "border-box",
    borderColor: selected ? theme.palette.primary.main : 'transparent',
    border: 'none',
    '&:hover': {
        backgroundColor: selected ? theme.palette.background.paper : theme.palette.action.hover,
        boxShadow: selected ? theme.shadows[4] : theme.shadows[1],
    },
}));

const DialogAddVariations = ({ setDialogAddVariations, dialogAddVariations, variations, addNewVariationName, memoizedVariacionesFiltradas, addNewVariationAttribute, selectedCardIndex, setSelectedCardIndex, title, setTitle, attribute, setAttribute, loader, setLoader }) => {
    //     const [title, setTitle] = useState('');
    //     const [attribute, setAttribute] = useState('');
    const [attributes, setAttributes] = useState([]);
    // const [selectedCardIndex, setSelectedCardIndex] = useState(null);


    const handleClose = () => {
        setDialogAddVariations(false);
    };

    const handleAddAttribute = () => {
        if (attribute) {
            setAttributes([...attributes, attribute]);
            setAttribute('');
        }
    };

    const handleRemoveAttribute = (attr) => {
        setAttributes(attributes.filter(a => a !== attr));
    };

    const [selectedAttribute, setSelectedAttribute] = useState([]);
    const selectedCarRef = React.useRef(null);

    const handleCardClick = (index, id) => {
        setSelectedCardIndex(id);
        selectedCarRef.current = id;
        setSelectedAttribute(memoizedVariacionesFiltradas.find(v => v.customVariationId === id)?.customVariationValues ? memoizedVariacionesFiltradas.find(v => v.customVariationId === id)?.customVariationValues : []);
        let data = memoizedVariacionesFiltradas.find(v => v.customVariationId === id)
    };


    useEffect(() => {
        if (selectedCardIndex !== null) {
            setSelectedAttribute(memoizedVariacionesFiltradas.find(v => v.customVariationId === selectedCardIndex)?.customVariationValues ? memoizedVariacionesFiltradas.find(v => v.customVariationId === selectedCardIndex)?.customVariationValues : []);
        }
    }, [selectedCardIndex]);




    // const [loader, setLoader] = useState({ title: false, attribute: false });

    const handleKeyDown = (e, type) => {
        if (e.key === 'Enter' && title) {

            if (type === "click") e.preventDefault();
            setLoader({ title: true, attribute: false });
            addNewVariationName(title);
            // setVariations([...variations, { customVariationName: title }]);
            // setTitle('');
        }
    };

    const handleKeyDownAttribute = (e, type) => {
        if (e.key === 'Enter' && attribute) {
            if (type === "click") e.preventDefault();
            setLoader({ title: false, attribute: true });
            addNewVariationAttribute(selectedCardIndex, attribute);
            // setTitle('');
            // console.log(memoizedVariacionesFiltradas, "SADASDSA2222222")
        }
    };

    const dispatch = useDispatch();

    const exitAddCustomVariation = useSelector((store) => store.productos.exitAddCustomVariation);
    const existAddCustomVariationAttribute = useSelector((store) => store.productos.existAddCustomVariationAttribute);
    const [message, setMessage] = useState({ message: "", severity: "" });

    useEffect(() => {

        if (exitAddCustomVariation === 201) {
            // variationsFiltradas.push({
            //   customVariationName: name,
            //   customVariationId: Date.now() + Math.random().toString(36).substr(2, 9),
            //   customVariationValues: []
            // });
            dispatch(obtenerAllVariacionesFiltradas(""));
            setTitle('');
            setLoader({ title: false, attribute: false });
            setMessage({ message: "Titulo agregada correctamente", severity: "success" });
            dispatch(obtenerVariacionProductos())

            return
        } else {
            if (exitAddCustomVariation) {
                setMessage({ message: "Titulo no agregada correctamente", severity: "error" });
                setLoader({ title: false, attribute: false })
            }
        }
        if (existAddCustomVariationAttribute === 201) {
            setSelectedAttribute([...selectedAttribute, { customVariationValue: attribute, customVariationValueId: Math.random() * 100 }]);
            setAttribute('');
            setLoader({ title: false, attribute: false });
            dispatch(obtenerAllVariacionesFiltradas(""));
            setMessage({ message: "Atributo agregada correctamente", severity: "success" });
            return
        } else {
            if (existAddCustomVariationAttribute) {

                setMessage({ message: "Error al agregar Attributo", severity: "error" });
                setLoader({ title: false, attribute: false });
            }

        }

    }, [exitAddCustomVariation, existAddCustomVariationAttribute])


    const [visible, setVisible] = useState(!!message.message);

    useEffect(() => {
        if (message.message !== "") {
            setVisible(true);
            const timer = setTimeout(() => {
                setVisible(false);
                // Aquí puedes agregar cualquier otra lógica que necesites cuando se cierra el mensaje, como dispatch(cleanMessage());
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [message]);

    return (
        <div style={{ maxWidth: "675px" }}>
            {visible && (
                <Alert
                    sx={{ position: "absolute", bottom: "3rem", left: "2rem", maxWidth: "40%", zIndex: "9999" }}
                    onClose={() => {
                        setMessage({ message: "", severity: "" });
                    }}
                    severity={message.severity}
                >
                    {message.message}
                </Alert>)}


            <CustomDialog
                open={dialogAddVariations}
                onClose={handleClose}
                title="Agrega una Variación"
                dialogStyles={{
                    borderRadius: "16px !important",
                }}
                maxHeight='60vh'
                width='560px'
                maxWidth = "800px"
                actions = {
                    <>
                     <Button color='buttonGreenPink' onClick={handleClose}>Cancelar</Button>
                    <Button color='buttonGreenPink' onClick={() => {
                        // Aquí puedes manejar el envío de los datos
                        handleClose();
                    }} autoFocus>
                        Listo
                    </Button></>}
            >
                <DialogContent sx={{ minHeight: "265px" }}>
                    <DialogContentText>
                        Por favor ingrese un título para la variación y añada los atributos correspondientes.
                    </DialogContentText>
                    <div style={{ display: 'flex', gap: '1em' }}>
                        <div style={{ flex: 1, marginTop: "7px" }}>
                            <div style={{ flex: 1 }}>
                                {memoizedVariacionesFiltradas?.map((variation, index) => (
                                    <AnimatedCard
                                        key={index}
                                        variant="outlined"
                                        selected={selectedCardIndex === variation.customVariationId}
                                        onClick={() => handleCardClick(index, variation.customVariationId)}
                                    >
                                        <CardContent  sx={{ padding: "12px", paddingBottom: "12px !important" }}>
                                            <Typography sx={{ padding: "0" }} variant="h6">
                                                {variation.customVariationName}
                                            </Typography>
                                        </CardContent>
                                    </AnimatedCard>
                                ))}
                            </div>
                            <TextField
                                focused
                                label="Agregar Variación"
                                fullWidth
                                sx={{
                                    position: "sticky",
                                    bottom: "-21px",
                                    paddingBottom: "1em", top: "0"
                                }}
                                color='buttonGreenPink'
                                value={title}   
                                onChange={(e) => setTitle(e.target.value)}
                                onKeyDown={(e) => handleKeyDown(e, "key")}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            {loader.title && <CircularProgress size={23} />}
                                            <IconButton
                                                aria-label="enviar"
                                                onClick={(e) => handleKeyDown({ key: "Enter", type: "click" })}
                                                edge="end"
                                                disabled={loader.title}
                                            >
                                                <SendIcon />
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                            />
                            {/* <IconButton onClick={handleAddAttribute} color="primary">
                                <AddIcon />
                            </IconButton> */}
                        </div>
                        <div style={{ flex: 1 }}>
                            {selectedCardIndex === null && (
                                <div style={{ padding: "1em", border: "1px solid #cccccc63", borderRadius: "4px", marginBottom: "14px", display: "flex", alignItems: "center", }}>
                                    <p style={{ textAlign: "center" }}>Selecciona una variacion para ver sus atributos</p>
                                </div>)
                            }

                            <div style={{ display: "flex", flexDirection: "column", gap: "1.12em" }}>
                                {selectedAttribute.map((attr, index) => (
                                    <TextField
                                        autoFocus
                                        label="Atributo"
                                        color='buttonGreenPink'
                                        fullWidth
                                        value={attr.customVariationValue}
                                        disabled
                                        sx={{ marginBottom: "8px", height: "40px" }}
                                    />
                                ))}
                            </div>
                            <TextField
                                focused
                                margin="dense"
                                label="Agregar Atributo"
                                fullWidth
                                sx={{
                                    position: "sticky",
                                    bottom: "-21px",
                                    top: "0",
                                    background: "transparent",
                                    paddingBottom: "1em", zIndex: "10",
                                    marginTop: "26px"
                                }}
                                color='buttonGreenPink'
                                disabled={selectedCardIndex === null}
                                value={attribute}
                                onChange={(e) => setAttribute(e.target.value)}
                                onKeyDown={handleKeyDownAttribute}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            {loader.attribute && <CircularProgress size={23} />}
                                            <IconButton
                                                aria-label="enviar"
                                                onClick={(e) => handleKeyDownAttribute({ key: "Enter", type: "click" })}
                                                edge="end"
                                                color='buttonGreenPink'
                                                disabled={loader.attribute}
                                            >
                                                <SendIcon />
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                            />
                        </div>
                    </div>
                </DialogContent>

            </CustomDialog>

        </div>
    );
};

export default DialogAddVariations;
