import React from "react";
import { styled } from "@mui/material/styles";
import ButtonBase from "@mui/material/ButtonBase";
import DeleteIcon from "@mui/icons-material/Delete";
import { ImagesFormContext } from "../../../context/ImagesFormProvider";
import anonimoLogo from "../../img/anonimo.jpg";
import { ImagesVariationsFormContext } from "../../../context/ImagesVariationsFormProvider";
const ImageButton = styled(ButtonBase)(({ theme }) => ({
  position: "relative",
  height: 140,
  width: 140,
  [theme.breakpoints.down("sm")]: {
    width: "100% !important", // Overrides inline-style
    height: 140,
  },
  "&:hover, &.Mui-focusVisible": {
    zIndex: 1,
    "& .MuiImageBackdrop-root": {
      opacity: 0.35,
    },
    "& .iconBorrar": {
      opacity: 1,
    },
  },
  "&:not(:hover)": {
    "& .iconBorrar": {
      opacity: 0,
    },
  },
}));

const ImageSrc = styled("span")({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundSize: "cover",
  backgroundPosition: "center",
});

const ImageStyled = styled("span")(({ theme }) => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  color: theme.palette.common.white,
}));

const ImageBackdrop = styled("span")(({ theme }) => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: theme.palette.common.black,
  opacity: 0.0,
  transition: theme.transitions.create("opacity"),
}));

//aca al agregar nuevo producto se agrega la imagen a la lista de imagenes
const borrarImagen = (setImageListVariations, imageListVariations, idItem) => {
  setImageListVariations(
    imageListVariations.filter((item) => {
      return item.key !== idItem;
    })
  );
};

const ImageVariationsUpdaterItem = (props) => {
  const { imageListVariations, setImageListVariations } = React.useContext(
    ImagesVariationsFormContext
  );

  return (
    <div
      style={{
        flexBasis: "140px",
        flexFlow: 0,
        flexShrink: 1,
        margin: "auto",
        padding: "3px 0",
      }}
    >
      <ImageButton
        focusRipple
        key={props.title}
        style={{
          width: "120px",
          height: "120px",
          boxShadow: "0px 0px 4px 1px #003876",
          borderRadius: ".5rem",
        }}
        onClick={() =>{
          borrarImagen(setImageListVariations, imageListVariations, props.title)
        }
        }
      >
        <ImageSrc
          style={{
            backgroundImage: `url(${props.url})`,
            borderRadius: ".5rem",
          }}
        />
        <ImageBackdrop
          className="MuiImageBackdrop-root"
          style={{ borderRadius: ".5rem" }}
        />
        <ImageStyled>
          <DeleteIcon fontSize="large" className="iconBorrar" />
        </ImageStyled>
      </ImageButton>
    </div>
  );
};

export default ImageVariationsUpdaterItem;
