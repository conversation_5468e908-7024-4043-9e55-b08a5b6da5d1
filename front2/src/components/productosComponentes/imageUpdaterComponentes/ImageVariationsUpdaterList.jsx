import React, { useEffect, useState } from "react";
import ImageUpdaterTwoOptionButton from "./ImageUpdaterTwoOptionsButton";
import { ImagesVariationsFormContext } from "../../../context/ImagesVariationsFormProvider";
import { NewProductVariationsContext } from "../../../context/NewProductVariationsProvider";
import ImageVariationsUpdaterItem from "./ImageVariationsUpdaterItem";
import { useDispatch, useSelector } from "react-redux";
import { persistirDatosNuevoProducto } from "../../../redux/productosDucks";
import { Container, Draggable } from "react-smooth-dnd";
import { ListImgDraggeable } from "./ListImgDraggeable";

const ImageVariationsUpdaterList = ({ counter, product, isVariation }) => {
  const { imageListVariations, setImageListVariations, photosInitiales } =
    React.useContext(ImagesVariationsFormContext);
  const photos = product.imageListVariations;

  const { containerImagesVariations, setContainerImagesVariations } =
    React.useContext(NewProductVariationsContext);

  const productPreserves = useSelector(
    (state) => state.productos.persevereDataNewProduct
  );
  const dispatch = useDispatch();

  console.log("ImageVariationsUpdaterList", imageListVariations);

  useEffect(() => {
    setImagesInContext();
  }, [imageListVariations]);

  const setImagesInContext = () => {
    let clave = `index${counter}`;
    let newState = { ...containerImagesVariations };
    newState[clave] = imageListVariations;
    setContainerImagesVariations(newState);
    // al iniicio de la app, si no hay files en el estado
    // este no hara dispatch ya que hara dispatch de [ ] y se hay
    // datos en redux se sobreescribiran
    if (
      newState[clave]?.length > 0 &&
      productPreserves?.variations.length > 0
    ) {
      dispatch(persistirDatosNuevoProducto({ key: "imgs", value: newState }));
    }
  };

  useEffect(() => {
    if (Object.keys(productPreserves.imgs).length > 0) {
      let clave = `index${counter}`;
      // si no existe esa propiedad retorna
      if (!productPreserves.imgs[clave]) return;
      setImageListVariations(productPreserves.imgs[clave]);
    }
  }, []);
  

  return (
    <ListImgDraggeable imageList={imageListVariations} setImage={setImageListVariations}  isVariation={isVariation} />
  );
};

export default ImageVariationsUpdaterList;
