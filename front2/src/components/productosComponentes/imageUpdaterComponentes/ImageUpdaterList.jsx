import React, { useState } from "react";
import ImageUpdaterTwoOptionButton from "./ImageUpdaterTwoOptionsButton";
import { ImagesFormContext } from "../../../context/ImagesFormProvider";
import { ImagesVariationsFormContext } from "../../../context/ImagesVariationsFormProvider";
import { ListImgDraggeable } from "./ListImgDraggeable";

const ImageUpdaterList = ({ type }) => {
  const { imageList, setImageList } = React.useContext(ImagesFormContext);
  
  return (
    <ListImgDraggeable imageList={imageList} setImage={setImageList}  />
  );

};

export default ImageUpdaterList;
