import React from "react";
import { Container, Draggable } from "react-smooth-dnd";
import ImageUpdaterTwoOptionButton from "./ImageUpdaterTwoOptionsButton";

export const ListImgDraggeable = ({
  imageList,
  setImage,
  isVariation,
}) => {
  const [containerWidth, setContainerWidth] = React.useState(
    window.innerWidth - 140 - 60
  );

  React.useEffect(() => {
    const handleResize = () => {
      setContainerWidth(window.innerWidth - 140 - 60);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const onDrop = (dropResult) => {
    const { removedIndex, addedIndex, payload } = dropResult;

    if (removedIndex !== null && addedIndex !== null) {
      // Reordenar la lista en base al resultado de la caída
      const reorderedList = [...imageList];
      const [movedItem] = reorderedList.splice(removedIndex, 1);
      reorderedList.splice(addedIndex, 0, movedItem);
      setImage(reorderedList);
    }
  };

  return (
    <React.Fragment>
      <section
        style={{
          display: "flex",
          gap: "10px",
          height: "auto",
          width: "100%",
          overflow: "hidden", // Evita desbordamiento
        }}
      >
        <ImageUpdaterTwoOptionButton
          imageList={imageList}
          setImageList={setImage}
          isVariation={isVariation}
        />
        <div style={{ 
          width: `${containerWidth}px`, 
          maxWidth: "calc(100% - 150px)",
          overflow: "hidden",
          position: "relative"
        }}>
          <Container
            orientation="horizontal"
            onDrop={onDrop}
            style={{
              display: "flex",
              gap: "10px",
              padding: "10px",
              overflowX: "auto",
              width: "100%",
              height: "100%",
              flexShrink: 0,
              margin: 0,
            }}
          >
            {imageList.map((image, index) => (
              <Draggable key={index}>
                <div
                  style={{
                    flexShrink: 0,
                    width: window.innerWidth < 600 ? "80px" : "120px",
                    height: window.innerWidth < 600 ? "80px" : "120px",
                    marginRight: "10px",
                  }}
                >
                  {React.cloneElement(image, {
                    style: { 
                      width: "100%", 
                      height: "100%", 
                      objectFit: "cover",
                      borderRadius: "4px" 
                    },
                  })}
                </div>
              </Draggable>
            ))}
          </Container>
        </div>
      </section>
    </React.Fragment>
  );
};
