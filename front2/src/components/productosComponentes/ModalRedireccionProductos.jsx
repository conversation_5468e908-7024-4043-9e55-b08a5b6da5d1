import React from "react";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import { Button } from "@mui/material";
import TaskIcon from "@mui/icons-material/Task";
import { useSelector } from "react-redux";

const ModalRedireccionProductos = ({ mensaje, product }) => {
  const newProductOrigin = useSelector((store) => store.productos.newProductOrigin);
  return (
    <>
      <DialogTitle>
        {" "}
        Operación exitosa <TaskIcon
          fontSize="large"
          sx={{ color: "green" }}
        />{" "}
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          {" "}
          {mensaje}, a continuación elige si quieres {product ? "volver a editar el producto" : "seguir agregando productos"} o ir a consultarlos
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          type="button"
          color="buttonGreen"
          onClick={() => {
            window.location.reload(false);
          }}
        >
          {product ? "Volver a editar" : "Agregar otro"}
        </Button>

        <Button
          color="buttonGreen"
          type="button"
          onClick={() => {
            newProductOrigin
              ? window.location.href = `facturas/consultar` :
            window.location.href = "productos/consultar";
          }}
        >
          Consultar
        </Button>
      </DialogActions>
    </>
  );
};

export default ModalRedireccionProductos;
