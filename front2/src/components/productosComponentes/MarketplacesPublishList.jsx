import {
  Avatar,
  Badge,
  Box,
  Button,
  Chip,
  Table,
  TableBody,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  useAutocomplete,
} from "@mui/material";
import React from "react";
import { StyledTableCell, StyledTableRow } from "../StyledTableComponents";
import { useSelector } from "react-redux";
import { useTheme } from "@mui/material/styles";
import { useMediaQuery } from "@mui/material";
import { is } from "date-fns/locale";

const MarketplacesPublishList = (props) => {
  const { marketplaces, setMarketplaceToPublish, setAnchorEl, producto, full } =
    props;
  const marketplaceLogos = useSelector(
    (store) => store.productos.marketplaceLogos
  );

  const themeBreak = useTheme();
  const theme = useTheme();

  // abajo de 700
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));

  // abajo de 1900
  const isDownFullScreen = useMediaQuery(theme.breakpoints.down("fs"));

  // abajo de 1500
  const isDown1500Screen = useMediaQuery(theme.breakpoints.down("lg1500"));

  // abajo de 1700
  const isDown1700Screen = useMediaQuery(theme.breakpoints.down("xl1700"));

  // abajo de 1800
  const isDown1800Screen = useMediaQuery(theme.breakpoints.down("xl1800"));

  console.log("marketplaces", marketplaces, marketplaceLogos);

  const quantity = marketplaces?.length;

  return (
    <Box sx={{ maxHeight: "170px", overflowY: "auto", overflowX: "hidden" }}>
      <Table>
        <TableBody>
          {full ? (
            <StyledTableRow sx={{ display: "flex", flexDirection: "row" }}>
              {marketplaces?.map((marketplace) => (
                <StyledTableCell>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                    }}
                  >
                    <Badge
                      badgeContent={9}
                      color="success"
                      overlap="circular"
                      anchorOrigin={{
                        vertical: "top",
                        horizontal: "right",
                      }}
                    >
                      <Avatar
                        src={
                          marketplaceLogos?.find(
                            (logo) =>
                              logo.supportedMarketplaceId === marketplace.id
                          )?.imagen
                        }
                        onClick={(e) => {
                          setAnchorEl(e.currentTarget);
                        }}
                      />
                    </Badge>
                    <Tooltip
                      title="Margen de rentabilidad"
                      sx={{ margin: "5px 0 5px 0" }}
                    >
                      <Chip label="15%" />
                    </Tooltip>
                    <Tooltip title="Precio publicado">
                      <Chip label="$ 1000" color="primary" />
                    </Tooltip>
                  </Box>
                </StyledTableCell>
              ))}
            </StyledTableRow>
          ) : isSmallScreen ? (
            <StyledTableRow>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "repeat(3, 1fr)",
                  gap: "10px",
                  width: "100%",
                }}
              >
                {marketplaces?.map((marketplace) => (
                  <StyledTableCell>
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                      }}
                    >
                      <Badge
                        badgeContent={9}
                        color="success"
                        overlap="circular"
                        anchorOrigin={{
                          vertical: "top",
                          horizontal: "right",
                        }}
                      >
                        <Avatar
                          src={
                            marketplaceLogos?.find(
                              (logo) =>
                                logo.supportedMarketplaceId === marketplace.id
                            )?.imagen
                          }
                          onClick={(e) => {
                            setAnchorEl(e.currentTarget);
                          }}
                        />
                      </Badge>
                      <Tooltip
                        title="Margen de rentabilidad"
                        sx={{ margin: "5px 0 5px 0" }}
                      >
                        <Chip label="15%" />
                      </Tooltip>
                      <Tooltip title="Precio publicado">
                        <Chip label="$ 1000" color="primary" />
                      </Tooltip>
                    </Box>
                  </StyledTableCell>
                ))}
              </Box>
            </StyledTableRow>
          ) : (
            <StyledTableRow sx={{ display: "flex", flexDirection: "row" }}>
              {isDown1500Screen
                ? quantity > 2
                  ? marketplaces?.slice(0, 2).map((marketplace) => (
                      <StyledTableCell>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                          }}
                        >
                          <Badge
                            badgeContent={9}
                            color="success"
                            overlap="circular"
                            anchorOrigin={{
                              vertical: "top",
                              horizontal: "right",
                            }}
                          >
                            <Avatar
                              src={
                                marketplaceLogos?.find(
                                  (logo) =>
                                    logo.supportedMarketplaceId ===
                                    marketplace.id
                                )?.imagen
                              }
                              onClick={(e) => {
                                setAnchorEl(e.currentTarget);
                              }}
                            />
                          </Badge>
                          <Tooltip
                            title="Margen de rentabilidad"
                            sx={{ margin: "5px 0 5px 0" }}
                          >
                            <Chip label="15%" />
                          </Tooltip>
                          <Tooltip title="Precio publicado">
                            <Chip label="$ 1000" color="primary" />
                          </Tooltip>
                        </Box>
                      </StyledTableCell>
                    ))
                  : marketplaces?.map((marketplace) => (
                      <StyledTableCell>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                          }}
                        >
                          <Badge
                            badgeContent={9}
                            color="success"
                            overlap="circular"
                            anchorOrigin={{
                              vertical: "top",
                              horizontal: "right",
                            }}
                          >
                            <Avatar
                              src={
                                marketplaceLogos?.find(
                                  (logo) =>
                                    logo.supportedMarketplaceId ===
                                    marketplace.id
                                )?.imagen
                              }
                              onClick={(e) => {
                                setAnchorEl(e.currentTarget);
                              }}
                            />
                          </Badge>
                          <Tooltip
                            title="Margen de rentabilidad"
                            sx={{ margin: "5px 0 5px 0" }}
                          >
                            <Chip label="15%" />
                          </Tooltip>
                          <Tooltip title="Precio publicado">
                            <Chip label="$ 1000" color="primary" />
                          </Tooltip>
                        </Box>
                      </StyledTableCell>
                    ))
                : isDown1700Screen
                  ? quantity > 3
                    ? marketplaces?.slice(0, 3).map((marketplace) => (
                        <StyledTableCell>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "center",
                            }}
                          >
                            <Badge
                              badgeContent={9}
                              color="success"
                              overlap="circular"
                              anchorOrigin={{
                                vertical: "top",
                                horizontal: "right",
                              }}
                            >
                              <Avatar
                                src={
                                  marketplaceLogos?.find(
                                    (logo) =>
                                      logo.supportedMarketplaceId ===
                                      marketplace.id
                                  )?.imagen
                                }
                                onClick={(e) => {
                                  setAnchorEl(e.currentTarget);
                                }}
                              />
                            </Badge>
                            <Tooltip
                              title="Margen de rentabilidad"
                              sx={{ margin: "5px 0 5px 0" }}
                            >
                              <Chip label="15%" />
                            </Tooltip>
                            <Tooltip title="Precio publicado">
                              <Chip label="$ 1000" color="primary" />
                            </Tooltip>
                          </Box>
                        </StyledTableCell>
                      ))
                    : marketplaces?.map((marketplace) => (
                        <StyledTableCell>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "center",
                            }}
                          >
                            <Badge
                              badgeContent={9}
                              color="success"
                              overlap="circular"
                              anchorOrigin={{
                                vertical: "top",
                                horizontal: "right",
                              }}
                            >
                              <Avatar
                                src={
                                  marketplaceLogos?.find(
                                    (logo) =>
                                      logo.supportedMarketplaceId ===
                                      marketplace.id
                                  )?.imagen
                                }
                                onClick={(e) => {
                                  setAnchorEl(e.currentTarget);
                                }}
                              />
                            </Badge>
                            <Tooltip
                              title="Margen de rentabilidad"
                              sx={{ margin: "5px 0 5px 0" }}
                            >
                              <Chip label="15%" />
                            </Tooltip>
                            <Tooltip title="Precio publicado">
                              <Chip label="$ 1000" color="primary" />
                            </Tooltip>
                          </Box>
                        </StyledTableCell>
                      ))
                  : isDown1800Screen
                    ? quantity > 4
                      ? marketplaces?.slice(0, 4).map((marketplace) => (
                          <StyledTableCell>
                            <Box
                              sx={{
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                              }}
                            >
                              <Badge
                                badgeContent={9}
                                color="success"
                                overlap="circular"
                                anchorOrigin={{
                                  vertical: "top",
                                  horizontal: "right",
                                }}
                              >
                                <Avatar
                                  src={
                                    marketplaceLogos?.find(
                                      (logo) =>
                                        logo.supportedMarketplaceId ===
                                        marketplace.id
                                    )?.imagen
                                  }
                                  onClick={(e) => {
                                    setAnchorEl(e.currentTarget);
                                  }}
                                />
                              </Badge>
                              <Tooltip
                                title="Margen de rentabilidad"
                                sx={{ margin: "5px 0 5px 0" }}
                              >
                                <Chip label="15%" />
                              </Tooltip>
                              <Tooltip title="Precio publicado">
                                <Chip label="$ 1000" color="primary" />
                              </Tooltip>
                            </Box>
                          </StyledTableCell>
                        ))
                      : marketplaces?.map((marketplace) => (
                          <StyledTableCell>
                            <Box
                              sx={{
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                              }}
                            >
                              <Badge
                                badgeContent={9}
                                color="success"
                                overlap="circular"
                                anchorOrigin={{
                                  vertical: "top",
                                  horizontal: "right",
                                }}
                              >
                                <Avatar
                                  src={
                                    marketplaceLogos?.find(
                                      (logo) =>
                                        logo.supportedMarketplaceId ===
                                        marketplace.id
                                    )?.imagen
                                  }
                                  onClick={(e) => {
                                    setAnchorEl(e.currentTarget);
                                  }}
                                />
                              </Badge>
                              <Tooltip
                                title="Margen de rentabilidad"
                                sx={{ margin: "5px 0 5px 0" }}
                              >
                                <Chip label="15%" />
                              </Tooltip>
                              <Tooltip title="Precio publicado">
                                <Chip label="$ 1000" color="primary" />
                              </Tooltip>
                            </Box>
                          </StyledTableCell>
                        ))
                    : isDownFullScreen
                      ? quantity > 5
                        ? marketplaces?.slice(0, 5).map((marketplace) => (
                            <StyledTableCell>
                              <Box
                                sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                  alignItems: "center",
                                }}
                              >
                                <Badge
                                  badgeContent={9}
                                  color="success"
                                  overlap="circular"
                                  anchorOrigin={{
                                    vertical: "top",
                                    horizontal: "right",
                                  }}
                                >
                                  <Avatar
                                    src={
                                      marketplaceLogos?.find(
                                        (logo) =>
                                          logo.supportedMarketplaceId ===
                                          marketplace.id
                                      )?.imagen
                                    }
                                    onClick={(e) => {
                                      setAnchorEl(e.currentTarget);
                                    }}
                                  />
                                </Badge>
                                <Tooltip
                                  title="Margen de rentabilidad"
                                  sx={{ margin: "5px 0 5px 0" }}
                                >
                                  <Chip label="15%" />
                                </Tooltip>
                                <Tooltip title="Precio publicado">
                                  <Chip label="$ 1000" color="primary" />
                                </Tooltip>
                              </Box>
                            </StyledTableCell>
                          ))
                        : marketplaces?.map((marketplace) => (
                            <StyledTableCell>
                              <Box
                                sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                  alignItems: "center",
                                }}
                              >
                                <Badge
                                  badgeContent={9}
                                  color="success"
                                  overlap="circular"
                                  anchorOrigin={{
                                    vertical: "top",
                                    horizontal: "right",
                                  }}
                                >
                                  <Avatar
                                    src={
                                      marketplaceLogos?.find(
                                        (logo) =>
                                          logo.supportedMarketplaceId ===
                                          marketplace.id
                                      )?.imagen
                                    }
                                    onClick={(e) => {
                                      setAnchorEl(e.currentTarget);
                                    }}
                                  />
                                </Badge>
                                <Tooltip
                                  title="Margen de rentabilidad"
                                  sx={{ margin: "5px 0 5px 0" }}
                                >
                                  <Chip label="15%" />
                                </Tooltip>
                                <Tooltip title="Precio publicado">
                                  <Chip label="$ 1000" color="primary" />
                                </Tooltip>
                              </Box>
                            </StyledTableCell>
                          ))
                      : marketplaces?.map((marketplace) => (
                          <StyledTableCell>
                            <Box
                              sx={{
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                              }}
                            >
                              <Badge
                                badgeContent={9}
                                color="success"
                                overlap="circular"
                                anchorOrigin={{
                                  vertical: "top",
                                  horizontal: "right",
                                }}
                              >
                                <Avatar
                                  src={
                                    marketplaceLogos?.find(
                                      (logo) =>
                                        logo.supportedMarketplaceId ===
                                        marketplace.id
                                    )?.imagen
                                  }
                                  onClick={(e) => {
                                    setAnchorEl(e.currentTarget);
                                  }}
                                />
                              </Badge>
                              <Tooltip
                                title="Margen de rentabilidad"
                                sx={{ margin: "5px 0 5px 0" }}
                              >
                                <Chip label="15%" />
                              </Tooltip>
                              <Tooltip title="Precio publicado">
                                <Chip label="$ 1000" color="primary" />
                              </Tooltip>
                            </Box>
                          </StyledTableCell>
                        ))}
            </StyledTableRow>
          )}
        </TableBody>
      </Table>
    </Box>
  );
};

export default MarketplacesPublishList;
