import React, { Fragment } from "react";
import { Routes, Route } from "react-router-dom";

import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import ConsultarProductos from "./ConsultarProductos";
import FormularioNuevoProducto from "./FormularioNuevoProducto";
import ProductoWrapper from "./ProductoWrapper";
import CargaMasivaPrincipal from "./CargaMasivaPrincipal";
import NewProductVariationsProvider from "../../context/NewProductVariationsProvider";
import { useEffect } from "react";
import AdministrarKits from "./kitsComponentes/AdministrarKits";
import ConsultarKits from "./kitsComponentes/ConsultarKits";
import EditarKit from "./kitsComponentes/EditarKit";
import { TitleModule } from "../componentesGenerales/TitleModule";
const Item = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  textAlign: "center",
}));

const FacturasMenu = () => {
  return (
    <Fragment>
      <TitleModule title="Módulo de productos" />
      <Routes>
        <Route
          path="/nuevo"
          element={
            <NewProductVariationsProvider>
              {/* <FormularioNuevoProducto producto={myProduct2} /> */}
              <FormularioNuevoProducto />
            </NewProductVariationsProvider>
          }
        />
        <Route path="/consultar" element={<ConsultarProductos />} />
        <Route path="/editar/:skuInterno" element={<ProductoWrapper />} />
        <Route path="/kit" element={<AdministrarKits />} />
        <Route path="/editarKit/:id" element={<EditarKit />} />
        <Route path="/consultarKits" element={<ConsultarKits />} />
        <Route path="/cargaMasiva" element={<CargaMasivaPrincipal />} />
      </Routes>
    </Fragment>
  );
};

export default FacturasMenu;
