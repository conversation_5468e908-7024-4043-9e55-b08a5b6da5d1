import React from "react";
import Pagination from "@mui/material/Pagination";
import Stack from "@mui/material/Stack";
import Box from "@mui/material/Box";
import { useDispatch, useSelector } from "react-redux";
import Grid from "@mui/material/Grid";
import Fab from "@mui/material/Fab";
import { useSearchParams } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import SearchField from "../SearchField";
import Button from "@mui/material/Button";
import DoneOutlineIcon from "@mui/icons-material/DoneOutline";
import TablaDesplegableProductosWrapper from "./TablaDesplegableProductosWrapper";
import { obtenerTotalProductos } from "../../redux/productosDucks";
import { ManejarErrores } from "../ManejarErrores";
import { SkeletonTables } from "../componentesGenerales/SkeletonTables";

const ConsultarProductos = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const page = searchParams.get("page") === null ? 1 : searchParams.get("page");
  const searchText =
    searchParams.get("search") === null ? "" : searchParams.get("search");

  const esperaTortuga = useSelector((store) => store.productos.esperaTortuga);
  const cantidadDePaginas = useSelector(
    (store) => store.productos.cantidadDePaginas
  );
  const cantidadDeProductosPorPagina = useSelector(
    (store) => store.productos.nextC
  );
  const [showingFilters, setShowingFilters] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const [mensajeModal, setMensajeModal] = React.useState("");
  /*const [allSelected, setAllSelected] = React.useState(false);*/

  const offset = cantidadDeProductosPorPagina * (page - 1);
  const goNew = () => {
    navigate("../nuevo");
  };

  const cambioDePagina = (evento, valor) => {
    dispatch(obtenerTotalProductos({ search: searchText }));
    navigate(`?page=${valor}&search=${searchText}`);
  };

  const ItemCenter = styled(Paper)(() => ({
    textAlign: "center",
    boxShadow: "none",
  }));

  const ItemRight = styled(Paper)(() => ({
    textAlign: "right",
    boxShadow: "none",
  }));

  const enviarSearch = (event) => {
    event.preventDefault();
    var nuevoSearch = event.target[1].value;

    dispatch(obtenerTotalProductos({ search: nuevoSearch }));
    navigate(`?page=${1}&search=${nuevoSearch}`);
  };

  const handleShowingFilters = () => {
    setShowingFilters(!showingFilters);
  };

  // const manejarSeleccionarTodosProductos = () => {
  //   setAllSelected(true)
  // };

  const FiltersGrid = () => {
    return (
      <Box sx={{ flexGrow: 1, mb: 1 }}>
        <Grid container spacing={1}
        sx={{
          justifyContent: "end",
          alignItems: "center",
          paddingX: 5,
          paddingY: 2,
        }}
        >
          {/* <Grid item xs={1}>
            <ItemCenter>
              <Fab
                size="medium"
                color="buttonGreen"
                aria-label="add"
                style={{ marginBottom: "5px" }}
                onClick={() => handleShowingFilters()}
              >
                <DoneOutlineIcon onClick={manejarSeleccionarTodosProductos} />
              </Fab>
            </ItemCenter>
          </Grid>
          <Grid item xs={5}>
            <SearchField enviarSearch={enviarSearch} searchText={searchText} />
          </Grid> */}
          {/* <Grid item xs={3}></Grid> */}
          <Grid
            item
            xs={3}
            sx={{
              display: "flex",
              justifyContent: "end",
              alignItems: "center",
            }}
          >
            <Button
              variant="outlined"
              color="buttonGreenPink"
              onClick={goNew}
              sx={{ maxHeight: "70%" }}
            >
              Publicar varios
            </Button>
          </Grid>
          <Grid item xs={1} sx={{ display: "flex", alignItems: "center" }}>
            <Button
              variant="contained"
              color="buttonGreenPink"
              onClick={goNew}
              sx={{ maxHeight: "70%" }}
            >
              Nuevo
            </Button>
          </Grid>
        </Grid>
      </Box>
    );
  };

  /*UseEffect */
  React.useEffect(() => {
    const fetchData = () => {
      dispatch(obtenerTotalProductos({ search: searchText }));
    };
    fetchData();
  }, []);

  /*Manejar el return */
  if (cantidadDePaginas !== null) {
    if (typeof cantidadDePaginas === "string") {
      return <ManejarErrores errorCode={cantidadDePaginas} />;
    } else {
      return (
        <React.Fragment>
          {/*filtros*/}
          {/*row filtros */}
          <FiltersGrid />
          {/*row filtros */}
          {/*filtros*/}
          {cantidadDePaginas === 0 ? (
            <div>No hay productos que cumplan con estas especificiaciones</div>
          ) : (
            <>
              <div style={{ justifyContent: "center", display: "flex" }}>
                <Box
                  sx={{
                    display: "flex",
                    width: "90%",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  {/*<div>ositos</div>*/}
                  <TablaDesplegableProductosWrapper
                    offset={offset}
                    search={searchText}
                  />
                </Box>
              </div>
              <div style={{ justifyContent: "center", display: "flex" }}>
                <Stack spacing={2}>
                  <Pagination
                    count={cantidadDePaginas}
                    page={parseInt(page)}
                    onChange={cambioDePagina}
                  />
                </Stack>
              </div>
            </>
          )}
        </React.Fragment>
      );
    }
  } 
  // else {
  //   return <SkeletonTables />;
  // }
};

export default ConsultarProductos;
