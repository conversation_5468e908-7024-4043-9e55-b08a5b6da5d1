import {
  Autocomplete,
  Box,
  Checkbox,
  CircularProgress,
  Popper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  tableCellClasses,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { obtenerProductosFiltradosAccion } from "../../redux/productosDucks";
import styled from "@emotion/styled";
import { lighten, darken } from "@mui/system";
import { set } from "date-fns";

const BuscarProducto = ({
  setSelectedProduct,
  selectedProduct,
  kits,
  listProductStores,
  direcSale,
  setVariationOption,
  variationOption,
  setSelectedStores,
  selectedStores,
  fullW,
}) => {
  const [open, setOpen] = useState(false);
  const [options, setOptions] = useState([]);
  const [query, setQuery] = useState("");
  const [results, setResults] = useState([]);
  const [timer, setTimer] = useState(null);
  const [value, setValue] = useState("");
  const dispatch = useDispatch();
  const autocompleteRef = useRef(null);

  const [loadingSearch, setLoadingSearch] = useState(false);

  const getSearch = useSelector((store) => store.productos.productos);

  const handleToggleStore = (store) => {
    setSelectedStores((prevSelected) =>
      prevSelected.includes(store)
        ? prevSelected.filter((s) => s !== store)
        : [...prevSelected, store]
    );
  };

  useEffect(()=> {
    console.log(selectedStores, "selectedStores");
  }, [selectedStores]);

  useEffect(() => {
    setLoadingSearch(true);
    if (getSearch) {
      setResults(getSearch);
      setLoadingSearch(false);
    }
    setValue("");
  }, [getSearch, dispatch, loadingSearch]);

  const handleChange = (event) => {
    const inputValue = event.target.value;
    setValue(inputValue);
    setLoadingSearch(true);
  };

  const handleChangeValue = (event, value) => {
    setSelectedProduct(value);
  };

  useEffect(() => {
    if (!open) {
      setResults([]);
    }
    if (timer) {
      clearTimeout(timer);
    }
    setLoadingSearch(true);
    dispatch(obtenerProductosFiltradosAccion({ search: value, scope: "products-stores" }));
  }, [value]);

  const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
      backgroundColor: "#003876",
      color: theme.palette.common.white,
    },
    [`&.${tableCellClasses.body}`]: {
      fontSize: 14,
    },
  }));

  const GroupItems = styled("ul")({
    padding: 0,
  });

  const GroupHeader = styled("div")(({ theme }) => ({
    position: "sticky",
    top: "-8px",
    padding: "4px 10px",
    color: theme.palette.primary.main,
    backgroundColor:
      theme.palette.mode === "light"
        ? lighten(theme.palette.primary.light, 0.85)
        : darken(theme.palette.primary.main, 0.8),
  }));

  return listProductStores ? (
    <div>
      <Autocomplete
        multiple
        id="grouped-demo"
        value={selectedStores}
        options={
          typeof results === "string"
            ? []
            : results?.flatMap((option) =>
                option.products.map((product) => ({
                  ...product,
                  internalBaseSku: option.internalBaseSku,
                  stores: option.product_stores,
                  parentOption: option,
                }))
              )
        }
        onChange={(event, newValue) => {
          setSelectedStores(newValue);
        }}
        loading={loadingSearch}
        groupBy={(option) => option.internalBaseSku}
        getOptionLabel={(option) => {
          // Verifica si option es un string o un objeto
          return typeof option === 'string' ? option : option?.internalSku || '';
        }}
        sx={{ width: fullW ? "100%" : 250 }}
        renderInput={(params) => (
          <TextField
            {...params}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <React.Fragment>
                  {loadingSearch ? (
                    <CircularProgress color="inherit" size={20} />
                  ) : null}
                  {params.InputProps.endAdornment}
                </React.Fragment>
              ),
            }}
            label="Busca un producto"
          />
        )}
        renderGroup={(params) => (
          <li key={params.key}>
            <GroupHeader>{params.group}</GroupHeader>
            <GroupItems>
            {params.children.map((child) => {
                const product = results
                  .flatMap((option) => option.products)
                  .find(
                    (product) => product.internalSku === child.props.children
                  );

                return (
                  <div key={child.key}>
                    <Box sx={{ paddingLeft: 2, fontWeight: "bold" }}>
                      {child.props.children}
                    </Box>
                    {product?.product_stores?.length > 0 ? (
                      product.product_stores.map((store) => (
                        <Box
                          key={store.store.storeName}
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            paddingLeft: 4,
                          }}
                        >
                            {console.log(product, "product")}
                          <Checkbox
                            checked={ selectedProduct === child.props.children && selectedStores.includes(store.store.storeName)}
                            onChange={() => {
                              setSelectedProduct(child.props.children);
                              setVariationOption(product);
                              setSelectedStores((prevSelected) =>
                                prevSelected.includes(store.store.storeName)
                                  ? prevSelected.filter(
                                      (s) => s !== store.store.storeName
                                    )
                                  : [...prevSelected, store.store.storeName]
                              )
                            }
                            }
                            />
                            {store.store.storeName}
                        </Box>
                      ))
                    ) : (
                      <Box>{"No hay almacenes con existencias"}</Box>
                    )}
                  </div>
                );
              })}
            </GroupItems>
          </li>
        )}
      />
    </div>
  ) : direcSale ? (
    <div>
      <Autocomplete
        id="grouped-demo"
        value={variationOption}
        options={
          typeof results === "string"
            ? []
            : results?.flatMap((option) =>
                option.products.map((product) => ({
                  ...product,
                  internalBaseSku: option.internalBaseSku,
                  stores: option.product_stores,
                  parentOption: option,
                }))
              )
        }
        onChange={(event, newValue) => {
          toggleSelection(
            newValue.internalSku,
            newValue.parentOption,
            newValue
          );
        }}
        loading={loadingSearch}
        groupBy={(option) => option.internalBaseSku}
        getOptionLabel={(option) => option.internalSku}
        isOptionEqualToValue={(option, value) =>
          option.internalSku === value.internalSku
        }
        sx={{ width: fullW ? "100%" : 250 }}
        renderInput={(params) => (
          <TextField {...params} InputProps={{
            ...params.InputProps,
            endAdornment: (
              <React.Fragment>
                {loadingSearch ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </React.Fragment>
            ),
          }} label="Busca un producto" />
        )}
        renderGroup={(params) => (
          <li key={params.key}>
            <GroupHeader>{params.group}</GroupHeader>
            <GroupItems>{params.children}</GroupItems>
          </li>
        )}
      />
    </div>
  ) : (
      <Autocomplete
        id="asynchronous-demo"
        open={open}
        onOpen={() => {
          setOpen(true);
        }}
        onClose={() => {
          setOpen(false);
        }}
        isOptionEqualToValue={(option, value) =>
          option.description === value.description
        }
        getOptionLabel={(option) =>
          option.description + "-" + option.brand + "-" + option.model
        }
        sx={{ width: "100%" }}
        options={typeof results === "string" ? [] : results}
        loading={loadingSearch}
        onChange={handleChangeValue}
        ref={autocompleteRef}
        PopperComponent={({ children, ...props }) => (
          <Popper {...props} placement="bottom-start">
            <Table
              size="small"
              sx={{
                borderRadius: "5rem",
              }}
            >
              <TableHead>
                <TableRow>
                  <StyledTableCell align="left">Descripcion</StyledTableCell>
                  <StyledTableCell align="center">Marca</StyledTableCell>
                  <StyledTableCell align="right">Modelo</StyledTableCell>
                </TableRow>
              </TableHead>
            </Table>
            {children}
          </Popper>
        )}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Busca un Producto"
            onChange={handleChange}
            value={value}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <React.Fragment>
                  {loadingSearch ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </React.Fragment>
              ),
            }}
          />
        )}
        renderOption={(props, option, { selected }) => (
          <Box {...props}>
            <Table size="small">
              <TableBody>
                <TableRow selected={selected}>
                  <TableCell align="left" width="33%">
                    {option.description}
                  </TableCell>
                  <TableCell align="center" width="33%">
                    {option.brand}
                  </TableCell>
                  <TableCell align="right" width="33%">
                    {option.model}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </Box>
        )}
      />
  );
};


export default BuscarProducto;
