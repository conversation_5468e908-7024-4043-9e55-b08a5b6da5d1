import React from 'react'
import { instanceAxios } from '../../redux/axiosInstance'
import { isEmpty } from '../utils/utils'
import {
    Autocomplete,
    CircularProgress,
    TextField,
    MenuItem,
} from "@mui/material";
import styled from "@emotion/styled";
import { lighten, darken } from "@mui/system";

const BuscarProductoRe = ({
    product,
    setProducts,
    disabled
}) => {
    const [listProducts, setListProducts] = React.useState(null)
    const [search, setSearch] = React.useState("")
    const [loading, setLoading] = React.useState(false)
    const manageChange = (event, newValue) => {
        setProducts((partidas) =>
            partidas.map((partida) => {
                if (partida.id === product.id) {
                    return {
                        ...partida,
                        productSelected: {
                            product: newValue,
                            store: ''
                        }
                    }
                }

                return partida
            })
        );
    }
    const GroupItems = styled("ul")({
        padding: 0,
    });
    const GroupHeader = styled("div")(({ theme }) => ({
        position: "sticky",
        top: "-8px",
        padding: "4px 10px",
        color: theme.palette.primary.main,
        backgroundColor:
            theme.palette.mode === "light"
                ? lighten(theme.palette.primary.light, 0.85)
                : darken(theme.palette.primary.main, 0.8),
    }));
    React.useEffect(() => {
        const getProductsList = async () => {
            const offsetActual = ""
            const scope = "consolidated_info"
            //const scope = "product_stores"
            const cantidadDeProductosPorPagina = 30
            const url = `api/products/fuzzyProductsBaseFiltro?search=${search}&scope=${scope}&offset=${offsetActual}&next=${cantidadDeProductosPorPagina}`;
            const res = await instanceAxios.get(url);
            setListProducts(res.data.productos)
        };
        setLoading(true)
        setListProducts(null)
        getProductsList()
        setLoading(false)

    }, [search]);

    const returnOptions = () => {
        return listProducts.flatMap((option) =>
            option.products.map((product) => ({
                ...product,
                internalBaseSku: option.internalBaseSku,
                productBase: option
            }))
        )
    }
    return (
            <Autocomplete
                id="grouped-demo"
                value={loading ? 'circularProgress' : product.productSelected.product}
                options={listProducts ? returnOptions() : []}
                onChange={manageChange}
                loading={loading}
                disabled={disabled}
                groupBy={(option) => option.internalBaseSku}
                getOptionLabel={(option) => option.internalSku}
                isOptionEqualToValue={(option, value) =>
                    option.internalSku === value.internalSku
                }
                //sx={{ width: fullW ? "100%" : 250 }}
                sx={{ width: "100%", margin:"auto" }}
                renderInput={(params) => (
                    <TextField {...params} InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                            <React.Fragment>
                                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                            </React.Fragment>
                        ),
                    }} label="Busca un producto" />
                )}
                renderGroup={(params) => (
                    <li key={params.key}>
                        <GroupHeader>{params.group}</GroupHeader>
                        <GroupItems>{params.children}</GroupItems>
                    </li>
                )}
            />
    )
}

export default BuscarProductoRe