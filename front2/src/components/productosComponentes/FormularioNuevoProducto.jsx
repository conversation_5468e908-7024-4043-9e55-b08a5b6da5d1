import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import TextField from "@mui/material/TextField";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Alert,
  Autocomplete,
  Button,
  Chip,
  Fab,
  Input,
  MenuItem,
  Select,
} from "@mui/material";
import ImagesFormProvider from "../../context/ImagesFormProvider";
import { ImagesFormContext } from "../../context/ImagesFormProvider";
import ImagesVariationsFormProvider from "../../context/ImagesVariationsFormProvider";
import { NewProductVariationsContext } from "../../context/NewProductVariationsProvider";
import theme from "../../temaConfig";
import ImageUpdaterList from "./imageUpdaterComponentes/ImageUpdaterList";
import { useDispatch, useSelector } from "react-redux";
import {
  dar<PERSON>ltaProducto,
  setMensaje,
  actualizarProducto,
  obtenerVariacionProductos,
  obtenerVariacionesFiltradas,
  colocarEstadoEliminandoProducto,
  persistirDatosNuevoProducto,
  resetPersistirDatosNuevoProducto,
  addCustomVariation,
  addCustomVariationAttribute,
  obtenerAllVariacionesFiltradas,
  eliminarProducto
} from "../../redux/productosDucks";
import { styled } from "@mui/material/styles";
import { useCookies } from "react-cookie";
import LoadingButton from "@mui/lab/LoadingButton";
import EstadosDinamicos from "./EstadosDinamicos";
import "../css/variations.css";
import DeleteForeverIcon from "@mui/icons-material/DeleteForever";
import ConfirmarEliminarProducto from "./ConfirmarEliminarProducto";
import CircularProgress from "@mui/material/CircularProgress";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import Fade from '@mui/material/Fade';

import {
  EXTENSIONES_PERMITIDAS_IMAGENES_PRODUCTOS,
  BRAND,
  MODEL,
  DESCRIPTION,
  LONGDESCRIPTION,
  UPC,
  SATCODE,
  CONDITION,
  VIDEO,
  UNITCODE,
  UNITLONGITUDESHIPPING,
  UNITWEIGHTSHIPPING,
  UNITLONGITUDEPRODUCT,
  UNITWEIGHTPRODUCT,
  LENGTHSHIPPING,
  WIDTHSHIPPING,
  HEIGHTSHIPPING,
  WEIGHTSHIPPING,
  LENGTHPRODUCT,
  WIDTHPRODUCT,
  HEIGHTPRODUCT,
  WEIGHTPRODUCT,
} from "../../Utils/config";
import {
  toCm,
  toKg,
  checkLength,
  checkValidVideoUrl,
  checkSATCode,
  checkUnitCode,
  checkLengthUnit,
  checkWeightUnit,
  checkMagnitude,
  checkUPC,
  concatErrors,
} from "../../Utils/generalFunctions";
import { DeleteForever, Tune } from "@mui/icons-material";
import Dialog from "@mui/material/Dialog";
import ModalRedireccionProductos from "./ModalRedireccionProductos";
import { useContext } from "react";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import SendIcon from "@mui/icons-material/Send";
import { useLocation, useNavigate } from "react-router-dom";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import DialogAddVariations from "./componentsHelpers/DialogAddVariations";
import DialogWarning from "./componentsHelpers/DialogWarning";
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { UntCode } from "../componentesGenerales/UntCode";
import { NumericFormat } from "react-number-format";
import { useTheme } from "@emotion/react";
import { AlertComponent } from "../componentesGenerales/Alert";

const CssTextField = styled(TextField)(({ zoneColor }) => {
  return {
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        borderColor: zoneColor,
      },
    },
  };
});


const AddAtribute = ({
  atributeList,
  setAtributeList,
  atributeListShow,
  setAtributeListShow,
  producto,
  attrbutesSend,
}) => {
  const product = useSelector(
    (state) => state.productos.persevereDataNewProduct
  );

  const handleChangeName = (e, id) => {
    let existValue = JSON.parse(attrbutesSend);

    let updatedList = [];

    if (existValue != [] && existValue.hasOwnProperty(e.target.value)) {
      updatedList = atributeList.map((atribute) => {
        if (atribute.id === id) {
          return {
            ...atribute,
            error: true,
            message: "Atributo existente",
            name: e.target.value,
          };
        }
        return atribute;
      });
    } else {
      updatedList = atributeList.map((atribute) => {
        if (atribute.id === id) {
          return { ...atribute, name: e.target.value, error: false };
        }
        return atribute;
      });
    }

    setAtributeList(updatedList);
  };

  const setObjectAtributes = useCallback((name = "", value = "") => {
    const id = Date.now().toString() + Math.random().toString().substr(2, 5);
    const newAtribute = {
      id,
      name,
      value,
      error: false,
      message: "",
    };
    return newAtribute;
  }, []);

  //si hay atributos mostrarlos

  const newAtributes = useMemo(() => {
    if (producto) {
      return producto.map((obj) =>
        setObjectAtributes(obj.attributeName, obj.attributeValue)
      );
    }
    return [];
  }, [producto, setObjectAtributes]);

  useEffect(() => {
    setAtributeListShow((prevAtributes) => [...prevAtributes, ...newAtributes]);
  }, [newAtributes]);

  // console.log(producto);

  const handleChangeValue = (e, id) => {
    const updatedList = atributeList.map((atribute) => {
      if (atribute.id === id) {
        return { ...atribute, value: e.target.value, error: false };
      }
      return atribute;
    });

    setAtributeList(updatedList);
  };

  const handleAddAtribute = () => {
    setAtributeList([...atributeList, setObjectAtributes()]);
  };

  const handleSubmitAtribute = (id) => {
    //verifica wu eno este vacio los campos
    const emptyFields = atributeList.filter(
      (atribute) => atribute.name === "" || atribute.value === ""
    );

    if (emptyFields.length > 0) {
      const updatedList = atributeList.map((atribute) => {
        if (emptyFields.includes(atribute)) {
          return { ...atribute, error: true };
        }
        return atribute;
      });

      setAtributeList(updatedList);
      return;
    }

    //eliminando el item del array y añadiendoilo al array que lo muestra
    const deletedItem = atributeList.find((atribute) => atribute.id === id);
    const updatedList = atributeList.filter((atribute) => atribute.id !== id);
    setAtributeList(updatedList);
    setAtributeListShow([...atributeListShow, deletedItem]);
  };

  const handleDelete = (id) => {
    const updatedList = atributeListShow.filter(
      (atribute) => atribute.id !== id
    );
    setAtributeListShow(updatedList);
  };
  //borra el elemento del formulario
  const handleDeleteText = (id) => {
    const updatedList = atributeList.filter((atribute) => atribute.id !== id);
    setAtributeList(updatedList);
  };

  const handleClick = (id) => {
    const updatedList = atributeList.map((atribute) => {
      if (atribute.id === id) {
        return { ...atribute, error: false };
      }
      return atribute;
    });

    setAtributeList(updatedList);
  };

  const handleClickChip = (id) => {
    const deletedItem = atributeListShow.find((atribute) => atribute.id === id);
    const updatedList = atributeListShow.filter(
      (atribute) => atribute.id !== id
    );
    setAtributeListShow(updatedList);
    setAtributeList([...atributeList, deletedItem]);
  };

  return (
    <Grid item sx={{ width: "90%" }}>
      <h4>{atributeListShow?.length > 0 ? "Atributos" : "Agrega Atributos"}</h4>
      <Box
        sx={{ display: "flex", flexWrap: "wrap", width: "90%", margin: "auto" }}
      >
        {atributeListShow?.length > 0
          ? atributeListShow.map((atribute) => (
            <Box key={atribute.id} sx={{ display: "flex" }}>
              <Chip
                onClick={() => handleClickChip(atribute.id)}
                size="medium"
                sx={{
                  margin: "0.5rem",
                  padding: "0.5rem",
                  height: "50px",
                  fontSize: "1.5em",
                }}
                label={atribute.name + ": " + atribute.value}
                onDelete={() => handleDelete(atribute.id)}
              />
            </Box>
          ))
          : null}
      </Box>
      <Box padding={2} display="flex" flexDirection="column">
        {atributeList.map((atribute, index) => (
          <Box key={atribute.id + index} display="flex" flexWrap="wrap">
            <Box margin={2}>
              <Box display="flex" alignItems="center">
                <Box display="flex" justifyContent="center" marginRight={1}>
                  <Button
                    color="error"
                    style={{ height: "30px", padding: "0" }}
                    onClick={() => handleDeleteText(atribute.id)}
                  >
                    <DeleteForever />
                  </Button>
                </Box>
                <Box display="flex">
                  <TextField
                    error={atribute.error}
                    label="nombre"
                    helperText={atribute.message}
                    value={atribute.name}
                    onClick={() => handleClick(atribute.id)}
                    onChange={(e) => handleChangeName(e, atribute.id)}
                  />
                </Box>
                <Box display="flex" sx={{ marginLeft: "1rem" }}>
                  <TextField
                    error={atribute.error}
                    label="valor"
                    helperText={atribute.message != "" ? " " : null}
                    value={atribute.value}
                    onClick={() => handleClick(atribute.id)}
                    onChange={(e) => handleChangeValue(e, atribute.id)}
                  />
                </Box>
                <Button
                  variant="contained"
                  disabled={atribute.error}
                  onClick={() => handleSubmitAtribute(atribute.id)}
                  sx={{ width: "30px", marginLeft: "1rem", height: "2rem" }}
                  endIcon={
                    <SendIcon
                      sx={{
                        margin: "0",
                        padding: "0",
                      }}
                    />
                  }
                ></Button>
              </Box>
            </Box>
          </Box>
        ))}

        <Button
          variant="contained"
          color="primary"
          sx={{ width: "100px" }}
          onClick={handleAddAtribute}
        >
          <AddCircleOutlineIcon /> Atributo
        </Button>
        {/* <Box marginTop={2}>Lista de etiquetas: {renderTagList()}</Box> prueba para mostrar los tags ingresados*/}
      </Box>
    </Grid>
  );
};

const FormularioNuevoProductoCascaron = (props) => {
  const [open, setOpen] = React.useState(false);
  const producto = props.producto?.producto;
  let isChecked = props.isChecked;
  const { imageList, initialListPhotos } = React.useContext(ImagesFormContext);
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const resetForm = props.resetForm;
  const setResetForm = props.setResetForm;

  const {
    flag,
    setFlag,
    formDataAll,
    setFormDataAll,
    deleteImageUrl,
    selectNumberVAriation,
    setSelectNumberVariation,
    parentStates,
    addFilesToFormData,
    componentsFlag,
    setComponentsFlag,
  } = useContext(NewProductVariationsContext);




  const productPersist = useSelector((state) => {
    return state.productos.persevereDataNewProduct;
  });

  useEffect(() => {
    if (flag) {
      procesarDatos();
      setFlag(false);
    }
    if (resetForm) {
      resetDataForm();
      setResetForm(false);
    }
  }, [flag, resetForm]);

  const internalBaseSku = producto ? producto.internalBaseSku : ""

  const [brandValidation, setBrandValidation] = React.useState({
    valor:
      producto && producto.brand
        ? producto.brand
        : productPersist?.brand != ""
          ? productPersist?.brand
          : "",
    error: false,
    color: null,
    textoAyuda: " ",
  });

  const [modelValidation, setModelValidation] = React.useState({
    valor:
      producto && producto.model
        ? producto.model
        : productPersist?.model != ""
          ? productPersist?.model
          : "",
    error: false,
    color: null,
    textoAyuda: " ",
  });

  const [descripcionValidation, setDescriptionValidation] = React.useState({
    valor:
      producto && producto.description
        ? producto.description
        : productPersist?.description != ""
          ? productPersist?.description
          : "",
    error: false,
    color: null,
    textoAyuda: " ",
  });
  const [longDescriptionValidation, setLongDescriptionValidation] =
    React.useState({
      valor:
        producto && producto.longDescription
          ? producto.longDescription
          : productPersist?.longDescription != ""
            ? productPersist?.longDescription
            : "",
      error: false,
      color: null,
      textoAyuda: " ",
    });
    
  const [videoValidation, setVideoValidation] = React.useState({
    valor:
      producto && producto.video
        ? producto.video
        : productPersist?.video != ""
          ? productPersist?.video
          : "",
    error: false,
    color: null,
    textoAyuda: " ",
  });
  const [satCodeValidation, setSatCodeValidation] = React.useState({
    valor:
      producto && producto.satCode
        ? producto.satCode
        : productPersist?.satCode != ""
          ? productPersist?.satCode
          : "",
    error: false,
    color: null,
    textoAyuda: " ",
  });
  const [unitCodeValidation, setUnitCodeValidation] = React.useState({
    valor:
      producto && producto.unitCode
        ? producto.unitCode
        : productPersist?.unitCode != ""
          ? productPersist?.unitCode
          : null,
    error: false,
    color: null,
    textoAyuda: " ",
  });

  const [unitLongitudeProductValidation, setUnitLongitudeProductValidation] =
    React.useState({
      valor: productPersist?.dimentionProduct?.unitLength || "cm",
      error: false,
      color: null,
      textoAyuda: " ",
    });
  const [unitWeightProductValidation, setUnitWeightProductValidation] =
    React.useState({
      valor: productPersist?.dimentionProduct?.unitWeight || "kg",
      error: false,
      color: null,
      textoAyuda: " ",
    });
  const [unitLongitudeShippingValidation, setUnitLongitudeShippingValidation] =
    React.useState({
      valor: productPersist?.dimentionShipment?.unitLength || "cm",
      error: false,
      color: null,
      textoAyuda: " ",
    });
  const [unitWeightShippingValidation, setUnitWeightShippingValidation] =
    React.useState({
      valor: productPersist?.dimentionShipment?.unitWeight || "kg",
      error: false,
      color: null,
      textoAyuda: " ",
    });

  let valor;
  if (producto) {
    if (producto.shippingDimensions?.length !== undefined) {
      valor = producto.shippingDimensions.length;
    } else if (producto.productBaseShippingDimensions?.length !== undefined) {
      valor = producto.productBaseShippingDimensions.length;
    } else {
      valor = "";
    }
  } else {
    valor = productPersist.dimentionShipment?.length || "";
  }
  const [
    lengthShippingDimentionValidation,
    setLengthShippingDimentionValidation,
  ] = React.useState({
    valor,
    error: false,
    color: null,
    textoAyuda: " ",
  });
  const [
    widthShippingDimentionValidation,
    setWidthShippingDimentionValidation,
  ] = React.useState({
    valor: producto
      ? producto.shippingDimensions || producto.productBaseShippingDimensions
        ? producto.shippingDimensions?.width != undefined
          ? producto.shippingDimensions.width
          : producto.productBaseShippingDimensions.width
        : ""
      : productPersist?.dimentionShipment?.width || "",
    error: false,
    color: null,
    textoAyuda: " ",
  });

  const [
    heightShippingDimentionValidation,
    setHeightShippingDimentionValidation,
  ] = React.useState({
    valor: producto
      ? producto.shippingDimensions || producto.productBaseShippingDimensions
        ? producto.shippingDimensions?.height != undefined
          ? producto.shippingDimensions.height
          : producto.productBaseShippingDimensions.height
        : ""
      : productPersist?.dimentionShipment?.height || "",
    error: false,
    color: null,
    textoAyuda: " ",
  });

  const [
    weightShippingDimentionValidation,
    setWeightShippingDimentionValidation,
  ] = React.useState({
    valor: producto
      ? producto.shippingDimensions || producto.productBaseShippingDimensions
        ? producto.shippingDimensions?.length != undefined
          ? producto.shippingDimensions.length
          : producto.productBaseShippingDimensions.length
        : ""
      : productPersist?.dimentionShipment?.weight || "",
    error: false,
    color: null,
    textoAyuda: " ",
  });
  const [
    lengthProductDimentionValidation,
    setLengthProductDimentionValidation,
  ] = React.useState({
    valor: producto
      ? producto.productDimensions || producto.productBaseProductDimensions
        ? producto.productDimensions?.length != undefined
          ? producto.productDimensions.length
          : producto.productBaseProductDimensions.length
        : ""
      : productPersist?.dimentionProduct?.length || "",
    error: false,
    color: null,
    textoAyuda: " ",
  });
  const [widthProductDimentionValidation, setWidthProductDimentionValidation] =
    React.useState({
      valor: producto
        ? producto.productDimensions || producto.productBaseProductDimensions
          ? producto.productDimensions?.width != undefined
            ? producto.productDimensions.width
            : producto.productBaseProductDimensions.width
          : ""
        : productPersist?.dimentionProduct?.width || "",
      error: false,
      color: null,
      textoAyuda: " ",
    });
  const [
    heightProductDimentionValidation,
    setHeightProductDimentionValidation,
  ] = React.useState({
    valor: producto
      ? producto.productDimensions || producto.productBaseProductDimensions
        ? producto.productDimensions?.height != undefined
          ? producto.productDimensions.height
          : producto.productBaseProductDimensions.height
        : ""
      : productPersist?.dimentionProduct?.height || "",
    error: false,
    color: null,
    textoAyuda: " ",
  });
  const [
    weightProductDimentionValidation,
    setWeightProductDimentionValidation,
  ] = React.useState({
    valor: producto
      ? producto.productDimensions || producto.productBaseProductDimensions
        ? producto.productDimensions?.weight != undefined
          ? producto.productDimensions.weight
          : producto.productBaseProductDimensions.weight
        : ""
      : productPersist?.dimentionProduct?.weight || "",
    error: false,
    color: null,
    textoAyuda: " ",
  });

  const [upcValidation, setUpcValidation] = React.useState({
    valor: producto && producto.upc ? producto.upc : productPersist?.upc || "",
    error: false,
    color: null,
    textoAyuda: " ",
  });
  const sinUnidad = "Sin unidad de medida";
  const manejarCambioDeMarca = (e) => {
    const marca =
      typeof e === "object"
        ? e.target.value
        : productPersist?.brand != ""
          ? productPersist?.brand
          : e;
    if (marca.length === 0) {
      let marcaVacia = "La marca esta vacia";
      setBrandValidation({
        valor: marca,
        error: true,
        color: null,
        textoAyuda: marcaVacia,
      });
      return marcaVacia;
    } else {
      if (marca.length === BRAND.longitud) {
        setBrandValidation({
          valor: marca,
          error: false,
          color: "warning",
          textoAyuda: "Límite de caracteres alcanzado",
        });
      } else {
        let longitudValidation = checkLength(
          BRAND.label,
          marca,
          BRAND.longitud
        );
        if (longitudValidation === true) {
          setBrandValidation({
            valor: marca,
            error: false,
            color: "success",
            textoAyuda: "Marca correcta",
          });
          dispatch(persistirDatosNuevoProducto({ key: "brand", value: marca }));
        } else {
          setBrandValidation({
            valor: marca,
            error: true,
            color: null,
            textoAyuda: longitudValidation,
          });
          return longitudValidation;
        }
      }
    }
    return "";
  };
  const manejarCambioDeModelo = (e) => {
    const modelo =
      typeof e === "object"
        ? e.target.value
        : productPersist?.model != ""
          ? productPersist?.model
          : e;
    if (modelo.length === 0) {
      let modeloVacio = "El modelo esta vacio";
      setModelValidation({
        valor: modelo,
        error: true,
        color: null,
        textoAyuda: modeloVacio,
      });
      return modeloVacio;
    } else {
      if (modelo.length >= MODEL.longitud) {
        setModelValidation({
          valor: modelo,
          error: false,
          color: "warning",
          textoAyuda: "Límite de caracteres alcanzado",
        });
      } else {
        let longitudValidation = checkLength(
          MODEL.label,
          modelo,
          MODEL.longitud
        );
        if (longitudValidation === true) {
          setModelValidation({
            valor: modelo,
            error: false,
            color: "success",
            textoAyuda: "Modelo correcto",
          });
          dispatch(
            persistirDatosNuevoProducto({ key: "model", value: modelo })
          );
        } else {
          setModelValidation({
            valor: modelo,
            error: true,
            color: null,
            textoAyuda: longitudValidation,
          });
          return longitudValidation;
        }
      }
    }
    return "";
  };
  const manejarCambioDeDescripcion = (e) => {
    const descripcion =
      typeof e === "object"
        ? e.target.value
        : productPersist?.description != ""
          ? productPersist?.description
          : e;
    if (descripcion.length === 0) {
      let descripcionVacia = "la descripcion es obligatoria";

      setDescriptionValidation({
        valor: descripcion,
        error: true,
        color: null,
        textoAyuda: descripcionVacia,
      });
      return descripcionVacia;
    } else {
      if (descripcion.length >= DESCRIPTION.longitud) {
        setDescriptionValidation({
          valor: descripcion,
          error: false,
          color: "warning",
          textoAyuda: "Límite de caracteres alcanzado",
        });
      } else {
        let longitudValidation = checkLength(
          DESCRIPTION.label,
          descripcion,
          DESCRIPTION.longitud
        );
        if (longitudValidation === true) {
          setDescriptionValidation({
            valor: descripcion,
            error: false,
            color: "success",
            textoAyuda: "Descripción correcta",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "description",
              value: descripcion,
            })
          );
        } else {
          setDescriptionValidation({
            valor: descripcion,
            error: true,
            color: null,
            textoAyuda: longitudValidation,
          });
          return longitudValidation;
        }
      }
    }
    return "";
  };
  const manejarCambioDeDescripcionLarga = (value) => {
    // const descripcionLarga =
    //   value || productPersist?.longDescription != ""
    //     ? productPersist?.longDescription
    //     : value;
    const descripcionLarga = value;

    if (descripcionLarga.length === 0) {
      setLongDescriptionValidation({
        valor: descripcionLarga,
        error: false,
        color: "warning",
        textoAyuda: "Vacio",
      });
    } else {
      if (descripcionLarga.length >= LONGDESCRIPTION.longitud) {
        setLongDescriptionValidation({
          valor: descripcionLarga,
          error: false,
          color: "warning",
          textoAyuda: "Límite de caracteres alcanzado",
        });
      } else {
        let longitudValidation = checkLength(
          LONGDESCRIPTION.label,
          descripcionLarga,
          LONGDESCRIPTION.longitud
        );
        if (longitudValidation === true) {
          setLongDescriptionValidation({
            valor: descripcionLarga,
            error: false,
            color: "success",
            textoAyuda: "Descripción larga correcta",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "longDescription",
              value: descripcionLarga,
            })
          );
        } else {
          setLongDescriptionValidation({
            valor: descripcionLarga,
            error: true,
            color: null,
            textoAyuda: longitudValidation,
          });
          return longitudValidation;
        }
      }
    }
    return "";
  };

  const manejarCambioVideo = (e) => {
    const url = typeof e === "object" ? e.target.value : e;
    if (url.length === 0) {
      setVideoValidation({
        valor: url,
        error: false,
        color: "warning",
        textoAyuda: "Vacio",
      });
    } else {
      let validateURL = checkValidVideoUrl(url);
      if (validateURL === true) {
        setVideoValidation({
          valor: url,
          error: false,
          color: "success",
          textoAyuda: "URL correcta",
        });
        dispatch(persistirDatosNuevoProducto({ key: "video", value: url }));
      } else {
        setVideoValidation({
          valor: url,
          error: true,
          color: null,
          textoAyuda: validateURL,
        });
        return validateURL;
      }
    }
    return "";
  };
  const manejarCambioDeCodigoUnidad = (event, value) => {
    let unitCode = value;
    if (unitCode === null) {
      setUnitCodeValidation({
        valor: unitCode,
        error: false,
        color: "warning",
        textoAyuda: "Vacio",
      });
    } else {
      let validateUnitCode = checkUnitCode(unitCode);
      if (validateUnitCode === true) {
        setUnitCodeValidation({
          valor: unitCode,
          error: false,
          color: "success",
          textoAyuda: "Código correcto",
        });
        dispatch(
          persistirDatosNuevoProducto({ key: "unitCode", value: unitCode })
        );
      } else {
        setUnitCodeValidation({
          valor: unitCode,
          error: true,
          color: null,
          textoAyuda: validateUnitCode,
        });
        return validateUnitCode;
      }
    }
    return "";
  };
  const manejarCambioDeCodigoSAT = (e) => {
    const codigoSAT = typeof e === "object" ? e.target.value : e;
    const onlyNums = codigoSAT.replace(/[^0-9]/g, "");
    if (onlyNums.length === 0) {
      setSatCodeValidation({
        valor: onlyNums,
        error: false,
        color: "warning",
        textoAyuda: "Vacio",
      });
    } else {
      let validacionSAT = checkSATCode(onlyNums);
      if (validacionSAT === true) {
        setSatCodeValidation({
          valor: onlyNums,
          error: false,
          color: "success",
          textoAyuda: "Código SAT correcto",
        });
        dispatch(
          persistirDatosNuevoProducto({ key: "satCode", value: onlyNums })
        );
      } else {
        setSatCodeValidation({
          valor: onlyNums,
          error: true,
          color: null,
          textoAyuda: validacionSAT,
        });
        return "El código SAT debe tener 8 dígitos";
      }
    }
    return "";
  };

  const manejarCambioDeUpc = (e) => {
    const upc = typeof e === "object" ? e.target.value : e;
    const onlyNums = upc.replace(/[^0-9]/g, "");
    if (onlyNums.length === 0) {
      setUpcValidation({
        valor: onlyNums,
        error: false,
        color: "warning",
        textoAyuda: "Vacio",
      });
    } else {
      let validacionUPC = checkUPC(onlyNums);
      if (validacionUPC === true) {
        setUpcValidation({
          valor: onlyNums,
          error: false,
          color: "success",
          textoAyuda: "UPC correcto",
        });
        dispatch(persistirDatosNuevoProducto({ key: "upc", value: onlyNums }));
      } else {
        setUpcValidation({
          valor: onlyNums,
          error: true,
          color: null,
          textoAyuda: validacionUPC,
        });
        return "El upc debe ser numérico con longitud de 8, 12 o 13";
      }
    }
    return "";
  };
  const manejarCambioDeUnidadLongitudEnvio = (event, value) => {
    let unidadLongitudEnvio = value;

    if (unidadLongitudEnvio === null) {
      setUnitLongitudeShippingValidation({
        valor: unidadLongitudEnvio,
        error: true,
        color: null,
        textoAyuda: sinUnidad,
      });
      return sinUnidad;
    } else {
      let validateLengthUnit = checkLengthUnit(unidadLongitudEnvio);
      if (validateLengthUnit === true) {
        setUnitLongitudeShippingValidation({
          valor: unidadLongitudEnvio,
          error: false,
          color: "success",
          textoAyuda: "unidad correcta",
        });
        dispatch(
          persistirDatosNuevoProducto({
            key: "unitLength",
            value: unidadLongitudEnvio,
            type: "dimentionShipment",
          })
        );
      } else {
        setUnitLongitudeShippingValidation({
          valor: unidadLongitudEnvio,
          error: true,
          color: null,
          textoAyuda: validateLengthUnit,
        });
        return `${validateLengthUnit} de longitud para envio`;
      }
    }
    return "";
  };
  const manejarCambioDeUnidadPesoEnvio = (event, value) => {
    let unidadPesoEnvio = value;
    if (unidadPesoEnvio === null) {
      setUnitWeightShippingValidation({
        valor: unidadPesoEnvio,
        error: true,
        color: null,
        textoAyuda: sinUnidad,
      });
      return sinUnidad;
    } else {
      let validateWeightUnit = checkWeightUnit(unidadPesoEnvio);
      if (validateWeightUnit === true) {
        setUnitWeightShippingValidation({
          valor: unidadPesoEnvio,
          error: false,
          color: "success",
          textoAyuda: "unidad correcta",
        });
        dispatch(
          persistirDatosNuevoProducto({
            key: "unitWeight",
            value: unidadPesoEnvio,
            type: "dimentionShipment",
          })
        );
      } else {
        setUnitWeightShippingValidation({
          valor: unidadPesoEnvio,
          error: true,
          color: null,
          textoAyuda: validateWeightUnit,
        });
        return `${validateWeightUnit} de peso para envio`;
      }
    }
    return "";
  };
  const manejarCambioDeUnidadLongitudProducto = (event, value) => {
    let unidadLongitudProducto = value;
    if (unidadLongitudProducto === null) {
      setUnitLongitudeProductValidation({
        valor: unidadLongitudProducto,
        error: true,
        color: null,
        textoAyuda: sinUnidad,
      });
      return sinUnidad;
    } else {
      let validateLengthUnit = checkLengthUnit(unidadLongitudProducto);
      if (validateLengthUnit === true) {
        setUnitLongitudeProductValidation({
          valor: unidadLongitudProducto,
          error: false,
          color: "success",
          textoAyuda: "unidad correcta",
        });
        dispatch(
          persistirDatosNuevoProducto({
            key: "unitLength",
            value: unidadLongitudProducto,
            type: "dimentionProduct",
          })
        );
      } else {
        setUnitLongitudeProductValidation({
          valor: unidadLongitudProducto,
          error: true,
          color: null,
          textoAyuda: validateLengthUnit,
        });
        return `${validateLengthUnit} de longitud para producto`;
      }
    }
    return "";
  };
  const manejarCambioDeUnidadPesoProducto = (event, value) => {
    let unidadPesoProducto = value;
    if (unidadPesoProducto === null) {
      setUnitWeightProductValidation({
        valor: unidadPesoProducto,
        error: true,
        color: null,
        textoAyuda: sinUnidad,
      });
      return sinUnidad;
    } else {
      let validateWeightUnit = checkWeightUnit(unidadPesoProducto);
      if (validateWeightUnit === true) {
        setUnitWeightProductValidation({
          valor: unidadPesoProducto,
          error: false,
          color: "success",
          textoAyuda: "unidad correcta",
        });
        dispatch(
          persistirDatosNuevoProducto({
            key: "unitWeight",
            value: unidadPesoProducto,
            type: "dimentionProduct",
          })
        );
      } else {
        setUnitWeightProductValidation({
          valor: unidadPesoProducto,
          error: true,
          color: null,
          textoAyuda: validateWeightUnit,
        });
        return `${validateWeightUnit} de peso para producto`;
      }
    }
    return "";
  };

  const manejarCambioDeLargoEnvio = (e) => {
    const largo = typeof e === "object" ? e.target.value : e;
    if (largo === "") {
      setLengthShippingDimentionValidation({
        valor: largo,
        error: false,
        color: "warning",
        textoAyuda: "vacio",
      });
    } else {
      let validateMagnitude = checkMagnitude(largo);
      if (validateMagnitude === true) {
        if (parseFloat(largo) === 0) {
          setLengthShippingDimentionValidation({
            valor: largo,
            error: false,
            color: "warning",
            textoAyuda: "largo 0",
          });
        } else {
          setLengthShippingDimentionValidation({
            valor: largo,
            error: false,
            color: "success",
            textoAyuda: " ",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "length",
              value: largo,
              type: "dimentionShipment",
            })
          );
        }
      } else {
        setLengthShippingDimentionValidation({
          valor: largo,
          error: true,
          color: "success",
          textoAyuda: validateMagnitude,
        });
        dispatch(
          persistirDatosNuevoProducto({
            key: "length",
            value: largo,
            type: "dimentionShipment",
          })
        );
        return `${validateMagnitude} en largo de envio`;
      }
    }
    return "";
  };
  const manejarCambioDeAnchoEnvio = (e) => {
    const ancho = typeof e === "object" ? e.target.value : e;
    if (ancho === "") {
      setWidthShippingDimentionValidation({
        valor: ancho,
        error: false,
        color: "warning",
        textoAyuda: "vacio",
      });
    } else {
      let validateMagnitude = checkMagnitude(ancho);
      if (validateMagnitude === true) {
        if (parseFloat(ancho) === 0) {
          setWidthShippingDimentionValidation({
            valor: ancho,
            error: false,
            color: "warning",
            textoAyuda: "ancho 0",
          });
        } else {
          setWidthShippingDimentionValidation({
            valor: ancho,
            error: false,
            color: "success",
            textoAyuda: " ",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "width",
              value: ancho,
              type: "dimentionShipment",
            })
          );
        }
      } else {
        setWidthShippingDimentionValidation({
          valor: ancho,
          error: true,
          color: "success",
          textoAyuda: validateMagnitude,
        });
        return `${validateMagnitude} en ancho de envio`;
      }
    }
    return "";
  };
  const manejarCambioDeAltoEnvio = (e) => {
    const alto = typeof e === "object" ? e.target.value : e;
    if (alto === "") {
      setHeightShippingDimentionValidation({
        valor: alto,
        error: false,
        color: "warning",
        textoAyuda: "vacio",
      });
    } else {
      let validateMagnitude = checkMagnitude(alto);
      if (validateMagnitude === true) {
        if (parseFloat(alto) === 0) {
          setHeightShippingDimentionValidation({
            valor: alto,
            error: false,
            color: "warning",
            textoAyuda: "alto 0",
          });
        } else {
          setHeightShippingDimentionValidation({
            valor: alto,
            error: false,
            color: "success",
            textoAyuda: " ",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "height",
              value: alto,
              type: "dimentionShipment",
            })
          );
        }
      } else {
        setHeightShippingDimentionValidation({
          valor: alto,
          error: true,
          color: "success",
          textoAyuda: validateMagnitude,
        });
        return `${validateMagnitude} en alto de envio`;
      }
    }
    return "";
  };
  const manejarCambioDePesoEnvio = (e) => {
    const peso = typeof e === "object" ? e.target.value : e;
    if (peso === "") {
      setWeightShippingDimentionValidation({
        valor: peso,
        error: false,
        color: "warning",
        textoAyuda: "vacio",
      });
    } else {
      let validateMagnitude = checkMagnitude(peso);
      if (validateMagnitude === true) {
        if (parseFloat(peso) === 0) {
          setWeightShippingDimentionValidation({
            valor: peso,
            error: false,
            color: "warning",
            textoAyuda: "peso 0",
          });
        } else {
          setWeightShippingDimentionValidation({
            valor: peso,
            error: false,
            color: "success",
            textoAyuda: " ",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "weight",
              value: peso,
              type: "dimentionShipment",
            })
          );
        }
      } else {
        setWeightShippingDimentionValidation({
          valor: peso,
          error: true,
          color: "success",
          textoAyuda: validateMagnitude,
        });
        return `${validateMagnitude} en peso de envio`;
      }
    }
    return "";
  };
  const manejarCambioDeLargoProducto = (e) => {
    const largo = typeof e === "object" ? e.target.value : e;
    if (largo === "") {
      setLengthProductDimentionValidation({
        valor: largo,
        error: false,
        color: "warning",
        textoAyuda: "vacio",
      });
    } else {
      let validateMagnitude = checkMagnitude(largo);
      if (validateMagnitude === true) {
        if (parseFloat(largo) === 0) {
          setLengthProductDimentionValidation({
            valor: largo,
            error: false,
            color: "warning",
            textoAyuda: "largo 0",
          });
        } else {
          setLengthProductDimentionValidation({
            valor: largo,
            error: false,
            color: "success",
            textoAyuda: " ",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "length",
              value: largo,
              type: "dimentionProduct",
            })
          );
        }
      } else {
        setLengthProductDimentionValidation({
          valor: largo,
          error: true,
          color: "success",
          textoAyuda: validateMagnitude,
        });
        return `${validateMagnitude} en largo de producto`;
      }
    }
    return "";
  };
  const manejarCambioDeAnchoProducto = (e) => {
    const ancho = typeof e === "object" ? e.target.value : e;
    if (ancho === "") {
      setWidthProductDimentionValidation({
        valor: ancho,
        error: false,
        color: "warning",
        textoAyuda: "vacio",
      });
    } else {
      let validateMagnitude = checkMagnitude(ancho);
      if (validateMagnitude === true) {
        if (parseFloat(ancho) === 0) {
          setWidthProductDimentionValidation({
            valor: ancho,
            error: false,
            color: "warning",
            textoAyuda: "ancho 0",
          });
        } else {
          setWidthProductDimentionValidation({
            valor: ancho,
            error: false,
            color: "success",
            textoAyuda: " ",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "width",
              value: ancho,
              type: "dimentionProduct",
            })
          );
        }
      } else {
        setWidthProductDimentionValidation({
          valor: ancho,
          error: true,
          color: "success",
          textoAyuda: validateMagnitude,
        });
        return `${validateMagnitude} en ancho de producto`;
      }
    }
    return "";
  };
  const manejarCambioDeAltoProducto = (e) => {
    const alto = typeof e === "object" ? e.target.value : e;
    if (alto === "") {
      setHeightProductDimentionValidation({
        valor: alto,
        error: false,
        color: "warning",
        textoAyuda: "vacio",
      });
    } else {
      let validateMagnitude = checkMagnitude(alto);
      if (validateMagnitude === true) {
        if (parseFloat(alto) === 0) {
          setHeightProductDimentionValidation({
            valor: alto,
            error: false,
            color: "warning",
            textoAyuda: "altura 0",
          });
        } else {
          setHeightProductDimentionValidation({
            valor: alto,
            error: false,
            color: "success",
            textoAyuda: " ",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "height",
              value: alto,
              type: "dimentionProduct",
            })
          );
        }
      } else {
        setHeightProductDimentionValidation({
          valor: alto,
          error: true,
          color: "success",
          textoAyuda: validateMagnitude,
        });
        return `${validateMagnitude} en alto de producto`;
      }
    }
    return "";
  };
  const manejarCambioDePesoProducto = (e) => {
    const peso = typeof e === "object" ? e.target.value : e;
    if (peso === "") {
      setWeightProductDimentionValidation({
        valor: peso,
        error: false,
        color: "warning",
        textoAyuda: "vacio",
      });
    } else {
      let validateMagnitude = checkMagnitude(peso);
      if (validateMagnitude === true) {
        if (parseFloat(peso) === 0) {
          setWeightProductDimentionValidation({
            valor: peso,
            error: false,
            color: "warning",
            textoAyuda: "peso 0",
          });
        } else {
          setWeightProductDimentionValidation({
            valor: peso,
            error: false,
            color: "success",
            textoAyuda: " ",
          });
          dispatch(
            persistirDatosNuevoProducto({
              key: "weight",
              value: peso,
              type: "dimentionProduct",
            })
          );
        }
      } else {
        setWeightProductDimentionValidation({
          valor: peso,
          error: true,
          color: null,
          textoAyuda: validateMagnitude,
        });
        return `${validateMagnitude} en peso de producto`;
      }
    }
    return "";
  };
  /*set Variables */
  const showRedirect = () => {
    setOpen(true);
  };

  const mensajeAlert = useSelector((store) => store.productos.mensajeAlert);
  const clickInAlert = () => {
    dispatch(setMensaje(null));
  };

  //ATRIBUTOS
  const uniqueArray = productPersist?.attributes?.reduce((acc, current) => {
    if (!acc.find((item) => item.name === current.name)) {
      acc.push(current);
    }
    return acc;
  }, []);

  const [atributeList, setAtributeList] = useState([]);
  const [atributeListShow, setAtributeListShow] = useState(
    // []
    productPersist?.attributes?.length > 0 && !props.product
      ? productPersist?.attributes
      : []
  );

  const [attrbutesSend, setAttrbutesSend] = useState([]);
  //actualizando el objeto de atributos que enviare y esto se ejeucta cada vez que se actualiza mi array de atributos
  useEffect(() => {
    const uniqueArray = atributeListShow?.reduce((acc, current) => {
      if (!acc.find((item) => item.name === current.name)) {
        acc.push(current);
      }
      return acc;
    }, []);
    const objetoFinal = uniqueArray?.reduce(
      (objetoResultante, objetoActual) => {
        const { name, value } = objetoActual;
        if (!objetoResultante.hasOwnProperty(name)) {
          objetoResultante[name] = value;
        }
        return objetoResultante;
      },
      {}
    );

    //se setea el objeto transformado a cademna
    if (uniqueArray.length > 0 && !props.producto) {
      // if (props.product) return;
      dispatch(
        persistirDatosNuevoProducto({
          key: "attributes",
          value: uniqueArray,
        })
      );
    }
    setAttrbutesSend(JSON.stringify(objetoFinal));
  }, [atributeListShow]);

  const resetObjet = (data = "") => {
    return {
      valor: data,
      error: false,
      color: null,
      textoAyuda: " ",
    };
  };

  const resetDataForm = () => {
    setBrandValidation(resetObjet());
    setModelValidation(resetObjet());
    setDescriptionValidation(resetObjet());
    setLongDescriptionValidation(resetObjet());
    setVideoValidation(resetObjet());
    // setConditionValidation(resetObjet());
    setSatCodeValidation(resetObjet());
    setUnitCodeValidation(resetObjet(null));
    setUnitLongitudeProductValidation(resetObjet("cm"));
    setUnitWeightProductValidation(resetObjet("kg"));
    setUnitLongitudeShippingValidation(resetObjet("cm"));
    setUnitWeightShippingValidation(resetObjet("kg"));
    setLengthShippingDimentionValidation(resetObjet());
    setWidthShippingDimentionValidation(resetObjet());
    setHeightShippingDimentionValidation(resetObjet());
    setWeightShippingDimentionValidation(resetObjet());
    setLengthProductDimentionValidation(resetObjet());
    setWidthProductDimentionValidation(resetObjet());
    setHeightProductDimentionValidation(resetObjet());
    setWeightProductDimentionValidation(resetObjet());
    setUpcValidation(resetObjet());
    // atributos
    setAttrbutesSend([]);
    setAtributeListShow([]);
    setAtributeList([]);
    setFormDataAll(new FormData());
    // tags
    setTags([""]);
  };

  const procesarDatos = () => {
    /*validaciones */
    //formDataAll = new FormData();
    let textoErrores = "";

    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeMarca(brandValidation.valor)
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeModelo(modelValidation.valor)
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeDescripcion(descripcionValidation.valor)
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeUpc(upcValidation.valor)
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeDescripcionLarga(longDescriptionValidation.valor)
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeCodigoUnidad(null, unitCodeValidation.valor)
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeCodigoSAT(satCodeValidation.valor)
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioVideo(videoValidation.valor)
    );

    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeUnidadLongitudEnvio(
        null,
        unitLongitudeShippingValidation.valor
      )
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeUnidadPesoEnvio(null, unitWeightShippingValidation.valor)
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeUnidadLongitudProducto(
        null,
        unitLongitudeProductValidation.valor
      )
    );
    textoErrores = concatErrors(
      textoErrores,
      manejarCambioDeUnidadPesoProducto(null, unitWeightProductValidation.valor)
    );

    // textoErrores = concatErrors(
    //   textoErrores,
    //   manejarCambioDeLargoEnvio(lengthShippingDimentionValidation.valor)
    // );
    // textoErrores = concatErrors(
    //   textoErrores,
    //   manejarCambioDeAnchoEnvio(widthShippingDimentionValidation.valor)
    // );
    // textoErrores = concatErrors(
    //   textoErrores,
    //   manejarCambioDeAltoEnvio(heightShippingDimentionValidation.valor)
    // );
    // textoErrores = concatErrors(
    //   textoErrores,
    //   manejarCambioDePesoEnvio(weightShippingDimentionValidation.valor)
    // );
    // textoErrores = concatErrors(
    //   textoErrores,
    //   manejarCambioDeLargoProducto(lengthProductDimentionValidation.valor)
    // );
    // textoErrores = concatErrors(
    //   textoErrores,
    //   manejarCambioDeAnchoProducto(widthProductDimentionValidation.valor)
    // );
    // textoErrores = concatErrors(
    //   textoErrores,
    //   manejarCambioDeAltoProducto(heightProductDimentionValidation.valor)
    // );
    // textoErrores = concatErrors(
    //   textoErrores,
    //   manejarCambioDePesoProducto(weightProductDimentionValidation.valor)
    // );
    /*convertir longitudes shipping */
    /*validaciones */


    for (let clave of formDataAll.keys()) {
      if (clave !== 'variationProduct[]') {
        formDataAll.delete(clave);
      }
    }

    let listImageToDelete = [...deleteImageUrl];

    listImageToDelete.forEach((item) => {
      formDataAll.set("imageToDelete[]", item);
    });

    //si hay errores no se se muestra este mensaje
    if (textoErrores !== "") {
      dispatch(setMensaje({ mensaje: textoErrores, severity: "warning" }));
      return;
    }
    // let auxImages = [...imageList]
    // getFile(imageList, listImageToDelete, formData);


    for (let i = 0; i <= imageList.length - 1; i++) {
      let item = imageList[i];

      if (listImageToDelete.includes(item.props.title)) {
        listImageToDelete = listImageToDelete.filter(
          (fotoTitulo) => fotoTitulo !== item.props.title
        );
      } else {
        if (item.props.file) {
          let nombreCompletoSplit = item.props.file.name.split(".");
          let extension = nombreCompletoSplit[nombreCompletoSplit.length - 1];
          nombreCompletoSplit.pop();
          let nombre = nombreCompletoSplit.join();
          if (!EXTENSIONES_PERMITIDAS_IMAGENES_PRODUCTOS.includes(extension)) {
            textoErrores === ""
              ? (textoErrores = `Algún archivo seleccionado no es imagen`)
              : (textoErrores = `${textoErrores},Algún archivo seleccionado no es imagen`);
            return;
          }
          if (nombre.length === 0) {
            textoErrores === ""
              ? (textoErrores = `Algún archivo seleccionado no tiene nombre válido`)
              : (textoErrores = `${textoErrores},Algún archivo seleccionado no tiene nombre válido`);
            return;
          }
          // let dataimgSend = {
          //   // img: {
          //   //   name: item.props.file.name,
          //   //   size: item.props.file.size,
          //   //   type: item.props.file.type,
          //   //   // Puedes agregar más propiedades si son necesarias
          //   // },
          //   img: JSON.stringify(item.props.file,
          //   position: i,
          // };
          let newFile = new File([item.props.file], `${nombre}.${extension}`, {
            type: item.props.file.type,
          });

          newFile.position = i;
          formDataAll.append("imageBaseToAdd[]", newFile);
          formDataAll.append(`${newFile.name}`, i);

          setFormDataAll(formDataAll);
        } else {
          //se ejecuta cuando es una url y no un archivo
          let dataimgSend = {
            img: item.props.url,
            position: i,
          };
          formDataAll.append("urlBaseToAdd[]", JSON.stringify(dataimgSend));

          setFormDataAll(formDataAll);
        }
      }
    }

    let lengthShippingDimentionCm = toCm(
      lengthShippingDimentionValidation.valor,
      unitLongitudeShippingValidation.valor
    );
    let widthShippingDimentionCm = toCm(
      widthShippingDimentionValidation.valor,
      unitLongitudeShippingValidation.valor
    );
    let heightShippingDimentionCm = toCm(
      heightShippingDimentionValidation.valor,
      unitLongitudeShippingValidation.valor
    );
    let weightShippingDimentionKg = toKg(
      weightShippingDimentionValidation.valor,
      unitWeightShippingValidation.valor
    );
    let lengthProductDimentionCm = toCm(
      lengthProductDimentionValidation.valor,
      unitLongitudeProductValidation.valor
    );
    let widthProductDimentionCm = toCm(
      widthProductDimentionValidation.valor,
      unitLongitudeProductValidation.valor
    );
    let heightProductDimentionCm = toCm(
      heightProductDimentionValidation.valor,
      unitLongitudeProductValidation.valor
    );
    let weightProductDimentionKg = toKg(
      weightProductDimentionValidation.valor,
      unitWeightProductValidation.valor
    );

    function setData(key, validationValue, persistValue) {
      formDataAll.set(
        key,
        validationValue !== "" ? validationValue : persistValue
      );
    }
    formDataAll.set("internalBaseSku", internalBaseSku);
    formDataAll.set("brand", brandValidation.valor);
    formDataAll.set("model", modelValidation.valor);
    formDataAll.set("description", descripcionValidation.valor);
    formDataAll.set("upc", upcValidation.valor);
    formDataAll.set("longDescription", longDescriptionValidation.valor);
    formDataAll.set("satCode", satCodeValidation.valor);
    // formDataAll.set(
    //   "condition",
    //   conditionValidation.valor === null ? "" : conditionValidation.valor
    // );
    formDataAll.set(
      "unitCode",
      unitCodeValidation.valor === null ? "" : unitCodeValidation.valor.split(" ")[0]
    );
    formDataAll.set("video", videoValidation.valor);

    formDataAll.set(
      "productBaseShippingDimensions",
      JSON.stringify({
        length: lengthShippingDimentionCm,
        width: widthShippingDimentionCm,
        height: heightShippingDimentionCm,
        weight: weightShippingDimentionKg,
      })
    );
    formDataAll.set(
      "productBaseProductDimensions",
      JSON.stringify({
        length: lengthProductDimentionCm,
        width: widthProductDimentionCm,
        height: heightProductDimentionCm,
        weight: weightProductDimentionKg,
      })
    );
    // Recorrer el arreglo 'tags' y agregar cada elemento al FormData
    if (tags[0] !== "") {
      tags.map((tag, index) => {
        formDataAll.append(`tagsProductBase[]`, tag);
      });
    }

    formDataAll.set("attributesProductBase", attrbutesSend);
    formDataAll.delete("variationProduct[]");

    if(parentStates.length > 0){
      parentStates.forEach((variation) => {
        const value = Object.values(variation)[0];
        if (value?.status && isChecked) {
          formDataAll.append("variationProduct[]", JSON.stringify(value));
        }
      });
    }else{
      componentsFlag.forEach((variacion, index) => {
        let newVariation = variacion.state.props.data
        const value = Object.values(newVariation)[index];

        if (value?.status && isChecked) {
          formDataAll.append("variationProduct[]", JSON.stringify(value));
        }
      });
    }
    // setSelectNumberVariation(arr);
    // formDataAll.delete("productBaseVariation[]");

    if (isChecked) {
      formDataAll.delete("productBaseVariation[]");

      selectNumberVAriation.map((item) => {
        return formDataAll.append("productBaseVariation[]", item);
      });
    }
    addFilesToFormData();

    dispatch(setMensaje(null));
    setFlag(false);

    const action = producto
      ? actualizarProducto(formDataAll, cookies.csrf_access_token)
      : darAltaProducto(formDataAll, cookies.csrf_access_token);

    dispatch(action)
      .then(() => {
        // una vez que se hizo dispact verifico si hay errores
        if (mensajeAlert.severity == "success") {
          dispatch(resetPersistirDatosNuevoProducto());
        } else {
          formDataAll.delete("imageBaseToAdd[]");
          formDataAll.delete("urlBaseToAdd[]");
        }
      })
      .catch((error) => {
        console.error("Error al realizar la acción:", error);
      });


    // for (const [key, value] of formDataAll.entries()) {
    //   console.log(`${key}: ${value}`);
    // }
  };

  /*UseEffect */
  React.useEffect(() => {
    const fetchData = () => {
      /* dispatch(  obtenerAlmacenesDesdeProveedores())*/
    };
    fetchData();
  }, []);

  //Código para el manejo de tags

  const [tags, setTags] = useState(
    producto
      ? producto.productBaseTags.map((tag) => {
        return tag.tagValue;
      })
      : productPersist.tags
        ? productPersist.tags
        : ["",]
  );
  const [touched, setTouched] = useState(false);

  const handleAddTag = () => {
    if (tags.length < 3) {
      setTags([...tags, ""]);
    } else {
      setTags([...tags.slice(0, 3), "", ...tags.slice(3)]);
    }
  };

  const handleRemoveTag = (index) => {
    if (tags.length > 1) {
      const newTags = tags.filter((_, i) => i !== index);

      setTags(newTags);
      dispatch(persistirDatosNuevoProducto({ key: "tags", value: newTags }));
    }
  };

  const handleTagChange = (index, value) => {
    const newTags = [...tags];
    newTags[index] = value.slice(0, 50); // Limitar a 50 caracteres
    setTags(newTags);
    setTouched(true); // Marcar como interactuado
    dispatch(persistirDatosNuevoProducto({ key: "tags", value: newTags }));
  };

  const renderTagList = () => {
    const formattedTags = tags.filter((tag) => tag.trim() !== ""); // Filtrar campos vacíos
    return JSON.stringify(formattedTags);
  };

  //manejo de atributos
  // const [atributeList, setAtributeList] = useState([]);
  // const [atributeListShow, setAtributeListShow] = useState([]);

  // useEffect(() => {
  //   const objetoFinal = atributeListShow.reduce(
  //     (objetoResultante, objetoActual) => {
  //       const { name, value } = objetoActual;
  //       objetoResultante[name] = value;
  //       return objetoResultante;
  //     },
  //     {}
  //   );

  //   console.log(objetoFinal);

  //   formDataAll.set("attributesProductBase", JSON.stringify(objetoFinal));

  //   for (const [key, value] of formDataAll.entries()) {
  //     console.log(`${key}: ${value}`);
  //   }
  // }, [atributeListShow]);

  /*Manejar el return */
  return (
    <Box sx={{ mt: 1 }} width="100%">
      {producto && (
        <Typography
          variant="h6"
          sx={{
            fontStyle: "italic",
            fontWeight: "bold",
            backgroundColor: theme().palette.primary.main,
            color: "white",
            display: "inline",
            padding: "5px",
            borderRadius: "10px",
          }}
        >
          SKU interno:{" "}
          {internalBaseSku}
        </Typography>
      )}
      <Grid container spacing={2}>
        <Grid item xss={12} sm={6} md={2} >
          <TextField
            disabled={producto ? true : false}
            margin="normal"
            required
            fullWidth
            id={BRAND.nombre}
            name={BRAND.nombre}
            label={BRAND.label}
            autoFocus
            onChange={manejarCambioDeMarca}
            value={brandValidation.valor}
            color={brandValidation.color}
            error={brandValidation.error}
            helperText={brandValidation.textoAyuda}
            inputProps={{ maxLength: BRAND.longitud }}
          />
        </Grid>
        <Grid item xss={12} sm={6} md={2}>
          <TextField
            disabled={producto ? true : false}
            margin="normal"
            required
            fullWidth
            id={MODEL.nombre}
            name={MODEL.nombre}
            label={MODEL.label}
            onChange={manejarCambioDeModelo}
            value={modelValidation.valor}
            color={modelValidation.color}
            error={modelValidation.error}
            helperText={modelValidation.textoAyuda}
            inputProps={{ maxLength: MODEL.longitud }}
          />
        </Grid>
        <Grid item xss={12} sm={12} md={8}>
          <TextField
            margin="normal"
            required
            fullWidth
            id={DESCRIPTION.nombre}
            name={DESCRIPTION.nombre}
            label={DESCRIPTION.label}
            onChange={manejarCambioDeDescripcion}
            value={descripcionValidation.valor}
            color={descripcionValidation.color}
            error={descripcionValidation.error}
            helperText={descripcionValidation.textoAyuda}
            inputProps={{ maxLength: DESCRIPTION.longitud }}
          />
        </Grid>
       
        <Grid item xss={12} sm={12} md={2}>
          <TextField
            disabled={producto ? true : false}
            margin="normal"
            required
            fullWidth
            id={UPC.nombre}
            name={UPC.nombre}
            label={UPC.label}
            onChange={manejarCambioDeUpc}
            value={upcValidation.valor}
            color={upcValidation.color}
            error={upcValidation.error}
            helperText={upcValidation.textoAyuda}
            inputProps={{ maxLength: UPC.longitud }}
          />
        </Grid>
        <Grid item xss={12} sm={12} md={2}
          sx={{ display: "flex", alignItems: "center", marginTop: "8px" }}
        >
          <UntCode
            UNITCODE={UNITCODE}
            unitCodeValidation={unitCodeValidation}
            manejarCambioDeCodigoUnidad={manejarCambioDeCodigoUnidad}
          />
        </Grid>
        {/* <Grid item xs={12} sm={12} md={2}>
          <Autocomplete
            margin="normal"
            disablePortal
            fullWidth
            id={UNITCODE.nombre}
            options={UNITCODE.options}
            value={unitCodeValidation.valor}
            // value={productPersist?.unitCode}
            sx={{ width: "100%" }}
            onChange={manejarCambioDeCodigoUnidad}
            renderInput={(params) => (
              <TextField
                margin="normal"
                color={unitCodeValidation.color}
                error={unitCodeValidation.error}
                helperText={unitCodeValidation.textoAyuda}
                {...params}
                label={UNITCODE.label}
              />
            )}
          />
        </Grid> */}
        <Grid item xss={12} sm={12} md={2}>
          <TextField
            margin="normal"
            required
            fullWidth
            id={SATCODE.nombre}
            name={SATCODE.nombre}
            label={SATCODE.label}
            onChange={manejarCambioDeCodigoSAT}
            // value={satCodeValidation.valor}
            value={satCodeValidation.valor}
            color={satCodeValidation.color}
            error={satCodeValidation.error}
            helperText={satCodeValidation.textoAyuda}
            inputProps={{ maxLength: SATCODE.longitud }}
          />
        </Grid>

        <Grid item xss={12} sm={12} md={6}>
          <TextField
            margin="normal"
            fullWidth
            id={VIDEO.nombre}
            name={VIDEO.nombre}
            label={VIDEO.label}
            onChange={manejarCambioVideo}
            // value={videoValidation.valor}
            value={videoValidation.valor}
            color={videoValidation.color}
            error={videoValidation.error}
            helperText={videoValidation.textoAyuda}
            inputProps={{ maxLength: VIDEO.longitud }}
          />
        </Grid>

        <Grid item xss={12} sm={12} md={6}>
          <Grid container direction="column">

            <Grid item xss={12} sm={12} md={6}>
              <Box
                border={1}
                sx={{
                  marginBottom: "40px",
                  borderColor: "#c4c4c4",
                  borderRadius: "5px",
                }}
              >
                <Grid container spacing={1} padding={2}>
                  <Grid item xss={12} sm ={4}>
                    Dimensiones de envío
                  </Grid>

                  <Grid item xss={12} sm ={4}>
                    <Autocomplete
                      margin="normal"
                      disablePortal
                      fullWidth
                      id={UNITLONGITUDESHIPPING.nombre}
                      size="small"
                      options={UNITLONGITUDESHIPPING.options}
                      value={unitLongitudeShippingValidation.valor}
                      // value={
                      //   productPersist?.dimentionShipment?.unitLength != ""
                      //     ? productPersist.dimentionShipment.unitLength
                      //     : unitLongitudeShippingValidation.valor
                      // }
                      sx={{ width: "100%" }}
                      onChange={manejarCambioDeUnidadLongitudEnvio}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          color={unitLongitudeShippingValidation.color}
                          error={unitLongitudeShippingValidation.error}
                          helperText={
                            unitLongitudeShippingValidation.textoAyuda
                          }
                          {...params}
                          label={UNITLONGITUDESHIPPING.label}
                        />
                      )}
                    />
                  </Grid>
                  
                  <Grid item xss={12} sm={4} >
                    {/* checar */}
                    <Autocomplete
                      margin="normal"
                      disablePortal
                      fullWidth
                      id={UNITWEIGHTSHIPPING.nombre}
                      size="small"
                      options={UNITWEIGHTSHIPPING.options}
                      value={unitWeightShippingValidation.valor}
                      // value={
                      //   productPersist.dimentionShipment?.unitWeight != ""
                      //     ? productPersist.dimentionShipment.unitWeight
                      //     : unitLongitudeShippingValidation.valor
                      // }
                      sx={{ width: "100%" }}
                      onChange={manejarCambioDeUnidadPesoEnvio}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          color={unitWeightShippingValidation.color}
                          error={unitWeightShippingValidation.error}
                          helperText={unitWeightShippingValidation.textoAyuda}
                          {...params}
                          label={UNITWEIGHTSHIPPING.label}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xss={12} xs={3} >
                    <NumericFormat
                      margin="normal"
                      required
                      fullWidth
                      customInput={TextField}
                      id={LENGTHSHIPPING.nombre}
                      name={LENGTHSHIPPING.nombre}
                      label={LENGTHSHIPPING.label}
                      onChange={manejarCambioDeLargoEnvio}
                      value={lengthShippingDimentionValidation.valor}
                      color={
                        lengthShippingDimentionValidation.color}
                      error={lengthShippingDimentionValidation.error}
                      helperText={lengthShippingDimentionValidation.textoAyuda}
                      decimalScale={2}
                      decimalSeparator="."
                      fixedDecimalScale={true}
                      allowNegative={false}
                      isNumericString={true}
                      thousandSeparator={true}
                      prefix={""}
                      inputProps={{ min: 0 }}
                      />
                    {/* <TextField
                      margin="normal"
                      type="number"
                      required
                      fullWidth
                      inputProps={{ min: 0 }}
                      id={LENGTHSHIPPING.nombre}
                      name={LENGTHSHIPPING.nombre}
                      label={LENGTHSHIPPING.label}
                      onChange={manejarCambioDeLargoEnvio}
                      value={lengthShippingDimentionValidation.valor}
                      color={lengthShippingDimentionValidation.color}
                      error={lengthShippingDimentionValidation.error}
                      helperText={lengthShippingDimentionValidation.textoAyuda}
                    /> */}
                  </Grid>

                  <Grid item xss={12} xs={3}>
                    {/* <TextField
                      margin="normal"
                      type="number"
                      required
                      fullWidth
                      inputProps={{ min: 0 }}
                      id={WIDTHSHIPPING.nombre}
                      name={WIDTHSHIPPING.nombre}
                      label={WIDTHSHIPPING.label}
                      onChange={manejarCambioDeAnchoEnvio}
                      value={widthShippingDimentionValidation.valor}
                      // value={
                      //   productPersist?.dimentionShipment?.width &&
                      //   productPersist?.dimentionShipment?.width != ""
                      //     ? productPersist?.dimentionShipment?.width
                      //     : widthShippingDimentionValidation.valor
                      // }
                      color={widthShippingDimentionValidation.color}
                      error={widthShippingDimentionValidation.error}
                      helperText={widthShippingDimentionValidation.textoAyuda}
                    /> */}
                    <NumericFormat
                      margin="normal"
                      required
                      fullWidth
                      customInput={TextField}
                      id={WIDTHSHIPPING.nombre}
                      name={WIDTHSHIPPING.nombre}
                      label={WIDTHSHIPPING.label}
                      onChange={manejarCambioDeAnchoEnvio}
                      value={widthShippingDimentionValidation.valor}
                      color={
                        widthShippingDimentionValidation.color}
                      error={widthShippingDimentionValidation.error}
                      helperText={widthShippingDimentionValidation.textoAyuda}
                      decimalScale={2}
                      decimalSeparator="."
                      fixedDecimalScale={true}
                      allowNegative={false}
                      isNumericString={true}
                      thousandSeparator={true}
                      prefix={""}
                      inputProps={{ min: 0 }}
                      />
                  </Grid>
                  <Grid item xss={12} xs={3}>
                    {/* <TextField
                      margin="normal"
                      type="number"
                      required
                      fullWidth
                      inputProps={{ min: 0 }}
                      id={HEIGHTSHIPPING.nombre}
                      name={HEIGHTSHIPPING.nombre}
                      label={HEIGHTSHIPPING.label}
                      onChange={manejarCambioDeAltoEnvio}
                      value={heightShippingDimentionValidation.valor}
                      // value={
                      //   productPersist?.dimentionShipment?.height &&
                      //   productPersist?.dimentionShipment?.height != ""
                      //     ? productPersist?.dimentionShipment?.height
                      //     : heightShippingDimentionValidation.valor
                      // }
                      color={heightShippingDimentionValidation.color}
                      error={heightShippingDimentionValidation.error}
                      helperText={heightShippingDimentionValidation.textoAyuda}
                    /> */}
                    <NumericFormat
                      margin="normal"
                      required
                      fullWidth
                      customInput={TextField}
                      id={HEIGHTSHIPPING.nombre}
                      name={HEIGHTSHIPPING.nombre}
                      label={HEIGHTSHIPPING.label}
                      onChange={manejarCambioDeAltoEnvio}
                      value={heightShippingDimentionValidation.valor}
                      color={
                        heightShippingDimentionValidation.color}
                      error={heightShippingDimentionValidation.error}
                      helperText={heightShippingDimentionValidation.textoAyuda}
                      decimalScale={2}
                      decimalSeparator="."
                      fixedDecimalScale={true}
                      allowNegative={false}
                      isNumericString={true}
                      thousandSeparator={true}
                      prefix={""}
                      inputProps={{ min: 0 }}
                      />
                  </Grid>

                  <Grid item xss={12} xs={3}>
                    {/* <TextField
                      margin="normal"
                      type="number"
                      required
                      fullWidth
                      inputProps={{ min: 0 }}
                      id={WEIGHTSHIPPING.nombre}
                      name={WEIGHTSHIPPING.nombre}
                      label={WEIGHTSHIPPING.label}
                      onChange={manejarCambioDePesoEnvio}
                      value={weightShippingDimentionValidation.valor}
                      // value={
                      //   productPersist?.dimentionShipment?.weight &&
                      //   productPersist?.dimentionShipment?.weight != ""
                      //     ? productPersist?.dimentionShipment?.weight
                      //     : weightShippingDimentionValidation.valor
                      // }
                      color={weightShippingDimentionValidation.color}
                      error={weightShippingDimentionValidation.error}
                      helperText={weightShippingDimentionValidation.textoAyuda}
                    /> */}
                    <NumericFormat
                      margin="normal"
                      required
                      fullWidth
                      customInput={TextField}
                      id={WEIGHTSHIPPING.nombre}
                      name={WEIGHTSHIPPING.nombre}
                      label={WEIGHTSHIPPING.label}
                      onChange={manejarCambioDePesoEnvio}
                      value={weightShippingDimentionValidation.valor}
                      color={
                        weightShippingDimentionValidation.color}
                      error={weightShippingDimentionValidation.error}
                      helperText={weightShippingDimentionValidation.textoAyuda}
                      decimalScale={2}
                      decimalSeparator="."
                      fixedDecimalScale={true}
                      allowNegative={false}
                      isNumericString={true}
                      thousandSeparator={true}
                      prefix={""}
                      inputProps={{ min: 0 }}
                      />
                  </Grid>
                </Grid>
              </Box>
            </Grid>

            <Grid item xss={12} sm={12} lg={6}>
              <Box
                border={1}
                padding={2}
                sx={{
                  marginBottom: "40px",
                  borderColor: "#c4c4c4",
                  borderRadius: "5px",
                }}
              >
                <Grid container spacing={2}>
                  <Grid item xss={12} sm ={4}>
                    Dimensiones producto
                  </Grid>
                  <Grid item xss={12} sm ={4}>
                    <Autocomplete
                      margin="normal"
                      disablePortal
                      fullWidth
                      id={UNITLONGITUDEPRODUCT.nombre}
                      size="small"
                      options={UNITLONGITUDEPRODUCT.options}
                      value={unitLongitudeProductValidation.valor}
                      // value={
                      //   productPersist?.dimentionProduct?.unitLength &&
                      //   productPersist?.dimentionProduct?.unitLength != ""
                      //     ? productPersist?.dimentionProduct?.unitLength
                      //     : unitLongitudeProductValidation.valor
                      // }
                      sx={{ width: "100%" }}
                      onChange={manejarCambioDeUnidadLongitudProducto}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          color={unitLongitudeProductValidation.color}
                          error={unitLongitudeProductValidation.error}
                          helperText={unitLongitudeProductValidation.textoAyuda}
                          {...params}
                          label={UNITLONGITUDEPRODUCT.label}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xss={12} sm ={4}>
                    <Autocomplete
                      margin="normal"
                      disablePortal
                      fullWidth
                      id={UNITWEIGHTPRODUCT.nombre}
                      size="small"
                      options={UNITWEIGHTPRODUCT.options}
                      value={unitWeightProductValidation.valor}
                      sx={{ width: "100%" }}
                      onChange={manejarCambioDeUnidadPesoProducto}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          color={unitWeightProductValidation.color}
                          error={unitWeightProductValidation.error}
                          helperText={unitWeightProductValidation.textoAyuda}
                          {...params}
                          label={UNITWEIGHTPRODUCT.label}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xss={12} xs={3}>
                    {/* <TextField
                      margin="normal"
                      type="number"
                      required
                      fullWidth
                      inputProps={{ min: 0 }}
                      id={LENGTHPRODUCT.nombre}
                      name={LENGTHPRODUCT.nombre}
                      label={LENGTHPRODUCT.label}
                      onChange={manejarCambioDeLargoProducto}
                      value={lengthProductDimentionValidation.valor}
                      // value={
                      //   productPersist?.dimentionProduct?.length &&
                      //   productPersist?.dimentionProduct?.length != ""
                      //     ? productPersist?.dimentionProduct?.length
                      //     : lengthProductDimentionValidation.valor
                      // }
                      color={lengthProductDimentionValidation.color}
                      error={lengthProductDimentionValidation.error}
                      helperText={lengthProductDimentionValidation.textoAyuda}
                    /> */}
                    <NumericFormat
                      margin="normal"
                      required
                      fullWidth
                      customInput={TextField}
                      id={LENGTHPRODUCT.nombre}
                      name={LENGTHPRODUCT.nombre}
                      label={LENGTHPRODUCT.label}
                      onChange={manejarCambioDeLargoProducto}
                      value={lengthProductDimentionValidation.valor}
                      color={
                        lengthProductDimentionValidation.color}
                      error={lengthProductDimentionValidation.error}
                      helperText={lengthProductDimentionValidation.textoAyuda}
                      decimalScale={2}
                      decimalSeparator="."
                      fixedDecimalScale={true}
                      allowNegative={false}
                      isNumericString={true}
                      thousandSeparator={true}
                      prefix={""}
                      inputProps={{ min: 0 }}
                      />
                  </Grid>
                  <Grid item xss={12} xs={3}>
                    {/* <TextField
                      margin="normal"
                      type="number"
                      required
                      fullWidth
                      inputProps={{ min: 0 }}
                      id={WIDTHPRODUCT.nombre}
                      name={WIDTHPRODUCT.nombre}
                      label={WIDTHPRODUCT.label}
                      onChange={manejarCambioDeAnchoProducto}
                      value={widthProductDimentionValidation.valor}
                      // value={
                      //   productPersist?.dimentionProduct?.width &&
                      //   productPersist?.dimentionProduct?.width != ""
                      //     ? productPersist?.dimentionProduct?.width
                      //     : widthProductDimentionValidation.valor
                      // }
                      color={widthProductDimentionValidation.color}
                      error={widthProductDimentionValidation.error}
                      helperText={widthProductDimentionValidation.textoAyuda}
                    /> */}
                    <NumericFormat
                      margin="normal"
                      required
                      fullWidth
                      customInput={TextField}
                      id={WIDTHPRODUCT.nombre}
                      name={WIDTHPRODUCT.nombre}
                      label={WIDTHPRODUCT.label}
                      onChange={manejarCambioDeAnchoProducto}
                      value={widthProductDimentionValidation.valor}
                      color={
                        widthProductDimentionValidation.color}
                      error={widthProductDimentionValidation.error}
                      helperText={widthProductDimentionValidation.textoAyuda}
                      decimalScale={2}
                      decimalSeparator="."
                      fixedDecimalScale={true}
                      allowNegative={false}
                      isNumericString={true}
                      thousandSeparator={true}
                      prefix={""}
                      inputProps={{ min: 0 }}
                      />
                  </Grid>
                  <Grid item xss={12} xs={3}>
                    {/* <TextField
                      margin="normal"
                      type="number"
                      required
                      fullWidth
                      inputProps={{ min: 0 }}
                      id={HEIGHTPRODUCT.nombre}
                      name={HEIGHTPRODUCT.nombre}
                      label={HEIGHTPRODUCT.label}
                      onChange={manejarCambioDeAltoProducto}
                      value={heightProductDimentionValidation.valor}
                      // value={
                      //   productPersist?.dimentionProduct?.height &&
                      //   productPersist?.dimentionProduct?.height != ""
                      //     ? productPersist?.dimentionProduct?.height
                      //     : heightProductDimentionValidation.valor
                      // }
                      color={heightProductDimentionValidation.color}
                      error={heightProductDimentionValidation.error}
                      helperText={heightProductDimentionValidation.textoAyuda}
                    /> */}
                    <NumericFormat
                      margin="normal"
                      required
                      fullWidth
                      customInput={TextField}
                      id={HEIGHTPRODUCT.nombre}
                      name={HEIGHTPRODUCT.nombre}
                      label={HEIGHTPRODUCT.label}
                      onChange={manejarCambioDeAltoProducto}
                      value={heightProductDimentionValidation.valor}
                      color={
                        heightProductDimentionValidation.color}
                      error={heightProductDimentionValidation.error}
                      helperText={heightProductDimentionValidation.textoAyuda}
                      decimalScale={2}
                      decimalSeparator="."
                      fixedDecimalScale={true}
                      allowNegative={false}
                      isNumericString={true}
                      thousandSeparator={true}
                      prefix={""}
                      inputProps={{ min: 0 }}
                      />
                  </Grid>
                  <Grid item xss={12} xs={3}>
                    {/* <TextField
                      margin="normal"
                      type="number"
                      required
                      fullWidth
                      inputProps={{ min: 0 }}
                      id={WEIGHTPRODUCT.nombre}
                      name={WEIGHTPRODUCT.nombre}
                      label={WEIGHTPRODUCT.label}
                      onChange={manejarCambioDePesoProducto}
                      value={weightProductDimentionValidation.valor}
                      // value={
                      //   productPersist?.dimentionProduct?.weight &&
                      //   productPersist?.dimentionProduct?.weight != ""
                      //     ? productPersist?.dimentionProduct?.weight
                      //     : weightProductDimentionValidation.valor
                      // }
                      color={weightProductDimentionValidation.color}
                      error={weightProductDimentionValidation.error}
                      helperText={weightProductDimentionValidation.textoAyuda}
                    /> */}
                    <NumericFormat
                      margin="normal"
                      required
                      fullWidth
                      customInput={TextField}
                      id={WEIGHTPRODUCT.nombre}
                      name={WEIGHTPRODUCT.nombre}
                      label={WEIGHTPRODUCT.label}
                      onChange={manejarCambioDePesoProducto}
                      value={weightProductDimentionValidation.valor}
                      color={
                        weightProductDimentionValidation.color}
                      error={weightProductDimentionValidation.error}
                      helperText={weightProductDimentionValidation.textoAyuda}
                      decimalScale={2}
                      decimalSeparator="."
                      fixedDecimalScale={true}
                      allowNegative={false}
                      isNumericString={true}
                      thousandSeparator={true}
                      prefix={""}
                      inputProps={{ min: 0 }}
                      />
                  </Grid>
                </Grid>
              </Box>
            </Grid>
            
          </Grid>
        </Grid>

        <Grid item xs={12} md={6} sx={{ maxHeight: "530px", minHeight:"180px"
          , overflowY:{
            sm: "inherit",
            md: "auto",
          }, marginBottom:{
            xs: "2rem",
            sm: "2rem",
            md: "0rem",
          },
          paddingTop:{
            xs: "0rem ",
            sm: "0rem ",
          },

          '& .ql-container': {
            height: "100% !important",
            minHeight: {
              xss: "150px !important",
              md: "420px !important",
            }
          },

          minHeight:{
            xss: "150px !important",  
            sm: "150px !important",
            md: "100% !important",
          },
          
          
         }}>
          <ReactQuill
            required
            id={LONGDESCRIPTION.nombre}
            name={LONGDESCRIPTION.nombre}
            onChange={manejarCambioDeDescripcionLarga}
            value={longDescriptionValidation.valor}
            className={longDescriptionValidation.color}
            error={longDescriptionValidation.error}

            modules={{
              toolbar: [
                [{ header: [1, 2, 3, 4, 5, 6, false] }],
                ["bold", "italic", "underline", "strike", "blockquote"],
                [{ list: "ordered" }, { list: "bullet" }],
                ["link", "image"],
                ["clean"],
              ],
            }}
            /* style={{ height: "83.75%" }} */
          />
        </Grid>

        <Grid item xs={12}>
          <Box
            fullWidth
            border={1}
            padding={2}
            display="flex"
            flexDirection="column"
            sx={{
              marginBottom: "20px",
              borderColor: "#c4c4c4",
              borderRadius: "5px",
            }}
          >
            <Box display="flex" flexWrap="wrap">
              {tags.map((tag, index) => (
                <Box key={index} margin={2}>
                  <Box display="flex">
                    <TextField
                      label="Tag"
                      value={tag}
                      onChange={(e) => handleTagChange(index, e.target.value)}
                      error={touched && tag.trim() === ""} // Mostrar en rojo si el campo está vacío y se ha interactuado
                      helperText={
                        touched && tag.trim() === "" ? "Campo requerido" : ""
                      }
                    />
                  </Box>
                  {index !== 0 && (
                    <Box display="flex" justifyContent="center" marginTop={1}>
                      <Button
                        variant="outlined"
                        color="secondary"
                        onClick={() => handleRemoveTag(index)}
                        style={{ height: "30px", width: "210.4px" }}
                      >
                        Eliminar
                      </Button>
                    </Box>
                  )}
                </Box>
              ))}
            </Box>
            <Button variant="contained" color="primary" onClick={handleAddTag}>
              Agregar Tag
            </Button>
            {/* <Box marginTop={2}>Lista de etiquetas: {renderTagList()}</Box> prueba para mostrar los tags ingresados*/}
          </Box>
        </Grid>

        <AddAtribute
          atributeList={atributeList}
          atributeListShow={atributeListShow}
          setAtributeList={setAtributeList}
          setAtributeListShow={setAtributeListShow}
          producto={
            producto !== undefined ? producto.productBaseAttributes : null
          }
          attrbutesSend={attrbutesSend}
        />

        <Grid item xs={12}>
          <Box>
            <Typography gutterBottom variant="h6" component="div">
              Imágenes del producto
            </Typography>
          </Box>
        </Grid>

        <Grid item xs={12} sx={{ height: "180px" }}
        
        >
          <ImageUpdaterList />
        </Grid>
      </Grid>

      {/*<Dialog fullWidth={true} open={mensajeAlert?.severity == "success" ? true : false}>
        <ModalRedireccionProductos mensaje={mensajeAlert?.mensaje} />
        <p>1</p>
      </Dialog>*/}
    </Box>
  );
};

const FormularioNuevoProducto = (props) => {
  const [isChecked, setIsChecked] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [counter, setCounter] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const firstCharge = useRef(false);

  const loading = useSelector((store) => store.productos.loading);
  const estadoEliminandoProducto = useSelector(
    (store) => store.productos.estadoEliminandoProducto
  );
  const messageDeletingElementProduct = useSelector(
    (store) => store.productos.messageDeletingElementProduct
  );
  const dispatch = useDispatch();
  //sizeSet es el numero de componentes que se pueden crear
  const [sizeSet, setSizeSet] = useState(0);
  // console.log("sizeSet", sizeSet);
  // para activar el btn de eliminar
  const [openDelete, setOpenDelete] = React.useState(false);

  const productPersist = useSelector(
    (state) => state.productos.persevereDataNewProduct
  );

  const variations = useSelector(
    (store) => store.productos.variacionesProducto
  );
  const variationsFiltradas = useSelector(
    (store) => store.productos.productoFiltro
  );
  const variationsFiltradas2 = useSelector(
    (store) => { return store.productos.productoFiltro2 }
  );
  const [variationsFiltradasState, setVariationsFiltradasState] = useState()

  useEffect(() => {
    if (variationsFiltradas2?.length > 0) {
      setVariationsFiltradasState(variationsFiltradas2)
    }
  }, [variationsFiltradas2])



  const memoizedVariacionesFiltradas = useMemo(() => {
    const resultado = variationsFiltradas;
    return resultado;
  }, [variationsFiltradas]);

  console.log("memoizedVariacionesFiltradas", memoizedVariacionesFiltradas, variations, variationsFiltradas, variationsFiltradas2);

  const firstLoaded = (data) => {
    if (props?.producto) {
      dispatch(resetPersistirDatosNuevoProducto());
    }
    const fetchData = async () => {
      dispatch(obtenerVariacionProductos());
      dispatch(obtenerVariacionesFiltradas(""));
      dispatch(obtenerAllVariacionesFiltradas(""));
    };
    //si hay un producto para actualizar se activa el checkbox automaticamente
    // DEsde ahora siempre habra varicion
    if (props?.producto?.producto.productBase_CustomVariations.length > 0) {
      props?.producto.producto.productBase_CustomVariations.map((item) => {
        if (item.customVariation.customVariationId) {
          // setSelectNumberVariation([item.customVariation.customVariationId]);
          setSelectNumberVariation(prev => {
            return [...prev, item.customVariation.customVariationId]
          });
        }
      });
    }



    // in the first load, i call the function to handleAddComponent and this
    // function is the one that adds the first component
    if (!props?.producto?.producto.products && !firstCharge.current) {
      setIsChecked(true);
      setSelectNumberVariation([1]);
      handleAddComponent();
      forceRender({});
      firstCharge.current = true;
    }

    // setSelectNumberVariation(["Tamaño"]);
    // if (
    //   props.producto &&
    //   props.producto.producto?.products[0]?.productVariationCustom.length > 0
    // ) {
    //   // se activa para ver si hay
    //   setIsChecked(true);
    // }
    if (productPersist?.variations?.length > 0) {
      setIsChecked(true);
      setComponentsFlag(productPersist.componentsFlag);
      setParentStates(productPersist.variations);
    }
    if (productPersist?.selectedVariations.length > 0) {
      setSelectedVariations(productPersist.selectedVariations);
    }
    if (productPersist?.selectNumberVAriation?.length > 0) {
      let arr = [...productPersist.selectNumberVAriation];

      setSelectNumberVariation(arr);
    }
    fetchData();
    handleAddComponentSize();
  };

  useEffect(() => {
    firstLoaded();
  }, []);

  const {
    setFlag,
    selectedVariations,
    setSelectedVariations,
    formDataAll,
    addFilesToFormData,
    selectNumberVAriation,
    setSelectNumberVariation,
    parentStates,
    setParentStates,
    componentsFlag,
    setComponentsFlag,
  } = useContext(NewProductVariationsContext);

  let counterRef = useRef(counter);
  const parentStatesRef = useRef(parentStates);

  const mensajeAlert = useSelector((store) => store.productos.mensajeAlert);

  const [errsVariations, setErrsVariations] = useState([]);

  const handleSubmit = (event) => {
    let countVariationsEmpty = 0;
    //esta funcion se encarga de establecer una flag para que se ejecute el useEffect
    //que se encarga de enviar los datos al servidor en el componente hijo
    //tambien trae los datos de cada variacion y los agrega al formdata
    event.preventDefault();
    let textoErrores = "";
    if (selectedVariations.length === 0) {
      // textoErrores = textoErrores + "Debes seleccionar al menos una variación";
      textoErrores = concatErrors(
        textoErrores,
        "Debes seleccionar al menos una variación"
      );
    }
   
    if (parentStates?.length > 0) {
          parentStates.forEach((state) => {
            const value = Object.values(state)[0];
            if (!value?.status) return;
      
            if (value?.title === "") {
              textoErrores = concatErrors(
                textoErrores,
                `Debes ingresar un título para la variación`
              );
            }
      
            if (value?.variations === "") {
              textoErrores = concatErrors(
                textoErrores,
                `Debes ingresar un atributo de la variación`
              );
            }
      
            formDataAll.append("variationProduct[]", JSON.stringify(value));
          });
        } else {
          componentsFlag.forEach((variacion, index) => {
            let newVariation = variacion.state.props.data;
            const value = Object.values(newVariation)[index];
            if (!value?.status) return;
      
            if (value?.title === "") {
              textoErrores = concatErrors(
                textoErrores,
                `Debes ingresar un título para la variación ${index + 1}`
              );
            }
      
            if (value?.variations === "") {
              textoErrores = concatErrors(
                textoErrores,
                `Debes ingresar un atributo de la variación ${index + 1}`
              );
            }
      
            formDataAll.append("variationProduct[]", JSON.stringify(value));
          });
        }

  //  componentsFlag.forEach((variacion, index) => {
  //   let newVariation = variacion.state.props.data
  //     const value = Object.values(newVariation)[index];
  //     if (!value?.status) return;

  //     if (
  //       value?.title == "" &&
  //       value?.status
  //     ) {
  //       textoErrores = concatErrors(
  //         textoErrores,
  //         `Debes ingresar un título para la varicion ${index + 1}`
  //       );

  //       // textoErrores =
  //       //   textoErrores +
  //       //   `, Debes ingresar un título para la varicion ${index + 1}`;
  //     }
  //     if (
  //       value?.variations == "" &&
  //       value?.status
  //     ) {
  //       textoErrores = concatErrors(
  //         textoErrores,
  //         `Debes ingresar un atributo de la variacion ${index + 1}`
  //       );
  //     }
  //   });
    componentsFlag.forEach((flag) => {
      if (flag.flag) {
        countVariationsEmpty = countVariationsEmpty + 1;
      }
    });
    if (countVariationsEmpty === 0) {
      textoErrores = concatErrors(
        textoErrores,
        ` Debes ingresar al menos una variacion`
      );
    }

    if (textoErrores !== "") {
      dispatch(setMensaje({ mensaje: textoErrores, severity: "warning" }));
      return;
    }

    //bandera para definir que se active el componente hijo
    setFlag(true);
  };
  
  // const handleSubmit = (event) => {
  //   event.preventDefault();
  //   let countVariationsEmpty = 0;
  //   let textoErrores = "";
  
  //   if (selectedVariations.length === 0) {
  //     textoErrores = concatErrors(
  //       textoErrores,
  //       "Debes seleccionar al menos una variación"
  //     );
  //   }
  // debugger
  //   if (parentStates?.length > 0) {
  //     parentStates.forEach((state) => {
  //       const value = Object.values(state)[0];
  //       if (!value?.status) return;
  
  //       if (value?.title === "") {
  //         textoErrores = concatErrors(
  //           textoErrores,
  //           `Debes ingresar un título para la variación`
  //         );
  //       }
  
  //       if (value?.variations === "") {
  //         textoErrores = concatErrors(
  //           textoErrores,
  //           `Debes ingresar un atributo de la variación`
  //         );
  //       }
  
  //       formDataAll.append("variationProduct[]", JSON.stringify(value));
  //     });
  //   } else {
  //     componentsFlag.forEach((variacion, index) => {
  //       let newVariation = variacion.state.props.data;
  //       const value = Object.values(newVariation)[index];
  //       if (!value?.status) return;
  
  //       if (value?.title === "") {
  //         textoErrores = concatErrors(
  //           textoErrores,
  //           `Debes ingresar un título para la variación ${index + 1}`
  //         );
  //       }
  
  //       if (value?.variations === "") {
  //         textoErrores = concatErrors(
  //           textoErrores,
  //           `Debes ingresar un atributo de la variación ${index + 1}`
  //         );
  //       }
  
  //       formDataAll.append("variationProduct[]", JSON.stringify(value));
  //     });
  //   }
  
  //   componentsFlag.forEach((flag) => {
  //     if (flag.flag) {
  //       countVariationsEmpty += 1;
  //     }
  //   });
  
  //   if (countVariationsEmpty === 0) {
  //     textoErrores = concatErrors(
  //       textoErrores,
  //       `Debes ingresar al menos una variación`
  //     );
  //   }
  
  //   if (textoErrores !== "") {
  //     dispatch(setMensaje({ mensaje: textoErrores, severity: "warning" }));
  //     return;
  //   }
  
  //   if (isChecked) {
  //     formDataAll.delete("productBaseVariation[]");
  //     selectNumberVAriation.forEach((item) => {
  //       formDataAll.append("productBaseVariation[]", item);
  //     });
  //   }
  
  //   addFilesToFormData();
  //   dispatch(setMensaje(null));
  //   setFlag(true);
  
  //   const action = producto
  //     ? actualizarProducto(formDataAll, cookies.csrf_access_token)
  //     : darAltaProducto(formDataAll, cookies.csrf_access_token);
  
  //   dispatch(action)
  //     .then(() => {
  //       if (mensajeAlert.severity === "success") {
  //         dispatch(resetPersistirDatosNuevoProducto());
  //       } else {
  //         formDataAll.delete("imageBaseToAdd[]");
  //         formDataAll.delete("urlBaseToAdd[]");
  //       }
  //     })
  //     .catch((error) => {
  //       console.error("Error al realizar la acción:", error);
  //     });
  // };
  
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const clickInAlert = () => {
    dispatch(setMensaje(null));
  };

  const [, forceRender] = useState({});

  //creacion de estados dinamicos
  const addState = (number, product = "") => {
    // añade el objeto a cada variacion
    //tambien verifica si existe una variacion desde el back
    // para agregarla al arreglo de estados
    let arr = {};
    // para editar
    if (product != "" && product) {
      product.productVariationCustom.map((variation) => {
        arr[variation.customVariationValue.customVariation.customVariationId] = JSON.stringify(
          variation.customVariationValue.customVariationValueId
        );
      });
    }
    let data = {
      ["index" + number]: {
        index: number,
        title: product.variationDescription ? product.variationDescription : "",
        sku: product.variationSku ? product.variationSku : "",
        "urlToAdd[]": [],
        // [1:"2"]
        variations: product.productVariationCustom ? arr : "",
        status: true,
      },
    };

    // const dataAfter = [...parentStates, data];

    parentStates.push(data);
    // console.log(parentStates, [...parentStates, data], "ADADADAAD");

    // setParentStates(dataAfter);
    // setParentStates((prev) => [data, ...prev]);
    // setParentStates(data);
    parentStatesRef.current = [...parentStates];
    setParentStates(parentStatesRef.current);
    forceRender({});
  };

  // console.log("parentStates", parentStates, parentStatesRef.current);

  const makeDispatch = (componentFlag) => {
    if (props.producto) return;
    dispatch(
      persistirDatosNuevoProducto({ key: "variations", value: parentStates })
    );
    dispatch(
      persistirDatosNuevoProducto({
        key: "componentsFlag",
        value: componentsFlag,
      })
    );
  };

  const [expanded, setExpanded] = React.useState(false);

  const handleChangeAcc = () => {
    setExpanded((prevExpanded) => !prevExpanded);
  };


  const handleAddComponent = async (product = "") => {
    // añade el componente a un arreglo
    if (!isChecked && props.producto) {
      setIsChecked(true);
    }
    setCounter((prevCounter) => handleSizeParent() + 1);
    // setCounter((prevCounter) => prevCounter + 1);
    // counterRef.current = counterRef.current + 1;
    const parenARR = [...parentStates];

    // Obtener los índices actuales
    const indexs = parenARR.map((item) => {
      return Object.values(item)[0].index;
    });

    // Encontrar el primer índice faltante
    let missingIndex = null;
    for (let i = 1; i <= handleSizeParent(); i++) {
      if (!indexs.includes(i)) {
        missingIndex = i;
        break;
      }
    }
    // Si hay un índice faltante, úsalo, de lo contrario usa el siguiente índice disponible
    let aux;
    if (missingIndex !== null) {
      aux = missingIndex;
    } else {
      counterRef.current = handleSizeParent() + 1;
      aux = counterRef.current;
    }

    addState(aux, product);

    const newComponentFlag = {
      state: (
        <EstadosDinamicos
          counter={aux}
          product={product !== "" ? product : ""}
          data={parentStates[parentStates.length - 1]}
          expanded={expanded}
          handleChangeAcc={handleChangeAcc}
        />
      ),
      flag: true,
    };

    componentsFlag.push(newComponentFlag);
    // Filtrar los elementos con flag: true y ordenarlos por la propiedad counter
    const flaggedItems = componentsFlag.filter(item => item.flag)
      .sort((a, b) => a.state.props.counter - b.state.props.counter);

    // Mantener un índice para los elementos ordenados con flag: true
    let flaggedIndex = 0;

    // Crear una nueva copia del array original con los elementos ordenados
    const sortedComponentsFlag = componentsFlag.map(item => {
      if (item.flag) {
        return flaggedItems[flaggedIndex++];
      }
      return item;
    });

    // Actualizar el estado con la nueva copia ordenada
    setComponentsFlag(sortedComponentsFlag);
    //setComponentsFlag((prev) => [...prev, newComponentFlag]);
    makeDispatch(newComponentFlag);

    handleClose();
  };


  const passData = useRef(false);
  useEffect(() => {
    if (props.producto) {
      //añadiremos los componentes dinamicos que vienen al actualizar
      const data = props?.producto?.producto.products;
      if (data.length > 0 && !passData.current) {
        /*data.map((item) => {
          if (item.productVariationCustom.length > 0) {
            handleAddComponent(item);
          }
        });*/
        data.forEach((item) => {
          if (item.productVariationCustom.length > 0) {
            handleAddComponent(item);
          }
        })
        passData.current = true;
      }
    }
  }, [props?.producto?.producto?.products]);

  const handleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleChange = (event) => {
    // añade las variaciones seleccionadas a un arreglo, ej: ["color", "talla"]
   /*  setSelectedVariations(event.target.value);
    console.log(event.target.value, variations, variationsFiltradas);
    debugger
    // haciendo dispatch siempre y cuando se modifique
    dispatch(
      persistirDatosNuevoProducto({
        key: "selectedVariations",
        value: event.target.value,
      })
    ); */



  };

  //se va a ejecutar el useEffect cada vez que se seleccione una variacion
  useEffect(() => {
    if (
      memoizedVariacionesFiltradas != null ||
      memoizedVariacionesFiltradas != undefined
    ) {
      handleAddComponentSize();
    }
  }, [selectedVariations, memoizedVariacionesFiltradas]);

  const handleAddComponentSize = useCallback(() => {
    //la cantidad total de la muestra esta dada por
    //la cantidad de variaciones seleccionadas * la cantidad de valores de cada variacion ej: 2 * 3 = 6
    // donde la cantidad de variaciones seleccionadas es 2 ej verde y azul
    selectedVariations?.map((item) => {
      memoizedVariacionesFiltradas?.map((item2) => {
        if (item2.customVariationName === item) {
          const size  =  item2.customVariationValues?.length * selectedVariations.length - 1
          if ( handleSizeParent() <= sizeSet && sizeSet != 0 ){
            let aux = [...parentStates];
            let auxCompoents = [...componentsFlag];

            for (let i = size; i <= parentStates.length; i++) {

              if ( aux [i] !== undefined ){
                if (aux[i][`index${i}`] !== undefined) {
                aux[i][`index${i}`].status = false;
                }
              }
              
            }
            
            for (let i = size; i <= componentsFlag.length; i++) {
              if ( auxCompoents[i] !== undefined ){

              auxCompoents[i].flag = false;
              }
            }

            setParentStates(aux);
            setComponentsFlag(auxCompoents);
            

          }
          setSizeSet(
            size
          );
        }
      });
    });

  // console.log(memoizedVariacionesFiltradas, "memoizedVariacionesFiltradas222", selectedVariations);

  let variations = selectedVariations;
  let arr = [];
  let copy = [...parentStates];

  memoizedVariacionesFiltradas?.forEach((item) => {
    variations.forEach((item2) => {
      copy.forEach((item3, parentIndex) => {
        // Filtrar las propiedades de item3
        Object.entries(item3).forEach(([key, item4]) => {

          let exists = false;

          Object.keys(item4.variations).forEach((variationKey) => {
            if (item.customVariationId == +variationKey && selectedVariations.includes(item.customVariationName)) {
              exists = true;
            }
          });

          // Si no existe, eliminar el elemento de parentStates
          if (!exists) {
            Object.keys(item4.variations).forEach((variationKey) => {
              if (item.customVariationId == +variationKey) {
                delete item4.variations[variationKey];
              }
            });
            // delete parentStates[parentIndex][key];
          }
        });
      });
    });
  });

  setParentStates(copy);



  }, [selectedVariations, memoizedVariacionesFiltradas]);

  const [messageAlertVariations, setMessageAlertVariations] = useState({ message: null, severity: "" });
const [selectOpen, setSelectOpen] = useState(false);
const [loadingVariations, setLoadingVariations] = useState(false);
const variationsRef = useRef(null);

  const handleIdVariation = async (data) => {
    setSelectOpen(false);
    setLoadingVariations(true);
    //arreglo donde se guardan que ID de variacion se seleccionaron
    let arr = [...selectNumberVAriation];

    if(arr.includes(data)) {
      setSelectedVariations(selectedVariations.filter(item => item !== memoizedVariacionesFiltradas.find(v => v.customVariationId === data)?.customVariationName));
      setSelectNumberVariation(arr.filter(item => item !== data));
      setMessageAlertVariations({ message: "Variación eliminada", severity: "success" });
      setSelectOpen(false);
      setLoadingVariations(false);
      return;
    }

    let result =  null;

    if(variationsRef.current === null){
      result = await dispatch(obtenerAllVariacionesFiltradas());
      variationsRef.current = result;
    }else {
      result = variationsRef.current;
    }
    
    
    // Primero filtramos los elementos que tienen customVariationValues no vacíos
    const filteredResult = result.filter(item => item.customVariationValues.length > 0);

    // Obtenemos los IDs de los elementos filtrados
    const validIds = filteredResult.map(item => item.customVariationId);

    // Verificamos si el nuevo ID existe en validIds
    const isValidId = validIds.includes(data);
    if (isValidId) {
        if (!arr.includes(data)) {
            arr = [...arr, data];
          
        } else {
            arr = arr.filter((item) => item !== data);
            return;
        }
        setSelectedVariations(result.filter(item => arr.includes(item.customVariationId)).map(item => item.customVariationName));
        // haciendo dispatch siempre y cuando se modifique
        dispatch(
          persistirDatosNuevoProducto({
            key: "selectedVariations",
            value: event.target.value,
          })
        );
    
        const filteredArr = arr.filter(id => validIds.includes(id));

      
    
        setSelectNumberVariation(filteredArr);
        formDataAll.delete("productBaseVariation[]");
    
        dispatch(
            persistirDatosNuevoProducto({ key: "selectNumberVAriation", value: filteredArr })
        );
      setMessageAlertVariations({ message: "Variación agregada", severity: "success" });
        setSelectOpen(false);
      setLoadingVariations(false);

    }else{
      setMessageAlertVariations({ message: "Variación no tiene atributos", severity: "error" });
      setSelectOpen(false);
      setLoadingVariations(false);
        return;
    }

    // Filtramos arr para mantener solo los IDs que existen en validIds
 
};

  const openDeleteDialog = () => {
    dispatch(colocarEstadoEliminandoProducto(false));
    setOpenDelete(true);
  };

  const [isReset, setIsReset] = useState(false);

  //maneja el tamañlo de los componentes dinamicos para sacar
  //el tamaño total de la muestra
  const handleSizeParent = useCallback(() => {
    // i verify if the parentStates is empty and isReset is true so add the first variation
    // when the user click in the reset button
    if (isReset && parentStates.length == 0) {
      // setIsReset(false);
      botonRef.current.click();
    }
    let count = 0;
    parentStates.map((item, index) => {
      const value = Object.values(item)[0];
      if (value?.status) {
        count++;
      }
    });
    return count;
  }, [parentStates]);

  const [resetForm, setResetForm] = useState(false);
  const [resetAll, setResetAll] = useState(false);
  const botonRef = useRef(null);

  const handleResetForm = () => {
    // window.location.reload();
    setIsReset(true);
    dispatch(resetPersistirDatosNuevoProducto());

    setComponentsFlag([]);
    setParentStates([]);
    // setSizeSet(0);
    setIsChecked(true);
    setCounter(0);
    counterRef.current = 0;
    setIsCollapsed(false);
    setResetForm(true);
  };

  const navigate = useNavigate();

  const [dialogAddVariations, setDialogAddVariations] = useState(false);
  const [dialogWarning, setDialogWarning] = useState(false);

  const handleClickAddVariations = () => {
    setDialogAddVariations(true);
  }

  const exitAddCustomVariation = useSelector((store) => store.productos.exitAddCustomVariation);
  const existAddCustomVariationAttribute = useSelector((store) => store.productos.existAddCustomVariationAttribute);


  const [selectedCardIndex, setSelectedCardIndex] = useState(null);

  const addNewVariationName = (name) => {
    dispatch(
      addCustomVariation(name)
    );
  }
  const addNewVariationAttribute = (id, name) => {
    dispatch(
      addCustomVariationAttribute(id, name)
    );
    // setSelectedCardIndex(null);
  }

  const [title, setTitle] = useState('');
  const [attribute, setAttribute] = useState('');
  const [loader, setLoader] = useState({ title: false, attribute: false });
const themee = useTheme();

  if (props.product) {
    if (!memoizedVariacionesFiltradas || selectedVariations.length === 0) {
      return <h1>Cargando</h1>;
    }
  } else {
    return (
      <ImagesFormProvider producto={props.producto} resetForm={resetForm}>
        {props.producto == "status 500" ? (
          <h1>Hay algun error con este producto</h1>
        ) : (
          <>
            {variationsFiltradasState?.length > 0 ? (
              <DialogAddVariations dialogAddVariations={dialogAddVariations} addNewVariationName={addNewVariationName} setDialogAddVariations={setDialogAddVariations} variations={variations} memoizedVariacionesFiltradas={variationsFiltradasState} addNewVariationAttribute={addNewVariationAttribute} selectedCardIndex={selectedCardIndex} setSelectedCardIndex={setSelectedCardIndex} title={title} setTitle={setTitle} attribute={attribute} setAttribute={setAttribute} loader={loader} setLoader={setLoader} />
            ) : null}
            <DialogWarning setDialogWarning={setDialogWarning} dialogWarning={dialogWarning} />

            {/* {console.log(props)} */}
            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: "1em" }}>

              <Button
                variant=""
                size="small"
                startIcon={<FormatListBulletedIcon />}
                onClick={() => { setDialogWarning(true) }}
              >
                Administrar
              </Button>
              <Button
                variant=""
                size="small"
                startIcon={<RestartAltIcon />}
                onClick={handleResetForm}
                disabled={props?.producto}
              >
                Reset
              </Button>
            </Box>
            {/* <CircularProgress /> */}
            {isChecked ? (
              <Box sx={{ display: "flex", alignItems: "center", gap: "1em" }}>
                <AddCircleOutlineIcon onClick={handleClickAddVariations}
                  sx={(theme) => ({
                    // color: theme.palette.primary.main,
                    cursor: "pointer"
                  })}
                />

                <div className="col-5">
                  <Select
                    labelId="demo-mutiple-chip-label"
                    id="demo-mutiple-chip"
                    multiple
                    open={selectOpen}
                    onOpen={() => setSelectOpen(true)}
                    onClose={() => setSelectOpen(false)}
                    color={selectedVariations.length === 0 ? "error" : "primary"}
                    defaultValue={"Tamaño"}
                    value={selectedVariations}
                    onChange={(e) => handleChange(e)}
                    input={<Input id="select-multiple-chip" />}
                    disabled={
                        props.producto?.producto.products[0].productVariationCustom
                            .length > 0 || loadingVariations
                            ? true
                            : false
                    }
                    renderValue={(selected) => (
                        <div
                            style={{
                                display: "flex",
                                flexWrap: "wrap",
                                gap: "0.2rem",
                                alignItems: "center"
                            }}
                        >
                            {loadingVariations ? (
                                <CircularProgress size={20} sx={{ mr: 1 }} />
                            ) : null}
                            {selected.map((value) => (
                                <Chip
                                    key={value}
                                    label={value}
                                    style={{ margin: "2" }}
                                />
                            ))}
                        </div>
                    )}
                    MenuProps={{
                        PaperProps: {
                            style: {
                                maxHeight: 224,
                                width: 250,
                            },
                        },
                    }}
                  >
                    {memoizedVariacionesFiltradas != undefined ||
                    memoizedVariacionesFiltradas != null ? (
                        variations != null || variations != undefined ? (
                            variations?.map((option) =>
                                option.customVariationName == "Condición" ? null : (
                                    <MenuItem
                                        key={option.customVariationId}
                                        value={option.customVariationName}
                                        onClick={() => {
                                            handleIdVariation(option.customVariationId);
                                            setSelectOpen(false);
                                        }}
                                        style={{
                                            backgroundColor: selectedVariations.includes(option.customVariationName)
                                                ? themee.palette.mode == 'light' ? 'rgba(0, 0, 0, 0.1)' : themee.palette.background.paper
                                                : "none",
                                        }}
                                    >
                                        {option.customVariationName}
                                    </MenuItem>
                                )
                            )
                        ) : null
                    ) : (
                        <CircularProgress
                            style={{
                                margin: "auto",
                                display: "flex",
                                justifyContent: "center",
                            }}
                        />
                    )}
                  </Select>
                </div>
              </Box>
            ) : null}
            {isChecked ? (
              <>
                {" "}
                <div style={{ width: "100%"}}>
                  <Accordion expanded={expanded} onChange={handleChangeAcc}
                    slots={{ transition: Fade }}
                    slotProps={{ transition: { timeout: 400 } }}
                    sx={{
                      '& .MuiAccordion-region': { height: expanded ? 'auto' : 0 },
                      '& .MuiAccordionDetails-root': { display: expanded ? 'block' : 'none' },
                      width: {
                        xss: "95%",
                        sm: "100%",
                      }, margin: "2rem auto !important",borderRadius: "16px !important",
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls="panel1-content"
                      id="panel1-header"
                    >
                      <Typography sx={{ width: {
                        xss: "100%",
                        sm: "33%",
                        }, flexShrink: 0 }}>
                        Información Básica
                      </Typography>
                      <Typography 
                        sx={{ 
                          color: 'text.secondary',
                          display: {
                            xss: 'none',  // Oculto en pantallas extra pequeñas
                            sm: 'block'  // Visible desde pantallas pequeñas en adelante
                          }
                        }}
                      >
                        Información básica necesaria
                      </Typography>
                    </AccordionSummary>

                    <AccordionDetails>
                      <FormularioNuevoProductoCascaron
                        producto={props.producto}
                        isChecked={isChecked}
                        setResetForm={setResetForm}
                        resetForm={resetForm}
                      />
                      {/* <h1>HOla</h1> */}
                      {/* {handleReturnComponent()} */}
                      {/* </div> */}
                    </AccordionDetails>
                  </Accordion>


                </div>

                {/* mostramos el btn de borrar si hay alguna variacion */}
                {props.producto?.producto?.products[0]
                  .productVariationCustom ? (
                  <Fab
                    variant="extended"
                    color="error"
                    aria-label="eliminar"
                    style={{
                      position: "fixed",
                      bottom: 90,
                      right: 20,
                      zIndex: "99",
                      marginBottom: "5px",
                      height: "40px",
                    }}
                    onClick={() => openDeleteDialog()}
                  >
                    <DeleteForeverIcon sx={{ mr: 1 }} />
                    Eliminar Producto
                  </Fab>
                ) : null}

                <ConfirmarEliminarProducto
                  open={openDelete}
                  setOpen={setOpenDelete}
                  idElement={props.producto?.producto.internalBaseSku}
                  idName={"internalBaseSku"}
                  urlReturn={"/productos/consultar"}
                  nameElement={"producto"}
                  deleteElementFunction={eliminarProducto}
                  deleteElementState={estadoEliminandoProducto}
                  messageDeletingElement={messageDeletingElementProduct}
                />
              {/* AGREGAR NUEVO PRODUCTO BOTNO */}
                <Fab
                  // solo puede agregar si hay variaciones seleccionadas
                  disabled={
                    handleSizeParent() <= sizeSet && sizeSet != 0 ? false : true
                  }
                  
                  className= { handleSizeParent() <= sizeSet && sizeSet == 0 ? "greey" : "buttonGreenPink" }
                  variant="extended"
                  aria-label="add"
                  style={{
                    position: "fixed",
                    bottom: 20,
                    right: 20,
                    zIndex: "99",
                    marginBottom: "5px",
                    height: "40px",

                  }}
                  onClick={() =>
                    // solo puede agregar si hay variaciones seleccionadas
                    handleSizeParent() <= sizeSet && sizeSet != 0
                      ? handleAddComponent()
                      : null
                  }
                  ref={botonRef}
                >
                    <AddCircleOutlineIcon sx={{ mr: 1 }} />
                    Agregar Variacion
                </Fab>
                
                {componentsFlag?.map((component, index) =>
                  component.flag ? (
                    <Box sx={{
                      marginTop: "10px",
                    }}>
                      <ImagesVariationsFormProvider>
                          <Box sx={{mt: "2rem"}} key={index}>{component.state}</Box>
                      </ImagesVariationsFormProvider>
                    </Box>
                  ) : null
                )}

                <Box sx={{ display: "flex", justifyContent: "center", margin: "auto", width: "100%", 
                my:{
                  xss: "2rem",
                  md: "2rem",
                } , mb:{
                  xss: "5rem",
                  md: "5rem",
                  lg: "5rem",
                }
                }}>
                <LoadingButton
                  
                  variant="contained"
                  color="buttonGreenPink"
                  sx={{ mt: 2, width: {
                    xss: "95%",
                    md: "100%",
                  }
                    , margin: "auto !important",borderRadius: "16px !important", }}
                  loading={loading}
                  onClick={handleSubmit}
                >
                  {props.producto
                    ? "Actualizar producto"
                    : "Registrar producto"}
                </LoadingButton>
                </Box>

                <Dialog
                  open={mensajeAlert?.severity == "success" ? true : false}
                  fullWidth={true}
                >
                  <ModalRedireccionProductos mensaje={mensajeAlert?.mensaje} product={props.producto} />
                </Dialog>
              </>
            ) : (
              <FormularioNuevoProductoCascaron
                producto={props.producto}
                data={true}
                isChecked={isChecked}
                setResetForm={setResetForm}
                resetForm={resetForm}
              />
            )}
          </>
        )}
         {messageAlertVariations.message && (
        <AlertComponent
          color={messageAlertVariations.severity}
          message={messageAlertVariations.message}
          cleanMessage={() => setMessageAlertVariations({ message: null, severity: "" })}
          time={3000}
        />
      )}
      </ImagesFormProvider>
    );
  }
};
export default FormularioNuevoProducto;



// let variations = selectedVariations;
// let arr = [];
// memoizedVariacionesFiltradas?.forEach((item) => {
//   variations.forEach((item2) => {
//     parentStates.forEach((item3) => {
//       Object.values(item3).forEach((item4) => {
//         console.log(item4, "item4 completo", item, "item2");
    
//         Object.keys(item4.variations).forEach((key) => {
//           if(item.customVariationId == +key){
//           console.log(key, "Clave en variations"); // Claves como '1', '2', '3'
//           console.log(item4.variations[key], "Valor asociado a la clave"); // Valores como '1', '4', '8'}
//           }
//         });
//       });
//     });
    
//   });
// });
