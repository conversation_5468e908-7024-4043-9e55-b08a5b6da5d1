import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import ExitToAppIcon from "@mui/icons-material/ExitToApp";
import DeleteIcon from "@mui/icons-material/Delete";
import Alert from "@mui/material/Alert";
import AlertTitle from "@mui/material/AlertTitle";
import boteDeBasura from "../img/boteDeBasura.gif";
import done from "../img/done.gif";
import { useDispatch, useSelector } from "react-redux";
import { useCookies } from "react-cookie";
import { Navigate, useNavigate } from "react-router-dom";

const AlertDialog = ({
  open,
  setOpen,
  idElement,
  idName,
  urlReturn,
  nameElement,
  deleteElementFunction,
  deleteElementState,
  messageDeletingElement
}) => {
  const dispatch = useDispatch();
  const [cookies, setCookie] = useCookies();
  const navigate = useNavigate();

  //this is the function that is called when the user clicks the "delete" button
  //and redirects the user to the "consultar" page or close the dialog
  const handleCloseDeleteExit = () => {
    setOpen(false);
    navigate(urlReturn);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const manageDelete = (idElement) => {
    dispatch(deleteElementFunction(idElement, cookies.csrf_access_token));
  };

  const DeleteDialog = () => {
    let titulo = null;
    let content = null;
    let actions = null;
    let onCloseDialog = null;
    switch (deleteElementState) {
      case "500":
        titulo = 
        <Alert severity="error"> Ocurrió un error
        </Alert>;
        content = (
          <DialogContentText id="alert-dialog-description">
            {messageDeletingElement}
          </DialogContentText>
        );
        onCloseDialog = handleCloseDeleteExit
        break;
      case "404":
        titulo = <Alert severity="warning">
         <AlertTitle>Alerta</AlertTitle> 
          No se realizó ningún cambio</Alert>;
        content = (
          <DialogContentText id="alert-dialog-description">
            {messageDeletingElement}
          </DialogContentText>
        );
        onCloseDialog = handleCloseDeleteExit
        break;
      case "cargando":

        titulo = <Alert severity="info"> Espere por favor </Alert>;
        content = (
          <DialogContentText id="alert-dialog-description">
            <img
              src={boteDeBasura}
              style={{ height: "250px", width: "300px" }}
              alt=""
            />
          </DialogContentText>
        );
        break;
      case "exito":
        titulo = <Alert severity="success"> {messageDeletingElement} </Alert>;
        content = (
          <DialogContentText id="alert-dialog-description">
            <img
              src={done}
              style={{ height: "250px", width: "300px" }}
              alt=""
            />
          </DialogContentText>
        );
        actions = (
          <DialogActions>
            <Button
              variant="contained"
              color={"success"}
              onClick={handleCloseDeleteExit}
              endIcon={<ExitToAppIcon autoFocus />}
            >
              Aceptar
            </Button>
          </DialogActions>
        );
        break;
      default:
        titulo = (
          <Alert severity="error">
            {" "}
            <AlertTitle>Estas  seguro?</AlertTitle>
            {`Eliminar ${nameElement}`}
          </Alert>
        );
        content = (
          <DialogContentText id="alert-dialog-description">
            {`Se eliminara ${nameElement} con ${idName} `}
            <strong>{`${idElement}`}</strong>
            {" de forma permanente"}
          </DialogContentText>
        );
        actions = (
          <DialogActions>
            <Button
              variant="contained"
              color={"info"}
              onClick={handleClose}
              endIcon={<ExitToAppIcon autoFocus />}
            >
              Cancelar
            </Button>
            <Button
              variant="contained"
              color={"error"}
              onClick={() => manageDelete(idElement)}
              startIcon={<DeleteIcon />}
            >
              Eliminar
            </Button>
          </DialogActions>
        );
        onCloseDialog = handleClose
    }
    return (
      <Dialog
        open={open}
        onClose={onCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{titulo}</DialogTitle>
        <DialogContent>{content}</DialogContent>
        {actions}
      </Dialog>
    );
  };
  return <DeleteDialog />;
};

export default AlertDialog;
