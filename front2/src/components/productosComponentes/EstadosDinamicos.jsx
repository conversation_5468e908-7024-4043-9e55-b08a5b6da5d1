import { useState } from "react";
import IconButton from "@mui/material/IconButton";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import { Accordion, AccordionDetails, AccordionSummary, Autocomplete, Box, Collapse, TextField, Typography } from "@mui/material";
import "../css/variations.css";
import ImageUpdaterList from "./imageUpdaterComponentes/ImageUpdaterList";
import { ImagesVariationsFormContext } from "../../context/ImagesVariationsFormProvider";
import ImagesVariationsFormProvider from "../../context/ImagesVariationsFormProvider";
import React from "react";
import ImageVariationsUpdaterList from "./imageUpdaterComponentes/ImageVariationsUpdaterList";
import { EXTENSIONES_PERMITIDAS_IMAGENES_PRODUCTOS } from "../../Utils/config";
import { NewProductVariationsContext } from "../../context/NewProductVariationsProvider";
import { useEffect } from "react";
import { LoadingButton } from "@mui/lab";
import { useDispatch, useSelector } from "react-redux";
import { obtenerVariacionesFiltradas } from "../../redux/productosDucks";
import DeleteIcon from "@mui/icons-material/Delete";
import CircularProgress from "@mui/material/CircularProgress";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Fade from '@mui/material/Fade';
import Grid from '@mui/material/Grid';

// aqui se imprimen los inputs de las variaciones
const InputVariation = ({ counter, value, product, data, errsVariations, hasStores, handleDuplicatedVariation, setHandleDuplicatedVariation }) => {
  const dispatch = useDispatch();
  const {
    selectNumberVAriation,
    setSelectNumberVariation,
    parentStates,
    setParentStates,
  } = React.useContext(NewProductVariationsContext);
  const [optionsPrint, setOptionsPrint] = useState([]);

  const variationsFiltradas = useSelector(
    (store) => store.productos.productoFiltro
  );


  // necesario pq cada que quiero actualizar no se me muestran mis opciones
  useEffect(() => {
    if (variationsFiltradas && variationsFiltradas.length > 0) {
      handlevariation(variationsFiltradas);
    }
  }, [variationsFiltradas, selectNumberVAriation]);





  const handleInputChange = (event, newValue) => {
    if (newValue) {
      setSelectedValue(newValue); // Actualiza el estado del valor seleccionado
      // Lógica adicional aquí, si es necesario
    }
    let clave = `index${counter}`;
    let indice = parentStates.findIndex(function (elemento) {
      return elemento[clave]?.index === counter;
    });

    if (indice != -1) {
      let nuevoArreglo = [...parentStates];
      let data = {
        ...nuevoArreglo[indice][clave].variations,
      };
      let repeatVariations = [];
      parentStates?.map((variacion) => {
        const value = Object.values(variacion)[0];
        if (value?.status) {
          if (value?.variations == '') return
          repeatVariations.push(value?.variations);
        }
      });
      nuevoArreglo[indice][clave] = {
        ...nuevoArreglo[indice][clave],
        [newValue.id]: newValue[newValue.id],
        variations: {
          ...nuevoArreglo[indice][clave].variations, // Esto esparcirá las propiedades existentes, o será un objeto vacío si 'variations' es undefined
          [newValue.CustomVariationId]: JSON.stringify(
            newValue.customVariationValueId
          ),
        },
      };
      if (repeatVariations.length > 0) {
        let duplicated = repeatVariations.filter((item) => {
          let it = JSON.stringify(item);
          let it2 = JSON.stringify(nuevoArreglo[indice][clave].variations);
          let result = it === it2;
          return result;
        });

        if (duplicated.length > 0) {
          setHandleDuplicatedVariation(true);
        } else {
          setHandleDuplicatedVariation(false);
        }
      }

      setParentStates(nuevoArreglo);
    }
  };

  const handlevariation = (opcion) => {
    let clave = `index${counter}`;
    // creo un nuevo array donde esten los datos de las variaciones
    // ej : [{color: rojo, id: 1, CustomVariationId: 1, customVariationValueID: 1}]
    let datos = [{}];
    opcion.map((item) => {
      item.customVariationValues.map((item2) => {
        datos.push({
          [item.customVariationName]: item2.customVariationValue,
          ["id"]: item.customVariationName,
          ["CustomVariationId"]: item.customVariationId,
          ["customVariationValueId"]: item2.customVariationValueId,
          [clave]: clave,
        });
      });
    });
    setOptionsPrint(datos);
  };

  const handleVAriationAutocomplete = () => {
    const variacionName = product?.map((item) =>
      item["customVariationValue"]["customVariation"]["customVariationName"] === value
      && {
        [value]: item.customVariationValue.customVariationValue,
        CustomVariationId: item.customVariationValue.customVariation.customVariationId,
        customVariationValueId: item.customVariationValue.customVariationValueId,
        id: value,
        index1: `index${counter}`,
      }

    );

    Condición: "usado"
    CustomVariationId: 1
    customVariationValueId: 2
    id: "Condición"
    index1: "index1"

    // return "regreso";
    // return variacionName?["customVariationValue"]?["customVariationValue"];
    // Filtramos los elementos que no sean false
    const filteredVariacionName = variacionName.filter((item) => item !== false);
    // Retorna el primer elemento o una cadena vacía si no hay ninguno
    return filteredVariacionName[0] ? filteredVariacionName[0] : "";
  };

  const variacionNameReduxSelect = () => {
    const variacionName = data?.[`index${counter}`]?.[value];
    return variacionName ? variacionName : "";
  };

  const isOptionEqualToValue = (option, value) => {
    if (!value) return false; // Si el valor es null o undefined
    return option.id === value.id; // Comparar por la propiedad id
  };



  useEffect(() => {
    if (errsVariations) {
      setHandleDuplicatedVariation(true);
    }
  }, [errsVariations]);

  const [selectedValue, setSelectedValue] = useState(() => {
    return product ? handleVAriationAutocomplete() : variacionNameReduxSelect();
  });


  useEffect(() => {
    if (product) {
      const name = handleVAriationAutocomplete();

      setSelectedValue(handleVAriationAutocomplete());
    } else {
      setSelectedValue(variacionNameReduxSelect());
    }
  }, [product]);  // El efecto se ejecutará cuando cambie 'product'

  return (
    // <div style={{ paddingBottom: "1rem" }}>
    <div style={{ padding: ".84rem 0" }}>
      <div className="">
        <div style={{ width: "100%" }}>

          <Autocomplete
            disableClearable
            sx={{
              border: handleDuplicatedVariation ? "1px solid red" : undefined,
              borderRadius: handleDuplicatedVariation ? "8px" : undefined,
            }}
            id="combo-box-demo"
            options={optionsPrint.filter((option) => option["id"] === value)}
            getOptionLabel={(option) => option[value] || ""}
            value={selectedValue || null}  // Valor controlado
            onChange={handleInputChange}  // Maneja el cambio de valor
            isOptionEqualToValue={isOptionEqualToValue}
            disabled={hasStores}
            renderInput={(params) => (
              <TextField
                {...params}
                label={value}
                variant="outlined"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {!variationsFiltradas ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : (
                        params.InputProps.endAdornment
                      )}
                    </>
                  ),
                }}
              />
            )}
          />
          <span style={{ color: "#ff0000a8", fontSize: "10px" }}>
            {handleDuplicatedVariation ? "Esta variacion ya existe" : ""}
          </span>

        </div>
      </div>
    </div>
  );
};

// funcion donde estan los inputs de las variaciones
// y aqui se llama al InputVariation
const ProductVariation = ({
  counter,
  //son mis variaciones en caso de actualizar
  product,
  data,
  errsVariations
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [formData, setFormData] = useState();

  const {
    //SelectedVariations son las variaciones que se seleccionaron ej : [2, 1]
    selectedVariations,
    setSelectedVariations,
    componentsFlag,
    setComponentsFlag,
    parentStates,
    setParentStates,
  } = React.useContext(NewProductVariationsContext);
  const { setPhotosInitiales } = React.useContext(ImagesVariationsFormContext);
  useEffect(() => {
    if (product.productVariationCustom != null) {
      //estableciendo las fotos iniciales
      setPhotosInitiales(product.productPhotos);
      let auxSelectedVariations = [];

      product.productVariationCustom.forEach((item) => {
        if (
          !auxSelectedVariations.includes(
            item.customVariationValue.customVariation.customVariationName
          )
        ) {
          auxSelectedVariations.push(item.customVariationValue.customVariation.customVariationName);
        }
      });
      setSelectedVariations(auxSelectedVariations);
    }
    // setSelectedVariations([]);
  }, []);

  //condicion para ver si se duplica la variacion
  const [handleDuplicatedVariation, setHandleDuplicatedVariation] = useState(false);

  useEffect(() => {
    if (selectedVariations.length > 0) {
      let clave = `index${counter}`;
      let indice = parentStates.findIndex(function (elemento) {
        return elemento[clave]?.index === counter;
      });

      if (indice != -1) {
        let nuevoArreglo = [...parentStates];

        let repeatVariations = [];
        parentStates?.map((variacion) => {
          const value = Object.values(variacion)[0];
          let key = Object.keys(variacion)[0];
          if (value?.status) {
            if (value?.variations == '') return
            repeatVariations.push({
              ...value?.variations,
              "key": key
            })
          }
        });
        // nuevoArreglo[indice][clave] = {
        //   ...nuevoArreglo[indice][clave],
        //   [newValue.id]: newValue[newValue.id],
        //   variations: {
        //     ...nuevoArreglo[indice][clave].variations, // Esto esparcirá las propiedades existentes, o será un objeto vacío si 'variations' es undefined
        //     [newValue.CustomVariationId]: JSON.stringify(
        //       newValue.customVariationValueId
        //     ),
        //   },
        // };

        if (repeatVariations.length > 0) {
          let duplicated = repeatVariations.filter((item) => {
            if (clave === item.key) return
            // let s
            let { key, ...newObj } = item;
            // debugger
            let it = JSON.stringify(newObj);
            let it2 = JSON.stringify(nuevoArreglo[indice][clave].variations);
            let result = it === it2;
            return result;
          });

          if (duplicated.length > 0) {
            setHandleDuplicatedVariation(true);
          } else {
            setHandleDuplicatedVariation(false);
          }
        }

        // setParentStates(nuevoArreglo);

      }
      // setHandleDuplicatedVariation(false);
    }

  }, [selectedVariations, parentStates])




  const [titleVariation, setTitleVariation] = useState(
    product?.variationDescription
      ? product?.variationDescription
      : data[`index${counter}`]?.title != ""
        ? data[`index${counter}`].title
        : ""
  );
  const [skuVariation, setSkuVariation] = useState(
    product?.variationSku
      ? product.variationSku
      : data[`index${counter}`].sku != ""
        ? data[`index${counter}`].sku
        : ""
  );

  const handleInputChange = (event) => {
    const { name, value } = event.target;

    let clave = `index${counter}`;
    let indice = parentStates.findIndex(function (elemento) {
      return elemento[clave]?.index === counter;
    });
    if (indice != -1) {
      let nuevoArreglo = [...parentStates];
      nuevoArreglo[indice][clave][name] = value;
      setParentStates(nuevoArreglo);
    }
    //establecienndo el valor de los inputs
    if (name === "title") setTitleVariation(value);
    if (name === "sku") setSkuVariation(value);
  };

  const handleDeleteDadA = (counterDelete) => {
    const clave = `index${counter}`;
    let aux = [...componentsFlag];
    let auxIndex = [...parentStates];

    auxIndex.map((item, index) => {
      const value = Object.values(item)[0];
      const dataD = Object.values(data)[0];

      if (value.index === dataD.index) {
        Object.values(item)[0].status = false;
      }
    });
    const dataD = Object.values(data)[0];

    auxIndex = auxIndex.filter((item) => Object.values(item)[0].index !== dataD.index);
    aux.map((item, index) => {
      //  const dataa =  Object.values(item)[0]
      const dataD = Object.values(data)[0];
      if (item.state.props.counter === dataD.index) {
        item.flag = false;
      }
    });
    // aux = aux.filter((item) => item.state.props.counter !== counter);
    setParentStates(auxIndex);
    setComponentsFlag(aux);
  };

  //   let bdas =   selectedVariations == "Condición"
  //   ? ""
  //   : selectedVariations.join(" -").split("Condición -")[1] + "gola"
  // }`;

  const [expanded, setExpanded] = React.useState(false);

  const handleExpansion = () => {
    setExpanded((prevExpanded) => !prevExpanded);
  };


  const hasStores = product?.product_stores?.length > 0 ? true : false;


  return (

    <Accordion expanded={expanded} onChange={handleExpansion}
      slots={{ transition: Fade }}
      slotProps={{ transition: { timeout: 400 } }}
      sx={{
        '& .MuiAccordion-region': { height: expanded ? 'auto' : 0 },
        '& .MuiAccordionDetails-root': { display: expanded ? 'block' : 'none' },
        width: {
          xss: "95%",
          sm: "100%",
        }, margin: "auto !important", borderRadius: "16px !important",
        mt: "2rem"
      }}

    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="panel2-content"
        id="panel2-header"
      >
        <Typography sx={{ width: {
          xss: "100%",
          sm: "33%",
        }
        , flexShrink: 0 }}>{`Variacion ${counter} ${selectedVariations == "Condición"
          ? ""
          : selectedVariations
            .filter((item) => item !== "Condición")
            .join(" - ")
          }`}</Typography>

        <Box sx={{
          width: {
            xss: "100%",
          }
          , display: "flex", justifyContent: {
            xss: "end",
            sm: "space-between",
          }
        }}>

          <Typography
            sx={{
              color: 'text.secondary',
              display: {
                xss: 'none',  // Oculto en pantallas extra pequeñas
                sm: 'block'  // Visible desde pantallas pequeñas en adelante
              }
            }}
          >
            Información necesaria
          </Typography>

          <Box
            sx={{ display: 'flex', alignItems: 'center', position: "relative", right: {
              xss: "3rem",
              sm: "2rem",
            } }}
            // className="divIconDelete"
            onClick={() => {
              hasStores ? null :
                handleDeleteDadA(counter, product)
            }}
          >
            <DeleteIcon className="iconDelete" color={hasStores ? "disabled" : "error"} cursor={hasStores ? "not-allowed" : "pointer"}
            />
          </Box>

        </Box>

      </AccordionSummary>
      <AccordionDetails>
        <div className="containerInputs">
          
          <Grid container spacing={3}>
            <Grid item xss={12} sm={3} md={3}>
              {selectedVariations.map((value, index) => (
                <InputVariation
                  key={index + value}
                  counter={counter}
                  setFormData={setFormData}
                  formData={formData}
                  value={value}
                  parentStates={parentStates}
                  setParentStates={setParentStates}
                  product={product.productVariationCustom}
                  data={data}
                  errsVariations={errsVariations}
                  hasStores={hasStores}
                  handleDuplicatedVariation={handleDuplicatedVariation}
                  setHandleDuplicatedVariation={setHandleDuplicatedVariation}
                />
              ))}
            </Grid>

            <Grid item xss={12} sm={3} md={3} sx={{
              pt:{
                xss: "0rem !important",
                sm: "auto",
              }
            }}>
              <div className="form-group">
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="title"
                  label="Titulo"
                  onChange={handleInputChange}
                  color={titleVariation ? "success" : "error"}
                  inputProps={{ maxLength: 150 }}
                  value={titleVariation}
                />
              </div>
              <div className="form-group">
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="sku"
                  label="Sku"
                  onChange={handleInputChange}
                  color="primary"
                  inputProps={{ maxLength: 50 }}
                  value={skuVariation}
                />
              </div>
            </Grid>

            <Grid item xss={12} sm={6} md={6}>
              <ImageVariationsUpdaterList
                counter={counter}
                product={product != " " ? product : ""}
                isVariation={true}
              />
            </Grid>
          </Grid>

        </div>
      </AccordionDetails>
    </Accordion>
    // <div>
    //   <div
    //     className={
    //       isCollapsed ? "containerVariation" : "containerVariationClose"
    //     }
    //   >
    //     <div className={isCollapsed ? "barraVariacion" : "barraVariacionClose"}>
    //       <div onClick={() => setIsCollapsed(!isCollapsed)}>
    //         <h5 >{`Variacion ${counter} ${
    //           selectedVariations == "Condición"
    //             ? ""
    //             : selectedVariations
    //                 .filter((item) => item !== "Condición")
    //                 .join(" - ")
    //         }`}</h5>
    //       </div>
    //       <div className="containerDeleteIconArrow">
    //         {/* icono de borrar */}
    //         <div
    //           className="divIconDelete"
    //           onClick={() => handleDeleteDadA(counter)}
    //         >
    //           <DeleteIcon className="iconDelete" />
    //         </div>
    //         <IconButton
    //           onClick={() => setIsCollapsed(!isCollapsed)}
    //           aria-label="expand row"
    //           size="small"
    //         >
    //           {isCollapsed ? (
    //             <KeyboardArrowUpIcon />
    //           ) : (
    //             <KeyboardArrowDownIcon />
    //           )}
    //         </IconButton>
    //       </div>
    //     </div>
    //   </div>
    //   <div
    //     className="containerPrincipal"
    //     style={
    //       isCollapsed
    //         ? { width: "100%", margin: "auto" }
    //         : { display: "table-column" }
    //     }
    //   >
    //     <div className="containerInputs">
    //       <div className="row">
    //         <div className="col-3" style={{ height: "100%", margin: "auto" }}>
    //           {selectedVariations.map((value, index) => (
    //             <InputVariation
    //               key={index + value}
    //               counter={counter}
    //               setFormData={setFormData}
    //               formData={formData}
    //               value={value}
    //               parentStates={parentStates}
    //               setParentStates={setParentStates}
    //               product={product.productVariationCustom}
    //               data={data}
    //             />
    //           ))}
    //         </div>
    //         <div className="col-3">
    //           <div className="col">
    //             <div className="form-group">
    //               <TextField
    //                 style={{
    //                   backgroundColor: "white",
    //                   borderRadius: "4px",
    //                 }}
    //                 margin="normal"
    //                 required
    //                 fullWidth
    //                 name="title"
    //                 label="Titulo"
    //                 onChange={handleInputChange}
    //                 color={titleVariation ? "success" : "error"}
    //                 inputProps={{ maxLength: 50 }}
    //                 value={titleVariation}
    //               />
    //             </div>
    //           </div>
    //           <div className="col">
    //             <div className="form-group">
    //               <TextField
    //                 style={{
    //                   backgroundColor: "white",
    //                   borderRadius: "4px",
    //                 }}
    //                 margin="normal"
    //                 required
    //                 fullWidth
    //                 name="sku"
    //                 label="Sku"
    //                 onChange={handleInputChange}
    //                 color="primary"
    //                 inputProps={{ maxLength: 50 }}
    //                 value={skuVariation}
    //               />
    //             </div>
    //           </div>
    //         </div>
    //         <div className="col" style={{ margin: "auto" }}>
    //           <ImageVariationsUpdaterList
    //             counter={counter}
    //             product={product != " " ? product : ""}
    //           />
    //         </div>
    //       </div>
    //     </div>
    //   </div>
    //   {/* </Collapse> */}
    // </div>
  );
};

// funcion principal donde se llama mis otras funciones
const EstadosDinamicos = ({ counter, product, data, errsVariations }) => {
  //selectedVariations son mis titulos ej: ["1", "2"]
  return (
    <ProductVariation
      counter={counter}
      product={product != "" ? product : ""}
      //data son mis index ej {index1: {}, index2: {}}
      data={data}
      errsVariations={errsVariations}
    />
  );
};

export default EstadosDinamicos;
