import React from "react";
import TableContainer from "@mui/material/TableContainer";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import fantasma from "../img/fantasma.gif";
import TableCell from "@mui/material/TableCell";
import TableRow from "@mui/material/TableRow";
import { styled } from "@mui/material/styles";
import Grid from "@mui/material/Grid";
import Box from "@mui/material/Box";
import {
  Button,
  Card,
  Collapse,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid2,
  IconButton,
  Menu,
  MenuItem,
  Modal,
  Skeleton,
  TableHead,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import Fab from "@mui/material/Fab";
import ModeEditIcon from "@mui/icons-material/ModeEdit";
import Checkbox from "@mui/material/Checkbox";
import { useNavigate } from "react-router-dom";
import ConfirmarEliminarProducto from "./ConfirmarEliminarProducto";
import Stack from "@mui/material/Stack";
import {
  colocarEstadoEliminandoProducto,
  obtenerStockProductos,
  eliminarProducto,
  setMarketplacesImages,
} from "../../redux/productosDucks";
import { useDispatch, useSelector } from "react-redux";
import theme from "../../temaConfig";
import stylos from "../css/findBySku.module.css";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import { Cancel, Send } from "@mui/icons-material";
import { StyledTableCell } from "../StyledTableComponents";
import notImage from "../img/notImage.png";
import MarketplacesPublishList from "./MarketplacesPublishList";
import { useTheme } from "@mui/material/styles";
import { useMediaQuery } from "@mui/material";
import OpenInBrowserIcon from "@mui/icons-material/OpenInBrowser";
import CustomModal from "../componentesGenerales/CustomModal";
import { NumericFormat } from "react-number-format";

const AlertStack = styled(Stack)(({ colorborde, colorrelleno }) => {
  return {
    // border: "solid 2px #f5f5f5", // Valor predeterminado para el borde
    borderRadius: "6px",
    transition: "border-color 0.3s, background-color 0.3s", // Transición suave para animar cambios
    // padding: "0.5rem 0rem",
    padding: "0.5rem 0.2rem 0.5rem 0.2rem",
    ".iconColor": {
      color: colorborde,
    },

    "&:hover": {
      borderColor: colorborde, // Cambia el borde en estado hover
      backgroundColor: colorrelleno, // Cambia el fondo en estado hover
    },
  };
});

/*ErrorIcon *
'div'*/
const StyledIcon = styled("div")(({ theme }) => ({
  "&": {
    backgroundColor: "#44b700",
    color: "red",
    boxShadow: `0 0 0 2px ${theme().palette.background.paper}`,
    content: '"ii"',
    "&::after": {
      position: "relative",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      borderRadius: "50%",
      animation: "ripple 1.2s infinite ease-in-out",
      border: "1px solid currentColor",
      content: '""',
    },
  },
  "@keyframes ripple": {
    "0%": {
      transform: "scale(.8)",
      opacity: 1,
    },
    "100%": {
      transform: "scale(2.4)",
      opacity: 0,
    },
  },
}));

//stylos de mi tabla que envuelve mis productos
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(2n+1)": {
    backgroundColor: theme.palette.action.hover,
  },

  border: "1px solid #00387642",

  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

//stylos para mis productos con variaciones
const StyledTableRowVariations = styled(TableRow)(({ theme }) => ({
  backgroundColor: "#006ee80f",
  borderTop: "1px solid #b7b7b8",
  borderBottom: "1px solid #b7b7b8",

  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const ItemRight = styled("div")(() => ({
  textAlign: "right",
  boxShadow: "none",
}));

const Row = (props) => {
  const dispatch = useDispatch();
  const marketplaces = props.marketplaces;
  const { producto, allSelected } = props;
  const [seleccionIndividualCheckBox, setSeleccionIndividualCheckBox] =
    React.useState(allSelected);
  const almacenes = useSelector((store) => store.almacenes.almacenes);
  const [openMarketplaces, setOpenMarketplaces] = React.useState(false);

  const [selectedAlmacen, setSelectedAlmacen] = React.useState("");
  const [selectedTimeWarranty, setSelectedTimeWarranty] = React.useState("");
  const [selectedTime, setSelectedTime] = React.useState("");
  const productoRecargando = useSelector(
    (store) => store.productos.productoRecargando
  );

  const [open, setOpen] = React.useState(false);

  const navigate = useNavigate();

  const themeBreak = useTheme();
  const theme = useTheme();

  const isMediumScreen = useMediaQuery(themeBreak.breakpoints.down("md"));

  // abajo de 700
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  // abajo de 1280
  const isDownLargeScreen = useMediaQuery(theme.breakpoints.down("lg"));
  // entre 960 y 1060
  const isDowndetailOperationScreen = useMediaQuery(
    theme.breakpoints.down("detailOperationScreenNumber")
  );
  // abajo de 1600
  const isDown1600Screen = useMediaQuery(theme.breakpoints.down("config3"));

  const isSxScreen = useMediaQuery(theme.breakpoints.down("xs"));

  const goEdit = (internalBaseSku) => {
    navigate(`../editar/${internalBaseSku}`);
  };

  const handleCloseMarketplaces = () => {
    setOpenMarketplaces(false);
  };

  const openDeleteDialog = (internalBaseSku) => {
    dispatch(colocarEstadoEliminandoProducto(false));
    props.setOpen(true);
    props.setInternalSkuDialog(internalBaseSku);
  };

  const calcularTotalPorZonas = (almacenes) => {
    let zonaStockDict = {};
    let supplierStockDict = {};
    let costoMenor = null;
    let proveedorCostoMenor = null;
    let totalStock = 0;

    almacenes?.forEach((almacen) => {
      if (almacen.store.zone in zonaStockDict) {
        zonaStockDict[almacen.store.zone] =
          zonaStockDict[almacen.store.zone] + almacen.stock;
      } else {
        zonaStockDict[almacen.store.zone] = almacen.stock;
      }

      if (almacen.store.supplier in supplierStockDict) {
        supplierStockDict[almacen.store.supplier] =
          supplierStockDict[almacen.store.supplier] + almacen.stock;
      } else {
        supplierStockDict[almacen.store.supplier] = almacen.stock;
      }

      if (costoMenor === null) {
        costoMenor = almacen.cost;
        proveedorCostoMenor = almacen.store.supplier;
      } else {
        if (almacen.cost < costoMenor) {
          costoMenor = almacen.cost;
          proveedorCostoMenor = almacen.store.supplier;
        }
      }

      totalStock = totalStock + almacen.stock;
    });

    let zonaStock = [];
    for (let key in zonaStockDict) {
      let objetoZonaStock = {
        zone: key,
        stock: zonaStockDict[key],
      };
      zonaStock.push(objetoZonaStock);
    }

    let supplierStock = [];
    for (let key in supplierStockDict) {
      let objetoSupplierStock = {
        supplier: key,
        stock: supplierStockDict[key],
      };
      supplierStock.push(objetoSupplierStock);
    }
    return {
      zonaStock,
      supplierStock,
      costoMenor,
      proveedorCostoMenor,
      totalStock,
    };
  };

  //manejar peticion de stock almacen por producto
  const productStock = useSelector((store) => {
    return store.productos.productoIndividualStock;
  });

  const memoizedProductStock = React.useMemo(
    () => productStock,
    [productStock]
  );

  const [isLoading, setIsLoading] = React.useState(false);
  const [productStockPre, setProductStockPre] = React.useState([]);

  const prevProductStockLengthRef = React.useRef(memoizedProductStock.length);

  const handleMemoizedProductStockChange = React.useCallback(() => {
    if (memoizedProductStock.length !== prevProductStockLengthRef.current) {
      setIsLoading(false);
      prevProductStockLengthRef.current = memoizedProductStock.length;
    }
  }, [memoizedProductStock]);

  React.useEffect(() => {
    handleMemoizedProductStockChange();
  }, [memoizedProductStock, handleMemoizedProductStockChange]);

  const [anchorEl, setAnchorEl] = React.useState(null);
  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleDeployTable = (internalBaseSku) => {
    setOpen(!open);
    setIsLoading(true);
    if (open) return;
    if (productStock.length > 0) {
      const foundProduct = productStock.find((product) => {
        return product.sku === internalBaseSku;
      });
      if (foundProduct) {
        setProductStockPre(productStock);
        setIsLoading(false);
        return;
      }
    }
    setTimeout(() => {
      dispatch(obtenerStockProductos(producto.products[0].internalSku));
    }, 1000);
  };

  const setBackgroundColor = (productExternal) => {
    const zone =
      productExternal.supplierStore?.zone.zoneNumber || productExternal;

    if (zone === null) {
      return "#ffffff";
    }
    if (zone === 0) {
      return "#0080005c";
    }
    if (zone === 1) {
      return "#ffff0030";
    }
    if (zone === 2) {
      return "#ffa5003d";
    }
    if (zone >= 3) {
      return "#ff000047";
    }
  };

  const handleChangeCheckbox = (event) => {
    setSeleccionIndividualCheckBox(event.target.checked);
  };

  const [openModal, setOpenModal] = React.useState(false);

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const handleSendInfoToMarketplace = () => {
    setOpenModal(false);
    setSelectedAlmacen("");
    setSelectedTimeWarranty("");
    setSelectedTime("");
  };

  const [itemToPublish, setItemToPublish] = React.useState();
  const [marketToPublish, setMarketToPublish] = React.useState();

  const handleOpenModal = (item) => {
    setItemToPublish(item);
    setOpenModal(true);
  };
  if (productoRecargando === producto.internalBaseSku) {
    return (
      <React.Fragment>
        <StyledTableRow key={producto.internalBaseSku}>
          <StyledTableCell
            align="center"
            style={{
              paddingBottom: 0,
              paddingTop: 0,
              marginBottom: 0,
              marginTop: 0,
            }}
            colSpan={9}
          >
            <img src={fantasma} alt="" />
          </StyledTableCell>
        </StyledTableRow>
      </React.Fragment>
    );
  } else {
    const {
      zonaStock,
      supplierStock,
      costoMenor,
      proveedorCostoMenor,
      totalStock,
    } = 0;
    const stockBySupplier = producto?.stock?.stockBySupplier
      ? producto.stock.stockBySupplier
      : [];
    const stockByZone = producto?.stock?.stockByZone
      ? producto.stock.stockByZone
      : [];

    return producto.products && producto.products.length > 0 ? (
      <div className={stylos.divContainerProducts}>
        {/* //verifico si el producto tiene variaciones entpnces se agrega el titulo h3*/}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            width: "100%",
            padding: "1% 2% 0 2%",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: isSxScreen ? "column" : "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography
              variant="h6"
              style={{ margin: "5px" }}
            >
              {isMediumScreen
                ? producto.brand.length > 18
                  ? `${producto.brand.slice(0, 18)}...`
                  : producto.brand
                : producto.brand}
            </Typography>
            <Typography
              variant="h6"
              style={{ margin: "5px" }}
            >
              {isMediumScreen
                ? producto.model.length > 18
                  ? `${producto.model.slice(0, 18)}...`
                  : producto.model
                : producto.model}
            </Typography>
            <Typography
              variant="h6"
              key={producto.internalBaseSku}
              align="center"
            >
              {isMediumScreen
                ? producto.internalBaseSku.length > 18
                  ? `${producto.internalBaseSku.slice(0, 18)}...`
                  : producto.internalBaseSku
                : producto.internalBaseSku}
            </Typography>
            {!isSxScreen ? (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                }}
              >
                <Tooltip title="Editar" placement="top">
                  <Fab
                    size="small"
                    className="buttonGreenPink"
                    aria-label="edit"
                    onClick={() => goEdit(producto.internalBaseSku)}
                  >
                    <ModeEditIcon />
                  </Fab>
                </Tooltip>
              </Box>
            ) : null}
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: isSxScreen ? "column" : "row",
              justifyContent: "left",
              alignItems: "center",
            }}
          >
            {producto.productBasePhotos?.length > 0 ? (
              <img
                src={
                  producto.productBasePhotos[0]?.URLPhoto
                    ? producto.productBasePhotos[0]?.URLPhoto
                    : null
                }
                style={{
                  height: "100px",
                  width: "100px",
                }}
                alt={producto.description}
              />
            ) : (
              <img
                src={notImage}
                style={{
                  height: "100px",
                  width: "100px",
                }}
                alt={producto.description}
              />
            )}
            <Tooltip title={producto.description} placement="top">
              <Typography
                variant="subtitle2"
                style={{ margin: "1%", width: isSxScreen ? "100%" : "24%" }}
              >
                {producto.description.length > 150
                  ? `${producto.description.slice(0, 150)}...`
                  : producto.description}
              </Typography>
            </Tooltip>
            {producto.upc ? (
              <Typography
                variant="subtitle2"
                style={{ margin: "1%", width: isSxScreen ? "100%" : "24%" }}
              >
                UPC: {producto.upc}
              </Typography>
            ) : null}
            {producto.productBaseAttributes?.length > 0 ? (
              <Typography
                variant="subtitle2"
                style={{
                  margin: "1%",
                  width: isSxScreen ? "100%" : "24%",
                  display: isSxScreen ? "flex" : "block",
                }}
              >
                {producto.productBaseAttributes?.map((attribute) => {
                  return `●${attribute.attributeName}: ${attribute.attributeValue}`;
                })}
              </Typography>
            ) : null}
            {producto.productBaseProductDimensions?.length > 0 ? (
              <div
                style={{
                  margin: "1%",
                  width: isSxScreen ? "100%" : "14%",
                  display: isSxScreen ? "flex" : "block",
                }}
              >
                <Typography variant="subtitle2" sx={{ fontWeight: "bold" }}>
                  Dimensiones del producto
                </Typography>
                <Typography variant="subtitle2">
                  Alto: {producto.productBaseProductDimensions.height} cm
                </Typography>
                <Typography variant="subtitle2">
                  Largo: {producto.productBaseProductDimensions.length} cm
                </Typography>
                <Typography variant="subtitle2">
                  Ancho: {producto.productBaseProductDimensions.width} cm
                </Typography>
                <Typography variant="subtitle2">
                  Peso: {producto.productBaseProductDimensions.weight} kg
                </Typography>
              </div>
            ) : null}

            {producto.productBaseShippingDimensions?.length > 0 ? (
              <div
                style={{
                  margin: "1%",
                  width: isSxScreen ? "100%" : "14%",
                  display: isSxScreen ? "flex" : "block",
                }}
              >
                <Typography variant="subtitle2" sx={{ fontWeight: "bold" }}>
                  Dimensiones de envío
                </Typography>
                <Typography variant="subtitle2">
                  Alto: {producto.productBaseShippingDimensions.height} cm
                </Typography>
                <Typography variant="subtitle2">
                  Largo: {producto.productBaseShippingDimensions.length} cm
                </Typography>
                <Typography variant="subtitle2">
                  Ancho: {producto.productBaseShippingDimensions.width} cm
                </Typography>
                <Typography variant="subtitle2">
                  Peso: {producto.productBaseShippingDimensions.weight} kg
                </Typography>
              </div>
            ) : null}
          </Box>
        </Box>
        {producto.products[0]?.productVariationCustom?.length > 0 ? (
          <Typography
            variant="h4"
            key={producto.internalBaseSku}
            align="center"
          >
            Variacion del producto {producto.internalBaseSku}{" "}
          </Typography>
        ) : null}

        {/* //recorro el array de productos para mostrarlos */}
        {producto.products.map((item, index) => {
          return (
            <Table key={producto.internalBaseSku}>
              <TableBody>
                <StyledTableRow
                  sx={{
                    display: "flex",
                    flexDirection: isSmallScreen ? "column" : "row",
                  }}
                >
                  <StyledTableCell>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "100%",
                      }}
                    >
                      <IconButton
                        sx={{ margin: "auto", height: "35px" }}
                        aria-label="expand row"
                        size="small"
                        onClick={() =>
                          handleDeployTable(producto.products[0].internalSku)
                        }
                      >
                        <Checkbox
                          checked={seleccionIndividualCheckBox}
                          onChange={handleChangeCheckbox}
                        />
                        {open ? (
                          <KeyboardArrowUpIcon />
                        ) : (
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "center",
                              maxWidth: "40px",
                            }}
                          >
                            <Typography variant="h7" sx={{ fontSize: "14px" }}>
                              Stock
                            </Typography>
                            <KeyboardArrowDownIcon />
                          </Box>
                        )}
                      </IconButton>
                    </Box>
                  </StyledTableCell>

                  {!isDownLargeScreen || isDowndetailOperationScreen ? (
                    <StyledTableCell>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          height: "100%",
                        }}
                      >
                        {item.productPhotos?.length > 0 ? (
                          <img
                            src={
                              item.productPhotos[0]?.URLPhoto
                                ? item.productPhotos[0]?.URLPhoto
                                : null
                            }
                            style={{
                              height: "100px",
                              width: "100px",
                            }}
                            alt={producto.description}
                          />
                        ) : (
                          <img
                            src={notImage}
                            style={{
                              height: "100px",
                              width: "100px",
                            }}
                            alt={producto.description}
                          />
                        )}
                      </Box>
                    </StyledTableCell>
                  ) : null}

                  <StyledTableCell sx={{ padding: 0, maxWidth: "250px" }}>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "start",
                        height: "100%",
                        flexDirection: "column",
                        minWidth: "210px",
                      }}
                    >
                      {console.log("item", item)}
                      <Typography
                        title={producto.products[index].internalSku}
                        variant="h6"
                        sx={{
                          fontSize: "18px",
                          fontWeight: "bold",
                          fontStyle: "italic",
                        }}
                      >
                        {producto.products[index].internalSku.length > 18
                          ? `${producto.products[index].internalSku.slice(
                              0,
                              18
                            )}...`
                          : producto.products[index].internalSku}
                      </Typography>
                      {producto.upc ? (
                        <p style={{ margin: 0 }}>UPC: {producto.upc}</p>
                      ) : null}
                      <p style={{ margin: 0 }}>
                        {item.productVariationCustom
                          ? item.productVariationCustom.map(
                              (variation, index) => {
                                return `${index !== 0 ? " - " : "Variación:"} ${
                                  variation.customVariationValue
                                    .customVariationValue
                                }`;
                              }
                            )
                          : null}
                      </p>
                      <p style={{ margin: 0 }}>
                        {producto.variantDescription
                          ? producto.variantDescription
                          : null}
                      </p>
                    </Box>
                  </StyledTableCell>

                  {!isDowndetailOperationScreen ? (
                    <StyledTableCell sx={{ padding:0 }}>
                      <Box
                        sx={{
                          padding:0,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          height: "100%",
                        }}
                      >
                        <MarketplacesPublishList
                          producto={item}
                          setAnchorEl={setAnchorEl}
                          setMarketToPublish={setMarketToPublish}
                          marketplaces={marketplaces}
                        />
                        <Menu
                          id="simple-menu"
                          anchorEl={anchorEl}
                          keepMounted
                          open={Boolean(anchorEl)}
                          onClose={handleClose}
                        >
                          <MenuItem
                            key={1}
                            onClick={() => handleOpenModal(item)}
                          >
                            Publicar
                          </MenuItem>
                          <MenuItem key={2} disabled={true}>
                            Editar publicación en marketplace
                          </MenuItem>
                          <MenuItem key={3} disabled={true}>
                            Ver publicación en marketplace
                          </MenuItem>
                        </Menu>
                        <Modal
                          open={openModal}
                          onClose={handleCloseModal}
                          aria-labelledby="modal-complete-info-to-post"
                          aria-describedby="modal-complete-info-to-post-to-marketplace"
                        >
                          <Box
                            sx={{
                              position: "absolute",
                              top: "50%",
                              left: "50%",
                              transform: "translate(-50%, -50%)",
                              width: "30%",
                              bgcolor: "background.paper",
                              border: "2px solid #000",
                              borderRadius: "10px",
                              boxShadow: 24,
                              p: 4,
                            }}
                          >
                            <Typography
                              id="modal-title"
                              variant="h6"
                              component="h2"
                              sx={{ marginBottom: "2%" }}
                            >
                              Llena la información para publicar el producto en
                              marketplace
                            </Typography>
                            <Grid container spacing={2} direction={"row"}>
                              <Grid item xs={12}>
                                <TextField
                                  id="outlined-select-"
                                  select
                                  label="Almacen del stock"
                                  value={selectedAlmacen}
                                  onChange={(e) => {
                                    setSelectedAlmacen(e.target.value);
                                  }}
                                  sx={{ width: "100%" }}
                                >
                                  {almacenes.map((option) => (
                                    <MenuItem
                                      key={option.storeId}
                                      value={option.storeId}
                                    >
                                      {option.storeName}
                                    </MenuItem>
                                  ))}
                                </TextField>
                              </Grid>
                              {itemToPublish &&
                              marketToPublish !== "claroShop" ? (
                                <>
                                  <Grid item xs={12}>
                                    <TextField
                                      id="outlined-basic"
                                      select
                                      label="Tiempo garantía"
                                      value={selectedTimeWarranty}
                                      onChange={(e) => {
                                        setSelectedTimeWarranty(e.target.value);
                                      }}
                                      sx={{ width: "100%" }}
                                    >
                                      <MenuItem key={1} value={1}>
                                        Día(s)
                                      </MenuItem>
                                      <MenuItem key={2} value={2}>
                                        Mes(es)
                                      </MenuItem>
                                      <MenuItem key={3} value={3}>
                                        Año(s)
                                      </MenuItem>
                                    </TextField>
                                  </Grid>
                                  <Grid item xs={12}>
                                    {/* <TextField
                                      id="outlined-basic"
                                      label="Cantidad de tiempo"
                                      type="number"
                                      sx={{ width: "100%" }}
                                      value={selectedTime}
                                      onChange={(e) => {
                                        const input = e.target.value;
                                        // Verificar si el valor ingresado es un número válido y no negativo
                                        if (
                                          /^\d*$/.test(input) &&
                                          input >= 0 &&
                                          input.length <= 3
                                        ) {
                                          setSelectedTime(input);
                                        }
                                      }}
                                    /> */}
                                    <NumericFormat
                                      customInput={TextField}
                                      label="Cantidad de tiempo"
                                      value={selectedTime}
                                      onValueChange={(values) => {
                                        const { formattedValue, value } = values;
                                        if (
                                          /^\d*$/.test(value) &&
                                          value >= 0 &&
                                          value.length <= 3
                                        ) {
                                          setSelectedTime(value);
                                        }
                                      }}
                                      thousandSeparator={true}
                                      decimalScale={0}
                                      fixedDecimalScale={false}
                                      allowNegative={false}
                                      isNumericString
                                      type="text"
                                      placeholder="Cantidad de tiempo"
                                      sx={{ width: "100%" }}
                                      InputProps={{
                                        inputProps: {
                                          maxLength: 3,
                                        },
                                      }}
                                      InputLabelProps={{
                                        shrink: true,
                                      }}
                                      variant="outlined"
                                      />
                                  </Grid>
                                </>
                              ) : null}

                              <Grid
                                item
                                xs={12}
                                display={"flex"}
                                justifyContent={"space-evenly"}
                              >
                                <Button
                                  variant="outlined"
                                  color="primary"
                                  startIcon={<Cancel />}
                                  onClick={handleCloseModal}
                                >
                                  Cancelar
                                </Button>
                                <Button
                                  variant="contained"
                                  color="primary"
                                  startIcon={<Send />}
                                  onClick={handleSendInfoToMarketplace}
                                >
                                  Publicar
                                </Button>
                              </Grid>
                            </Grid>
                          </Box>
                        </Modal>
                        <IconButton onClick={() => setOpenMarketplaces(true)}>
                          <OpenInBrowserIcon />
                        </IconButton>

                        <Dialog
                          open={openMarketplaces}
                          onClose={handleCloseMarketplaces}
                          PaperProps={{
                            classes: {
                              borderRadius: "24px",
                              width: "700px",
                              height: "500px",
                            },
                          }}
                        >
                          <DialogTitle>Marketplaces</DialogTitle>
                          <DialogContent>
                            <MarketplacesPublishList
                              producto={item}
                              setAnchorEl={setAnchorEl}
                              setMarketToPublish={setMarketToPublish}
                              marketplaces={marketplaces}
                              full={true}
                            />                        <Menu
                            id="simple-menu"
                            anchorEl={anchorEl}
                            keepMounted
                            open={Boolean(anchorEl)}
                            onClose={handleClose}
                          >
                            <MenuItem
                              key={1}
                              onClick={() => handleOpenModal(item)}
                            >
                              Publicar
                            </MenuItem>
                            <MenuItem key={2} disabled={true}>
                              Editar publicación en marketplace
                            </MenuItem>
                            <MenuItem key={3} disabled={true}>
                              Ver publicación en marketplace
                            </MenuItem>
                          </Menu>
                          </DialogContent>
                        </Dialog>
                      </Box>
                    </StyledTableCell>
                  ) : null}

                  {!isDown1600Screen ||
                  (isDowndetailOperationScreen && !isMediumScreen) ? (
                    <>
                      <StyledTableCell
                        sx={{
                          padding: 0,
                          width: "200px",
                          alignItems: "center",
                        }}
                      >
                        <Card
                          sx={{
                            padding: "10px",
                            border: "1px solid",
                            borderRadius: "10px",
                            borderColor: "primary",
                          }}
                        >
                          <Typography variant="h6" sx={{ fontSize: "14px" }}>
                            Publicado en: 6
                          </Typography>
                          <Typography variant="h6" sx={{ fontSize: "14px" }}>
                            Pausado en: 0
                          </Typography>
                          <Typography variant="h6" sx={{ fontSize: "14px" }}>
                            Sin publicar en: 0
                          </Typography>
                        </Card>
                      </StyledTableCell>

                      <StyledTableCell
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          padding: 0,
                          maxWidth: "250px",
                        }}
                      >
                        <Card
                          sx={{
                            padding: "10px",
                            border: "1px solid",
                            borderRadius: "10px",
                            borderColor: "primary",
                          }}
                        >
                          <Typography variant="h6" sx={{ fontSize: "14px" }}>
                            Rentabilidad promedio: 15%
                          </Typography>
                          <Typography variant="h6" sx={{ fontSize: "14px" }}>
                            Precio promedio: {(1000).toLocaleString('es-MX', {style: 'currency', currency: 'MXN'})}
                          </Typography>
                          <Typography variant="h6" sx={{ fontSize: "14px" }}>
                            Stock bajo en: 0
                          </Typography>
                        </Card>
                      </StyledTableCell>
                    </>
                  ) : null}
                </StyledTableRow>

                {isDowndetailOperationScreen ? (
                  <StyledTableRow>
                    <StyledTableCell sx={{ padding: 0 }}>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          height: "100%",
                        }}
                      >
                        <MarketplacesPublishList
                          producto={item}
                          setAnchorEl={setAnchorEl}
                          setMarketToPublish={setMarketToPublish}
                          marketplaces={marketplaces}
                        />
                        <Menu
                          id="simple-menu"
                          anchorEl={anchorEl}
                          keepMounted
                          open={Boolean(anchorEl)}
                          onClose={handleClose}
                        >
                          <MenuItem
                            key={1}
                            onClick={() => handleOpenModal(item)}
                          >
                            Publicar
                          </MenuItem>
                          <MenuItem key={2} disabled={true}>
                            Editar publicación en marketplace
                          </MenuItem>
                          <MenuItem key={3} disabled={true}>
                            Ver publicación en marketplace
                          </MenuItem>
                        </Menu>
                        <Modal
                          open={openModal}
                          onClose={handleCloseModal}
                          aria-labelledby="modal-complete-info-to-post"
                          aria-describedby="modal-complete-info-to-post-to-marketplace"
                        >
                          <Box
                            sx={{
                              position: "absolute",
                              top: "50%",
                              left: "50%",
                              transform: "translate(-50%, -50%)",
                              width: "30%",
                              bgcolor: "background.paper",
                              border: "2px solid #000",
                              borderRadius: "10px",
                              boxShadow: 24,
                              p: 4,
                            }}
                          >
                            <Typography
                              id="modal-title"
                              variant="h6"
                              component="h2"
                              sx={{ marginBottom: "2%" }}
                            >
                              Llena la información para publicar el producto en
                              marketplace
                            </Typography>
                            <Grid container spacing={2} direction={"row"}>
                              <Grid item xs={12}>
                                <TextField
                                  id="outlined-select-"
                                  select
                                  label="Almacen del stock"
                                  value={selectedAlmacen}
                                  onChange={(e) => {
                                    setSelectedAlmacen(e.target.value);
                                  }}
                                  sx={{ width: "100%" }}
                                >
                                  {almacenes.map((option) => (
                                    <MenuItem
                                      key={option.storeId}
                                      value={option.storeId}
                                    >
                                      {option.storeName}
                                    </MenuItem>
                                  ))}
                                </TextField>
                              </Grid>
                              {itemToPublish &&
                              marketToPublish !== "claroShop" ? (
                                <>
                                  <Grid item xs={12}>
                                    <TextField
                                      id="outlined-basic"
                                      select
                                      label="Tiempo garantía"
                                      value={selectedTimeWarranty}
                                      onChange={(e) => {
                                        setSelectedTimeWarranty(e.target.value);
                                      }}
                                      sx={{ width: "100%" }}
                                    >
                                      <MenuItem key={1} value={1}>
                                        Día(s)
                                      </MenuItem>
                                      <MenuItem key={2} value={2}>
                                        Mes(es)
                                      </MenuItem>
                                      <MenuItem key={3} value={3}>
                                        Año(s)
                                      </MenuItem>
                                    </TextField>
                                  </Grid>
                                  <Grid item xs={12}>
                                    {/* <TextField
                                      id="outlined-basic"
                                      label="Cantidad de tiempo"
                                      type="number"
                                      sx={{ width: "100%" }}
                                      value={selectedTime}
                                      onChange={(e) => {
                                        const input = e.target.value;
                                        // Verificar si el valor ingresado es un número válido y no negativo
                                        if (
                                          /^\d*$/.test(input) &&
                                          input >= 0 &&
                                          input.length <= 3
                                        ) {
                                          setSelectedTime(input);
                                        }
                                      }}
                                    /> */}
                                    <NumericFormat
                                      customInput={TextField}
                                      label="Cantidad de tiempo"
                                      value={selectedTime}
                                      onValueChange={(values) => {
                                        const { formattedValue, value } = values;
                                        if (
                                          /^\d*$/.test(value) &&
                                          value >= 0 &&
                                          value.length <= 3
                                        ) {
                                          setSelectedTime(value);
                                        }
                                      }}
                                      thousandSeparator={true}
                                      decimalScale={0}
                                      fixedDecimalScale={false}
                                      allowNegative={false}
                                      isNumericString
                                      type="text"
                                      placeholder="Cantidad de tiempo"
                                      sx={{ width: "100%" }}
                                      InputProps={{
                                        inputProps: {
                                          maxLength: 3,
                                        },
                                      }}
                                      InputLabelProps={{
                                        shrink: true,
                                      }}
                                      variant="outlined"
                                      />
                                  </Grid>
                                </>
                              ) : null}

                              <Grid
                                item
                                xs={12}
                                display={"flex"}
                                justifyContent={"space-evenly"}
                              >
                                <Button
                                  variant="outlined"
                                  color="primary"
                                  startIcon={<Cancel />}
                                  onClick={handleCloseModal}
                                >
                                  Cancelar
                                </Button>
                                <Button
                                  variant="contained"
                                  color="primary"
                                  startIcon={<Send />}
                                  onClick={handleSendInfoToMarketplace}
                                >
                                  Publicar
                                </Button>
                              </Grid>
                            </Grid>
                          </Box>
                        </Modal>
                      </Box>
                    </StyledTableCell>
                  </StyledTableRow>
                ) : null}

                <Collapse in={open} timeout="auto" unmountOnExit>
                  <Box sx={{ margin: 1 }}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Nombre</TableCell>
                          <TableCell>Costo</TableCell>
                          <TableCell align="right">Stock</TableCell>
                          <TableCell align="right">Zona</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {isLoading ? (
                          <TableRow>
                            <TableCell colSpan={4}>
                              <Box sx={{ width: "80%", margin: "auto" }}>
                                <Skeleton />
                                <Skeleton animation="wave" />
                                <Skeleton animation={false} />
                              </Box>
                            </TableCell>
                          </TableRow>
                        ) : (
                          <>
                            {(productStock &&
                              productStock.productoStock?.product_stores
                                ?.length > 0) ||
                            productStock.productoStock?.product_supplierStores
                              ?.length > 0 ||
                            (productStockPre && productStockPre.length > 0) ? (
                              productStock.map((product) =>
                                product.sku ===
                                producto.products[0].internalSku ? (
                                  <React.Fragment key={product.id}>
                                    {/* Almacén Interno */}
                                    {product.productoStock !== "sin stock" &&
                                      product.productoStock.product_stores
                                        ?.length > 0 && (
                                        <>
                                          <TableRow>
                                            <TableCell
                                              colSpan={4}
                                              sx={{
                                                textAlign: "center",
                                                background: "#08793ad4",
                                                color: "white",
                                              }}
                                            >
                                              ALMACEN INTERNO
                                            </TableCell>
                                          </TableRow>
                                          {product.productoStock.product_stores.map(
                                            (productInternal) => (
                                              <TableRow
                                                key={productInternal.id}
                                                sx={{ background: "#00800014" }}
                                              >
                                                <TableCell>
                                                  {
                                                    productInternal.store
                                                      .storeName
                                                  }
                                                </TableCell>
                                                <TableCell>
                                                  $ {productInternal.cost}
                                                </TableCell>
                                                <TableCell align="right">
                                                  {productInternal.stock} pz
                                                </TableCell>
                                                <TableCell align="right">
                                                  {
                                                    productInternal.store.zone
                                                      .zoneNumber
                                                  }
                                                </TableCell>
                                              </TableRow>
                                            )
                                          )}
                                        </>
                                      )}
                                    {/* Almacén Externo */}
                                    {product.productoStock !== "sin stock" &&
                                      product.productoStock
                                        .product_supplierStores.length > 0 && (
                                        <>
                                          <TableRow>
                                            <TableCell
                                              colSpan={4}
                                              sx={{
                                                textAlign: "center",
                                                background: "#0030ffb0",
                                                color: "white",
                                              }}
                                            >
                                              ALMACEN EXTERNO
                                            </TableCell>
                                          </TableRow>
                                          {product.productoStock.product_supplierStores.map(
                                            (productExternal) => (
                                              <TableRow
                                                key={productExternal.id}
                                                style={{
                                                  background:
                                                    setBackgroundColor(
                                                      productExternal
                                                    ),
                                                }}
                                              >
                                                <TableCell>
                                                  {
                                                    productExternal
                                                      .supplierStore
                                                      .supplierName
                                                  }{" "}
                                                  -{" "}
                                                  {
                                                    productExternal
                                                      .supplierStore.storeName
                                                  }
                                                </TableCell>
                                                <TableCell>
                                                  $ {productExternal.cost}
                                                </TableCell>
                                                <TableCell align="right">
                                                  {productExternal.stock} pz
                                                </TableCell>
                                                <TableCell align="right">
                                                  {
                                                    productExternal
                                                      .supplierStore.zone
                                                      .zoneNumber
                                                  }
                                                </TableCell>
                                              </TableRow>
                                            )
                                          )}
                                        </>
                                      )}
                                  </React.Fragment>
                                ) : null
                              )
                            ) : (
                              <TableRow>
                                <TableCell colSpan={4} align="center">
                                  No hay datos disponibles
                                </TableCell>
                              </TableRow>
                            )}
                          </>
                        )}
                      </TableBody>
                    </Table>
                  </Box>
                </Collapse>
              </TableBody>
            </Table>
          );
        })}
      </div>
    ) : null;
  }
};

const TablaDesplegableProductos = (props) => {
  const dispatch = useDispatch();
  const allSelected = props.allSelected;
  const [open, setOpen] = React.useState(false);
  const [internalSkuDialog, setInternalSkuDialog] = React.useState(null);
  const productos = props.productos;
  const navigate = useNavigate();
  const marketplaces = useSelector((store) => store.productos.marketplacesInfo);
  const estadoEliminandoProducto = useSelector(
    (store) => store.productos.estadoEliminandoProducto
  );
  const messageDeletingElementProduct = useSelector(
    (store) => store.productos.messageDeletingElementProduct
  );

  React.useEffect(() => {
    if (marketplaces) {
      dispatch(setMarketplacesImages(marketplaces));
    }
  }, [marketplaces]);

  return (
    <>
      <ConfirmarEliminarProducto
        open={open}
        setOpen={setOpen}
        idElement={internalSkuDialog}
        idName={"internalBaseSku"}
        urlReturn={"/productos/consultar"}
        nameElement={"producto"}
        deleteElementFunction={eliminarProducto}
        deleteElementState={estadoEliminandoProducto}
        messageDeletingElement={messageDeletingElementProduct}
      />
      {productos.length === 0 ? (
        <h1 style={{ padding: "2rem 0", textAlign: "center" }}>
          sin productos{" "}
          <div className={stylos.divContainerBtnP}>
            <p style={{ margin: "0" }}>Añadir productos 👉 </p>
            <Button
              size="small"
              className={stylos.btn}
              variant="contained"
              color="primary"
              onClick={() => {
                navigate("productos/nuevo");
              }}
            >
              Nuevo
            </Button>
          </div>
        </h1>
      ) : (
        (console.log("marketplaces", marketplaces),
        (
          <TableContainer component={Paper}>
            <Table aria-label="collapsible table" sx={{display: "flex"}}>
              <TableBody>
                {productos.map((producto) => (
                  <Row
                    key={producto.internalBaseSku}
                    producto={producto}
                    open={open}
                    setOpen={setOpen}
                    setInternalSkuDialog={setInternalSkuDialog}
                    allSelected={allSelected}
                    marketplaces={marketplaces}
                  />
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ))
      )}
    </>
  );
};

export default TablaDesplegableProductos;
