import React from "react";
import CargandoLista from "../CargandoLista";
import { useDispatch, useSelector } from "react-redux";
import TablaDesplegableProductos from "./TablaDesplegableProductos";
import { obtenerProductosFiltradosAccion } from "../../redux/productosDucks";
import { ManejarErrores } from "../ManejarErrores";
import { obtenerAlmacenesInternos } from "../../redux/almacenesDucks";
import { CircularProgress } from "@mui/material";
import { SkeletonTables } from "../componentesGenerales/SkeletonTables";

const TablaDesplegableProductosWrapper = (props) => {
  const productos = useSelector((store) => store.productos.productos);
  const dispatch = useDispatch();
  const allSelected = props.allSelected;
  const offset = props.offset;
  const search = props.search;

  React.useEffect(() => {
    const fetchData = () => {
      dispatch(
        obtenerProductosFiltradosAccion({
          offsetActual: offset,
          search: search,
          scope: "extended-products_extended"
        })
      );
      dispatch(obtenerAlmacenesInternos());
    };
    fetchData();
  }, []);
 
  if (productos) {
    if (typeof productos === "string" && productos.startsWith("status")) {
      return <ManejarErrores errorCode={productos} />;
    } else {
      return (
        /*<div>
                    {JSON.stringify(productos)}
                </div>*/

        <TablaDesplegableProductos
          productos={productos}
          allSelected={allSelected}
        />
      );
    }
  } 
  else {
    return <SkeletonTables />;
  }
};

export default TablaDesplegableProductosWrapper;
