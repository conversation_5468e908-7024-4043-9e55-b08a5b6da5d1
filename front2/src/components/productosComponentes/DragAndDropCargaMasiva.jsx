import React, { useState } from "react";
import { setColorDragDrop, setMensaje } from "../../redux/facturasDucks";
import { enviarArchivoCargaMasiva } from "../../redux/productosDucks";
import { useDispatch } from "react-redux";
import { useCookies } from "react-cookie";
import LoadingButton from "@mui/lab/LoadingButton";
import Alert from "@mui/material/Alert";
import "../css/dragAndDrop.css";
import { useSelector } from "react-redux";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Dialog from "@mui/material/Dialog";
import ModalRedireccionProductos from "./ModalRedireccionProductos";
import { useTheme } from "@emotion/react";

const DragAndDropCargaMasiva = () => {
  const [open, setOpen] = React.useState(false);
  const mensaje = useSelector((store) => store.facturas.mensaje);
  const severity = useSelector((store) => store.facturas.severity);
  const loading = useSelector((store) => store.facturas.loading);
  const colorDragDrop = useSelector((store) => store.facturas.colorDragDrop);
  const mensajeDentroDeDragDrop = useSelector(
    (store) => store.facturas.mensajeDentroDeDragDrop
  );
  const theme = useTheme();

  const dispatch = useDispatch();
  const showRedirect = () => {
    setOpen(true);
  };

  const [cookies, setCookie] = useCookies();
  const [selectedFile, setSelectedFile] = useState(null);
  const [isFilePicked, setIsFilePicked] = useState(false);
  // drag state
  const [dragActive, setDragActive] = React.useState(false);
  // ref
  const inputRef = React.useRef(null);
  //drag and drop events------------------------------------------------
  // handle drag events
  const handleDrag = function (e) {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
      dispatch(setColorDragDrop("drag-active", "Agregando archivo"));
    } else if (e.type === "dragleave") {
      setDragActive(false);
      dispatch(setColorDragDrop("", "Sin archivo seleccionado"));
      dispatch(setMensaje(null));
    }
  };

  // triggers when file is dropped
  const handleDrop = function (e) {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      var nombreArchivos;
      if (e.dataTransfer.files.length === 1) {
        //Validates only one file is being picked
        nombreArchivos =
          "Archivo: " +
          e.dataTransfer.files[0].name +
          ",Tamaño: " +
          e.dataTransfer.files[0].size +
          " bytes"; //Set the files name and size

        if (e.dataTransfer.files[0].name === "Plantilla_Carga_Masiva.xlsx") {
          //Triggers when file's name is correct
          dispatch(setColorDragDrop("file-charged", nombreArchivos)); //Set file's name and size on the label and set the color of the drag N drop
          dispatch(setMensaje(null)); //Don't display a pop up message
          setSelectedFile(e.dataTransfer.files); //Set the file the user picked
          setIsFilePicked(true); //Set the status of the file to enable the submission button
        } else {
          nombreArchivos =
            'NO debes modificar el nombre a tu plantilla "Plantilla_Carga_Masiva.xlsx"';
          dispatch(setColorDragDrop("file-error", nombreArchivos)); //Set the error message on the label and set the color of the drag n drop
          dispatch(setMensaje(null)); //Don't display a pop up message
          setSelectedFile(null); //Set the file as null in case there was a picked file previously
          setIsFilePicked(false); //Set the status of the file "false" to disable the submission button
        }
      } else {
        nombreArchivos = "Solo puedes subir un archivo";
        dispatch(setColorDragDrop("file-error", nombreArchivos)); //Set the error message on the label and set the color of the drag n drop
        dispatch(setMensaje(null)); //Don't display a pop up message
        setSelectedFile(null); //Set the file as null in case there was a picked file previously
        setIsFilePicked(false); //Set the status of the file "false" to disable the submission button
      }
    }
  };
  //Handle clicks------------------------------------------------------------
  // triggers when file is selected with click
  const handleChange = function (e) {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      let nombreArchivos;
      if (e.target.files.length === 1) {
        //Validates only one file is being selected
        nombreArchivos =
          "Archivo: " +
          e.target.files[0].name +
          ",Tamaño: " +
          e.target.files[0].size +
          " bytes"; //Set file's name and size

        if (e.target.files[0].name === "Plantilla_Carga_Masiva.xlsx") {
          //Triggers when file's name is correct
          dispatch(setColorDragDrop("file-charged", nombreArchivos)); //Set file's name and size on the label and set the color of the drag N drop
          dispatch(setMensaje(null)); //Don't display a pop up message
          setSelectedFile(e.target.files); //Set the file the user picked
          setIsFilePicked(true); //Set the status of the file to enable the submission button
        } else {
          //trigger when the file's name is not "Plantilla_Carga_Masiva"
          nombreArchivos =
            'NO debes modificar el nombre a tu plantilla "Plantilla_Carga_Masiva.xlsx"'; //Error message
          dispatch(setColorDragDrop("file-error", nombreArchivos)); //Set the error message on the label and set the color of the drag n drop
          dispatch(setMensaje(null)); //Don't display a pop up message
          setSelectedFile(null); //Set the file as null in case there was a picked file previously
          setIsFilePicked(false); //Set the status of the file "false" to disable the submission button
        }
      } else {
        //Triggers when multiple files are being selected
        nombreArchivos = "Selecciona unicamente un archivo"; //Error message
        dispatch(setColorDragDrop("file-error", nombreArchivos)); //Set the error message on the label and set the color of the drag n drop
        dispatch(setMensaje(null)); //Don't display a pop up message
        setSelectedFile(null); //Set the file as null in case there was a picked file previously
        setIsFilePicked(false); //Set the status of the file "false" to disable the submission button
      }
    }
  };

  // triggers the input when the button is clicked
  const onButtonClick = () => {
    inputRef.current.click();
  };

  const handleSubmission = () => {
    const formData = new FormData();
    if (selectedFile && selectedFile.length === 1) {
      //
      let file = selectedFile[0];
      let nombreCompletoSplit = file.name.split(".");
      let extension = nombreCompletoSplit[nombreCompletoSplit.length - 1];
      nombreCompletoSplit.pop();
      let nombre = nombreCompletoSplit.join();
      if (extension !== "xlsx") {
        dispatch(
          setMensaje({mensaje: "Algún archivo seleccionado no es xlsx", severity: "warning"})
        );
        return;
      }
      if (nombre.length === 0) {
        dispatch(
          setMensaje({
            mensaje: 'NO debes modificar el nombre a tu plantilla "Plantilla_Carga_Masiva"',
            severity: "warning"
        })
        );
        return;
      }
      formData.append("file", file);
      dispatch(enviarArchivoCargaMasiva(formData, cookies.csrf_access_token));
      ////
      showRedirect();
      setIsFilePicked(false);
    } else {
      dispatch(
        setColorDragDrop("file-error", "Solo debes seleccionar un archivo xlsx")
      ); //Set the error message on the label and set the color of the drag n drop
      dispatch(setMensaje(null)); //Don't display a pop up message
      setSelectedFile(null); //Set the file as null in case there was a picked file previously
      setIsFilePicked(false); //Set the status of the file "false" to disable the submission button
    }
  };

  const clickInAlert = () => {
    setSelectedFile(null);
    setIsFilePicked(false);
    dispatch(setColorDragDrop("", "No has seleccionado archivo"));
    dispatch(setMensaje(null));
    inputRef.current.form.reset();
  };

  return (
    <React.Fragment>
      <form
        id="form-file-upload"
        onDragEnter={handleDrag}
        onSubmit={(e) => e.preventDefault()}
        style={{
          border: `2px dashed ${theme.palette.text.primary}`,
          borderRadius: "18px",
        }}
      >
        <input
          ref={inputRef}
          type="file"
          id="input-file-upload"
          multiple={true}
          onChange={handleChange}
          accept=".xlsx"
        />
        <label
          id="label-file-upload"
          htmlFor="input-file-upload"
          className={colorDragDrop}
        >
          <div>
            <p>{mensajeDentroDeDragDrop}</p>
            <button className="upload-button" onClick={onButtonClick}
            style={{
              color: theme.palette.text.primary,
            }}
            >
              Elige tu plantilla
            </button>
          </div>
        </label>
        {dragActive && (
          <div
            id="drag-file-element"
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          ></div>
        )}
      </form>
      {mensaje === null || mensaje === "" || !mensaje ? null : (
        <Alert
          variant="filled"
          severity={severity}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={clickInAlert}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
        >
          {mensaje}
        </Alert>
      )}

      <LoadingButton
        fullWidth
        variant="contained"
        sx={{ mt: 2 }}
        disabled={!isFilePicked}
        loading={loading}
        onClick={handleSubmission}
      >
        Subir productos
      </LoadingButton>

      <Dialog open={open} fullWidth={true}>
        <ModalRedireccionProductos />
      </Dialog>
    </React.Fragment>
  );
};

export default DragAndDropCargaMasiva;
