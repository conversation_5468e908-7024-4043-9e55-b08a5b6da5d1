import React from "react";
import { useParams } from "react-router-dom";
import { obtenerProductoUsandoSKUInterno } from "../../redux/productosDucks";
import { useDispatch, useSelector } from "react-redux";
import CargandoLista from "../CargandoLista";
import FormularioNuevoProducto from "./FormularioNuevoProducto";
import NewProductVariationsProvider from "../../context/NewProductVariationsProvider";
import { Button } from "@mui/material";
import styles from "../css/findBySku.module.css";


const ProductoWrapper = () => {
  const producto = useSelector((store) => store.productos.productoIndividual);
  const { skuInterno } = useParams();
  const dispatch = useDispatch();
  // console.log(producto);
  React.useEffect(() => {
    const fetchData = () => {
      dispatch(obtenerProductoUsandoSKUInterno(skuInterno,"extended-products_extended-stores"));
    };
    fetchData();
  }, []);

  const skuNotFound = () => {
    return (
      <div className={styles.divContainer}>
        <h3 className={styles.h3Style}>
          sku interno no encontrado, verifica el producto
        </h3>
        <div className={styles.divContainerBtnP}>
          <p style={{ margin: "0" }}>Consultar los productos 👉 </p>
          <Button
            size="small"
            className={styles.btn}
            variant="contained"
            color="buttonGreen"
            sx={{ ml: 1 }}
            onClick={() => {
              window.location.href = "productos/consultar";
            }}
          >
            Consultar
          </Button>
        </div>
      </div>
    );
  };

  if (producto) {
    if (
      typeof producto === "string" &&
      producto.startsWith("status")
    ) {
      return skuNotFound()
    } else {
          // return skuNotFound();
      switch (producto) {
        case "sku interno no encontrado":
          return skuNotFound();
        case 'status 500':
          return skuNotFound();
        default:
          return (
            <NewProductVariationsProvider>
              <FormularioNuevoProducto producto={producto} />
            </NewProductVariationsProvider>
          );
      }
    }
  } else {
    return <CargandoLista />;
  }
};

export default ProductoWrapper;
