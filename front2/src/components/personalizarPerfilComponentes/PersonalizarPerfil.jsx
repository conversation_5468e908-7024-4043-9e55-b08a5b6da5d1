import React, { useState } from 'react';
import { Avatar, IconButton, Box, TextField, Typography, Skeleton, Dialog, DialogTitle, DialogContent, DialogActions, Stack, Chip } from '@mui/material';
import AddPhotoIcon from '@mui/icons-material/AddPhotoAlternate';
import ClearIcon from '@mui/icons-material/Clear';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import WorkIcon from '@mui/icons-material/Work';
import { useDispatch, useSelector } from 'react-redux';
import Button from '@mui/material/Button';
import EditIcon from '@mui/icons-material/Edit';
import { Send } from '@mui/icons-material';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import { actualizarNicknameAccion, actualizarPhoneNumberAccion, leerUsuarioInfoAccion, uploadImageAccion, actualizarPasswordAccion, limpiarMensajeUsers } from '../../redux/usersDucks';
import { useCookies } from 'react-cookie';
import CustomDialog from '../componentesGenerales/CustomDialog';
import LockIcon from '@mui/icons-material/Lock';
import InputAdornment from '@mui/material/InputAdornment';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { LoadingButton } from '@mui/lab';
import { AlertComponent } from '../componentesGenerales/Alert';

export const customStyles = {
  avatar: {
    width: 160,
    height: 160,
    position: 'relative',
    filter: 'grayscale(0%)', // Por defecto, sin filtro
  },
  hovered: {
    width: 160,
    height: 160,
    position: 'relative',
    filter: 'grayscale(100%)', // Por defecto, sin filtro
  },
  addButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    opacity: 0,
    transition: 'opacity 0.3s',
    '&:hover': {
      backgroundColor: "#003876",
    },
  },
  removeButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-32.5%, -225%)',
    backgroundColor: "red",
    color: "red",
    opacity: 0,
    transition: 'opacity 0.3s',
    '&:hover': {
      backgroundColor: "red",
    },
  },
  avatarWrapper: {
    position: 'relative',
    display: "flex",
    justifyContent: "center",
    width: 160,
    height: 160,
    '&:hover $addButton, &:hover $removeButton': {
      opacity: 1,
    },
  },
  input: {
    display: 'none',
  },
};


export const EditableLabel = ({ label, value, onSave, concept, icon: IconComponent }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value || ''); // Inicializar con una cadena vacía si value es undefined

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleSaveClick = (valor) => {
    onSave(editValue);
    setIsEditing(false);
  };

  const handleCancelClick = () => {
    setIsEditing(false);
    setEditValue(value);
  };

  const handleInputChange = (event) => {
    setEditValue(event.target.value);
  };

  // Versión con estilo de tarjeta moderna
  const ModernCardDisplay = ({
    label = " ",
    value = " ",
    icon: IconComponent
  }) => {
    if(value === null || label === null) {
      return null;
    }
    return (
      <Box sx={{ width: '100%', maxWidth: 400, mx: 'auto' }}>
        <Card
          elevation={4}
          sx={{
            borderRadius: "12px",
            p: 3,
            transition: 'box-shadow 0.3s',
            '&:hover': { boxShadow: 6 },
          }}
        >
          <Stack direction="row" alignItems="start" justifyContent="space-between" flexDirection={"column"}>
            <Stack direction="row" alignItems="center" spacing={1}>
              {IconComponent && <IconComponent color="action" sx={{ fontSize: '1.2rem' }} />}
              {/* Etiqueta estilo "badge" */}
              <Chip
                label={label}
                size="small"
                sx={{
                  fontSize: ".8rem",
                  fontWeight: 500,
                  px: 0.5,
                }}
              />
            </Stack>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 500,
                ml: 2,
                fontSize: ".8rem",
              }}
            >
              {value}
            </Typography>
          </Stack>
        </Card>
      </Box>
    );
  };

  return (
      <ModernCardDisplay
        label={label}
        value={value}
        icon={IconComponent}
      />
  );
};


const UserInfo = ({ cookies, handleOpenChangePasswordDialog }) => {
  const dispatch = useDispatch();
  const name = useSelector(store => store.usuario.name);
  const email = useSelector(store => store.usuario.email);
  const nickname = useSelector(store => store.usuario.alias);
  const phoneNumber = useSelector(store => store.usuario.phoneNumber);
  const role = useSelector(store => store.usuario.role);
  const loadingInfo = useSelector((store) => store.usuario.loadingInfo);

  const handleNicknameSave = (newValue) => {
    const formData = new FormData();
    if (newValue.length > 0) {
      formData.append('nickname', newValue)

      dispatch(actualizarNicknameAccion(formData, cookies.csrf_access_token));
    } else {
      alert('El nickname no puede estar vacío');
    }
  };

  const handlePhoneNumberSave = (newValue) => {
    // Validar el formato del número telefónico
    if (newValue.length < 1) {
      alert('El número telefónico no puede estar vacío');
    } else {
      if (/(\+\d{1,3}\s?)?((\(\d{3}\)\s?)|(\d{3})(\s|-?))(\d{3}(\s|-?))(\d{4})(\s?(([E|e]xt[:|.|]?)|x|X)(\s?\d+))?/g.test(newValue)) {
        const formData = new FormData();
        formData.append('phoneNumber', newValue)

        dispatch(actualizarPhoneNumberAccion(formData, cookies.csrf_access_token));
      } else {
        alert('El número telefónico no es válido');
      }
    }
  };

  return (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, fontWeight: 'bold', color: 'primary.main' }}>
        Información
      </Typography>

      <Box marginBottom={3}>
        {loadingInfo.nickname ? (
          <Typography variant="h2"><Skeleton animation="wave" /></Typography>
        ) : (
          <EditableLabel
            concept="Nickname"
            label="Nickname"
            value={nickname}
            onSave={handleNicknameSave}
            icon={PersonIcon}
          />
        )}
      </Box>
      <Box marginBottom={3}>
        {loadingInfo.phoneNumber ? (
          <Typography variant="h4" component="div" color={"primary"}><Skeleton animation="wave" /></Typography>
        ) : (
          <EditableLabel
            concept="PhoneNumber"
            label="Número de Teléfono"
            value={phoneNumber}
            onSave={handlePhoneNumberSave}
            icon={PhoneIcon}
          />
        )}
      </Box>
    
      <Box marginBottom={3}>
        <Stack direction="row" alignItems="center" spacing={1}>
          <PersonIcon color="action" />
          <Typography sx={{ fontWeight: "bold" }} variant="subtitle2" >Nombre</Typography>
        </Stack>
        <Typography variant='subtitle1'>{name}</Typography>
      </Box>
      <Box marginBottom={3}>
        <Stack direction="row" alignItems="center" spacing={1}>
          <EmailIcon color="action" />
          <Typography sx={{ fontWeight: "bold" }} variant="subtitle2"  >Correo</Typography>
        </Stack>
        <Typography variant='subtitle1'>{email}</Typography>
      </Box>
      <Box marginBottom={3}>
        <Stack direction="row" alignItems="center" spacing={1}>
          <WorkIcon color="action" />
          <Typography sx={{ fontWeight: "bold" }} variant="subtitle2"  >Rol</Typography>
        </Stack>
        <Typography variant='subtitle1'>{role}</Typography>
      </Box>
      <Box marginBottom={1}>
        <Button
          onClick={handleOpenChangePasswordDialog}
          color='buttonGreenPink'
          sx={{
            padding: 0,
            cursor: 'pointer',
            minWidth: 0,
            '&:hover': {
              backgroundColor: 'transparent',
            },
          }}
        >
          Cambiar Contraseña
        </Button>
      </Box>
    </Box>
  );
};


const PersonalizarPerfil = () => {
  const dispatch = useDispatch();
  const loadingInfo = useSelector((store) => store.usuario.loadingInfo);
  const [cookies, setCookie] = useCookies();

  const [image, setImage] = useState(null);
  const [isHovered, setIsHovered] = useState(false);
  const URLphoto = useSelector(store => store.usuario.URLphoto);
  const info = useSelector((store) => store.usuario.info);
  const [openChangePasswordDialog, setOpenChangePasswordDialog] = useState(false);
  const loadingPassword = useSelector((store) => store.usuario.loadingInfo.password);
  const usersMensaje = useSelector((store) => store.usuario.usersMensaje);
  const usersSeverity = useSelector((store) => store.usuario.usersSeverity);

  const handleOpenChangePasswordDialog = () => {
    setOpenChangePasswordDialog(true);
  };

  const handleCloseChangePasswordDialog = () => {
    setOpenChangePasswordDialog(false);
    setOldPassword('');
    setNewPassword('');
    setConfirmNewPassword('');
    setNewPasswordError('');
    setConfirmNewPasswordError('');
  };

  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');

  const [newPasswordError, setNewPasswordError] = useState('');
  const [confirmNewPasswordError, setConfirmNewPasswordError] = useState('');

  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);

  const handleClickShowOldPassword = () => setShowOldPassword((show) => !show);
  const handleClickShowNewPassword = () => setShowNewPassword((show) => !show);
  const handleClickShowConfirmNewPassword = () => setShowConfirmNewPassword((show) => !show);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const validatePassword = (password) => {
    const errors = [];
    if (password.length < 8) {
      errors.push("Al menos 8 caracteres");
    }
    if (!/[A-Z]/.test(password)) {
      errors.push("Al menos una mayúscula");
    }
    if (!/[0-9]/.test(password)) {
      errors.push("Al menos un dígito");
    }
    if (!/[^A-Za-z0-9]/.test(password)) {
      errors.push("Al menos un carácter especial");
    }
    return errors;
  };

  const handleChangePassword = () => {
    const newPassValidationErrors = validatePassword(newPassword);

    if (newPassValidationErrors.length > 0) {
      setNewPasswordError(newPassValidationErrors.join(", "));
      return;
    } else {
      setNewPasswordError('');
    }

    if (newPassword !== confirmNewPassword) {
      setConfirmNewPasswordError("Las contraseñas no coinciden");
      return;
    } else {
      setConfirmNewPasswordError('');
    }

    dispatch(actualizarPasswordAccion(oldPassword, newPassword, cookies.csrf_access_token));
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('image', file);

      const reader = new FileReader();
      reader.onload = function (event) {
        const image = new Image();
        image.src = event.target.result;

        image.onload = function () {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          let width = image.width;
          let height = image.height;
          const maxWidth = 1280; // Ancho máximo para resolución HD
          const maxHeight = 720; // Alto máximo para resolución HD

          if (width > height && width > maxWidth) {
            height *= maxWidth / width;
            width = maxWidth;
          } else if (height > maxHeight) {
            width *= maxHeight / height;
            height = maxHeight;
          }

          // Rotación y dibujo de la imagen en el canvas
          canvas.width = width;
          canvas.height = height;
          ctx.drawImage(image, 0, 0, width, height);

          // Convierte el canvas a Blob y crea un nuevo archivo
          canvas.toBlob((blob) => {
            const rotatedFile = new File([blob], file.name, { type: file.type });
            formData.set('image', rotatedFile);

            // Ahora puedes enviar formData al servidor con la imagen rotada
            dispatch(uploadImageAccion(formData, cookies.csrf_access_token));
          }, file.type);
        };
      };

      reader.readAsDataURL(file);
    }
  };


  React.useEffect(() => {
    if (!info || Object.keys(info).length === 0) {
      dispatch(leerUsuarioInfoAccion());
    }
  }, []);

  React.useEffect(() => {
    if (usersMensaje) {
      handleCloseChangePasswordDialog();
    }
  }, [usersMensaje]);

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
        <Card variant='outlined' sx={{ width: '80%', borderRadius: '20px', borderColor: '#003876', borderWidth: '3' }}>
          <Box>
            <CardContent>
              <Box display='flex' flexDirection='column' sx={{ justifyContent: 'center' }}>
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <Box sx={{ width: 160, height: 160 }}>
                      <div style={customStyles.avatarWrapper}>
                        {loadingInfo.image ? (
                          <Skeleton variant='circular' width={160} height={160} />
                        ) : (
                          <>
                            <Avatar
                              sx={{ width: 160, height: 160 }}
                              src={URLphoto}
                              alt="Avatar"
                              style={customStyles.avatar}
                            />
                            <label htmlFor="avatar-upload">
                              <IconButton
                                sx={{ width: 160, height: 160 }}
                                style={customStyles.addButton}
                                component="span"
                              >
                                <AddPhotoIcon sx={{ width: 80, height: 80 }} />
                              </IconButton>
                              <input
                                accept="image/*"
                                style={{ display: 'none' }}
                                id="avatar-upload"
                                type="file"
                                onChange={handleImageUpload}
                              />
                            </label>
                          </>
                        )}
                      </div>
                    </Box>
                  </div>
                </Box>
                <Box alignSelf='center' alignContent='start' marginTop='17px'>
                  <UserInfo cookies={cookies} handleOpenChangePasswordDialog={handleOpenChangePasswordDialog} />
                </Box>
              </Box>
            </CardContent>
          </Box>
        </Card>
      </Box>

      <CustomDialog
        open={openChangePasswordDialog}
        onClose={handleCloseChangePasswordDialog}
        title="Cambiar Contraseña"
       
        maxHeight="80vh"
        actions={
          <>
            <Button onClick={handleCloseChangePasswordDialog} color='buttonGreenPink'>Cancelar</Button>
            <LoadingButton
              onClick={handleChangePassword}
              variant="contained"
              loading={loadingPassword}
              disabled={!!newPasswordError || !!confirmNewPasswordError || !newPassword || !confirmNewPassword || !oldPassword}
            >
              Guardar
            </LoadingButton>
          </>
        }
      >
        <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
          Para cambiar tu contraseña, ingresa tu contraseña actual y la nueva contraseña.
        </Typography>
        <Stack spacing={3} sx={{ mt: 2 }}>

          <TextField
            label="Contraseña Antigua"
            type={showOldPassword ? 'text' : 'password'}
            fullWidth
            name='oldPassword'
            variant="outlined"
            value={oldPassword}
            onChange={(e) => setOldPassword(e.target.value)}
            helperText="Ingresa tu contraseña actual"
            InputProps={{
              autocomplete: 'current-password',
              startAdornment: (
                <InputAdornment position="start">
                  <LockIcon />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowOldPassword}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                  >
                    {showOldPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          <TextField
            label="Nueva Contraseña"
            type={showNewPassword ? 'text' : 'password'}
            fullWidth
            name='newPassword'
            variant="outlined"
            value={newPassword}
            onChange={(e) => {
              const value = e.target.value;
              setNewPassword(value);
              const errors = validatePassword(value);
              setNewPasswordError(errors.join(", "));

              // También validar confirmNewPassword si newPassword cambia
              if (confirmNewPassword && value !== confirmNewPassword) {
                setConfirmNewPasswordError("Las contraseñas no coinciden");
              } else if (confirmNewPassword && value === confirmNewPassword) {
                setConfirmNewPasswordError('');
              }
            }}
            error={!!newPasswordError}
            helperText={newPasswordError || "Mínimo 8 caracteres, al menos un número, una mayúscula y un carácter especial"}
            InputProps={{
              autocomplete: 'new-password',
              startAdornment: (
                <InputAdornment position="start">
                  <LockIcon />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowNewPassword}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                  >
                    {showNewPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          <TextField
            label="Confirmar Nueva Contraseña"
            type={showConfirmNewPassword ? 'text' : 'password'}
            fullWidth
            name='confirmNewPassword'
            variant="outlined"
            value={confirmNewPassword}
            onChange={(e) => {
              const value = e.target.value;
              setConfirmNewPassword(value);
              if (newPassword !== value) {
                setConfirmNewPasswordError("Las contraseñas no coinciden");
              } else {
                setConfirmNewPasswordError('');
              }
            }}
            error={!!confirmNewPasswordError}
            helperText={confirmNewPasswordError || "Repite tu nueva contraseña"}
            InputProps={{
              autocomplete: 'new-password',
              startAdornment: (
                <InputAdornment position="start">
                  <LockIcon />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowConfirmNewPassword}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                  >
                    {showConfirmNewPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Stack>
      </CustomDialog>

      {usersMensaje && (
        <AlertComponent
          color={usersSeverity}
          message={usersMensaje}
          cleanMessage={limpiarMensajeUsers}
        />
      )}
    </>
  );
};

export default PersonalizarPerfil;