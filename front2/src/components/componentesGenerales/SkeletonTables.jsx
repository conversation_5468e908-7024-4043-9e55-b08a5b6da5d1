import { Box, Skeleton } from "@mui/material"


export const SkeletonTables = () => {
    return (
        <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center"
           , width: "100%", height: "auto"
         }}>
            <Skeleton
                variant="rectangular"
                width="100%"
                height={166}
                sx={{ borderRadius: "10px", margin: "1em auto" }}
                animation="wave"
            />
            <Skeleton
                variant="rectangular"
                width="100%"
                height={166}
                sx={{ borderRadius: "10px", margin: "1em auto" }}
                animation="wave"
            />
            <Skeleton
                variant="rectangular"
                width="100%"
                height={166}
                sx={{ borderRadius: "10px", margin: "1em auto" }}
                animation="wave"
            />
            <Skeleton
                variant="rectangular"
                width="100%"
                height={166}
                sx={{ borderRadius: "10px", margin: "1em auto" }}
                animation="wave"
            />
        </Box>
    )
}