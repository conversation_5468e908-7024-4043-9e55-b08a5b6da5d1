import { Dialog, DialogTitle } from "@mui/material";
import React, { Children } from "react";


const CustomModal = (handleClose, open, title) => {
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      PaperProps={{
        classes: {
          borderRadius: "14px",
          width: "400px",
          height: "200px",
        },
      }}
    >
      <DialogTitle>{title}</DialogTitle>
      {Children}
    </Dialog>
  );
};

export default CustomModal;
