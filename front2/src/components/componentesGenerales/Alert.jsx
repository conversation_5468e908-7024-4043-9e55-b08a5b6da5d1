import { Alert } from "@mui/material";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

export const AlertComponent = ({ color, message, cleanMessage, time = 5000 }) => {
  const dispatch = useDispatch();

  useEffect(() => {
    const timer = setTimeout(() => {
      if (cleanMessage) {
        dispatch(cleanMessage());
      }
    }, time); // Temporizador de 5 segundos

    return () => clearTimeout(timer);
  }, [message, cleanMessage, dispatch]);


  // Limpieza del temporizador

  return (
     <Alert
      sx={{
        position: "fixed",
        bottom: "30px",
        left: "10px",
        zIndex: 9999,
      }}
      onClose={() => cleanMessage ? dispatch(cleanMessage()) : {}}
      severity={color}
    >
      {message}
    </Alert>
  );
};
