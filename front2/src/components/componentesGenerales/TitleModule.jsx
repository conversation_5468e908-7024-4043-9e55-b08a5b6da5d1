import { useTheme } from "@emotion/react";
import { Grid, styled, Typography } from "@mui/material"
import Paper from '@mui/material/Paper';



export const TitleModule = ({title}) => {

    const Item = styled(Paper)(({ theme }) => ({
        padding: theme.spacing(2),
        textAlign: 'center',
        // backgroundColor: 'transparent',
      }));
      
      const theme = useTheme();
    return (
        <Grid  spacing={2} marginTop={2} marginBottom={2} width={"100%"} >
            <Grid item xs={12} width={"100%"} >
                <Item elevation={0}>
                    <Typography variant="h5"  gutterBottom
                    style={{fontFamily: 'Filson Pro'}}
                >{title}</Typography>
                </Item>
                {/* <Typography variant="subtitle1" color={"primary"} gutterBottom >{title}</Typography> */}
            </Grid>
        </Grid>
    )
}