import { CircularProgress, Grid, Tooltip, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";
import Paper from "@mui/material/Paper";
import { useSelector } from "react-redux";

// se exporta el componente donde se muestra el
export const ContadorTooltip = ({
  totalProductosPedidos,
  totalOrders,
  isOrders,
  page,
}) => {
  const loading = useSelector((store) => store.pedidos.loading);
  let lowerLevel = 0;
  let upLevel = 0;
  // traemos la pagina y dependiendo de esat se hacer el conteo correspondiente
  // con la siguiemte formula  (page - 1) * 30 + 1
  if (page == 1) {
    lowerLevel = 1;
    // upLevel = totalProductosPedidos?.length > totalOrders ? totalProductosPedidos : totalOrders;
    upLevel = totalProductosPedidos;
  } else {
    lowerLevel = (page - 1) * 30 + 1;
    upLevel = (page - 1) * 30 + totalProductosPedidos;
  }

  const ItemCenter = styled(Paper)(() => ({
    textAlign: "center",
    boxShadow: "none",
    background: "transparent",
  }));

  return (
    totalProductosPedidos > 0 && (
      <Grid
        alignItems="center"
        sx={{
          display: "flex",
          minWidth: "110px",
        }}
      >
        <ItemCenter
          sx={{
            display: "flex",
            justifyContent: "center",
            fontSize: "25px",
            alignItems: "center",
            fontStyle: " bold",
            // color: "#003876",
            fontFamily: "Filson Pro"
          }}
        >
          <Tooltip
            placement="top-end"
            title={`numero de pedidos: ${totalProductosPedidos}`}
            sx={{ display: "flex", alignItems: "center", }}
          >
            <Typography
              sx={{
               fontSize: "clamp(1rem, 1.5vw + 0.5rem, 2rem)",
              }}>
              {loading || totalOrders == undefined ? (
                <CircularProgress size={16} />
              ) : isOrders ? (
                lowerLevel + " - " + upLevel + " de " + totalOrders
              ) : (
                totalProductosPedidos
              )}
            </Typography>
          </Tooltip>
        </ItemCenter>
      </Grid>
    )
  );
};
