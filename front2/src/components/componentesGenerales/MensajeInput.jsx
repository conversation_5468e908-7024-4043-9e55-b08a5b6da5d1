import React, { useEffect, useRef, useState } from 'react';
import { Box, CircularProgress, IconButton, InputAdornment, TextField, Typography } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import QuickreplyIcon from '@mui/icons-material/Quickreply';
import { useDispatch, useSelector } from 'react-redux';
import { DefaultMessagesChip } from '../mensajesComponentes/components/DefaultMessagesChip';
import { MessagePopover } from '../mensajesComponentes/MessagePopover';
import { getDefaultMessages } from '../../redux/mensajesDucks';

/**
 * Componente reutilizable para entrada de mensajes con integración de mensajes predeterminados.
 * Props:
 *  - value: string (valor del input)
 *  - setValue: function (setter del valor)
 *  - onSend: function (acción al enviar)
 *  - loading: boolean (si está enviando)
 *  - maxLength: number (máximo de caracteres, obligatorio)
 *  - onMaxLengthAlert: function (opcional, se llama cuando se llega al límite de caracteres)
 *  - placeholder: string
 *  - label: string
 *  - rows: number (filas del textarea)
 *  - disabled: boolean
 *  - error: boolean
 *  - helperText: string
 *  - ...otros de TextField
 */
export const MensajeInput = ({
  value,
  setValue,
  onSend,
  loading = false,
  maxLength,
  onMaxLengthAlert,
  placeholder = "Escribe un mensaje",
  label = "Mensaje",
  rows = 3,
  disabled = false,
  error = false,
  helperText = "",
  showMaxLengthAlert,
  setShowMaxLengthAlert,
  ...props
}) => {
  const dispatch = useDispatch();
  const messagesDefault = useSelector((state) => state.mensajes.messagesDefault);
  const [showDefaultMessages, setShowDefaultMessages] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [filterText, setFilterText] = useState("");
  const [noMatchTerms, setNoMatchTerms] = useState([]);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const inputRef = useRef();
  // Obtener mensajes predeterminados al montar
  useEffect(() => {
    if (!messagesDefault || messagesDefault.length === 0) {
      dispatch(getDefaultMessages());
    }
  }, [dispatch, messagesDefault]);

  // Manejar cambios en el input
  const handleInputChange = (event) => {
    let newValue = event.target.value;
    // Truncar si excede maxLength
    if (maxLength && newValue.length > maxLength) {
      newValue = newValue.substring(0, maxLength);
      if (showMaxLengthAlert) setShowMaxLengthAlert(true);
    }
    setValue(newValue);

    // Lógica de arroba para popover
    const match = newValue.match(/@([^\s]*)$/);
    if (match) {
      const term = match[1];
      if (!noMatchTerms.includes(term)) {
        setFilterText(term);
        setAnchorEl(event.currentTarget);
        setShowDefaultMessages(true);
      }
    } else {
      setShowDefaultMessages(false);
      setAnchorEl(null);
      setFilterText("");
    }
  };

  // Manejar pegado de texto
  const handlePaste = (event) => {
    if (maxLength) {
      event.preventDefault();
      const pastedText = event.clipboardData.getData('text');
      const currentLength = value.length;
      const remainingSpace = maxLength - currentLength;
      if (remainingSpace <= 0) {
        if (showMaxLengthAlert) setShowMaxLengthAlert(true);
        return;
      }
      const textToAdd = pastedText.substring(0, remainingSpace);
      setValue(value + textToAdd);
        if (pastedText.length > remainingSpace && showMaxLengthAlert) setShowMaxLengthAlert(true);
    }
  };

  // Manejar envío con Enter (sin Shift)
  const handleKeyDown = (event) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (!loading && value.trim() !== "") {
        onSend();
      }
    }
    // Tab para autocompletar mensaje predeterminado
    if (event.key === 'Tab') {
      if (/@$/.test(value) && messagesDefault.length > 0) {
        event.preventDefault();
        const nuevoMensaje = value.replace(/@$/, messagesDefault[0].message);
        setValue(nuevoMensaje);
        setSelectedMessage(messagesDefault[0]);
        setShowDefaultMessages(false);
        setAnchorEl(null);
      }
    }
  };

  // Chips: agregar mensaje predeterminado
  const handleChipClick = (msg) => {
    setValue(prev => {
      const actual = typeof prev === "string" ? prev : (typeof value === "string" ? value : "");
      let nuevoValor = actual ? actual + " " + msg.message : msg.message;
      if (maxLength && nuevoValor.length > maxLength) {
        nuevoValor = nuevoValor.substring(0, maxLength);
        if (onMaxLengthAlert) onMaxLengthAlert();
      }
      return nuevoValor;
    });
    setSelectedMessage(msg);
    setShowDefaultMessages(false);
  };

  // Botón de mensaje predeterminado (abre popover)
  const handleQuickReplyClick = (event) => {
    setAnchorEl(event.currentTarget);
    setShowDefaultMessages(true);
  };

  // Popover: cerrar
  const handleClosePopover = () => {
    setAnchorEl(null);
    setShowDefaultMessages(false);
  };

  // Si no hay coincidencias, guardar término
  useEffect(() => {
    if (filterText && messagesDefault.length > 0) {
      const hasMatches = messagesDefault.some(m =>
        m.name.toLowerCase().includes(filterText.toLowerCase()) ||
        m.message.toLowerCase().includes(filterText.toLowerCase())
      );
      if (!hasMatches && !noMatchTerms.includes(filterText)) {
        setNoMatchTerms(prev => [...prev, filterText]);
      }
    }
  }, [filterText, messagesDefault, noMatchTerms]);
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 1, width: "100%" }}>
      <TextField
        id="mensaje-input"
        type="text"
        multiline
        rows={rows}
        label={label}
        placeholder={placeholder}
        value={value}
        inputRef={inputRef}
        onChange={handleInputChange}
        onPaste={handlePaste}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        error={error}
        helperText={helperText}
        InputProps={{
          endAdornment: (
            <Box sx={{ display: "flex", alignItems: "center", marginTop: "1rem", width: "90%", overflowX: "hidden", justifyContent: "space-around", margin: "10px  auto 0 auto", backgroundColor: "transparent" }}
            style={{
              backgroundColor: "transparent",
              backdropFilter: "blur(10px)",
              borderRadius: "20px",
              padding: "10px",
              border: "1px solid #e0e0e0",
              boxShadow: "0 0 10px 0 rgba(0, 0, 0, 0.1)",
            }}
            >
              <InputAdornment position="start">
                <IconButton
                  sx={{ padding: 0 }}
                  aria-label="Mensaje predeterminado"
                  edge="start"
                  color="buttonGreenPink"
                  onClick={handleQuickReplyClick}
                >
                  <QuickreplyIcon sx={{ padding: 0 }} />
                </IconButton>
              </InputAdornment>
              {messagesDefault?.length > 0 && (
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '8px',
                    py: 0,
                    width: '70%',
                    overflowX: 'auto',
                    position: 'relative',
                    maskImage: 'linear-gradient(to right, transparent 0%, black 20px, black calc(100% - 20px), transparent 100%)',
                    WebkitMaskImage: 'linear-gradient(to right, transparent 0%, black 20px, black calc(100% - 20px), transparent 100%)',
                    '&::-webkit-scrollbar': {
                      height: '6px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      backgroundColor: 'rgba(0,0,0,0.2)',
                      borderRadius: '6px',
                    }
                  }}
                >
                  {messagesDefault.map((msg) => (
                    <DefaultMessagesChip
                      message={msg}
                      key={msg.id}
                      sx={{ flexShrink: 0, whiteSpace: 'nowrap' }}
                      handleEdit={() => handleChipClick(msg)}
                    />
                  ))}
                </Box>
              )}
              <InputAdornment position="end">
                <IconButton
                  aria-label="Enviar mensaje"
                  edge="end"
                  color="buttonGreenPink"
                  onClick={(e) => {
                    e.preventDefault();
                    if (loading || value.trim() === "") return;
                    onSend();
                  }}
                  disabled={loading || disabled}
                >
                  {loading ? <CircularProgress size={24} /> : <SendIcon size={18} />}
                </IconButton>
              </InputAdornment>
            </Box>
          ),
        }}
        sx={{
          marginY: "4px",
          bgcolor: "background.default",
          borderRadius: "12px",
          "& .MuiOutlinedInput-root": {
            borderRadius: "12px",
            display: "flex",
            flexDirection: "column",
            alignItems: "stretch",
            padding: "10px",
          },
          '& .MuiOutlinedInput-input': {
            padding: " 0 0 15px 0",
          },
        }}
        {...props}
      />
      <Typography
        variant="subtitle2"
        sx={{ textAlign: "right" }}
        color={
          value.length >= maxLength ? "error" :
            value.length >= maxLength * 0.8 ? "warning" :
              "textSecondary"
        }
      >
        {value.length}/{maxLength}
      </Typography>

      <MessagePopover
        open={showDefaultMessages}
        anchorEl={anchorEl}
        onClose={handleClosePopover}
        defaultMessage={() => {}}
        setShowDefaultMessages={setShowDefaultMessages}
        setSelectedMessageFromMessages={setValue}
        filterText={filterText}
        setFilterText={setFilterText}
      />
    </Box>
  );
};

export default MensajeInput; 