import { Button, CircularProgress } from '@mui/material';
import React, { useState } from 'react'
import CustomDialog from './CustomDialog';

const ModalConfirmacionBorrar = ({deleteFunction, open, setOpen, msg = "¿Estás seguro de que deseas borrar este elemento?"}) => {
    const [loading, setLoading] = useState(false);
    
    const handleDelete = async () => {
        setLoading(true);
        await deleteFunction();
        setLoading(false);
        setOpen(false);
    }
    
    return (
        <>
        <CustomDialog
            open={open}
            onClose={()=>setOpen(false)}
            title={msg}
            actions={
                <>
                    <Button onClick={()=>setOpen(false)} disabled={loading} color='error'>Cancelar</Button>
                    <Button 
                        onClick={handleDelete} 
                        color='buttonGreenPink'
                        disabled={loading}
                        startIcon={loading ? <CircularProgress size={20} /> : null}
                    >
                        {loading ? "Borrando..." : "Borrar"}
                    </Button>
                </>
            }
        >
            {/* No hay contenido adicional, solo acciones y título */}
        </CustomDialog>
        </>
    )
}

export default ModalConfirmacionBorrar
