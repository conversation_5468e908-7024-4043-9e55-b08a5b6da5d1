import React from 'react'
import { ListComents } from '../ventasComponentes/ListComents'
import { NewComment } from '../ventaDirectaComponentes/NewComment'
import { Grid, Button } from '@mui/material'
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
const CommentsWrapper = ({ comments, deleteComment, updateComment, addNewCommentFunction, isAddingComment, setIsAddingComment, commentType, getOrderCommentsWithDeletedFunction }) => {
    return (
        <>
            <ListComents
                comments={comments}
                deleteComment={deleteComment}
                updateComment={updateComment}
            />
            {isAddingComment ? (
                <NewComment
                    commentType={commentType}
                    addNewCommentFunction={addNewCommentFunction}
                    setIsAddingComment={setIsAddingComment}
                    getOrderCommentsWithDeletedFunction={getOrderCommentsWithDeletedFunction}
                />
            ) : (
                <Grid container spacing={2}>
                    <Grid item sx={{ textAlign: "right" }}>
                        <Button
                            size="small"
                            variant="outlined"
                            sx={{ flexBasis: "15%", marginBottom: "5px", width: "10%" }}
                            // color={
                            //   hasNonEmptyStores(pedido) === true ? "success" : "error"
                            // }
                            color="buttonGreenPink"
                        >
                            <AddCommentIcon />

                        </Button>
                    </Grid>
                </Grid>
            )}
        </>
    )
}

export default CommentsWrapper