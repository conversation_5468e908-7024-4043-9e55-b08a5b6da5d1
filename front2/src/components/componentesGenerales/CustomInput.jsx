import React, { useEffect, forwardRef } from 'react';
import { Box, CircularProgress, Divider, IconButton, InputAdornment, TextField } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import QuickreplyIcon from "@mui/icons-material/Quickreply";
import { useDispatch, useSelector } from 'react-redux';
import { DefaultMessagesChip } from '../mensajesComponentes/components/DefaultMessagesChip';
import { MessagePopover } from '../mensajesComponentes/MessagePopover';
import { getDefaultMessages } from '../../redux/mensajesDucks';
import MensajeInput from './MensajeInput';



export const CustomInput = forwardRef(({
  inputValue,
  setInputValue,
  onSend,
  loading = false,
  isError = false,
  placeholder = "Escribe un mensaje",
  addNewCommentFunction,
  maxLength
}, ref) => {

  const handleKeyDown = (event) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (!loading && inputValue.trim() !== "") {
        onSend();
      }
    }
  };

  // Función para manejar el cambio de input respetando el maxLength
  const handleInputChange = (event) => {
    const newValue = event.target.value;
    
    // Si hay un maxLength definido, verificar que no se exceda
    if (maxLength && newValue.length > maxLength) {
      // Si se excede, truncar al maxLength
      const truncatedValue = newValue.substring(0, maxLength);
      setInputValue(truncatedValue);
    } else {
      setInputValue(newValue);
    }

    const match = newValue.match(/@([^\s]*)$/);
    if (match) {
      const term = match[1];
      setFilterText(term);
      setAnchorEl(event.currentTarget);
      setShowDefaultMessages(true);
    } else {
      setFilterText("");
      setShowDefaultMessages(false);
      setAnchorEl(null);
    }
  };

  // Función para manejar el pegado de texto
  const handlePaste = (event) => {
    if (maxLength) {
      event.preventDefault();
      const pastedText = event.clipboardData.getData('text');
      const currentLength = inputValue.length;
      const remainingSpace = maxLength - currentLength;
      
      if (remainingSpace <= 0) {
        return; // No hay espacio disponible
      }
      
      const textToAdd = pastedText.substring(0, remainingSpace);
      const newValue = inputValue + textToAdd;
      setInputValue(newValue);
    }
  };

  const dispatch = useDispatch();
  const messagesDefault = useSelector((state) => state.mensajes.messagesDefault);
  const [selectedMessage, setSelectedMessage] = React.useState(null);
  const [message, setMessage] = React.useState("");
  const [showDefaultMessages, setShowDefaultMessages] = React.useState(false);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [filterText, setFilterText] = React.useState("");
  const [noMatchTerms, setNoMatchTerms] = React.useState([]);
  const [loadingNoMatch, setLoadingNoMatch] = React.useState(false);

  const handleOpenPopover = (event) => {
    setAnchorEl(event.currentTarget);
    setShowDefaultMessages(true);
  };

  const handleClosePopover = () => {
    setAnchorEl(null);
    setShowDefaultMessages(false);
  };

  const handleSelectMessage = (message) => {
    setMessage(prev => prev + " " + message.message);
    setShowDefaultMessages(false);
  };

  const handleNoMatch = () => {
    setLoadingNoMatch(false);
  };

  const handleNoMatchRequest = async (search) => {
    setLoadingNoMatch(true);
    const offset = 0;
    const nc = 10;
    dispatch(getDefaultMessages({ search, offset, nc }));
    setLoadingNoMatch(false);
  };

  const handleFilterInputChange = (event, newValue) => {
    setFilterText(newValue);
  };

  const defaultMessage = (message) => {
    setInputValue(message);
    setShowDefaultMessages(false);
  };


  useEffect(() => {
    if (messagesDefault.length === 0) {
      dispatch(getDefaultMessages());
    }
  }, [ dispatch]);

  useEffect(() => {
    if (filterText !== "" && 
        !loadingNoMatch && 
        !loading &&
        messagesDefault.length > 0 &&
        messagesDefault.filter(m =>
            m.name.toLowerCase().includes(filterText.toLowerCase()) ||
            m.message.toLowerCase().includes(filterText.toLowerCase())
        ).length === 0) {
        const timer = setTimeout(() => {
            setShowDefaultMessages(false);
        }, 0);
        
        return () => clearTimeout(timer);
    }
  }, [loadingNoMatch, loading, filterText, messagesDefault]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "end",
        alignItems: "end",
        width: "100%",
        borderRadius: "12px",
      }}
    >

     <MensajeInput
      value={inputValue}
      setValue={setInputValue}
      onSend={onSend}
      loading={loading}
      maxLength={maxLength}
      placeholder={placeholder}
     />


    
    </Box >
  )
})