import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  TextField,
} from "@mui/material";
import theme from "../../../temaConfig";
import { useDispatch } from "react-redux";
import { registerClient, updateClient } from "../../../redux/usersDucks";
import { useCookies } from "react-cookie";
import { NumericFormat } from "react-number-format";
import CustomDialog from '../CustomDialog';

const initialState = {
  name: "",
  email: "",
  address: "",
  zipCode: "",
  city: "",
  state: "",
  phoneNumber: "",
  nickname: "",
  score: "0",
};

export const FormularioDatos = ({ setOpenDialog, openDialog, edit, handleCloseDetails, selectedCliente }) => {
  const [cookies, setCookie] = useCookies();

  const [formData, setFormData] = useState(initialState);

  const [invalidFields, setInvalidFields] = useState([]);

  const dispatch = useDispatch();

  const handleClose = () => {
    setOpenDialog(false);
    setInvalidFields([]);
    handleCloseDetails();
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    setInvalidFields(invalidFields.filter((field) => field !== name));
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    const invalidFieldsArray = [];
    if (!formData.name) {
      invalidFieldsArray.push("name");
    }
    if (!formData.email) {
      invalidFieldsArray.push("email");
    }
    if (!formData.address) {
      invalidFieldsArray.push("address");
    }
    if (!formData.zipCode) {
      invalidFieldsArray.push("zipCode");
    }

    if (invalidFieldsArray.length > 0) {
      setInvalidFields(invalidFieldsArray);
      return;
    }

    dispatch(
      edit
        ? updateClient(formData, cookies.csrf_access_token)
        : registerClient(formData, cookies.csrf_access_token)
    );

    // Aquí puedes realizar acciones adicionales con los datos ingresados
    setFormData(initialState);
    handleClose();
  };

  const isFieldInvalid = (fieldName) => {
    let include = invalidFields.includes(fieldName);
    return include;
  };

  useEffect(() => {
    if (edit) {
      setFormData(edit);
    } else {
      setFormData(initialState);
    }
    // setFormData(initialState);
  }, [edit]);

  return (
    <div>
      <CustomDialog
        open={openDialog && !selectedCliente}
        onClose={handleClose}
        title="Registrar Cliente"
        maxWidth="md"
        width="100%"
        maxHeight="60vh"
        actions={
          <>
            <Button 
              variant="outlined"
              color="error"
              onClick={handleClose}
            >
              Cancelar
            </Button>
            <Button 
              variant="contained" 
              color="buttonGreenPink"
              onClick={handleSubmit}
            >
              Enviar
            </Button>
          </>
        }
      >
        <form
          style={{
            margin: "1rem 0",
            paddingTop: "1rem",
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: theme().spacing(2),
            padding: theme().spacing(2),
          }}
          onSubmit={handleSubmit}
        >
          <TextField
            label="Nombre"
            name="name"
            variant="outlined"
            sx={{ margin: "1rem 0 0 0" }}
            value={formData.name}
            onChange={handleChange}
            required
            error={isFieldInvalid("name")}
          />
          <TextField
            label="Correo"
            name="email"
            variant="outlined"
            sx={{ margin: "1rem 0 0 0 " }}
            value={formData.email}
            onChange={handleChange}
            required
            error={isFieldInvalid("email")}
          />
          <TextField
            label="Dirección"
            name="address"
            variant="outlined"
            value={formData.address}
            onChange={handleChange}
            required
            error={isFieldInvalid("address")}
          />
          <NumericFormat
            label="Código Postal"
            name="zipCode"
            variant="outlined"
            value={formData.zipCode}
            onValueChange={(values) => {
              const { formattedValue, value } = values;
              setFormData({
                ...formData,
                zipCode: value,
              });
            }}
            customInput={TextField}
            decimalScale={0}
            allowNegative={false}
            prefix=""
            maxLength={5}
            max={99999}
            isNumericString
            required
            error={isFieldInvalid("zipCode")}
          />
          <TextField
            label="Ciudad"
            name="city"
            variant="outlined"
            value={formData.city}
            onChange={handleChange}
          />
          <TextField
            label="Estado"
            name="state"
            variant="outlined"
            value={formData.state}
            onChange={handleChange}
          />
          <TextField
            label="Teléfono"
            name="phoneNumber"
            variant="outlined"
            type="number"
            value={formData.phoneNumber}
            onChange={handleChange}
            required
          />
          <TextField
            label="Nickname"
            name="nickname"
            variant="outlined"
            value={formData.nickname}
            onChange={handleChange}
            required
          />
          <NumericFormat
            label="Score"
            name="score"
            variant="outlined"
            value={formData.score}
            onValueChange={(values) => {
              const { formattedValue, value } = values;
              setFormData({
                ...formData,
                score: value,
              });
            }}
            customInput={TextField}
            decimalScale={0}
            allowNegative={false}
            prefix=""
            isNumericString
            required
          />
        </form>
      </CustomDialog>
    </div>
  );
};
