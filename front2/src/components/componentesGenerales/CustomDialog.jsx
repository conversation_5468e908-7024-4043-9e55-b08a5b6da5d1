import React from "react";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTheme } from "@emotion/react";

const CustomDialog = ({
  open,
  onClose,
  title,
  children,
  actions,
  dialogStyles = {},
  maxHeight = "50vh",
  maxWidth = "429px",
  width = "429px",
}) => {
  const theme = useTheme();


  const defaultStyles = {
    '& .MuiDialog-paper': {
      borderRadius: '24px',
      maxWidth: maxWidth,
      width: width,
      maxHeight: maxHeight, // Limitar la altura máxima al 80% de la pantalla
      padding: '20px',
      boxShadow: '0px 4px 10px rgba(0,0,0,0.2)',
      fontFamily: 'Filson pro',
      backgroundColor: theme.palette.background.paper,
      display: 'flex',
      flexDirection: 'column', // Asegurar que el contenido del diálogo sea columnar
    },
    '& .MuiDialogTitle-root': {
      fontFamily: 'Filson pro',
      padding: '8px',
      fontSize: '16px',
      fontWeight: '500',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
  
      '& .MuiIconButton-root': {
        padding: '0',
        position: 'unset',
        '&:hover': {
          backgroundColor: '#f5f5f5',
        },
      },
    },
    '& .MuiDialogContent-root': {
      flex: 1, // Hacer que el contenido ocupe el espacio disponible
      padding: '0',
      // overflowY: 'auto', // Permitir desplazamiento interno si el contenido crece
    },
    '& .MuiDialogActions-root': {
      display: 'block',
      height: 'auto',
      overflowY: 'hidden',
    },
  };
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      scroll="paper"
      sx={{ ...defaultStyles, ...dialogStyles }}
      aria-labelledby="custom-dialog-title"
      aria-describedby="custom-dialog-description"
    >
      <DialogTitle id="custom-dialog-title">
        {title}
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={(theme) => ({
            color: theme.palette.grey[500],
            position: 'absolute',
            right: 8,
            top: 8,
          })}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
  
      <DialogContent>{children}</DialogContent>
  
      {actions && (
        <DialogActions sx={{ width: '100%', display: 'flex !important', justifyContent: 'flex-end', padding: '0' }}>
          {actions}
        </DialogActions>
      )}
    </Dialog>
  );

};

export default CustomDialog;
