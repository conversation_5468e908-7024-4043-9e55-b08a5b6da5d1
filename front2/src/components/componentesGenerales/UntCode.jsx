import { Autocomplete, Grid, TextField } from "@mui/material";


export const UntCode = (
    {
        UNITCODE,
        unitCodeValidation,manejarCambioDeCodigoUnidad,
        setUnitCodeValidation,
        productPersist,
        setProductPersist,
        req=false
    }) => {
    return (
            <Autocomplete
                margin="normal"
                disablePortal
                fullWidth
                id={UNITCODE.nombre}
                options={UNITCODE.options}
                value={unitCodeValidation.valor}
                // value={productPersist?.unitCode}
                sx={{ width: "100%" }}
                onChange={manejarCambioDeCodigoUnidad}
                renderInput={(params) => (
                    <TextField
                        color={unitCodeValidation.color}
                        error={unitCodeValidation.error}
                        helperText={unitCodeValidation.textoAyuda}
                        {...params}
                        label={UNITCODE.label}
                        required={req}
                    />
                )}
            />
    );
}