import React from 'react'
import { Box, Typography } from '@mui/material'
import { useTheme } from '@mui/material/styles';



const FormatMoneyDetails = ({ type, amount }) => {
    const theme = useTheme();
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >

        <Typography
          variant="body2" color="text.secondary"
          sx={{
            fontWeight: "400",
            // lineHeight: "24px",
            // fontSize: "16px",
            color: theme.palette.text.greenGrey
          }}>{type}: </Typography>

        <Box sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          gap: "5px",
          width: "40%",
        }}>

          <Typography component="span" variant="body2" sx={{
            fontWeight: "400",
            lineHeight: "24px",
            fontSize: "16px",
            color: theme.palette.text.primary,
          }} >
            ${amount}
          </Typography>
        </Box>
      </Box>
    );
  };

export default FormatMoneyDetails
