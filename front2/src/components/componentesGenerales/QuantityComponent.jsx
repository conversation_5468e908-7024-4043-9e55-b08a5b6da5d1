import React, { useEffect, useState } from "react";
import { Box, IconButton, TextField, Typography } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import RemoveIcon from "@mui/icons-material/Remove";
import RemoveCircleIcon from "@mui/icons-material/RemoveCircle";
import { NumericFormat } from "react-number-format";

const QuantityComponent = (props) => {
  const {
    amount,
    totalUnits,
    index,
    enterIndex,
    handleDecrease,
    handleIncrease,
    handleChangeAmount,
    totalEntered,
    totalAmount,
  } = props;

  const [unitRemaining, setUnitRemaining] = useState(totalEntered - totalAmount );


  useEffect(() => {
    setUnitRemaining(totalUnits - totalAmount);
  }, [totalUnits, totalAmount]);

  return (
    <Box display="flex" alignItems="center">
      <IconButton
        variant="outlined"
        color="primary"
        onClick={() => handleDecrease(index, enterIndex)}
        disabled={amount === 0}
      >
        {amount === 0 ? <RemoveIcon /> : <RemoveIcon color="buttonGreenPink"/>}
      </IconButton>
     {/*  <TextField
        value={amount}
         onChange={(event) => handleChangeAmount(event.target.value, index, enterIndex)} 
        variant="standard"
        sx={{ width: "80px", mx: "8px" }}
        inputProps={{ style: { textAlign: "center" } }}
      />
 */}
      <Typography variant="body1" sx={{ mx: "8px" }}>{amount}</Typography>
      {/* <NumericFormat
        value={amount}
        onValueChange={(values) => {
          const { formattedValue, value } = values;
          handleChangeAmount(value, index, enterIndex);
        }}
        customInput={TextField}
        thousandSeparator
        decimalScale={0}
        allowNegative={false}
        decimalSeparator="."
        fixedDecimalScale={true}
        prefix=""
        variant="outlined"
        sx={{ width: "80px", mx: "8px" }}
        inputProps={{ style: { textAlign: "center" } }}
      /> */}
      <IconButton
        variant="outlined"
        color="primary"
        onClick={() => handleIncrease(index, enterIndex)}
        disabled={totalUnits == totalEntered}
      >
        {totalAmount + parseInt(totalEntered) >= totalUnits ? (
          <AddIcon fontSize="10px" width="10px" height="10px" color="buttonGreenPink"/>
        ) : (
          <AddIcon color="disabled"/>
        )}
      </IconButton>
    </Box>
  );
};

export default QuantityComponent;
