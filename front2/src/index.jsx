//Version React 17
// import React from "react";
// import ReactDOM from "react-dom";
// import "./index.css";
// import App from "./App.jsx";
// import { CookiesProvider } from "react-cookie";

// import { Provider } from "react-redux";
// import generateStore from "./redux/store";

// const store = generateStore();

// ReactDOM.render(
//   <React.StrictMode>
//     <Provider store={store}>
//       <CookiesProvider>
//         <App />
//       </CookiesProvider>
//     </Provider>
//   </React.StrictMode>,
//   document.getElementById("root")
// );

import React from "react";
import { createRoot } from "react-dom/client";
import "./index.css";

import App from "./App.jsx";
import { CookiesProvider } from "react-cookie";

import { Provider } from "react-redux";
import generateStore from "./redux/store";

const store = generateStore();

const root = document.getElementById("root");
createRoot(root).render(
  // <React.StrictMode>
    <Provider store={store}>
      <CookiesProvider>
        <App />
      </CookiesProvider>
    </Provider>
  // </React.StrictMode>
);
