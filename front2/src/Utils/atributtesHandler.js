import amazonLogo from "../components/img/amazonLogo.png";
import claroShopLogo from "../components/img/claroShopLogo.png"
import mercadoLibreLogo from "../components/img/mercadoLibreLogo.png"
import anonimoLogo from "../components/img/anonimo.jpg"
import walmartLogo from "../components/img/walmartLogo.png"

const orderInternalStatuses = [
    { type: "listSubheader", subHeaderName: "Flujo general" },
    { type: "menuItem", orderInternalStatus: "No revisado", orderInternalStatusId: 1, orderInternalStatusType: "Flujo general", orderInternalStatusTypeId: 1 },
    
    { type: "listSubheader", subHeaderName: "Pedido típico" },
    { type: "menuItem", orderInternalStatus: "Pedido al proveedor", orderInternalStatusId: 2, orderInternalStatusType: "Pedido típico", orderInternalStatusTypeId: 2 },
    { type: "menuItem", orderInternalStatus: "En camino(proveedor)", orderInternalStatusId: 3, orderInternalStatusType: "Pedido típico", orderInternalStatusTypeId: 2 },
    { type: "menuItem", orderInternalStatus: "Recibido parcialmente", orderInternalStatusId: 4, orderInternalStatusType: "Pedido típico", orderInternalStatusTypeId: 2 },
    { type: "menuItem", orderInternalStatus: "Stock Atenea", orderInternalStatusId: 5, orderInternalStatusType: "Pedido típico", orderInternalStatusTypeId: 2 },
    { type: "menuItem", orderInternalStatus: "Listo para empaquetar", orderInternalStatusId: 6, orderInternalStatusType: "Pedido típico", orderInternalStatusTypeId: 2 },
    { type: "menuItem", orderInternalStatus: "Enviado al cliente", orderInternalStatusId: 7, orderInternalStatusType: "Pedido típico", orderInternalStatusTypeId: 2 },
    { type: "menuItem", orderInternalStatus: "Entregado al cliente", orderInternalStatusId: 8, orderInternalStatusType: "Pedido típico", orderInternalStatusTypeId: 2 },

    { type: "listSubheader", subHeaderName: "Pedido atípico" },
    { type: "menuItem", orderInternalStatus: "En espera... No surtir", orderInternalStatusId: 9, orderInternalStatusType: "Pedido atípico", orderInternalStatusTypeId: 3 },
    { type: "menuItem", orderInternalStatus: "Finalizado: No concretado", orderInternalStatusId: 10, orderInternalStatusType: "Pedido atípico", orderInternalStatusTypeId: 3 }
];

export const getStatusInternoPropertyUsingStatusId2 = (statusInternoIdParam,property,namesStatusInterno) =>{
    var index = orderInternalStatuses.findIndex(statusObj => statusObj.orderInternalStatusId===statusInternoIdParam)
    if(index === -1){
        if(property === 'type'){
            return 'noType'
        }else if(property === 'orderInternalStatusId'){
            return -1
        }else if(property === 'orderInternalStatus'){
            return 'Status interno desconocido'
        }
            return "Status interno desconocido"
    }else{
        return  orderInternalStatuses[index][property]
    }
}

const amazonMarketplaceStyle = {
    color : "#fdfffa",
    backgroundColor : "#242f41",
    logo : amazonLogo
}  
const claroShopMarketplaceStyle = {
    color : "#ffffff",
    backgroundColor : "#df262c",
    logo : claroShopLogo
}
const mercadoLibreMarketplaceStyle = {
    color : "#ffe600",
    backgroundColor : "#2d3277",
    logo : mercadoLibreLogo
}

const walmartMarketplaceStyle = {
    color : "#ffc220",
    backgroundColor : "#0071ce",
    logo : walmartLogo
}
const noReconocidoMarketplaceStyle = {
    color : "#696969",
    backgroundColor : "#F5F5F5",
    logo : anonimoLogo
}
    

  export const checkPaidAmount = (pedido) => {
    let paidAmount
    let fee
    let shipping
    let receivedAmount
    pedido.paidAmount=pedido.paidAmount.toString()
    if(pedido.paidAmount.includes('-')){
        paidAmount = 'Desconocido'
        fee = 'Desconocido'
        shipping = 'Desconocido'
        receivedAmount = 'Desconocido'
      }else{
          paidAmount = pedido.paidAmount
          fee = pedido.fee
          shipping = pedido.shipping
          receivedAmount = pedido.receivedAmount
      }
      return {paidAmount,fee,shipping,receivedAmount}
}

const fullFilmentChannelList  = [
    {fullFilmentChannelNumber:'1',fullFilmentChannelName:'Marketplace'},
    {fullFilmentChannelNumber:'2',fullFilmentChannelName:'Seller'},
];

export const getfullFilmentChannelListPropertyUsingfullFiltmentNumber = (fullFilmentChannelNumberParam,property) =>{
    var index = fullFilmentChannelList.findIndex(statusObj => statusObj.fullFilmentChannelNumber===fullFilmentChannelNumberParam)
    if(index === -1){
        if(property === 'fullFilmentChannelNumber'){
            return '-1'
        }else if(property === 'fullFilmentChannelName'){
            return 'fulFiltmentChannelName desconocido'
        }
            return "FulfilmentChannelName desconocido"
    }else{
        return  fullFilmentChannelList[index][property]
    }
}

export const Red = {
    color:"white",
    backgroundColor: "red",
    borderRadius: "0.5em",
    fontWeight: "bold",
    padding: ".20em"
  };
export const Yellow = {
    color:"white",
    backgroundColor: "rgba(227,227,0)",
    borderRadius: "0.5em",
    fontWeight: "bold",
    padding: ".20em"
  };
export const Green = {
    color:"white",
    backgroundColor: "green",
    borderRadius: "0.5em",
    fontWeight: "bold",
    padding: ".20em"
  };
  
export const Orange = {
    marginTop: "-5",
    color: "white",
    backgroundColor: "orange",
    borderRadius: "0.5em",
    fontWeight:"bold",
    padding:".20em"
  }
  
export const Grey = {
    color: "white",
    backgroundColor: "dimgrey",
    borderRadius: "0.5em",
    fontWeight:"bold",
    padding:".20em"
  }
  const orderStatuses = [
    { orderStatus: "Pendiente de pago", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 1 },
    { orderStatus: "Cancelado no procesado", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 2 },
    { orderStatus: "Error en el pedido", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 3 },
    { orderStatus: "Inconsistencia de datos", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 4 },
    { orderStatus: "Reembolsado", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 5 },
    { orderStatus: "Devuelto", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 6 },
    { orderStatus: "Rechazado", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 7 },
    { orderStatus: "Contracargo", orderStatusFlag: "Red", orderStatusFlagId: 1, orderStatusId: 8 },
    { orderStatus: "Enviado", orderStatusFlag: "Yellow", orderStatusFlagId: 2, orderStatusId: 9 },
    { orderStatus: "Entregado", orderStatusFlag: "Green", orderStatusFlagId: 3, orderStatusId: 10 },
    { orderStatus: "Cancelado ya procesado", orderStatusFlag: "Green", orderStatusFlagId: 3, orderStatusId: 11 },
    { orderStatus: "Entregado sin posibilidad de cambios", orderStatusFlag: "Green", orderStatusFlagId: 3, orderStatusId: 12 },
    { orderStatus: "Esperando stock", orderStatusFlag: "Orange", orderStatusFlagId: 4, orderStatusId: 13 },
    { orderStatus: "Pendiente de envío", orderStatusFlag: "Orange", orderStatusFlagId: 4, orderStatusId: 14 },
    { orderStatus: "En Devolución", orderStatusFlag: "Orange", orderStatusFlagId: 4, orderStatusId: 15 },
    { orderStatus: "Acordar con el comprador", orderStatusFlag: "Orange", orderStatusFlagId: 4, orderStatusId: 16 }
];
export  const getStatusPropertyUsingStatusId2 = (statusIdParam,property,namesStatus) =>{
    let index = -1
    if( namesStatus.length > 0){
         index = namesStatus.findIndex(statusObj => statusObj.orderStatusId===statusIdParam)
    }else{
     index = orderStatuses.findIndex(statusObj => statusObj.orderStatusId===statusIdParam)
    }
    let arr = namesStatus.length > 0 ? namesStatus : orderStatuses
    if(index === -1){
        if(property === 'orderStatusId'){
            return -1
        }else if(property === 'orderStatus'){
            return 'Status desconocido'
        }else if(property === 'orderStatusFlag'){
            return Grey
        } 
        return "Status desconocido"
    }else{
        return  arr[index][property]
    }
}


export const crearFechaFormato = (fechaParam) => {
    let fecha
    let t = fechaParam.split(/[- :]/);

    try{
        let fechaDate = new Date(t[0], t[1]-1, t[2], t[3], t[4], t[5])
        let dia = fechaDate.getDate()
        let mes = fechaDate.getMonth()
        let anio = fechaDate.getFullYear()
        let mins = fechaDate.getMinutes()
        let secs = fechaDate.getSeconds()
        let hour = fechaDate.getHours()
        fecha = dia + '/' + (mes+1) + '/' + anio + ' ' + hour + ':' + mins + ':' + secs
    }catch(error){
          fecha = fechaParam.toString()
    }
    return fecha
}

// User Roles
// export const GetRole = () => {
//     const role = useSelector(state => state.user.role)
//     return role
// }
// export const IsAdmin = () => {
//     const role = useSelector(state => state.user.role)
//     console.log('roleeeeeeee', role)
//     if(role === 'admin' || role === 'Administrative-accountant')  return true
//     return false
// }