/*Import configs */
import { useDispatch } from "react-redux";
import {
  UNIDADES_MEDIDA_PESO,
  CONDICIONES_POSILBES,
  CODIGO_POSIBLE_UNIDAD,
  UNIDADES_MEDIDA_LONGITUD,
  LONGITUD_PERIMITIDA_UPC,
} from "./config";

// import { obtenerTotalFacturas } from "../redux/facturasDucks";

import moment from "moment";
import { obtenerTotalFacturas } from "../redux/facturasDucks";

/*Expresiones regulares */
const regexYouTubeUrl =
  /^((?:https?:)?\/\/)?((?:www|m)\.)?((?:youtube(-nocookie)?\.com|youtu.be))(\/(?:[\w\-]+\?v=|embed\/|v\/)?)([\w\-]+)(\S+)?$/;
const regexNumbersOnly = /^\d+$/;
const regexEmail = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const regexName = /^[a-zA-Z\'\-]+$/;
const regexRFC = /^[A-Za-zñÑ&]{3,4}\d{6}\w{3}$/;
/*Ratios de conversion */
/*to cm*/
const ratioMmtoCm = 0.1;
const ratioMtrtoCm = 100;
const ratioPulgtoCm = 2.54;
/*to kg*/
const ratioGrtoKg = 0.001;
const ratioLbtokg = 0.453592;

export const toCm = (magnitude, unitMeasure) => {
  if (magnitude === "") {
    return magnitude;
  }
  let selectedRatio;
  switch (unitMeasure) {
    case "mm":
      selectedRatio = ratioMmtoCm;
      break;
    case "mtr":
      selectedRatio = ratioMtrtoCm;
      break;
    case "pulg":
      selectedRatio = ratioPulgtoCm;
      break;
    default:
      selectedRatio = 1;
  }
  let unidadConvertida = parseFloat(magnitude) * selectedRatio;
  return unidadConvertida;
};

export const formatDate = (timestamp) => {
  return moment(timestamp).format("DD/MM HH:mm");
};

export const toKg = (magnitude, unitMeasure) => {
  if (magnitude === "") {
    return magnitude;
  }
  let selectedRatio;
  switch (unitMeasure) {
    case "gr":
      selectedRatio = ratioGrtoKg;
      break;
    case "lb":
      selectedRatio = ratioLbtokg;
      break;
    default:
      selectedRatio = 1;
  }
  return parseFloat(magnitude) * selectedRatio;
};

export const checkLength = (campo, stringToCheck, length) => {
  return stringToCheck.length <= length
    ? true
    : `${campo} debe ser menor a ${length}`;
};

export const checkUnitCode = (unitCode) => {

  // Extraer solo las iniciales de cada elemento en el array
  const unitCodesOnly = CODIGO_POSIBLE_UNIDAD.map((item) => item.split(" - ")[0]);

  // Extraer solo las iniciales en caso de que `unitCode` venga con descripción
  const extractedCode = unitCode.includes(" - ") ? unitCode.split(" - ")[0] : unitCode;

  return unitCodesOnly.includes(extractedCode)
    ? true
    : "Seleccionar código de unidad";
};


export const checkNuevoUsado = (nuevoUsado) => {
  return CONDICIONES_POSILBES.includes(nuevoUsado)
    ? true
    : `Seleccionar una condición del catalogo`;
};

export const checkSATCode = (SATCode) => {
  if (SATCode.length !== 8) {
    return `Deben ser 8 dígitos`;
  }
  return regexNumbersOnly.test(SATCode)
    ? true
    : `El código de unidad debe tener solo números`;
};

export const checkUPC = (upc) => {
  if (!LONGITUD_PERIMITIDA_UPC.includes(upc.length)) {
    return `Longitud no permitida`;
  }
  return regexNumbersOnly.test(upc) ? true : `El upc debe tener solo números`;
};

export const checkValidVideoUrl = (videoUrl) => {
  return regexYouTubeUrl.test(videoUrl)
    ? true
    : `El url no es un link de youtube válido`;
};

export const checkValidEmail = (userEmail) => {
  return regexEmail.test(userEmail)
    ? true
    : false;
};
export const checkValidName = (userName) => {
  return regexName.test(userName) ? true : false;
};
export const checkValidRFC = (userRFC) => {
  return regexRFC.test(userRFC) ? true : false;
};

export const checkValid3Char = (text2validate) => {
  return text2validate.length >= 3;
};

/*Obligatorio */
export const checkLengthUnit = (unidadLongitud) => {
  return UNIDADES_MEDIDA_LONGITUD.includes(unidadLongitud)
    ? true
    : `Seleccionar unidad`;
};

export const checkWeightUnit = (unidadPeso) => {
  return UNIDADES_MEDIDA_PESO.includes(unidadPeso)
    ? true
    : `Seleccionar unidad`;
};
/*Solo formulario*/
export const checkMagnitude = (stringToTry) => {
  try {
    let unidadConvertida = parseFloat(stringToTry);

    return isNaN(unidadConvertida) ? "Magnitud inválida" : true;
  } catch (error) {
    return "Magnitud no válida";
    // expected output: ReferenceError: nonExistentFunction is not defined
    // (Note: the exact output may be browser-dependent)
  }
};

export const concatErrors = (totalErrors, errorToConcat) => {
  if (errorToConcat !== "") {
    totalErrors === ""
      ? (totalErrors = `${errorToConcat.charAt(0).toUpperCase() + errorToConcat.slice(1)
        }`)
      : (totalErrors = `${totalErrors}, ${errorToConcat.charAt(0).toLowerCase() + errorToConcat.slice(1)
        }`);
  }
  return totalErrors;
};

export const rateToPercent = (rate) => {
  return rate / 100
}


export const traversePath = (path, obj) => {
  let ons = obj;
  path.forEach(element => {
    ons = ons[element];
  });
  return ons;
};

const createObjectUsingPath = (path, key, objectOriginal, nestedObj) => {
  const objetoCreado = { ...objectOriginal }
  let aux = objetoCreado
  path.forEach((element) => {
    aux[element] = aux[element] instanceof Array ? [...aux[element]] : { ...aux[element] }
    aux = aux[element]
  });
  aux[key] = nestedObj
  return objetoCreado
}

export const createObjectFromInside = (path, key, originalObj, nestedObj) => {
  const parent_obj = traversePath(path, originalObj)
  let objR = parent_obj instanceof Array ? (
    parent_obj.map((c, i) => {
      if (i === key) {
        return nestedObj;
      } else {
        return c
      }
    })
  ) : { ...parent_obj, [key]: nestedObj }
  for (let i = path.length - 1; i > -1; i--) {
    const nO = traversePath(path.slice(0, i), originalObj)
    if (nO instanceof Array) {
      objR = nO.map((c, j) => {
        if (j === path[i]) {
          return objR;
        } else {
          return c
        }
      });
    } else {
      objR = { ...nO, [path[i]]: objR }
    }
  }
  return objR
}

export const numberOrCero = stringNumber => {
  return Number(stringNumber) || 0
}

/* utils/dateFormat.js */
export const sameDay = (d1, d2) =>
  d1.getFullYear() === d2.getFullYear() &&
  d1.getMonth() === d2.getMonth() &&
  d1.getDate() === d2.getDate();

export const fmtHeader = (ts) => {
  const date = new Date(ts);
  const now  = new Date();

  const today     = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today); yesterday.setDate(today.getDate() - 1);

  if (date >= today)             return "hoy";
  if (date >= yesterday)         return "ayer";

  return date.toLocaleDateString("es-ES", {
    day: "2-digit",
    month: "long",
    year: "numeric",
  });
};

export const fmtHour = (ts) =>
  new Date(ts).toLocaleTimeString("es-ES", { hour: "2-digit", minute: "2-digit" });


export const formatHeaderDate = (isoKey) => {
  // isoKey viene en formato yyyy-mm-dd (UTC)
  const dateUTC = new Date(`${isoKey}T00:00:00Z`);

  // "Hoy" y "Ayer" calculados también en UTC
  const now = new Date();
  const todayUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
  const yesterdayUTC = new Date(todayUTC);
  yesterdayUTC.setUTCDate(todayUTC.getUTCDate() - 1);

  if (dateUTC.getTime() === todayUTC.getTime()) return "Hoy";
  if (dateUTC.getTime() === yesterdayUTC.getTime()) return "Ayer";

  return dateUTC.toLocaleDateString("es-ES", {
    day: "numeric",
    month: "long",
    year: "numeric",
    timeZone: "UTC",
  });
};

export  const formatTime = (ts) =>
  new Date(ts).toLocaleTimeString("es-ES", {
    hour: "2-digit",
    minute: "2-digit",
    timeZone: "UTC",
  });