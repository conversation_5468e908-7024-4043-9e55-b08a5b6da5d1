import ctLogo from "../components/img/ctinternacional.png";
import dcLogo from "../components/img/dcmayorista.png"
import exelLogo from "../components/img/exel.png"
import ingramLogo from "../components/img/ingram.png"
import intcomexLogo from "../components/img/intcomex.png"
import pchLogo from "../components/img/logo-pch-2.png"
import sterenLogo from "../components/img/steren.png"
import syscomLogo from "../components/img/SYSCOM-(2).png"
import tecnoLogo from "../components/img/tecnosinergia.png"
import tvcLogo from "../components/img/tvc1.png"
import anonimoLogo from "../components/img/anonimo.jpg"

const ctStyle = {
    logo : ctLogo
}
const dcStyle = {
    logo : dcLogo
}
const exelStyle = {
    logo : exelLogo
}
const ingramStyle = {
    logo : ingramLogo
}
const intcomexStyle = {
    logo : intcomexLogo
}
const pchStyle = {
    logo : pchLogo
}
const sterenStyle = {
    logo : sterenLogo
}
const syscomStyle = {
    logo : syscomLogo
}
const tecnoStyle = {
    logo : tecnoLogo
}
const tvcStyle = {
    logo : tvcLogo
}

const noReconocidoProviderStyle = {
    logo : anonimoLogo
}
export const providerList  = [
    {issuerRfc:'CIN960904FQ2',providerName:'Ct', providerStyle:ctStyle},
    {issuerRfc:'DMA980313MW7',providerName:'Dc', providerStyle:dcStyle},
    {issuerRfc:'ENO8910131AA',providerName:'Exel', providerStyle:exelStyle},
    {issuerRfc:'IMM9304016Z4',providerName:'Ingram', providerStyle:ingramStyle},
    {issuerRfc:'CEN980619FU4',providerName:'Intcomex', providerStyle:intcomexStyle},
    {issuerRfc:'PMA14021043A',providerName:'Pch', providerStyle:pchStyle},
    {issuerRfc:'EST850628K51',providerName:'Steren', providerStyle:sterenStyle},
    {issuerRfc:'SSC840823JT3',providerName:'Syscom', providerStyle:syscomStyle},
    {issuerRfc:'TEC060605PM1',providerName:'Tecnosinergia', providerStyle:tecnoStyle},
    {issuerRfc:'TVC060802NE4',providerName:'Tvc', providerStyle:tvcStyle},
];

export const getProviderListPropertyUsingIssuerRFC = (issuerRfc,property) =>{
    var index = providerList.findIndex(providerObj => providerObj.issuerRfc===issuerRfc)
    if(index === -1){
      if(property === 'issuerRfc'){
        return '-1'
      }else if(property === 'providerName'){
        return 'proveedor desconocido'
      }else if(property === 'providerStyle'){
        return noReconocidoProviderStyle
      }
        return "Proveedor desconocido"
    }else{
      return  providerList[index][property]
    }
  }
