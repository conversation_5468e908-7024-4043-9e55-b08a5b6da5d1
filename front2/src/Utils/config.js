//export const URLSERVER = "https://127.0.0.1:5000/"; // Servidor de desarrollo (local)
export const URLSERVER = "https://localhost:5000"; // Servidor de desarrollo (local)
//export const URLSERVER = "https://*************:5000"; // Servidor de desarrollo (local)
//export const URLSERVER = 'https://*************' //Servidor de produccion windows
// export const URLSERVER = "https://*************"; // Servidor de preproduccion
//export const URLSERVER = 'https://*************' // Servidor MLO VPS
//export const URLSERVER = 'https://***************' // Servidor CP VPS
//export const URLSERVER = "https://tortugalmo-api.creativeplanet.com.mx"; // Servidor CP VPS
//export const URLSERVER = "https://tortuga-api.creativeplanet.com.mx"; // Servidor CP VPS
//export const URLSERVER = "https://tortugalmo.creativeplanet.com.mx";
//export const URLSERVER = 'https://*************' //Servidor de produccion Linux
//export const URLSERVER = "https://*************:5000"; // Servidor de produccion Linux
//export const URLSERVER = "https://*************:5000"; // Servidor de produccion Linux
// console.log("ppppppppppppppppppppp");
//console.log(process.env.NODE_ENV);
// console.log("ppppppppppppppppppppp");
/* export const URLSERVER = process.env.NODE_ENV; */
export const EXTENSIONES_PERMITIDAS_IMAGENES_PRODUCTOS = [
  "pjp",
  "jpg",
  "pjpeg",
  "jpeg",
  "jfif",
  "png",
  "webp",
];
export const CODIGO_POSIBLE_UNIDAD = [//
  "H87 - Pieza",
  "EA - Elemento",
  "E48 - Unidad de servicio",
  "ACT - Actividad",
  "KGM - Kilogramo",
  "E51 - Trabajo",
  "A9 - Tarifa",
  "MTR - Metro",
  "AB - Paquete a granel",
  "BB - Caja base",
  "KT - KIT",
  "SET - Conjunto",
  "LTR - Litro",
  "XBX - Caja",
  "MON - Mes",
  "HUR - Hora",
  "MTK - Metro Cuadrado",
  "11 - Equipos",
  "MGM - Miligramo",
  "XPK - Paquete",
  "XKI - Kit (Conjunto de piezas)",
  "AS - Variedad",
  "GRM - Gramo",
  "PR - Par",
  "DPC - Docenas de piezas",
  "XUN - Unidad",
  "DAY - Día",
  "XLT - Lote",
  "10 - Grupos",
  "MLT - Mililitro",
  "E54 - Viaje",
];

export const CONDICIONES_POSILBES = ["Nuevo", "Usado"];
export const UNIDADES_MEDIDA_PESO = ["gr", "lb", "kg"];
export const LONGITUD_PERIMITIDA_UPC = [8, 12, 13];
export const UNIDADES_MEDIDA_LONGITUD = ["mm", "cm", "mtr", "pulg"];
const LABEL_LONGITUD_UNIDAD = "Unidad de longitud";
const LABEL_PESO_UNIDAD = "Unidad de peso";
const LABEL_PESO = "Peso";
const LABEL_LARGO = "Largo";
const LABEL_ANCHO = "Ancho";
const LABEL_ALTO = "Alto";

export const BRAND = {
  label: "Marca",
  longitud: 50,
  nombre: "brand",
};

export const SATCODE = {
  label: "Código SAT",
  longitud: 8,
  nombre: "satCode",
};

export const CONDITION = {
  label: "Condición",
  nombre: "condition",
  options: CONDICIONES_POSILBES,
};

export const UNITCODE = {
  label: "Código de unidad",
  nombre: "unitCode",
  options: CODIGO_POSIBLE_UNIDAD,
};

export const MODEL = {
  label: "Modelo",
  longitud: 50,
  nombre: "model",
};
export const DESCRIPTION = {
  label: "Descripción",
  longitud: 300,
  nombre: "description",
};
export const LONGDESCRIPTION = {
  label: "Descripción larga",
  longitud: 3000,
  nombre: "longDescription",
};
export const UPC = {
  label: "Upc",
  longitud: 15,
  nombre: "upc",
};
export const VIDEO = {
  label: "Video",
  longitud: 150,
  nombre: "video",
};

export const UNITLONGITUDESHIPPING = {
  label: LABEL_LONGITUD_UNIDAD,
  nombre: "unidadLongitudShipping",
  options: UNIDADES_MEDIDA_LONGITUD,
};
export const UNITWEIGHTSHIPPING = {
  label: LABEL_PESO_UNIDAD,
  nombre: "unidadPesoShipping",
  options: UNIDADES_MEDIDA_PESO,
};
export const UNITLONGITUDEPRODUCT = {
  label: LABEL_LONGITUD_UNIDAD,
  nombre: "unidadLongitudProducto",
  options: UNIDADES_MEDIDA_LONGITUD,
};
export const UNITWEIGHTPRODUCT = {
  label: LABEL_PESO_UNIDAD,
  nombre: "unidadLongitudProducto",
  options: UNIDADES_MEDIDA_PESO,
};

export const LENGTHSHIPPING = {
  label: LABEL_LARGO,
  nombre: "lengthShippingDimention",
  nombreParaUsuario: "largo para envío",
};
export const WIDTHSHIPPING = {
  label: LABEL_ANCHO,
  nombre: "widthShippingDimention",
  nombreParaUsuario: "ancho para envío",
};
export const HEIGHTSHIPPING = {
  label: LABEL_ALTO,
  nombre: "heightShippingDimention",
  nombreParaUsuario: "alto para envío",
};
export const WEIGHTSHIPPING = {
  label: LABEL_PESO,
  nombre: "weightShippingDimention",
  nombreParaUsuario: "peso para envío",
};
export const LENGTHPRODUCT = {
  label: LABEL_LARGO,
  nombre: "lengtProductDimention",
  nombreParaUsuario: "largo para producto",
};
export const WIDTHPRODUCT = {
  label: LABEL_ANCHO,
  nombre: "widthProductDimention",
  nombreParaUsuario: "ancho para producto",
};
export const HEIGHTPRODUCT = {
  label: LABEL_ALTO,
  nombre: "heightProductDimention",
  nombreParaUsuario: "alto para producto",
};
export const WEIGHTPRODUCT = {
  label: LABEL_PESO,
  nombre: "weightProductDimention",
  nombreParaUsuario: "peso para producto",
};

export const SUPPLIERNAME = {
  label: "nombre del proveedor",
  nombre: "supplierName",
  longitud: 50,
};

export const SUPPLIEREXECUTIVE = {
  label: "Ejecutivo de venta",
  nombre: "supplierExecutiveName",
  longitud: 50,
};

export const SUPPLIEREMAIL = {
  label: "Email de proveedor",
  nombre: "supplierEmail",
  longitud: 50,
};

export const SUPPLIERRFC = {
  label: "Rfc del proveedor",
  nombre: "supplierRFC",
  longitud: 13,
};

export const ISSUERRFC = {
  label: "Issuer RFC",
  nombre: "issuerRFC",
  longitud: 13,
};

export const SALESREP = {
  label: "Sales Rep",
  nombre: "salesRep",
  longitud: 25,
};
