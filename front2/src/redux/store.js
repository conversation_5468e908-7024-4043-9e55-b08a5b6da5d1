import { createStore, combineReducers, applyMiddleware } from "redux";
import thunk from "redux-thunk";
import { composeWithDevTools } from "redux-devtools-extension";

import pedidosReducer from "./pedidosDucks";
import usuarioReducer, { leerUsuarioActivoAccion } from "./usersDucks";
import facturasReducer from "./facturasDucks";
import productosReducer from "./productosDucks";
import almacenesReducer from "./almacenesDucks";
import proveedoresReducer from "./proveedoresDucks";
import metricasReducer from "./metricasDucks";
import configuracionReducer from "./configuracionesDucks";
import ventaDirectaReducer from "./ventaDirecta";
import ordenesConsolidadasReducer from "./ordenesConsolidadas";
import kitsReducer from "./kitsDucks";
import publicationsReducer from "./publicacionesDucks";
import companyReducer from "./companyDucks";
import mensajesReducer from "./mensajesDucks";


const rootReducer = combineReducers({
  pedidos: pedidosReducer,
  usuario: usuarioReducer,
  facturas: facturasReducer,
  productos : productosReducer,
  almacenes : almacenesReducer,
  proveedores : proveedoresReducer,
  metricas: metricasReducer,
  ConfiguracionSistema: configuracionReducer,
  ventaDirecta: ventaDirectaReducer,
  ordenesConsolidadas: ordenesConsolidadasReducer,
  kits: kitsReducer,
  publications: publicationsReducer,
  company: companyReducer,
  mensajes: mensajesReducer,
});

export default function generateStore() {
  const store = createStore(
    rootReducer,
    composeWithDevTools(applyMiddleware(thunk))
  );
  leerUsuarioActivoAccion()(store.dispatch);
  return store;
}
