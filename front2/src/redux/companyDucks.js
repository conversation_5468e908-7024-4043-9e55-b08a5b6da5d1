import { instanceAxios } from "./axiosInstance";
//Data inicial

const dataInicial = {
    loadingInfo:{
        name: false,
        image: false,
        rfc: false,
        qr_code: false,
        bankInfo: false
    },
    info: null,
    changingImage: false,
    changingInfo: false,
    message: null,
    severity: "info",
    messageBank: null,
    severityBank: "info",
    loadingCondicion: false,
    messageCondicion: null,
    severityCondicion: "info",
}

const LOADING_INFO = 'LOADING_INFO';
const COLOCAR_MENSAJE_COMPANY = 'COLOCAR_MENSAJE_COMPANY';
const COLOCAR_INFO_COMPANY = 'COLOCAR_INFO_COMPANY';
const COLOCAR_CHANGING_IMAGE = 'COLOCAR_CHANGING_IMAGE';
const COLOCAR_CHANGING_INFO = 'COLOCAR_CHANGING_INFO';
const COLOCAR_MENSAJE_BANK_INFO = 'COLOCAR_MENSAJE_BANK_INFO';
const LOADING_CONDICION = 'LOADING_CONDICION';
const COLOCAR_MENSAJE_CONDICION = 'COLOCAR_MENSAJE_CONDICION';

export default function companyReducer(state = dataInicial, action){
    switch(action.type){
        case LOADING_INFO:
            return {...state, loadingInfo: action.payload}
        case COLOCAR_MENSAJE_COMPANY:
            return {...state, message: action.payload.message, severity: action.payload.severity}
        case COLOCAR_INFO_COMPANY:
            return {...state, info: action.payload}
        case COLOCAR_CHANGING_IMAGE:
            return {...state, changingImage: action.payload}
        case COLOCAR_CHANGING_INFO:
            return {...state, changingInfo: action.payload
            }
        case COLOCAR_MENSAJE_BANK_INFO:
            return {...state, messageBank: action.payload.message, severityBank: action.payload.severity}
        case LOADING_CONDICION:
            return {...state, loadingCondicion: action.payload}
        case COLOCAR_MENSAJE_CONDICION:
            return {...state, messageCondicion: action.payload.message, severityCondicion: action.payload.severity}
        default:
            return state
    }
}

//acciones

export const getCompanyInfo = () => async (dispatch, getState) => {
    dispatch({
        type: LOADING_INFO,
        payload: {name: true, image: true, rfc: true, qr_code: true}
    })
    try {
        const url = "/api/company/getInfoCompany?scope=extended";
        const res = await instanceAxios.get(url);
        if (res.status===200)
        {
            const newUrlCompany = `${res.data.company.imageCompany ? res.data.company.imageCompany : "/companyGeneric.png"}?unique=${new Date().getTime()}`;
            const newUrlQR = `${res.data.company.qr_code_SAT ? res.data.company.qr_code_SAT : "/qr.png"}?unique=${new Date().getTime()}`;
            const company = {...res.data.company, imageCompany: newUrlCompany, qr_code_SAT: newUrlQR};
            dispatch({
                type: COLOCAR_INFO_COMPANY,
                payload: company
            })
        }
    } catch (error) {
        dispatch({
            type: COLOCAR_MENSAJE_COMPANY,
            payload: {message: error.response.data.message, severity: "error"}
        })  
    }
    dispatch({
        type: LOADING_INFO,
        payload: {name: false, image: false, rfc: false, qr_code: false}
    })
    dispatch
}

export const actualizarCompanyInfo = (formData, csrf_access_token, concept) => async (dispatch, getState) => {
    dispatch({
        type: LOADING_INFO,
        payload: {name: true, image: true, rfc: true, qr_code: true}
    })

    try {
        const url = "/api/company/setInfoCompany";
        const res = await instanceAxios.put(url, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'X-CSRF-TOKEN': csrf_access_token
            }
        });
        dispatch({
            type: COLOCAR_MENSAJE_COMPANY,
            payload: {message: "Actualizado con éxito", severity: "success"}
        })
    } catch (error) {
        dispatch({
            type: COLOCAR_MENSAJE_COMPANY,
            payload: {message: error.response.data, severity: "error"}
        })  
    }
    dispatch({
        type: COLOCAR_CHANGING_INFO,
        payload: true
    })
    dispatch({
        type: LOADING_INFO,
        payload: {name: false, image: false, rfc: false, qr_code: false}
    })
    
}

export const cambiarStatusImage = (status) => async (dispatch, getState) => {
    dispatch({
        type: COLOCAR_CHANGING_IMAGE,
        payload: status
    })
}

export const cambiarStatusInfo = (status) => async (dispatch, getState) => {
    dispatch({
        type: COLOCAR_CHANGING_INFO,
        payload: status
    })
}

export const agregarCuentaBancaria = (data, csrf_access_token) => async (dispatch, getState) => {
    dispatch({
        type: LOADING_INFO,
        payload: {bankInfo: true}
    })
    try {
        const url = "/api/company/bankAccount";
        const res = await instanceAxios.post(url, data, {
            headers: {
                'X-CSRF-TOKEN': csrf_access_token
            }
        });
        dispatch({
            type: COLOCAR_MENSAJE_BANK_INFO,
            payload: {message: res.data.mensaje, severity: "success"}
        })
    } catch (error) {
        dispatch({
            type: COLOCAR_MENSAJE_BANK_INFO,
            payload: {message: error.response.data, severity: "error"}
        })  
    }
    dispatch({
        type: LOADING_INFO,
        payload: {bankInfo: false}
    })
}

export const limpiarMessageBank = () => async (dispatch, getState) => {
    dispatch({
        type: COLOCAR_MENSAJE_BANK_INFO,
        payload: {message: null, severity: "info"}
    })
}

export const eliminarCuentaBancaria = (id, csrf_access_token) => async (dispatch, getState) => {
    dispatch({
        type: LOADING_INFO,
        payload: {bankInfo: true}
    })
    try {
        const url = `/api/company/bankAccount/${id}`;
        const res = await instanceAxios.delete(url, {
            headers: {
                'X-CSRF-TOKEN': csrf_access_token
            }
        });
        dispatch({
            type: COLOCAR_MENSAJE_BANK_INFO,
            payload: {message: res.data.mensaje, severity: "success"}
        })
    } catch (error) {
        dispatch({
            type: COLOCAR_MENSAJE_BANK_INFO,
            payload: {message: error.response.data, severity: "error"}
        })  
    }
    dispatch({
        type: LOADING_INFO,
        payload: {bankInfo: false}
    })
}

export const actualizarCuentaBancaria = (id, data, csrf_access_token) => async (dispatch, getState) => {
    dispatch({
        type: LOADING_INFO,
        payload: {bankInfo: true}
    })
    try {
        const url = `/api/company/bankAccount/${id}`;
        const res = await instanceAxios.put(url, data, {
            headers: {
                'X-CSRF-TOKEN': csrf_access_token
            }
        });
        dispatch({
            type: COLOCAR_MENSAJE_BANK_INFO,
            payload: {message: res.data.mensaje, severity: "success"}
        })
    } catch (error) {
        dispatch({
            type: COLOCAR_MENSAJE_BANK_INFO,
            payload: {message: error.response.data, severity: "error"}
        })  
    }
    dispatch({
        type: LOADING_INFO,
        payload: {bankInfo: false}
    })
}

export const agregarCondicionComercial = (data, csrf_access_token) => async (dispatch, getState) => {
    dispatch({
        type: LOADING_CONDICION,
        payload: true
    })
    try {
        const url = "/api/company/commercialTerm";
        const res = await instanceAxios.post(url, data, {
            headers: {
                'X-CSRF-TOKEN': csrf_access_token
            }
        });

        if (res.status===201)
        {
            const newInfo = {...getState().company.info, commercialTerms: [...getState().company.info.commercialTerms, res.data.commercialTermInfo]};

            console.log("newInfo", newInfo);
            dispatch({
                type: COLOCAR_INFO_COMPANY,
                payload: newInfo
            })
            dispatch({
                type: COLOCAR_MENSAJE_CONDICION,
                payload: {message: res.data.mensaje, severity: "success"}
            })
        }
    } catch (error) {
        dispatch({
            type: COLOCAR_MENSAJE_CONDICION,
            payload: {message: error.response.data, severity: "error"}
        })  
    }
    dispatch({
        type: LOADING_CONDICION,
        payload: false
    })
}

export const limpiarMessageCondicion = () => async (dispatch, getState) => {
    dispatch({
        type: COLOCAR_MENSAJE_CONDICION,
        payload: {message: null, severity: "info"}
    })
}

export const eliminarCondicionComercial = (id, csrf_access_token) => async (dispatch, getState) => {
    dispatch({
        type: LOADING_CONDICION,
        payload: true
    })
    try {
        const url = `/api/company/commercialTerm/${id}`;
        const res = await instanceAxios.delete(url, {
            headers: {
                'X-CSRF-TOKEN': csrf_access_token
            }
        });
        if (res.status===200)
        {
            const info = getState().company.info;
            const newInfo = {...info, commercialTerms: info.commercialTerms.filter((item) => item.id !== id)};
            dispatch({
                type: COLOCAR_INFO_COMPANY,
                payload: newInfo
            })
            dispatch({
                type: COLOCAR_MENSAJE_CONDICION,
                payload: {message: res.data.mensaje, severity: "success"}
            })
        }
    } catch (error) {
        dispatch({
            type: COLOCAR_MENSAJE_CONDICION,
            payload: {message: error.response.data, severity: "error"}
        })  
    }
    dispatch({
        type: LOADING_CONDICION,
        payload: false
    })
}

export const actualizarCondicionComercial = (id, data, csrf_access_token) => async (dispatch, getState) => {
    dispatch({
        type: LOADING_CONDICION,
        payload: true
    })
    try {
        const url = `/api/company/commercialTerm/${id}`;
        const res = await instanceAxios.put(url, data, {
            headers: {
                'X-CSRF-TOKEN': csrf_access_token
            }
        });
        console.log("res", res);
        if(res.status===200)
        {
            const newData ={
                commercialTerm: data.commercialTerm,
                id: id
            }
            const info = getState().company.info;
            const newInfo = {...info, commercialTerms: info.commercialTerms.map((item) => item.id === id ? newData : item)};
            console.log("newInfo", newInfo);
            dispatch({
                type: COLOCAR_INFO_COMPANY,
                payload: newInfo
            })
            console.log("res.data.mensaje", res.data.mensaje);
            dispatch({
                type: COLOCAR_MENSAJE_CONDICION,
                payload: {message: res.data.mensaje, severity: "success"}
            })
        }
    } catch (error) {
        dispatch({
            type: COLOCAR_MENSAJE_CONDICION,
            payload: {message: error.response, severity: "error"}
        })  
    }
    dispatch({
        type: LOADING_CONDICION,
        payload: false
    })
}