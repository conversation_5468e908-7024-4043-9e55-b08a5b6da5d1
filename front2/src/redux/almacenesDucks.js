import { instanceAxios } from "./axiosInstance";

//constantes
const dataInicial = {
  loading: false,
  mensaje: null,
  severity: "info",
  esperaTortuga: false,
  almacenes: [],
  proveedor: null,
  RFC: null,
  mensajeStock: null,
  severityStock: "info",
  stock: null,
  nombreAlmacen: null,
  idAlmacen: null,
  origen: null,
  selectedLocation: null,
  selectedStock: null,
  storeLocationsList: null,
  internalLocations: null,
  locationMessage: null,
  locationSeverity: null,
  zones: null,
};
//types

const CAMBIAR_MENSAJE_EXITO = "CAMBIAR_MENSAJE_EXITO";
const LIMPIAR_MENSAJE_EXITO = "LIMPIAR_MENSAJE_EXITO";
const CAMBIAR_LOADING = "CAMBIAR_LOADING";
const TORTUGA = "TORTUGA";
const COLOCAR_ALMACENES = "COLOCAR_ALMACENES";
const COLOCAR_PROVEEDOR = "COLOCAR_PROVEEDOR";
const COLOCAR_STOCK = "COLOCAR_STOCK";
const COLOCAR_NOMBRE_ALMACEN = "COLOCAR_NOMBRE_ALMACEN";
const CAMBIAR_MENSAJE_EXITO_STOCK = "CAMBIAR_MENSAJE_EXITO_STOCK";
const COLOCAR_SELECTED_LOCATION = "COLOCAR_SELECTED_LOCATION";
const COLOCAR_SELECTED_STOCK = "COLOCAR_SELECTED_STOCK";
const COLOCAR_STORE_LOCATIONS_LIST = "COLOCAR_STORE_LOCATIONS_LIST";
const COLOCAR_INTERNAL_LOCATIONS = "COLOCAR_INTERNAL_LOCATIONS";
const COLOCAR_LOCATION_MESSAGE = "COLOCAR_LOCATION_MESSAGE";
const COLOCAR_ZONAS = "COLOCAR_ZONAS";

//reducer
export default function almacenesReducer(state = dataInicial, action) {
  switch (action.type) {
    case CAMBIAR_MENSAJE_EXITO:
      return {
        ...state,
        mensaje: action.payload.mensaje,
        severity: action.payload.severity,
      };
    case LIMPIAR_MENSAJE_EXITO:
      return {
        ...state,
        mensaje: null,
        severity: "info",
      };
    case CAMBIAR_LOADING:
      return {
        ...state,
        loading: action.payload.loading,
      };
    case TORTUGA:
      return {
        ...state,
        almacenes: action.payload,
        esperaTortuga: false,
      };
    case COLOCAR_ALMACENES:
      return {
        ...state,
        almacenes: action.payload.almacenes,
        origen: action.payload.origen,
      };
    case COLOCAR_PROVEEDOR:
      return {
        ...state,
        proveedor: action.payload.proveedor,
        RFC: action.payload.RFC,
      };
    case COLOCAR_STOCK:
      return {
        ...state,
        stock: action.payload.stock,
        origen: action.payload.origen,
      };
    case COLOCAR_NOMBRE_ALMACEN:
      return {
        ...state,
        nombreAlmacen: action.payload.nombreAlmacen,
        idAlmacen: action.payload.idAlmacen,
      };
    case CAMBIAR_MENSAJE_EXITO_STOCK:
      return {
        ...state,
        mensajeStock: action.payload.mensajeStock,
        severityStock: action.payload.severityStock,
      };
    case COLOCAR_SELECTED_LOCATION:
      return {
        ...state,
        selectedLocation: action.payload.selectedLocation,
      };
    case COLOCAR_SELECTED_STOCK:
      return {
        ...state,
        selectedStock: action.payload.selectedStock,
      };
    case COLOCAR_STORE_LOCATIONS_LIST:
      return {
        ...state,
        storeLocationsList: action.payload.storeLocationsList,
      };
    case COLOCAR_INTERNAL_LOCATIONS:
      return {
        ...state,
        internalLocations: action.payload.internalLocations,
      };
    case COLOCAR_LOCATION_MESSAGE:
      return {
        ...state,
        locationMessage: action.payload.locationMessage,
        locationSeverity: action.payload.locationSeverity,
      };
    case COLOCAR_ZONAS:
      return {
        ...state,
        zones: action.payload.zones,
      };
    default:
      return state;
  }
}

//acciones

export const limpiarMensajeAlmacen = () => async (dispatch) => {
  dispatch({
    type: LIMPIAR_MENSAJE_EXITO,
  });
};

export const cambiarSelectedLocation =
  (selectedLocation) => async (dispatch) => {
    dispatch({
      type: COLOCAR_SELECTED_STOCK,
      payload: {
        selectedStock: null,
      },
    });
    dispatch({
      type: COLOCAR_SELECTED_LOCATION,
      payload: {
        selectedLocation: selectedLocation,
      },
    });
  };

export const cambiarSelectedStock = (selectedStock) => async (dispatch) => {
  dispatch({
    type: COLOCAR_SELECTED_LOCATION,
    payload: {
      selectedLocation: null,
    },
  });
  dispatch({
    type: COLOCAR_SELECTED_STOCK,
    payload: {
      selectedStock: selectedStock,
    },
  });
};

export const obtenerAlmacenesDesdeProveedores =
  (proveedor) => async (dispatch) => {
    try {
      let url = "/api/suppliers/supplier/getSupplierStores/" + proveedor;
      const res = await instanceAxios.get(url);
      if (res.status === 200) {
        dispatch({
          type: COLOCAR_ALMACENES,
          payload: {
            almacenes: res.data.supplierStores,
          },
        });
      } else {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITO,
          payload: {
            mensaje: res.statusText,
            severity: "warning",
          },
        });
      }
    } catch (error) {
      console.log(error);
      var erroresParaMostrar;
      if (error.response.data.errores) {
        erroresParaMostrar = error.response.data.errores;
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: erroresParaMostrar,
          severity: "error",
        },
      });
    }
  };

export const colocarProveedor = (proveedor, RFC) => async (dispatch) => {
  dispatch({
    type: COLOCAR_PROVEEDOR,
    payload: {
      proveedor: proveedor,
      RFC: RFC,
    },
  });
};

export const colocarAlmacen = (id, almacen) => async (dispatch) => {
  dispatch({
    type: COLOCAR_NOMBRE_ALMACEN,
    payload: {
      nombreAlmacen: almacen,
      idAlmacen: id,
    },
  });
};
export const obtenerStock = (almacen) => async (dispatch) => {
  try {
    let url =
      "/api/supplierStores/supplierStore/getSupplierStoreProducts/" + almacen;
    const res = await instanceAxios.get(url);
    if (res.status === 200) {
      dispatch({
        type: COLOCAR_STOCK,
        payload: {
          stock: res.data,
          origen: "proveedor",
        },
      });
      return res.data;
    } else {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_STOCK,
        payload: {
          mensajeStock: res.statusText,
          severityStock: "warning",
        },
      });
    }
  } catch (error) {
    console.log(error);
    console.log(error.response.data);
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO_STOCK,
      payload: {
        mensajeStock: erroresParaMostrar,
        severityStock: "error",
      },
    });
  }
};

export const crearAlmacenInterno = (data, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  dispatch({
    type: LIMPIAR_MENSAJE_EXITO,
  });
  try {
    const url = "/api/stores/store";
    const res = await instanceAxios.post(url, data, {
      headers: {
        "x-csrf-token": "b4ddd623-6214-4829-af6e-13ce53ba3051",
      },
    });
    if (res.status === 201) {
      

      const almacenes = getState().almacenes.almacenes;
      const newStore = transformAlmacen(data);
      almacenes.push(newStore);
      dispatch({
        type: COLOCAR_ALMACENES,
        payload: {
          almacenes: almacenes,
        },
      });

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: res.data,
          severity: "success",
        },
      });
      dispatch({
        type: CAMBIAR_LOADING,
        payload: {
          loading: false,
        },
      });
    }
  } catch (error) {
    var erroresParaMostrar;
    if (error.response?.data.errores || error.message ) {
      erroresParaMostrar =  error.response.data.errores || error.message ;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  }
};

export const deleteAlmacenInterno = (id, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  dispatch({
    type: LIMPIAR_MENSAJE_EXITO,
  });
  try {
    const url = "/api/stores/store/" + id;
    const res = await instanceAxios.delete(url, {
      headers: {
        "x-csrf-token": csrf_token,
      },
    });
    if (res.status === 200) {
      const almacenes = getState().almacenes.almacenes;
      const newStores = almacenes.filter((store) => store.id !== id);
      dispatch({
        type: COLOCAR_ALMACENES,
        payload: {
          almacenes: newStores,
        },
      });
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: res.data,
          severity: "success",
        },
      });
      dispatch({
        type: CAMBIAR_LOADING,
        payload: {
          loading: false,
        },
      });
    }
  } catch (error) {
    console.log(error);
    var erroresParaMostrar;
    if (error.response?.data.msg) {
      erroresParaMostrar = error.response.data.msg;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  }
};
const transformAlmacen = (data , deliveryTime) => {
  return {
    id: data.id,
    storeName: data.name,
    address: data.address,
    phone: data.phone,
    storeDescription: data.description,
    urlMaps: data.urlMaps,
    zone: {
      deliveryTime: deliveryTime,
      zoneNumber: data.zoneNumber
    }
  };
};

export const modificarAlmacenInterno =
  (id, data, csrf_token) => async (dispatch, getState) => {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {
      const url = "/api/stores/store/" + id;
      const res = await instanceAxios.put(url, data, {
        headers: {
          "x-csrf-token": csrf_token,
        },
      });
      if (res.status === 200) {
        console.log(res.data);

        const state = getState();

        const almacenes = state.almacenes.almacenes;
        const deliveryTime = almacenes.filter(almacen => almacen.id === id)[0].zone.deliveryTime;

        const almacenUpdated = transformAlmacen({...data, id}, deliveryTime) ;

        const newStores =
        almacenes.map((store) =>
          store.id === id ? almacenUpdated : store
        );

        dispatch({
          type: COLOCAR_ALMACENES,
          payload: {
            almacenes: newStores,
          },
        });
        dispatch({
          type: CAMBIAR_MENSAJE_EXITO,
          payload: {
            mensaje: res.data,
            severity: "success",
          },
        });
        dispatch({
          type: CAMBIAR_LOADING,
          payload: {
            loading: false,
          },
        });
      }
    } catch (error) {
      console.log(error.response.data.errores);
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: error.response.data.errores,
          severity: "error",
        },
      });
      dispatch({
        type: CAMBIAR_LOADING,
        payload: {
          loading: false,
        },
      });
    }
  };

export const obtenerAlmacenesInternosStock =
  (csrf_token) => async (dispatch) => {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {
      const url = "/api/stores/getStores?scope=stockDistribution";
      const res = await instanceAxios.get(url, {
        headers: {
          "x-csrf-token": csrf_token,
        },
      });
      if (res.status === 200) {
        console.log(res.data);
        dispatch({
          type: COLOCAR_ALMACENES,
          payload: {
            almacenes: res.data,
          },
        });
      } else {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITO,
          payload: {
            mensaje: res.statusText,
            severity: "warning",
          },
        });
      }
    } catch (error) {
      console.log(error);
      console.log(error.response.data);
      var erroresParaMostrar;
      if (error.response.data.errores) {
        erroresParaMostrar = error.response.data.errores;
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: erroresParaMostrar,
          severity: "error",
        },
      });
    }
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  };

export const obtenerAlmacenesInternos = (scope) => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    let url = "";
    scope
      ? (url = "/api/stores/getStores?scope=" + scope)
      : (url = "/api/stores/loadStoresPage");
    const res = await instanceAxios.get(url);
    if (res.status === 200) {
      dispatch({
        type: COLOCAR_ALMACENES,
        payload: {
          //almacenes:res.data.almacenes,
          almacenes: res.data.stores,
        },
      });
      dispatch({
        type: COLOCAR_ZONAS,
        payload: {
          zones: res.data.zones,
        },
      });
    } else {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: res.statusText,
          severity: "warning",
        },
      });
    }
  } catch (error) {
    console.log(error);
    console.log(error.response.data);
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};

export const obtenerStockInterno = (almacen) => async (dispatch) => {
  try {
    let url = "/api/stores/store/getStoreProducts/" + almacen;
    const res = await instanceAxios.get(url);
    if (res.status === 200) {
      dispatch({
        type: COLOCAR_STOCK,
        payload: {
          stock: res.data,
          origen: "interno",
        },
      });
      return res.data;
    } else {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_STOCK,
        payload: {
          mensajeStock: res.statusText,
          severityStock: "warning",
        },
      });
    }
  } catch (error) {
    console.log(error);
    console.log(error.response.data);
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO_STOCK,
      payload: {
        mensajeStock: erroresParaMostrar,
        severityStock: "error",
      },
    });
  }
};

export const borrarUbicacion = (idUbicacion) => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    const url = "/api/stores/store/internalLocation/" + idUbicacion;
    const res = await instanceAxios.delete(url);
    if (res.status === 200) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: res.statusText,
          severity: "success",
        },
      });
      dispatch({
        type: CAMBIAR_LOADING,
        payload: {
          loading: false,
        },
      });
    }
  } catch (error) {
    console.log(error);
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  }
};

export const modificarUbicacion =
  (idUbicacion, data, csrf_token) => async (dispatch) => {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {
      const url = "/api/stores/store/internalLocation/" + idUbicacion;
      const res = await instanceAxios.put(url, data, {
        headers: {
          "x-csrf-token": csrf_token,
        },
      });
      if (res.status === 200) {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITO,
          payload: {
            mensaje: res.statusText,
            severity: "success",
          },
        });
        dispatch({
          type: CAMBIAR_LOADING,
          payload: {
            loading: false,
          },
        });
      }
    } catch (error) {
      console.log(error);
      var erroresParaMostrar;
      if (error.response?.data.errores) {
        erroresParaMostrar = error.response.data.errores;
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: erroresParaMostrar,
          severity: "error",
        },
      });
      dispatch({
        type: CAMBIAR_LOADING,
        payload: {
          loading: false,
        },
      });
    }
  };

export const crearUbicacionInterna =
  (ubicacion, csrf_token) => async (dispatch, getState) => {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {
      const url =
        "/api/stores/store/internalLocation?infoToReturn=listInternalLocations";
      const res = await instanceAxios.post(url, ubicacion, {
        headers: {
          "x-csrf-token": "b4ddd623-6214-4829-af6e-13ce53ba3051",
        },
      });
      if (res.status === 200) {
        const almacenUpdated = res.data.store;
        const state = getState();
        const almacenes = state.almacenes.almacenes;

        // Reemplazar el objeto en stores si el id coincide con ubicacion.storeId
        const newStores =
        almacenes.map((store) =>
          store.id === ubicacion.storeId ? almacenUpdated : store
        );

        dispatch({
          type: COLOCAR_LOCATION_MESSAGE,
          payload: {
            locationMessage: res.data.mensaje,
            locationSeverity: "success",
          },
        });
        dispatch({
          type: COLOCAR_ALMACENES,
          payload: {
            almacenes: newStores,
          },
        });
        dispatch({
          type: CAMBIAR_LOADING,
          payload: {
            loading: false,
          },
        });
      }
    } catch (error) {
      var erroresParaMostrar;
      if (error.response?.data.msg) {
        erroresParaMostrar = error.response.data.msg;
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: COLOCAR_LOCATION_MESSAGE,
        payload: {
          locationMessage: erroresParaMostrar,
          locationSeverity: "error",
        },
      });
      dispatch({
        type: CAMBIAR_LOADING,
        payload: {
          loading: false,
        },
      });
    }
  };

export const limpiarMensajeUbicacionInterna = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_LOCATION_MESSAGE,
    payload: {
      locationMessage: null,
      locationSeverity: null,
    },
  });
};

export const limpiarMensaje = () => async (dispatch) => {
  dispatch({
    type: CAMBIAR_MENSAJE_EXITO,
    payload: {
      mensaje: null,
      severity: "info",
    },
  });
};

export const listaDeUbicacionesEnAlmacen = (array) => async (dispatch) => {
  dispatch({
    type: COLOCAR_STORE_LOCATIONS_LIST,
    payload: {
      storeLocationsList: array,
    },
  });
};

export const moverStock = (data, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    const url = "/api/stores/store/changeInternalLocation";
    const internalSku = data.internalSku;
    const updatedStock = getState().almacenes.selectedStock.map((product) => {
      if (product.internalSku === internalSku) {
        return {
          ...product,
          stock: product.stock - data.stockToMove,
        };
      }
      return product;
    });
    const res = await instanceAxios.put(url, data, {
      headers: {
        "x-csrf-token": csrf_token,
      },
    });
    if (res.status === 200) {
      console.log(res.data);
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_STOCK,
        payload: {
          mensajeStock: res.data.mensaje,
          severityStock: "success",
        },
      });
      dispatch({
        type: COLOCAR_SELECTED_STOCK,
        payload: {
          selectedStock: updatedStock,
        },
      });
      dispatch({
        type: CAMBIAR_LOADING,
        payload: {
          loading: false,
        },
      });
    }
  } catch (error) {
    console.log(error);
    let erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO_STOCK,
      payload: {
        mensajeStock: erroresParaMostrar,
        severityStock: "error",
      },
    });
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  }
};

export const limpiarMensajeStock = () => async (dispatch) => {
  dispatch({
    type: CAMBIAR_MENSAJE_EXITO_STOCK,
    payload: {
      mensajeStock: null,
      severityStock: "info",
    },
  });
};

export const limpiarSelectedStock = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_SELECTED_STOCK,
    payload: {
      selectedStock: null,
    },
  });
}

export const obtenerUbicacionesInternas = () => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    const url = "/api/stores/loadPageInternalLocations";
    const res = await instanceAxios.get(url);
    if (res.status === 200) {
      dispatch({
        type: COLOCAR_INTERNAL_LOCATIONS,
        payload: {
          internalLocations: res.data,
        },
      });
      dispatch({
        type: COLOCAR_ALMACENES,
        payload: {
          almacenes: res.data.almacenes,
        },
      });
    }
  } catch (error) {
    console.log(error);
    console.log(error.response.data);
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};
