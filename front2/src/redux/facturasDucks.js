import { instanceAxios } from "./axiosInstance";

//url's
//--url base
//constantes
const dataInicial = {
  loading: false,
  mensajeFacturas: null,
  colorDragDrop: "",
  mensajeDentroDeDragDrop:
    "Sin archivos seleccionados, puedes arrastrarlo aquí ó...",
  entryDocumentIndividual: null,
  nextC: 30,
  cantidadDePaginas: null,
  esperaTortuga: false,
  facturas: null,
  facturaRecargando: null,
  enlaceRecargando: null,
  productVariations: null,
  loadingVariations: false,
  facturaSeleccionada: null,
  selectedProduct: null,
  multipleEnters: false,
  amountGreaterThanZero: false,
  jsonToSend: {},
  ingresoRecargando: false,
  historialMovimientos: null,
  loadingHistorialMovimientos: false,
};
//types
//DragAndDrop
const CAMBIAR_MENSAJE_EXITI = "CAMBIAR_MENSAJE_EXITI";
const CAMBIAR_LOADING = "CAMBIAR_LOADING";
const CAMBIAR_COLOR_DRAG_DROP = "CAMBIAR_COLOR_DRAG_DROP";
//Obtener facturas
const COLOCAR_CANTIDADPAGINAS_NULL = "COLOCAR_CANTIDADPAGINAS_NULL";
const OBTENER_NUMEROFACTURAS_EXITO = "OBTENER_NUMEROFACTURAS_EXITO";
const OBTENER_FACTURAS_FILTRADAS_EXITO = "OBTENER_FACTURAS_FILTRADAS_EXITO";
const COLOCAR_NULL_FACTURAS = "COLOCAR_NULL_FACTURAS";
const OBTENER_FACTURA_INDIVIDUAL = "OBTENER_FACTURA_INDIVIDUAL"
//Enlace de facturas
const ENLAZAR_FACTURAS_RECARGANDO = "ENLAZAR_FACTURAS_RECARGANDO";
const COLOCAR_VARIACIONES_PRODUCTOS = "COLOCAR_VARIACIONES_PRODUCTOS";
const COLOCAR_LOADING_VARIATIONS = "COLOCAR_LOADING_VARIATIONS";
const COLOCAR_FACTURA_SELECCIONADA = "COLOCAR_FACTURA_SELECCIONADA";
const COLOCAR_SELECTED_PRODUCT = "COLOCAR_SELECTED_PRODUCT";
const COLOCAR_MULTIPLE_ENTERS = "COLOCAR_MULTIPLE_ENTERS";
const COLOCAR_AMOUNT_GREATER_THAN_ZERO = "COLOCAR_AMOUNT_GREATER_THAN_ZERO";
const COLOCAR_JSON_TO_SEND = "COLOCAR_JSON_TO_SEND";
const COLOCAR_INGRESO_RECARGANDO = "COLOCAR_INGRESO_RECARGANDO";
//Historial de movimientos
const OBTENER_HISTORIAL_MOVIMIENTOS = "OBTENER_HISTORIAL_MOVIMIENTOS";
const COLOCAR_LOADING_HISTORIAL_MOVIMIENTOS = "COLOCAR_LOADING_HISTORIAL_MOVIMIENTOS";
//PUT (modificación)
const COLOCAR_FACTURA_RECARGANDO = "COLOCAR_PEDIDO_RECARGANDO";
const CAMBIAR_COMMENTS_TABLA = "CAMBIAR_COMMENTS_TABLA";
const ELIMINAR_FACTURA = "ELIMINAR_FACTURA";
//reducer
export default function facturasReducer(state = dataInicial, action) {
  switch (action.type) {
    case CAMBIAR_MENSAJE_EXITI:
      return {
        ...state,
        mensajeFacturas: action.payload.mensaje,
        colorDragDrop: action.payload.colorDragDrop,
      };
    case CAMBIAR_LOADING:
      return {
        ...state,
        loading: action.payload.loading,
      };
    case CAMBIAR_COLOR_DRAG_DROP:
      return {
        ...state,
        colorDragDrop: action.payload.colorDragDrop,
        mensajeDentroDeDragDrop: action.payload.mensajeDentroDeDragDrop,
      };
    case COLOCAR_CANTIDADPAGINAS_NULL:
      return {
        ...state,
        cantidadDePaginas: action.payload,
      };
    case OBTENER_NUMEROFACTURAS_EXITO:
      return {
        ...state,
        recargandoInformacion: 0,
        cantidadDePaginas: action.payload.cantidadDePaginas,
      };
    case OBTENER_FACTURAS_FILTRADAS_EXITO:
      return {
        ...state,
        facturas: action.payload,
        esperaTortuga: false,
      };
    case COLOCAR_NULL_FACTURAS:
      return {
        ...state,
        facturas: action.payload,
      };
    case OBTENER_FACTURA_INDIVIDUAL://////////////
      return {
        ...state,
        entryDocumentIndividual: action.payload,
      };
    case COLOCAR_FACTURA_RECARGANDO:
      return {
        ...state,
        facturaRecargando: action.payload,
      };
    case CAMBIAR_COMMENTS_TABLA:
      return {
        ...state,
        facturas: action.payload,
      };
    case ENLAZAR_FACTURAS_RECARGANDO:
      return {
        ...state,
        enlaceRecargando: action.payload,
      };
    case COLOCAR_VARIACIONES_PRODUCTOS:
      return {
        ...state,
        productVariations: action.payload,
      };
      case COLOCAR_LOADING_VARIATIONS:
      return {
        ...state,
        loadingVariations: action.payload,
      };
    case COLOCAR_FACTURA_SELECCIONADA:
      return {
        ...state,
        facturaSeleccionada: action.payload,
      };
    case COLOCAR_SELECTED_PRODUCT:
      return {
        ...state,
        selectedProduct: action.payload,
      };
    case COLOCAR_MULTIPLE_ENTERS:
      return {
        ...state,
        multipleEnters: action.payload,
      };
    case COLOCAR_AMOUNT_GREATER_THAN_ZERO:
      return {
        ...state,
        amountGreaterThanZero: action.payload,
      };
    case COLOCAR_JSON_TO_SEND:
      return {
        ...state,
        jsonToSend: action.payload,
      };
    case COLOCAR_INGRESO_RECARGANDO:
      return {
        ...state,
        ingresoRecargando: action.payload,
      };
    case OBTENER_HISTORIAL_MOVIMIENTOS:
      return {
        ...state,
        historialMovimientos: action.payload,
      };
    case COLOCAR_LOADING_HISTORIAL_MOVIMIENTOS:
      return {
        ...state,
        loadingHistorialMovimientos: action.payload,
      };
    case ELIMINAR_FACTURA:
      return {
        ...state,
        facturas: state.facturas.filter((item) => item.id !== action.payload),
      };
    default:
      return state;
  }
}
//acciones

export const setSelectedProduct =
  (selectedProduct, csrf_token) => async (dispatch, getState) => {
    dispatch({
      type: COLOCAR_SELECTED_PRODUCT,
      payload: selectedProduct,
    });
  };

export const setVariationMultipleEnters =
  (variationMultipleEnters) => async (dispatch, getState) => {
    dispatch({
      type: COLOCAR_MULTIPLE_ENTERS,
      payload: variationMultipleEnters,
    });
  };

export const setAmountGreaterThanZero =
  (amountGreaterThanZero) => async (dispatch, getState) => {
    dispatch({
      type: COLOCAR_AMOUNT_GREATER_THAN_ZERO,
      payload: amountGreaterThanZero,
    });
  };

export const setJsonToSend = (jsonToSend) => async (dispatch, getState) => {
  dispatch({
    type: COLOCAR_JSON_TO_SEND,
    payload: jsonToSend,
  });
};

export const ingresarProductos =
  (data, csrf_token, units, setCurrentlyEntering) => async (dispatch, getState) => {
    dispatch({
      type: COLOCAR_INGRESO_RECARGANDO,
      payload: true,
    });
    try {
      let url = "/api/invoices/product/enterInventory";
      const res = await instanceAxios.post(url, data, {
        headers: {
          "x-csrf-token": "b4ddd623-6214-4829-af6e-13ce53ba3051",
        },
      });
      const totalFaltantes = units
      const totalIngresados = data.productsEnteredIntoStore
        .map((item) =>
          item.enters.map((item2) => item2.amount).reduce((a, b) => a + b, 0)
        )
        .reduce((a, b) => a + b, 0) 

      const id = getState().facturas.facturaSeleccionada;
      const facturas = getState().facturas.facturas;
      const facturasNuevo = facturas.map((item) =>
        item.id == id
          ? {
            ...item,
            productsInEntryDocument: item.productsInEntryDocument.map((item2) =>
              item2.id == data.productFromEntryDocumentId
                ? { ...item2 , totalEntered: units, totalMissing: totalFaltantes }
                : item2
            ),
          }
          : item
      );
      setCurrentlyEntering(0);
      const selectedProd = facturasNuevo
        .filter(
          (item) => item.id === getState().facturas.facturaSeleccionada
        )[0]
        .productsInEntryDocument.filter(
          (item) => item.id === data.productFromEntryDocumentId
        )[0];

      if (res.status == 200) {
        dispatch({
          type: COLOCAR_INGRESO_RECARGANDO,
          payload: false,
        });
        dispatch({
          type: CAMBIAR_MENSAJE_EXITI,
          payload: {
            mensaje: {
              mensaje: "Ingreso exitoso",
              severity: "success"
            },
          },
        });
        dispatch({
          type: OBTENER_FACTURAS_FILTRADAS_EXITO,
          payload: facturasNuevo,
        });
        dispatch({
          type: COLOCAR_SELECTED_PRODUCT,
          payload: selectedProd,
        });
      }
    } catch (error) {
      console.log(error);
      dispatch({
        type: COLOCAR_INGRESO_RECARGANDO,
        payload: false,
      });

      setCurrentlyEntering(0);
      
      var erroresParaMostrar;
      if (error.response.data.errores) {
        erroresParaMostrar = error.response.data.errores;
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITI,
        payload: {
          mensaje: {
            mensaje: erroresParaMostrar,
            severity: "error"
          },
        },
      });
    }
  };

export const setMensajeAlert = (mensaje, severity) => (dispatch) => {
  dispatch({
    type: CAMBIAR_MENSAJE_EXITI,
    payload: {
      mensaje: {
        mensaje: mensaje,
        severity: severity
      }
    },
  });
};


export const setMensaje = (mensaje) => (dispatch, getState) => {
  const colorDragDrop = getState().facturas.colorDragDrop;
  dispatch({
    type: CAMBIAR_MENSAJE_EXITI,
    payload: {
      mensaje: mensaje,
      colorDragDrop: colorDragDrop,
      //////
    },
  });
};

export const deleteFactura =
  (id, csrf_token) => async (dispatch, getState) => {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {
      let url = `/api/invoices/invoice/delete/${id}`;
      const res = await instanceAxios.delete(url, {
        headers: {
          "x-csrf-token": csrf_token,
        },
      });
      if (res.status == 200) {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITI,
          payload: {
            mensaje: {
              mensaje: res.data.mensaje,
              severity: "success"
            },
            colorDragDrop: "file-success",
          },
        });
        dispatch({
          type: ELIMINAR_FACTURA,
          payload: id,
        });
      } else {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITI,
          payload: {
            mensaje: {
              mensaje: "Error desconocido",
              severity: "warning"
            },
            colorDragDrop: "file-error",
          },
        });
      }
    } catch (error) {
      console.log(error);
      console.log(error.response.data);
      var erroresParaMostrar;
      if (error.response.data.errores) {
        erroresParaMostrar = error.response.data.errores;
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITI,
        payload: {
          mensaje: {
            mensaje: erroresParaMostrar,
            severity: "error"
          },
          colorDragDrop: "file-error",
        },
      });
    }
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  };

export const setColorDragDrop =
  (colorDragDrop, mensajeDentroDeDragDrop) => (dispatch) => {
    dispatch({
      type: CAMBIAR_COLOR_DRAG_DROP,
      payload: {
        colorDragDrop: colorDragDrop,
        mensajeDentroDeDragDrop: mensajeDentroDeDragDrop,
      },
    });
  };

export const enviarFacturas =
  (formData, csrf_token, clickInAlert) => async (dispatch, getState) => {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {
      let url = "/api/invoices/upfile";
      const res = await instanceAxios.post(url, formData, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "multipart/form-data",
        },
      });
      if (res.status == 200) {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITI,
          payload: {
            mensaje: {
              mensaje: "Facturas subidas con éxito",
              severity: "success",
              extraFunction: clickInAlert
            },
            colorDragDrop: "file-success",
          },
        });
      } else {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITI,
          payload: {
            mensaje: {
              mensaje: "Error desconocido",
              severity: "warning",
              extraFunction: clickInAlert
            },
            colorDragDrop: "file-error",
          },
        });
      }
    } catch (error) {
      console.log(error);
      console.log(error.response.data);
      var erroresParaMostrar;
      if (error.response.data.errores) {
        const keys = []
        //invoice.
        erroresParaMostrar = error.response.data.errores;
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITI,
        payload: {
          mensaje: {
            mensaje: erroresParaMostrar,
            severity: "error",
            extraFunction: clickInAlert
          },
          colorDragDrop: "file-error",
        },
      });
    }
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  };

export const facturaSeleccionada = (facturaId) => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_FACTURA_SELECCIONADA,
      payload: facturaId,
    });
  } catch (error) {
    console.log(error);
  }
};

export const limpiarFacturaSeleccionada = () => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_FACTURA_SELECCIONADA,
      payload: null,
    });
  } catch (error) {
    console.log(error);
  }
};

export const darAltaFactura =
  (data, csrf_token, eng_spa) => async (dispatch, getState) => {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {

      let url = "/api/invoices/invoice/upinvoice";
      const res = await instanceAxios.post(url, data, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });
      if (res.status == 200 || res.status == 201) {
        console.log(res);
        dispatch({
          type: CAMBIAR_MENSAJE_EXITI,
          payload: {
            mensaje: {
              mensaje: res.data.mensaje,
              severity: "success"
            },
          },
        });
        dispatch({
          type: COLOCAR_FACTURA_RECARGANDO,
          payload: res.data.factura,
        });
      } else {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITI,
          payload: {
            mensaje: {
              mensaje: "Error desconocido",
              severity: "warning"
            },
          },
        });
      }
    } catch (error) {
      console.log(error);
      console.log(error.response.data);
      var erroresParaMostrar;
      if (error.response.data.errores) {
        erroresParaMostrar = eng_spa(error.response.data.errores)
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITI,
        payload: {
          mensaje: {
            mensaje: erroresParaMostrar,
            severity: "error"
          },
        },
      });
    }
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  };


  export const actualizarFactura =
  (idEntryDocument, data, csrf_token, eng_spa) => async (dispatch, getState) => {
    console.log('eng_spa')
    console.log(eng_spa)
    console.log('eng_spa')
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {
      const url = `/api/invoices/factura/${idEntryDocument}`;
      const res = await instanceAxios.put(url, data, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });
      if (res.status == 200 || res.status == 201) {
        console.log(res);
        dispatch({
          type: CAMBIAR_MENSAJE_EXITI,
          payload: {
            mensaje: {
              mensaje: res.data.mensaje,
              severity: "success"
            },
          },
        });
        dispatch({
          type: COLOCAR_FACTURA_RECARGANDO,
          payload: res.data.factura,
        });
      } else {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITI,
          payload: {
            mensaje: {
              mensaje: "Error desconocido",
              severity: "warning"
            },
          },
        });
      }
    } catch (error) {
      console.log(error);
      console.log(error.response.data);
      var erroresParaMostrar;
      if (error.response.data.errores) {
        erroresParaMostrar = eng_spa(error.response.data.errores)
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITI,
        payload: {
          mensaje: {
            mensaje: erroresParaMostrar,
            severity: "error"
          },
        },
      });
    }
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  };

export const obtenerTotalFacturas =
  ({ proveedor = "", finalDate = "", initialDate = "", search = "" } = {}) =>
    async (dispatch, getState) => {
      const cantidadDeFacturasPorPagina = getState().facturas.nextC;
      try {
        dispatch({
          type: COLOCAR_CANTIDADPAGINAS_NULL,
          payload: null,
        });
        let url = "/api/invoices/numfacturasfiltro";
        const res = await instanceAxios.get(url, {
          params: {
            proveedores: proveedor,
            fechaInicial: initialDate,
            fechaFinal: finalDate,
            search: search,
          },
        });
        const numFacturas = res.data.numeroFacturas;
        const cantidadDePaginas = Math.ceil(
          numFacturas / cantidadDeFacturasPorPagina
        );
        dispatch({
          type: OBTENER_NUMEROFACTURAS_EXITO,
          payload: {
            cantidadDePaginas: cantidadDePaginas,
          },
        });
      } catch (error) {
        console.log(error);
        const status = error.response.status;
        dispatch({
          type: OBTENER_NUMEROFACTURAS_EXITO,
          payload: {
            cantidadDePaginas: "status " + status,
          },
        });
      }
    };

export const limpiarVariaciones = () => async (dispatch, getState) => {
  dispatch({
    type: COLOCAR_VARIACIONES_PRODUCTOS,
    payload: null,
  });
};

export const enlazarProductosConFacturas =
  (id, sku, factura, csrf_token) => async (dispatch, getState) => {
    console.log('111111111111111111111111111111111111111111111111')
    console.log(id)
    console.log('----')
    console.log(sku)
    console.log('----')
    console.log(factura)
    console.log('111111111111111111111111111111111111111111111111')
    dispatch({
      type: ENLAZAR_FACTURAS_RECARGANDO,
      payload: true,
    });
    try {
      const data = {
        productFromEntryDocumentId: id,
        internalBaseSku: sku,
      };
      let url = "/api/invoices/product/relateProductReturnVariations";
      const res = await instanceAxios.put(url, data, {
        headers: {
          "x-csrf-token": "b4ddd623-6214-4829-af6e-13ce53ba3051",
        },
      });
      const facturas = getState().facturas.facturas;
      console.log('----------------------------------------')
      console.log(facturas)
      console.log('----------------------------------------')

      const facturasNuevo = facturas.map((item) =>
        item.id == factura
          ? {
            ...item,
            productsInEntryDocument: item.productsInEntryDocument.map((item2) =>
              item2.id == id
                ? { ...item2, internalBaseSku: sku, productBase: sku }
                : item2
            ),
          }
          : item
      );
      const newFactura = facturasNuevo.filter(
        (item) => item.id === getState().facturas.facturaSeleccionada
      );
      const newProduct = newFactura[0].productsInEntryDocument.find(
        (product) => product.id === id
      );
      if (res.status == 200) {
        dispatch({
          type: COLOCAR_VARIACIONES_PRODUCTOS,
          payload: res.data.enteredVariations,
        });
        dispatch({
          type: OBTENER_FACTURAS_FILTRADAS_EXITO,
          payload: facturasNuevo,
        });
        dispatch({
          type: COLOCAR_SELECTED_PRODUCT,
          payload: newProduct,
        });
      }
    } catch (error) {
      console.log(error);
      const status = error.response.status;
      dispatch({
        type: COLOCAR_VARIACIONES_PRODUCTOS,
        payload: "status " + status,
      });
    }
    dispatch({
      type: ENLAZAR_FACTURAS_RECARGANDO,
      payload: false
    });
  };

export const obtenerVariaciones = (id) => async (dispatch, getState) => {
  dispatch({
    type: ENLAZAR_FACTURAS_RECARGANDO,
    payload: true,
  });
  dispatch({
    type: COLOCAR_LOADING_VARIATIONS,
    payload: true,
  });
  try {
    let url = "/api/invoices/productBase/getPossibleProductToEnter/" + id;
    const res = await instanceAxios.get(url);

    console.log('res.data.ingresos.enteredVariations')
    console.log(res.data)
    console.log('res.data.ingresos.enteredVariations')

    if (res.status === 200) {
      dispatch({
        type: COLOCAR_VARIACIONES_PRODUCTOS,
        payload: res.data,
      });
    }
  } catch (error) {
    console.log(error);
    const status = error.response?.status ? error.response.status : error;
    dispatch({
      type: COLOCAR_VARIACIONES_PRODUCTOS,
      payload: "status " + status,
    });
  }
  dispatch({
    type: ENLAZAR_FACTURAS_RECARGANDO,
    payload: false,
  });
  dispatch({
    type: COLOCAR_LOADING_VARIATIONS,
    payload: false,
  });
};

export const obtenerFacturaUsandoId =
  (id) => async (dispatch) => {
    try {
      dispatch({
        type: OBTENER_FACTURA_INDIVIDUAL,
        payload: null,
      });
      let url = `/api/invoices/factura/${id}`;
      /*const res = await instanceAxios.get(url, {
        params: {
          scope: scope
        },
      });*/
      const res = await instanceAxios.get(url);
      dispatch({
        type: OBTENER_FACTURA_INDIVIDUAL,
        payload: {
            status: res.status,
            info: res.data.entryDocument
        },
      });
    } catch (error) {
      dispatch({
        type: OBTENER_FACTURA_INDIVIDUAL,
        payload: {
          status: error.response.status,
          info: error.response.data.errores
        },
      });
    }
  };

export const obtenerFacturasFiltradasAccion =
  ({
    offsetActual = 0,
    proveedor = "",
    initialDate = "",
    finalDate = "",
    search = "",
  } = {}) =>
    async (dispatch, getState) => {
      const cantidadDeFacturasPorPagina = getState().facturas.nextC;
      try {
        let url = "/api/invoices/facturasFiltro";
        dispatch({
          type: COLOCAR_NULL_FACTURAS,
          payload: null,
        });
        const res = await instanceAxios.get(url, {
          params: {
            offset: offsetActual,
            next: cantidadDeFacturasPorPagina,
            proveedores: proveedor,
            fechaInicial: initialDate,
            fechaFinal: finalDate,
            search: search,
          },
        });
        dispatch({
          type: OBTENER_FACTURAS_FILTRADAS_EXITO,
          payload: res.data.facturas,
        });
      } catch (error) {
        console.log(error);
        const status = error.response.status;
        dispatch({
          type: OBTENER_FACTURAS_FILTRADAS_EXITO,
          payload: "status " + status,
        });
      }
    };

export const obtenerHistorialMovimientos =
  (id) => async (dispatch, getState) => {
    // Limpiar historial anterior
    dispatch({
      type: OBTENER_HISTORIAL_MOVIMIENTOS,
      payload: null,
    });
    
    dispatch({
      type: COLOCAR_LOADING_HISTORIAL_MOVIMIENTOS,
      payload: true,
    });
    try {
      let url = `/api/entryDocument/productInEntryDocument/productsEnteredIntoStore/${id}/directInventoryMovement`;
      const res = await instanceAxios.get(url);
      dispatch({
        type: OBTENER_HISTORIAL_MOVIMIENTOS,
        payload: res.data,
      });
    } catch (error) {
      console.log(error);
      const status = error.response?.status ? error.response.status : error;
      dispatch({
        type: OBTENER_HISTORIAL_MOVIMIENTOS,
        payload: "status " + status,
      });
    }
    dispatch({
      type: COLOCAR_LOADING_HISTORIAL_MOVIMIENTOS,
      payload: false,
    });
  };

export const limpiarCantidadesTemporales = () => (dispatch) => {
  dispatch({
    type: COLOCAR_JSON_TO_SEND,
    payload: {},
  });
};
