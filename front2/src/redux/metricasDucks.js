import { instanceAxios } from "./axiosInstance";
//Data inicial

const dataInicial = {
    loading:false,
    mensaje:null,
    severity:'info',
    metric:null,
    metricOneWeek : [],
    metricsDynamic : null,
    metricOneMonth : [],
    metricHour : []
  };

  const CAMBIAR_MENSAJE = 'CAMBIAR_MENSAJE'
  const CAMBIAR_LOADING = 'CAMBIAR_LOADING'
  const METRICAS_ONE_WEEK = 'METRICAS_ONE_WEEK'
  const DYNAMIC_METRICS = 'DYNAMIC_METRICS'
  const DYNAMIC_METRICS_30 = 'DYNAMIC_METRICS_30'
  const DYNAMIC_METRICS_DAYS = 'DYNAMIC_METRICS_DAYS'


  export default function metricasReducer(state = dataInicial, action) {
    switch (action.type) {
        case CAMBIAR_MENSAJE:
          return {
            ...state,
            mensaje: action.payload.mensaje,
            severity: action.payload.severity,
            metric: action.payload.metric,
          }
        case CAMBIAR_LOADING:
          return {
            ...state,
            loading: action.payload.loading,
          };
        case 'METRICAS_ONE_WEEK':
          return {
            ...state,
            metricOneWeek: action.payload.metricOneWeek,
          };
        case 'DYNAMIC_METRICS':
          return {
            ...state,
            metricsDynamic: action.payload.metric,
          };
        case 'DYNAMIC_METRICS_30':
          return {
            ...state,
            metricOneMonth: action.payload.metricOneMonth,
          };
        case DYNAMIC_METRICS_DAYS:
          return {
            ...state,
            metricHour: action.payload.metricHour,
          };
        
      default:
        return state;
    }
  }


export const enviarPeriodo = (obj,csrf_token) => async (dispatch) => {
    dispatch({
        type:CAMBIAR_LOADING,
        payload:{
          loading:true
        }
      })
    try {
      let url = '/api/metrics/utility';
      const res = await instanceAxios.post(
        url,obj,{
          headers:{
            'x-csrf-token':csrf_token,
            "Content-Type": "application/json",          }
        }
      );
      if(res.status===200){
        dispatch({
          type: CAMBIAR_MENSAJE,        
          payload:{
            metric:res.data,
            mensaje:res.statusText,
            severity:"success",
          }
          
        });
      }else{
        dispatch({
          type: CAMBIAR_MENSAJE,
          payload:{
            metric:null,
            mensaje:res.status,
            severity:"warning",
          }
        });
      }
      
      
    } catch (error) {
        console.log(error);
        console.log(error.response.data)
        var erroresParaMostrar
        if(error.response.data.errores){
          erroresParaMostrar = error.response.data.errores
        }else{
          erroresParaMostrar = "Error desconocido"
        }
        dispatch({
          type: CAMBIAR_MENSAJE,
          payload:{
            metric:null,
            mensaje:erroresParaMostrar,
            severity:"error",
          }
        });
    }
    dispatch({
      type:CAMBIAR_LOADING,
      payload:{
        loading:false
      }
    })
  };
  

export const getMetrics = (csrf_token) => async (dispatch) => {
    dispatch({
        type:CAMBIAR_LOADING,
        payload:{
          loading:true
        }
      })
    try {
      let url = '/api/metrics/all';
      const res = await instanceAxios.get(
        url,{
          headers:{
            // 'x-csrf-token':csrf_token,
            "Content-Type": "application/json",          }
        }
      );
      if(res.status===200){

        dispatch({
          type: CAMBIAR_MENSAJE,        
          payload:{
            metric:res.data,
            mensaje:res.statusText,
            severity:"success",
          }
          
        });
      }else{
        console.log('else')
        console.log(res)
        console.log('elese')
        dispatch({
          type: CAMBIAR_MENSAJE,
          payload:{
            metric:null,
            mensaje:res.status,
            severity:"warning",
          }
        });
      }
      
      
    } catch (error) {
        console.log("error");
        console.log(error);
        console.log(error.response.data)
        var erroresParaMostrar
        if(error.response.data.errores){
          erroresParaMostrar = error.response.data.errores
        }else{
          erroresParaMostrar = "Error desconocido"
        }
        dispatch({
          type: CAMBIAR_MENSAJE,
          payload:{
            metric:null,
            mensaje:erroresParaMostrar,
            severity:"error",
          }
        });
    }
    dispatch({
      type:CAMBIAR_LOADING,
      payload:{
        loading:false
      }
    })
  
}

const getDatesArray = (start, end) => {
  const arr = [];
  const dt = new Date(start);
  while (dt <= end) {
    arr.push(new Date(dt));
    dt.setDate(dt.getDate() + 1);
  }
  return arr;
};

// Formatear una fecha a la cadena 'dd-mm-yyyy'
const formatDate = (date) => {
  let dd = date.getDate();
  let mm = date.getMonth() + 1; // Enero es 0
  const yyyy = date.getFullYear();
  if (dd < 10) dd = '0' + dd;
  if (mm < 10) mm = '0' + mm;
  return `${dd}-${mm}-${yyyy}`;
};

function getLastWeekInterval(days) {
  const currentDate = new Date();
  const startDate = new Date(currentDate);
  startDate.setDate(currentDate.getDate() - days); // Fecha inicial 7 días atrás
  const endDate = currentDate; // Fecha final es hoy


  const formatDate = (date) => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Los meses son 0-indexados
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  return {
    fechaInicial: formatDate(startDate),
    fechaFinal: formatDate(endDate)
  };
}

export const getMetricsDinamyc = ( days) => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true
    }
  });

  try {
  //   const fechaInicial = '20-05-2024';
  // const fechaFinal = '26-05-2024';
  const { fechaInicial, fechaFinal } = getLastWeekInterval(days);
    let url = ""
    let fechahoy = formatDate(new Date());
     url =  days == 0 ?   `/api/metrics/daybyhours?fecha=${fechahoy}` : `/api/metrics/interval?fechaInicial=${fechaInicial}&fechaFinal=${fechaFinal}`;

    const result = await instanceAxios.get(url, {
          headers: {
            // 'x-csrf-token': csrf_token,
            "Content-Type": "application/json",
          }
        });

        if (days === 0) {
          dispatch({
            type: DYNAMIC_METRICS_DAYS,
            payload: {
              metricHour: result.data,
            },
          });
        } else {
          dispatch({
            type: DYNAMIC_METRICS_30,
            payload: {
              metricOneMonth: result.data,
            },
          });
        }

  } catch (error) {
    console.log("error");
    console.log(error);
    let erroresParaMostrar = error.response?.data?.errores || "Error desconocido";
    dispatch({
      type: CAMBIAR_MENSAJE,
      payload: {
        metric: null,
        mensaje: erroresParaMostrar,
        severity: "error",
      }
    });
  }

  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false
    }
  });
}


export const getMetricsOneWeek = (csrf_token, days=6) => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true
    }
  });

  try {
  //   const fechaInicial = '20-05-2024';
  // const fechaFinal = '26-05-2024';
  const { fechaInicial, fechaFinal } = getLastWeekInterval(days);

  const url = `/api/metrics/interval?fechaInicial=${fechaInicial}&fechaFinal=${fechaFinal}`;

    const result = await instanceAxios.get(url, {
          headers: {
            'x-csrf-token': csrf_token,
            "Content-Type": "application/json",
          }
        });
    dispatch({
      type: METRICAS_ONE_WEEK,
      payload: {
        metricOneWeek: result.data,
      }
    });
  } catch (error) {
    console.log("error");
    console.log(error);
    let erroresParaMostrar = error.response?.data?.errores || "Error desconocido";
    dispatch({
      type: CAMBIAR_MENSAJE,
      payload: {
        metric: null,
        mensaje: erroresParaMostrar,
        severity: "error",
      }
    });
  }

  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false
    }
  });
}

