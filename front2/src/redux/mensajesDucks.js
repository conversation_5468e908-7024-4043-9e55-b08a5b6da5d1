import { instanceAxios } from "./axiosInstance";
//Data inicial

const dataInicial = {
  loading: false,
  mensaje: null,
  severity: "info",
  error: null,
  messages: [],
  orderMessages: [],
  messagesDefault: [],
};

const CAMBIAR_MENSAJE = "CAMBIAR_MENSAJE";
const CAMBIAR_LOADING = "CAMBIAR_LOADING";
const COLOCAR_MENSAJES_PREDETERMINADOS = "COLOCAR_MENSAJES_PREDETERMINADOS";
const COLOCAR_MENSAJES_ORDEN = "COLOCAR_MENSAJES_ORDEN";

export default function mensajesReducer(state = dataInicial, action) {
  switch (action.type) {
    case CAMBIAR_MENSAJE:
      return {
        ...state,
        mensaje: action.payload.mensaje,
        severity: action.payload.severity,
      };
    case CAMBIAR_LOADING:
      return {
        ...state,
        loading: action.payload.loading,
      };
    case COLOCAR_MENSAJES_PREDETERMINADOS:
      return {
        ...state,
        messagesDefault: action.payload.messages,
      };
    case COLOCAR_MENSAJES_ORDEN:
      return {
        ...state,
        orderMessages: action.payload.orderMessages,
      };
    default:
      return state;
  }
}

export const cambiarMensajePredefinido = (id, mensaje) => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
try {
  const url = "/api/messages/message/" + id;
  const res = await instanceAxios.put(url, { message: mensaje });
  if (res.status === 200) {
    dispatch({
      type: COLOCAR_MENSAJES_PREDETERMINADOS,
      payload: {
        messages: res.data,
      },
    });
  }
} catch (error) {
  var erroresParaMostrar;
  if (error.response.data.errores) {
    erroresParaMostrar = error.response.data.errores;
  } else {
    erroresParaMostrar = "Error desconocido";
  }
  dispatch({
    type: CAMBIAR_MENSAJE,
    payload: {
      mensaje: erroresParaMostrar,
      severity: "error",
    },
  });
}
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};

export const obtenerMensajesOrden = (id) => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    const url = "/api/orders/order/" + id + "/messages";
    const res = await instanceAxios.get(url);
    if (res.status === 200) {
      dispatch({
        type: COLOCAR_MENSAJES_ORDEN,
        payload: {
          orderMessages: res.data.messages,
        },
      });
    }
  } catch (error) {
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: COLOCAR_MENSAJES_ORDEN,
      payload: {
        orderMessages: null
      },
    }),
    dispatch({
      type: CAMBIAR_MENSAJE,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};

export const clearOrderMessages = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_MENSAJES_ORDEN,
    payload: {
      orderMessages: [],
    },
  });
};

export const enviarMensaje = (id, message, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    const url = "/api/order/messages/message"

    const body = {
      orderId: id,
      message: message
    }

    const res = await instanceAxios.post(url, body,
      {
        headers: {
          "x-csrf-token": csrf_token,
        },
      }
    );
    if (res.status === 201 || res.status === 200) {
      dispatch({
        type: CAMBIAR_MENSAJE,
        payload: {
          mensaje: res.data.mensaje,
          severity: "success",
        },
      });
      const newOrderMessages = getState().mensajes.orderMessages;
      newOrderMessages.push({
        fromClient: false,
        message: message,
        orderId: id,
        timeStamp: new Date(),
        id: getState().mensajes.orderMessages.length + 1,
      });
      dispatch({
        type: COLOCAR_MENSAJES_ORDEN,
        payload: {
          orderMessages: newOrderMessages,
        },
      });
    }
  } catch (error) {
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
}

export const markAsRead = (id, value, csrf_token) => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload:{
      loading: true,
    }
  })
  try {
    const url = '/api/order/' + id + '/messages/markAsRead'
    
    const body = {
      markAsRead: value
    }
    const res = await instanceAxios.patch(url, body,
      {
        headers: {
          "x-csrf-token": csrf_token,
        },
      }
    )
    if (res.status === 200 || res.status === 201) {
      dispatch({
        type: CAMBIAR_MENSAJE,
        payload: {
          mensaje: res.data.mensaje,
          severity: "success",
        },
      });
    }
  } catch (error) {
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};


/* default messages */

export const getDefaultMessages = ({ search, offset, nc = 20 } = {}) => async (dispatch, getState) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    const url = "/api/messages/defaultMessage";
    const res = await instanceAxios.get(url, {
      params: {
        search: search || "",
        offset: offset || 0,
        nc: nc,
      },
    });


    if (res.status === 200) {

      const oldMessages = getState().mensajes.messagesDefault;
      // quitar si hay un mensaje con el mismo id
      const newMessages = res.data.messages.filter(newMessage => {
        return !oldMessages.some(oldMessage => oldMessage.id === newMessage.id);
      });
      // unir los mensajes nuevos con los antiguos
      const allMessages = [...oldMessages, ...newMessages];
      dispatch({
        type: COLOCAR_MENSAJES_PREDETERMINADOS,
        payload: {
          messages: allMessages,
        },
      });
    }

  } catch (error) {
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};

export const editDefaultMessage = (message, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    const url = "/api/messages/defaultMessage/" + message.id;
    const res = await instanceAxios.put(url, { message: message.message, name: message.name },
      {
        headers: {
          "x-csrf-token": csrf_token,
        },
      }
    );
    if (res.status === 200) {

      const messages = getState().mensajes.messagesDefault;
      // Actualizar el mensaje editado en el estado y ponerlo al inicio
      const updatedMessages = messages.map(msg => {
        if (msg.id === message.id) {
          return {
            ...msg,
            message: message.message,
            name: message.name,
          };
        }
        return msg;
      });

      // Reordenar para que el mensaje editado aparezca primero
      const reorderedMessages = [
        ...updatedMessages.filter(msg => msg.id === message.id),
        ...updatedMessages.filter(msg => msg.id !== message.id)
      ];
      // Actualizar el estado con los mensajes editados
      dispatch({
        type: COLOCAR_MENSAJES_PREDETERMINADOS,
        payload: {
          messages: reorderedMessages,
        },
      });


    }
  } catch (error) {
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};

export const newDefaultMessage = (message, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    const url = "/api/messages/defaultMessage";
    const res = await instanceAxios.post(url, { message: message.message, name: message.name },
      {
        headers: {
          "x-csrf-token": csrf_token,
        },
      }
    );
    
    if (res.status === 201 || res.status === 200) {
      const messages = getState().mensajes.messagesDefault;
      // Agregar el nuevo mensaje al inicio del array
      const newMessages = [
        res.data.message,
        ...messages
      ];
      // Actualizar el estado con los mensajes editados
      dispatch({
        type: COLOCAR_MENSAJES_PREDETERMINADOS,
        payload: {
          messages: newMessages,
        },
      });
    }
  } catch (error) {
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};
