import { instanceAxios } from './axiosInstance';
//constantes
const dataInicial = {
  loading: false,
  user: false,
  role: null,
  mensajeError: null,
  info: null,
  name: null,
  email: null,
  phoneNumber: null,
  alias: null,
  URLphoto: null,
  loadingInfo: {
    nickname: false,
    phoneNumber: false,
    image: false,
    password: false,
  },
  usersList: {},
  loadingUsers: false,
  usersMensaje: null,
  usersSeverity: "info",
  clientesList: [],
  loadingClientes: false,
  loadingClientsRegister: false,
  clientMensaje: null,
  clientSeverity: "info",
  cantidadDeClientes: 0,
  onlyClients: null,
};
//types
const LOADING = "LOADING";
const LOADING_USERS = "LOADING_USERS";
const USUARIO_ERROR = "USUARIO_ERROR";
const USUARIO_EXITO = "USUARIO_EXITO";
const CERRAR_SESION = "CERRAR_SESION";
const MENSAJE_ERROR = "MENSAJE_ERROR";
const CLIENTES_EXITO = "CLIENTES_EXITO"
const USUARIO_INFO = "USUARIO_INFO"
const ACTUALIZAR_NICKNAME = "ACTUALIZAR_NICKNAME";
const ACTUALIZAR_PHONE_NUMBER = "ACTUALIZAR_PHONE_NUMBER";
const ACTUALIZAR_URL_PHOTO = "ACTUALIZAR_URL_PHOTO";
const ACTUALIZAR_PASSWORD = "ACTUALIZAR_PASSWORD";
const INFO_LOADING = "ACTUALIZAR_INFO_LOADING";
const COLOCAR_USUARIOS = "COLOCAR_USUARIOS";
const COLOCAR_MENSAJE_USUARIOS = "COLOCAR_MENSAJE_USUARIOS";
const COLOCAR_CLIENTES = "COLOCAR_CLIENTES";
const LOADING_CLIENTES = "LOADING_CLIENTES";
const LOADING_CLIENTS_REGISTER = "LOADING_CLIENTS_REGISTER";
const COLOCAR_MENSAJE_CLIENTES = "COLOCAR_MENSAJE_CLIENTES";
const COLOCAR_NUMERO_CLIENTES = "COLOCAR_NUMERO_CLIENTES";
const COLOCAR_CLIENTE = "COLOCAR_CLIENTE";
//reducer

export default function usuarioReducer(state = dataInicial, action) {
  switch (action.type) {
    case LOADING:
      return { ...state, loading: true };
    case LOADING_USERS:
      return { ...state, loadingUsers: action.payload };
    case USUARIO_ERROR:
      return { ...dataInicial, user: action.payload.user, loading: false };
    case USUARIO_EXITO:
      return { ...state, loading: false, user: action.payload.user, role: action.payload.role };
    case CERRAR_SESION:
      return { ...dataInicial, user: 'status 401' };
    case MENSAJE_ERROR:
      return { ...dataInicial, user: 'status 401', mensajeError: action.payload };
    case CLIENTES_EXITO:
      return { ...state, loading: false, clientes: action.payload }
    case USUARIO_INFO:
      return { ...state, loading: false, info: action.payload.userInfo, name: action.payload.userInfo.name, email: action.payload.userInfo.email, phoneNumber: action.payload.userInfo.phoneNumber, alias: action.payload.userInfo.alias, URLphoto: action.payload.userInfo.URLPhoto }
    case ACTUALIZAR_NICKNAME:
      return { ...state, loadingInfo: false, alias: action.payload.nickname };
    case ACTUALIZAR_PHONE_NUMBER:
      return { ...state, loadingInfo: false, phoneNumber: action.payload.phoneNumber };
    case ACTUALIZAR_URL_PHOTO:
      return { ...state, loadingInfo: false, URLphoto: action.payload.URLphoto };
    case INFO_LOADING:
      return { ...state, loadingInfo: { ...state.loadingInfo, ...action.payload } };
    case COLOCAR_USUARIOS:
      return { ...state, usersList: action.payload, loadingUsers: false };
    case COLOCAR_MENSAJE_USUARIOS:
      return { ...state, usersMensaje: action.payload.mensaje, usersSeverity: action.payload.severity, loadingUsers: false };
    case COLOCAR_CLIENTES:
      return { ...state, clientesList: action.payload.clients, loadingClientes: false };
    case LOADING_CLIENTES:
      return { ...state, loadingClientes: action.payload };
    case COLOCAR_MENSAJE_CLIENTES:
      return { ...state, clientMensaje: action.payload.mensaje, clientSeverity: action.payload.severity, loadingClientes: false };
    case LOADING_CLIENTS_REGISTER:
      return { ...state, loadingClientsRegister: action.payload };
    case COLOCAR_NUMERO_CLIENTES:
      return { ...state, cantidadDeClientes: action.payload.cantidadDeClientes };
    case COLOCAR_CLIENTE:
      return { ...state, onlyClients: action.payload };
    case ACTUALIZAR_PASSWORD:
      return { ...state, loadingInfo: { ...state.loadingInfo, password: false } };
    default:
      return { ...state };
  }
}

//acciones

export const editarUsuarioAccion = (userId, name, email, roleId, usersList, csrf_token) => async (dispatch) => {
  dispatch({
    type: LOADING_USERS,
  });
  try {
    let url = '/api/users/updateUsers';
    const res = await instanceAxios.put(url, {
      "userId": userId,
      "name": name,
      "email": email,
      "roleId": roleId
    }, {
      headers: {
        'x-csrf-token': csrf_token,
      },
    });
    const user = res.data.user;
    const indexToUpdate = usersList.usersInfo.findIndex(userInfo => userInfo.userId === user.userId);
    if (indexToUpdate !== -1) {
      // Actualizamos el objeto en usersList.usersInfo con la información de user
      usersList.usersInfo[indexToUpdate] = user;
    }
    dispatch({
      type: COLOCAR_USUARIOS,
      payload: usersList,
    });
    dispatch({
      type: COLOCAR_MENSAJE_USUARIOS,
      payload: {
        mensaje: res.data.status,
        severity: "success",
      },
    });
  } catch (error) {
    const status = error.response.status;
    dispatch({
      type: COLOCAR_MENSAJE_USUARIOS,
      payload: {
        mensaje: status,
        severity: "error",
      },
    });
  }
};

export const limpiarMensajeUsers = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_MENSAJE_USUARIOS,
    payload: {
      mensaje: null,
      severity: "info",
    },
  });
};

export const eliminarUsuarioAccion = (userId, usersList, csrf_token) => async (dispatch) => {
  dispatch({
    type: LOADING_USERS,
  });
  try {
    let url = '/api/users/deleteUsers';
    const res = await instanceAxios.delete(url, {
      headers: {
        'x-csrf-token': csrf_token,
      },
      data: {
        "userId": userId,
      },
    });
    const indexToDelete = usersList.usersInfo.findIndex(userInfo => userInfo.userId === userId);
    if (indexToDelete !== -1) {
      // Eliminamos el objeto en usersList.usersInfo con la información de user
      usersList.usersInfo.splice(indexToDelete, 1);
    }
    dispatch({
      type: COLOCAR_USUARIOS,
      payload: usersList,
    });
    dispatch({
      type: COLOCAR_MENSAJE_USUARIOS,
      payload: {
        mensaje: res.data.status,
        severity: "success",
      },
    });
  } catch (error) {
    const status = error.response.status;
    dispatch({
      type: COLOCAR_MENSAJE_USUARIOS,
      payload: {
        mensaje: status,
        severity: "error",
      },
    });
  }
};

export const registrarUsuarioAccion = (userData, roles, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: LOADING_USERS,
    payload: true,
  });
  try {
    let url = '/api/users/news/signup';
    const res = await instanceAxios.post(url, userData, {
      headers: {
        'x-csrf-token': csrf_token,
      },
    });
    const roleName = roles.find((role) => role.roleId === userData.roleId).roleName;
    // Actualizar la lista de usuarios en el estado
    /* debugger; */
    const userList = getState().usuario.usersList;
    if (!res.data.newUser) {
      throw {
        message: res.data ? res.data?.mensaje : "Se presento un error",
        status: res.status,
        data: res.data
      };
    }
    userList.usersInfo.push(
      {
        alias: res.data.newUser.alias,
        email: res.data.newUser.email,
        name: res.data.newUser.name,
        phoneNumber: res.data.newUser.phonenumber,
        roleName: roleName,
        userId: res.data.newUser.userId,
      }
    );
    dispatch({
      type: COLOCAR_USUARIOS,
      payload: userList,
    });
    dispatch({
      type: COLOCAR_MENSAJE_USUARIOS,
      payload: {
        mensaje: "Usuario registrado con éxito",
        severity: "success",
      },
    });
  } catch (error) {
    const err = typeof error.message === 'string' && error.message ;
    const status = error?.response?.status ? error?.response?.status : error?.response?.mensaje ? error?.response?.mensaje : "Se presento un error";
    console.log("Error completo:", error);
    console.log("Mensaje de error:", error.message);
    console.log("Status:", error.status);
    console.log("Datos adicionales:", error.data);
    dispatch({
      type: COLOCAR_MENSAJE_USUARIOS,
      payload: {
        mensaje: err ? err : status,
        severity: "error",
      },
    });
  } finally {
    dispatch({
      type: LOADING_USERS,
      payload: false,
    });
  }
};


export const obtenerUsuarios = () => async (dispatch) => {
  dispatch({
    type: LOADING_USERS,
  });
  try {
    let url = '/api/users/loadPageUsers?scope=role';
    const res = await instanceAxios.get(url);
    dispatch({
      type: COLOCAR_USUARIOS,
      payload: res.data,
    });
  } catch (error) {
    const status = error.response.status;
    dispatch({
      type: COLOCAR_USUARIOS,
      payload: 'status ' + status,
    });
  }
};

export const setUsuario = (res) => async (dispatch) => {
  dispatch({
    type: USUARIO_EXITO,
    payload: res.data,
  });
}

export const setMensajeError = (error) => async (dispatch) => {
  dispatch({
    type: MENSAJE_ERROR,
    payload: error
  });
}

export const cerrarSesionAccion = () => async (dispatch) => {
  try {
    let url = '/logout'
    await instanceAxios.post(url);
    dispatch({
      type: CERRAR_SESION,
    });
  } catch (error) {
    dispatch({
      type: CERRAR_SESION,
    });

  }

}

export const leerUsuarioActivoAccion = () => async (dispatch) => {
  dispatch({
    type: LOADING,
  });
  try {
    let url = '/user'
    const res = await instanceAxios.get(url);
    //url's
    dispatch({
      type: USUARIO_EXITO,
      payload: res.data,
    });

  }
  catch (error) {
    if (error.message === 'Network Error') {
      // Network Error, possibly due to ERR_CONNECTION_REFUSED
      console.error(error);
    }
    error.response ? (
      dispatch({
        type: USUARIO_ERROR,
        payload: {
          user: 'status ' + error.response.status,
        }
      })
    ) : (
      dispatch({
        type: USUARIO_ERROR,
        payload: {
          user: 'status ' + error.message,
        }
      })
    )
  }
};

export const leerUsuarioInfoAccion = () => async (dispatch) => {
  dispatch({
    type: LOADING,
  });
  try {
    let url = '/user'
    const res = await instanceAxios.get(url);
    //url's
    dispatch({
      type: USUARIO_INFO,
      payload: {
        userInfo: res.data.userInfo,
        name: res.data.userInfo.name,
        email: res.data.userInfo.email,
        phoneNumber: res.data.userInfo.phoneNumber,
        alias: res.data.userInfo.alias,
        URLphoto: res.data.userInfo.URLphoto,
      }
    });
  }
  catch (error) {
    const status = error.response.status ? error.response.status : 'desconocido';
    dispatch({
      type: USUARIO_ERROR,
      payload: {
        user: 'status ' + status,
      }
    });
    dispatch({
      type: CERRAR_SESION,
    });
  }
};

export const actualizarNicknameAccion = (formData, csrf_token) => async (dispatch) => {
  dispatch({
    type: INFO_LOADING,
    payload: {
      nickname: true,
    },
  });
  try {
    let url = '/api/users/user/updateUser'
    const res = await instanceAxios.put(
      url, formData, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "multipart/form-data",
      }
    }
    );
    if (res.status === 200) {
      dispatch({
        type: ACTUALIZAR_NICKNAME,
        payload: {
          nickname: res.data.update,
        },
      });
    } else {
      const status = res.status;
      dispatch({
        type: USUARIO_ERROR,
        payload: {
          user: 'status ' + status,
        }
      });
    }
  }
  catch (error) {
    const status = error.response.status;
    dispatch({
      type: USUARIO_ERROR,
      payload: {
        user: 'status ' + status,
      }
    });
  };
};

export const actualizarPhoneNumberAccion = (formData, csrf_token) => async (dispatch) => {
  dispatch({
    type: INFO_LOADING,
    payload: {
      phoneNumber: true,
    },
  });
  try {
    let url = '/api/users/user/updateUser'
    const res = await instanceAxios.put(
      url, formData, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "multipart/form-data",
      }
    }
    );
    if (res.status === 200) {
      dispatch({
        type: ACTUALIZAR_PHONE_NUMBER,
        payload: {
          phoneNumber: res.data.update,
        },
      });
    } else {
      const status = res.status;
      dispatch({
        type: USUARIO_ERROR,
        payload: {
          user: 'status ' + status,
        }
      });
    }
  }
  catch (error) {
    const status = error.response.status;
    dispatch({
      type: USUARIO_ERROR,
      payload: {
        user: 'status ' + status,
      }
    });
  };
};

export const uploadImageAccion = (formData, csrf_token) => async (dispatch) => {
  dispatch({
    type: INFO_LOADING,
    payload: {
      image: true,
    },
  });
  try {
    let url = '/api/users/user/updateUser';
    const res = await instanceAxios.put(
      url,
      formData,
      {
        headers: {
          "x-csrf-token": "8f93c3ed-dad1-4a4f-8c24-2e682db32884",

        },
      }
    );

    if (res.status === 200) {
      // Actualizar la URL de la foto del perfil en el estado
      dispatch({
        type: ACTUALIZAR_URL_PHOTO,
        payload: {
          URLphoto: res.data.update,
        },
      });
    } else {
      const status = res.status;
      dispatch({
        type: USUARIO_ERROR,
        payload: {
          user: 'status ' + status,
        }
      });
    }
  } catch (error) {
    const status = error.response.status;
    dispatch({
      type: USUARIO_ERROR,
      payload: {
        user: 'status ' + status,
      }
    });
  }
};

export const actualizarPasswordAccion = (oldPassword, newPassword, csrf_token) => async (dispatch) => {
  dispatch({
    type: INFO_LOADING,
    payload: {
      password: true,
    },
  });
  try {
    let url = '/api/users/changePassword';
    const res = await instanceAxios.patch(
      url,
      {
        oldPassword,
        newPassword,
      },
      {
        headers: {
          "x-csrf-token": "98183da4-4461-4f1e-81f6-21c679b31a2e",
          "Content-Type": "application/json",
        },
      }
    );
    if (res.status === 200) {
      dispatch({
        type: ACTUALIZAR_PASSWORD,
        payload: {
          mensaje: "Contraseña actualizada con éxito",
          severity: "success",
        },
      });
      dispatch({
        type: COLOCAR_MENSAJE_USUARIOS,
        payload: {
          mensaje: "Contraseña actualizada con éxito",
          severity: "success",
        },
      });
    } else {
      const status = res.status;
     /*  dispatch({
        type: USUARIO_ERROR,
        payload: {
          user: 'status ' + status,
        },
      }); */
      dispatch({
        type: COLOCAR_MENSAJE_USUARIOS,
        payload: {
          mensaje: res.data.mensaje || "Error al actualizar la contraseña",
          severity: "error",
        },
      });
    }
  } catch (error) {
    const status = error.response?.status ? error.response.status : 'desconocido';
    const errorMessage = error.response?.data?.errores || error.response?.data?.mensaje || 'Error al actualizar la contraseña';
   /*  dispatch({
      type: USUARIO_ERROR,
      payload: {
        user: 'status ' + status,
      },
    }); */
    dispatch({
      type: COLOCAR_MENSAJE_USUARIOS,
      payload: {
        mensaje: errorMessage,
        severity: "error",
      },
    });
  }
};

//clientes

export const leerClientesAccion = () => async (dispatch) => {
  // dispatch({
  //   type: LOADING,
  // });
  try {
    let url = 'api/clients/clientsFiltro'
    const res = await instanceAxios.get(url);
    dispatch({
      type: CLIENTES_EXITO,
      payload: res.data,

    })
  }
  catch (error) {
    const status = error.response.status;
    dispatch({
      type: USUARIO_ERROR,
      payload: {
        user: 'status ' + status,
      }
    });
  }
}

//clientes redux

export const getClients = (value = 1) => async (dispatch, getState) => {
  dispatch({
    type: LOADING_CLIENTES,
    payload: true,
  });
  try {
    let clientNum = 30;
    let topClient = value * clientNum;
    let baseClientsNum = topClient - 30;
    let url = `api/clients/clientsFiltro?search=&next=${topClient}&offset=${baseClientsNum}`;
    const res = await instanceAxios.get(url);
    dispatch({
      type: COLOCAR_CLIENTES,
      payload: res.data,
    });
  } catch (error) {
    const status = error.response;
    dispatch({
      type: COLOCAR_CLIENTES,
      payload: 'status ' + status,
    });
  }
  finally {
    dispatch({
      type: LOADING_CLIENTES,
      payload: false
    });
  }
}

export const eliminarClienteAccion = (userId, usersList, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: LOADING_CLIENTES,
  });
  try {
    let url = `/api/clients/delete/${userId}`;
    const res = await instanceAxios.delete(url, {
      headers: {
        'x-csrf-token': csrf_token,
      },
      // data: {
      //   "userId": userId,
      // },
    });
    const state = getState();
    const clientesList = state.usuario.clientesList.filter(cliente => cliente.clientId != userId);
    dispatch({
      type: COLOCAR_CLIENTES,
      payload: { clients: clientesList },
    });
    dispatch({
      type: COLOCAR_MENSAJE_CLIENTES,
      payload: {
        mensaje: "Cliente eliminado con éxito",
        severity: "success",
      },
    });
  } catch (error) {
    const status = error.response.status;
    dispatch({
      type: COLOCAR_MENSAJE_CLIENTES,
      payload: {
        mensaje: "Ocurrio un error " + status,
        severity: "error",
      },
    });
  }
};

export const limpiarMensajeClients = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_MENSAJE_CLIENTES,
    payload: {
      mensaje: null,
      severity: "info",
    },
  });
};

export const getNumClients = () => async (dispatch) => {
  try {
    let url = 'api/clients/numClientsFiltro?search';
    const res = await instanceAxios.get(url);
    const cantidadDeClientesCount = Math.ceil(res.data.numClients / 30);

    dispatch({
      type: COLOCAR_NUMERO_CLIENTES,
      payload: { cantidadDeClientes: cantidadDeClientesCount },
    });
  } catch (error) {
    const status = error.response.status;
    dispatch({
      type: COLOCAR_MENSAJE_CLIENTES,
      payload: 'status ' + status,
    });
  }
};


export const registerClient = (userData, usersList, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: LOADING_CLIENTS_REGISTER,
    payload: true,
  });
  try {

    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const registrationDate = `${year}-${month}-${day}`;
    let data = { ...userData, marketplaceClientId: "", registrationDate };
    let url = '/api/clients/new';
    const res = await instanceAxios.post(url, data, {
      headers: {
        'x-csrf-token': csrf_token,
      },
    });

    const state = getState();
    const clientesList = [...state.usuario.clientesList, res.data.client]

    dispatch({
      type: COLOCAR_CLIENTES,
      payload: { clients: clientesList },
    });
    dispatch({
      type: COLOCAR_CLIENTE,
      payload: res.data.client,
    });
    dispatch({
      type: COLOCAR_MENSAJE_CLIENTES,
      payload: {
        mensaje: "Cliente registrado con éxito",
        severity: "success",
      },
    });
  } catch (error) {
    const status = error.response.status;
    dispatch({
      type: COLOCAR_MENSAJE_CLIENTES,
      payload: {
        mensaje: "Ocurrio un error " + status,
        severity: "error",
      },
    });
  }
  finally {
    dispatch({
      type: LOADING_CLIENTS_REGISTER,
      payload: false,
    });
  }

};

export const updateClient = (userData, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: LOADING_CLIENTS_REGISTER,
    payload: true,
  });
  try {

    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const registrationDate = `${year}-${month}-${day}`;

    const { clientId } = userData;

    let url = `api/clients/update/${clientId}`;
    const res = await instanceAxios.put(url, userData, {
      headers: {
        'x-csrf-token': csrf_token,
      },
    });

    //como no arroja un error directamente, se verifica si el cliente fue encontrado en la respuesta
    if (!res.data.client?.name) throw new Error("El cliente no fue encontrado en la respuesta.");

    const state = getState();
    const index = state.usuario.clientesList.findIndex(cliente => cliente.clientId === clientId);
    const clientesList = [...state.usuario.clientesList];
    clientesList[index] = userData;
    dispatch({
      type: COLOCAR_CLIENTES,
      payload: { clients: clientesList },
    });
    dispatch({
      type: COLOCAR_MENSAJE_CLIENTES,
      payload: {
        mensaje: "Cliente actualizado con éxito",
        severity: "success",
      },
    });
  } catch (error) {
    const status = error.response?.status ? error.response.status : "desconocido";
    dispatch({
      type: COLOCAR_MENSAJE_CLIENTES,
      payload: {
        mensaje: "Ocurrio un error " + status,
        severity: "error",
      },
    });
  }
  finally {
    dispatch({
      type: LOADING_CLIENTS_REGISTER,
      payload: false,
    });
  }

};

export const nullOnlyClient = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_CLIENTE,
    payload: null,
  });
}
