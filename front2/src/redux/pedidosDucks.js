import { instanceAxios } from "./axiosInstance";
import {
  getfullFilmentChannelListPropertyUsingfullFiltmentNumber,
  checkPaidAmount,
} from "../Utils/atributtesHandler";
import { cs } from "date-fns/locale";

//url's
//--url base
//constantes
const dataInicial = {
  nextC: 30,
  // numero total de ordenes de la base
  totalOrders: 0,
  esperaTortuga: false,
  esperaCargando: null,
  unPedido: null,
  cantidadDePaginas: null,
  totalProductosPedidos: null,
  orderInternalStatus: [],
  orderStatus: [],
  orders: null,
  marketplaces: [],
  pedidoRecargando: null,
  logosEspera: false,
  marketplaceLogos: {},
  guiaEspera: false,
  guiaContenido: null,
  guiaType: null,
  guiaError: null,
  dataActualizacion: null,
  mensajeComentario: null,
  severityComentario: null,
  comments: [],
  msgVentas: null,
  loading: false,
  pedidoSurtir: null,
  messageSurtido: null,
  severitySurtido: null,
  msgRelacionProductos: null,
  severityRelacionProductos: null,
  commentMaxLength: 100,
  orderInternalStatusHistory: {},
  showDeletedComments: false, // <-- Agregado para el switch de comentarios eliminados
};
//types
const PEDIDO_INFO_EXITO = "PEDIDO_INFO_EXITO";
const COLOCAR_GUIA_ESPERA = "COLOCAR_GUIA_ESPERA";
const COLOCAR_GUIA_CONTENIDO = "COLOCAR_GUIA_CONTENIDO";
const COLOCAR_GUIA_ERROR = "COLOCAR_GUIA_ERROR";
const COLOCAR_PEDIDO_ESPERA = "COLOCAR_PEDIDO_ESPERA";
const COLOCAR_PEDIDOS_ESPERA = "COLOCAR_PEDIDOS_ESPERA";
const COLOCAR_PEDIDOS_ESPERA_NULL_PEDIDOS =
  "COLOCAR_PEDIDOS_ESPERA_NULL_PEDIDOS";
const COLOCAR_MENSAJE_RELACION = "COLOCAR_MENSAJE_RELACION";
const OBTENER_NUMEROPEDIDOS_EXITO = "OBTENER_NUMEROPEDIDOS_EXITO";
const COLOCAR_PEDIDO_NULL = "COLOCAR_PEDIDO_NULL";
const OBTENER_PEDIDOS_FILTRADOS_EXITO = "OBTENER_PEDIDOS_FILTRADOS_EXITO";
const COLOCAR_PEDIDOS_STATUS_500 = "COLOCAR_PEDIDOS_STATUS_500";
const COLOCAR_LOGOS_ESPERA = "COLOCAR_LOGOS_ESPERA";
const COLOCAR_MARKETPLACES_LOGOS = "COLOCAR_MARKETPLACES_LOGOS";
const COLOCAR_CANTIDADPAGINAS_NULL = "COLOCAR_CANTIDADPAGINAS_NULL";
const COLOCAR_DATA_ACTUALIZACION = "COLOCAR_DATA_ACTUALIZACION";
const COLOCAR_MENSAJE_COMENTARIO = "COLOCAR_MENSAJE_COMENTARIO";
const COLOCAR_PEDIDO_A_SURTIR = "COLOCAR_PEDIDO_A_SURTIR";
const COLOCAR_MENSAJE_SURTIDO = "COLOCAR_MENSAJE_SURTIDO";
const COLOCAR_MENSAJE_SURTIDO_NULL = "COLOCAR_MENSAJE_SURTIDO_NULL";
//PUT (modificación)
const COLOCAR_PEDIDO_RECARGANDO = "COLOCAR_PEDIDO_RECARGANDO";
const CAMBIAR_STATUS_INTERNO = "CAMBIAR_STATUS_INTERNO";
const CAMBIAR_STATUS_INTERNO_TABLA = "CAMBIAR_STATUS_INTERNO_TABLA";
const CAMBIAR_COMMENTS_TABLA = "CAMBIAR_COMMENTS_TABLA";
//Modificar info en hoja tablaPedidos
const CAMBIAR_RECARGANDO_INFORMACION = "CAMBIAR_RECARGANDO_INFORMACION";
const COMMENTS = "COMMENTS";
const COLOCAR_MSG_VENTAS = "COLOCAR_MSG_VENTAS";
const COLOCAR_PEDIDO_VACIO = "COLOCAR_PEDIDO_VACIO";
const COLOCAR_NULL_MSG_VENTAS = "COLOCAR_NULL_MSG_VENTAS";
const CAMBIAR_COMMENT_MAX_LENGTH = "CAMBIAR_COMMENT_MAX_LENGTH";
const COLOCAR_HISTORIAL_STATUS_INTERNO = "COLOCAR_HISTORIAL_STATUS_INTERNO";
const SET_SHOW_DELETED_COMMENTS = "SET_SHOW_DELETED_COMMENTS"; // <-- Nuevo type
//reducer
export default function pedidosReducer(state = dataInicial, action) {
  switch (action.type) {
    case CAMBIAR_RECARGANDO_INFORMACION:
      return {
        ...state,
        recargandoInformacion: action.payload,
      };
    case PEDIDO_INFO_EXITO:
      return {
        ...state,
        unPedido: action.payload.order,
        esperaCargando: false,
        totalProductosPedidos: action.payload.numProductos,
      };
    case COLOCAR_PEDIDO_ESPERA:
      return {
        ...state,
        esperaCargando: action.payload,
      };
    case COLOCAR_CANTIDADPAGINAS_NULL:
      return {
        ...state,
        cantidadDePaginas: action.payload,
      };
    case COLOCAR_PEDIDOS_ESPERA_NULL_PEDIDOS:
      return {
        ...state,
        esperaCargando: action.payload,
        // orders: null,
        // totalProductosPedidos: 0,
        loading: action.payload,
      };
    case COLOCAR_PEDIDOS_ESPERA:
      return { ...state, esperaTortuga: action.payload };
    case COLOCAR_PEDIDO_NULL:
      return {
        ...state,
        unPedido: action.payload,
        totalProductosPedidos: 0,
      };
    case OBTENER_NUMEROPEDIDOS_EXITO:
      return {
        ...state,
        cantidadDePaginas: action.payload.cantidadDePaginas,
        orderStatus: action.payload.orderStatus,
        orderInternalStatus: action.payload.orderInternalStatus,
        marketplaces: action.payload.marketplaces,
        orders: null,
        totalOrders: action.payload.totalOrders,
        search: action.payload.search,
      };
    case OBTENER_PEDIDOS_FILTRADOS_EXITO:
      return {
        ...state,
        orders: action.payload.orders,
        totalProductosPedidos: action.payload.numPedidos,
        esperaTortuga: false,
      };
    case COLOCAR_PEDIDOS_STATUS_500:
      return {
        ...state,
        orders: action.payload,
        esperaTortuga: false,
        unPedido: null,
        totalPedidos: 0,
      };
    case CAMBIAR_STATUS_INTERNO:
      return {
        ...state,
        unPedido: action.payload,
        esperaCargando: false,
      };
    case CAMBIAR_STATUS_INTERNO_TABLA:
      return {
        ...state,
        orders: action.payload.orders,
        esperaTortuga: false,
        recargandoInformacion: 0,
      };
    case CAMBIAR_COMMENTS_TABLA:
      return {
        ...state,
        orders: action.payload,
      };
    case COLOCAR_PEDIDO_RECARGANDO:
      return {
        ...state,
        pedidoRecargando: action.payload,
      };
    case COLOCAR_MARKETPLACES_LOGOS:
      return {
        ...state,
        marketplaceLogos: action.payload,
        esperaTortuga: false,
      };
    case COLOCAR_LOGOS_ESPERA:
      return {
        ...state,
        logosEspera: action.payload,
      };
    case COLOCAR_GUIA_ESPERA:
      return {
        ...state,
        guiaEspera: action.payload,
        guiaContenido: null,
        guiaType: null,
      };
    case COLOCAR_GUIA_CONTENIDO:
      return {
        ...state,
        guiaContenido: action.payload.fileUrl,
        guiaType: action.payload.type,
        guiaEspera: false,
      };
    case COLOCAR_GUIA_ERROR:
      return {
        ...state,
        guiaError: action.payload.message,
        guiaEspera: false,
      };
    case COLOCAR_DATA_ACTUALIZACION:
      return {
        ...state,
        dataActualizacion: action.payload,
      };
    case COLOCAR_MENSAJE_COMENTARIO:
      return {
        ...state,
        mensajeComentario: action.payload.mensaje,
        severityComentario: action.payload.severity,
      };
    case COMMENTS:
      return {
        ...state,
        comments: [...state.comments, action.payload],
      };
    case COLOCAR_MSG_VENTAS:
      return {
        ...state,
        msgVentas: action.payload,
      };
    case COLOCAR_PEDIDO_VACIO:
      return {
        ...state,
        comments: [],
      };
    case COLOCAR_NULL_MSG_VENTAS:
      return {
        ...state,
        msgVentas: null,
      };
    case COLOCAR_PEDIDO_A_SURTIR:
      return {
        ...state,
        pedidoSurtir: action.payload,
      };
    case COLOCAR_MENSAJE_SURTIDO:
      return {
        ...state,
        messageSurtido: action.payload.mensaje,
        severitySurtido: action.payload.severity,
      };
    case COLOCAR_MENSAJE_SURTIDO_NULL:
      return {
        ...state,
        messageSurtido: null,
        severitySurtido: null,
      };
    case COLOCAR_MENSAJE_RELACION:
      return {
        ...state,
        msgRelacionProductos: action.payload.mensaje,
        severityRelacionProductos: action.payload.severity
      }
    case CAMBIAR_COMMENT_MAX_LENGTH:
      return {
        ...state,
        commentMaxLength: action.payload || 100
      };
    case COLOCAR_HISTORIAL_STATUS_INTERNO:
      return {
        ...state,
        orderInternalStatusHistory: {
          ...state.orderInternalStatusHistory,
          [action.payload.orderId]: action.payload.data,
        },
      };
    case SET_SHOW_DELETED_COMMENTS:
      return {
        ...state,
        showDeletedComments: action.payload,
      };
    default:
      return state;
  }
}
//acciones
export const surtiendoPedido = (data) => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_PEDIDO_A_SURTIR,
      payload: data,
    });
  } catch (error) {
    console.log(error);
  }
};

export const cambiarRecargandoInformacion =
  (nuevoEstado) => async (dispatch) => {
    try {
      dispatch({
        type: CAMBIAR_RECARGANDO_INFORMACION,
        payload: nuevoEstado,
      });
    } catch (error) {
      console.log(error);
    }
  };

export const obtenerImagenesMarketplaces =
  (marketplaces) => async (dispatch) => {
    dispatch({
      type: COLOCAR_LOGOS_ESPERA,
      payload: true,
    });
    try {
      const imagesArr = await Promise.all(
        marketplaces.map(async (marketplace) => {
          try {
            const response = await instanceAxios.get(
              marketplace.supportedMarketplace.urlPhoto,
              { responseType: "blob" }
            );
            const blob = response.data;
            const objectUrl = URL.createObjectURL(blob);
            return {
              supportedMarketplaceId:
                marketplace.supportedMarketplace.id,
              imagen: objectUrl,
            };
          } catch (error) {
            return {
              supportedMarketplaceId:
                marketplace.supportedMarketplace.id,
              imagen: null,
            };
          }
        })
      );
      dispatch({
        type: COLOCAR_MARKETPLACES_LOGOS,
        payload: imagesArr,
      });
    } catch (error) {
      dispatch({
        type: COLOCAR_MARKETPLACES_LOGOS,
        payload: {},
      });
    }
    dispatch({
      type: COLOCAR_LOGOS_ESPERA,
      payload: false,
    });
  };

export const colocarDataActualizacion = (csrf_token) => async (dispatch) => {
  try {
    let url = "/api/marketplaces/marketplaceGroup?scope=marketplaces_info";
    const res = await instanceAxios.get(url, {
      headers: {
        "x-csrf-token": csrf_token,
      },
    });
    if (res.status == 200) {
      dispatch({
        type: COLOCAR_DATA_ACTUALIZACION,
        payload: res.data,
      });
    }
  } catch (error) {
    dispatch({
      type: COLOCAR_DATA_ACTUALIZACION,
      payload: {
        message: "Error al obtener la fecha de actualización",
      },
    });
  }
};

export const cambiarStatusInternoDeUnaOrden =
  (orderIdParam, newInternalStatusParam, csrf_token) =>
    async (dispatch, getState) => {
      const unPedidoPrevio = getState().pedidos.unPedido;
      try {
        let url = "/api/orders/modificarStatusInterno";
        const res = await instanceAxios.put(
          url,
          {
            orderId: orderIdParam,
            newInternalStatus: newInternalStatusParam,
          },
          {
            headers: {
              "x-csrf-token": csrf_token,
            },
          }
        );
        if (res.status == 200) {
          dispatch({
            type: CAMBIAR_STATUS_INTERNO,
            payload: {
              ...unPedidoPrevio,
              internalStatus: newInternalStatusParam,
            },
          });
        } else {
          dispatch({
            type: CAMBIAR_STATUS_INTERNO,
            payload: {
              ...unPedidoPrevio,
            },
          });
        }
      } catch (error) {
        console.log(error);
        dispatch({
          type: CAMBIAR_STATUS_INTERNO,
          payload: {
            ...unPedidoPrevio,
          },
        });
      }
    };


export const setNullMsgVentas = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_NULL_MSG_VENTAS,
    payload: null,
  });
};


export const setMsgVentas = (mensaje, severity, extraFunction = null) => async (dispatch) => {
  dispatch({
    type: COLOCAR_MSG_VENTAS,
    payload: {
      mensaje: mensaje,
      severity: severity,
      extraFunction: extraFunction
    },
  });

};


function detectMime(buffer) {
  const view = new Uint8Array(buffer);

  // Helper para comparar con firmas de 4 u 8 bytes
  const match = (sig) =>
    sig.every((b, i) => view[i] === b);

  // PDF
  if (match([0x25, 0x50, 0x44, 0x46])) return 'application/pdf';

  // PNG
  if (match([0x89, 0x50, 0x4E, 0x47])) return 'image/png';

  // JPEG (varias variantes)
  if (match([0xFF, 0xD8, 0xFF, 0xE0]) ||
    match([0xFF, 0xD8, 0xFF, 0xE1]) ||
    match([0xFF, 0xD8, 0xFF, 0xE2]))
    return 'image/jpeg';

  // GIF
  if (match([0x47, 0x49, 0x46, 0x38])) return 'image/gif';

  // TIFF little-endian (II*) y big-endian (MM*)
  if (match([0x49, 0x49, 0x2A, 0x00]) ||
    match([0x4D, 0x4D, 0x00, 0x2A]))
    return 'image/tiff';

  // BMP
  if (match([0x42, 0x4D])) return 'image/bmp';

  // WEBP: “RIFF....WEBP”
  if (match([0x52, 0x49, 0x46, 0x46]) &&
    view[8] === 0x57 && view[9] === 0x45 &&
    view[10] === 0x42 && view[11] === 0x50)
    return 'image/webp';

  // ICO
  if (match([0x00, 0x00, 0x01, 0x00])) return 'image/vnd.microsoft.icon';

  // HEIC/HEIF (ftyp heic/ftyp heix)
  if (match([0x00, 0x00, 0x00, 0x18]) &&
    view[4] === 0x66 && view[5] === 0x74 &&
    view[6] === 0x79 && view[7] === 0x70)
    return 'image/heic';

  return 'application/octet-stream'; // desconocido
}



export const imprimirGuiaDeUnaOrden =
  (orderId, csrf_token) => async (dispatch, getState) => {
    dispatch({
      type: COLOCAR_GUIA_ESPERA,
      payload: true,
    });
    try {
      let url = "/api/guides/getPDF/" + orderId;
      const res = await instanceAxios.get(url, { responseType: "arraybuffer" });
      if (res.status === 200) {
        const realMime = detectMime(res.data);
        const blob = new Blob([res.data], { type: realMime });
        const fileUrl = URL.createObjectURL(blob);
        dispatch({
          type: COLOCAR_GUIA_CONTENIDO,
          payload: {
            fileUrl: fileUrl,
            type: realMime,
          },
        });
      } else {
        // Manejar errores de la petición
        const response = res.data;

        // Convierte el ArrayBuffer en un texto usando TextDecoder
        const decoder = new TextDecoder('utf-8');
        const text = decoder.decode(response);

        dispatch({
          type: COLOCAR_GUIA_ERROR,
          payload: {
            message: text,
          },
        });
      }
    } catch (error) {
      const response = error.response.data;

      // Convierte el ArrayBuffer en un texto usando TextDecoder
      const decoder = new TextDecoder('utf-8');
      const text = decoder.decode(response);


      dispatch({
        type: COLOCAR_GUIA_ERROR,
        payload: {
          message: text,
        },
      });
    }
  };


export const colocarPedidoNull = () => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_PEDIDO_NULL,
      payload: null,
    });
  } catch (error) {
    console.log(error);
  }
};

export const colocarPedidosStatus500 = () => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_PEDIDOS_STATUS_500,
      payload: "status 500",
    });
  } catch (error) {
    console.log(error);
  }
};

export const obtenerTotalPedidos =
  ({
    proveedor = "",
    filtrosStatus = "",
    filtrosStatusInterno = "",
    finalDate = "",
    initialDate = "",
    abiertosCerrados = "",
    sellerMarketplace = "",
    search = "",
  } = {}) =>
    async (dispatch, getState) => {
      const cantidadDePedidosPorPagina = getState().pedidos.nextC;
      try {
        dispatch({
          type: COLOCAR_CANTIDADPAGINAS_NULL,
          payload: null,
        });
        let url = "/api/orders/loadPageOrders";
        const res = await instanceAxios.get(url, {
          params: {
            search: search,
            proveedores: proveedor,
            fechaInicial: initialDate,
            fechaFinal: finalDate,
            orderStatusId: filtrosStatus,
            orderStatusInternoId: filtrosStatusInterno,
            abiertosCerrados: abiertosCerrados,
            sellerMarketplace: sellerMarketplace,
          },
        });
        const numPedidos = res.data.numeroRegistros;
        const orderStatus = res.data.orderStatus;
        const orderInternalStatus = res.data.orderInternalStatus;
        const cantidadDePaginas = Math.ceil(
          numPedidos / cantidadDePedidosPorPagina
        );
        dispatch({
          type: CAMBIAR_COMMENT_MAX_LENGTH,
          payload: res.data.configs.commentMaxLength,
        });
        dispatch({
          type: OBTENER_NUMEROPEDIDOS_EXITO,
          payload: {
            cantidadDePaginas: cantidadDePaginas,
            orderStatus: orderStatus,
            orderInternalStatus: orderInternalStatus,
            marketplaces: res.data.marketplaces,
            totalOrders: numPedidos,
            search: search,

          },
        });
      } catch (error) {
        const status = error.response.status;
        dispatch({
          type: OBTENER_NUMEROPEDIDOS_EXITO,
          payload: {
            cantidadDePaginas: "status " + status,
          },
        });
      }
    };

export const colocarPedidoEspera = () => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_PEDIDO_ESPERA,
      payload: true,
    });
  } catch (error) {
    console.log(error);
  }
};

export const colocarPedidosEspera = () => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_PEDIDOS_ESPERA,
      payload: true,
    });
  } catch (error) {
    console.log(error);
  }
};

export const obtenerPedidosFiltradosAccion =
  ({
    offsetActual = 0,
    proveedor = "",
    filtrosStatus = "",
    filtrosStatusInterno = "",
    initialDate = "",
    finalDate = "",
    abiertosCerrados = "",
    sellerMarketplace = "",
    search = "",
  } = {}) =>
    async (dispatch, getState) => {
      const cantidadDePedidosPorPagina = getState().pedidos.nextC;
      try {
        let url = "/api/orders/ordersFiltro";
        dispatch({
          type: COLOCAR_PEDIDOS_ESPERA_NULL_PEDIDOS,
          payload: true,
          // totalProductosPedidos: 0,
          // loading: true,
        });
        const res = await instanceAxios.get(url, {
          params: {
            search: search,
            next: cantidadDePedidosPorPagina,
            offset: offsetActual,
            proveedores: proveedor,
            fechaInicial: initialDate,
            fechaFinal: finalDate,
            orderStatusId: filtrosStatus,
            orderStatusInternoId: filtrosStatusInterno,
            abiertosCerrados: abiertosCerrados,
            sellerMarketplace: sellerMarketplace,
          },
        });
        const orders = res.data.orders.map(
          (order) => {
            const { orderStackableComments, productsInOrder, ...rest } = order
            return {
              comments: orderStackableComments,
              products: productsInOrder,
              ...rest
            }
          }
        )
        ///
        dispatch({
          type: OBTENER_PEDIDOS_FILTRADOS_EXITO,
          payload: {
            orders: orders,
            numPedidos: orders.length,
            loading: false,
          },
        });
      } catch (error) {
        const status = error.response.status;
        dispatch({
          type: OBTENER_PEDIDOS_FILTRADOS_EXITO,
          payload: "status " + status,
        });
      } finally {
        dispatch({
          type: COLOCAR_PEDIDOS_ESPERA_NULL_PEDIDOS,
          payload: false,
        });
      }
    };

export const RelacionarProductosConOrdenes = (data, orderId, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: COLOCAR_PEDIDOS_ESPERA,
    payload: true,
  })
  try {
    const url = "/api/publication/relatePublicationProductToProductStores";
    const res = await instanceAxios.post(url, data, {
      headers: {
        "x-csrf-token": csrf_token,
      },
    });
    if (res.status == 200) {
      dispatch({
        type: COLOCAR_MENSAJE_RELACION,
        payload: {
          mensaje: "Relación de productos exitosa",
          severity: "success"
        }
      })
      dispatch({
        type: COLOCAR_PEDIDOS_ESPERA,
        payload: false,
      })
    }
  }
  catch (error) {
    dispatch({
      type: COLOCAR_MENSAJE_RELACION,
      payload: {
        mensaje: error.response.data.error,
        severity: "error"
      }
    })
    dispatch({
      type: COLOCAR_PEDIDOS_ESPERA,
      payload: false,
    })
  }
}

export const limpiarMensajeRelacion = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_MENSAJE_RELACION,
    payload: {
      mensaje: null,
      severity: null
    }
  })
}

export const surtirOrden = (data, csrf_token) => async (dispatch, getState) => {
  dispatch({
    type: COLOCAR_PEDIDOS_ESPERA,
    payload: true,
  })
  try {
    const url = "/api/order/supply";
    const res = await instanceAxios.post(url, data, {
      headers: {
        "x-csrf-token": csrf_token,
      },
    });
    if (res.status == 200) {
      dispatch({
        type: COLOCAR_MENSAJE_SURTIDO,
        payload: {
          mensaje: "Orden surtida con éxito",
          severity: "success"
        }
      })
    }
  }
  catch (error) {
    dispatch({
      type: COLOCAR_MENSAJE_SURTIDO,
      payload: {
        mensaje: error.response.data.errores,
        severity: "error"
      }
    })
  }
  dispatch({
    type: COLOCAR_PEDIDOS_ESPERA,
    payload: false,
  })
}

export const limpiarMensajeSurtido = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_MENSAJE_SURTIDO_NULL,
  })
}

export const obtenerHistorialStatusInterno = (orderId, csrf_token) => async (dispatch) => {
  try {
    const url = `/api/orders/${orderId}/internalStatusChange`;
    const res = await instanceAxios.get(url, {
      headers: {
        "x-csrf-token": csrf_token,
      },
    });
    dispatch({
      type: COLOCAR_HISTORIAL_STATUS_INTERNO,
      payload: { orderId, data: res.data },
    });
    return res.data;
  } catch (error) {
    dispatch({
      type: COLOCAR_HISTORIAL_STATUS_INTERNO,
      payload: { orderId, data: { error: "Error al obtener historial" } },
    });
    return { error: "Error al obtener historial" };
  }
};

// Action creator
export const setShowDeletedComments = (value) => ({
  type: SET_SHOW_DELETED_COMMENTS,
  payload: value,
});