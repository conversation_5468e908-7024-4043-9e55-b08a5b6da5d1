//import  axios  from 'axios';
import { add } from "date-fns";
import { instanceAxios } from "./axiosInstance";

//Data inicial
const dataInicial = {
  loading: false,  
  mensajeAlert: null,
  openModal: false,
  nextC: 30,
  cantidadDePaginas: null,
  esperaTortuga: false,
  productos: null,
  productoIndividual: null,
  selectedProduct: null,
  estadoEliminandoProducto: null,
  msnesajeEliminando: null,
  plantillaDescargada: null,
  messageDeletingElementProduct:"",
  productofiltro2: null,
  exitAddCustomVariation: null,
  existAddCustomVariationAttribute: null,

  variacionesProducto: null,
  productoIndividualStock: [],
  persevereDataNewProduct: {
    description: "",
    longDescription: "",
    brand: "",
    model: "",
    upc: "",
    video: "",
    satCode: "",
    unitLengthShipping: "",
    unitCode: "",
    condition: "",
    dimentionShipment: {
      unitLength: "",
      unitWeigth: "",
      height: "",
      width: "",
      length: "",
      weight: "",
    },
    dimentionProduct: {
      unitLength: "",
      unitWeigth: "",
      height: "",
      width: "",
      length: "",
      weight: "",
    },
    tags: [],
    attributes: [],
    imageList: null,
    data: null,
    variations: [],
    componentsFlag: [],
    selectedVariations: [],
    selectNumberVAriation: [],
    imgs: [],
    marketplacesInfo: null,
    marketplaceLogos: null,
  },
  newProductOrigin: null,
};
const resetPersevereDataNewProduct = {
  description: "",
  longDescription: "",
  brand: "",
  model: "",
  upc: "",
  video: "",
  satCode: "",
  unitLengthShipping: "",
  unitCode: "",
  condition: "",
  dimentionShipment: {
    unitLength: "",
    unitWeigth: "",
    height: "",
    width: "",
    length: "",
    weight: "",
  },
  dimentionProduct: {
    unitLength: "",
    unitWeigth: "",
    height: "",
    width: "",
    length: "",
    weight: "",
  },
  tags: [],
  attributes: [],
  imageList: null,
  data: null,
  new: [],
  variations: [],
  componentsFlag: [],
  // ["talla"]
  selectedVariations: [],
  // [1,2]
  selectNumberVAriation: [],
  imgs: [],
};
//types
//DragAndDrop
const CAMBIAR_MENSAJE_EXITO = "CAMBIAR_MENSAJE_EXITO";
const CAMBIAR_LOADING = "CAMBIAR_LOADING";
//Obtener productos
const COLOCAR_CANTIDADPAGINAS_NULL = "COLOCAR_CANTIDADPAGINAS_NULL";
const OBTENER_NUMEROPRODUCTOS_EXITO = "OBTENER_NUMEROPRODUCTOS_EXITO";
const OBTENER_PRODUCTOS_FILTRADOS_EXITO = "OBTENER_PRODUCTOS_FILTRADOS_EXITO";
const COLOCAR_NULL_PRODUCTOS = "COLOCAR_NULL_PRODUCTOS";
const COLOCAR_PRODUCTOS_ESPERA_NULL_PRODUCTOS =
  "COLOCAR_PRODUCTOS_ESPERA_NULL_PRODUCTOS";
//Obtener producto individual
const OBTENER_PRODUCTOINDIVIDUAL_EXITO = "OBTENER_PRODUCTOINDIVIDUAL_EXITO";
const OBTENER_PRODUCTOINDIVIDUALSTOCK_EXITO =
  "OBTENER_PRODUCTOINDIVIDUALSTOCK_EXITO";
const COLOCAR_PRODUCTOINDIVIDUAL_NULL = "COLOCAR_PRODUCTOINDIVIDUAL_NULL";
const COLOCAR_SELECTED_PRODUCT = "COLOCAR_SELECTED_PRODUCT";
//PUT (modificación) POST(Agregación)
//DELETE (eliminación)
const COLOCAR_ESTADEILIMINANDOPRODUCTO = "COLOCAR_ESTADEILIMINANDOPRODUCTO";
//Carga masiva
const COLOCAR_PLANTILLA_DESCARGADA = "COLOCAR_PLANTILLA_DESCARGADA";
// variaciones
const OBTENER_VARIACIONES_PRODUCTO_EXITO = "OBTENER_VARIACIONES_PRODUCTO_EXITO";
const COLOCAR_FORMULARIO_PRODUCTO = "COLOCAR_FORMULARIO_PRODUCTO";
//variaciones con filtro
const OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO =
  "OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO";
  const OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO2 = "OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO2";
const COLOCAR_VARIACIONES_FILTRO_PRODUCTO_NULL =
  "COLOCAR_VARIACIONES_FILTRO_PRODUCTO_NULL";

  const COLOCAR_VARIACIONES_FILTRO_PRODUCTO_NULL2 =
  "COLOCAR_VARIACIONES_FILTRO_PRODUCTO_NULL2";
//reducer
const COLOCAR_KITS = "COLOCAR_KITS";
const COLOCAR_UN_KIT =  "COLOCAR_UN_KIT";
// mantener el estado de los productos
const PERSISTIR_DATOS_NUEVO_PRODUCTO = "PERSISTIR_DATOS_NUEVO_PRODUCTO";
const RESET_PERSISTIR_DATOS_NUEVO_PRODUCTO =
  "RESET_PERSISTIR_DATOS_NUEVO_PRODUCTO";

  // addCustomVariation
  const ADD_CUSTOM_VARIATION = "ADD_CUSTOM_VARIATION";
  const ADD_CUSTOM_VARIATION_NULL = "ADD_CUSTOM_VARIATION_NULL";

  const ADD_CUSTOM_VARIATION_ATTRIBUTE = "ADD_CUSTOM_VARIATION_ATTRIBUTE";

  const COLOCAR_MARKETPLACES_INFO = "COLOCAR_MARKETPLACES_INFO";

  const COLOCAR_MARKETPLACES_LOGOS = "COLOCAR_MARKETPLACES_LOGOS";

  const COLOCAR_NEW_PRODUCT_ORIGIN = "COLOCAR_NEW_PRODUCT_ORIGIN";


export default function productosReducer(state = dataInicial, action) {
  switch (action.type) {
    case CAMBIAR_MENSAJE_EXITO:
      return {
        ...state,
        mensajeAlert: action.payload.mensaje,
        colorDragDrop: action.payload.colorDragDrop,
        openModal: action.payload.openModal,
      };
    case CAMBIAR_LOADING:
      return {
        ...state,
        loading: action.payload.loading,
      };

    case COLOCAR_CANTIDADPAGINAS_NULL:
      return {
        ...state,
        cantidadDePaginas: action.payload,
      };
    case COLOCAR_PRODUCTOINDIVIDUAL_NULL:
      return {
        ...state,
        productoIndividual: action.payload,
      };

    case COLOCAR_SELECTED_PRODUCT:
      return {
        ...state,
        selectedProduct: action.payload.selectedProduct,
      };
    case OBTENER_NUMEROPRODUCTOS_EXITO:
      return {
        ...state,
        cantidadDePaginas: action.payload.cantidadDePaginas,
        productos: null,
      };

    case OBTENER_PRODUCTOINDIVIDUAL_EXITO:
      return {
        ...state,
        productoIndividual: action.payload.producto,
      };
    case OBTENER_PRODUCTOINDIVIDUALSTOCK_EXITO:
      return {
        ...state,
        productoIndividualStock: [
          ...state.productoIndividualStock,
          action.payload,
        ],
      };

    case OBTENER_PRODUCTOS_FILTRADOS_EXITO:
      return {
        ...state,
        productos: action.payload,
        esperaTortuga: false,
      };
    case COLOCAR_NULL_PRODUCTOS:
      return {
        ...state,
        productos: action.payload,
      };
    case COLOCAR_PRODUCTOS_ESPERA_NULL_PRODUCTOS:
      return {
        ...state,
        esperaCargando: action.payload,
        productos: null,
      };
    case COLOCAR_ESTADEILIMINANDOPRODUCTO:
      return {
        ...state,
        estadoEliminandoProducto: action.payload.loading,
        messageDeletingElementProduct: action.payload.messageDeletingElement,
        productos: action.payload.productos,
      };
    case COLOCAR_PLANTILLA_DESCARGADA:
      return {
        ...state,
        plantillaDescargada: action.payload,
      };
    case OBTENER_VARIACIONES_PRODUCTO_EXITO:
      return {
        ...state,
        variacionesProducto: action.payload.variacionesProducto,
      };
    case COLOCAR_FORMULARIO_PRODUCTO:
      return {
        ...state,
        variacionesProducto: action.payload,
      };
    case COLOCAR_VARIACIONES_FILTRO_PRODUCTO_NULL:
      return {
        ...state,
        productoFiltro: action.payload,
      };
      case COLOCAR_VARIACIONES_FILTRO_PRODUCTO_NULL2:
      return {
        ...state,
        productoFiltro2: action.payload,
      };
    case OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO:
      return {
        ...state,
        productoFiltro: action.payload.productoFiltro,
      };
      case OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO2:
        return {
          ...state,
          productoFiltro2: action.payload.productoFiltro2,
        };
    case PERSISTIR_DATOS_NUEVO_PRODUCTO:
      return {
        ...state,
        persevereDataNewProduct: {
          ...state.persevereDataNewProduct,
          ...(action.payload.type
            ? {
                [action.payload.type]: {
                  ...state.persevereDataNewProduct[action.payload.type],
                  [action.payload.key]: action.payload.value,
                },
              }
            : { [action.payload.key]: action.payload.value }),
        },
      };
    case RESET_PERSISTIR_DATOS_NUEVO_PRODUCTO:
      return {
        ...state,
        persevereDataNewProduct: resetPersevereDataNewProduct,
      };
    case ADD_CUSTOM_VARIATION:
      return {
        ...state,
        exitAddCustomVariation: action.payload.exitAddCustomVariation,
      };
    case ADD_CUSTOM_VARIATION_NULL:
      return {
        ...state,
        exitAddCustomVariation: null,
        existAddCustomVariationAttribute: null,
      };
    case ADD_CUSTOM_VARIATION_ATTRIBUTE:
      return {
        ...state,
        existAddCustomVariationAttribute: action.payload.existAddCustomVariationAttribute,
      };
    case COLOCAR_MARKETPLACES_INFO:
      return {
        ...state,
        marketplacesInfo: action.payload.marketplacesInfo,
      };
    case COLOCAR_MARKETPLACES_LOGOS:
      return {
        ...state,
        marketplaceLogos: action.payload,
      };

    case COLOCAR_NEW_PRODUCT_ORIGIN:
      return {
        ...state,
        newProductOrigin: action.payload.newProductOrigin,
      };
    default:
      return state;
  }
}
//acciones

export const setSelectedProduct =
  (selectedProduct) => async (dispatch, getState) => {
    dispatch({
      type: COLOCAR_SELECTED_PRODUCT,
      payload: {
        selectedProduct: selectedProduct,
      },
    });
  };

  export const setNewProductOrigin = (newProductOrigin) => async (dispatch, getState) => {
    dispatch({
      type: COLOCAR_NEW_PRODUCT_ORIGIN,
      payload: {
        newProductOrigin: newProductOrigin,
      },
    });
  };

export const enviarArchivoCargaMasiva =
  (formData, csrf_token) => async (dispatch, getState) => {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {
      let url = "/api/products/subirProductosBaseMasivo";
      const res = await instanceAxios.post(url, formData, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "multipart/form-data",
        },
      });
      if (res.status == 200) {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITO,
          payload: {
            mensaje: {
              mensaje: "Productos subidos con éxito",
              severity: "success"},
            colorDragDrop: "file-success",
          },
        });
      } else {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITO,
          payload: {
            mensaje: {
              mensaje: "Error desconocido",
              severity: "warning"},
            colorDragDrop: "file-error",
          },
        });
      }
    } catch (error) {
      console.log(error);
      console.log(error.response.data);
      var erroresParaMostrar;
      if (error.response.data.errores) {
        erroresParaMostrar = error.response.data.errores;
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: {
            mensaje: erroresParaMostrar,
            severity: "warning"},
          colorDragDrop: "file-error",
        },
      });
    }
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  };

export const darAltaProducto = (formData, csrf_token) => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    let url = "api/products/productBase/new";
    const res = await instanceAxios.post(url, formData, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "multipart/form-data",
      },
    });
    if (res.status == 200) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: {
            mensaje: res.data.mensaje,
            severity: "success"},
          openModal: true,
        },
      });
    }else {
      console.log('ñp+++++++++++++++++')
       console.log("else");
       console.log(res);
       console.log("elese");
       dispatch({
         type: CAMBIAR_MENSAJE_EXITO,
         payload: {
           mensaje: {
            mensaje: res.data.errores,
            severity: "error"},
         },
       });
     }
  } catch (error) {
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        mensaje: {
          mensaje: erroresParaMostrar,
          severity: "error"},
      },
    });
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};

export const colocarEstadoEliminandoProducto =
  (estado) => async (dispatch, getState) => {
    const productos = getState().productos.productos;
    dispatch({
      type: COLOCAR_ESTADEILIMINANDOPRODUCTO,
      payload: {
        loading: estado,
        productos: productos,
        messageDeletingElement: ""
      },
    });
  };

export const eliminarProducto =
  (skuInterno, csrf_token) => async (dispatch, getState) => {
    const productos = getState().productos.productos;
    dispatch({
      type: COLOCAR_ESTADEILIMINANDOPRODUCTO,
      payload: {
        loading: "cargando",
        productos: productos,
        messageDeletingElement: ""
      },
    })
    try {
      let url = "/api/products/productBase/eliminar";
      const res = await instanceAxios.delete(url, {
        data: {
          internalSku: skuInterno,
        },
        headers: {
          "x-csrf-token": csrf_token,
        },
      });
        const productosNuevo = productos.filter(item => item.internalBaseSku !== skuInterno)
        dispatch({
          type: COLOCAR_ESTADEILIMINANDOPRODUCTO,
          payload: {
            loading: "exito",
            productos: productosNuevo,
            messageDeletingElement: res.data.mensaje
          },
        });
    } catch (error) {
      const erroresParaMostrar = error.response.data ? error.response.data.errores : "Error desconocido"
    if (error.response.status === 404){
      dispatch({
        type: COLOCAR_ESTADEILIMINANDOPRODUCTO,
        payload: {
          loading: "404",
          productos: productos,
          messageDeletingElement: erroresParaMostrar
        },
      });
    }else if(error.response.status === 409){
      dispatch({
        type: COLOCAR_ESTADEILIMINANDOPRODUCTO,
        payload: {
          loading: "500",
          productos: productos,
          messageDeletingElement: erroresParaMostrar
        },
      });
    }else{
      dispatch({
        type: COLOCAR_ESTADEILIMINANDOPRODUCTO,
        payload: {
          loading: "500",
          productos: productos,
          messageDeletingElement: erroresParaMostrar
        },
      });
    }
    }
  };

export const actualizarProducto =
  (formData, csrf_token) => async (dispatch) => {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: true,
      },
    });
    try {
      let url = "/api/products/productBase/actualizar";
      const res = await instanceAxios.put(url, formData, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "multipart/form-data",
        },
      });
      if (res.status == 200) {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITO,
          payload: {
            mensaje: {
              mensaje: res.data.mensaje,
              severity: "success"},
          },
        });
      } else {
        dispatch({
          type: CAMBIAR_MENSAJE_EXITO,
          payload: {
            mensaje: {
              mensaje: res.data.mensaje,
              severity: "warning"},
          },
        });
      }
    } catch (error) {
      console.log(error);
      console.log(error.response.data);
      var erroresParaMostrar;
      if (error.response.data.errores) {
        erroresParaMostrar = error.response.data.errores;
      } else {
        erroresParaMostrar = "Error desconocido";
      }
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          mensaje: {
            mensaje: erroresParaMostrar,
            severity: "error"},
        },
      });
    }
    dispatch({
      type: CAMBIAR_LOADING,
      payload: {
        loading: false,
      },
    });
  };

export const obtenerTotalProductos =
  ({ search = "" } = {}) =>
  async (dispatch, getState) => {
    const cantidadDeProductosPorPagina = getState().productos.nextC;
    try {
      dispatch({
        type: COLOCAR_CANTIDADPAGINAS_NULL,
        payload: null,
      });
      let url = "/api/products/loadPageProducts";
      const res = await instanceAxios.get(url, {
        params: {
          //search: search,
        },
      });
      const numProductos = res.data.numeroProductos;
      const cantidadDePaginas = Math.ceil(
        numProductos / cantidadDeProductosPorPagina
      );
      dispatch({
        type: OBTENER_NUMEROPRODUCTOS_EXITO,
        payload: {
          cantidadDePaginas: cantidadDePaginas,
        },
      });
      dispatch({
        type: COLOCAR_MARKETPLACES_INFO,
        payload: {
          marketplacesInfo: res.data.marketplaces,
        },
      });
    } catch (error) {
      console.log(error);
      const status = error.response.status;
      dispatch({
        type: OBTENER_NUMEROPRODUCTOS_EXITO,
        payload: {
          cantidadDePaginas: "status " + status,
        },
      });
    }
  };

  export const setMarketplacesImages = (marketplaces) => async (dispatch) => {
    try {
      const imagesArr = await Promise.all(
        marketplaces.map(async (marketplace) => {
          try {
            const response = await instanceAxios.get(
              marketplace.supportedMarketplace.urlPhoto,
              { responseType: "blob" }
            );
            const blob = response.data;
            const objectUrl = URL.createObjectURL(blob);
            return {
              supportedMarketplaceId:
                marketplace.supportedMarketplace.id,
              name: marketplace.supportedMarketplace.name,
              imagen: objectUrl,
            };
          } catch (error) {
            console.log(error);
            return {
              supportedMarketplaceId:
                marketplace.supportedMarketplace.id,
              name: marketplace.supportedMarketplace.name,
              imagen: null,
            };
          }
        })
      );
      dispatch({
        type: COLOCAR_MARKETPLACES_LOGOS,
        payload: imagesArr,
      });
    } catch (error) {
      console.log(error);
      dispatch({
        type: COLOCAR_MARKETPLACES_LOGOS,
        payload: {},
      });
    }
  };

export const obtenerProductosFiltradosAccion =
  ({ offsetActual = "", search = "", scope="" } = {}) =>
  async (dispatch, getState) => {
    const cantidadDeProductosPorPagina = getState().productos.nextC;
    try {
      let url = `api/products/fuzzyProductsBaseFiltro?search=${search}&scope=${scope}&offset=${offsetActual}&next=${cantidadDeProductosPorPagina}`;

      dispatch({
        type: COLOCAR_PRODUCTOS_ESPERA_NULL_PRODUCTOS,
        payload: true,
      });
      const res = await instanceAxios.get(url);

      dispatch({
        type: OBTENER_PRODUCTOS_FILTRADOS_EXITO,
        payload: res.data.productos,
      });
    } catch (error) {
      const status = error.response.status;
      dispatch({
        type: OBTENER_PRODUCTOS_FILTRADOS_EXITO,
        payload: "status " + status,
      });
    }
  };

export const setMensaje = (mensaje) => (dispatch, getState) => {
  const colorDragDrop = getState().facturas.colorDragDrop;
  dispatch({
    type: CAMBIAR_MENSAJE_EXITO,
    payload: {
      mensaje: mensaje,
      colorDragDrop: colorDragDrop,
    },
    },
  );
};

export const obtenerProductoUsandoSKUInterno =
  (skuInterno, scope) => async (dispatch) => {
    try {
      dispatch({
        type: COLOCAR_PRODUCTOINDIVIDUAL_NULL,
        payload: null,
      });
      let url = `/api/products/productBase/${skuInterno}`;
      const res = await instanceAxios.get(url, {
        params: {
          scope: scope
        },
      });
      dispatch({
        type: OBTENER_PRODUCTOINDIVIDUAL_EXITO,
        payload: {
          producto: res.data,
        },
      });
    } catch (error) {
      console.log(error);
      const status = error.response.status;
      dispatch({
        type: OBTENER_PRODUCTOINDIVIDUAL_EXITO,
        payload: {
          producto: "status " + status,
        },
      });
    }
  };

export const obtenerPlantillaFile = () => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_PRODUCTOINDIVIDUAL_NULL,
      payload: null,
    });
    const url = `/api/products/obtenerPlantillaCargaMasiva`; //File's URL

    instanceAxios
      .get(url, { responseType: "blob" }) //Make the GET call to the API using the response type blob used for binary files
      .then((response) => {
        //Handles the response and use ".data" method to get the file data
        const url = window.URL.createObjectURL(new Blob([response.data])); //createObjectURL creates a valid URL to download the file
        const link = document.createElement("a"); //creates an element of type link in the HTML
        link.href = url; //Set the URL of the file in the link we created
        link.setAttribute("download", "Plantilla_Carga_Masiva.xlsx"); //set the attribute 'download' so the file downloads automatically when clicked and set the file's name
        document.body.appendChild(link); //adds the link to the HTML's body
        link.click(); //simulates a click on the link to trigger the file download
        link.remove(); //remove the link created before
      });

    dispatch({
      type: COLOCAR_PLANTILLA_DESCARGADA,
      payload: "descargado",
    });
  } catch (error) {
    console.log(error);
    dispatch({
      type: OBTENER_PRODUCTOINDIVIDUAL_EXITO,
      payload: "error",
    });
  }
};

export const obtenerVariacionProductos = () => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_FORMULARIO_PRODUCTO,
      payload: null,
    });
    let url = "/api/products/getVariations";
    const res = await instanceAxios.get(url);
    const variaciones = res.data.variaciones;

    dispatch({
      type: OBTENER_VARIACIONES_PRODUCTO_EXITO,
      payload: {
        variacionesProducto: variaciones,
      },
    });
  } catch (error) {
    console.log(error);
    const status = error.response.status;
    dispatch({
      type: OBTENER_VARIACIONES_PRODUCTO_EXITO,
      payload: {
        variacionesProducto: "status " + status,
      },
    });
  }
};

export const obtenerVariacionesFiltradas =
  (data = "") =>
  async (dispatch) => {
    try {
      dispatch({
        type: COLOCAR_VARIACIONES_FILTRO_PRODUCTO_NULL2,
        payload: null,
      });
      let url = `/api/products/getPossibleValuesOfVariations?variations=${data}`;
      const res = await instanceAxios.get(url);
      const productos = res.data.variacionesFiltradasConValores;
    //   if(data === ""){
    //   dispatch({
    //     type: OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO2,
    //     payload: {
    //       productoFiltro2: productos,
    //     },
    //   });
    // }else{
      dispatch({
        type: OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO,
        payload: {
          productoFiltro: productos,
        },
      });
      return productos; 
    // }
    } catch (error) {
      console.log(error);
      const status = error.response.status;
      dispatch({
        type: OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO,
        payload: {
          productoFiltro: "status " + status,
        },
      });
    }
  };

  export const obtenerAllVariacionesFiltradas =
  (data = "") =>
  async (dispatch) => {
    try {
      dispatch({
        type: COLOCAR_VARIACIONES_FILTRO_PRODUCTO_NULL2,
        payload: null,
      });
      let url = `/api/products/getPossibleValuesOfVariations?variations=${data}`;
      const res = await instanceAxios.get(url);
      const productos = res.data.variacionesFiltradasConValores;
    //   if(data === ""){
      dispatch({
        type: OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO2,
        payload: {
          productoFiltro2: productos,
        },
      });
    // }else{
      // dispatch({
      //   type: OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO,
      //   payload: {
      //     productoFiltro: productos,
      //   },
      // });
    // }
    return productos;
    } catch (error) {
      console.log(error);
      const status = error.response.status;
      dispatch({
        type: OBTENER_VARIACIONES_FILTRO_PRODUCTO_EXITO,
        payload: {
          productoFiltro2: "status " + status,
        },
      });
    }
  };

//obtener stock de productos en variaciones

export const obtenerStockProductos = (skuInterno) => async (dispatch) => {
  try {
    dispatch({
      type: COLOCAR_PRODUCTOINDIVIDUAL_NULL,
      payload: null,
    });
    let url = `/api/products/product/${skuInterno}?scope=stores`;
    const res = await instanceAxios.get(url, {
      params: {
        scope: "stock",
      },
    });
    dispatch({
      type: OBTENER_PRODUCTOINDIVIDUALSTOCK_EXITO,
      payload: {
        productoStock: res.data.product,
        sku: skuInterno,
      },
    });
  } catch (error) {
    console.log(error);
    dispatch({
      type: OBTENER_PRODUCTOINDIVIDUALSTOCK_EXITO,
      payload: {
        productoStock: "sin stock",
        sku: skuInterno,
      },
    });
  }
};

// para persistir datos de nuevo producto

export const persistirDatosNuevoProducto = (data) => async (dispatch) => {
  dispatch({
    type: PERSISTIR_DATOS_NUEVO_PRODUCTO,
    payload: data,
  });
};

export const resetPersistirDatosNuevoProducto = () => async (dispatch) => {
  dispatch({
    type: RESET_PERSISTIR_DATOS_NUEVO_PRODUCTO,
  });
};


// modificar variaciones

export const addCustomVariation = (nameVariation) => async (dispatch) => {
  try {
    dispatch({
      type: ADD_CUSTOM_VARIATION_NULL,
      payload: null,
    });
    let url = `/api/productsBase/customVariation`;
    const res = await instanceAxios.post(url, {"customVariationName": nameVariation});
    
    // if(res.status === 201) 
    dispatch({
      type: ADD_CUSTOM_VARIATION,
      payload: {
        exitAddCustomVariation: res.status,
      },
    });
  } catch (error) {
    console.log(error);
    dispatch({
      type: ADD_CUSTOM_VARIATION,
      payload: {
        // productoStock: "sin stock",
        exitAddCustomVariation: null,
      },
    });
  }
};

export const addCustomVariationAttribute = (id, nameVariation) => async (dispatch) => {
  try {
    dispatch({
      type: ADD_CUSTOM_VARIATION_NULL,
      payload: null,
    });
    let url = `/api/productsBase/customVariation/customVariationValue`;
    const res = await instanceAxios.post(url, {"customVariationId": id, "customVariationValue": nameVariation});
    
    // if(res.status === 201) 
    dispatch({
      type: ADD_CUSTOM_VARIATION_ATTRIBUTE,
      payload: {
        existAddCustomVariationAttribute: res.status,
      },
    });
  } catch (error) {
    console.log(error);
    dispatch({
      type: ADD_CUSTOM_VARIATION_ATTRIBUTE,
      payload: {
        // productoStock: "sin stock",
        existAddCustomVariationAttribute: error.response.status,
      },
    });
  }
};

