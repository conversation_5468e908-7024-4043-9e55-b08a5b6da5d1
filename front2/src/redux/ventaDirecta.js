import { tr } from "date-fns/locale";
import { instanceAxios } from "./axiosInstance";
import { PaymentOutlined } from "@mui/icons-material";
import { colocarOrdenesConsolidadas } from "./ordenesConsolidadas";
const initialState = {
  ventaDirecta: [],
  loading: false,
  message: null,
  severity: "info",
  directSales: [],
  taxes: null,
  utilityProduct: null,
  messageDelete: null,
  editOrder: null,
  messageUpdate: null,
  guia<PERSON>ontenido: null,
  guiaEspera: false,
  isLoadinSale: null,
  ventaDirectaById: null,
  estadoEliminandoVentaDirecta: null,
  messageDeletingElementDirectSale: "",

  loadingVoucher: null,
  responseVoucher: null,
  statusVoucher: null,
  voucher: null,

  cantidadDePaginas: 0,
};

const NEW_DIRECT_SALE = "NEW_DIRECT_SALE";
const CLEAN_MESSAGE = "CLEAN_MESSAGE";
const SET_DIRECT_SALES = "SET_DIRECT_SALES";
const SET_TAXES = "SET_TAXES";
const UPDATE_STATUS_DIRECT_SALE = "UPDATE_STATUS_DIRECT_SALE";
const GET_UTILITIES_PRODUCT = "GET_UTILITIES_PRODUCT";
const CLEAN_UTILITIES_PRODUCT = "CLEAN_UTILITIES_PRODUCT";
const DELETE_DIRECT_SALE = "DELETE_DIRECT_SALE";
const CLEAN_MESSAGE_DELETE = "CLEAN_MESSAGE_DELETE";
const MODIFICAR_DIRECT_SALE = "MODIFICAR_DIRECT_SALE";
const EDIT_ORDER = "EDIT_ORDER";
const UPDATE_STATUS_DIRECT_SALE_CLEAN = "UPDATE_STATUS_DIRECT_SALE_CLEAN";
const CLEAN_ORDER_iD = "CLEAN_ORDER_iD";
const COLOCAR_GUIA_ESPERA = "COLOCAR_GUIA_ESPERA";
const COLOCAR_GUIA_CONTENIDO = "COLOCAR_GUIA_CONTENIDO";
const COLOCAR_SALE_ESPERA = "COLOCAR_SALE_ESPERA";
const COLOCAR_SALE_NULL = "COLOCAR_SALE_NULL";
const OBTENER_VENTA_DIRECTA = "OBTENER_VENTA_DIRECTA";
const LODING_DIRECT_SALES = "LODING_DIRECT_SALES";
const COLOCAR_ELIMINANDO_DIRECTSALE = "COLOCAR_ELIMINANDO_DIRECTSALE"

//voucher
const LOADING_VOUCHER = "LOADING_VOUCHER";
const NEW_VOUCHER = "NEW_VOUCHER";
const CLEAN_VOUCHER = "CLEAN_VOUCHER";
const GET_VOUCHER = "GET_VOUCHER";

export default function ventaDirectaReducer(state = initialState, action) {
  switch (action.type) {
    case COLOCAR_ELIMINANDO_DIRECTSALE:
      return {
        ...state,
        estadoEliminandoVentaDirecta: action.payload.loading,
        messageDeletingElementDirectSale: action.payload.messageDeletingElement,
        directSales: action.payload.directSales
      }
    case NEW_DIRECT_SALE:
      return {
        ...state,
        message: action.payload.mensaje,
        severity: action.payload.severity
      };
    case CLEAN_MESSAGE:
      return {
        ...state,
        message: null,
        severity: "info",
      };
    case SET_DIRECT_SALES:
      return {
        ...state,
        directSales: action.payload,
      };
    case SET_TAXES:
      return {
        ...state,
        taxes: action.payload.taxes,
        cantidadDePaginas: action.payload.cantidadDePaginas
      };
    case GET_UTILITIES_PRODUCT:
      return {
        ...state,
        utilityProduct: action.payload,
      };
    case CLEAN_UTILITIES_PRODUCT:
      return {
        ...state,
        utilityProduct: null,
      };
    case DELETE_DIRECT_SALE:
      return {
        ...state,
        messageDelete: action.payload,
      };
    case CLEAN_MESSAGE_DELETE:
      return {
        ...state,
        messageDelete: null,
      };
    case MODIFICAR_DIRECT_SALE:
      return {
        ...state,
        directSales: action.payload,
      };
    case EDIT_ORDER:
      return {
        ...state,
        editOrder: action.payload,
      };
    case UPDATE_STATUS_DIRECT_SALE:
      return {
        ...state,
        messageUpdate: action.payload,
      };
    case UPDATE_STATUS_DIRECT_SALE_CLEAN:
      return {
        ...state,
        messageUpdate: null,
      };
    case CLEAN_ORDER_iD:
      return {
        ...state,
        editOrder: null,
      };
    case COLOCAR_GUIA_CONTENIDO:
      return {
        ...state,
        guiaContenido: action.payload,
        guiaEspera: false,
      };
    case COLOCAR_GUIA_ESPERA:
      return {
        ...state,
        guiaEspera: action.payload,
        guiaContenido: null,
      };
    case COLOCAR_SALE_ESPERA:
      return {
        ...state,
        isLoadinSale: action.payload,
      };
    case COLOCAR_SALE_NULL:
      return {
        ...state,
        isLoadinSale: null,
      };
    case OBTENER_VENTA_DIRECTA:
      return {
        ...state,
        ventaDirectaById: action.payload,
      };
    case LODING_DIRECT_SALES:
      return {
        ...state,
        loading: action.payload,
      };
    case LOADING_VOUCHER:
      return {
        ...state,
        loadingVoucher: action.payload,
      };
    case NEW_VOUCHER:
      if (action.payload.directSales === null || action.payload.directSales === undefined){
        return {
          ...state,
          responseVoucher: action.payload.responseVoucher,
          statusVoucher: action.payload.statusVoucher
        };
      }else{
        return {
          ...state,
          responseVoucher: action.payload.responseVoucher,
          statusVoucher: action.payload.statusVoucher,
          directSales: action.payload.directSales
        };
      }
      
    case CLEAN_VOUCHER:
      return {
        ...state,
        responseVoucher: null,
        statusVoucher: null,
        loadingVoucher: null,
        voucher: null
      };
    case GET_VOUCHER:
      return {
        ...state,
        voucher: action.payload,
      };

    default:
      return state;
  }
}

export const obtenerUnaVentaDirecta = (id) => async (dispatch) => {
  try {
    const url = `/api/directSales/getDirectSale/${id}`;
    const res = await instanceAxios.get(url);
    dispatch({
      type: OBTENER_VENTA_DIRECTA,
      payload: res.data,
    });
  }
  catch (error) {
    console.error(error);
  }
};

export const surtirVentaDirecta = (data, csrf_token) => async (dispatch) => {
  dispatch({
    type: CLEAN_MESSAGE,
  });
  try {
    const url = "/api/directSales/directSale/supplyDirectSale";
    const res = await instanceAxios.post(url, data, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "application/json",
      },
    });
    dispatch({
      type: NEW_DIRECT_SALE,
      payload: {
        mensaje: res.data.info,
        severity: "success"
      }
    });
  }
  catch (error) {
    console.error(error);
    dispatch({
      type: NEW_DIRECT_SALE,
      payload: {
        mensaje: error.response?.data?.errores
          ? error.response?.data?.errores
          : "Error desconocido",
        severity: "error"
      }
    });
  }
}


export const cleanMessage = () => async (dispatch) => {
  dispatch({
    type: CLEAN_MESSAGE,
  });
};

export const newDirectSale = (dataa, csrf_token) => async (dispatch) => {
  const api = "api/directSales/recordDirectSale";
  dispatch({
    type: CLEAN_MESSAGE,
  });
  dispatch({
    type: COLOCAR_SALE_ESPERA,
    payload: true,
  });
  try {
    // conso
    const data2 = await instanceAxios.post(api, dataa, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "application/json",
      },
    });
    dispatch({
      type: NEW_DIRECT_SALE,
      payload: {
        mensaje: "cambiado con éxito",
        severity: "success"
      }
    });
  } catch (error) {
    console.error("ACA ERROR", error.response.data.errores);
    dispatch({
      type: NEW_DIRECT_SALE,
      payload: {
        mensaje: error.response?.data?.errores
          ? error.response?.data?.errores
          : "Error desconocido",
        severity: "error"
      }
    });
  } finally {
    dispatch({
      type: COLOCAR_SALE_ESPERA,
      payload: false,
    });
  }
};

export const getDirectSale = (
  {
    orderInternalStatusId = '1-2-3-4-5-6-7',
    startDate = "",
    endDate = "",
    page = 1,
    search = "",
    next = 30,
    offset = 0
  }
) => async (dispatch) => {
  // const url = "api/directSales/getDirectSalesFiltro";
  const offset = (page - 1) * 30;
  const next =  30;
  const url = `api/directSales/getDirectSalesFiltro?orderInternalStatusId=${orderInternalStatusId}&startDate=${startDate}&endDate=${endDate}&page=${page}&search=${search}&offset=${offset}&next=${next}`;
  dispatch({
    type: CLEAN_MESSAGE,
  });
  dispatch({
    type: LODING_DIRECT_SALES,
    payload: true,
  });
  try {
    const response = await instanceAxios.get(url);
    const directSales = response.data.ventas_directas.map(
      (directSale) => {
        const { directSaleStackableComments, productsInDirectSale, ...rest } = directSale
        return {
          comments: directSaleStackableComments,
          products: productsInDirectSale,
          ...rest
        }
      }
    )
    dispatch({
      type: SET_DIRECT_SALES,
      payload: directSales,
    });
    dispatch({
      type: LODING_DIRECT_SALES,
      payload: false,
    });
  } catch (error) {
    console.error(error);
    dispatch({
      type: LODING_DIRECT_SALES,
      payload: false,
    });
  }
};

export const setSaleNull = () => async (dispatch) => {
  dispatch({
    type: COLOCAR_SALE_NULL,
  });
};

export const getTaxes = (
  orderInternalStatusId = '1-2-3-4-5-6-7',
  startDate = "",
  endDate = "",
  page = 1,
  search = "",
  next = 30,
  offset = 0
) => async (dispatch) => {
  const url = "api/directSales/loadPageNewDirectSale";
  dispatch({
    type: CLEAN_MESSAGE,
  });
  try {
    const response = await instanceAxios.get(url + `?orderInternalStatusId=${orderInternalStatusId}&startDate=${startDate}&endDate=${endDate}&page=${page}&search=${search}&offset=${offset}&next=${next}`);
    const numPedidos = response.data.loadPageNewDirectSale.numDirectSales;
    const cantidadDePaginas = Math.ceil(
      numPedidos / 30
    );
    dispatch({
      type: SET_TAXES,
      payload: {
        taxes: response.data.loadPageNewDirectSale,
        cantidadDePaginas: cantidadDePaginas
      }
    });
    // dispatch({
      
  } catch (error) {
    console.error(error);
  }
};

export const updateStatusDirectSale =
  (dataa, csrf_token) => async (dispatch) => {
    const api = "api/directSales/directSale/updateInternalStatus";
    dispatch({
      type: CLEAN_MESSAGE,
    });
    try {
      // conso
      const data2 = await instanceAxios.put(api, dataa, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });
      dispatch({
        type: NEW_DIRECT_SALE,
        payload: {
          mensaje: data2.data.message,
          severity: "success"
        }
      });
    } catch (error) {
      console.error("ACA ERROR", error.response.data.errores);
      dispatch({
        type: NEW_DIRECT_SALE,
        payload: {
          mensaje: error.response?.data?.errores
            ? error.response?.data?.errores
            : "Error desconocido",
          severity: "error"
        }
      });
    }
  };

export const getUtilitiesProduct = (product) => async (dispatch) => {
  const url = `api/directSales/productToSell/${product}`;

  dispatch({
    type: CLEAN_MESSAGE,
  });

  try {
    const response = await instanceAxios.get(url);
    dispatch({
      type: GET_UTILITIES_PRODUCT,
      payload: response.data,
    });
  } catch (error) {
    console.error(error);
  }
};

export const cleanUtilitiesProduct = () => async (dispatch) => {
  dispatch({
    type: CLEAN_UTILITIES_PRODUCT,
  });
};

export const colocarEstadoEliminandoVentaDirecta =
  (estado) => async (dispatch, getState) => {
    const directSales = getState().ventaDirecta.directSales
    dispatch({
      type: COLOCAR_ELIMINANDO_DIRECTSALE,
      payload: {
        loading: estado,
        directSales: directSales,
        messageDeletingElement: ""
      },
    });
  };

export const deleteDirectSale =
  (id, csrf_token) => async (dispatch, getState) => {
    const directSales = getState().ventaDirecta.directSales
    dispatch({
      type: COLOCAR_ELIMINANDO_DIRECTSALE,
      payload: {
        loading: "cargando",
        directSales: directSales,
        messageDeletingElement: ""
      }
    })
    try {
      const url = `/api/directSales/deleteDirectSale`;
      const res = await instanceAxios.delete(url, {
        data: {
          direct_sale_id: id,
        },
        headers: {
          "x-csrf-token": csrf_token,
        },
      });
      if (res.status == 200) {
        const newDirectSales = directSales.filter(item => item.directSaleId !== id)
        dispatch({
          type: COLOCAR_ELIMINANDO_DIRECTSALE,
          payload: {
            loading: "exito",
            directSales: newDirectSales,
            messageDeletingElement: res.data.mensaje
          },
        });
      } else {
        const newDirectSales = directSales.filter(item => item.directSaleId !== id)
        dispatch({
          type: COLOCAR_ELIMINANDO_DIRECTSALE,
          payload: {
            loading: "exito",
            directSales: newDirectSales,
            messageDeletingElement: res.data.mensaje
          },
        });
      }
    } catch (error) {
      console.log('rrrrrrrrrrrrrrrrrrrrrrrrrrrr')
      console.log(error)
      console.log('rrrrrrrrrrrrrrrrrrrrrrrrrrrr')
      const erroresParaMostrar = error.response.data ? error.response.data.errores : "Error desconocido"
      if (error.response.status === 404) {
        dispatch({
          type: COLOCAR_ELIMINANDO_DIRECTSALE,
          payload: {
            loading: "404",
            directSales: directSales,
            messageDeletingElement: erroresParaMostrar
          },
        });
      } else {
        dispatch({
          type: COLOCAR_ELIMINANDO_DIRECTSALE,
          payload: {
            loading: "500",
            directSales: directSales,
            messageDeletingElement: `code ${error.response.status.toString()}: ${erroresParaMostrar}`
          },
        });
      }

    }
  };

export const cleanMessageDelete = () => async (dispatch) => {
  dispatch({
    type: CLEAN_MESSAGE_DELETE,
  });
};

export const modificarDirectSale = (dataa) => async (dispatch) => {
  const api = "api/directSales/directSale/updateDirectSale";
  dispatch({
    type: CLEAN_MESSAGE,
  });
  dispatch({
    type: MODIFICAR_DIRECT_SALE,
    payload: dataa,
  });
};

export const getDirectSaleById = (id) => async (dispatch) => {
  const url = `api/directSales/getDirectSale/${id}`;

  dispatch({
    type: CLEAN_MESSAGE,
  });

  try {
    const response = await instanceAxios.get(url);
    dispatch({
      type: EDIT_ORDER,
      payload: response.data,
    });
  } catch (error) {
    console.error(error);
  }
};

export const updateDirectSale = (data, csrf_token) => async (dispatch) => {
  const api = "/api/directSales/updateDirectSale";
  dispatch({
    type: CLEAN_MESSAGE,
  });

  try {
    // conso
    const data2 = await instanceAxios.put(api, data, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "application/json",
      },
    });
    dispatch({
      type: UPDATE_STATUS_DIRECT_SALE,
      payload: "EXITOOOO",
    });
  } catch (error) {
    console.error("ACA ERROR", error.response.data.errores);
    dispatch({
      type: UPDATE_STATUS_DIRECT_SALE,
      payload: error.response?.data?.errores
        ? error.response?.data?.errores
        : "Error desconocido",
    });
  }
};

export const cleanMessageUpdate = () => async (dispatch) => {

  dispatch({
    type: UPDATE_STATUS_DIRECT_SALE_CLEAN,
  });
};

export const cleanOrderId = () => async (dispatch) => {
  dispatch({
    type: CLEAN_ORDER_iD,
  });
};

export const imprimirGuiaDeUnaVenta =
  (orderId, csrf_token) => async (dispatch, getState) => {
    dispatch({
      type: COLOCAR_GUIA_ESPERA,
      payload: true,
    });
    try {
      let url = "/api/directSales/direcSale/getpdfdoc/" + orderId;
      const res = await instanceAxios.get(url, { responseType: "arraybuffer" });
      if (res.status === 200) {
        const blob = new Blob([res.data], { type: "application/pdf" });
        const pdfUrl = URL.createObjectURL(blob);
        // const a = document.createElement("a");
        // a.style.display = "none";
        // a.href = pdfUrl;
        // a.download = "nombreArchivo.pdf";

        // // Agregar el elemento al cuerpo del documento y hacer clic en él
        // document.body.appendChild(a);
        // a.click();

        // // Limpiar y remover el objeto URL y el elemento <a>
        // document.body.removeChild(a);
        // URL.revokeObjectURL(pdfUrl);
        // window.open(pdfUrl);
        dispatch({
          type: COLOCAR_GUIA_CONTENIDO,
          payload: pdfUrl,
        });
      } else {
        // Manejar errores de la petición
        console.error("Error en la petición");
        dispatch({
          type: COLOCAR_GUIA_CONTENIDO,
          payload: null,
        });
      }
    } catch (error) {
      console.error("Error en la petición", error);
      dispatch({
        type: COLOCAR_GUIA_CONTENIDO,
        payload: null,
      });
    }
  };

//voucher    
export const newVoucher = (data, id, from="consolidado",csrf_token) => async (dispatch, getState) => {
  const sales = from === "ventaDirecta" ? getState().ventaDirecta.directSales : getState().ordenesConsolidadas.ordenesConsolidadas
  dispatch({
    type: LOADING_VOUCHER,
    payload: true,
  });
  try {
    const url = `/api/directSales/registerPayment/${id}`
    const res = await instanceAxios.post(url, data, {
      headers: {
        // "x-csrf-token": csrf_token,
        "Content-Type": "multipart/form-data",
      },
    });
    const directSaleInternalStatuses = getState().ventaDirecta.taxes.directSaleInternalStatuses
    const cerradaStatus = directSaleInternalStatuses.find((directSaleInternalStatus => directSaleInternalStatus.directSaleInternalStatus === "Cerrada(Pago verificado)"))
    const newSales = sales.map((sale) => {
      const saleId = from==="consolidado" ? sale.id : sale.directSaleId
      if(saleId === id){
        console.log('iiifffffffffffffff..')
        return {...sale, directSaleInternalStatus: cerradaStatus, directSaleInternalStatusId: cerradaStatus.directSaleInternalStatusId, internalStatusId:cerradaStatus.directSaleInternalStatusId}
      }else{
        return sale
        }
    })
    if(from==="consolidado"){
      dispatch(colocarOrdenesConsolidadas(newSales))
      dispatch({
        type: NEW_VOUCHER,
        payload: {
          responseVoucher: 'vaucher registrado con exito',
          statusVoucher: 'success',
        }
      });
      
    }else{
      dispatch({
        type: NEW_VOUCHER,
        payload: {
          responseVoucher: 'vaucher registrado con exito',
          statusVoucher: 'success',
          directSales: newSales
        }
      });
    }
   
  } catch (error) {
    console.error(error);
    dispatch({
      type: NEW_VOUCHER,
      payload: {
        responseVoucher: error.response?.data?.errores
          ? error.response?.data?.errores
          : "Error desconocido",
        statusVoucher: 'error',
      }
    });
  } finally {
    dispatch({
      type: LOADING_VOUCHER,
      payload: false,
    });
  }
}

export const cleanVoucher = () => async (dispatch) => {
  dispatch({
    type: CLEAN_VOUCHER,
    payload: null,
  });
}

export const getVoucher = (id) => async (dispatch) => {
  const url = `/api/directSales/proofPayment/${id}`;

  dispatch({
    type: CLEAN_MESSAGE,
  });
  dispatch({
    type: LOADING_VOUCHER,
    payload: true,
  });

  try {
    const response = await instanceAxios.get(url);
    dispatch({
      type: GET_VOUCHER,
      payload: response.data,
    });
  } catch (error) {
    console.error(error);
  } finally {
    dispatch({
      type: LOADING_VOUCHER,
      payload: false,
    });
  }
};