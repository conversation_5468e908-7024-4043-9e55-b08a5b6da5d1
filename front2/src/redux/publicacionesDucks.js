import { instanceAxios } from "./axiosInstance";
//Data inicial

const dataInicial = {
    loading:false,
    mensaje:null,
    publicaciones: null,
    severity:'info',
  };

    const CAMBIAR_MENSAJE = 'CAMBIAR_MENSAJE'
    const CAMBIAR_LOADING = 'CAMBIAR_LOADING'
    const COLOCAR_PUBLICACIONES = 'COLOCAR_PUBLICACIONES'


export default function publicationsReducer(state = dataInicial, action) {
    switch (action.type) {
        case CAMBIAR_MENSAJE:
        return {
            ...state,
            mensaje: action.payload.mensaje,
            severity: action.payload.severity,
        }
        case CAMBIAR_LOADING:
        return {
            ...state,
            loading: action.payload.loading,
        }
        case COLOCAR_PUBLICACIONES:
            return {
            ...state,
            publicaciones: action.payload,
        }
        default:
        return state;
    }
}

export const createPublication = (data, csrf_token) => async (dispatch, getState) => {
    dispatch({
        type: CAMBIAR_LOADING,
        payload: {
            loading: true,
        },
    });
    try {
        const response = await instanceAxios.post(
            '/api/postNewProducts/postProductByMarketAndInternalSku',
            data,
            {
                headers: {
                    'X-CSRFToken': csrf_token,
                },
            }
        );
        if (response.status === 200) {
            dispatch({
                type: CAMBIAR_MENSAJE,
                payload: {
                    mensaje: response.data.message,
                    severity: 'success',
                },
            });
        }
    } catch (error) {
        dispatch({
            type: CAMBIAR_MENSAJE,
            payload: {
                mensaje: error.response.data.message,
                severity: 'error',
            },
        });
    }
    dispatch({
        type: CAMBIAR_LOADING,
        payload: {
            loading: false,
        },
    });
}

