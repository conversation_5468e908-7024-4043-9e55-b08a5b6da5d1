import { instanceAxios } from "./axiosInstance";

//Data inicial
const dataInicial = {
  loading: false,
  mensaje: null,
  severity: "info",

  esperaTortuga: false,
  proveedores: [],

  estadoEliminandoProveedor: null,
  mensajeEliminando: null,
};

const CAMBIAR_MENSAJE_EXITO_PROVEEDORES = "CAMBIAR_MENSAJE_EXITO_PROVEEDORES";
const LIMPIAR_MENSAJE_EXITO_PROVEEDORES = "LIMPIAR_MENSAJE_EXITO_PROVEEDORES";
const CAMBIAR_LOADING = "CAMBIAR_LOADING";
const TORTUGA = "TORTUGA";
const COLOCAR_ESTADO_ELIMINANDOPROVEEDOR = "COLOCAR_ESTADO_ELIMINANDOPROVEEDOR";
const COLOCAR_PROVEEDORES = "COLOCAR_PROVEEDORES";

export default function proveedoresReducer(state = dataInicial, action) {
  switch (action.type) {
    case CAMBIAR_MENSAJE_EXITO_PROVEEDORES:
      return {
        ...state,
        mensaje: action.payload.mensaje,
        severity: action.payload.severity,
      };
    case LIMPIAR_MENSAJE_EXITO_PROVEEDORES:
      return {
        ...state,
        mensaje: null,
        severity: "info",
      };
    case CAMBIAR_LOADING:
      return {
        ...state,
        loading: action.payload.loading,
      };

    case TORTUGA:
      return {
        ...state,
        productos: action.payload,
        esperaTortuga: false,
      };
    case COLOCAR_ESTADO_ELIMINANDOPROVEEDOR:
      return {
        ...state,
        estadoEliminandoProveedor: action.payload.loading,
        productos: action.payload.productos,
      };
    case COLOCAR_PROVEEDORES:
      return {
        ...state,
        proveedores: action.payload.proveedores,
      };
    default:
      return state;
  }
}

export const limpiarMensajeProveedor = () => async (dispatch) => {
  dispatch({
    type: LIMPIAR_MENSAJE_EXITO_PROVEEDORES,
  });
};

export const darAltaProveedor = (formData, csrf_token) => async (dispatch) => {
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: true,
    },
  });
  try {
    let url = "/api/suppliers/supplier/new";
    const obj = {};
    formData.forEach((value, key) => (obj[key] = value));
    const objeto = JSON.stringify(obj);
    const res = await instanceAxios.post(url, objeto, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "application/json",
      },
    });
    if (res.status === 200) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_PROVEEDORES,
        payload: {
          mensaje: res.data,
          severity: "success",
        },
      });
    } else {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_PROVEEDORES,
        payload: {
          mensaje: res.data,
          severity: "warning",
        },
      });
    }
    return true;
  } catch (error) {
    console.log(error);
    console.log(error.response.data);
    var erroresParaMostrar;
    if (error.response.data || error.response.data.errores) {
      erroresParaMostrar = error.response.data || error.response.data.errores;
    } else {
      erroresParaMostrar = " Error al guardar el proveedor";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO_PROVEEDORES,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
    return false;
  }
  dispatch({
    type: CAMBIAR_LOADING,
    payload: {
      loading: false,
    },
  });
};

export const obtenerProveedores = () => async (dispatch, getState) => {
  try {
    let url = "/api/suppliers/getSuppliers";
    const res = await instanceAxios.get(url);
    if (res.status === 200) {
      dispatch({
        type: COLOCAR_PROVEEDORES,
        payload: {
          proveedores: res.data.proveedores,
        },
      });
    } else {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_PROVEEDORES,
        payload: {
          mensaje: res.statusText,
          severity: "warning",
        },
      });
    }
  } catch (error) {
    console.log(error);
    console.log(error.response.data);
    var erroresParaMostrar;
    if (error.response.data.errores) {
      erroresParaMostrar = error.response.data.errores;
    } else {
      erroresParaMostrar = "Error desconocido";
    }
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO_PROVEEDORES,
      payload: {
        mensaje: erroresParaMostrar,
        severity: "error",
      },
    });
  }
};
