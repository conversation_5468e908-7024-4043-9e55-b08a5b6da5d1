import { instanceAxios } from "./axiosInstance";
//Data inicial

const dataInicial = {
    loading:false,
    kits: null,
    kitIndividual:null,
    mensaje:null,
    severity:'info',
  };

    const CAMBIAR_MENSAJE = 'CAMBIAR_MENSAJE'
    const CAMBIAR_LOADING = 'CAMBIAR_LOADING'
    const COLOCAR_KITS = 'COLOCAR_KITS'
    const COLOCAR_UN_KIT = 'COLOCAR_UN_KIT'


export default function kitsReducer(state = dataInicial, action) {
    switch (action.type) {
        case CAMBIAR_MENSAJE:
        return {
            ...state,
            mensaje: action.payload.mensaje,
            severity: action.payload.severity,
        }
        case CAMBIAR_LOADING:
        return {
            ...state,
            loading: action.payload.loading,
        };
        
        case COLOCAR_KITS:
            return {
            ...state,
            kits: action.payload,
        };
        case COLOCAR_UN_KIT:
            return{
            ...state,
            kitIndividual: action.payload,
        };
        default:
        return state;
    }
}

    export const createKit = (data) => async (dispatch, getState) => {
        dispatch({
            type: CAMBIAR_LOADING,
            payload: {
                loading: true,
            },
        });
        try {
            const response = await instanceAxios.post('api/kits/kit', data);
            if (response.status === 200) {
                dispatch({
                    type: CAMBIAR_MENSAJE,
                    payload: {
                        mensaje: response.data.mensaje,
                        severity: 'success',
                    },
                });
                dispatch({
                    type: CAMBIAR_LOADING,
                    payload: {
                        loading: false,
                    },
                });
            }
        } catch (error) {
            dispatch({
                type: CAMBIAR_MENSAJE,
                payload: {
                    mensaje: error.response.data.errores,
                    severity: 'error',
                },
            });
            dispatch({
                type: CAMBIAR_LOADING,
                payload: {
                    loading: false,
                },
            });
        }
    }

    export const obtenerUnKit = (id) => async(dispatch)=>{
        dispatch({
        type: CAMBIAR_LOADING,
        payload: true,
        });
        try{
        let url = `/api/kits/kit/${id}`
        const res = await instanceAxios.get(url);
        if (res.status == 200){
            dispatch({
            type: COLOCAR_UN_KIT,
            payload: res.data,
            });
        } 
        } catch (error) {
        const status = error.response.status;
        if (status == 404){
            dispatch({
            type: COLOCAR_UN_KIT,
            payload: 'notFound'
            })
        }else{
            dispatch({
            type: COLOCAR_UN_KIT,
            payload: "status " + status,
            });
        }     
        dispatch({
            type: CAMBIAR_MENSAJE,
            payload: {
            mensaje: "Error al obtener el kit",
            severity: "error",
            },
        });
        }
        dispatch({
        type: CAMBIAR_LOADING,
        payload: false,
        });
    }

    export const deleteKit = (id) => async(dispatch,getState)=>{
        dispatch({
        type: CAMBIAR_LOADING,
        payload: true,
        });
        try{
        let url = `/api/kits/kit`
        const jsonToSend = {
            kit_id: id
        }
        const res = await instanceAxios.delete(url, {data: jsonToSend});
        const newKits = getState().kits.kits.filter((kit) => kit.id !== id);
        if (res.status === 200){
            dispatch({
            type: COLOCAR_KITS,
            payload: newKits,
            });
            dispatch({
            type: CAMBIAR_MENSAJE,
            payload: {
                mensaje: res.data.mensaje,
                severity: "success",
            },
            });
        }
        } catch (error) {
        console.log(error);
        dispatch({
            type: CAMBIAR_MENSAJE,
            payload: {
            mensaje: "Error al eliminar el kit",
            severity: "error",
            },
        });
        }
        dispatch({
        type: CAMBIAR_LOADING,
        payload: false,
        });
    }

    export const updateKit = (data) => async (dispatch, getState) => {
        dispatch({
        type: CAMBIAR_LOADING,
        payload: true,
        });
        try {
            const response = await instanceAxios.put('api/kits/kit', data);
            if(response.status === 200){
                dispatch({
                type: CAMBIAR_MENSAJE,
                payload: {
                    mensaje: response.data.mensaje,
                    severity: "success",
                },
                });
            }
        } catch (error) {
            console.log(error);
            dispatch({
                type: CAMBIAR_MENSAJE,
                payload: {
                mensaje: "Error al actualizar el kit",
                severity: "error",
                },
            });
        }
        dispatch({
            type: CAMBIAR_LOADING,
            payload: false,
        });
    }


    export const obtenerKits = () => async (dispatch) => {
        dispatch({
        type: CAMBIAR_LOADING,
        payload: true,
        });
        try {
        let url = `/api/kits/filtered_kits`;
        const res = await instanceAxios.get(url);
        if (res.status == 200) {
            dispatch({
            type: COLOCAR_KITS,
            payload: res.data.kits,
            });
        } else {
            dispatch({
            type: COLOCAR_KITS,
            payload: "status " + res.status,
            });
            dispatch({
            type: CAMBIAR_MENSAJE,
            payload: {
                mensaje: "Error al obtener los kits",
                severity: "error",
            },
            });
        }
        } catch (error) {
        const status = error.response.status;
        dispatch({
            type: COLOCAR_KITS,
            payload: "status " + status,
        });
        dispatch({
            type: CAMBIAR_MENSAJE,
            payload: {
            mensaje: "Error al obtener los kits",
            severity: "error",
            },
        });
        }
        dispatch({
        type: CAMBIAR_LOADING,
        payload: false,
        });
    };

    export const resetMessage = () => (dispatch) => {
        dispatch({
        type: CAMBIAR_MENSAJE,
        payload: {
            mensaje: null,
            severity: "info",
        },
        });
    };