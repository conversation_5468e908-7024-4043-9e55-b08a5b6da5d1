// import { AxiosInstance } from "axios";
import { cs } from "date-fns/locale";
import { GroupsMarketplace } from "../components/configuracionComponentes/marketplaces/GroupsMarketplace";
import { instanceAxios } from "./axiosInstance";

const dataInicial = {
  image: null,
  error: null,
  loading: false,
  loadingSetMarketplace: false,
  loadingGetMarketplaceSupported: false,
  // obteniendo internal status
  loadingGetIternalStatus: false,
  mensaje: null,
  mensajeReOrder: null,
  supportedMarketplaces: [],
  registerMarketplaces: [],
  changeStatusMarketplace: false,
  // loadin al borrar un marketplace
  loadingDeleteMarketplace: false,
  // marketplace con credenciales
  marketplaceWithCredentials: [],
  //  venta directa internal status
  directSaleInternalStatus: [],
  directSaleInternalStatusOrders: [],
  // nueva venta directa
  newDirectSale: [],
  // check if the credentials are correct
  checkCredentials: null,
  // status to check if the credentials are incorrect
  status: null,
  // isEditFalse para saber si se edita un
  //  marketplace y no son las credenciales correctas
  // este se pone en true
  isEditFalse: false,
  // isEditComplete para saber si se edito un marketplace y este fue exitoso
  isEditComplete: false,

  // para las nuevas credenciales
  groupsMarketplace: [],
  loadingGroupsMarketplace: false,
  errorGroups: null,
};

const CAMBIAR_IMAGEN = "CAMBIAR_IMAGEN";
const CAMBIAR_ERROR = "CAMBIAR_ERROR";
const CAMBIAR_LOADING = "CAMBIAR_LOADING";
const CAMBIAR_MENSAJE_EXITO = "CAMBIAR_MENSAJE_EXITO";
const CAMBIAR_MENSAJE_EXITO_REORDER = "CAMBIAR_MENSAJE_EXITO_REORDER";
const CAMBIAR_MENSAJE_NULL = "CAMBIAR_MENSAJE_NULL";
const CAMBIAR_SUPPORTED_MARKETPLACES = "CAMBIAR_SUPPORTED_MARKETPLACES";
const CAMBIAR_MARKETPLACES_REGISTER = "CAMBIAR_MARKETPLACES_REGISTER";
const CAMBIAR_LOADING_GET_MARKETPLACE_SUPPORTED =
  "CAMBIAR_LOADING_GET_MARKETPLACE_SUPPORTED";
const CAMBIAR_LOADING_SET_MARKETPLACE = "CAMBIAR_LOADING_SET_MARKETPLACE";
const CAMBIAR_LOADING_GET_MARKETPLACE_REGISTERED =
  "CAMBIAR_LOADING_GET_MARKETPLACE_REGISTERED";
const CAMBIAR_LOADING_DELETE_MARKETPLACE = "CAMBIAR_LOADING_DELETE_MARKETPLACE";
const ESTABLECER_MARKETPLACES_REGISTRADOS_CRENDENCIALES =
  "ESTABLECER_MARKETPLACES_REGISTRADOS_CRENDENCIALES";
const ESTABLECER_STATUS_INTERNO_VENTA_DIRECTA =
  "ESTABLECER_STATUS_INTERNO_VENTA_DIRECTA";
const ESTABLECER_STATUS_INTERNO = "ESTABLECER_STATUS_INTERNO";
const CAMBIAR_LOADING_GET_INTERNAL_STATUS =
  "CAMBIAR_LOADING_GET_INTERNAL_STATUS";
const ESTABLECER_NEW_TAXES_VENTA_DIRECTA = "ESTABLECER_NEW_TAXES_VENTA_DIRECTA";
// check if the credentials are correct
const CAMBIAR_CHECK_CREDENTIALS = "CAMBIAR_CHECK_CREDENTIALS";
const EXITO_EDIT = "EXITO_EDIT";

// nuevas credenciales
const CAMBIAR_GROUPS_MARKETPLACE = "CAMBIAR_GROUPS_MARKETPLACE";
const CAMBIAR_LOADING_GROUPS_MARKETPLACE = "CAMBIAR_LOADING_GROUPS_MARKETPLACE";
const CAMBIAR_ERROR_GROUPS_MARKETPLACE = "CAMBIAR_ERROR_GROUPS_MARKETPLACE";


export default function configuracionReducer(state = dataInicial, action) {
  switch (action.type) {
    case CAMBIAR_IMAGEN:
      return { ...state, image: action.payload };
    case CAMBIAR_ERROR:
      return { ...state, error: action.payload };
    case CAMBIAR_LOADING:
      return { ...state, loading: action.payload };
    case CAMBIAR_MENSAJE_EXITO:
      return {
        ...state,
        mensaje: action.payload.data,
        checkCredentials: action.payload.checkCredentials,
        status: action.payload.status,
      };
    case CAMBIAR_MENSAJE_EXITO_REORDER:
      return { ...state, mensajeReOrder: action.payload };
    case CAMBIAR_MENSAJE_NULL:
      return { ...state, mensaje: null };
    case CAMBIAR_SUPPORTED_MARKETPLACES:
      return { ...state, supportedMarketplaces: action.payload };
    case CAMBIAR_MARKETPLACES_REGISTER:
      return {
        ...state,
        registerMarketplaces: action.payload.data,
        isEditFalse: action.payload.isEditFalse,
      };
    case CAMBIAR_LOADING_GET_MARKETPLACE_SUPPORTED:
      return { ...state, loadingGetMarketplaceSupported: action.payload };
    case CAMBIAR_LOADING_GET_INTERNAL_STATUS:
      return { ...state, loadingGetIternalStatus: action.payload };
    case CAMBIAR_LOADING_SET_MARKETPLACE:
      return { ...state, loadingSetMarketplace: action.payload };
    case CAMBIAR_LOADING_GET_MARKETPLACE_REGISTERED:
      return { ...state, loadingGetMarketplaceRegistered: action.payload };
    case CAMBIAR_LOADING_DELETE_MARKETPLACE:
      return { ...state, loadingDeleteMarketplace: action.payload };
    case ESTABLECER_MARKETPLACES_REGISTRADOS_CRENDENCIALES:
      return { ...state, marketplaceWithCredentials: action.payload };
    case ESTABLECER_STATUS_INTERNO_VENTA_DIRECTA:
      return { ...state, directSaleInternalStatus: action.payload };
    case ESTABLECER_NEW_TAXES_VENTA_DIRECTA:
      return { ...state, newDirectSale: action.payload };
    case ESTABLECER_STATUS_INTERNO:
      return { ...state, directSaleInternalStatusOrders: action.payload };
    // check if the credentials are correct
    case CAMBIAR_CHECK_CREDENTIALS:
      return { ...state, checkCredentials: null, mensaje: null, status: null };
    case EXITO_EDIT:
      return { ...state, isEditComplete: action.payload.isEditComplete };
    case CAMBIAR_GROUPS_MARKETPLACE:
      return { ...state, groupsMarketplace: action.payload };
    case CAMBIAR_LOADING_GROUPS_MARKETPLACE:
      return { ...state, loadingGroupsMarketplace: action.payload };
    case CAMBIAR_ERROR_GROUPS_MARKETPLACE:
      return { ...state, errorGroups: action.payload };
    default:
      return state;

    }
  }
export const cambiarImagenAccion =
  (imagen, csrf_token) => async (dispatch, getState) => {
    const url = "api/company/newLoginPhoto";
    dispatch({
      type: CAMBIAR_LOADING,
      payload: true,
    });
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: { data: "Failed" },
    });
    try {
      const data = await instanceAxios.post(url, imagen, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "multipart/form-data",
        },
      });
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: data.statusText },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: error.response },
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING,
        payload: false,
      });
    }
  };

export const getImageLogin = (csrf_token) => async (dispatch, getState) => {
  const url = "/api/getLoginPhoto";
  dispatch({
    type: CAMBIAR_LOADING,
    payload: true,
  });

  try {
    const data = await instanceAxios.get(url);
    dispatch({
      type: CAMBIAR_IMAGEN,
      payload: data.data,
    });
  } catch (error) {
    dispatch({
      type: CAMBIAR_ERROR,
      payload: error.response,
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: false,
    });
  }
};

export const getMarketplaceSupported = () => async (dispatch, getState) => {
  const url = "api/marketplaces/getSupportedMarketplacesByTurtle";

  dispatch({
    type: CAMBIAR_LOADING_GET_MARKETPLACE_SUPPORTED,
    payload: true,
  });

  try {
    const data = await instanceAxios.get(url);
    dispatch({
      type: CAMBIAR_SUPPORTED_MARKETPLACES,
      payload: data.data,
    });
  } catch (error) {
    dispatch({
      type: CAMBIAR_ERROR,
      payload: error.response,
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING_GET_MARKETPLACE_SUPPORTED,
      payload: false,
    });
  }
};

export const setMarketplaceSupported =
  (marketplaces, csrf_token) => async (dispatch, getState) => {
    const url = "api/marketplaces/registrarMarketplace";
    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: true,
    });

    try {
      const data = await instanceAxios.post(url, marketplaces, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: data.data.mensaje, checkCredentials: true },
      });
      // se agrega el marketplace a la lista de marketplaces registrados
      getState().ConfiguracionSistema.registerMarketplaces.marketplaces.push({
        commentUpdate: null,
        lastUpdate: "None",
        marketplaceId: marketplaces.id
      })

      getState().ConfiguracionSistema.marketplaceWithCredentials.marketplaces_with_credentials.push({
        commentUpdate: null,
        lastUpdate: "None",
        marketplaceId: marketplaces.id,
        marketplaceCredentials: {
          check: true,
          accessToken: null,
          refreshToken: null,
          status: "Credenciales correctas",
          clientId: marketplaces.credentials.clientId,
          clientSecret: marketplaces.credentials.clientSecret,
        },
        supportedMarketplace: {
          supportedMarketplaceId: marketplaces.id,
          supportedMarketplaceName: "",
          urlPhoto: ""
        }
      })

      // dispatch({
      //   type: EXITO_EDIT,
      //   payload: { isEditComplete: true },
      // })
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          data: error.response.data.errores,
          checkCredentials: false,
          status: error.response.status,
        },
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_SET_MARKETPLACE,
        payload: false,
      });
    }
  };

export const getMarketplacesRegister = () => async (dispatch, getState) => {
  const url = "api/marketplaces/getRegisteredMarketplaces";

  dispatch({
    type: CAMBIAR_LOADING_GET_MARKETPLACE_REGISTERED,
    payload: true,
  });

  try {
    const result = await instanceAxios.get(url);
    dispatch({
      type: CAMBIAR_MARKETPLACES_REGISTER,
      payload: { data: result.data },
    });
  } catch (error) {
    dispatch({
      type: CAMBIAR_ERROR,
      payload: error.response,
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING,
      payload: false,
    });
  }
};

export const deleteMarketplace =
  (marketplaceId, csrf_token, isEditFalse) => async (dispatch, getState) => {
    const url = "api/marketplaces/deleteRegisteredMarketplace";

    dispatch({
      type: CAMBIAR_LOADING_DELETE_MARKETPLACE,
      payload: true,
    });

    try {
      const data = await instanceAxios.delete(url, {
        headers: {
          "x-csrf-token": csrf_token,
        },
        data: {
          id: marketplaceId,
        },
      });
      dispatch({
        type: CAMBIAR_MARKETPLACES_REGISTER,
        payload: { data: data.data, isEditFalse: isEditFalse },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_ERROR,
        payload: error.response,
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_DELETE_MARKETPLACE,
        payload: false,
      });
    }
  };

export const getMarketplaceWithCredentials =
  () => async (dispatch, getState) => {
    const url = "api/marketplaces/getRegisteredMarketplacesWithCredentials";

    dispatch({
      type: CAMBIAR_LOADING,
      payload: true,
    });

    try {
      const data = await instanceAxios.get(url);
      dispatch({
        type: ESTABLECER_MARKETPLACES_REGISTRADOS_CRENDENCIALES,
        payload: data.data,
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_ERROR,
        payload: error.response,
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING,
        payload: false,
      });
    }
  };

export const updateMarketplaceWithCredentials =
  (marketplaces, csrf_token) => async (dispatch, getState) => {
    const url = "api/marketplaces/actualizarMarketplace";
    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: true,
    });

    try {
      const data = await instanceAxios.put(url, marketplaces, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });
      getState().ConfiguracionSistema.marketplaceWithCredentials.marketplaces_with_credentials.map(market => {
        if (market.marketplaceId === marketplaces.id) {
          market.marketplaceCredentials = {
            check: true,
            accessToken: null,
            refreshToken: null,
            status: "Credenciales correctas",
            clientId: marketplaces.credentials.clientId,
            clientSecret: marketplaces.credentials.clientSecret,
          }
        }
      })

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: data.data.mensaje, checkCredentials: true },
      });
      dispatch(
        {
          type: EXITO_EDIT,
          payload: { isEditComplete: true },
        }
      )
    } catch (error) {
      if (error.response.status) {
        // si se edita un market y este no tiene credenciales
        // se elimina y trae los marketplaces con credenciales
        dispatch(deleteMarketplace(marketplaces.id, csrf_token, true));
      }

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: {
          data: error.response.data.errores,
          checkCredentials: false,
          status: error.response.status,
        },
      });
      dispatch(
        {
          type: EXITO_EDIT,
          payload: { isEditComplete: true },
        }
      )
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_SET_MARKETPLACE,
        payload: false,
      });
    }
  };

export const cleanEditComplete = () => async (dispatch) => {
  dispatch({
    type: EXITO_EDIT,
    payload: { isEditComplete: false },
  });
};

// function to clean variable to check if the credentials are correct
export const cleanCheck = () => async (dispatch) => {
  dispatch({
    type: CAMBIAR_CHECK_CREDENTIALS,
    payload: null,
  });
};

// end points para status interno de venta directa

// Debes recibir 'dispatch' como argumento en la función
export const getInternalStatusDirectSale = () => async (dispatch) => {
  const url = "/api/directSales/internalStatuses/getInternalStatuses";

  dispatch({
    type: CAMBIAR_LOADING_GET_MARKETPLACE_SUPPORTED,
    payload: true,
  });

  try {
    const data = await instanceAxios.get(url);
    dispatch({
      type: ESTABLECER_STATUS_INTERNO_VENTA_DIRECTA,
      payload: data.data,
    });
  } catch (error) {
    dispatch({
      type: CAMBIAR_ERROR,
      payload: error.response,
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING_GET_MARKETPLACE_SUPPORTED,
      payload: false,
    });
  }
};
export const getNewDirectSale = () => async (dispatch) => {
  const url = "api/directSales/loadPageNewDirectSale";

  dispatch({
    type: CAMBIAR_LOADING_GET_MARKETPLACE_SUPPORTED,
    payload: true,
  });

  try {
    const data = await instanceAxios.get(url);
    dispatch({
      type: ESTABLECER_NEW_TAXES_VENTA_DIRECTA,
      payload: data.data,
    });
  } catch (error) {
    dispatch({
      type: CAMBIAR_ERROR,
      payload: error.response,
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING_GET_MARKETPLACE_SUPPORTED,
      payload: false,
    });
  }
};

export const reOrderInternalStatusDirectSale =
  (order, csrf_token) => async (dispatch, getState) => {
    const url = "/api/directSales/internalStatuses/reOrder";
    // dispatch({
    //     type: CAMBIAR_LOADING_SET_MARKETPLACE,
    //     payload: true
    // })
    // dispatch({
    //     type: CAMBIAR_MENSAJE_NULL
    // })

    try {
      const data = await instanceAxios.put(url, order, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_REORDER,
        payload: { data: data.status },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_REORDER,
        payload: { data: error.response },
      });
    }
    // finally{
    //     dispatch({
    //         type: CAMBIAR_LOADING_SET_MARKETPLACE,
    //         payload: false
    //     })
    // }
  };
export const updateInternalStatusDirectSale =
  (params, csrf_token) => async (dispatch, getState) => {
    const url = "/api/directSales/internalStatuses/renameInternalStatus";
    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: true,
    });
    dispatch({
      type: CAMBIAR_MENSAJE_NULL,
    });

    try {
      const data = await instanceAxios.put(url, params, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: data.status },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: error.response },
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_SET_MARKETPLACE,
        payload: false,
      });
    }
  };

export const deleteInternalStatusDirectSale =
  (statusId, csrf_token) => async (dispatch, getState) => {
    const url = "/api/directSales/internalStatuses/deleteInternalStatus";

    dispatch({
      type: CAMBIAR_LOADING_DELETE_MARKETPLACE,
      payload: true,
    });

    try {
      const data = await instanceAxios.delete(url, {
        headers: {
          "x-csrf-token": csrf_token,
        },
        data: {
          id: statusId,
        },
      });
      dispatch({
        type: CAMBIAR_MARKETPLACES_REGISTER,
        payload: { data: data.data },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_ERROR,
        payload: error.response,
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_DELETE_MARKETPLACE,
        payload: false,
      });
    }
  };

export const setNewInternalStatus =
  (status, csrf_token) => async (dispatch, getState) => {
    const url = "/api/directSales/internalStatuses/createInternalStatus";
    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: true,
    });

    dispatch({
      type: CAMBIAR_MENSAJE_NULL,
    });

    try {
      const data = await instanceAxios.post(url, status, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: data.status },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: error.response },
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_SET_MARKETPLACE,
        payload: false,
      });
    }
  };

// --------------------------  END POINTS PARA STATUS Interno ------------------------------

export const getInternalStatus = () => async (dispatch) => {
  const url =
    "api/orders/internalStatuses/getInternalStatusTypesWithInternalStatus";

  dispatch({
    type: CAMBIAR_LOADING_GET_INTERNAL_STATUS,
    payload: true,
  });

  try {
    const data = await instanceAxios.get(url);
    dispatch({
      type: ESTABLECER_STATUS_INTERNO,
      payload: data.data,
    });
  } catch (error) {
    dispatch({
      type: CAMBIAR_ERROR,
      payload: error.response,
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING_GET_INTERNAL_STATUS,
      payload: false,
    });
  }
};

export const updateInternalStatus =
  (params, csrf_token) => async (dispatch, getState) => {
    const url = "/api/orders/internalStatuses/internalStatus/rename";

    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: true,
    });
    dispatch({
      type: CAMBIAR_MENSAJE_NULL,
    });

    try {
      const data = await instanceAxios.put(url, params, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: data.status },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: error.response },
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_SET_MARKETPLACE,
        payload: false,
      });
    }
  };

export const reOrderInternalStatus =
  (order, csrf_token) => async (dispatch, getState) => {
    const url = "/api/orders/internalStatuses/internalStatus/reOrder";
    // dispatch({
    //     type: CAMBIAR_LOADING_SET_MARKETPLACE,
    //     payload: true
    // })
    // dispatch({
    //     type: CAMBIAR_MENSAJE_NULL
    // })

    try {
      const data = await instanceAxios.put(url, order, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_REORDER,
        payload: { data: data.status },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_REORDER,
        payload: { data: error.response },
      });
    }
    // finally{
    //     dispatch({
    //         type: CAMBIAR_LOADING_SET_MARKETPLACE,
    //         payload: false
    //     })
    // }
  };

export const setNewInternalStatusOrders =
  (status, csrf_token) => async (dispatch, getState) => {
    const url = "/api/orders/internalStatuses/internalStatus/create";
    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: true,
    });

    dispatch({
      type: CAMBIAR_MENSAJE_NULL,
    });

    try {
      const data = await instanceAxios.post(url, status, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: data.status },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: error.response },
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_SET_MARKETPLACE,
        payload: false,
      });
    }
  };

export const deleteInternalStatus =
  (statusId, csrf_token) => async (dispatch, getState) => {
    const url = "/api/orders/internalStatuses/internalStatus/delete";

    dispatch({
      type: CAMBIAR_LOADING_DELETE_MARKETPLACE,
      payload: true,
    });

    try {
      const data = await instanceAxios.delete(url, {
        headers: {
          "x-csrf-token": csrf_token,
        },
        data: {
          id: statusId,
        },
      });
      dispatch({
        type: CAMBIAR_MARKETPLACES_REGISTER,
        payload: { data: data.data },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_ERROR,
        payload: error.response,
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_DELETE_MARKETPLACE,
        payload: false,
      });
    }
  };

export const updateInternalStatusType =
  (params, csrf_token) => async (dispatch, getState) => {
    const url = "/api/orders/internalStatuses/types/rename";

    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: true,
    });
    dispatch({
      type: CAMBIAR_MENSAJE_NULL,
    });

    try {
      const data = await instanceAxios.put(url, params, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: data.status },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: error.response },
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_SET_MARKETPLACE,
        payload: false,
      });
    }
  };

export const setNewTypeInternalStatus =
  (status, csrf_token) => async (dispatch, getState) => {
    const url = "/api/orders/internalStatuses/types/create";
    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: true,
    });

    dispatch({
      type: CAMBIAR_MENSAJE_NULL,
    });

    try {
      const data = await instanceAxios.post(url, status, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: data.status },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO,
        payload: { data: error.response },
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_SET_MARKETPLACE,
        payload: false,
      });
    }
  };

export const deleteTypeInternalStatus =
  (statusId, csrf_token) => async (dispatch, getState) => {
    const url = "/api/orders/internalStatuses/types/delete";

    dispatch({
      type: CAMBIAR_LOADING_DELETE_MARKETPLACE,
      payload: true,
    });

    try {
      const data = await instanceAxios.delete(url, {
        headers: {
          "x-csrf-token": csrf_token,
        },
        data: {
          id: statusId,
        },
      });
      dispatch({
        type: CAMBIAR_MARKETPLACES_REGISTER,
        payload: { data: data.data },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_ERROR,
        payload: error.response,
      });
    } finally {
      dispatch({
        type: CAMBIAR_LOADING_DELETE_MARKETPLACE,
        payload: false,
      });
    }
  };

export const reOrderTypeInternalStatus =
  (order, csrf_token) => async (dispatch, getState) => {
    const url = "/api/orders/internalStatuses/types/reOrder";
    // dispatch({
    //     type: CAMBIAR_LOADING_SET_MARKETPLACE,
    //     payload: true
    // })
    // dispatch({
    //     type: CAMBIAR_MENSAJE_NULL
    // })

    try {
      const data = await instanceAxios.put(url, order, {
        headers: {
          "x-csrf-token": csrf_token,
          "Content-Type": "application/json",
        },
      });

      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_REORDER,
        payload: { data: data.status },
      });
    } catch (error) {
      dispatch({
        type: CAMBIAR_MENSAJE_EXITO_REORDER,
        payload: { data: error.response },
      });
    }
    // finally{
    //     dispatch({
    //         type: CAMBIAR_LOADING_SET_MARKETPLACE,
    //         payload: false
    //     })
    // }
  };


// new endPoints for marketplaces credentials
///////222222222222222222222222222222222222222222222222222222222222222222222222222
export const getMarketplacesByGroup = () => async (dispatch) => {
  const url = "/api/marketplaces/supportedMarketplaceGroup?scope=marketplacegroupcredentials_supportedmarketplace";

  dispatch({
    type: CAMBIAR_LOADING_GROUPS_MARKETPLACE,
    payload: true,
  });

  try {
    const data = await instanceAxios.get(url);

    dispatch({
      type: CAMBIAR_GROUPS_MARKETPLACE,
      payload: data.data,
    });
  } catch (error) {
    dispatch({
      type: CAMBIAR_ERROR_GROUPS_MARKETPLACE,
      payload: error.response,
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING_GROUPS_MARKETPLACE,
      payload: false,
    });
  }
};



export const updateMarketplaceWithCredentialsGroup = (id_marketplace_credentials, data, csrf_token) => async (dispatch, getState) => {
  const url = `/api/marketplaces/marketplaceGroupCredentials/${id_marketplace_credentials}`// int

  dispatch({
    type: CAMBIAR_LOADING_SET_MARKETPLACE,
    payload: true,
  });

  try {
    const result = await instanceAxios.put(url, data, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "application/json",
      },
    });

    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        data: result.data.mensaje, 
        checkCredentials: true,
        status: result.status
      },
    });
    console.log("getState")
    console.log(getState())
    console.log("getState")
    let newarr = getState().ConfiguracionSistema.groupsMarketplace.supportedMarketplaceGroups;
    console.log("idddddddddddddddddddddddddd")
    console.log(id_marketplace_credentials)
    console.log("idddddddddddddddddddddddddd")

    newarr = newarr.map(market => {
      if (market.id === id_marketplace_credentials) {
        return {
          ...market,
          marketplaceGroup: {
            ... market.marketplaceGroup,
            marketplaceGroupCredentials:{
              check: true,
              clientId: data.clientId,
              clientSecret: null,
              refreshToken: null,
              status: "Credenciales correctas",
            }
          }
        }
      }else{
        return market
      }
    });

    dispatch({
      type: CAMBIAR_GROUPS_MARKETPLACE,
      payload: { supportedMarketplaceGroups: newarr },
    });


  } catch (error) {
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        data: error.response.data.errores,
        checkCredentials: false,
        status: error.response.status,
      },
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: false,
    });
  }
}

export const desynchronizeMarketplaceGroup = (marketplace_group_id, csrf_token) => async (dispatch, getState) => {
  console.log('csrf_token')
  console.log(csrf_token)
  console.log('csrf_token')
  const url = `/api/marketplaces/marketplaceGroupCredentials/${marketplace_group_id}`
  dispatch({
    type: CAMBIAR_LOADING_SET_MARKETPLACE,
    payload: true,
  });

  try {
    const result = await instanceAxios.delete(url, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "application/json",
      },
    });
    console.log("getState")
    console.log(getState())
    console.log("getState")
    let newarr = getState().ConfiguracionSistema.groupsMarketplace.supportedMarketplaceGroups;
    console.log("idddddddddddddddddddddddddd11")
    console.log(newarr)
    console.log("idddddddddddddddddddddddddd12")
    newarr = newarr.map(market => {
      console.log('jjjjjjjjjjjjjjjjjjj')
      console.log(market.id)
      console.log(marketplace_group_id)
      console.log('jjjjjjjjjjjjjjjjjjj')
      if (market.id === marketplace_group_id) {
        return {
          ...market,
          marketplaceGroup: {
            ... market.marketplaceGroup,
            marketplaceGroupCredentials:{
              check: false,
              clientId: null,
              clientSecret: null,
              refreshToken: null,
              status: "No sincronizado",
            }
          }
        }
      }else{
        return market
      }
    });
    console.log("idddddddddddddddddddddddddd21")
    console.log(newarr)
    console.log("idddddddddddddddddddddddddd22")
    dispatch({
      type: CAMBIAR_GROUPS_MARKETPLACE,
      payload: { supportedMarketplaceGroups: newarr },
    });
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: { data: result.data.mensaje, status: result.status, checkCredentials: false },
    });

  } catch (error) {
    console.log("error")
    console.log(error)
    console.log("error")
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        data: error.response.data.errores,
        checkCredentials: false,
        status: error.response.status,
      },
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: false,
    });
  }
}


export const setCredentialMarketplaceGroup = (data, csrf_token) => async (dispatch, getState) => {
  const url = "/api/marketplaces/marketplaceGroupCredentials"
  console.log(1)

  dispatch({
    type: CAMBIAR_LOADING_SET_MARKETPLACE,
    payload: true,
  });
  console.log(2)

  try {
    const result = await instanceAxios.post(url, data, {
      headers: {
        "x-csrf-token": csrf_token,
        "Content-Type": "application/json",
      },
    });
  console.log(3)
  
    let newarr = getState().ConfiguracionSistema.groupsMarketplace.supportedMarketplaceGroups;
    console.log("idddddddddddddddddddddddddd11")
    console.log(newarr)
    console.log("idddddddddddddddddddddddddd12")
  console.log(4)
    
    newarr = newarr.map(market => {
      console.log('000000000000000000000000000000000000000000000000000000000000')
      console.log(market.id)
      console.log(data.supportedMarketplaceGroupId)
      console.log(market.id === data.supportedMarketplaceGroupId)
      console.log('000000000000000000000000000000000000000000000000000000000000')
      if (market.id === data.supportedMarketplaceGroupId) {
        return {
          ...market,
          marketplaceGroup: {
            ... market.marketplaceGroup,
            marketplaceGroupCredentials:{
              check: true,
              clientId: data.credentials.clientId,
              clientSecret: null,
              refreshToken: null,
              status: "Credenciales correctas",
            }
          }
        }
      }else{
        return market
      }
    });
    console.log("idddddddddddddddddddddddddd21")
    console.log(newarr)
    console.log("idddddddddddddddddddddddddd22")
    dispatch({
      type: CAMBIAR_GROUPS_MARKETPLACE,
      payload: { supportedMarketplaceGroups: newarr },
    });
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        data: result.data.mensaje, 
        checkCredentials: true,
        status: result.status
      },
    });

  } catch (error) {
    console.log("error")
    console.log(error)
    console.log("error")
    dispatch({
      type: CAMBIAR_MENSAJE_EXITO,
      payload: {
        data: error.response.data.errores,
        checkCredentials: false,
        status: error.response.status,
      },
    });
  } finally {
    dispatch({
      type: CAMBIAR_LOADING_SET_MARKETPLACE,
      payload: false,
    });
  }
}