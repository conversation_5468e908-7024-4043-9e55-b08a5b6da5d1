body {
  margin: 0;
  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif; */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

}



@media screen and (max-width: 700px) {

  .MuiDrawer-docked .MuiDrawer-paper {
    /* border-radius: none !important; */
    /* height: 80px !important; */
  }

 
  .MuiDrawer-root {
    position: absolute !important;
  }

  .MuiDrawer-docked,
  .css-hzn6ke-MuiDrawer-docked {
    display: block !important;
  }

}

.css-1i5orr8{
  padding: 0px !important;
}





@media screen and (min-width: 450px) {


  .MuiDrawer-docked,
  .MuiDrawer-docked {
    display: block !important;

  }

}


.apexcharts-tooltip {
  top: 20px !important
}

header {
  z-index: 999 !important;
}

.css-ruc65x-MuiPaper-root-MuiDrawer-paper {
  /* height: auto !important; */
  /* border-top-right-radius: 25px; */
  border-bottom-right-radius: 25px;
  background-color: #41644A !important;
  /* color: #FFFFFF !important; */
  z-index: 1000 !important;
}

.css-1dp0x6n-MuiPaper-root-MuiAppBar-root,
.css-xm6jtx-MuiPaper-root-MuiAppBar-root,
.css-lntlm6-MuiPaper-root-MuiAppBar-root,
.css-1f6v9bc-MuiPaper-root-MuiAppBar-root,
.css-vu3sw4-MuiPaper-root-MuiAppBar-root {
  /* width: calc( 100% - 65px ) !important; */
}

.css-dgym8b-MuiDrawer-docked .MuiDrawer-paper {
  border-top-right-radius: 25px;
}

.css-ruc65x-MuiPaper-root-MuiDrawer-paper svg {
  color: #FFFFFF !important;
  z-index: 10000 !important;
}

.css-r31vn8-MuiPaper-root-MuiDrawer-paper {
  /* border-bottom-right-radius: 25px; */

}

.css-rzakf4-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked+.MuiSwitch-track {
  background-color: #FFFFFF !important;
}

.css-1hmlt4w-MuiSwitch-root {
  margin: 0px !important;

}

/* .css-164r41r {
  width: 460px !important;
  max-width: 560px !important;
  min-width: 160px !important;
} */