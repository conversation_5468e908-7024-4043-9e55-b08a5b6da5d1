import { createTheme } from '@mui/material/styles';
import { grey, deepOrange } from '@mui/material/colors';

// import notosansLigth from '../public/fonts/sans/NotoSansKR-Light.ttf';
// import notoSansNormal from '../public/fonts/sans/NotoSansKR-Regular.ttf';
// import filsonProRegular from '../public/fonts/filson/FilsonProRegular.otf';
// import filsonProMedium from '../public/fonts/filson/FilsonProMedium.otf';
// import filsonProBold from '../public/fonts/filson/FilsonProBold.otf';
// import filsonProBlack from '../public/fonts/filson/FilsonProBlack.otf';

const themeLocal = localStorage.getItem('mode') || 'dark';

const notoSansLight = {
  fontFamily: 'Noto Sans',
  fontStyle: 'normal',
  fontWeight: 300,
  src: `url(/fonts/sans/NotoSansKR-Light.ttf) format('truetype')`,
};

const notoSansRegular = {
  fontFamily: 'Noto Sans',
  fontStyle: 'normal',
  fontWeight: 400,
  src: `url(/fonts/sans/NotoSansKR-Regular.ttf) format('truetype')`,
};

const filsonProRegularFont = {
  fontFamily: 'Filson Pro',
  fontStyle: 'normal',
  fontWeight: 400,
  src: `url(/fonts/filson/FilsonProRegular.otf) format('opentype')`,
};

const filsonProMediumFont = {
  fontFamily: 'Filson Pro',
  fontStyle: 'normal',
  fontWeight: 500,
  src: `url(/fonts/filson/FilsonProMedium.otf) format('opentype')`,
};

const filsonProBoldFont = {
  fontFamily: 'Filson Pro',
  fontStyle: 'normal',
  fontWeight: 600,
  src: `url(/fonts/filson/FilsonProBold.otf) format('opentype')`,
};

const filsonProBlackFont = {
  fontFamily: 'Filson Pro',
  fontStyle: 'normal',
  fontWeight: 700,
  src: `url(/fonts/filson/FilsonProBlack.otf) format('opentype')`,
};


// Función que crea el tema basado en el modo (light o dark)
const theme = (mode = themeLocal) =>
  createTheme({
    typography: {
      fontFamily: ' Noto Sans',

      // Estilos para encabezados
      h1: {
        fontFamily: 'Filson Pro, sans-serif',
        fontWeight: 700,
      },
      h2: {
        fontFamily: 'Filson Pro, sans-serif',
        fontWeight: 600,
      },
      // ... otros encabezados


      // Estilos para otros elementos
      subtitle1: {
        fontFamily: 'Noto Sans',
        fontWeight: 400,
      },
      subtitle2: {
        fontFamily: 'Noto Sans',
        fontWeight: 300,
      },
      body1: {
        fontFamily: 'Noto Sans',
        fontWeight: 400,
      },
      body2: {
        fontFamily: 'Noto Sans',
        fontWeight: 300,
      },
      
    },

    palette: {
      mode,
      text: {
        // primary: mode === 'light' ? '#263A29' : '#FFCDEA',
        primary: mode === 'light' ? '#41644a' : '#FFCDEA',
        greenLight: mode === 'light' ? '#41644a' : '#FFCDEA',
        secondary: mode === 'light' ? grey[800] : grey[500],
        greenGrey: mode === 'light' ? ' #717D74' : '#FFCDEA',
        pinkMain: '#FFCDEA',
      },
      // alert: {
      //   error: '#FFA1A1',
      //   warning: '#F9A37E',
      //   info: '#FFF5AE',
      //   success: '#C2F4CE',
      //   errorText: '#812929',
      //   warningText: '#C53C00',
      //   infoText: '#715600',
      //   successText: '#41644A',
      // },
      primary: {
        main: '#d4d8d6',
        greenMain: '#263A29',
        pinkMain: '#FFCDEA',
        backgroundPink: '#FFF1EB',
        ...(mode === 'dark' && {
          main: '#FFCDEA', // Ajustado para modo oscuro
          greenMainTable: '#151e21',
          greenMain: '#FFCDEA'
        }),
      },
      colorGreen: {
        main: '#41644a',
        // contrastText: '#FFCDEA',
      },
      buttonGreen: {
        main: '#263A29',
        contrastText: '#ffcdea',
      },
      buttonGreenPink: {
        main: mode === 'light' ? '#263A29' : '#FFCDEA',
        contrastText: mode === 'light' ? '#ffcdea' : '#263A29',
      },
      secondary: {
        main: mode === 'light' ? '#E86A33' : '#E86A33',
      },
      background: {
        default: mode === 'light' ? '#d4d8d6' : '#151e21',
        paper: mode === 'light' ? grey[200] : '#1c2629',
      },
      border: mode === 'light' ? '#cbcbcb' : '#373737',
      divider: mode === 'dark' ? '#373737' : grey[400],
    },

    components: {
      MuiAlert: {
        styleOverrides: {
          root: {
            borderRadius: '10px', // Estilo general para los Alert
            // backgroundColor: '#F0F0F0', // Color de fondo por defecto
          },
          filledError: {
            backgroundColor: '#FFA1A1',
            color: '#812929',
          },
          filledSuccess: {
            backgroundColor: '#C2F4CE',
            color: '#41644A',
          },
          filledInfo: {
            backgroundColor: '#FFF5AE',
            color: '#715600',
          },
          filledWarning: {
            backgroundColor: '#F9A37E',
            color: '#C53C00',
          },
        },
      },

      MuiCssBaseline: {
        styleOverrides: {
          html: [
            { '@font-face': notoSansLight },
            { '@font-face': notoSansRegular },
            { '@font-face': filsonProRegularFont },
            { '@font-face': filsonProMediumFont },
            { '@font-face': filsonProBoldFont },
            { '@font-face': filsonProBlackFont },
          ],
          'html, body': {
            padding: 0,
            scrollbarWidth: 'thin',
          },
          'h1, h2, h3, h4, h5, h6': {
            fontFamily: 'Filson Pro, sans-serif',
          },

          root: {
            [`.MuiAlert-filled.MuiAlert-filledError`]: {
              backgroundColor: '#FFA1A1',
              color: '#812929',
            },
          }

        },

      },

      MuiTableCell: {
        styleOverrides: {
          root: {
            fontFamily: '"Filson Pro", sans-serif',
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundColor: '#fffff',  // Color por defecto
          },
          outlined: {
            backgroundColor: '#fffff',  // Color cuando sea "outlined"
          },
          elevation1: {
            backgroundColor: '#fffff',  // Color cuando tenga una elevación de 1
          },
        },
      },
      MuiTableContainer: {
        styleOverrides: {
          root: {
            backgroundColor: mode === 'light' ? '#ffffff' : '#273134',  // Cambia el color de fondo
          },
        },
      },
      MuiFab: {
        styleOverrides: {
          root: {
            '&.buttonGreenPink': {
              backgroundColor: mode === 'light' ? '#263A29' : '#FFCDEA',
              color: mode === 'light' ? '#ffcdea' : '#263A29',
              '&:hover': {
                backgroundColor: mode === 'light' ? '#1a2a1d' : '#ffc0e3',
              }
            }
          }
        }
      }
    },


    // MuiTypography: {
    //   styleOverrides: {
    //     h1: {
    //       fontFamily: 'Filson Pro',
    //     },
    //     h2: {
    //       fontFamily: 'Filson Pro',
    //     },
    //     h3: {
    //       fontFamily: 'Filson Pro',
    //     },
    //     h4: {
    //       fontFamily: 'Filson Pro',
    //     },
    //     h5: {
    //       fontFamily: 'Filson Pro',
    //     },
    //     h6: {
    //       fontFamily: 'Filson Pro',
    //     }
    //   }
    // },
    // @font-face {
    //   font-family: 'Filson pro';
    //   src: url('../public/fonts/filson/FilsonProRegular.otf') format('opentype');
    //   font-weight: normal;
    //   font-style: normal;
    // }
    breakpoints: {
      values: {
        xss: 200,
        xs: 450,
        sm: 700,
        md: 960,
        detailOperationScreenNumber: 1060,
        lg: 1280,
        xl: 1920,
        fs:1900,
        lg1500: 1500,
        xl1800: 1800,
        xl1700: 1700,
        config1: 801,
        config2: 915,
      },
    },
  });

export default theme;


// import { createTheme } from '@mui/material/styles';

// const theme = createTheme({
//   palette: {
//     mode: 'dark',
//     primary: {
//       main: '#003876',
//     },
//     secondary: {
//       main: '#00a5da',
//     },
//   },
//   breakpoints: {
//     values: {
//       xss: 200,
//       xs: 450,
//       sm: 700, 
//       md: 960,
//       detailOperationScreenNumber: 1060,
//       lg: 1280,
//       xl: 1920,
//       config1: 801,
//       config2: 915,
//     },
//   },
// });

// export default theme;
// palette: {
//   mode,
//   ...(mode === 'light'
//       && {
//       // valores de la paleta para el modo claro
//       primary: {
//           main: '#003876',
//       },
//       secondary: {
//           main: '#00a5da',
//       },
//       background: {
//           default: grey[100],
//           paper: grey[100],
//       },
//       border: '#cbcbcb',
//       text: {
//           primary: grey[900],
//           secondary: grey[800],
//       },
//   }
//       // : {
//       //     // valores de la paleta para el modo oscuro
//       //     primary: deepOrange,
//       //     divider: deepOrange[700],
//       //     background: {
//       //       default: deepOrange[900],
//       //       paper: deepOrange[900],
//       //     },
//       //     text: {
//       //       primary: '#fff',
//       //       secondary: grey[500],
//       //     },
//       //   }
//   ),
// },
