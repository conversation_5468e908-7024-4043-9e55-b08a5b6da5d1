import React from 'react'

export const ProductListContext = React.createContext()

const ProductListProvider = (props) => {
    
    const [todosSeleccionados, setTodosSeleccionados] = React.useState(false);
    return (
    <ProductListContext.Provider value={{todosSeleccionados, setTodosSeleccionados}}>
        {props.children}
    </ProductListContext.Provider>
  )
}

export default ProductListProvider