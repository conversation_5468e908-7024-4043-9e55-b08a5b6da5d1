import React, { useEffect } from 'react';
import { useTheme, ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { deepOrange, grey } from '@mui/material/colors';
import theme from '../temaConfig';

export const ModeContext = React.createContext();

const ModeProvider = (props) => {
    const [mode, setMode] = React.useState(
        localStorage.getItem('mode') || 'dark'
    );

    const colorMode = React.useMemo(
        () => ({
            toggleColorMode: () => {
                setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
            },
        }),
        []
    );
    useEffect(() => {
        localStorage.setItem('mode', mode);
    }, [mode])

    const themee = React.useMemo(
        () =>
            createTheme(theme(mode)),
        [mode]
    );

    return (
        <ModeContext.Provider value={{ colorMode, themee }}>
            <ThemeProvider theme={themee}>
                <CssBaseline />
                {props.children}
            </ThemeProvider>
        </ModeContext.Provider>
    );
};

export default ModeProvider;

