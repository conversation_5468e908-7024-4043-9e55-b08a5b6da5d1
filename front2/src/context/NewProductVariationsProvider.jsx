import React from "react";
import { useEffect } from "react";
import { EXTENSIONES_PERMITIDAS_IMAGENES_PRODUCTOS } from "../Utils/config";
import { useSelector } from "react-redux";

export const NewProductVariationsContext = React.createContext();

const NewProductVariationsProvider = (props) => {
  const productPreserves = useSelector(
    (state) => state.productos.persevereDataNewProduct
  );

  const [flag, setFlag] = React.useState(false);
  const [selectedVariations, setSelectedVariations] = React.useState([
    "Condición",
  ]);
  // const [parentStates, setParentStates] = React.useState( productPreserves?.variations?.length > 0 ?  productPreserves?.variations : []);
  const [parentStates, setParentStates] = React.useState([]);

  // console.log("parentStates", parentStates);

  const [formDataAll, setFormDataAll] = React.useState(new FormData());
  //con este armo mis files para enviarle al back
  // const [containerImagesVariations, setContainerImagesVariations] = React.useState( productPreserves.imgs &&
  //   Object.keys(productPreserves?.imgs)?.length > 0 ? { ...productPreserves.imgs } : []
  // );
  const [containerImagesVariations, setContainerImagesVariations] =
    React.useState([]);
  const [components, setComponents] = React.useState([]);
  const [componentsFlag, setComponentsFlag] = React.useState([]);
  const [deleteImageUrl, setDeleteImageUrl] = React.useState([]);

  const [selectNumberVAriation, setSelectNumberVariation] = React.useState([]);

  const addFilesToFormData = () => {
    let listImageToDelete = [...deleteImageUrl];
    let textoErrores = "";
    for (let key in containerImagesVariations) {
      let index = key.split("x")[1];
      // debugger
      for (let i = 0; i < containerImagesVariations[key].length; i++) {
        let item = containerImagesVariations[key][i];
        if (listImageToDelete.includes(item.props.title)) {
          listImageToDelete = listImageToDelete.filter(
            (fotoTitulo) => fotoTitulo !== item.props.title
          );
        } else {
          if (item.props.file) {
            let nombreCompletoSplit = item.props.file.name.split(".");
            let extension = nombreCompletoSplit[nombreCompletoSplit.length - 1];
            nombreCompletoSplit.pop();
            let nombre = nombreCompletoSplit.join();
            if (
              !EXTENSIONES_PERMITIDAS_IMAGENES_PRODUCTOS.includes(extension)
            ) {
              textoErrores === ""
                ? (textoErrores = `Algún archivo seleccionado no es imagen`)
                : (textoErrores = `${textoErrores},Algún archivo seleccionado no es imagen`);
              return;
            }
            if (nombre.length === 0) {
              textoErrores === ""
                ? (textoErrores = `Algún archivo seleccionado no tiene nombre válido`)
                : (textoErrores = `${textoErrores},Algún archivo seleccionado no tiene nombre válido`);
              return;
            }
            // componentsFlag.
            componentsFlag.map((variation) => {
              // debugger
              let newVariation = variation.state.props.data
              if (newVariation[key] && newVariation[key].status) {
                let newFile = new File(
                  [item.props.file],
                  `${nombre}.${extension}`,
                  {
                    type: item.props.file.type,
                  }
                );
                formDataAll.append(`variationImagesToAdd_${index}[]`, newFile);
                formDataAll.append(
                  `variationImagesToAdd_${index}${item.props.file.name}`,
                  i
                );
                setFormDataAll(formDataAll);
              }
            });
          } else {
            componentsFlag.map((variation) => {
              let newVariation = variation.state.props.data
              if (newVariation[key] && newVariation[key].status) {
                let aux = formDataAll.getAll("variationProduct[]");
                let object = aux.find((itemVariacion) => {
                  if (JSON.parse(itemVariacion).index === parseInt(index)) {
                    // return JSON.parse(item);
                    return itemVariacion;
                  }
                });
                // debugger
                if (typeof object === "string" && object.trim() !== "") {
                  object = JSON.parse(object);
              } else {
                console.log("No se encontro la variacion", object, aux);
                  return
                }

                object["urlToAdd[]"].push({ img: item.props.url, position: i });

                let indice = aux.findIndex(
                  (objeto) => JSON.parse(objeto).index === object.index
                );

                if (indice !== -1) {
                  aux[indice] = JSON.stringify(object);
                }
                formDataAll.delete("variationProduct[]");

                aux.map((item) => {
                  formDataAll.append("variationProduct[]", item);
                });
              }
            });
          }
        }
      }
    }
  };

  useEffect(() => {
    if (
      productPreserves.imgs &&
      Object.keys(productPreserves?.imgs)?.length > 0
    ) {
      setContainerImagesVariations(productPreserves.imgs);
    }
  }, []);

  // for (const [key, value] of formDataAll.entries()) {
  //   console.log(`${key}: ${value}`);
  // }

  const files = {
    //para dar la pauta a que ya se envio el formulario
    flag,
    setFlag,
    //para agreagr las variaciones ej color, tamaño, etc
    selectedVariations,
    setSelectedVariations,
    //formData
    formDataAll,
    setFormDataAll,
    containerImagesVariations,
    setContainerImagesVariations,
    //funcion para agregar las images a formData
    addFilesToFormData,
    //mis ID de las variaciones [1, 2...]
    selectNumberVAriation,
    setSelectNumberVariation,
    //components ya no se usa
    components,
    setComponents,
    //arreglo de mis variaciones
    parentStates,
    setParentStates,
    //para saber si se agrego o no una variacion con la bandera flag
    componentsFlag,
    setComponentsFlag,
    //para selecionar las imagenes a borrar
    setDeleteImageUrl,
    deleteImageUrl,
  };

  return (
    <NewProductVariationsContext.Provider value={files}>
      {props.children}
    </NewProductVariationsContext.Provider>
  );
};

export default NewProductVariationsProvider;
