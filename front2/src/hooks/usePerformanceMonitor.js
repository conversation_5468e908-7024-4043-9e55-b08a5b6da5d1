import { useEffect, useRef, useCallback } from 'react';

/**
 * Hook para monitorear el rendimiento de componentes React
 * Útil para detectar renderizados excesivos y problemas de performance
 * 
 * @param {string} componentName - Nombre del componente para identificación
 * @param {Object} options - Opciones de configuración
 * @param {boolean} options.enabled - Si el monitoreo está habilitado (default: process.env.NODE_ENV === 'development')
 * @param {number} options.warningThreshold - Número de renders en timeWindow para mostrar warning (default: 10)
 * @param {number} options.timeWindow - Ventana de tiempo en ms para contar renders (default: 1000)
 * @param {boolean} options.logEveryRender - Si debe loggear cada render individual (default: false)
 * 
 * @returns {Object} - Objeto con métodos y estadísticas de rendimiento
 */
export const usePerformanceMonitor = (
  componentName, 
  options = {}
) => {
  const {
    enabled = process.env.NODE_ENV === 'development',
    warningThreshold = 10,
    timeWindow = 1000,
    logEveryRender = false
  } = options;

  const renderCount = useRef(0);
  const renderTimes = useRef([]);
  const lastWarningTime = useRef(0);
  const startTime = useRef(Date.now());

  const logRenderInfo = useCallback(() => {
    if (!enabled) return;

    const now = Date.now();
    renderCount.current += 1;
    renderTimes.current.push(now);

    // Limpiar renders antiguos fuera de la ventana de tiempo
    renderTimes.current = renderTimes.current.filter(
      time => now - time <= timeWindow
    );

    const recentRenders = renderTimes.current.length;
    const timeSinceStart = now - startTime.current;

    if (logEveryRender) {
      console.log(
        `🔄 ${componentName} render #${renderCount.current} ` +
        `(${recentRenders} en últimos ${timeWindow}ms)`
      );
    }

    // Warning si hay demasiados renders recientes
    if (recentRenders >= warningThreshold && now - lastWarningTime.current > timeWindow) {
      console.warn(
        `⚠️ PERFORMANCE WARNING: ${componentName} ha renderizado ${recentRenders} veces ` +
        `en los últimos ${timeWindow}ms. Esto podría indicar un problema de rendimiento.`
      );
      lastWarningTime.current = now;
    }

    // Log de estadísticas cada 50 renders
    if (renderCount.current % 50 === 0) {
      const avgRenderTime = timeSinceStart / renderCount.current;
      console.log(
        `📊 ${componentName} Stats: ${renderCount.current} renders totales, ` +
        `promedio: ${avgRenderTime.toFixed(2)}ms entre renders`
      );
    }
  }, [componentName, enabled, warningThreshold, timeWindow, logEveryRender]);

  useEffect(() => {
    logRenderInfo();
  });

  const getStats = useCallback(() => {
    const now = Date.now();
    const recentRenders = renderTimes.current.filter(
      time => now - time <= timeWindow
    ).length;

    return {
      totalRenders: renderCount.current,
      recentRenders,
      timeWindow,
      averageRenderInterval: renderCount.current > 1 
        ? (now - startTime.current) / (renderCount.current - 1)
        : 0
    };
  }, [timeWindow]);

  const reset = useCallback(() => {
    renderCount.current = 0;
    renderTimes.current = [];
    startTime.current = Date.now();
    lastWarningTime.current = 0;
  }, []);

  return {
    renderCount: renderCount.current,
    getStats,
    reset,
    enabled
  };
};

/**
 * Hook para detectar cambios en props entre renders
 * Versión mejorada del useWhyDidYouUpdate
 * 
 * @param {Object} props - Props del componente a monitorear
 * @param {string} componentName - Nombre del componente
 * @param {Object} options - Opciones de configuración
 */
export const usePropsChangeMonitor = (props, componentName, options = {}) => {
  const {
    enabled = process.env.NODE_ENV === 'development',
    deepCompare = false,
    ignoreProps = []
  } = options;

  const prevProps = useRef();
  const changeHistory = useRef([]);

  useEffect(() => {
    if (!enabled || !prevProps.current) {
      prevProps.current = props;
      return;
    }

    const changedProps = [];
    const propsToCheck = Object.keys(props).filter(
      key => !ignoreProps.includes(key)
    );

    propsToCheck.forEach(key => {
      const prevValue = prevProps.current[key];
      const currentValue = props[key];
      
      let hasChanged = false;
      
      if (deepCompare) {
        // Comparación profunda básica (solo para objetos simples)
        hasChanged = JSON.stringify(prevValue) !== JSON.stringify(currentValue);
      } else {
        hasChanged = prevValue !== currentValue;
      }
      
      if (hasChanged) {
        changedProps.push({
          prop: key,
          from: prevValue,
          to: currentValue,
          type: typeof currentValue
        });
      }
    });

    if (changedProps.length > 0) {
      const changeInfo = {
        timestamp: Date.now(),
        component: componentName,
        changes: changedProps
      };
      
      changeHistory.current.push(changeInfo);
      
      // Mantener solo los últimos 10 cambios
      if (changeHistory.current.length > 10) {
        changeHistory.current = changeHistory.current.slice(-10);
      }

      console.group(`📝 ${componentName} - Props Changed`);
      changedProps.forEach(({ prop, from, to, type }) => {
        console.log(`  ${prop} (${type}):`, from, '→', to);
      });
      console.groupEnd();
    } else {
      console.log(`✅ ${componentName} - Re-render sin cambios en props`);
    }

    prevProps.current = props;
  });

  return {
    changeHistory: changeHistory.current,
    getLastChanges: () => changeHistory.current.slice(-5)
  };
};

/**
 * Hook combinado para monitoreo completo de rendimiento
 */
export const useComponentMonitor = (componentName, props, options = {}) => {
  const performanceMonitor = usePerformanceMonitor(componentName, options.performance);
  const propsMonitor = usePropsChangeMonitor(componentName, props, options.props);

  return {
    performance: performanceMonitor,
    props: propsMonitor
  };
};

export default usePerformanceMonitor;
