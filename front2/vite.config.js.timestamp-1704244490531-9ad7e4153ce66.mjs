// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/Documentos/node_modules/vite/dist/node/index.js";
import reactRefresh from "file:///C:/Users/<USER>/Documentos/node_modules/@vitejs/plugin-react-refresh/index.js";
import svgr from "file:///C:/Users/<USER>/Documentos/node_modules/vite-plugin-svgr/dist/index.js";
import react from "file:///C:/Users/<USER>/Documentos/node_modules/@vitejs/plugin-react/dist/index.mjs";
var vite_config_default = defineConfig({
  define: {
    "process.env": {}
  },
  build: {
    outDir: "./build"
  },
  plugins: [
    react({
      jsxImportSource: "@emotion/react",
      include: ["**/*.jsx", "**/*.tsx"],
      babel: {}
    }),
    reactRefresh(),
    svgr()
  ]
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJDOlxcXFxVc2Vyc1xcXFxqYXNpZWwuc2lsdmFcXFxcRG9jdW1lbnRvc1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiQzpcXFxcVXNlcnNcXFxcamFzaWVsLnNpbHZhXFxcXERvY3VtZW50b3NcXFxcdml0ZS5jb25maWcuanNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0M6L1VzZXJzL2phc2llbC5zaWx2YS9Eb2N1bWVudG9zL3ZpdGUuY29uZmlnLmpzXCI7aW1wb3J0IHsgZGVmaW5lQ29uZmlnIH0gZnJvbSAndml0ZSc7XHJcbmltcG9ydCByZWFjdFJlZnJlc2ggZnJvbSAnQHZpdGVqcy9wbHVnaW4tcmVhY3QtcmVmcmVzaCc7XHJcbmltcG9ydCBzdmdyIGZyb20gJ3ZpdGUtcGx1Z2luLXN2Z3InO1xyXG5pbXBvcnQgcmVhY3QgZnJvbSAnQHZpdGVqcy9wbHVnaW4tcmVhY3QnO1xyXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoe1xyXG4gIGRlZmluZToge1xyXG4gICAgJ3Byb2Nlc3MuZW52Jzoge30sXHJcbiAgfSxcclxuICBidWlsZDoge1xyXG4gICAgb3V0RGlyOiAnLi9idWlsZCcsXHJcbiAgICB9LFxyXG4gIHBsdWdpbnM6IFtcclxuICAgIHJlYWN0KHtcclxuICAgICAgICBqc3hJbXBvcnRTb3VyY2U6ICdAZW1vdGlvbi9yZWFjdCcsXHJcbiAgICAgICAgaW5jbHVkZTogWycqKi8qLmpzeCcsICcqKi8qLnRzeCddLFxyXG4gICAgICAgIGJhYmVsOiB7XHJcbiAgICAgICAgfSxcclxuICAgIH0pLFxyXG4gICAgcmVhY3RSZWZyZXNoKCksIFxyXG4gICAgc3ZncigpXHJcbiAgICBdLFxyXG59KTsiXSwKICAibWFwcGluZ3MiOiAiO0FBQTBSLFNBQVMsb0JBQW9CO0FBQ3ZULE9BQU8sa0JBQWtCO0FBQ3pCLE9BQU8sVUFBVTtBQUNqQixPQUFPLFdBQVc7QUFDbEIsSUFBTyxzQkFBUSxhQUFhO0FBQUEsRUFDMUIsUUFBUTtBQUFBLElBQ04sZUFBZSxDQUFDO0FBQUEsRUFDbEI7QUFBQSxFQUNBLE9BQU87QUFBQSxJQUNMLFFBQVE7QUFBQSxFQUNSO0FBQUEsRUFDRixTQUFTO0FBQUEsSUFDUCxNQUFNO0FBQUEsTUFDRixpQkFBaUI7QUFBQSxNQUNqQixTQUFTLENBQUMsWUFBWSxVQUFVO0FBQUEsTUFDaEMsT0FBTyxDQUNQO0FBQUEsSUFDSixDQUFDO0FBQUEsSUFDRCxhQUFhO0FBQUEsSUFDYixLQUFLO0FBQUEsRUFDTDtBQUNKLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
