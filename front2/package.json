{"name": "turtle-cp", "version": "1.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/icons-material": "^6.1.9", "@mui/lab": "^5.0.0-alpha.154", "@mui/material": "^6.1.9", "@mui/x-date-pickers": "^5.0.20", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@toolpad/core": "^0.10.0", "@vitejs/plugin-react": "^4.3.1", "@vitejs/plugin-react-refresh": "^1.3.6", "apexcharts": "^3.49.1", "array-move": "^4.0.0", "axios": "^1.6.2", "date-fns": "^2.28.0", "firebase": "^8.7.1", "framer-motion": "^12.4.7", "is-image": "^3.1.0", "is-image-url": "^1.1.8", "js-file-download": "^0.4.12", "jwt-decode": "^3.1.2", "moment": "^2.29.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-cookie": "^4.1.1", "react-dom": "^18.2.0", "react-number-format": "^5.4.3", "react-quill": "^2.0.0", "react-rating-stars-component": "^2.2.0", "react-redux": "^7.2.6", "react-router-dom": "^6.3.0", "react-smooth-dnd": "^0.11.1", "react-swipeable-views": "^0.14.0", "redux": "^4.1.2", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^2.4.1", "uuid": "^11.0.5", "vite": "^5.4.1", "vite-plugin-svgr": "^4.2.0", "web-vitals": "^1.1.2"}, "scripts": {"start": "vite", "build": "vite build", "serve": "vite preview"}, "type": "module", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}