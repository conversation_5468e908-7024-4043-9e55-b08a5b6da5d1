services:
  frontendcpnotifications:
    image: frontend:latest
    networks:
      - inbound
    deploy:
      restart_policy:
        condition: on-failure
      labels:
        - "traefik.http.routers.frontendcpnotifications.rule=Host(`cpnotifications.creativeplanet.com.mx`)"
        - "traefik.http.routers.frontendcpnotifications.service=frontendcpnotifications"
        - "traefik.http.routers.frontendcpnotifications.entrypoints=websecurity"
        - "traefik.http.routers.frontendcpnotifications.tls.certresolver=myresolver"
        - "traefik.http.services.frontendcpnotifications.loadbalancer.server.port=80"
        - "traefik.docker.network=inbound"

networks:
  inbound:
   external: true