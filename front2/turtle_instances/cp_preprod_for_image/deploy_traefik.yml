services:
  frontendcppreprod:
    image: frontend:latest
    networks:
      - inbound
    deploy:
      restart_policy:
        condition: on-failure
      labels:
        - "traefik.http.routers.frontendcppreprod.rule=Host(`tortuga.creativeplanet.com.mx`)"
        - "traefik.http.routers.frontendcppreprod.service=frontendcppreprod"
        - "traefik.http.routers.frontendcppreprod.entrypoints=websecurity"
        - "traefik.http.routers.frontendcppreprod.tls.certresolver=myresolver"
        - "traefik.http.services.frontendcppreprod.loadbalancer.server.port=80"
        - "traefik.docker.network=inbound"

networks:
  inbound:
   external: true