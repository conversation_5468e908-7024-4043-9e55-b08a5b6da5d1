services:
  frontendcp:
    image: frontend:latest
    networks:
      - inbound
    deploy:
      restart_policy:
        condition: on-failure
      labels:
        - "traefik.http.routers.frontendcp.rule=Host(`cp.mercaleader.io`)"
        - "traefik.http.routers.frontendcp.service=frontendcp"
        - "traefik.http.routers.frontendcp.entrypoints=websecurity"
        - "traefik.http.routers.frontendcp.tls.certresolver=myresolver"
        - "traefik.http.services.frontendcp.loadbalancer.server.port=80"
        - "traefik.docker.network=inbound"

networks:
  inbound:
   external: true